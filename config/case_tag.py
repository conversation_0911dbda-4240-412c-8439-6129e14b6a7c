CASE_TAG = {
    'test_case/trade/service_market': '服务市场',
    'test_case/trade/order': '订单',
    'test_case/trade/refund': '售后',
    'test_case/trade/industry': '履约',
    'test_case/assistant': 'B端-直播助手',
    'test_case/merchant_recruit': 'B端-招商',
    'test_case/distribution': 'B端-分销',
    'test_case/funds': 'B端-资金',
    'test_case/merchant_syt': 'B端-生意通',
    'test_case/customer': 'B端-客服',
    'test_case/workbench': 'B端-工作台',
    'test_case/core_link/product_center': '商品1',
    'test_case/product': '商品2',
    'test_case/helper_assistant': '营销导购-跟播助手',
    'test_case/short_video': '营销导购-短视频',
    'test_case/syt_screen': "B端-生意通",
    'test_case/marketing': "营销工具",
    'test_case/merchant_member': "B端-商家私域-会员/会员crm",
    'test_case/core_link/supply_chain': '供应链',
    'test_case/ecology': 'B端-治理',
    'test_case/marketing_prem': "营销导购-精准营销",
    'test_case/investment_activity': "商城pc自动化-招商",
    'test_case/marking_mall': "商城pc自动化-商城",
    'test_case/education': "客户平台-教育"
}

DEFAULT_CASE_TAG = ('未分类',)


def get_tag_list(item):
    tag_list = []
    # tag标记
    for mark in item.iter_markers(name="tag"):
        tag_list.extend(mark.args)
    # CASE_TAG判断
    for path, tag in CASE_TAG.items():
        if item.nodeid.startswith(path):
            tag_list.append(tag)
    if len(tag_list) == 0:
        tag_list = DEFAULT_CASE_TAG
    return list(set(tag_list))
