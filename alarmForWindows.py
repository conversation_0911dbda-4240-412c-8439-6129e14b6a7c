import shutil
import socket
import requests
import json
import platform


def get_server_ip():
    """获取本机内网IPv4地址"""
    try:
        # 尝试连接外部DNS以获取实际使用的IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        try:
            # 备选方案：获取主机名对应的第一个IP
            return socket.gethostbyname(socket.gethostname())
        except:
            return "IP未知"


def send_alert(message):
    """发送报警信息到指定接口"""
    server_ip = get_server_ip()
    # 在消息开头添加服务器IP标识
    formatted_message = f"【服务器:{server_ip}】 {message}"

    url = "https://kim-robot.kwaitalk.com/api/robot/send?key=a9a32379-396b-4bfa-9c0a-6a50f13477ef"
    payload = json.dumps({
        "msgtype": "text",
        "text": {"content": formatted_message}
    })
    headers = {'Content-Type': 'application/json'}

    try:
        response = requests.post(url, headers=headers, data=payload)
        print(f"报警发送状态: {response.status_code}")
        return True
    except Exception as e:
        print(f"报警发送失败: {str(e)}")
        return False


def check_disk_space():
    """检查C盘剩余空间是否小于5GB"""
    try:
        usage = shutil.disk_usage("C:\\")
        free_gb = round(usage.free / (1024 ** 3), 2)
        alert_threshold = 5

        if free_gb < alert_threshold:
            return False, f"⚠️ C盘空间不足: 剩余{free_gb}GB < 5GB"
        return True, f"C盘空间正常: 剩余{free_gb}GB"
    except Exception as e:
        return False, f"磁盘检查失败: {str(e)}"


def check_port_access():
    """检查本地8080端口是否可访问"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(3)
            result = s.connect_ex(('127.0.0.1', 8080))
            if result == 0:
                return True, "Jenkins端口访问正常"
            else:
                return False, "❌ Jenkins端口无法访问"
    except Exception as e:
        return False, f"端口检查失败: {str(e)}"


if __name__ == "__main__":
    print("开始系统检查...")
    server_ip = get_server_ip()
    print(f"当前服务器IP: {server_ip}")

    # 检查磁盘空间
    disk_ok, disk_msg = check_disk_space()
    print(disk_msg)
    if not disk_ok:
        send_alert(disk_msg)

    # 检查端口
    port_ok, port_msg = check_port_access()
    print(port_msg)
    if not port_ok:
        send_alert(port_msg)

    print("系统检查完成")