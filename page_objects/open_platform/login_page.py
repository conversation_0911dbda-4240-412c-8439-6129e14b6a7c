class LoginPage(object):
    # "登陆"button
    """
    # 切换到 iframe（如果有必要）
    driver.switch_to.frame("iframe_id_or_name")

    # 使用 CSS 选择器定位元素
    # 通过类名定位元素
    element_by_class = driver.find_element(By.CSS_SELECTOR, ".element-class")
    # 通过 ID 定位元素
    element_by_id = driver.find_element(By.CSS_SELECTOR, "#element-id")
    # 通过标签名定位元素
    element_by_tag = driver.find_element(By.CSS_SELECTOR, "p")  # 定位第一个 <p> 元素
    # 通过属性定位元素
    element_by_attribute = driver.find_element(By.CSS_SELECTOR, 'input[name="username"]')
    # 定位具有特定父元素的元素
    child_element = driver.find_element(By.CSS_SELECTOR, "div.parent > p")  # 定位 div.parent 的直接 <p> 子元素

    """
    login_button = "#ssoSubmitLabel"
