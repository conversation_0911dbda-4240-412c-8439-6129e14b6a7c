class TradeOrderPage(object):
    """pc订单页面元素定位定义"""

    #进入快手小店，有个弹框，需要处理掉
    open_kwaixiaodian_confirm = 'button.driver-close-btn.driver-close-only-btn'
    #打开订单页面弹窗
    open_order_page_confirm = 'div.ant-modal-confirm-btns > button:nth-child(1)'

    #小店pc，侧边栏 订单tab入口
    # left_order_enter = '#first_menu_for_intersectionObserver_R553YgpLnhQ'
    # left_order_enter = "(//span[@class='bASqM1kzJlIX6ZNynVIc'][contains(text(),'订单')])[1]"
    left_order_enter = "#menu_item_R553YgpLnhQ"
    left_order_enter_new = "//div[@class='menuItemTitle___ZUC9t'][contains(text(),'订单')]"

    #待发货紧急消息通知
    urgent_message_notice = "//div[@class='kpro-important-modal-text-title']"
    urgent_message_close_button = "//button[@class='seller-main-btn seller-main-btn-primary seller-main-btn-dangerous']"

    # 弹窗
    test_window = "//div[@class='seller-main-modal-header']"
    test_window_confirm = "//span[contains(text(),'知道了')]"
    # 小店pc 侧边栏 订单查询入口
    order_list_enter = '#menu_item_zAoD7EEcix0'

    #24小时需发货的tab
    # twenty_four_delivery_tab = 'div:contains("24h需发货")'
    twenty_four_delivery_tab = 'div.nOizsWgZ_mVxYoix5iOW'

    #订单搜索框-订单状态
    search_order_status_ele = 'span.ant-select-selection-item'

    #订单状态下拉框元素
    search_order_status_dropbox = 'div.rc-virtual-list-holder > div > div'

    #订单列表第一笔订单,点商品，进入商品详情
    first_order = 'div.ant-pro-list-row-content > div:nth-child(6) > div > div > button'

    #订单详情里 商品id信息
    goods_status = 'span.ant-descriptions-item-label'

    #订单列表页面上，订单详情链接
    # # order_detail_href = 'div.order-list-item > div > div:nth-child(8) > a'
    # order_detail_href = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(2) > div > div > div > div > div:nth-child(2) > div:nth-child(1) > div.ant-pro-list-row-content > div:nth-child(1) > div > div > div.fX0TnOfWLHeoqmsJG45f > div:nth-child(1)'
    # order_detail_href = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[6]/div/div/button'
    order_detail_href = "// span[contains(text(), '订单详情')]"

    #查看订单导出按钮
    # order_export_button = 'span:contains("查看已导出报表")'
    order_export_button = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[5]'

    #订单导出页面，check 标题
    order_export_page_title = 'h3:contains("已导出的订单报表")'

    #筛选发货状态表单项
    order_status_form_select = 'span.ant-select-selection-item'

    #订单筛选按钮
    order_select_button = 'div.ant-space.ant-space-horizontal.ant-space-align-center > div:nth-child(2) > button > span'
    # order_select_button = 'span:contains("查询")'
    #订单列表发货按钮
    order_delivery_button = 'div.order-list-item > div > div:nth-child(8) > button'

    #发货弹窗元素：快递单号
    delivery_popup_element = 'label[title="快递单号"]'

    #客服对话框入口
    customer_service_enter = "(//img[@alt='icon'])[1]"

    #客服页面：校验顶部渲染出来
    today_ask_num = 'div.information-data'

    #合并发货按钮
    merge_delivery_button = 'span:contains("合并发货")'

    #批量发货按钮
    many_delivery_button = '#import-excel-driver-share'
    #批量发货弹窗-下载模版
    download_template = 'div.jVnhpF3RPomgdNoVdGMQ'
    #关闭批量发货弹窗的x号
    close_many_delivery_ele = 'span.ant-modal-close-x'

    #打开订单页面时的"订单筛选升级啦"弹框
    # order_select_confirm = 'button.driver-close-btn'
    order_select_confirm = '#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'

    #筛选出待发货的订单
    # first_order_status_select = 'div.rc-virtual-list-holder > div > div>div'
    first_order_status_select = 'div:nth-child(1) > div:nth-child(2) > div > div.ant-col.ant-form-item-control > div > div > div'
    wait_for_delivery_order = 'div:nth-child(6) > div > div > div > div.rc-virtual-list > div.rc-virtual-list-holder > div > div > div:nth-child(3)>div'

    #立即发货弹窗
    immediately_delivery_confirm = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(2) > div > div > div > div > div:nth-child(2) > div:nth-child(1) > div.ant-pro-list-row-content > div:nth-child(6) > div > div > button.ant-btn.ant-btn-primary'

    #暂不处理
    current_not_deploy = 'div.ant-modal-confirm-btns>button:nth-child(1)'

    """订单列表上面的菜单栏"""
    # 订单列表上面的菜单栏【协商发货时间】按钮
    # NEGOTIATE_SHIPPING_BUTTON = '#kwaishop-negotiate-shipping > button'
    NEGOTIATE_SHIPPING_BUTTON = '#negotiationCenter> button'


    # 订单列表上面的菜单栏【延迟发货报备】按钮
    DELAY_SHIPPING_BUTTON = '#kwaishop-delay-shipping > div > span'

    # 订单列表上面的菜单栏【优先发货设置】按钮
    PRIORITY_SHIPPING_SETTING_BUTTON = '#kwaishop-seller-order-pc-ship-setting > button'

    # 订单列表上面的菜单栏【近6个月订单】按钮
    ORDERS_RECENT_SIX_MONTH_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-tabs.ant-tabs-top.qgczmziqnjx7H7Bbezer > div.ant-tabs-nav > div.ant-tabs-nav-wrap > div > div.ant-tabs-tab.ant-tabs-tab-active'

    # 订单列表上面的菜单栏【6个月前订单】按钮
    ORDERS_BEFORE_SIX_MONTH_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-tabs.ant-tabs-top.qgczmziqnjx7H7Bbezer > div.ant-tabs-nav > div.ant-tabs-nav-wrap > div > div:nth-child(2)'

    # 订单列表上面的菜单栏【买家催发货】按钮
    BUYER_PUSH_SHIPMENT_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.hG9LxqEdILY0Pqokq_kY > div > div > div > div:nth-child(1)'

    # 订单列表上面的菜单栏【24h需发货】按钮
    # REQUIRED_SHIPMENT_IN_24HOURS_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.hG9LxqEdILY0Pqokq_kY > div > div > div > div:nth-child(2)'
    # 快筛第三个
    REQUIRED_SHIPMENT_IN_24HOURS_BUTTON = "//div[@class='iR9LcaFulxHgxn7c5HLE']//div[3]//div[1]"

    # 订单列表上面的菜单栏【超时未发货】按钮
    # NO_SHIPMENT_AFTER_TIME_LIMIT_BUTTON = "#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.hG9LxqEdILY0Pqokq_kY > div > div > div > div:nth-child(3)"
    # 快筛第六个
    NO_SHIPMENT_AFTER_TIME_LIMIT_BUTTON = "//div[@class='iR9LcaFulxHgxn7c5HLE']//div[6]//div[1]"

    # 订单列表上面的菜单栏【优先发货】按钮
    PRIORITY_SHIPMENT_BUTTON = "#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.hG9LxqEdILY0Pqokq_kY > div > div > div > div:nth-child(4)"

    # 订单列表上面的【订单编号】输入框
    ORDER_ID_INPUT_BOX_SELECTOR = '#valueMixed'

    # 订单列表上面的菜单栏【展开全部】按钮
    # MORE_FILTERING_BUTTON = '#pro-form-wrapper > div:nth-child(3) > div.ant-col.ant-col-8.ant-col-offset-16 > div > div.ant-pro-form-layout-collapse'
    MORE_FILTERING_BUTTON = "//div[@class='ant-pro-form-layout-collapse']"

    # 订单列表上面的菜单栏【收起筛选】按钮
    # COLLAPSE_FILTERING_BUTTON = '//*[@id="pro-form-wrapper"]/div[7]/div[3]/div/div[2]'
    COLLAPSE_FILTERING_BUTTON = "//div[@class='ant-pro-form-layout-collapse']"

    # 订单列表上面菜单栏【订单状态】下拉框
    ORDER_STATUS_SELECTOR = '//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【售后状态】下拉框
    AFTER_SALES_SELECTOR = '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div'

    # 订单列表上面菜单栏【插旗备注】下拉框
    INSERT_FLAG_REMARKS_TEXT = '//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div[1]/label'
    INSERT_FLAG_REMARKS_SELECTOR = '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div/div'

    # 订单列表上面菜单栏【商品名称】搜索框
    # GOODS_NAME_INPUT_BOX_SELECTOR = 'div.ant-row:nth-child(2) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > span:nth-child(1) > input:nth-child(1)'
    GOODS_NAME_INPUT_BOX_SELECTOR = '#pro-form-wrapper > div:nth-child(1) > div:nth-child(3) > div > div.ant-col.ant-form-item-control > div > div > span > input'

    # 订单列表上面菜单栏【渠道】下拉框
    CHANNEL_SELECTOR = '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【快递单号】输入框
    DELIVERY_ID_INPUT_BOX_SELECTOR = 'div.ant-row:nth-child(3) > div:nth-child(1) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > span:nth-child(1) > input:nth-child(1)'

    # 订单列表上面菜单栏【收货人姓名/手机号】输入框
    NAME_OR_PHONE_INPUT_BOX_SELECTOR = 'div.ant-row:nth-child(3) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > span:nth-child(1) > input:nth-child(1)'

    # 订单列表上面菜单栏【推广者ID】下拉框
    PROMOTER_ID_SELECTOR = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div[2]/div/div/span/div/div/span[2]'

    # 订单列表上面菜单栏【活动类型】下拉框
    ACTIVITY_ORDER_SELECTOR = '//*[@id="pro-form-wrapper"]/div[4]/div[1]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【待发货时间】下拉框
    SHIPPING_TIME_SELECTOR = '//*[@id="pro-form-wrapper"]/div[4]/div[2]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【卖家昵称/ID】输入框
    # BUYER_NICKNAME_ID_INPUT_BOX_SELECTOR = 'div.ant-row:nth-child(5) > div:nth-child(1) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > span:nth-child(1) > input:nth-child(1)'
    BUYER_NICKNAME_ID_INPUT_BOX_SELECTOR = "//input[@placeholder='请输入买家昵称/ID']"
    # 订单列表上面的菜单栏【订单标签】下拉框
    ORDER_LABEL_TEXT = '//*[@id="pro-form-wrapper"]/div[5]/div[2]/div/div[1]/label'
    ORDER_LABEL_SELECTOR = '//*[@id="pro-form-wrapper"]/div[5]/div[2]/div/div[2]/div/div/div'

    # 订单列表上面菜单栏【报备类型】下拉框
    REPORTING_TYPE_SELECTOR = '//*[@id="pro-form-wrapper"]/div[5]/div[2]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【买家催发货】按钮
    # EXPEDITE_SHIPPING_SELECTOR = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[3]/div/div/div/div[1]/div[1]'
    # 快筛第二个
    # EXPEDITE_SHIPPING_SELECTOR = '/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]/section[1]/section[1]/main[1]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/div[2]/div[1]'
    EXPEDITE_SHIPPING_SELECTOR = "//div[@class='iR9LcaFulxHgxn7c5HLE']//div[2]//div[1]"

    # 订单列表上面菜单栏【延长自动收货时间】下拉框
    EXTEND_AUTO_SIGNED_TIME_SELECTOR = '//*[@id="pro-form-wrapper"]/div[6]/div[1]/div/div[2]/div/div/div/div/span[2]'
    # EXTEND_AUTO_SIGNED_TIME_SELECTOR = '//*[@id="pro-form-wrapper"]/div[6]/div[1]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【隐私授权状态】下拉框
    PRIVACY_AUTHORIZATION_STATUS = '//*[@id="pro-form-wrapper"]/div[7]/div[1]/div/div[2]/div/div/div/div/span[2]'

    # 订单列表上面菜单栏【优先发货】按钮
    # PRIORITY_SHIPPING_SELECTOR = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[3]/div/div/div/div[4]/div[1]'
    # 快筛第四个
    # PRIORITY_SHIPPING_SELECTOR = '/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]/section[1]/section[1]/main[1]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/div[4]/div[1]'
    PRIORITY_SHIPPING_SELECTOR = "//div[@class='ogP6JUfZShyGYrIXriwi'][contains(text(),'优先发货')]"

    # 订单列表上面菜单栏【保证金退款】勾选框
    # SECURITY_DEPOSIT_CHECKBOX = '//*[@id="pro-form-wrapper"]/div[7]/div[1]/div/div[2]/div/div/div/label[1]/span[2]'
    SECURITY_DEPOSIT_CHECKBOX = "//span[contains(text(),'保证金退款')]"

    # 订单列表上面菜单栏【退货补运费】勾选框
    # RETURN_SHIPPING_SUPPLEMENT_CHECKBOX = '//*[@id="pro-form-wrapper"]/div[7]/div[1]/div/div[2]/div/div/div/label[2]/span[1]/span'
    RETURN_SHIPPING_SUPPLEMENT_CHECKBOX = "//label[@class='ant-checkbox-wrapper ant-checkbox-group-item']//span[contains(text(),'退货补运费')]"

    # 订单列表上面的菜单栏【重 置】按钮
    # RESETTING_BUTTON = "#pro-form-wrapper > div:nth-child(3) > div.ant-col.ant-col-8.ant-col-offset-16 > div > div.ant-space.ant-space-horizontal.ant-space-align-center > div:nth-child(1) > button > span"
    RESETTING_BUTTON = "//span[contains(text(),'重置')]"

    # 订单列表上面的菜单栏【查 询】按钮
    SEARCH_BUTTON = '//*[@id="pro-form-wrapper"]/div[3]/div[4]/div/div[1]/div[2]/button'
    # SEARCH_BUTTON_MORE_FILTERING = '/html/body/div[1]/div/div/div[1]/div[2]/div[2]/div/div/div/div/section/section/main/div/div/div[2]/div[4]/div/form/div[7]/div[3]/div/div[1]/div[2]/button'
    SEARCH_BUTTON_MORE_FILTERING = "//button[@type='submit']//span[contains(text(),'查询')]"
    """对订单详情进行筛选的按钮"""
    # 对订单详情进行筛选的【全 部】按钮
    ALL_ORDER_BUTTON = '#rc-tabs-1-tab-0'

    # 对订单详情进行筛选的【待付款】按钮
    AWAITING_PAYMENT_ORDER_BUTTON = '#rc-tabs-1-tab-1'

    # 对订单详情进行筛选的【待发货】按钮
    AWAITING_SHIPMENT_ORDER_BUTTON = '#rc-tabs-1-tab-2'

    # 待发货Tab 下【下单/发货时间】下拉框
    # PAYMENT_DELIVERY_TIME_SELECTOR = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/div/div/span[2]'
    PAYMENT_DELIVERY_TIME_SELECTOR = "// span[ @ title = '最新下单时间在上']"

    # 待发货Tab 下【部分发货】勾选框
    # PARTIAL_DELIVERY_CHECKBOX = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/label/span[1]/span'
    PARTIAL_DELIVERY_CHECKBOX = "// span[contains(text(), '部分发货')]"

    # 对订单详情进行筛选的【已发货】按钮
    SHIPMENT_DISPATCHED_ORDER_BUTTON = '#rc-tabs-1-tab-3'

    # 对订单详情进行筛选的【已收货】按钮
    RECEIVED_ORDER_BUTTON = '#rc-tabs-1-tab-4'

    # 对订单详情进行筛选的【交易成功】按钮
    SUCCESSFUL_TRANSACTION_ORDER_BUTTON = '#rc-tabs-1-tab-5'

    # 对订单详情进行筛选的【订单关闭】按钮
    CLOSED_ORDER_BUTTON = '#rc-tabs-1-tab-6'

    # 订单列表上方的【在线寄件】按钮
    ONLINE_DELIVERY_BUTTON = '#order-list-online-delivery'

    # 对订单详情进行筛选的【导出数据】按钮
    ORDER_BATCH_EXPORT_BUTTON = '#order-list-export-button'

    # 对订单详情进行筛选的【查看已导出报表】按钮
    # VIEW_EXPORTED_REPORT_BUTTON = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[5]'
    VIEW_EXPORTED_REPORT_BUTTON = "// span[contains(text(), '查看已导出报表')]"

    # 对订单详情进行筛选的【合并发货】按钮
    # COMBINED_SHIPMENT_BUTTON = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[4]'
    COMBINED_SHIPMENT_BUTTON = "// span[contains(text(), '合并发货')]"

    # 对订单详情进行筛选的【批量发货】按钮
    BATCH_SHIPMENT_BUTTON = '#import-excel-driver-share > span'
    BATCH_SHIPMENT_POPUP = '#rcDialogTitle1'

    # 对订单详情进行筛选的【打单发货】按钮
    # PRINT_WAYBILL_BUTTON = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[3]'
    PRINT_WAYBILL_BUTTON = "// button[ @ id = 'printWaybill'] // span[contains(text(), '打单发货')]"

    """订单列表详情"""
    # 订单列表详情 查看订单详情的【具体的订单编号一串数字】按钮
    ORDER_NUMBER_BUTTON = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[1]/div/div/div[1]/div/div[1]/div/a'

    # 商品单价
    PRODUCT_PRICE = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div/div[2]/div[1]/span'

    # 商品数量
    PRODUCT_NUMBER = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div/div[2]/div[2]/div[2]'

    # 商品规格
    PRODUCT_SKU = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div/div[2]/div[2]/div[1]/div'

    # 退货补运费权益按钮
    INSURANCE_BACK_FEE = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div/div[2]/div[3]/div[1]/span[1]'

    # 订单列表详情 查看历史备注【一面小旗】按钮
    VIEW_HISTORICAL_NOTES_BUTTON = 'MworhitIx37x9WomffRG'
    VIEW_HISTORICAL_NOTES_POPUP = 'kwaishop-trade-b-order-note-edit-pc-modal-content'

    # 订单列表详情 点击商品名-进入商品详情的按钮
    GOODS_DETAILS_BUTTON = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div/div[2]/div[1]/a'

    # 订单状态
    ORDER_STATUS = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[1]/div/div'


    # 订单状态副标题
    ORDER_SUB_TITLE = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[2]/div/div/span'

    # 订单列表详情 待付款订单的【修改价格】按钮
    PRICE_CHANGE_POPUP = 'body > div:nth-child(9) > div > div.ant-modal-wrap.ant-modal-centered > div > div.ant-modal-content > button'

    # 订单列表详情 待付款订单的【同意退定金】按钮
    AGREE_REFUND_DEPOSIT_POPUP = 'body > div:nth-child(10) > div > div.ant-modal-wrap > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button.ant-btn.ant-btn-primary > span'

    # 订单列表详情 待付款订单的【关闭交易】按钮
    # CLOSE_TRANSACTION_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(2) > div > div > div > div > div:nth-child(2) > div:nth-child(1) > div.ant-pro-list-row-content > div:nth-child(6) > div > div > button.ant-btn.ant-btn-link.sc-fFeiMQ.pvJig > span'
    CLOSE_TRANSACTION_POPUP = 'body > div:nth-child(7) > div > div.ant-modal-wrap.ant-modal-centered > div > div.ant-modal-content'

    # 订单列表详情 待发货订单的【退货补服务费--平台出资】按钮
    RETURN_SHIPPING_FEES_BUTTON = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[1]/div/div[2]/div[2]/div[2]/div/div[5]/div/span[2]/a'

    # 订单列表详情 待发货订单的【发货】按钮
    GOODS_DISPATCH_POPUP = '/html/body/div[5]/div/div[2]/div/div[2]'

    # 订单列表 订单/售后状态 文案
    ORDER_AFTER_SALE_CONTENT = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[1]/div/div'

    # 订单列表详情 已发货订单的【追加包裹】按钮
    ADDITIONAL_PACKAGE_POPUP = 'body > div:nth-child(8) > div > div.ant-modal-wrap > div > div.ant-modal-content'

    # 订单列表详情 已发货订单的【延长收货时间】按钮
    # EXTEND_RECEIPT_TIME_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(2) > div > div > div > div > div:nth-child(2) > div:nth-child(2) > div.ant-pro-list-row-content > div:nth-child(6) > div > div > button:nth-child(2) > span'
    EXTEND_RECEIPT_TIME_POPUP = '#rcDialogTitle1'

    # 订单列表详情 已发货订单的【修改物流】按钮
    CHANGE_LOGISTICS_POPUP = 'body > div:nth-child(7) > div > div.ant-drawer-content-wrapper > div > div'

    # 订单列表详情 交易成功订单的【打开售后入口】按钮
    # AFTER_SALE_ENTRANCE_BUTTON = '#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(2) > div > div > div > div > div:nth-child(2) > div:nth-child(5) > div.ant-pro-list-row-content > div:nth-child(6) > div > div > button:nth-child(2)'
    AFTER_SALE_ENTRANCE_POPUP = 'body > div:nth-child(7) > div > div.ant-modal-wrap > div > div.ant-modal-content > div'

    # 商家助手
    merchant_assistant = '#kpro-tool-box--sellerHelperBox'

    # 商家助手关闭button
    merchant_assistant_close_button = '#kpro-tool-box--sellerHelperBox > div:nth-child(1) > img'

    order_prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
    order_tail = ']/div[2]/div[6]/div/div/button[1]'