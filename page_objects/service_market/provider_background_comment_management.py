class ProviderBackgroundCommentManagement(object):
    """服务商后台-评价管理"""

    # 查询按钮
    select_button = 'button.ant-btn.ant-btn-primary'

    # 重置按钮
    remark_button = 'button.ml8.ant-btn'

    # 评价列表订单编号列
    order_id_list = 'div> div.item-header.text > span:nth-child(1)'

    # 服务名称下拉框
    service_name_down_box = 'div:nth-child(2) > div.ant-col.ant-form-item-control-wrapper > div > span > div > div'

    # 服务功能下拉框
    function_down_box = 'div:nth-child(3) > div.ant-col.ant-form-item-control-wrapper > div > span > div > div'

    # 使用效果下拉框
    effect_down_box = 'div:nth-child(4) > div.ant-col.ant-form-item-control-wrapper > div > span > div > div'

    # 服务态度下拉框
    attitude_down_box = 'div:nth-child(5) > div.ant-col.ant-form-item-control-wrapper > div > span > div > div'

    # 订单编号输入框
    order_id_input = "input[placeholder='请输入订单编号']"

    # 服务名称下拉框的内容
    service_name_down_box_content_list = 'div.rc-virtual-list-holder > div > div > div > div'

    # 评论列表-服务名称
    comment_list_service_name_list = 'div > div.item-body.text > div.item.img-wrapper > div > div.commend-servicename.mt8.subtext'

    # 服务功能下拉框内容
    service_function_down_box_content_list = 'body > div:nth-child(4) > div > div > div > div.rc-virtual-list > div.rc-virtual-list-holder > div > div > div > div'

    # 评价列表第一条评价的服务功能分数的第二个星星
    comment_list_first_row_service_function_second_score = ' div:nth-child(1) > div.item-body.text > div.item.comment-item > div:nth-child(1) > div:nth-child(2) > ul > li:nth-child(2) > div'

    # 使用效果下拉框内容
    effect_down_box_content_list = 'body > div:nth-child(4) > div > div > div > div.rc-virtual-list > div.rc-virtual-list-holder > div > div > div > div'

    # 评价列表第一条评价的使用效果分数的第二个星星
    comment_list_first_row_effect_second_score = 'div:nth-child(3) > ul > li:nth-child(2) > div'

    # 服务态度下拉框内容
    attitude_down_box_content_list = 'body > div:nth-child(4) > div > div > div > div.rc-virtual-list > div.rc-virtual-list-holder > div > div > div > div'

    # 评价列表第一条评价的服务态度分数的第二个星星
    comment_list_first_row_attitude_second_score = 'div:nth-child(1) > div.item-body.text > div.item.comment-item > div:nth-child(1) > div:nth-child(4) > ul > li:nth-child(2) > div'
