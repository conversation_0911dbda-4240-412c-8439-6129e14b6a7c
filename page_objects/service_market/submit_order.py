class SubmitOrder(object):
    """提交订单页面"""

    # 协议勾选框
    check_box = '[class="ant-checkbox"]'

    # 提交订单button
    submit_order_button = "//span[text()='提交订单']"

    # 服务套餐名
    service_package_name = 'div.service-packageName'

    # 时间类型服务周期数
    time_service_service_cycle = 'td:nth-child(3)'

    # 服务价格
    service_price = 'span.total-price-number'

    # pc端收银台的"支付"title
    pc_cash_register_title = '[class="ebank-title svelte-1pjs8lg"]'

    # 客服外包服务价格
    customer_service_outsourcing_service_price = 'span.total-price-number'

    # 代运营服务签署合同页面文案
    agent_operation_service_sign_contract_message = '[class="title"]'

    # 代运营服务详细需求输入框
    agent_operation_service_detail_demand_input = '[placeholder="请详细描述您的需求，如真实使用场景、预期达到的效果等"]'

    # pc端收银台展示的订单号
    pc_cash_register_order_id = 'dl:nth-child(2) > dd'

    # 0元下单button
    free_submit_order_button = "//span[text()='0元下单']"
