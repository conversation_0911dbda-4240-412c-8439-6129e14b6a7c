class MerchantBackgroundToolService(object):
    """商家后台-工具服务页面"""

    # 工具服务列表的第一行的操作button列表
    tool_service_first_operate_button_list = 'tr:nth-child(1) > td:nth-child(4)'

    # "使用服务"button
    use_service_button = "//span[text()='使用服务']"

    # 生效中tab
    effecting_tab = "//div[text()='生效中']"

    # 已过期tab
    expired_tab = "//div[text()='已过期']"

    # 已失效tab
    lose_effect_tab = "//div[text()='已失效']"

    # 已失效tab下文案
    lose_effect_tab_message = "//div[contains(text(),'如果您订购的服务还在有效期内，同时订购新套餐，则原服务将失效。仅支持查询')]"

    # 生效中时效列
    effecting_time_list = "//span[text()='距服务到期还有']"

    # 已过期去续费button列
    expired_go_to_buy_button = "//span[text()='立即续费']"

    # 服务列表服务名称列
    service_list_service_name_list = 'tbody > tr> td:nth-child(1) > div > div > div:nth-child(1)'

    # 服务名称输入框
    service_name_input = "input[placeholder='请输入服务名称']"

    # 查询button
    select_button = "//span[text()='查 询']"

    # 重置button
    remark_button = "//span[text()='重 置']"

    # 工具服务title
    service_info_title = "//div[text()='工具服务']"

    # 服务详情button
    service_detail_button = "//span[text()='服务详情']"
