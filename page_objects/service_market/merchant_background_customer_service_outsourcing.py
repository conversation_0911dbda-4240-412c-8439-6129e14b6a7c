class MerchantBackgroundCustomerServiceOutsourcing(object):
    """商家后台-客服外包页面"""

    # 全部订单tab
    all_order_tab = "//span[text()='全部订单']"

    # 待支付ab
    wait_pay_order_tab = "//span[text()='待支付']"

    # 合同准备中tab
    prepare_contract_tab = "//span[text()='合同准备中']"

    # 合同待签署tab
    wait_sign_contract_tab = "//span[text()='合同待签署']"

    # 待服务tab
    wait_served_tab = "//span[text()='待服务']"

    # 服务中tab
    in_service_tab = "//span[text()='服务中']"

    # 已结束tab
    close_tab = "//span[text()='已结束']"

    # 已售后
    refund_tab = "//span[text()='已售后']"

    # 订单状态列
    order_status_list = 'tr > td:nth-child(9)'

    # 订单概览tab
    order_overview_tab = "//div[text()='订单概览']"

    # 效果数据tab
    perform_data_tab = "//div[text()='效果数据']"

    # 子账号选择框
    sub_account_box = "//label[text()='子账号']"

    # 订单号下拉框
    order_id_down_box = "div:nth-child(1) > div.ant-col.ant-form-item-control > div > div > div > div > span.ant-select-selection-search"

    # 订单号下拉框内容列
    order_id_down_box_content_list = 'div.rc-virtual-list > div > div > div > div'

    # 客服承接方
    customer_service_undertaker = "//span[text()='客服承接方']"

    # 客服外包title
    customer_service_outsourcing_title = "//div[text()='客服外包']"
