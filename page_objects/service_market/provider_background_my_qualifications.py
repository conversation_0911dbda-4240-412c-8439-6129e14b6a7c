class ProviderBackgroundMyQualifications(object):
    """服务商后台-我的资质"""

    # 申请资质弹窗服务简介输入框
    apply_qualifications_popup_service_info_input = '#serviceDescription'

    # 申请资质弹窗申请理由输入框
    apply_qualifications_popup_apply_reason_input = '#applyReason'

    # 申请资质弹窗补充资料上传框
    apply_qualifications_popup_additional_materials_upload_box = 'input[type="file"]'

    # 申请资质弹窗服务关系证明上传框
    apply_qualifications_popup_additional_materials_upload_flie_box = '#serviceRelationshipProof >div >span >div >span > input[type="file"]'

    # 申请资质弹窗的确定button
    apply_qualifications_popup_confirm_button = "//span[text()='确 定']"

    # 资质列表第一行的申请状态
    qualifications_list_first_row_apply_status = 'tr:nth-child(1) > td:nth-child(3)'

    # 资质列表的资质类型列
    qualifications_type_list = 'tr> td:nth-child(2)'

    # 资质列表的申请状态列
    qualifications_apply_status_list = 'tr> td:nth-child(3)'

    # 资质列表的操作按钮列
    qualifications_operate_button_list = 'tr> td:nth-child(7)'

    # 已申请服务资质tab
    applied_for_service_qualification_tab = "//div[text()='已申请服务资质']"

    # 待申请服务资质tab
    to_be_applied_for_service_qualification_tab = "//div[text()='待申请服务资质']"

    # 查看资质要求button
    view_qualification_requirements_button = "//a[text()=' 查看资质要求 ']"
