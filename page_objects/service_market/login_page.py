class LoginPage(object):
    """服务市场登录页面"""

    # 主账号登录button
    login_button = "//div[text()='我是店主或服务商']"

    # 子账号登录button
    subAccount_login_button = "//div[text()='我是员工']"

    # 扫码登录tab
    scan_code_login_tab = "//div[text()='扫码登录']"

    # 手机号登录tab
    phone_num_login_tab = "//div[text()='手机号登录']"

    # 账号登录tab
    account_login_tab = "//div[text()='账号登录']"

    # 验证码登录tab
    verification_code_login_tab = "//div[text()='验证码登录']"

    # "登录"button
    login_button_2 = "//span[text()='登 录']"

    # 选择账号

    choochoose_account = "//div[text()='一阵风奔跑过']"
    # "确定"button
    login_button_3 = "//span[text()='确 定']"

    # 手机号输入框
    phone_input = "input[placeholder='请输入手机号']"

    # 密码输入框
    password_input = "input[placeholder='请输入密码']"

    # "登录"button
    confirm_button = "//span[text()='确 定']"
