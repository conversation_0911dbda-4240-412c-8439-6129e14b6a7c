class ProviderBackgroundOrderTrack(object):
    """服务商后台-订单查询"""

    # 修改订单价格弹窗的确定button
    modify_price_popup_confirm_button = "//span[text()='确 定']"

    # 改价成功弹窗文案
    modify_price_success_popup_message = 'div.ant-modal-confirm-body > div > div > div'

    # 改价成功弹窗确定button
    modify_price_success_popup_confirm_button = "body > div:nth-child(8) > div > div.ant-modal-wrap.ant-modal-centered.ant-modal-confirm-centered > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button"

    # 订单应付金额
    order_payment = 'td:nth-child(8) > span'

    # 查询button
    track_button = "//span[text()='查 询']"

    # 订单列表的订单编号列
    order_id_list = 'tr> td:nth-child(1) > div > div.img-wrapper > div > div.text.order-number'

    # 重置按钮
    remark_button = "//span[text()='重 置']"

    # 导出按钮
    export_button = "//span[text()='导 出']"

    # 导出记录按钮
    export_record_button = "//span[text()='导出记录']"

    # 导出成功消息
    export_success_message = 'div.ant-notification-notice-message'

    # 订单列表订单状态列
    order_status_list = 'tr > td:nth-child(9) > span'

    # 待支付tab
    wait_pay_tab = "//div[text()='待支付']"

    # 支付成功tab
    pay_success_tab = "//div[text()='支付成功']"

    # 已售后tab
    refund_tab = "//div[text()='已售后']"

    # 已关闭tab
    closed_tab = "//div[text()='已关闭']"

    # 订单列表的商家名称列
    merchant_name_list = 'tr > td:nth-child(4) > div.text > div.buyerShopName'

    # 订单列表的服务名称列
    service_name_list = 'tr> td:nth-child(1) > div > div.img-wrapper > div > div.serviceName'

    # 订单号输入框
    order_id_input = "input[placeholder='请输入订单编号']"

    # 商家名称输入框
    merchant_name_input = "input[placeholder='请输入商家名称']"

    # 商家名称输入框
    service_name_input = "input[placeholder='请输入商品名称']"

    # 订单改价button
    change_order_price_button = "#app > div > section > div > section > section > main > div.order-manage-contain > div:nth-child(1) > div.ant-table-wrapper.order-list > div > div > div > div > div.ant-table-fixed-right > div > div > table > tbody > tr > td > div > div.action"

    # 订单改价价格输入框
    change_order_price_input = "input[placeholder='请输入修改后的价格']"

    # 下载按钮
    download_button = "//span[text()='下 载']"
