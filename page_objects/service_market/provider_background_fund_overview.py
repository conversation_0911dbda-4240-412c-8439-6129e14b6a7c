class ProviderBackgroundFundOverview(object):
    """服务商后台-资金总览"""

    # 未提现金额
    unsettled_amount = 'div.unsettledAmount > div.number'

    # "提现"button
    withdraw_button = "//span[text()='提 现']"

    # 店铺资金提现弹窗的"提现"button
    fund_withdraw_popup_withdraw_button = 'div.ant-modal-footer > div > button.ant-btn.ant-btn-primary'

    # 前端提示
    message = '[class="ant-message-notice"]'

    # 订单列表订单编号列
    order_list_order_id_list = ' tr > td:nth-child(1) > div > div > p:nth-child(2)'

    # 查询button
    select_button = "//span[text()='查 询']"

    # 重置button
    remark_button = "//span[text()='重 置']"

    # 导出button
    export_button = "//span[text()='导 出']"

    # 导出记录button
    export_record_button = "//span[text()='导出记录']"

    # 订单编号输入框
    order_id_input = "input[placeholder='请输入订单编号']"

    # 结算状态下拉框
    settlement_status_down_box = 'span > div > div'

    # 结算状态-已入账
    settlement_status_completed = 'div.rc-virtual-list-holder > div > div > div:nth-child(3)'

    # 订单列表结算状态列
    order_list_settlement_status_list = 'tr > td:nth-child(10) > div'

    # 导出成功消息
    export_success_message = 'div.ant-notification-notice-message'

    # 提现记录button
    withdraw_record_button = "//span[text()='提现记录']"

    # 提现记录列
    withdraw_record_list = 'table > tbody > tr'

    # 每月账单tab
    month_bill_tab = "//div[text()='每月账单']"

    # 订单结算tab
    order_settle_tab = "//div[text()='订单结算']"

    # 每月账单-导出记录button
    month_bill_export_record_button = 'div.ant-tabs-tabpane.ant-tabs-tabpane-active > div:nth-child(2) > button'

    # 每月账单-导出记录-申请导出时间列
    month_bill_export_record_apply_export_time = ' div.exportrecord-contain > div:nth-child(1) > div> div'

    # 支付宝账号输入框
    ali_pay_account_input = "input[placeholder='请输入支付宝账号']"

    # 提现金额输入框
    withdraw_money_input = "input[placeholder='请输入提现金额']"

    # 验证码输入框
    check_code_input = "input[placeholder='请输入验证码']"

    # 下载按钮
    download_button = "//span[text()='下 载']"

    # 导出账单button
    export_bill_button = "//span[text()='导出账单']"
