class ServiceDetail(object):
    """服务详情页面"""

    # "联系服务商"button
    contact_service_provider_button = "//button[@class='ant-btn ant-btn-secondary contactBtn___AdPbT']"

    # 服务商头像
    provider_logo = "//img[@class='shopLogo___iObir']"

    # 时间类型第二个套餐
    time_service_second_package = 'div:nth-child(2) > div:nth-child(4) > div > span:nth-child(2)'

    # 服务名
    service_name = ' div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1)'

    # 时间类型服务套餐2下的第二个周期
    time_service_second_package_second_cycle = 'div:nth-child(2) > div:nth-child(5) > div > span:nth-child(2)'

    # 立即购买button
    submit_order_button = "//span[text()='立即购买']"

    # 次数类型服务的数字选择器的+号
    num_service_add_num_button = '[class="ant-input-number-handler ant-input-number-handler-up"]'

    # 客服外包服务人数的数字选择器的+号
    customer_service_outsourcing_service_people_add_num_button = 'div:nth-child(7) > div > div > div:nth-child(1) > span:nth-child(1)'

    # 客服外包服务周期的数字选择器的+号
    customer_service_outsourcing_service_cycle_add_num_button = 'div:nth-child(6) > div > div > div:nth-child(1) > span:nth-child(1)'

    # 客服外包服务服务时间选择框
    customer_service_outsourcing_service_time_select_box = '[placeholder="请选择服务开始日期"]'

    # 客服外包服务服务时间选择框的今天button
    customer_service_outsourcing_service_time_select_box_today_button = "//a[text()='今天']"

    # 代运营服务按合同收费文案提示
    agent_operation_service_charge_by_contract_message = '#main_root > div > div.yPXHTHq9cx_6vEhWohw_ > div.Jb15b0XMeXvmyWfEfrvq > div.m5IIGVbmvqsfZdb00GFO > div.JktibAFTihy_wzOaJXHl > div.Cbz2c2xjrwvEPJnVK4iA > div.KY1LeQ_L_xVgT9RzkLow.cgxWbYdA4fs28QuNN36F > div > div > span'

    # 图片视频服务的该服务商其他视频区域title
    picture_and_video_service_other_video_title = '#main_root > div > div:nth-child(3) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1)'

    # 0元下单按钮
    free_submit_order_button = "//span[text()='0元下单']"
