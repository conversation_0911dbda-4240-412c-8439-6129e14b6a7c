class ProviderBackgroundSubAccountManagement(object):
    """服务商后台-子账号管理"""

    # "角色"tab
    role_tab = "//div[text()='角色']"

    # "新增角色"button
    add_role_button = "//span[text()='新增角色']"

    # "角色名称"输入框
    role_name_input = '#roleName'

    # "角色简介"输入框
    role_profile_input = '#roleDesc'

    # "角色权限"下拉框
    role_permissions_drop_down_box = 'div > span > div > span'

    # "订单管理"权限选择框
    order_management_role_permissions = 'li:nth-child(1) > span.ant-select-tree-checkbox > span'

    # 创建角色弹窗的"确定"button
    add_role_popup_confirm_button = "//span[text()='确 定']"

    # 前端提示
    message = '[class="ant-message-notice"]'

    # 删除角色弹窗的"确认"button
    popup_confirm_button = "//span[text()='确 认']"

    # 创建子账号button
    create_subAccount_button = "//span[text()='创建子账号']"

    # "账号名称"输入框
    subAccount_name_input = '#name'

    # "备注"输入框
    remark_input = '#remarks'

    # "角色"下拉框
    role_drop_down_box = 'div > span > div > div'

    # "角色"下拉框中的第一个角色
    role_drop_down_box_role_list = ' div.ant-select-item.ant-select-item-option.ant-select-item-option-active'

    # "登录手机"输入框
    login_phone_input = '#phone'

    # 创建子账号弹窗的"确定"button
    create_subAccount_popup_confirm_button = "//span[text()='确 定']"

    # 角色列表的角色名称列
    role_name_list = 'div:nth-child(3) > div > div > div > div > div > div > table > tbody > tr > td:nth-child(1)'

    # 角色列表的编辑按钮列（第一行没有按钮）
    role_edit_button_list = 'table > tbody > tr> td:nth-child(4) > button:nth-child(1)'

    # 角色列表的删除按钮列（第一行没有按钮）
    role_delete_button_list = 'table > tbody > tr> td:nth-child(4) > button:nth-child(2)'

    # 获取子账号列表的账号名称列
    subAccount_name_list = "div:nth-child(3) > div > div > div > div > div > div > table > tbody > tr> td:nth-child(1)"

    # 获取子账号列表的删除按钮列
    subAccount_delete_button_list = 'table > tbody > tr> td:nth-child(6) > button:nth-child(1)'

    # 获取子账号列表的编辑按钮列
    subAccount_edit_button_list = 'table > tbody > tr> td:nth-child(6) > button:nth-child(2)'

    # 获取子账号列表的停用/启用按钮列
    subAccount_enable_and_disable_button_list = 'table > tbody > tr> td:nth-child(6) > button:nth-child(3)'

    # 获取子账号列表的账号状态
    subAccount_status_list = ' tr> td:nth-child(5) > div'
