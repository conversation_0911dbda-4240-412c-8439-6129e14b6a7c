class MerchantBackgroundMyOrder(object):
    """商家后台-我的订单页面"""

    # 订单列表查询button
    order_list_select_button = "//span[text()='查 询']"

    # 订单状态-全部订单
    all_order_status_tab = "//div[text()='全部订单']"

    # 订单状态-待支付
    wait_pay_order_status_tab = "//div[text()='待支付']"

    # 订单状态-支付成功
    success_pay_order_status_tab = "//div[text()='支付成功']"

    # 订单状态-已售后
    refund_order_status_tab = "//div[text()='已售后']"

    # 订单状态-已关闭
    close_order_status_tab = "//div[text()='已关闭']"

    # 订单列表订单状态列
    order_list_order_status_list = 'tr > td:nth-child(9) > span > span > span.ant-badge-status-text > span'

    # 订单列表订单号列
    order_list_order_id_list = 'div.ant-pro-list-row-header > div > div > div:nth-child(1) > span:nth-child(1)'

    # 订单编号输入框
    order_id_input = "input[placeholder='请输入订单编号']"

    # 重置button
    remark_button = "//span[text()='重 置']"

    # 复制button列
    copy_button_list = 'div:nth-child(1) > span.copy-url > span > svg'

    # 我的订单title
    my_order_title = "//div[text()='我的订单']"

    # 联系服务商button
    im_button = "//div[text()='在线沟通']"

    # 联系服务商图标
    im_img = "tr > td:nth-child(8) > div > div:nth-child(1) > img"
