class ProviderBackgroundDeposit(object):
    """服务商后台-保证金"""

    # "充值"button
    deposit_recharge_button = "//span[text()='充 值']"

    # 保证金充值弹窗的输入框
    deposit_recharge_popup_input = '[role="spinbutton"]'

    # 保证金充值弹窗的共计支付金额
    deposit_recharge_popup_all_payment = 'div:nth-child(3) > div.ant-col.ant-col-14.ant-form-item-control-wrapper > div > span > div'

    # 保证金充值弹窗的"充值"button
    deposit_recharge_popup_recharge_button = 'div.ant-modal-footer > div > button.ant-btn.ant-btn-primary'

    # pc端收银台的"支付"title
    pc_cash_register_title = '[class="ebank-title svelte-1pjs8lg"]'

    # "提退"button
    deposit_withdraw_button = "//span[text()='提 退']"

    # 保证金提退弹窗的"提退"button
    deposit_withdraw_popup_withdraw_button = 'div.ant-modal-footer > div > button.ant-btn.ant-btn-primary'

    # 前端提示
    message = '[class="ant-message"]'

    # 常见问题
    normal_problem = "//a[text()=' 常见问题 ']"

    # 查看电子凭证列
    select_electronic_certificate_list = 'tr> td:nth-child(8) > span'

    # 保证金凭证弹窗交款方
    payment_account = "//span[text()='交款方']"

    # 查询button
    select_button = 'span > button:nth-child(1)'

    # 重置button
    remark_button = 'span > button:nth-child(2)'

    # 导出button
    export_button = "//span[text()='导 出']"

    # 导出记录button
    export_record_button = 'button:nth-child(4)'

    # 下载button
    download_button = "//span[text()='下 载']"

    # 导出失败button
    download_fail_button = "//span[text()='导出失败']"

    # 操作类型下拉框
    operate_down_box = ' div:nth-child(2) > div.ant-col.ant-form-item-control-wrapper > div > span > div > div'

    # 操作类型-充值
    operate_charge = 'div.rc-virtual-list-holder > div > div > div:nth-child(2) > div'

    # 操作类型列
    operate_type_list = 'tr> td:nth-child(4) > span'

    # 弹窗确认button
    popup_confirm_button = "//span[text()='确 认']"

    # 支付宝账号输入框
    ali_pay_account_input = "input[placeholder='请输入支付宝账号']"

    # 提退金额输入框
    withdraw_money_input = "input[placeholder='请输入提退金额']"

    # 验证码输入框
    check_code_input = "input[placeholder='请输入验证码']"

