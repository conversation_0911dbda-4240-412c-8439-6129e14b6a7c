class ProviderBackgroundCustomerServiceSettings(object):
    """服务商后台-客服设置"""

    # 离线自动回复tab
    offline_auto_reply_tab = "//span[text()='离线自动回复']"

    # 回复列表title
    reply_list_title = "//span[text()='回复列表']"

    # 快捷回复tab
    fast_reply_tab = "//span[text()='快捷回复']"

    # 离线自动回复button
    offline_auto_reply_button = 'div.WutX7dRwWWzd2u5J8cVv > button'

    # 保存并发布button
    save_and_publish_button = "//span[text()='保存并发布']"

    # 离线自动回复输入框
    offline_auto_reply_input = 'span > textarea'

    # 快捷回复列表的快捷回复分组标题
    fast_reply_group_title_list = 'div> div.kwaishop-open-fuwu-pc-micro-collapse-header > div.Duh4csTL9n8PPZbSxDFd > div:nth-child(1) > span:nth-child(1)'

    # 快捷回复列表的删除列
    delete_fast_reply_group_list = 'div> div > div.Duh4csTL9n8PPZbSxDFd > div:nth-child(2) > span.anticon.anticon-system-delete-line.g21i709BMWsM8hR8RAdT'

    # 弹窗的删除button
    popup_delete_button = "//span[text()='删 除']"

    # 新增分组button
    add_fast_reply_group_button = "//span[text()='新增分组']"

    # 新增分组弹窗的分组名称输入框
    add_fast_reply_group_popup_input = 'div > span > input'

    # 弹窗的确定button
    popup_confirm_button = "//span[text()='确 定']"

    # 快捷回复分组行
    fast_reply_group_row_list = 'div.ADSi8SC6ch9gxSdXjSEl > div > div > div > div'

    # 快捷回复的消息内容列
    fast_reply_message_list = 'tr> td:nth-child(3)'

    # 快捷回复的删除button列
    fast_reply_delete_button_list = 'tr> td:nth-child(4) > button:nth-child(2)'

    # 快捷回复的修改button列
    fast_reply_edit_button_list = 'tr> td:nth-child(4) > button:nth-child(1)'

    # 新增快捷回复button
    add_fast_reply_button = "//span[text()='新增快捷回复']"

    # 新增快捷回复弹窗-请选择分组下拉框
    add_fast_reply_popup_select_group_downbox = 'div:nth-child(1) > div.kwaishop-open-fuwu-pc-micro-col.kwaishop-open-fuwu-pc-micro-form-item-control'

    # 新增快捷回复弹窗-分组下拉框列表
    add_fast_reply_popup_select_group_downbox_list = 'div.rc-virtual-list-holder > div > div > div > div'

    # 新增快捷回复弹窗-快捷编码输入框
    add_fast_reply_popup_fast_code_input = 'div.kwaishop-open-fuwu-pc-micro-col.kwaishop-open-fuwu-pc-micro-form-item-control > div > div > span > input'

    # 新增快捷回复弹窗-回复消息输入框
    add_fast_reply_popup_reply_message_input = 'span > textarea'

    # 快捷回复分组删除button列
    fast_reply_group_delete_button_list = 'div> div > div.Duh4csTL9n8PPZbSxDFd > div:nth-child(2) > span.anticon.anticon-system-edit-line.g21i709BMWsM8hR8RAdT'

    # 编辑快捷回复分组名称输入框
    edit_fast_reply_group_name_input = 'div > span > input'

    # 快捷回复列表-上移下移button列
    fast_reply_move_button_list = 'tr > td:nth-child(4) > button:nth-child(3)'
