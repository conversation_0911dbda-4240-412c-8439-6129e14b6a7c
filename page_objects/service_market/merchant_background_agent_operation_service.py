class MerchantBackgroundAgentOperationService(object):
    """商家后台-代运营服务页面"""

    # 全部订单tab
    all_order_tab = "//span[text()='全部订单']"

    # 合同准备中tab
    prepare_contract_tab = "//span[text()='合同准备中']"

    # 合同待签署tab
    wait_sign_contract_tab = "//span[text()='合同待签署']"

    # 待支付tab
    wait_pay_order_tab = "//span[text()='待支付']"

    # 待服务tab
    wait_served_tab = "//span[text()='待服务']"

    # 服务中tab
    in_service_tab = "//span[text()='服务中']"

    # 已结束tab
    close_tab = "//span[text()='已结束']"

    # 已售后tab
    refund_tab = "//span[text()='已售后']"

    # 订单列表订单状态列
    order_list_order_status_list = 'tr > td:nth-child(9)'

    # 代运营服务title
    agent_operation_service_title = "//div[text()='运营服务']"
