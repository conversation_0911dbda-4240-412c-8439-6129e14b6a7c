class ProviderBackgroundLeftTab(object):
    """服务商后台左侧tab"""

    # 左侧"客服外包数据"tab
    customer_service_outsourcing_data_tab = "//span[text()='客服外包数据']"

    # "客服外包数据"title
    customer_service_outsourcing_data_title = "//header[text()='客服外包数据']"

    # 左侧"订单查询"tab
    order_tracking_tab = "//span[text()='订单查询']"

    # "订单查询"title
    order_tracking_title = "//header[text()='订单查询']"

    # 左侧"评价管理"tab
    comment_management_tab = "//span[text()='评价管理']"

    # "评价管理"title
    comment_management_title = "//header[text()='评价管理']"

    # 左侧"服务列表"tab
    service_list_tab = "//span[text()='服务列表']"

    # "服务列表"title
    service_list_title = "//header[text()='服务列表']"

    # 左侧"我的资质"tab
    my_qualifications_tab = "//span[text()='我的资质']"

    # 左侧"我的资质"title
    my_qualifications_title = "//header[text()='我的资质']"

    # 左侧"子账号管理"tab
    subAccount_management_tab = "//span[text()='子账号管理']"

    # "子账号管理"title
    subAccount_management_title = "//header[text()='子账号管理']"

    # 左侧"店铺信息"tab
    shop_information_tab = "//span[text()='店铺信息']"

    # "店铺信息"title
    shop_information_title = "//header[text()='店铺信息']"

    # 左侧"企业信息"tab
    corporate_information_tab = "//span[text()='企业信息']"

    # "企业信息"title
    corporate_information_title = "//header[text()='企业信息']"

    # 左侧"服务商主页"tab
    provider_homepage_tab = "//span[text()='服务商主页']"

    # "服务商主页"title
    provider_homepage_title = "//span[text()='服务商主页']"

    # 左侧"保证金"tab
    deposit_tab = "//span[text()='保证金']"

    # "保证金"title
    deposit_title = "//header[text()='保证金']"

    # 左侧"发票管理"tab
    invoice_management_tab = "//span[text()='发票管理']"

    # "发票管理"title
    invoice_management_title = "//h2[text()='发票管理']"

    # 左侧"资金总览"tab
    fund_overview_tab = "//span[text()='资金总览']"

    # "资金总览"title
    fund_overview_title = "//header[text()='资金总览']"

    # 左侧"消息中心"tab
    message_center_tab = r'#消息中心\$Menu > li'

    # "消息中心"title
    message_center_title = "//header[text()='消息中心']"

    # 左侧"客服工作台"tab
    customer_service_desk_tab = "//span[text()='客服工作台']"

    # "客服工作台"title
    customer_service_desk_title = "//header[text()='客服工作台']"

    # 左侧"客服设置"tab
    customer_service_settings_tab = "//span[text()='客服设置']"

    # "客服设置"title
    customer_service_settings_title = "//header[text()='客服设置']"
