class ProviderBackgroundServiceList(object):
    """服务商后台-服务列表"""

    # 新增服务button
    add_service_button = '[class="ant-btn ant-btn-primary"]'

    # 服务分类下拉框
    service_category_down_box = '[class="ant-cascader-picker"]'

    # 服务分类-店铺服务类目
    service_category_shop_service = 'ul:nth-child(1) > li:nth-child(5)'

    # 服务分类-店铺服务-内容运营类目
    service_category_shop_service_content_operation = 'div >div > ul:nth-child(2) > li:nth-child(5)'

    # 服务主图上传图片框
    service_main_image_upload_image_box = '#serviceImageUrl'

    # 服务名称输入框
    service_name_input = '#name'

    # 服务描述输入框
    service_description_input = '#desc'

    # 服务详情图片上传图片框
    service_detail_image_upload_image_box = ' div:nth-child(8) > div.ant-col.ant-form-item-control-wrapper > div > span > span > div > span > input[type=file]'

    # 使用教程输入框
    tutorial_input = '#tutorial > div.ql-container.ql-snow > div.ql-editor.ql-blank'

    # 不公开服务button
    hidden_service_button = '#hidden > label:nth-child(2) > span:nth-child(2)'

    # 新增服务套餐button
    add_service_package_button = "//span[text()='新增服务套餐']"

    # 套餐名称输入框
    package_name_input = '#packageName'

    # 套餐有效期下拉框
    package_time_down_box = 'div:nth-child(2) > div > div > div > span > div > div'

    # 套餐有效期一个月
    package_time_one_month = 'div.ant-select-item.ant-select-item-option.ant-select-item-option-active > div'

    # 套餐价格输入框
    package_price_input = '#periodList_0_price'

    # 客服电话输入框
    customer_service_phone_input = 'div:nth-child(18) > div.ant-col.ant-form-item-control-wrapper > div > span > textarea'

    # 客服邮箱输入框
    customer_service_email_input = 'div:nth-child(19) > div.ant-col.ant-form-item-control-wrapper > div > span > textarea'

    # 提交审核button
    submit_review_button = "//span[text()='提交审核']"

    # 删除弹窗的确认button
    delete_popup_confirm_button = "//span[text()='确 认']"

    # 服务列表的服务信息列
    service_name_list = 'tr > td:nth-child(1) > div > div > p.info-name'

    # 服务列表的服务id列
    service_id_list = 'tr > td:nth-child(1) > div > div > p.info-id'

    # 服务列表的服务状态列
    service_status_list = 'tr> td:nth-child(3) > div:nth-child(1)'

    # 服务列表的操作按钮第一列
    operate_button_first_list = ' tr> td:nth-child(4) > span > a:nth-child(1)'

    # 服务列表的操作按钮第二列
    operate_button_second_list = ' tr> td:nth-child(4) > span > a:nth-child(2)'

    # 服务编辑页提交审核button
    edit_submit_review_button = 'div.edit-service-contain > div > button.ml16.ant-btn.ant-btn-primary'

    # 已上架tab
    online_tab = "//div[text()='已上架']"

    # 待上架tab
    wait_online_tab = "//div[text()='待上架']"

    # 待审核tab
    wait_review_tab = "//div[text()='待审核']"

    # 审核待修改tab
    wait_edit_tab = "//div[text()='审核待修改']"

    # 已上架服务的生成二维码按钮列
    generate_qr_code_button_list = 'tr > td:nth-child(4) > span > a:nth-child(5)'

    # 已上架服务的标签管理按钮列
    tag_management_button_list = 'tr > td:nth-child(4) > span > a:nth-child(4)'

    # 已上架服务的详情按钮列
    detail_button_list = 'tr > td:nth-child(4) > span > a:nth-child(3)'

    # 生成二维码弹窗button
    generate_qr_code_popup_button = 'div.ant-modal-footer > div > button.ant-btn.ant-btn-primary'

    # 保存二维码图片button
    save_qr_code_button = "//span[text()='保存二维码图片']"

    # 标签管理弹窗确定提交按钮
    tag_management_popup_confirm_button = "//span[text()='确定提交']"
