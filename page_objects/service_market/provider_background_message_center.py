class ProviderBackgroundMessageCenter(object):
    """服务商后台-消息中心"""

    # 全部tab
    all_tab = "//div[contains(text(),'全部')]"

    # 未读tab
    unread_tab = "//div[contains(text(),'未读')]"

    # 已读tab
    read_tab = "//div[contains(text(),'已读')]"

    # 系统消息tab
    system_message_tab = "//span[text()='系统消息']"

    # 平台公告tab
    platform_announcement_tab = "//span[text()='平台公告']"

    # 活动消息tab
    event_news_tab = "//span[text()='活动消息']"

    # 产品消息tab
    product_news_tab = "//span[text()='产品消息']"

    # 消息标题列
    message_title_list = 'tr > td:nth-child(2) > span'

    # 消息详情
    message_detail = 'span.ant-breadcrumb-link > span'

    # 消息内容
    message= 'div.ant-tabs-tabpane.ant-tabs-tabpane-active > div.ant-table-wrapper > div > div > div > div > div > table > tbody > tr'
