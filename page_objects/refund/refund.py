class RefundPage(object):
    """
    售后页面元素定义（工作台侧边栏
    """

    # 1侧边栏-售后按钮
    left_refund_btn = '//*[@id="menu_item_wQWtaVajziM"]/span/div/span'
    # 1侧边栏-店铺按钮
    left_shop_btn = '//*[@id="menu_item_nB1uqX31G8Q"]'
    # 2侧边栏-权益中心
    left_privilege_btn = '//*[@id="menu_item_0W1gXkevjdA"]'
    # 2侧边栏-售后助手
    left_atom_btn = '//*[@id="menu_item_cU3mIA2wLzc"]'
    # 2侧边栏-小额打款
    left_transfer_btn = '//*[@id="menu_item_zwucQX-jO5s"]'
    # 2侧边栏-退货补运费
    left_insurance = '//*[@id="menu_item_w3p1Zbb08n4"]'
    # 第一次进入售后，有新手教程
    first_btn = '//*[@id="driver-popover-item"]/div[4]/button'


class Transfer(object):
    # 小额打款立即使用
    quick_use = '#root > article > div > div > div > div > button'
    # 小额打款-发起打款
    First_tab = '//*[@id="rc-tabs-0-tab-1"]'
    # 发起打款-订单输入框
    First_tab_input = '#orderId'
    # 发起打款-查询按钮
    First_tab_query = '#rc-tabs-0-panel-1 > article > main > div.YWfjo0fT5e5XKSTO91KO > form > div:nth-child(2) > div > div > div > div > div:nth-child(1) > button'
    # 发起打款-发起打款按钮
    payment_btn = '#rc-tabs-0-panel-1 > article > main > div.fund-flow-pc-table-wrapper.ryJmiY3OdC9IA66EIM_a > div > div > div > div > div > table > tbody > tr > td:nth-child(8) > button'
    # 小额打款-打款记录
    Second_tab = '//*[@id="rc-tabs-0-tab-2"]'
    # 打款记录-订单输入框
    Second_tab_oidinput = '#RecordForm_orderNo'
    # 打款记录-查询按钮
    Second_tab_query = '#rc-tabs-0-panel-2 > div:nth-child(1) > form > div:nth-child(4) > div > button.fund-flow-pc-btn.fund-flow-pc-btn-primary'
    # 打款记录-查询结果1
    Second_tab_res = '//*[@id="rc-tabs-0-panel-2"]/div[2]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[1]/a'
    # 小额打款-待处理打款申请
    Third_tab = '//*[@id="rc-tabs-0-tab-3"]'
    # 小额打款-打款配额
    Fourth_tab = '//*[@id="rc-tabs-0-tab-4"]'
    # 快递拦截
    package_interception = "//span[@id='menu_item_kEmynq6Ntlo']"


class Insurance(object):
    """
    退货补运费页面
    """
    # 退货补运费-服务说明按钮
    fwsm = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[1]/div/div[1]/div[2]/div[2]/span'

    tab_manage = '//*[@id="rc-tabs-0-tab-Main"]'
    tab_record = '//*[@id="rc-tabs-0-tab-Record"]'

    # 开通服务
    gxk_btn = "//span[@class='open-prefixSchema']"
    open_btn = "//button[@class='kwaishop-seller-insurance-pc-btn kwaishop-seller-insurance-pc-btn-primary']"
    box_text = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[1]/div/div'
    box_btn_two = "//button[@class='kwaishop-seller-insurance-pc-btn kwaishop-seller-insurance-pc-btn-primary']//span[contains(text(),'充 值')]"
    open_status = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[1]/div[1]/div/div[1]/div/span'
    fwsz = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[1]/div/div[1]/div[3]/span'
    close_btn = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[1]/div/div[1]/div[4]/div/div/ul/li/span/div/button/span'
    ztfw = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[1]'
    reason = '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/div/div[1]/label/span[1]'
    ztfw_r = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[2]'

    # 自选权益模块
    zx_title = "//span[@class='title-content']"
    zx_highrisk_text = "//span[contains(text(),'高风险率开通退货补运费服务')]"
    zx_highrisk = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[last()]/div/div[2]/div[1]/div[1]/button'
    zx_sku_text = "//span[contains(text(),'小额商品退货补运费服务')]"
    zx_sku = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[last()]/div/div[2]/div[2]/div[1]/button'
    zx_big_sku_text = "//span[contains(text(),'大件商品退货补运费服务')]"
    zx_big_link_1 = "//a[contains(text(),'《大件商品服务费收取规则》')]"
    zx_big_link_2 = "//a[contains(text(),'《大件商品支持类目范围》')]"
    # 常见问题按钮
    cjwt_btn = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[1]/div/div[1]/div[2]/div[1]/span'
    # 常见问题标题
    cjwt_title = '//*[@id="rcDialogTitle0"]'
    cjwt_url = '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/div/div/div/div[3]/div[2]/span[2]/div[1]/a'
    cjwt_fwsm = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/div/div/div/a'

    # 资金模块
    zj_question = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[last()]/div/div[1]/div[1]/span[2]'
    zj_recharge = "//span[contains(text(),'充 值')]"
    zj_withdraw = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[last()]/div/div[2]/div[1]/div[2]/button[2]'
    zj_rwrecord = '//*[@id="rc-tabs-0-panel-Main"]/div/div/div/div[last()]/div/div[1]/div[2]/a/span'
    zj_autorecharge = "//button[@class='kwaishop-seller-insurance-pc-switch']"
    zj_auto_title = "//span[@class='kwaishop-seller-insurance-pc-modal-confirm-title']"
    zj_ptbt = "//span[contains(text(),'平台补贴')]"

    # 退货补运费-服务记录-oid输入框
    list_oidInput = '//*[@id="orderId"]'
    # 退货补运费-服务记录-订单编号
    list_oid = '//*[@id="rc-tabs-0-panel-Record"]/div/div/div/div/div[4]/div/div/div/div/div/div/table/tbody/tr/td[1]'
    # 退货补运费-服务记录-查询按钮
    list_queryBnt = '//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button/span'
    # 退货补运费-服务记录-查看详情按钮
    list_lookDetail = "//span[contains(text(),'查看详情')]"
    # 高风险标记
    list_highRiskTag = '//*[@id="rc-tabs-0-panel-Record"]/div/div/div/div/div[4]/div/div/div/div/div/div/table/tbody/tr/td[2]/span[1]'

    # 退货补运费-新功能引导弹窗标题
    new_title = '//*[@id="rcDialogTitle0"]'
    new_X = '/html/body/div[last()]/div/div[2]/div/div[2]/button/span'
    # 退货补运费详情页-服务状态标题
    detail_status = '//*[@id="root"]/div/div[2]/div/div[1]/div[1]/div[1]'
    # 退货补运费详情页-服务详情标题
    detail_fwxq = '//*[@id="root"]/div/div[2]/div/div[2]/div/div[1]/span'
    # 退货补运费详情页-补偿流程标题
    detail_bclc = '//*[@id="root"]/div/div[2]/div/div[3]/div[1]/div/span'

    # 退货补运费详情页-补偿标准
    # 入口
    bcbz_entrance = '//*[@id="root"]/div/div[2]/div/div[2]/div/div[4]/div/div[1]/button/span'
    # 标题
    bcbz_title = '/html/div/div/div[2]/div/div/div[1]/div/div'
    # 输入框1
    bcbz_input_from = '//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input'
    # 输入框2
    bcbz_input_to = '//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/span/input'
    # 查询按钮
    bcbz_querybtn = '//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button'
    # 发货地
    bcbz_from = '/html/div/div/div[2]/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr/td[1]'
    # 收货地
    bcbz_to = '/html/div/div/div[2]/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr/td[2]'
    bcbz_X = '/html/div[1]/div/div[2]/div/div/div[1]/div/button/span'

    # 退货补运费详情页-钱款去向
    # 入口
    qkqx_entrance = '//*[@id="root"]/div/div[2]/div/div[1]/div[1]/div[2]/div[1]/button'
    # 标题
    qkqx_title = '/html/div[2]/div/div[2]/div/div/div[1]/div/div'

    # 服务说明页—大标题
    fwsm_title = '//*[@id="root"]/div/div[2]/div/div/div/h2'
    # 服务说明页-1.1支持类目按钮
    fwsm_support_category = '//*[@id="root"]/div/div[2]/div/div/div/p[1]/a'
    # 服务说明页-1.2积木链接按钮
    fwsm_ppg = '//*[@id="root"]/div/div[2]/div/div/div/p[5]/a'
    # 服务说明页-3.2收费规则按钮
    fwsm_sfgz = '//*[@id="root"]/div/div[2]/div/div/div/p[53]/a'
    # 服务说明页-4.4跳转商友圈按钮
    fwsm_syq = '//*[@id="root"]/div/div[2]/div/div/div/p[42]/a'


class Privilege(object):
    slogan = '//*[@id="root"]/div/div[1]/div/div/div[1]/div[1]/div[2]'
    # 权益标题
    GENERAL = '//*[@id="root"]/div/div[2]/div/div[1]/div[1]'
    BRAND = '//*[@id="root"]/div/div[2]/div/div[2]/div[1]'
    OTHER = '//*[@id="root"]/div/div[2]/div/div[3]/div[1]'
    # 假一赔十
    fakeone_btn = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[1]/div[3]/button'
    popup_title2 = '//*[@id="rcDialogTitle2"]'
    popup_title0 = '//*[@id="rcDialogTitle0"]'
    fakeone_open_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[2]/span'
    fakeone_close_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[1]/span'
    fakeone_toast = '/html/body/div[last()]/div/div/div/div/div/span[2]'
    # 极速退款
    fastrefund_btn = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[2]/div[3]/button/span'
    fastrefund_title0 = '//*[@id="rcDialogTitle0"]'
    fastrefund_title1 = '//*[@id="rcDialogTitle1"]'
    fastrefund_open_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[2]/span'
    fastrefund_close_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[1]/span'
    # 退货补运费
    insurance_btn = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[3]/div[3]/button/span'
    # 主播实在宝
    zbszb_btn = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[3]/div[3]/button'
    # 换货
    exchange_btn = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[5]/div[3]/button'
    exchange_title0 = '//*[@id="rcDialogTitle0"]'
    exchange_title1 = '//*[@id="rcDialogTitle1"]'
    exchange_open_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[2]/span'
    exchange_close_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[1]/span'
    # 七天价保
    priceprotect_btn = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[4]/div[3]/button'
    # 退款不退货
    refundnoreturn_btn = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[5]/div[3]/button/span'
    refundnoreturn_title = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[1]/span[2]'
    refundnoreturn_popup_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[2]/button/span'
    # 先用后付
    payafteruse_btn = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[1]/div[3]/button'
    payafteruse_title = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[1]/span[2]'
    payafteruse_popup_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[2]/button/span'
    # 分期免息
    nointerest_btn = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[2]/div[3]/button'
    nointerest_title = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[1]/span[2]'
    nointerest_popup_btn = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[2]/button/span'
    # 详细说明
    # 信任购
    link_btn1 = '//*[@id="root"]/div/div[1]/div/div/div[2]/div'
    url1 = 'https://edu.kwaixiaodian.com/bbs/web/article?id=12356&layoutType=4'
    # 假一赔十
    '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[1]/div[2]/div'
    link_btn2 = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[1]/div[2]/div'
    url2 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=8faQD4Dm9n'
    # 极速退款
    link_btn3 = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[2]/div[2]/div'
    url3 = 'https://edu.kwaixiaodian.com/bbs/web/article?id=12502&layoutType=4'
    # 退货补运费
    link_btn4 = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[3]/div[2]/div'
    url4 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=nUuKGK0jav'
    # 价格保护
    link_btn5 = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[4]/div[2]/div'
    url5 = 'https://fangzhou.kwaixiaodian.com/pcspa/qHrPspKUcPhR/XfhlHzcjARMg'
    # 退款不退货-已下线
    link_btn6 = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[5]/div[2]/div'
    url6 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=Nb4bcqqWwD'
    # 换货
    link_btn7 = '//*[@id="root"]/div/div[2]/div/div[1]/div[2]/div[5]/div[2]/div'
    url7 = 'https://university.kwaixiaodian.com/kwaishop/newKnowledge/589533756206645286/581287853570379856'

    # 上门安装
    smaz_btn = "//span[contains(text(),'查看服务')]"
    smaz_url = "https://s.kwaixiaodian.com/zone/supplychain/express-service"
    smaz_prt_url = "https://prt-eshop-s.test.gifshow.com/zone/supplychain/express-service"

    # 过敏包退
    link_btn8 = "//div[@class='items___VD21e']//div[2]//div[2]//div[1]"
    url8 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=C9x0tXV1Tc'

    # 破损包退-剔除品退
    link_btn9 = "//div[@class='content___OoSL8']//div[3]//div[2]//div[1]"
    url9 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=BmuJbNciG9'

    # 破损包退
    psbt_btn = "//div[@class='content___OoSL8']//div[4]//div[2]//div[1]"
    psbt_url = "https://edu.kwaixiaodian.com/rule/web/detail?id=BmuJbNciG9"

    zjzl_btn = "//div[6]//div[2]//div[1]"
    zjzl_url = "https://edu.kwaixiaodian.com/rule/web/detail?id=sg4FHiKqRM"

    # 坏了包退
    link_btn10 = "//div[@class='content___OoSL8']//div[5]//div[2]//div[1]"
    url10 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=j4OW3X8TDl'
    # 先用后付
    link_btn11 = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[1]/div[2]/div'
    url11 = 'https://edu.kwaixiaodian.com/bbs/web/article?id=13447&layoutType=4'
    # 分期免息
    link_btn12 = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[2]/div[2]/div'
    url12 = 'https://edu.kwaixiaodian.com/bbs/web/article?id=825&layoutType=4'
    # 主播实在宝
    link_btn13 = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[3]/div[2]/div'
    url13 = 'https://edu.kwaixiaodian.com/rule/web/detail?id=kJGVBVqfog'
    # 信任购开通指南
    link_btn14 = '//*[@id="root"]/div/div[1]/div/div/div[1]/div[2]/div[1]/button/span'
    url14 = 'https://edu.kwaixiaodian.com/bbs/web/article?id=16557&layoutType=4'
    # 信任购开通查询
    trustbuy_btn = '//*[@id="root"]/div/div[1]/div/div/div[1]/div[2]/div[2]/button'

    # 主播实在宝
    szb_btn = '//*[@id="root"]/div/div[2]/div/div[3]/div[2]/div[3]/div[3]/button/span'
    szb_url_index = 'https://s.kwaixiaodian.com/zone/insurance/reality/index?channel=5&insuranceCode=5003'
    szb_url_open = 'https://s.kwaixiaodian.com/zone/insurance/reality/open?channel=5&insuranceCode=5003'
    szb_fwsm_btn = '//*[@id="root"]/div/div/div/div[1]/div[2]/div/span'
    szb_open_xy_btn = '//*[@id="root"]/div/div/div/div[3]/div/span/a'
    szb_index_xy_btn = '//*[@id="root"]/div/div/div/div[1]/div[2]/div[3]/a'
    szb_cx_title = '//*[@id="root"]/div[1]/div[1]'
    szb_fwsm_url = 'https://s.kwaixiaodian.com/zone/privilege/reality/introduce'
    szb_xy_url = 'https://fangzhou.kwaixiaodian.com/pcspa/QzbbtpIjDpGc/jWaGzIQYPzEG'
    szb_pfdcx = '//*[@id="root"]/div[1]/div[1]'


class RefundListElement(object):
    popup = 'body > div:nth-child(4) > div > div.ant-modal-wrap > div > div.ant-modal-content'
    popup_close = 'body > div:nth-child(4) > div > div.ant-modal-wrap > div > div.ant-modal-content > button'
    popup_title = '//*[@id="rcDialogTitle0"]'
    popup_content = '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/form/div[1]/span/div'
    toast = '/html/body/div[last()]/div/div/div/div/div/span[2]'
    transport_stagnation = "//span[contains(text(),'运输中停滞')]"
    refund_detail_btn = "//div[@class='ant-spin-container']//div[1]//div[2]//div[7]//div[1]//div[1]//a[1]//button[1]//span[1]"
    confirm_receive_popup_title = "//span[contains(text(),'确认收货')]"
    confirm_receive_popup_content = '/html/body/div[last()]/div/div[2]/div/div[2]/div/div/div[1]/div'  # //div[@class='ant-modal-confirm-content']

    # 列表页操作按钮
    refund_and_return_detail = "//span[contains(text(),'售后详情')]"
    confirm_receive_list = "//button[@class='ant-btn ant-btn-link']//span[contains(text(),'确认收货')]"
    select_first = '//*[@id="root"]/div/div/div/div/div[4]/div/div/div[4]/div[1]/div/ul/div[1]/div[1]/label/span/span'
    batch_agree_refund = "//span[contains(text(),'批量同意退款/退货 1')]"
    batch_confirm_receipt = "//span[contains(text(),'批量确认收货 1')]"

    strategy_select_url = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div[1]/div/div[2]/div/div[2]/a'
    # 备注信息
    # remarks_button = '//*[@id="root"]/div/div/div/div/div[4]/div/div/div[4]/div[1]/div/ul/div[1]/div[1]/div[5]/span/div/div'
    remarks_button = '//*[@id="root"]/div/div/div/div/div[4]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[5]/span/div/div'
    remarks_popup = '//*[@id="rcDialogTitle0"]/div'
    remarks_order_mark = '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/div/div[1]/span'

    # 通用部分
    cancel = "//span[contains(text(),'取 消')]"
    confirm = "//span[contains(text(),'确 认')]"
    detail_reject_tips = "//div[@class='title___kHJ6i']"
    logistics_id = "//label[@title='物流单号']"
    logistics_company = "//label[@title='物流公司']"
    logistics_company_1 = "//label[@title='快递公司']"
    return_goods_number = "//label[@title='退货单号']"
    return_goods_address = "//label[@title='退货地址']"

    # 操作按钮
    detail_popup_title = '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/form/div[1]/span/div/div'
    detail_close_popup = '/html/body/div[last()]/div/div[2]/div/div[2]/button'
    confirm_receive = "//span[contains(text(),'确认收货')]"
    agree_refund = "//span[contains(text(),'同意退款')]"
    agree_refund_return = "//span[contains(text(),'同意退货')]"
    intercept_logistics = "//span[contains(text(),'拦截快递')]"
    agree_refuse_delivery = "//span[contains(text(),'同意拒收后退款')]"
    more_btn = "//span[contains(text(),'更多')]"
    more_reject_refund = "//div[@class='E5HK9z00duXscZ7x_o59']"
    reject_refund_and_return = "//span[contains(text(),'拒绝退货退款')]"
    reject_application = "//span[contains(text(),'驳回申请')]"
    apply_for_platform = "//span[contains(text(),'申请平台介入')]"
    direct_refund = "//span[contains(text(),'直接退款')]"
    check_order_info = "//span[contains(text(),'查看订单详情')]"
    help_buyer_upload_number = "//span[contains(text(),'帮买家上传单号')]"

    # 确认收货半屏
    tips_seller_duty = "//div[contains(text(),'本单退款原因为商家责任，根据平台规则，商家需要承担买家的全部退货运费金额')]"
    text_freight_advice = "/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/div[5]/div[1]"
    text_agree_confirm_receive = "//div[@class='footer___amdtk']//div[@class='left___ynoV7']"
    btn_agree_freight = "//span[contains(text(),'同意退运费')]"
    btn_not_agree_freight = "//span[contains(text(),'不认可退货运费')]"
    btn_agree_with_edit_freight = "//span[contains(text(),'同意但需修改运费')]"
    label_explain = "//label[@title='说明']"
    label_upload_evidence = "//label[@title='上传凭证']"
    label_edit_money = "//label[@title='修改金额']"
    tips_not_agree = "//div[@class='ant-alert-description']"

    # 除协商按钮外，第二个按钮置灰时 待修改
    intercept_logistics_ash_text = '//*[@id="INTERCEPT_LOGISTICS"]/span'
    intercept_logistics_ash_button = '//*[@id="INTERCEPT_LOGISTICS"]'
    detail_toast = '/html/body/div[3]/div/div/div/div[2]'
    # 同意拒收后退款-按钮置灰时
    agree_refuse_delivery_ash_text = '//*[@id="AGREE_REFUSE_DELIVERY"]/span'
    agree_refuse_delivery_ash_button = '//*[@id="AGREE_REFUSE_DELIVERY"]'
    detail_kid_tips = "//div[@class='title___Hsiqd']"
    detail_i_know = "//span[contains(text(),'我知道了')]"

    # 拒绝按钮相关
    reject_plan_title = "//div[contains(text(),'拒绝售后')]"
    reject_plan_suggestion = "//div[@class='title___R1vXV']"
    reject_plan_message = "//div[@class='subTitle___a5wfa']"
    reject_refund_recommend_plan_text = "//div[@class='title___g2sbi']"
    confirm_reject = "//span[contains(text(),'确认拒绝')]"
    reject_reason = "//label[@title='拒绝原因']"
    reject_explain = "//label[@title='拒绝说明']"
    upload_evidence = "//label[@title='上传凭证']"

    # 退货退款二阶段
    refund_and_return_2_detail = "//div[@class='ant-row rxlfk2J8yeOPQRp9Z5T_']//div[2]//a[1]//button[1]//span[1]"
    extend_receive_btn = '//*[@id="EXTEND_CONFIRM_RECEIPT"]/span'

    # 换货相关按钮
    exchange_goods_agree = "//span[contains(text(),'同意换货')]"
    exchange_goods_refuse = "//span[contains(text(),'拒绝换货')]"
    exchange_goods_immediately = "//span[contains(text(),'立即换货')]"
    exchange_goods_to_refund = "//span[contains(text(),'直接退款')]"
    exchange_reject = "//span[contains(text(),'拒 绝')]"
    exchange_extend = "//span[@class='ant-tooltip-disabled-compatible-wrapper']"
    exchange_confirm_reject = "//span[contains(text(),'确认驳回')]"
    # 换货-驳回抽屉
    reject_title = "//div[@class='ant-drawer-title']"

    # 协商相关
    detail_negotiate_btn = "//span[contains(text(),'协商修改售后')]"
    detail_modify_negotiate_btn = "//span[contains(text(),'修改协商方案')]"
    # 详情页-协商方案板块
    negotiate_title = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div[1]/span'
    negotiate_status = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div[2]/div/div'
    negotiate_type = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[1]/div[1]'
    negotiate_type_vale = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[1]/div[2]/div/span'
    negotiate_refund_type = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[2]/div[1]'
    negotiate_refund_type_value = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[2]/div[2]/div/span'
    negotiate_refund_money = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[3]/div[1]'
    negotiate_refund_money_value = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[3]/div[2]/div/span'
    negotiate_shipped_status = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[4]/div[1]'
    negotiate_shipped_status_value = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[4]/div[2]/div/span'
    negotiate_refund_reason = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[5]/div[1]'
    negotiate_refund_reason_value = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div[5]/div[2]/div/span'
    # 协商方案设置弹层
    negotiate_popup = '//*[@id="rc-tabs-1-tab-1"]'
    negotiate_popup_2 = '//*[@id="rc-tabs-1-tab-2"]'
    negotiate_popup_3 = '//*[@id="rc-tabs-1-tab-3"]'
    negotiate_popup_title = "//div[contains(text(),'协商方案设置')]"
    negotiate_rule_description = "//a[contains(text(),'协商工具说明')]"
    negotiate_rule_description_url = "//div[contains(text(),'为减少因拒绝售后带来的纠纷，以及帮助商家缩短售后处理时效，平台推出了售后协商工具，请确认并选择您希望')]//a[contains(text(),'协商工具说明')]"
    # 协商弹窗第一个label
    negotiate_set_refund_type = "//label[@title='售后类型']"
    negotiate_set_shipped_status = "//label[@title='货物状态']"
    negotiate_set_refund_reason = "//label[@title='退款原因']"
    negotiate_set_refund_fee = "//label[@title='退款金额']"
    negotiate_set_refund_suggestion_remark = "//label[@title='协商方案']"
    negotiate_supplement_type = "//label[@title='补充场景']"
    negotiate_supplement_type_value1 = "//span[@title='补充售后凭证']"
    negotiate_supplement_type_value2 = "//span[@title='核对/变更退货物流信息']"
    negotiate_scene = "//label[@title='协商场景']"
    negotiate_plan = "//label[@title='协商方案']"
