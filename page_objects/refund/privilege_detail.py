from selenium.webdriver.common.by import By


class PrivilegeDetailPage:
    def __init__(self, driver):
        self.driver = driver

    @property
    def service_status(self):
        return self.driver.find_element(By.CSS_SELECTOR, ".title___e_zie")

    @property
    def service_description_button(self):
        return self.driver.find_element(By.XPATH, "//button[contains(., '服务说明')]")

    @property
    def pause_service_button(self):
        return self.driver.find_element(By.XPATH, "//button[contains(., '暂停服务')]")

    @property
    def service_config_details(self):
        return self.driver.find_elements(By.CSS_SELECTOR, ".col___bzUiz")

    @property
    def modify_config_button(self):
        return self.driver.find_element(By.XPATH, "//button[contains(., '修改配置')]")

    @property
    def faq_section(self):
        return self.driver.find_elements(By.CSS_SELECTOR, ".title___c2_ge")

    def click_service_description(self):
        self.service_description_button.click()

    def click_pause_service(self):
        self.pause_service_button.click()

    def click_modify_config(self):
        self.modify_config_button.click()

    @property
    def pause_popup_title(self):
        return self.driver.find_element(By.CLASS_NAME, "kwaishop-seller-privileges-pc-modal-confirm-title")

    @property
    def confirm_button(self):
        return self.driver.find_element(By.XPATH, "//button[contains(., '确 定')]")

    @property
    def cancel_button(self):
        return self.driver.find_element(By.XPATH, "//button[contains(., '取 消')]")

    def is_confirm_button_enabled(self):
        return self.confirm_button.is_enabled()

    def click_cancel_button(self):
        self.cancel_button.click()

    """
    以下为弹窗:修改配置
    """
    @property
    def compensation_option_no(self):
        return self.driver.find_element(By.XPATH, "//label[span[text()='否']]//input")

    @property
    def compensation_option_yes(self):
        return self.driver.find_element(By.XPATH, "//label[span[text()='是']]//input")

    @property
    def daily_limit_option_no(self):
        return self.driver.find_element(By.XPATH, "//label[span[text()='无上限']]//input")

    @property
    def daily_limit_option_yes(self):
        return self.driver.find_element(By.XPATH, "//label[span[text()='有上限']]//input")

    def select_compensation_option(self, value):
        if value == 0:
            self.compensation_option_no.click()
        elif value == 1:
            self.compensation_option_yes.click()

    def select_daily_limit_option(self, value):
        if value == 0:
            self.daily_limit_option_no.click()
        elif value == 1:
            self.daily_limit_option_yes.click()

    def is_compensation_option_selected(self, value):
        if value == 0:
            return self.compensation_option_no.is_selected()
        elif value == 1:
            return self.compensation_option_yes.is_selected()

    def is_daily_limit_option_selected(self, value):
        if value == 0:
            return self.daily_limit_option_no.is_selected()
        elif value == 1:
            return self.daily_limit_option_yes.is_selected()
