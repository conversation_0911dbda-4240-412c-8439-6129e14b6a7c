class GoodsShelvesPage(object):
    #核心总览
    core_overview = ''

    #全部商品
    all_goods = "(//div[@class='kwaishop-tianhe-itemShelves-pc-pro-checkableTag kwaishop-tianhe-itemShelves-pc-pro-checkableTag__middle kwaishop-tianhe-itemShelves-pc-pro-checkableTag__checked']//div//span[contains(text(),'全部')])[1]"
    #自建商品
    self_goods = "(//div[@class='kwaishop-tianhe-itemShelves-pc-pro-checkableTag kwaishop-tianhe-itemShelves-pc-pro-checkableTag__middle']//div//span[contains(text(),'自建商品')])[1]"
    #他人商品
    others_goods = "(//div[@class='kwaishop-tianhe-itemShelves-pc-pro-checkableTag kwaishop-tianhe-itemShelves-pc-pro-checkableTag__middle']//div//span[contains(text(),'他人商品')])[1]"

    chanel_all_goods = "(//div[@class='kwaishop-tianhe-itemShelves-pc-pro-checkableTag kwaishop-tianhe-itemShelves-pc-pro-checkableTag__middle kwaishop-tianhe-itemShelves-pc-pro-checkableTag__checked']//div//span[contains(text(),'全部')])[2]"

    chanel_self_goods = "(//div[@class='kwaishop-tianhe-itemShelves-pc-pro-checkableTag kwaishop-tianhe-itemShelves-pc-pro-checkableTag__middle']//div//span[contains(text(),'自建商品')])[2]"

    chanel_other_goods = "(//div[@class='kwaishop-tianhe-itemShelves-pc-pro-checkableTag kwaishop-tianhe-itemShelves-pc-pro-checkableTag__middle']//div//span[contains(text(),'他人商品')])[2]"


    #商品指标数据
    goods_indicator_data = "//div[@class='Fr4Cneutq5ayWCYHx9OA']"
    #指标按钮
    indicator_btn = "//div[@class='YdaoyQZA6Gt2HCeoEJX6 kpro-data-card-list-contain_indicatorTitle']//span[contains(text(),'{title}')]"

    #不同指标的趋势图
    trend_chart = "//div[@class='kwaishop-tianhe-itemShelves-pc-spin-container']//div//div//div//canvas"

    #各渠道数据明细
    #成交人数header
    transaction_customer_header = "//span[@class='kwaishop-tianhe-itemShelves-pc-table-column-title-no-align'][contains(text(),'成交人数')]"

    #成交人数金额 idx start from 2
    transaction_customer_data = '//*[@id="dilu_micro_root"]/div/div/div[5]/div[2]/div/div/div/div/div[2]/div/div/div/div/div/div/div[2]/table/tbody/tr[{idx}]/td[3]/div/div[1]'
    #渠道商品表现
    chanel_goods_performance = "(//span[contains(text(),'该渠道商品表现')])[{idx}]"
    # 渠道商品表现-热销商品-成交金额 idx start from 1
    chanel_goods_performance_amt_data = "(//tr[contains(@class, 'kwaishop-tianhe-itemShelves-pc-table-row')]/td[3]/div)[{idx}]"
    best_selling_go_to_detail = "(//button[@class='kwaishop-tianhe-itemShelves-pc-btn kwaishop-tianhe-itemShelves-pc-btn-link' and ./span[text()='去看详情']])[{idx}]"



    #查看所有商品跳转
    go_to_all_goods = "//span[contains(text(),'查看所有商品')]"

    #best-selling top 10 - 成交金额
    best_selling_amount = '//*[@id="dilu_micro_root"]/div/div/div[6]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[3]/div'

    #跳转后的应该展示商品数据
    goods_info = "//span[contains(text(),'商品数据')]"

    #跳转后应展示的商品列表字样
    goods_list_title = "//span[@class='root___oKnp7'][contains(text(),'查看所有商品')]"

    # 时间选择
    time_frame = '//div[contains(text(),"{time_frame}")]'

