class TransactionPage(object):
    # 时间选择
    time_frame = '//div[contains(text(),"{time_frame}")]'

    # 趋势图
    trend_chart = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div[2]/div[2]/div'



    #数据总览右侧指标
    data_overview = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div[2]/div[1]/div'

    #成交分析tab
    transaction_analysis_tab = "//span[contains(text(),'成交分析')]"

    #退款分析tab
    refund_analysis_tab = "//span[contains(text(),'退款分析')]"

    #数据总览 左侧成交金额
    transaction_amount = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/span[1]'

    #场域构成 - 成交金额/成交退款 header
    transaction_amount_header = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[2]/div[1]/span[1]'

    #场域构成 - 成交金额/成交退款 数据 idx 从2 开始代表第一行对应的成交金额
    transaction_amount_data = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[2]/div/div'

    #场域构成 - 详情 idx 从2 开始代表第一行对应的详情
    detail_btn = "(//button[@class='kwaishop-tianhe-tradeManagement-pc-btn kwaishop-tianhe-tradeManagement-pc-btn-link' and @type='button'])[{idx}]"


    # 详情明细 - 底部列表条数 eg 1-8 共8 条
    detail_list_count = "(//li[@class='kwaishop-tianhe-tradeManagement-pc-pagination-total-text'])[3]"
    refund_detail_list_count = "(//li[@class='kwaishop-tianhe-tradeManagement-pc-pagination-total-text'])[3]"
    # 详情明细 - 成交金额header
    detail_amount_header = "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align' and text()='成交金额'])[3]"
    refund_detail_amount_header = "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align' and text()='成交退款金额'])[3]"

    #详情明细 - 成交金额的值 idx 从2 开始代表第一行对应的成交金额
    detail_amount_data = '/html[1]/body[1]/div[7]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[{idx}]/td[2]/div[1]/div[1]'
    refund_detail_amount_data = '/html/body/div[2]/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[{idx}]/td[2]/div/div[1]'

    #账号构成 - 卖货账号列表
    seller_list = "//span[contains(text(),'卖货账号列表')]"


    #账号构成 - 供货账号列表
    provider_list = "//span[contains(text(),'供货账号列表')]"

    account_kuaishou_id = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div[1]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[2]/div/div/span[1]'
    #账号构成 - 账号搜索框
    account_search_input = '//*[@id="dilu_micro_root"]/div/div[5]/div[1]/div[2]/span/span/span[1]/input'

    #账号构成 - 账号列表条数 eg 1-8 共8 条
    account_list_count = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div[1]/div/div/div/div/div/div/div/div/ul/li[1]'

    #账号构成 - 账号列表下一页按钮
    account_list_nxt_page = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div[1]/div/div/div/div/div/div/div/div/ul/li[4]'

    #账号构成 - 账号列表成交金额/退款金额header
    account_list_amt_header = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div[1]/div/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[2]/div[1]/span[1]'

    #账号构成 - 账号列表成交金额/退款金额的值 idx 从2 开始代表第一行对应的成交金额
    account_list_amt_data = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div[1]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[2]/div/div[1]'

    #账号构成 - 搜索按钮
    search_btn = "(//span[@class='anticon anticon-system-search-line' and @aria-label='system-search-line'])[2]"

    #退款分析sub tab ------------------------------------------------------------
    #数据总览 - 全部，退货退款，仅退款 等 idx 从1开始
    data_overview_btn = '//*[@id="dilu_micro_root"]/div/div[2]/div[1]/div/div/div/div/div[{idx}]'

    #数据总览 左侧-成交退款金额
    refund_amount = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/span[1]'

    # 退款场域构成 - 详情
    refund_detail_btn = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[7]/div/button[2]/span'