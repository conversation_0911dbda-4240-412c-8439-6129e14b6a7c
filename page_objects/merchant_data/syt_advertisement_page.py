class AdvertisementPage(object):
    user_profile = "//span[text()='人群画像']"
    gender_distribution = "//h3[contains(text(),'性别分布')]"
    age_distribution = "//h3[contains(text(),'年龄分布')]"
    region_distribution = "//h3[contains(text(),'地域分布')]"
    audience_detail = "//span[text()='受众明细']"

    gender_btn = "//span[text()='性别']"
    age_btn = "//span[text()='年龄']"
    region_btn = "//span[text()='省份']"
    system_btn = "//span[text()='系统']"
    city_btn = "//span[text()='城市']"

    cover_exposure_cnt = "//span[contains(text(),'封面曝光数')]"
    cover_click_cnt = "//span[contains(text(),'封面点击数')]"
    material_exposure_cnt = "//span[contains(text(),'素材曝光数')]"
    action_cnt = "//span[contains(text(),'行为数')]"
    new_fans_cnt = "//span[contains(text(),'涨粉数')]"
    stream_view_cnt = "//span[contains(text(),'直播观看数')]"
    goods_click_cnt = "//span[contains(text(),'商品点击数')]"

    #磁力金牛 sub tab
    magnetic_advertisement = "//div[text()='磁力金牛']"

    #快手粉条 sub tab
    fentiao_advertisement = "//div[text()='快手粉条']"

    like_cnt = "//span[text()='点赞']"

    click_cnt = "//span[text()='点击']"

    comment_cnt = "//span[text()='评论']"

    share_cnt = "//span[text()='分享']"

    follow_cnt = "//span[text()='关注']"

    time_frame = '//span[contains(text(),"{time_frame}")]'





