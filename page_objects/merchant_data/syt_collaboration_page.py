class CollaborationPage(object):
    # 时间选择
    time_frame = '//div[contains(text(),"{time_frame}")]'

    # 达人数据总览 - 成交退款类

    transaction_refund = "//div[@id='rc-tabs-0-tab-sytSlimCoopOverview']//div[1]"
    # 达人数据总览 - 合作短视频
    collaboration_short_video = "//div[@id='rc-tabs-0-tab-sytSlimCoopPhotoOverview']//div[1]"

    #数据总览指标
    indicator_all = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div'

    #达人数据总览 - 各个指标 -start form 1
    indicator_value_btns = "(//span[contains(@class, 'kpro-data-card-list-contain_value')])[{idx}]"

    #//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div[1]/div[2]/div/span[2]
    #//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div[2]/div[2]/div/span[1]

    #数据拆分 - 直播间
    data_division_streaming = "//div[contains(@class,'kpro-data-calculation-simple-cards')]//div[1]//div[1]//div[1]//div[2]//div[1]"

    #数据拆分 - 短视频
    data_division_short_video = "//div[contains(@class,'kpro-data-calculation-simple-cards')]//div[3]//div[1]//div[1]//div[2]//div[1]"
    #数据拆分 - 商品卡
    data_division_goods_card = "//div[contains(@class,'kpro-data-calculation-simple-cards')]//div[5]//div[1]//div[1]//div[2]//div[1]"
    #数据拆分 - 其他
    data_division_other = "//div[contains(@class,'kpro-data-calculation-simple-cards')]//div[7]//div[1]//div[1]//div[2]//div[1]"

    #趋势图
    trend_chart = "//div[contains(@customstyle,'[object Object]')]//div//div//div//canvas"

    collaboration_list_tab = "//span[contains(text(),'合作达人列表')]"
    collaboration_detail = "//span[contains(text(),'合作内容明细')]"

    #达人列表 - 筛选选项
    # 全部
    collaboration_list_all_btn = "//div[@id='rc-tabs-1-tab-全部']//div[1]"
    #直播间
    collaboration_list_live_btn = "//div[@id='rc-tabs-1-tab-直播间']//div[1]"
    #短视频
    collaboration_list_short_video_btn = "//div[@id='rc-tabs-1-tab-短视频']//div[1]"
    #商品卡
    collaboration_list_goods_card_btn = "//div[@id='rc-tabs-1-tab-商品卡']//div[1]"
    #其他
    collaboration_list_other_btn = "//div[@id='rc-tabs-1-tab-其他']//div[1]"

    #根据成交金额排序
    collaboration_list_amt_sorter = "//span[@class='kwaishop-tianhe-collaboration-pc-table-column-title-no-align' and text()='成交金额']"

    #获取列表的条数
    collaboration_list_count = "//li[@class='kwaishop-tianhe-collaboration-pc-pagination-total-text']"

    #列表当中成交金额的值 start from 2
    collaboration_list_amt_value = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[{idx}]/td[3]'

    kuaishou_id = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div/div[2]/div[1]/div/span[1]'

    input_btn = "//input[@class='kwaishop-tianhe-collaboration-pc-input' and @type='text']"

    search_action_btn = "//button[@type='button']//span[@aria-label='system-search-line']//*[name()='svg']"

    #短视频列表第二页
    page_2 = "//li[@class='kwaishop-tianhe-collaboration-pc-pagination-item kwaishop-tianhe-collaboration-pc-pagination-item-2']"

    # start from 2 as well
    collaboration_list_title = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[{idx}]/td[1]/div/div/div[1]/div/span'

    #合作达人列表 idx start from 1
    collaboration_influencer_list = "(//span[contains(text(),'查看合作商品')])[{idx}]"

    #合作商品列表-成交金额 start form 2
    collaboration_goods_list_amt = '//*[@id="dilu_micro_root"]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[{idx}]/td[2]/div'
    #合作商品列表-title start from 2
    collaboration_goods_list_title = '//*[@id="dilu_micro_root"]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[{idx}]/td[1]/div/div/div'

    #流量页面转化漏斗存在
    flow_funnel = "//div[contains(@class,'kpro-data-funnel')]"

    #合作内容明细 - 流量页面转换漏斗 - checked box 关注页，发现页，个人页 .....
    checked_box = "(//span[@class='kwaishop-tianhe-collaboration-pc-checkbox kwaishop-tianhe-collaboration-pc-checkbox-checked'])[{idx}]"

    #短视频曝光次数
    short_video_exposure_count = "//div[contains(@class,'kpro-data-funnel')]//*[name()='svg']//*[name()='g' and @id='1-left']//*[name()='g' and @id='1-left-txt']//*[name()='text'][2]"

    # 漏斗左侧指标 idx start form 1 to 6
    funnel_left_indicator = '//*[@id="{idx}-left"]'

    #流量来源占比存在
    flow_source_proportion = "//span[contains(text(),'流量来源占比')]"
    #合作短视频分析存在
    collaboration_video_analysis = "//span[contains(text(),'合作短视频分析')]"

    #发布时间排序
    video_list_publish_time_sorter = "//label[@class='kwaishop-tianhe-collaboration-pc-radio-button-wrapper kwaishop-tianhe-collaboration-pc-radio-button-wrapper-checked kwaishop-tianhe-collaboration-pc-radio-button kwaishop-tianhe-collaboration-pc-radio-button-text']//span[contains(text(),'发布时间')]"
    #成交金额排序
    video_list_amt_sorter = "//label[@class='kwaishop-tianhe-collaboration-pc-radio-button-wrapper kwaishop-tianhe-collaboration-pc-radio-button kwaishop-tianhe-collaboration-pc-radio-button-text']//span[contains(text(),'成交金额')]"

    #列表发布时间
    video_list_publish_time = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div/div/div[1]/div[1]/div/ul/div[{idx}]/li/div/div[2]/div[1]/div[1]/div[2]/div[3]/span[2]'

    #列表成交金额
    video_list_amt = '//*[@id="dilu_micro_root"]/div/div[5]/div[2]/div/div/div[1]/div[1]/div/ul/div[{idx}]/li/div/div[2]/div[2]/div[4]/span'

    #列表条数
    video_list_count = "//li[@class='kwaishop-tianhe-collaboration-pc-pagination-total-text']"



























