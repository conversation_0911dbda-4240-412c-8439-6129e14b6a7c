class GoodsPage(object):
    #商品页下的商品列表
    goods_list = '//span[contains(text(),"商品列表")]'
    goods_ranking = '//span[contains(text(),"商品榜单")]'

    # 场域选择的XPath
    field = '//*[@id="pro-tag-form-wrapper"]/div[1]/div/div/div/div[1]/div/div[2]/div/div/div/div[{idx}]'

    # 商品范围的XPath
    scope = '//*[@id="pro-tag-form-wrapper"]/div[2]/div/div/div/div/div/div[2]/div/div/div/div[{idx}]'

    # 表格数据的XPath
    table_data = '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'

    # 商品ID的XPath
    product_id = '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[2]/div/div[2]/div[2]/div[1]/div'

    # 商品曝光次数排序按钮的XPath
    sort_exposure_button = "//div[@class='ant-table-column-sorters' and span[@class='ant-table-column-title-no-align' and .='商品曝光次数']]"

    # 表格第一条曝光次数的XPath
    first_row_exposure = '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]'

    # 表格第二条曝光次数的XPath
    second_row_exposure = '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]'

    # 核心数据的XPath
    core_index_class = "//div[contains(@class,'bu1TqFo1EKb0zdZ7Gwoh QuwCLULmZXRhLjAz5xJg Tx7n4dZFMSkvs2K7z1Jl')]//span[contains(text(),'{text}')]"

    # 场域分布的XPath
    field_distribution_title = '//*[@id="root"]/div/div/div[3]/div[1]/span'
    field_distribution_table = '//*[@id="root"]/div/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody'

    # 时间选择的XPath
    time_real = '//div[contains(text(),"实时")]'
    time_1_day = '//div[contains(text(),"近1日")]'
    time_7_days = '//div[contains(text(),"近7日")]'
    time_30_days = '//div[contains(text(),"近30日")]'

    # 自建商品的XPath
    self_goods = "//div[contains(@customstyle,'[object Object]')]//div[contains(@class,'ant-spin-nested-loading')]//div[2]"

    # 他人商品的XPath
    other_goods = '//div[contains(text(),"他人商品")]'

    #列表无数据的暂无数据
    no_data = "//div[@class='ant-empty-description' and text()='暂无数据']"

    #成交载体

    list_pagination_total = "//li[@class='kwaishop-tianhe-ranking-management-pc-pagination-total-text']"

    #下一页
    next_page = "//li[contains(@title,'下一页')]//button[contains(@type,'button')]"

    # 价格区间
    price_scope_lower = "//div[contains(@class,'lvxqQNHn04Mn7Ae_ocHb')]//div[1]//div[1]//div[1]//div[1]//input[1]"
    price_scope_upper = "//div[contains(@class,'kwaishop-tianhe-ranking-management-pc-col kwaishop-tianhe-ranking-management-pc-form-item-control')]//div[2]//div[1]//div[1]//div[1]//input[1]"

    # 品牌 不限，知名 非知名

    brand_all = "//div[contains(text(),'不限品牌')]"
    brand_famous = "//div[contains(text(),'知名品牌')]"
    brand_unknown = "//div[contains(text(),'非知名品牌')]"

    #列表成交价 start from 2
    deal_price = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[2]/div/div[2]/div[2]/div/div/span'

    # 商品榜单列表 - title
    product_title = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span'

    # 商品榜单列表 - shop title
    shop_title = '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div/div[1]/div[1]'

    #成交载体
    carrier_all = "//div[contains(@class,'FWPXsry88o4NIuBjHpZT PyDiZ2_qvN5pGeLiooGF tH6hGq2dl3AY2q339cIw')]"
    carrier_item_card = "//div[contains(@class,'kwaishop-tianhe-ranking-management-pc-spin-container')]//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[2]"
    carrier_live = "//div[contains(@class,'kwaishop-tianhe-ranking-management-pc-spin-nested-loading')]//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[3]"
    carrier_video = "//div[contains(@class,'kwaishop-tianhe-ranking-management-pc-form-item-control-input-content')]//div//div[contains(@class,'')][contains(text(),'短视频')]"

    relative_live = "//th[contains(text(),'关联直播间')]"
    relative_video = "//th[contains(text(),'关联短视频')]"

    #视频播放
    video_play = "//img[@class='gSLcwWf9KbbgKlpV9lU7' and @data-component-name='BizImage']"

    #弹窗视频播放
    video_play_popup = "//div[@class='xplayer-plugin-controls-btn xplayer-plugin-controls-play svelte-1towbxl']"

    #视频播放进度
    video_play_progress = "//div[@class='xplayer-plugin-controls-time svelte-1pdoup6']/span[1]"

    #商品榜单的列表
    ranking_list = "//div[@class='kwaishop-tianhe-ranking-management-pc-pro-table-content']"

    # 时间选择
    time_frame = '//div[contains(text(),"{time_frame}")]'

    # 分页的 页码
    page_num_btn = "//li[@title='{page_num}' and normalize-space()='{page_num}']"






