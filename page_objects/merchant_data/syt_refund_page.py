class RefundPage(object):
    #详情列表
    refund_detail_list = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-drawer-body')]//tbody[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-tbody')]/tr[2]/td[{idx}]/div"
    #详情列表_下一行
    refund_detail_list_second = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-drawer-body')]//tbody[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-tbody')]/tr[3]/td[{idx}]/div"
    #详情列表_带环比的指标
    refund_detail_list_compare = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-drawer-body')]//tbody[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-tbody')]/tr[2]/td[{idx}]/div/div"
    # 详情列表_带环比的指标_下一行
    refund_detail_list_compare_second = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-drawer-body')]//tbody[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-tbody')]/tr[3]/td[{idx}]/div/div"
    #列表共*条
    list_total_number = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-drawer-body')]//ul[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-pagination-right')]/li"
    #售卖方式选择
    sale_style = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-pro-checkableTags-wrapper')]/div[{idx}]"
    # 售卖载体选择
    sale_component = "//div[contains(@class,'kwaishop-tianhe-tradeManagement-pc-pro-checkableTags-wrapper')]/div[{idx}]"