class ShortVideoPage(object):
    # 数据概览的XPath
    new_video_count_xpath = "//span[contains(text(),'新发布短视频数')]"
    transaction_amount_xpath = "//span[contains(text(),'成交金额')]"
    transaction_order_count_xpath = "//span[contains(text(),'成交订单数')]"
    refund_amount_xpath = "//span[contains(text(),'退款金额（退款日）')]"
    view_count_xpath = "//span[contains(text(),'观看人数')]"
    exposure_count_xpath = "//span[contains(text(),'曝光次数')]"
    #观看曝光转化率
    view_exposure_conversion_rate_xpath = "//span[contains(text(),'观看-曝光转化率（人数）')]"
    #涨粉人数
    fan_count_xpath = "//span[contains(text(),'涨粉人数')]"

    #完播率
    view_completion_rate_xpath = "//span[contains(text(),'完播率')]"
    #右侧 趋势图
    right_trend_chart = "//div[@class='kwaishop-tianhe-shortVideos-pc-spin-container']//div//div//div//canvas"

#tr[class = 'kwaishop-tianhe-shortVideos-pc-table-row kwaishop-tianhe-shortVideos-pc-table-row-level-0']
    # 流量页面转化的渠道名称 比如全部，个人页，关注页 发现页 同城等
    chanel_name = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div[1]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]'
    #短视频详情页-渠道名称
    detail_chanel_name = '//*[@id="dilu_micro_root"]/div/div[6]/div[2]/div[1]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]'

    #渠道-观看人数值 from 3
    chanel_view_count = '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div/div/div[1]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[4]'
    detail_chanel_view_count = '//*[@id="dilu_micro_root"]/div/div[6]/div[2]/div[1]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[4]/div/div[1]'
    #漏斗 - 观看人数
    funnel_view_count = "//div[contains(@class,'kpro-data-funnel')]//*[name()='svg']//*[name()='g' and @id='2-right']//*[name()='g' and @id='2-right-txt']//*[name()='text'][2]"
    #去看合作短视频
    go_to_cooperation_shortVideo = "//span[contains(text(),'去看合作短视频')]"

    #合作短视频页面-合作内容明细
    cooperation_shortVideo_title = "//div[@customstyle='[object Object]']//span[contains(text(),'合作内容明细')]"

    # 时间选择
    time_frame = '//div[contains(text(),"{time_frame}")]'

    #带货短视频tab
    shortVideo_with_item_tab = "//div[@id='rc-tabs-0-tab-pcSlimPhotoList']//div[@class='tabs-tab-btn__content'][contains(text(),'带货短视频')]"
    #非带货短视频tab
    shortVideo_without_item_tab = "//div[@id='rc-tabs-0-tab-pcSlimNoGoodsPhotoListOff']//div[1]"

    # 短视频列表
    shortVideo_list = "//span[contains(text(),'短视频列表')]"

    #短视频详情
    shortVideo_detail = "//span[contains(text(),'查看详情')]"

    #短视频列表底部的条数
    shortVideo_list_bottom_count = "//li[@class='kwaishop-tianhe-shortVideos-pc-pagination-total-text']"

    #短视频列表的第一个短视频的id
    shortVideo_list_first_id = '//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span[1]'

    # 短视频详情的短视频id
    shortVideo_detail_id = '//*[@id="dilu_micro_root"]/div/div[3]/div[1]/div[2]/div[2]/span[3]'

    # 短视频详情的数据概览
    shortVideo_detail_data = "//div[@class='slick-slide slick-active slick-current']"


    #带货短视频搜索框
    search_input_with_item = "//input[@id='search_value']"


    #短视频列表-指标-曝光次数
    short_video_exposure_header = "//span[contains(text(),'曝光次数')]"

    # 短视频列表-指标-曝光次数 form 2
    short_video_exposure_value = '//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[{idx}]/td[6]/div'

    #短视频 商品列表
    goods_info_id = '//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[2]/div/div/span[1]'

    #短视频列表下一页
    next_page = "//li[contains(@title,'下一页')]//button[contains(@type,'button')]"

    #短视频列表 - 暂无数据
    no_data = "//div[@class='kwaishop-tianhe-shortVideos-pc-empty-description']"


