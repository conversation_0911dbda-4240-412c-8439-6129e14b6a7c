def find_unmatched_lines(file_a_path, file_b_path, output_path):
    # 读取B文件的所有行，去除换行符
    with open(file_b_path, 'r') as f_b:
        b_lines = [line.rstrip('\n') for line in f_b]

    # 打开A文件和输出文件
    with open(file_a_path, 'r') as f_a, open(output_path, 'w') as f_out:
        for a_line in f_a:
            a_clean = a_line.rstrip('\n')  # 去除换行符
            # 检查是否在B的任何一行中出现
            found = any(a_clean in b_line for b_line in b_lines)
            if not found:
                f_out.write(a_line)  # 保留原始行内容（包含换行符）


# 使用示例
find_unmatched_lines('cleaned_regex.log', 'output.log', 'result.log')