import sys
import csv
import os
import re
import datetime
sys.path.append('/Users/<USER>/pcUI')
import subprocess
# import utils.time_utils
# from common.openmanage_common import *
# from common.goods import *

def is_commented_out(line):
    """
    检查给定行是否被注释掉。
    """
    stripped_line = line.strip()
    return stripped_line.startswith('#') or stripped_line.startswith('@')

def is_commented_out_skip(line):
    """
    检查给定行是否被注释掉。
    """
    stripped_line = line.strip()
    return stripped_line.startswith('#')

# 11-23完成版 基本ok
def find_bad_cases_in_directory(directory):
    """
    递归遍历目录，查找符合新定义的简单断言的测试用例。
    :param directory: 根目录路径
    :return: 返回一个包含 bad cases 的字典，键是文件路径，值是包含 bad case 的方法列表
    """
    bad_cases = {}
    test_method_pattern = re.compile(r'.*pytest.mark.skip.*')
    bad_cases = {}
    # 遍历指定目录及其子目录中的所有Python文件
    for root, _, files in os.walk(directory):
        # root = '/Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member'
        for file in files:
            # file = 'test_merchant_member_activity.py'
            if file.startswith('test_') and file.endswith('.py'):
                # file_path /Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member/test_merchant_member_activity.py
                file_path = os.path.join(root, file)
                is_find_class_name = False
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    methods_with_excute_cases = []
                    pre_skip_found = False
                    pre_valid_method_found = False
                    method_body = []
                    method_name = []
                    for line in lines:
                        if is_commented_out_skip(line):
                            continue
                        # 匹配到pytest.mark.skip | 整个class被skip
                        if (test_method_pattern.search(line)):
                            pre_skip_found = True
                        is_skiped = is_find_class_name or pre_skip_found
                        found_type, real_current_method = find_method_name_or_class_name(line)
                        if is_skiped:
                            if (real_current_method == ""):
                                continue
                            if (found_type == '2'):
                                is_find_class_name = True
                            if  (found_type == '1'):
                                pre_skip_found = False
                        else:
                            if  (found_type == '1'):
                                if (pre_valid_method_found == True):
                                    if (judge_single_method_is_bad(method_body)):
                                        methods_with_excute_cases.append(method_name[0])
                                    method_name = []
                                    method_body = []
                                else:
                                    pre_valid_method_found = True
                                method_name.append(real_current_method)
                            if  (found_type == '0'):
                                method_body.append(line)
                    if (pre_valid_method_found == True):
                        if (judge_single_method_is_bad(method_body)):
                            methods_with_excute_cases.append(method_name[0])
                    if methods_with_excute_cases:
                        bad_cases[file_path] = methods_with_excute_cases
    return bad_cases

#判断当个方法体中只有只有简单断言
def judge_single_method_is_bad(method_body):
    only_has_simple_assert = True
    len_method = len(method_body)
    #先这样 后面再换成正则表达式
    #res['data'] != []  res['result'], is_(1)    "result") == 1  ['result'] == 1  未覆盖 单引号和双引号都要覆盖
    #res["data"] != []  res["result"], is_(1)    'result') == 1  ["result"] == 1
    #result["result"] == 1
    simple_assert = ['["result"] == 1','["result"]== 1', '[\'result\']==1',
                     '[\'result\'] == 1', '[\'result\']== 1', '[\'result\']==1',
                     '["data"] != []', '["data"]!= []','["data"]!=[]',
                     '[\'data\'] != []','[\'data\']!= []','[\'data\']!=[]',
                     '"result") == 1','"result")== 1','"result")==1',
                     '\'result\') == 1','\'result\')== 1','\'result\')==1',
                     '["result"], is_(1)','["result"],is_(1)',
                     '[\'result\'], is_(1)','[\'result\'],is_(1)',
                     '[\'result\'] == 1','["result"] == 1',
                     'get(\'result\') == 1', 'get("result") == 1']

    len_simple_assert = len(simple_assert)
    for i in range(len_method):
        line = method_body[i]
        if (line.find('assert') == -1):
            continue
        match_result = False
        for j in range(len_simple_assert):
            cur_simple_assert_line = simple_assert[j]
            tmp_match_result = line.find(cur_simple_assert_line)

            if (tmp_match_result != -1):
                match_result = True
                break
        if (not match_result):
            only_has_simple_assert = False
            break
    return only_has_simple_assert

# 找出当前商家在执行的用例数量 2024-11-22修正版
def find_total_cases_in_directory(directory):
    """
    递归遍历目录，查找符合新定义的简单断言的测试用例。

    :param directory: 根目录路径
    :return: 返回一个包含 bad cases 的字典，键是文件路径，值是包含 bad case 的方法列表

    需要过滤 skip方法和类
    """
    test_method_pattern = re.compile(r'.*pytest.mark.skip.*')
    excute_cases = {}
    # 遍历指定目录及其子目录中的所有Python文件
    for root, _, files in os.walk(directory):
        # root = '/Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member'
        for file in files:
            # file = 'test_merchant_member_activity.py'
            if file.startswith('test_') and file.endswith('.py'):
                # file_path /Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member/test_merchant_member_activity.py
                file_path = os.path.join(root, file)
                is_find_class_name = False
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    methods_with_excute_cases = []
                    pre_found = False
                    for line in lines:
                        if is_commented_out_skip(line):
                            continue
                        # 匹配到pytest.mark.skip | 整个class被skip
                        if (test_method_pattern.search(line)):
                            pre_found = True
                        is_skiped = is_find_class_name or pre_found
                        found_type, real_current_method = find_method_name_or_class_name(line)
                        if is_skiped:
                            if (real_current_method == ""):
                                continue
                            if (found_type == '2'):
                                is_find_class_name = True
                            if  (found_type == '1'):
                                pre_found = False
                        else:
                            if  (found_type == '1'):
                                methods_with_excute_cases.append(real_current_method)
                    if methods_with_excute_cases:
                        excute_cases[file_path] = methods_with_excute_cases
    return excute_cases

# 找的不全 废弃
def check_for_bad_cases(method_body, simple_assert_pattern):
    """
    检查方法体是否包含简单断言且没有其他断言。

    :param method_body: 方法体内容
    :param simple_assert_pattern: 简单断言的正则表达式模式
    :return: 如果方法体包含简单断言且没有其他断言则返回 True，否则返回 False
    """
    simple_asserts = []
    other_asserts = []

    for line in method_body:
        if is_commented_out(line):
            continue
        if simple_assert_pattern.search(line):
            simple_asserts.append(line)
        elif 'assert' in line:
            other_asserts.append(line)

    # 判断是否存在只有简单断言并且没有其他的 assert
    return len(simple_asserts) > 0 and not other_asserts


def print_tree(bad_cases):
    """
    打印 bad cases 以树形结构显示。

    :param bad_cases: 包含 bad cases 的字典，键是文件路径，值是包含 bad case 的方法列表
    """
    store_bad_cases = []
    for file_path, methods in bad_cases.items():
        # 打印文件路径
        # 打印每个方法，作为子项
        for method in methods:
            # print ("file_path {} {}".format(file_path, method))
            if (len(method) == 1):
                storeName = file_path + '/' + method
                if str(method).startswith('test_'):
                    store_bad_cases.append([storeName])
            else:
                author = method[0]
                time_str = method[1]
                cur_method = method[2]
                if str(cur_method).startswith('test_'):
                    # print ("file_path {} {} {}".format(file_path + '/' + cur_method, author, method))
                    store_bad_cases.append([file_path + '/' + cur_method, author, time_str])

    # cur_time = timestamp_to_date_str(current_timestamp(), fmt='%Y-%m-%d %H:%M:%S')
    data_header = ["path", "author", "time"]
    print ("len ", len(store_bad_cases))
    cur_time = '-2025-6-30'
    with open(f'/Users/<USER>/Desktop/badCase{cur_time}.csv', 'w', encoding="utf-8") as fobj:
        fobj_write = csv.writer(fobj)
        fobj_write.writerow(data_header)
        fobj_write.writerows(store_bad_cases)
        fobj.flush()

# 找出skip的用例进行治理 2024-11-22修正版
def find_skip_cases_in_directory(directory):
    """
    递归遍历目录，查找符合新定义的简单断言的测试用例。

    :param directory: 根目录路径
    :return: 返回一个包含 bad cases 的字典，键是文件路径，值是包含 bad case 的方法列表

    整个类被skip
    @pytest.mark.config(account="sso_account_zl")
    @pytest.mark.tag('B端-生意通')
    @pytest.mark.skip
    class TestNoahData(object):
    """
    test_method_pattern = re.compile(r'.*pytest.mark.skip.*')
    skip_cases = {}
    # 遍历指定目录及其子目录中的所有Python文件
    for root, _, files in os.walk(directory):
        # root = '/Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member'
        for file in files:
            # file = 'test_merchant_member_activity.py'
            if file.startswith('test_') and file.endswith('.py'):
                # file_path /Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member/test_merchant_member_activity.py
                file_path = os.path.join(root, file)
                is_find_class_name = False
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    methods_with_skip_cases = []
                    pre_found = False
                    for line in lines:
                        if is_commented_out_skip(line):
                            continue
                        # 匹配到pytest.mark.skip | 整个class被skip
                        if (test_method_pattern.search(line)):
                            pre_found = True
                        is_skiped = is_find_class_name or pre_found
                        if is_skiped:
                            found_type, real_current_method = find_method_name_or_class_name(line)
                            if (real_current_method == ""):
                                continue
                            if (found_type == '2'):
                                is_find_class_name = True
                            if  (found_type == '1'):
                                pre_found = False
                                methods_with_skip_cases.append(real_current_method)
                    if methods_with_skip_cases:
                        skip_cases[file_path] = methods_with_skip_cases
    return skip_cases

def find_method_name_or_class_name(line):
    test_method_name_pattern = re.compile(r'def test_(\w+)')
    test_class_name_pattern = re.compile(r'class (\w+)')
    method_name = ""
    found_type = '0'
    match_method_name = test_method_name_pattern.search(line)
    if match_method_name:
        method_name = 'test_' + match_method_name.group(1)
        found_type = '1'
    match_class_name = test_class_name_pattern.search(line)
    # match_class_name = re.search(r'class Test', line)
    if match_class_name:
        method_name = match_class_name.group(1)
        found_type = '2'

    return [found_type, method_name]

def check_for_skip_cases(method_body, simple_assert_pattern):
    """
    检查方法体是否包含简单断言且没有其他断言。

    :param method_body: 方法体内容
    :param simple_assert_pattern: 简单断言的正则表达式模式
    :return: 如果方法体包含简单断言且没有其他断言则返回 True，否则返回 False
    """
    simple_asserts = []
    other_asserts = []

    for line in method_body:
        if is_commented_out(line):
            continue
        if simple_assert_pattern.search(line):
            simple_asserts.append(line)
        elif 'assert' in line:
            other_asserts.append(line)

    # 判断是否存在只有简单断言并且没有其他的 assert
    return len(simple_asserts) > 0 and not other_asserts



def get_last_committers(directory):
    test_method_pattern = re.compile(r'.*pytest.mark.skip.*')
    excute_create_list = {}
    # 遍历指定目录及其子目录中的所有Python文件
    for root, _, files in os.walk(directory):
        # root = '/Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member'
        for file in files:
            # file = 'test_merchant_member_activity.py'
            if file.startswith('test_') and file.endswith('.py'):
                # file_path /Users/<USER>/IdeaProjects/autotestpytest/test-case/merchant_member/test_merchant_member_activity.py
                file_path = os.path.join(root, file)
                command = ["git", "blame", file_path]
                result = subprocess.check_output(command, universal_newlines=True)
                lines = result.splitlines()
                committers = []
                methods_with_excute_cases = []
                pre_found = False
                is_find_class_name = False
                for line in lines:
                    # 每行的格式通常是 "commit_hash (author_name timestamp) line_content"
                    # print ("line ", line)

                    commit_str = line.split('(')[1]

                    # 获取作者信息
                    author = commit_str.strip().split()[0]
                    # 获取代码信息
                    code_str = line.split(')')[1]
                    # 判断时间区间
                    time_str = commit_str.strip().split()[1]
                    if is_commented_out_skip(code_str):
                        continue
                    if (time_str < '2025-01-01'):
                        continue
                    # 匹配到pytest.mark.skip | 整个class被skip
                    if (test_method_pattern.search(code_str)):
                        pre_found = True
                    is_skiped = is_find_class_name or pre_found
                    found_type, real_current_method = find_method_name_or_class_name(code_str)
                    if is_skiped:
                        if (real_current_method == ""):
                            continue
                        if (found_type == '2'):
                            is_find_class_name = True
                        if  (found_type == '1'):
                            pre_found = False
                    else:
                        if  (found_type == '1'):
                            methods_with_excute_cases.append([author, time_str, real_current_method])
                if methods_with_excute_cases:
                    excute_create_list[file_path] = methods_with_excute_cases
    return excute_create_list

def show_cases(cur_cases):

    for file_path, methods in sorted(cur_cases.items()):
        # print (file_path)
        for method in methods:
            if (len(method) == 1):
                storeName = file_path + '/' + method
                if str(method).startswith('test_'):
                    print (storeName)
            else:
                author = method[0]
                time_str = method[1]
                cur_method = method[2]

                if str(cur_method).startswith('test_'):
                    print ("{} {} {}".format(author, time_str, file_path + '/' + cur_method))

if __name__ == '__main__':
    # 指定要扫描的根目录路径
    # test_method_body = ['        res = Http_client(api, json=body, account="sso_account_zl").send_request().json()', '        assert res[\'result\'], is_(1)', '        assert res["result"], is_(1)', '        assert [\'result\'] == 1', '        assert ["result"] == 1', '        assert  "result") == 1','        assert  \'result\') == 1', '        assert  res[\'data\'] != [] ','        assert  res["data"] != [] ','', '    @pytest.mark.p0', '    @pytest.mark.env("online")', '    ']
    #
    # judge_single_method_is_bad(test_method_body)

    directory = '/Users/<USER>/pcUI/test_case'  # 替换为实际路径
    # 查找skip bad cases
    # directoryList = ['ecology','funds','kpplatform','merchant_member','merchant_recruit','message','distribute','shop','user/merchant/kssyt','user/merchant/account','user/merchant/growth','user/merchant/diff','user/merchant/leads','user/merchant/growth/api/Education','user/merchant/merchant360','user/merchant/baomai']
    # 这个是没有Education的 不需要过滤 捞取用例创建人的
    # directoryList = ['distribution', 'assistant', 'customer', 'ecology', 'funds', 'growth', 'merchant_crm', 'merchant_member', 'merchant_recruit', 'merchant_syt', 'merchant_syt_test', 'shop', 'workbench']
    # directoryList = ['distribution', 'marketing_base', 'marketing_prem', 'marketing_mall', 'short_video', 'marketing']
    directoryList = ['assistant','customer','ecology', 'core_link', 'funds', 'growth', 'industry', 'investment_activity', 'jinjian_zzh', 'merchant_member', 'product', 'scm', 'merchant_syt', 'shop', 'trade', 'workbench', 'distribution', 'marketing_base', 'marketing_prem', 'marketing_mall', 'short_video', 'marketing']
    _len = len(directoryList)
    tol_dict = {}
    t_cnt = 0
    print ("start---------------")
    total_cases = {}
    for i in range(_len):
        cur_url = directory + '/' + directoryList[i]
        # cur_cases = find_total_cases_in_directory(cur_url)
        # cur_cases = find_skip_cases_in_directory(cur_url)
        # cur_cases = find_bad_cases_in_directory(cur_url)
        print ("cur_url ", cur_url)
        cur_cases = get_last_committers(cur_url)
        # print ("cur_cases ", cur_cases)
        for file_path, methods in cur_cases.items():
            # print ("file_path ", file_path)
            total_cases[file_path] = methods
        # show_cases(cur_cases)
    print (len(total_cases))
    print_tree(total_cases)

    #
    # skip_cases = find_skip_cases_in_directory(directory)
    #
    # if skip_cases:
    #     print("以下是检测到的 skip cases:")
    #     print_tree(skip_cases)
    # else:
    #     print("未发现 skip cases。")


