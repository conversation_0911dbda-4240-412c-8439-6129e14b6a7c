[pytest]
addopts = --capture=no -p no:cacheprovider --pdbcls=IPython.terminal.debugger:TerminalPdb
filterwarnings =
    ignore::pytest.PytestWarning
    ignore:.*U.*mode is deprecated:DeprecationWarning
junit_family = legacy
python_files = test_*.py *_test.py *_tests.py *_suite.py
python_classes = Test* *Test* *Test *Tests *Suite
python_functions = test_*
markers =
    sub_account: Custom marker for sub_account-related tests
    marker1: custom marker
    marker2: custom marker
    marker3: custom marker
    marker_test_suite: custom marker
    expected_failure: custom marker
    local: custom marker
    remote: custom marker
    offline: custom marker
    develop: custom marker
    qa: custom marker
    ci: custom marker
    e2e: custom marker
    ready: custom marker
    smoke: custom marker
    deploy: custom marker
    active: custom marker
    master: custom marker
    release: custom marker
    staging: custom marker
    production: custom marker
