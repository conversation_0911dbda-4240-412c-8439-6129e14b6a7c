import datetime
import json
import os
import re
import sys
import time
import requests

from utils.count import conn_mysql

__all__ = []

merchant_syt_oncall = 'zhaozhou03'
workbench_oncall = 'gaozhongshu03'
customer_oncall = 'xiashengxuan'
customer_service_oncall = 'shimengtao'


def filter_user(s):
    owner = s.split(',')[-1].strip('\n')
    if 'test_case/assistant' in s:
        return 'zhangpinghui03'
    if 'test_case/merchant_syt' in s:
        if 'test_syt_traffic' in s or "test_traffic_plan" in s:
            return 'zhangpinghui03'
        return merchant_syt_oncall
    if 'test_case/customer' in s:
        return customer_service_oncall
    if 'test_case/workbench' in s:
        return workbench_oncall
    if owner == 'unknown':
        if 'test_case/workbench' in s:
            return workbench_oncall
        if 'test_case/customer' in s:
            return 'shimengtao'
        if 'test_case/assistant' in s:
            return 'zhangpinghui03'
        if 'test_case/merchant_syt' in s:
            return merchant_syt_oncall
        if 'test_case/distribution' in s:
            return "chenshuzhan"
        if 'test_case/core_link/supply_chain' in s:
            return "yanxueyun"
        if 'test_case/scm' in s:
            return "yanxueyun"
        if 'test_case/merchant_member' in s:
            return "zhanghongbin"
        if 'test_case/ecology' in s:
            return "zhouchang03"
        if 'test_case/helper_assistant' in s:
            return "yuanpan"
        if 'test_case/marketing' in s:
            return "yuanpan"
        if 'test_case/short_video' in s:
            return "yuanpan"
        if 'test_case/marketing_prem' in s:
            return "yuanpan"
        if 'test_case/funds' in s:
            return "rongjunqiao"
        if 'test_case/shop' in s:
            return "pengshengpu"
        if 'test_case/growth' in s:
            return "zhanghongbin"
    elif owner == 'wb_zhangchenghong' or owner == 'zhangchenghong':
        return 'gaozhongshu03'
    elif owner == 'dingxiang03':
        return 'litianning'
    elif owner == 'zongxunqiang':
        return 'tangpeng06'
    elif owner == 'vipning':
        return 'litianning'
    elif owner == 'yinzhixin'or owner == 'yangping':
        return 'rongjunqiao'
    elif owner == 'jiangshaohua':
        return 'shimengtao'
    elif owner == 'leiyongcheng':
        return 'zhanghongbin'
    elif owner == 'weijingxiao':
        return 'yanghaiyang03'
    elif owner == 'zhouchang':
        return 'wb_qiangwenhui'
    else:
        return owner


def filter_user_by_html_report():
    if os.path.isfile('fail_list.log'):
        with open(f'fail_list.log', 'r') as f:
            result = f.readlines()
        users = [filter_user(x) for x in result]
        return ' '.join(list(set(['<@=username({})=>'.format(user) for user in users if user and user != ""])))
    else:
        print('日志文件不存在')
        return ''


def send_msg(key, param):
    pattern = r'report(\d+)'
    match = re.search(pattern, param)
    if match:
        report_number = match.group(1)
        report_address = r"C:\Users\<USER>\Desktop\kwaishopuiautotest\report\report" + report_number + ".html"
        pc_new_address = "http://*************/ui/report" + report_number + ".html"
    with open(report_address, "r+", encoding='utf-8') as f:
        for line in f:
            if 'tests ran in' in line:
                time_match = re.search(r'ran in (\d+\.\d+) seconds', line)
                if time_match:
                    time_value = time_match.group(1)
                    time_value = int(float(time_value) / 60)
                    print(f"The time value extracted is: {time_value} seconds")
                else:
                    print("No time value found in the line.")
    user_info = filter_user_by_html_report()
    with open(r"C:\Users\<USER>\Desktop\log.txt", "r+") as f:
        a = f.readline()
        pass_num = re.findall(r'<span class="passed">(.+?) passed', a)
        skip_num = re.findall(r'<span class="skipped">(.+?) skipped', a)
        fail_num = re.findall(r'<span class="failed">(.+?) failed', a)
        error_num = re.findall(r'<span class="error">(.+?) error', a)
    total_info = "UI自动化运行统计 " + skip_num[0] + "个跳过，" + error_num[0] + "个错误，" + fail_num[0] + "个失败，" + \
                 pass_num[0] + "个通过 " + "运行耗时为:" + str(time_value) + "分钟，请尽快处理，完整报告："
    if key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing":
        # with open(r"C:\Users\<USER>\Desktop\logtest.txt", "r+", encoding='UTF-8') as m1:
        #     day2 = datetime.datetime.now()
        #     day_str2 = day2.strftime("%Y-%m-%d")
        #     lines1 = m1.readlines()
        #     for line in lines1:
        #         if '<h2>营销导购-跟播助手' in line:
        #             source_tree1 = '营销导购-跟播助手'
        #             success_num1 = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num1 = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(success_num1) == 0:
        #                 success_num_marketing = 0
        #             else:
        #                 success_num_marketing = success_num1[0]
        #             if len(fails_num1) == 0:
        #                 fail_customer_num1 = 0
        #             else:
        #                 fail_customer_num1 = fails_num1[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert2 = "INSERT INTO pc_ui_autotest_marketing_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree1, int(success_num_marketing), int(fail_customer_num1), now1, day_str2)
        #             conn_mysql(sql_insert2)
        #         elif '<h2>营销工具' in line:
        #             source_tree1 = '营销工具'
        #             success_num1 = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num1 = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(success_num1) == 0:
        #                 success_num_marketing = 0
        #             else:
        #                 success_num_marketing = success_num1[0]
        #             if len(fails_num1) == 0:
        #                 fail_customer_num1 = 0
        #             else:
        #                 fail_customer_num1 = fails_num1[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert2 = "INSERT INTO pc_ui_autotest_marketing_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree1, int(success_num_marketing), int(fail_customer_num1), now1, day_str2)
        #             conn_mysql(sql_insert2)
        #         elif '<h2>营销导购-短视频' in line:
        #             source_tree1 = '营销导购-短视频'
        #             success_num1 = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num1 = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(success_num1) == 0:
        #                 success_num_marketing = 0
        #             else:
        #                 success_num_marketing = success_num1[0]
        #             if len(fails_num1) == 0:
        #                 fail_customer_num1 = 0
        #             else:
        #                 fail_customer_num1 = fails_num1[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert2 = "INSERT INTO pc_ui_autotest_marketing_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree1, int(success_num_marketing), int(fail_customer_num1), now1, day_str2)
        #             conn_mysql(sql_insert2)
        #         elif '<h2>营销导购-精准营销' in line:
        #             source_tree1 = '营销导购-精准营销'
        #             success_num1 = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num1 = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(success_num1) == 0:
        #                 success_num_marketing = 0
        #             else:
        #                 success_num_marketing = success_num1[0]
        #
        #             if len(fails_num1) == 0:
        #                 fail_customer_num1 = 0
        #             else:
        #                 fail_customer_num1 = fails_num1[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert2 = "INSERT INTO pc_ui_autotest_marketing_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree1, int(success_num_marketing), int(fail_customer_num1), now1, day_str2)
        #             conn_mysql(sql_insert2)
        #             break
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(xumeng11)=>'
            user_info += ' <@=username(wb_changboyu)=>'
        source = "marketing"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销自动化】\n" + total_info
        pc_new_address = "http://*************/ui/marketing/report" + report_number + ".html"

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=8a138351-f75d-4aa2-96bc-ab5d259c929c"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing-assistant":
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(wb_zhaojingjing03)=>'
        source = "marketing"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销-跟播助手自动化】\n" + total_info
        pc_new_address = "http://*************/ui/marketing/assistant/report" + report_number + ".html"

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=8a138351-f75d-4aa2-96bc-ab5d259c929c"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing-prem":
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(hongshuoguo)=>'
        source = "marketing"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销-精准营销自动化】\n" + total_info
        pc_new_address = "http://*************/ui/marketing/prem/report" + report_number + ".html"

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=8a138351-f75d-4aa2-96bc-ab5d259c929c"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing-video":
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(xumeng11)=>'
            user_info += ' <@=username(wb_changboyu)=>'
        source = "marketing"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销-短视频自动化】\n" + total_info
        pc_new_address = "http://*************/ui/marketing/video/report" + report_number + ".html"

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=8a138351-f75d-4aa2-96bc-ab5d259c929c"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing-base":
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(hengyongshuang)=>'
        source = "marketing"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销-基础营销】\n" + total_info
        pc_new_address = "http://*************/ui/marketing/base/report" + report_number + ".html"

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=8a138351-f75d-4aa2-96bc-ab5d259c929c"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing-marketing":
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(hongshuoguo)=>'
        source = "marketing"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销-营销工具】\n" + total_info
        pc_new_address = "http://*************/ui/marketing/marketing/report" + report_number + ".html"

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=8a138351-f75d-4aa2-96bc-ab5d259c929c"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-merchant":
        # with open(r"C:\Users\<USER>\Desktop\logcustomer.txt", "r+", encoding='UTF-8') as m:
        #     day1 = datetime.datetime.now()
        #     day_str1 = day1.strftime("%Y-%m-%d")
        #     lines = m.readlines()
        #     for line in lines:
        #         if '<h2>B端-客服' in line:
        #             source_tree = 'B端-客服'
        #             success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(fails_num) == 0:
        #                 fail_customer_num = 0
        #             else:
        #                 fail_customer_num = fails_num[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
        #             print('++++++++++++++' + sql_insert1)
        #             conn_mysql(sql_insert1)
        #
        #         elif '<h2>B端-分销' in line:
        #             source_tree = 'B端-分销'
        #             success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(fails_num) == 0:
        #                 fail_customer_num = 0
        #             else:
        #                 fail_customer_num = fails_num[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
        #             conn_mysql(sql_insert1)
        #         elif '<h2>B端-直播助手' in line:
        #             source_tree = 'B端-直播助手'
        #             success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
        #             fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
        #             if len(fails_num) == 0:
        #                 fail_customer_num = 0
        #             else:
        #                 fail_customer_num = fails_num[0]
        #             now1 = str(int(time.time() * 1000))
        #             sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
        #                 source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
        #             conn_mysql(sql_insert1)
                # elif '<h2>B端-资金' in line:
                #     source_tree = 'B端-资金'
                #     success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
                #     fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
                #     if len(fails_num) == 0:
                #         fail_customer_num = 0
                #     else:
                #         fail_customer_num = fails_num[0]
                #     now1 = str(int(time.time() * 1000))
                #     sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
                #         source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
                #     conn_mysql(sql_insert1)
                # elif '<h2>B端-生意通' in line:
                #     source_tree = 'B端-生意通'
                #     success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
                #     fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
                #     if len(fails_num) == 0:
                #         fail_customer_num = 0
                #     else:
                #         fail_customer_num = fails_num[0]
                #     now1 = str(int(time.time() * 1000))
                #     sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
                #         source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
                #     conn_mysql(sql_insert1)
                # elif '<h2>B端-工作台' in line:
                #     source_tree = 'B端-工作台'
                #     success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
                #     fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
                #     if len(fails_num) == 0:
                #         fail_customer_num = 0
                #     else:
                #         fail_customer_num = fails_num[0]
                #     now1 = str(int(time.time() * 1000))
                #     sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
                #         source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
                #     conn_mysql(sql_insert1)
                # # elif '<h2>B端-治理' in line:
                #     source_tree = 'B端-治理'
                #     success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
                #     fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
                #     if len(fails_num) == 0:
                #         fail_customer_num = 0
                #     else:
                #         fail_customer_num = fails_num[0]
                #     now1 = str(int(time.time() * 1000))
                #     sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
                #         source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
                #     conn_mysql(sql_insert1)
                # elif '<h2>B端-商家私域' in line:
                #     source_tree = 'B端-商家私域'
                #     success_num = re.findall(r'<span class="tag passed">成功: (.+?)<', line)
                #     fails_num = re.findall(r'<span class="tag failed">失败: (.+?)<', line)
                #     if len(fails_num) == 0:
                #         fail_customer_num = 0
                #     else:
                #         fail_customer_num = fails_num[0]
                #     now1 = str(int(time.time() * 1000))
                #     sql_insert1 = "INSERT INTO pc_ui_autotest_merchant_record_detail (source_tree,success_num,fails_num,create_time,run_time) VALUES ('{}','{}','{}','{}','{}')".format(
                #         source_tree, int(success_num[0]), int(fail_customer_num), now1, day_str1)
                #     conn_mysql(sql_insert1)
                #     break
        source = "merchant"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-客服自动化】\n" + total_info
        pc_new_address = "http://*************/ui/merchant/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-merchant-two":
        source = "merchantTwo"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家(私域、工作台、治理、店铺)自动化】\n" + total_info
        pc_new_address = "http://*************/ui/merchantTwo/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-ecology":
        source = "ecology"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-治理自动化】\n" + total_info
        if int(error_num[0]) > 0 or int(fail_num[0]) > 0:
            user_info += ' <@=username(wb_qiangwenhui)=>'
        pc_new_address = "http://*************/ui/ecology/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-shop":
        source = "shop"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-店铺自动化】\n" + total_info
        if int(error_num[0]) > 0 or int(fail_num[0]) > 0:
            user_info += ' <@=username(wb_leiwenyu)=>'
        pc_new_address = "http://*************/ui/shop/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-product":
        source = "product"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商品自动化】\n" + total_info
        pc_new_address = "http://*************/ui/product/report" + report_number + ".html"
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(chuyifeng)=>'
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-funds":
        source = "funds"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【资金自动化】\n" + total_info
        pc_new_address = "http://*************/ui/funds/report" + report_number + ".html"
    elif key == "28010f09-dfe7-4795-acf9-166be203c06c":
        source = "trade"
        total_info = "【交易-订单自动化】\n" + total_info
        # if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) and "zhangli17" not in user_info:
        #     user_info += ' <@=username(zhangli17)=>'

        # 大群也发一份报告
        pc_new_address = "http://*************/ui/tradeOrder/report" + report_number + ".html"
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=630ed201-1e98-4704-893f-5052ebbc35ac"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
    elif key == "28010f09-dfe7-4795-acf9-166be203c06c-industry":
        source = "industry"
        total_info = "【交易-履约自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(fangming03)=>'

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=630ed201-1e98-4704-893f-5052ebbc35ac"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + param
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
        key = "28010f09-dfe7-4795-acf9-166be203c06c"
        pc_new_address = "http://*************/ui/industry/report" + report_number + ".html"
    elif key == "28010f09-dfe7-4795-acf9-166be203c06c-service":
        source = "service"
        total_info = "【交易-服务市场自动化】\n" + total_info
        # if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) and "zhangli17" not in user_info:
        #     user_info += ' <@=username(zhangli17)=>'

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=630ed201-1e98-4704-893f-5052ebbc35ac"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
        key = "28010f09-dfe7-4795-acf9-166be203c06c"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-supply":
        source = "supply"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【供应链自动化】\n" + total_info
        pc_new_address = "http://*************/ui/supply/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-pay":
        source = "pay"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【支付自动化】\n" + total_info
        pc_new_address = "http://*************/ui/pay/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-merchant-syt":
        source = "syt"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-生意通自动化】\n" + total_info
        pc_new_address = "http://*************/ui/syt/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-distribute":
        source = "distribute"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-分销自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(chenshuzhan)=>'
        if 'yanxueyun' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        pc_new_address = "http://*************/ui/distribute/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-distribute-leader":
        source = "distribute"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-分销团长自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(chenshuzhan)=>'
        if 'yanxueyun' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if 'zhanle' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if (int(error_num[0]) == 0 and int(fail_num[0]) == 0) :
            if 'chenshuzhan' in user_info:
                user_info = user_info.replace('chenshuzhan', '')
        pc_new_address = "http://*************/ui/distribute/leader/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-distribute-merchant":
        source = "distribute"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-分销商家自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(chenshuzhan)=>'
        if 'yanxueyun' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if 'zhanle' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if (int(error_num[0]) == 0 and int(fail_num[0]) == 0) :
            if 'chenshuzhan' in user_info:
                user_info = user_info.replace('chenshuzhan', '')
        pc_new_address = "http://*************/ui/distribute/merchant/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-distribute-promoter":
        source = "distribute"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-分销达人自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(chenshuzhan)=>'
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(wb_huoyangyang)=>'
        if 'yanxueyun' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if 'zhanle' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if (int(error_num[0]) == 0 and int(fail_num[0]) == 0) :
            if 'chenshuzhan' in user_info:
                user_info = user_info.replace('chenshuzhan', '')
        pc_new_address = "http://*************/ui/distribute/promoter/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-distribute-kwaimoney":
        source = "distribute"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商家-分销快赚客自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(liyanfei)=>'
        if 'yanxueyun' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        if 'zhanle' in user_info:
            user_info = '<@=username(chenshuzhan)=>'
        pc_new_address = "http://*************/ui/distribute/kwaimoney/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-merchant-syt-live":
        source = "syt"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【生意通-直播自动化】\n" + total_info
        pc_new_address = "http://************/ui/syt/live/report" + report_number + ".html"
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0):
            # 小群也发一份报告
            url = "https://kim-robot.kwaitalk.com/api/robot/send?key=ca6e82ff-5a07-44c6-aca2-fcf80d5f8091"
            pc_new_address = "http://************/ui/syt/live/report" + report_number + ".html"
            payload = json.dumps({
                "msgtype": "text",
                "text": {
                    "content": user_info + "\n" + total_info + pc_new_address
                }
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=payload)
            time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-merchant-syt-commodity":
        source = "syt"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【生意通-商品卡、商品自动化】\n" + total_info
        pc_new_address = "http://************/ui/syt/commodity/report" + report_number + ".html"

        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0):
            # 小群也发一份报告
            url = "https://kim-robot.kwaitalk.com/api/robot/send?key=ca6e82ff-5a07-44c6-aca2-fcf80d5f8091"
            pc_new_address = "http://************/ui/syt/commodity/report" + report_number + ".html"
            payload = json.dumps({
                "msgtype": "text",
                "text": {
                    "content": user_info + "\n" + total_info + pc_new_address
                }
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=payload)
            time.sleep(2)
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-merchant-syt-home":
        source = "syt"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【生意通-首页、大屏自动化】\n" + total_info
        pc_new_address = "http://************/ui/syt/home/<USER>" + report_number + ".html"
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0):
            # 小群也发一份报告
            url = "https://kim-robot.kwaitalk.com/api/robot/send?key=ca6e82ff-5a07-44c6-aca2-fcf80d5f8091"
            pc_new_address = "http://************/ui/syt/home/<USER>" + report_number + ".html"
            payload = json.dumps({
                "msgtype": "text",
                "text": {
                    "content": user_info + "\n" + total_info + pc_new_address
                }
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=payload)
            time.sleep(2)
    elif key == "28010f09-dfe7-4795-acf9-166be203c06c-refund":
        source = "refund"
        total_info = "【交易-售后自动化】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0) :
            user_info += ' <@=username(huwen03)=>'

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=630ed201-1e98-4704-893f-5052ebbc35ac"
        pc_new_address = "http://*************/ui/trade/report" + report_number + ".html"
        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
        key = "28010f09-dfe7-4795-acf9-166be203c06c"
    elif key == "28010f09-dfe7-4795-acf9-166be203c06c-industry-prt":
        source = "industryPRT"
        total_info = "【交易-履约自动化PRT准出任务】\n" + total_info
        if (int(error_num[0]) > 0 or int(fail_num[0]) > 0):
            user_info += ' <@=username(fangming03)=>'

        # 大群也发一份报告
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=630ed201-1e98-4704-893f-5052ebbc35ac"

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + param
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        time.sleep(2)
        key = "28010f09-dfe7-4795-acf9-166be203c06c"
        pc_new_address = "http://*************/ui/industry/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-marketing-prt":
        source = "marketingPRT"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【营销自动化PRT准出任务营销】\n" + total_info
        pc_new_address = "http://*************/ui/test/report" + report_number + ".html"
    elif key == "630ed201-1e98-4704-893f-5052ebbc35ac-product-prt":
        source = "productPRT"
        key = "630ed201-1e98-4704-893f-5052ebbc35ac"
        total_info = "【商品PRT准出任务营销】\n" + total_info
        pc_new_address = "http://*************/ui/test/report" + report_number + ".html"
    elif key == "a9a32379-396b-4bfa-9c0a-6a50f13477ef-test":
        source = "test"
        key = "a9a32379-396b-4bfa-9c0a-6a50f13477ef"
        total_info = "【PC UI自动化调试任务】\n" + total_info
        pc_new_address = "http://*************/ui/test/report" + report_number + ".html"
    else:
        source = "other"

    if source != 'test':
        # 记录 近7天任务P90的耗时
        sqlP90num = conn_mysql(
            "SELECT CAST(COUNT(if(DATEDIFF(CURRENT_DATE(), run_day) <= 6 and DATEDIFF(CURRENT_DATE(), run_day) >= 0 and source = '{source}', id, null) ) * 0.1 AS SIGNED) as a FROM pc_ui_autotest_record_detail".format(
                source=source))
        print("P90的条数: " + str(sqlP90num))

        number = re.findall(r'\d+', str(sqlP90num))
        sqlSelect = conn_mysql(
            "SELECT AVG(run_minute) AS average_score FROM (SELECT run_minute FROM pc_ui_autotest_record_detail ORDER BY if(DATEDIFF(CURRENT_DATE(), run_day) <= 6 and DATEDIFF(CURRENT_DATE(), run_day) >= 0 and source = '{source}', id, null) DESC LIMIT {number}) AS top_ten".format(
                source=source, number=int(number[0])))
        print("P90的任务耗时为: " + str(sqlSelect))
        if int(number[0]) > 0:
            p90time = int(re.findall(r'\d+', str(sqlSelect))[0])
        else:
            p90time = 0

        # sql_update = conn_mysql(
        #     "UPDATE pc_ui_autotest_case_num SET p90_time_7_day = '{p90time}' WHERE source = '{source}'".format(
        #         p90time=p90time, source=source))
        # print(sql_update)

        total_num = int(error_num[0]) + int(fail_num[0]) + int(pass_num[0])
        now = str(int(time.time() * 1000))
        day = datetime.datetime.now()
        day_str = day.strftime("%Y-%m-%d")
        sql_insert = "INSERT INTO pc_ui_autotest_record_detail (source,skip_num,error_num,fail_num,pass_num,total_num,run_time,run_day,run_minute,report,P90,task_num) VALUES ('{}','{}','{}','{}','{}','{}','{}','{}','{}','{}','{}','{}')".format(
            source, int(skip_num[0]), int(error_num[0]), int(fail_num[0]), int(pass_num[0]), total_num, now, day_str,
            time_value, pc_new_address, p90time, report_number)
        print(sql_insert)

        # 判断是否发送高危提醒

        error_time = conn_mysql(
            "SELECT error_time FROM pc_ui_autotest_case_num WHERE source = '{source}'".format(source=source))
        error_time = int(re.findall(r'\d+', str(error_time))[0])

        if (int(error_num[0]) + int(fail_num[0])) == 0:
            error_time = 0

        if (int(error_num[0]) + int(fail_num[0])) > 0:
            error_time = error_time + 1

        if int(error_time) >= 3:
            url = "https://kim-robot.kwaitalk.com/api/robot/send?key=" + key

            payload = json.dumps({
                "msgtype": "text",
                "text": {
                    "content": "【高风险case】" + "\n" + "以下case最近连续失败次数>=" + str(error_time) + "\n" +
                               "请确认是否正确定位根因，及时避免线上问题" + "\n" + user_info
                }
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=payload)

        # 执行结果写入db
        conn_mysql(sql_insert)

        # sql_update = "UPDATE pc_ui_autotest_case_num SET total_num = '{total_num}', error_time = '{error_time}' WHERE source = '{source}'".format(
        #     total_num=total_num, source=source, error_time=error_time)
        # print(sql_update)
        # # 执行结果写入db
        # conn_mysql(sql_update)

        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=" + key

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return response
    else:
        url = "https://kim-robot.kwaitalk.com/api/robot/send?key=" + key

        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": user_info + "\n" + total_info + pc_new_address
            }
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return response


if __name__ == "__main__":
    argv = sys.argv[1:]
    print("wjx test:", argv)
    send_msg(argv[0], argv[1])
