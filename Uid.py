import pandas as pd

# 数据格式
ACCOUNT_CONFIG_ONLINE = {
    'returnnull': {'account': '***********', 'password': 'guessMe@2022', 'type': 'mobile', 'uid': '**********'},
    'gaohuzhen': {'account': '***********', 'password': 'Ghz@test123', 'type': 'mobile', 'uid': '**********'},
    'lijinglei': {'account': '***********', 'password': 'zhzh9999', 'type': 'mobile', 'uid': '**********'},
    'bjfeng': {'account': '***********', 'password': 'kkk111111', 'type': 'mobile', 'uid': '**********'},
    'huoyangyang': {'account': '***********', 'password': 'test12345678', 'type': 'mobile', 'uid': '**********'},

    'zhangchenghong': {'account': '***********', 'password': 'dstest@123', 'type': 'mobile', 'uid': '**********'},

    "syt_account": {"account": "***********", "password": "ks1234567", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "syt_account_admin": {"account": "***********", "password": "csr123456", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    "clj": {"account": "***********", "password": "chenlijun1227", "type": "mobile", "remark": "kssyt",
            "uid": "**********"},
    "sanzhimao":{"account": "***********", "password": "dstest@123", "type": "mobile", "remark": "kssyt",
            "uid": "**********"},
    "xiaohs":{"account": "***********", "password": "zxcvbnm0910", "type": "mobile", "uid": "*********"},
    # "xiaohs":{"account": "***********", "password": "123456ly", "type": "mobile", "uid": "**********"},
    "xiaohuishuang":{"account": "***********", "password": "abc123456", "type": "mobile", "uid": "**********"},
    "syt_account_member": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    "syt_account_zl": {"account": "***********", "password": "test1234", "type": "mobile","remark": "kssyt", "uid": "**********",
                          "countryCode": "+86"},
    "syt_account_wj": {"account": "***********", "password": "test123456", "type": "mobile","remark": "kssyt", "uid": "**********",
                          "countryCode": "+86"},
    # 售后
    "refund_account": {"account": "***********", "password": "dianlaotie000", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},
    "huwen": {"account": "***********", "password": "haha123456", "type": "mobile", "uid": "**********",
              "countryCode": "+86"},
    "sanzhimao": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
                  "countryCode": "+86"},

    "live_plan_account": {"account": "***********", "password": "csr123456", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    "live_screen_account": {"account": "***********", "password": "qa123123", "type": "mobile", "uid": "**********",
                            "countryCode": "+86"},
    # 供应链账号 child：*********** wy@123456 baba：***********. qweasd111
    "supply_account_child": {"account": "***********", "password": "wy@123456", "type": "mobile",
                             "uid": "***************",
                             "countryCode": "+86"},

    "supply_account": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "supply_account_template":{"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "supply_express_account": {"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "wb_account": {"account": "***********", "password": "cc1234566", "type": "mobile", "uid": "**********",
                   "countryCode": "+86"},

    "gaozhongshu": {"account": "***********", "password": "gzs19921217", "type": "mobile", "uid": "*********",
                    "countryCode": "+86"},

    "gaozhongshu1": {"account": "***********", "password": "gzs17031217", "type": "mobile", "uid": "**********",
                     "countryCode": "+86"},

    # 工作台子账号
    "child_account": {"account": "***********", "password": "Cc@123456", "type": "mobile", "uid": "***************",
                      "countryCode": "+86"},
    # 客服子账号B
    "detest017_74": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "div.shopSelect--G-H8I > div:nth-child(6)"},
    # 客服子账号A
    "detest017_76": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                     "child": True,
                     "default_log_select_page": "div.kwaishop-login-pc-modal-body > div:nth-child(1) > div:nth-child(1)"},
    # 客服主账号
    "customer_merchant": {"account": "***********", "password": "hkk.123456", "type": "mobile", "uid": "**********"},

    # 客服子账号丁湘
    "detest017_75": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "//*[text()='丁崽']"},
    "detest017_75_2": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                       "child": True, "default_log_select_page": "//*[text()='地猫小店']"},

    "customer-jsh": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "//*[contains(text(),'Csr')]"},

    "customer-***********":{"account": "***********", "password": "ltn@5090791", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "//*[contains(text(),'七里香的小店')]"},

    "customer-z": {"account": "***********", "password": "ltn5090791", "type": "mobile", "uid": "**********"},

    # 商品账号
    "product_account": {"account": "***********", "password": "ks654321", "type": "mobile", "uid": "**********",
                        "countryCode": "+86"},
    "product_account_2": {"account": "***********", "password": "ks654321", "type": "mobile", "uid": "********",
                        "countryCode": "+86"},
    # 订单账号

    "order_account": {"account": "***********", "password": "abcdefg987654321", "type": "mobile", "uid": "*********",
                      "countryCode": "+86"},

    # 跨境商家
    "clearance_account": {"account": "***********", "password": "kuajing666", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},

    # 物流相关账号
    "logistics_account": {"account": "***********", "password": "Fahuo666", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    # 跟播助手账号
    "helper_assistant_preLive": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "helper_assistant_preLive_child": {"account": "***********", "password": "zjj123456", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "helper_assistant_onLive": {"account": "***********", "password": "aa112233", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    # 短视频
    "short_video": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    # 直播计划账号
    "reservation_account": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                            "countryCode": "+86"},

    # 券码核销的账号
    "locallife_account": {"account": "***********", "password": "huangzt!2022", "type": "mobile",
                          "uid": "**********",
                          "countryCode": "+86"},
    # 商家会员账号
    "member_account": {"account": "***********", "password": "csr123456", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "member_account2": {"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                        "countryCode": "+86"},
    "lyh_UI": {"account": "***********", "password": "ks1234567", "type": "mobile", "uid": "2337192488",
               "countryCode": "+86"},
    "lyh_sub": {"account": "15936923392", "password": "ks123456", "type": "mobile", "uid": "2363852547",
                "countryCode": "+86"},
    # 资金账号
    "funds_account_01": {"account": "***********", "password": "abcdefg987654321", "type": "mobile", "uid": "*********",
                         "countryCode": "+86"},
    "funds_account_02": {"account": "***********", "password": "kuajing666", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "funds_account_03": {"account": "13161835001", "password": "yangping123", "type": "mobile", "uid": "2465049547",
                         "countryCode": "+86"},
    "funds_account_04": {"account": "15738857909", "password": "qa123456", "type": "mobile", "uid": "2891254409",
                         "countryCode": "+86"},
    "funds_account_08": {"account": "15645852025", "password": "qa123456", "type": "mobile", "uid": "3468730187",
                         "countryCode": "+86"},
        #个人店-未进件-已认真
    "funds_account_05": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
        #旗舰店-已进件-已认真
    "funds_account_06": {"account": "13161835001", "password": "yangping123", "type": "email",
                 "remark": "merchant","uid": "2465049547"},
        # 个人店-已进件-已认真
    "funds_account_07": {"account": "19700785002", "password": "qa123456", "type": "mobile", "uid": "4144384378",
                         "countryCode": "+86"},

    # 招商账号
    'rz_login': {"account": "***********", "password": "wyztest@1234", "type": "mobile", "uid": "**********",
                 "countryCode": "+86"},
    'gd_login': {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                 "countryCode": "+86"},

    # 分销账号
    "wb_huoyangyang": {"account": "***********", "password": "test12345678", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},
    "zhangwei": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},
    "wb_caijinwei": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
                     "countryCode": "+86"},
    "liuxiaohui07": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
                     "countryCode": "+86"},
    "maliya": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "taoheliang": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "nashui": {"account": "***********", "password": "wyztest@1234", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "zhudong": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "*********",
                   "countryCode": "+86"},
    "lizhiqiang06": {"account": "***********", "password": "test12345678", "type": "mobile", "uid": "**********",
                "countryCode": "+86"},
    "chenshuzhan": {"account": "***********", "password": "tom123456", "type": "mobile", "uid": "**********",
                "countryCode": "+86"},
    # 子账号【商家&团长】
    "bypass_account": {"account": "***********", "password": "344354", "type": "mobile", "uid": "***************",
                       "countryCode": "+86"},
    # 服务市场服务商主账号
    "service_market_provider": {"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                                "countryCode": "+86"},
    # 服务市场服务商主账号
    "service_market_provider_2": {"account": "***********", "password": "ks123456", "type": "mobile",
                                  "uid": "**********", "countryCode": "+86"},
    # 服务市场商家主账号
    "service_market_merchant": {"account": "***********", "password": "test112233", "type": "mobile",
                                "uid": "*********", "countryCode": "+86"},
    # 服务市场商家主账号
    "service_market_merchant_2": {"account": "***********", "password": "test1234", "type": "mobile",
                                  "uid": "**********", "countryCode": "+86"},
    # 服务市场服务商子账号
    "service_market_provider_child": {"account": "***********", "password": "test.1234", "type": "mobile",
                                      "uid": "***************", "countryCode": "+86", "child": True,
                                      "default_log_select_page": "//*[text()='东阳市横店鑫荣影视工作室']"},
    # 服务市场商家子账号
    "service_market_merchant_child": {"account": "***********", "password": "test.1234", "type": "mobile",
                                      "uid": "***************", "countryCode": "+86", "child": True,
                                      "default_log_select_page": "//*[text()='小黑不黑工作室']"},
    # 治理商家账号
    "themis": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},

    # 营销商家账号
    "kdstest102": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "marketing": {"account": "***********", "password": "lrx123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "auto_drill_good_luck_lottery": {"account": "***********", "password": "zjj123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    #会员群聊账号
    #会员群聊账号
    "marketing_member": {"account": "***********", "password": "hsg123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    'hanyanping': {'account': 'hanyanping', 'password': 'hyp@HYP123', 'type': 'mobile', 'uid': '**********'},

    # 商家成长账号
    "zhengguihua": {'account': '***********', 'password': 'a1234567', 'type': 'mobile', 'uid': '**********'},
    "csr": {'account': '***********', 'password': 'csr123456', 'type': 'mobile', 'uid': '**********'},

    #备店账号
    "zgh_beidian": {'account': '***********', 'password': 'a12345678', 'type': 'mobile', 'uid': '**********'},

    # 精准营销测试账号
    "zengjingwen": {"account": "***********", "password": "test0000", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "pengshengpu": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "xuwei": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "didengke": {"account": "***********", "password": "kkk111111", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "sanbanfu": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                 "countryCode": "+86"},

}

# 提取 uid 数据
uids = [{'name': key, 'uid': value['uid']} for key, value in ACCOUNT_CONFIG_ONLINE.items()]

# 转换为 DataFrame
df = pd.DataFrame(uids)

# 导出到 Excel
output_file = 'uids.xlsx'
df.to_excel(output_file, index=False)

print(f"UID 数据已成功导出到 {output_file}")
