appnope==0.1.4
asttokens==2.4.1
attrs==23.1.0
beautifulsoup4==4.12.2
behave==1.2.6
cacheout==0.13.1
certifi==2023.7.22
cffi==1.15.1
chardet==5.2.0
charset-normalizer==3.2.0
clipboard==0.0.4
colorama==0.4.6
comm==0.2.2
cryptography==41.0.3
cssselect==1.2.0
ddt==1.7.0
debugpy==1.8.1
decorator==5.1.1
document==1.0
exceptiongroup==1.1.3
execnet==2.0.2
executing==2.0.1
fasteners==0.18
filelock==3.12.2
h11==0.14.0
idna==3.4
importlib-metadata==6.8.0
iniconfig==2.0.0
ipykernel==6.29.4
ipython==8.13.0   # 8.24.0
jedi==0.19.1
jupyter_client==8.6.1
jupyter_core==5.7.2
markdown-it-py==3.0.0
matplotlib-inline==0.1.7
mdurl==0.1.2
more-itertools==10.1.0
MouseInfo==0.1.3
nest-asyncio==1.6.0
nose==1.3.7
numpy==1.24.4
opencv-contrib-python==********
opencv-python==********
outcome==1.2.0
packaging==23.1
parameterized==0.9.0
parse==1.19.1
parse-type==0.6.2
parso==0.8.4
pdbp==1.4.6
pexpect==4.9.0
pillow==10.3.0
platformdirs==3.10.0
pluggy==1.2.0
prompt-toolkit==3.0.43
psutil==5.9.8
ptyprocess==0.7.0
pure-eval==0.2.2
py==1.11.0
PyAutoGUI==0.9.54
pycparser==2.21
PyGetWindow==0.0.9
Pygments==2.16.1
PyHamcrest==2.1.0
PyMsgBox==1.0.9
pynose==1.4.8
pyobjc-core==10.2
pyobjc-framework-Cocoa==10.2
pyobjc-framework-Quartz==10.2
pyOpenSSL==23.2.0
pyotp==2.9.0
pyperclip==1.8.2
pyproject_hooks==1.0.0
PyRect==0.2.0
PyScreeze==0.1.30
PySocks==1.7.1
pytest==7.4.0
pytest-forked==1.6.0
pytest-html==2.0.1
pytest-metadata==3.0.0
pytest-ordering==0.6
pytest-rerunfailures==12.0
pytest-xdist==3.3.1
python-dateutil==2.9.0.post0
pytweening==1.2.0
PyYAML==6.0.1
pyzmq==26.0.3
requests==2.31.0
rich==13.5.2
rubicon-objc==0.4.9
sbvirtualdisplay==1.2.0
selenium==4.11.2
seleniumbase==4.17.6
six==1.16.0
sniffio==1.3.0
sortedcontainers==2.4.0
soupsieve==2.4.1
stack-data==0.6.3
tabcompleter==1.2.1
tomli==2.0.1
tornado==6.4
traitlets==5.14.3
trio==0.22.2
trio-websocket==0.10.3
typing_extensions==4.7.1
urllib3==1.26.16
urllib3-secure-extra==0.1.0
wcwidth==0.2.13
wsproto==1.2.0
zipp==3.16.2

PyMySQL~=1.1.1
pandas~=2.2.2
webdriver-manager~=4.0.2