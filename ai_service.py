import os
import json
import logging
import requests
from flask import Flask, request, jsonify
from flask_cors import CORS
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# Langbridge服务配置
LANG_BRIDGE_URL = "https://langbridge.corp.kuaishou.com/gateway/langbridge/model/service"
LANG_BRIDGE_HEADERS = {
    "Content-Type": "application/json",
    "Cookie": "soft_did=1619580708547; apdid=97a329cc-64d2-4705-baf3-6d452f0311bc059f16e65350c79eb2fa86f2cc7ab223:1749730913:1; hdige2wqwoino=xHKfYnct42pJ4rPZnzeMxers3GsDzpci4647747e; didv=1750145837000; idp_theme_change_identification=undefined; ksCorpDeviceid=805a2448-5a1f-43a3-a00b-bd53cdeca330; _ga=GA1.1.1571360481.1749736745; _ga_F6CM1VE30P=GS2.1.s1750918881$o3$g1$t1750918933$j8$l0$h0; 140387989_@@@_userId=2359894008; 140387989_@@@_apdid=9e23d335-e867-46fc-92c0-8a848b33c131fbb53c757db170e57b051e8c59ea4a57:1751957212:1; 140387989_@@@_weblogger_did=web_753482708302F2EB; bUserId=1000015384937; 140389506_@@@_apdid=c0ad76cb-2b21-4c49-800c-70700ea5c1ea5c953a7fcb0be9ba414d4e98ad14427a:1752147763:1; 140389506_@@@_did=web_hsin3ezx8k47sbh97io7w9aqv1jbtlve; 140389506_@@@_hdige2wqwoino=DpjC3MhJDFH6YFK5t4hM7wJzxBkiixmc97c940ec; 140389506_@@@_soft_did=1619580708547; 140389506_@@@_sid=kuaishou.shop.b; 140389506_@@@_ehid=8fTTQ-souUdIuaJOjIKfpjrF29nsySI_uUTNr; 140389613_@@@_apdid=107f4af1-0baf-462d-af2f-897e5922ac02a890c9dcfd4c9da4e95e177fbeaaa88c:1752155493:1; 140389613_@@@_did=web_ia7p3vd4i1ox3glqjmriclmj2hgrmte1; 140389613_@@@_hdige2wqwoino=DpjC3MhJDFH6YFK5t4hM7wJzxBkiixmc97c940ec; 140389613_@@@_soft_did=1619580708547; 140389613_@@@_ehid=06GlGketQbuje1ZV516Bn1ksHVyeBVMPxc2Sc; 140389613_@@@_sid=kuaishou.shop.b; K-Uims-Token=fa9936f8db658924b7bc920773714de3; K-Csrf-Token=c2095ea52f02d0c30c93512cbb5164c8; sid=kshop.api; did=web_bac810e72c50e3664e3bb0ed12c7b87d; userId=2337072173; accessproxy_session=2b3e8d08-8209-4d10-9523-f3b64f7dd4fb; ehid=7g-guoIkbgIpQOf7r9omz5ve8oOFkZOillyYp"
    # 替换为您的固定cookie值
}
BIZ_KEY = "c590b19ac7c544d8b4989f53bd3fa95d"  # 替换为您的实际bizKey
MODEL_NAME = "cloud/DeepSeek-R1-250528"  # 或您指定的模型

# 提示词模板（优化版，增加上下文信息）
PROMPT_TEMPLATE = """
基于以下操作生成3个有价值的测试想法。
请确保每个想法都包含可操作的elementXpath。操作都为click点击操作
测试想法：
URL: {url}
操作: {actions}
输出JSON: {{"ideas": [{{"description":"...", "type":"click", "priority":"...", "elementXpath":"..."}}]}}
"""


def call_langbridge_with_retry(payload, max_retries=3, initial_delay=1):
    """带重试机制的Langbridge调用"""
    for attempt in range(max_retries):
        try:
            response = requests.post(
                LANG_BRIDGE_URL,
                headers=LANG_BRIDGE_HEADERS,
                json=payload,
                timeout=120  # 增加超时时间
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.Timeout:
            logger.warning(f"Langbridge调用超时，第{attempt + 1}次重试...")
            time.sleep(initial_delay * (2 ** attempt))  # 指数退避
        except requests.exceptions.RequestException as e:
            logger.error(f"Langbridge调用失败: {str(e)}")
            if attempt == max_retries - 1:
                raise
            time.sleep(initial_delay * (2 ** attempt))

    return None


def extract_json_from_text(text):
    """尝试从文本中提取JSON结构"""
    try:
        # 先尝试直接解析整个文本
        return json.loads(text)
    except json.JSONDecodeError:
        pass

    try:
        # 尝试找到第一个{和最后一个}
        start_idx = text.find('{')
        end_idx = text.rfind('}') + 1
        if start_idx >= 0 and end_idx > start_idx:
            json_str = text[start_idx:end_idx]
            return json.loads(json_str)
    except json.JSONDecodeError:
        pass

    return None


@app.route('/generate-test-ideas', methods=['POST'])
def generate_test_ideas():
    """生成测试想法API接口"""
    try:
        # 获取请求数据
        data = request.json
        logger.info(f"收到请求，操作数量: {len(data.get('actions', []))}")
        logger.info(data)

        # 提取关键信息
        page_context = data.get("pageContext", {})
        url = page_context.get("url", "未知URL")
        title = page_context.get("title", "未知标题")

        # 构建操作序列描述（增加上下文信息）
        actions = []
        for i, action in enumerate(data.get("actions", []), 1):
            action_type = action.get('type', '未知操作')
            xpath = action.get('elementXpath', '')
            value = action.get('value', '')

            # 新增上下文信息
            context_info = []
            if action.get('parentXpath'):
                context_info.append(f"父节点: {action['parentXpath']}")
            if action.get('siblingXpaths'):
                context_info.append(f"兄弟节点: {action['siblingXpaths']}")
            if action.get('childrenXpaths'):
                context_info.append(f"子节点: {action['childrenXpaths']}")

            context_str = " | ".join(context_info) if context_info else "无上下文"

            action_desc = f"{i}. {action_type} - {xpath} | {context_str}"
            if value:
                action_desc += f" (值: {value[:10]}...)"  # 只取前10个字符

            actions.append(action_desc)

        # 构建提示词
        prompt = PROMPT_TEMPLATE.format(
            url=url,
            title=title,
            actions="\n".join(actions)
        )

        logger.debug(f"构建的提示词:\n{prompt}")

        # 构建Langbridge请求体
        langbridge_payload = {
            "model": MODEL_NAME,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0,
            "operator": "system",
            "bizKey": BIZ_KEY
        }

        # 调用Langbridge服务（带重试机制）
        langbridge_response = call_langbridge_with_retry(langbridge_payload)

        if not langbridge_response:
            return jsonify({
                "error": "Langbridge服务调用失败",
                "message": "多次重试后仍失败"
            }), 500

        logger.info(f"Langbridge响应状态: {langbridge_response.get('result', '未知')}")

        # 检查响应结构
        if langbridge_response.get("result") != 1:
            return jsonify({
                "error": "Langbridge服务返回错误",
                "code": langbridge_response.get("code", "未知"),
                "error_msg": langbridge_response.get("error_msg", "未知错误")
            }), 500

        # 提取AI生成的内容
        ai_content = ""
        try:
            data = langbridge_response.get("data", {})
            choices = data.get("choices", [])
            if choices and len(choices) > 0:
                ai_content = choices[0].get("message", {}).get("content", "")
        except Exception as e:
            logger.error(f"提取AI内容失败: {str(e)}")
            ai_content = json.dumps(langbridge_response)

        logger.info(f"AI生成的内容: {ai_content[:2000]}...")

        # 尝试解析AI返回的JSON
        result = {"ideas": []}

        # 尝试从内容中提取JSON
        json_data = extract_json_from_text(ai_content)

        if json_data and "ideas" in json_data:
            # 如果成功提取到ideas，直接使用
            result = json_data
        else:
            # 如果无法解析，返回原始内容
            result = {
                "error": "无法解析AI响应",
                "raw_content": ai_content
            }

        return jsonify(result)

    except Exception as e:
        logger.exception("处理请求时出错")
        return jsonify({
            "error": "服务器内部错误",
            "message": str(e)
        }), 500


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({"status": "ok", "service": "test-idea-generator"})


@app.route('/test', methods=['GET'])
def test_endpoint():
    """测试端点，返回固定响应"""
    return jsonify({
        "ideas": [
            {
                "id": "test-idea-1",
                "description": "测试想法1 - 验证用户名输入字段的最大长度限制",
                "type": "边界测试",
                "priority": "高",
                "elementXpath": "//input[@id='username']"
            },
            {
                "id": "test-idea-2",
                "description": "测试想法2 - 检查提交按钮在表单未完成时的状态",
                "type": "状态测试",
                "priority": "中",
                "elementXpath": "//button[@id='submit']"
            }
        ]
    })


if __name__ == '__main__':
    # 获取端口号，优先使用环境变量
    port = int(os.environ.get('PORT', 5001))  # 使用5001端口避免冲突

    # 启动服务
    logger.info(f"启动测试想法生成服务，端口: {port}")
    app.run(host='0.0.0.0', port=port, debug=False)
