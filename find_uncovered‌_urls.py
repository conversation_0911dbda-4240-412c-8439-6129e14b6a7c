from pathlib import Path


# 获取PC UI自动化运行过的url
# 1. 动态获取当前目录作为根目录（或替换为你的路径）
root_dir = Path.cwd()
print(f"正在扫描目录：{root_dir}")
output_file = "output.log"

# 2. 查找所有符合条件的文件
log_files = root_dir.rglob("*_urls.log")

# 3. 读取内容并写入到输出文件
with open(output_file, "w", encoding="utf-8") as f_out:
    for file_path in log_files:
        try:
            with open(file_path, "r", encoding="utf-8") as f_in:
                content = f_in.read().strip()  # 读取内容并去掉首尾空行
                # 写入文件名作为分隔标识
                # f_out.write(f"\n\n===== 文件内容来自：{file_path} =====\n")
                f_out.write(content + "\n")
        except UnicodeDecodeError:
            print(f"警告：文件 {file_path} 编码不兼容，已跳过")
        except Exception as e:
            print(f"读取 {file_path} 失败：{str(e)}")

print(f"内容已合并保存到：{output_file}")

# 将雷达url进行过滤
with open('pattern_log.log', 'r') as f_in, open('cleaned_regex.log', 'w') as f_out:
    for line in f_in:
        # 新增条件：如果行包含 login 则跳过
        if 'login' in line:
            continue

        # 原有处理逻辑
        processed = line.strip()
        processed = processed.split('.com')[-1]  # 删 com 前内容
        processed = processed.split('?')[0]  # 删 ? 后内容
        processed = processed.split(' ')[0]  # 删 and,
        f_out.write(processed + '\n')

# 将过滤后的雷达url在pc ui自动化运行过的url中查找，若不包含，则表示改雷达url没有被ui自动化覆盖
# 读取B文件的所有行，去除换行符
with open('output.log', 'r') as f_b:
    b_lines = [line.rstrip('\n') for line in f_b]

# 打开A文件和输出文件
with open('cleaned_regex.log', 'r') as f_a, open('result.log', 'w') as f_out:
    for a_line in f_a:
        a_clean = a_line.rstrip('\n')  # 去除换行符
        # 检查是否在B的任何一行中出现
        found = any(a_clean in b_line for b_line in b_lines)
        if not found:
            f_out.write(a_line)  # 保留原始行内容（包含换行符）


# 得到结果后去pattern_log.log找原有的地址，记录下来
with open('pattern_log.log', 'r') as f_b:
    b_lines = f_b.readlines()

with open('result.log', 'r') as f_a, open('result2.log', 'w') as f_out:
    for a_line in f_a:
        a_clean = a_line.rstrip('\n')  # 去除换行符
        found = False

        # 在B中查找第一个匹配行
        for b_line in b_lines:
            if a_clean in b_line.rstrip('\n'):  # 比较时忽略B行尾换行符
                f_out.write(b_line)  # 写入原始B行内容（包含换行符）
                found = True
                break

# 去重
    """
    去重日志文件，确保输出文件的每一行唯一

    :param input_path: 输入文件路径
    :param output_path: 输出文件路径
    :param keep_empty_lines: 是否保留空行 (默认False)
    """
    seen_lines = set()

    with open('result2.log', 'r', encoding='utf-8') as f_in, \
            open('result3.log', 'w', encoding='utf-8') as f_out:

        for line in f_in:
            # 标准化处理：统一换行符为\n
            normalized = line.rstrip('\n\r') + '\n'
            content = normalized.rstrip('\n')

            # 去重逻辑
            if content not in seen_lines:
                seen_lines.add(content)
                f_out.write(normalized)
