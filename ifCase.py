import os
import ast
import pandas as pd
from collections import defaultdict

class MethodInfo(ast.NodeVisitor):
    def __init__(self):
        self.has_if = False
        self.has_else = False

    def visit_If(self, node):
        self.has_if = True
        if node.orelse:
            self.has_else = True
        self.generic_visit(node)

def find_methods_without_else(directory):
    methods_without_else = []

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                print(f"Processing file: {file_path}")
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                        node = ast.parse(f.read(), filename=file_path)
                        for n in node.body:
                            if isinstance(n, ast.FunctionDef):
                                method_info = MethodInfo()
                                method_info.visit(n)
                                if method_info.has_if and not method_info.has_else:
                                    methods_without_else.append({
                                        'Method': n.name,
                                        'File': file_path
                                    })
                except (SyntaxError, UnicodeDecodeError) as e:
                    print(f"Error in {file_path}: {e}")

    return methods_without_else

def export_to_excel(methods, filename):
    df = pd.DataFrame(methods)
    df.to_excel(filename, index=False)
    print(f"Exported methods without else to {filename}")

# 使用当前目录
current_directory = os.getcwd()
methods_without_else = find_methods_without_else(current_directory)
output_file = 'methods_without_else.xlsx'
export_to_excel(methods_without_else, output_file)