
from selenium.webdriver.remote.webelement import WebElement
from seleniumbase import BaseCase
from selenium.webdriver.common.action_chains import ActionChains


# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By

class TestMyCooperatePromoter:

    def setup_method(self, method):
        # 初始化 WebDriver（以 Chrome 为例）
        self.driver = webdriver.Chrome(executable_path='/path/to/chromedriver')
        self.driver.get("https://www.example.com")
        self.login("DISTRIBUTION_PROMTER", "wb_caijinwei")

    def teardown_method(self, method):
        # 关闭浏览器
        self.driver.quit()

    def login(self, username, password):
        # 实现登录逻辑
        pass

    def click(self, xpath):
        element = self.driver.find_element(By.XPATH, xpath)
        element.click()

    def type(self, xpath, text):
        element = self.driver.find_element(By.XPATH, xpath)


class BaseCase(BaseCase):

    def login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            # 选择我是员工
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[1]/div[2]/span[2]')
            # 选择密码登陆
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.type("input[placeholder='请输入密码']", account_data['password'])
            # 登陆
            self.click(
                '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button')
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'我是员工')])[1]")
            self.sleep(1)
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.type("(//input[@placeholder='请输入手机号'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentB svelte-am7f5d'])[1]")
            self.sleep(2)
            self.click_if_visible(account_data['default_log_select_page'])  # 点击小店试试的小店
            self.sleep(2)

    # 分销达人登录
    def talent_login(self, domain, account):
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        self.click('//div[@class="tab"]')
        self.type("//input[@placeholder='请输入手机号']", account_data['account'])
        self.type("//input[@placeholder='请输入密码']", account_data['password'])
        self.click("//button[@type='button']")

    def map_user_id(self, user_id):
        self.click("img[class='head']")
        self.click("div.menu-blue")
        self.type("input[id='rc_select_13']", user_id)
        self.sleep(2)
        self.send_keys("input[id='rc_select_13']", '\ue007')
        self.click("div.ant-modal-footer button.ant-btn.ant-btn-primary")

    def is_element_clickable(self, element: WebElement, **kwargs) -> bool:
        """
        判断元素是否可点击
        :param element: WebElement对象
        :return: 元素是否可点击，可点击返回True，否则返回False
        """
        try:
            if element.is_enabled() and element.is_displayed():
                return True
            else:
                return False
        except Exception:
            return False

    def scroll_down(self,times):
        """
        模拟向下滑动页面
        :param times: 向下滑动的次数
        """
        for i in range(times):
            ActionChains(self.driver).send_keys(Keys.PAGE_DOWN).perform()




    def press_enter(self, element):
        """
        模拟按下回车键
        :element: xpath方法的位置定位
        """
        self.input_element = self.driver.find_element(By.XPATH, element)  # 初始化 input_element
        self.input_element.send_keys(Keys.RETURN)  # 模拟按下回车键





