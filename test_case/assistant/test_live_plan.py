"""
# Time       ：2022/7/8 7:32 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
import pytest
from ddt import ddt

from .base import BaseCase
from constant.account import get_account_info
from constant.domain import get_domain

# 获取账号信息

# 用户名 account_data['account']
# 密码 account_data['password']
@ddt
class TestLivePlan(BaseCase):
    @pytest.mark.skip
    def test_live_plan_overview(self):
        self.login("KWAIXIAODIAN","reservation_account")
        if (self.is_element_visible('div.guide-btns--t5V9R>button:nth-child(1)')):
            self.click('div.guide-btns--t5V9R>button:nth-child(1)')
        tabs_text = self.get_text("div.headerWrap--XDNMe>ul")
        tab_list = ["直播日历","直播计划","跟播助手","我的直播"]
        for tab in tab_list:
            self.assert_in(tab,tabs_text)
        self.click('div.headerWrap--XDNMe>ul>li:nth-child(2)>span')
        self.assert_text("我的直播计划","div.title--zMM98.title--njAbf")
        for i in range(1,5):
            self.click('div.ant-card-body>div:nth-child(2)>label:nth-child(' + str(i) + ')>span:nth-child(2)')
        for i in range(1,8):
            self.click('div.ant-card-body>div:nth-child(3)>label:nth-child(' + str(i) + ')>span:nth-child(2)')

    @pytest.mark.skip
    def test_create_live_plan_overview(self):
        self.login("KWAIXIAODIAN", "reservation_account")
        if (self.is_element_visible('div.guide-btns--t5V9R>button:nth-child(1)')):
            self.click('div.guide-btns--t5V9R>button:nth-child(1)')
        self.click('div.headerWrap--XDNMe>ul>li:nth-child(2)>span')
        self.click('div.rightTop--SRPuL>button')
        plan_text = self.get_text('div.ant-drawer-wrapper-body')
        plan_text_list = ["创建直播计划","开播标题","盘播设置项","开播时间","直播时长","直播类型","盘货设置项","销售额目标",
                          "销售额目标","计划选品数","其他设置项","商业化消耗","目标金额","重要时刻"]
        for plan_text_1 in plan_text_list:
            self.assert_in(plan_text_1,plan_text)
        self.click('div.ant-drawer-footer>div>button:nth-child(1)')
        self.sleep(3)

    @pytest.mark.skip
    def test_live_reservation(self):
        self.login("KWAIXIAODIAN","reservation_account")
        if(self.is_element_visible('div.guide-btns--t5V9R>button:nth-child(1)')):
            self.click('div.guide-btns--t5V9R>button:nth-child(1)')
        self.click('div.headerWrap--XDNMe>ul>li:nth-child(2)>span')
        self.click('div.ant-table-body>table>tbody>tr:nth-child(2)>td:nth-child(8)>button:nth-child(2)')
        self.click('div.ant-drawer-footer>div>button:nth-child(1)')
        if(self.is_element_visible('div.ant-table-body>table>tbody>tr:nth-child(2)>td:nth-child(8)>button:nth-child(3)')):
            self.click('div.ant-table-body>table>tbody>tr:nth-child(2)>td:nth-child(8)>button:nth-child(3)')
            self.click('div.ant-modal-confirm-btns>button:nth-child(1)')
        self.sleep(2)
        self.click('div.ant-table-body>table>tbody>tr:nth-child(2)>td:nth-child(8)>button:nth-child(1)')
        basic_text = self.get_text('div.basic-info--BcDDP')
        basic_text_list = ["基础信息","盘播设置项","盘货设置项","其他设置项","编辑基础信息"]
        for basic_tab in basic_text_list:
            self.assert_in(basic_tab,basic_text)
        self.assert_text("设置直播计划-让直播更高效",'div.main--VsA1J')
        self.assert_text("完成全部设置能有效获取精准流量，提升开播转化效率，助力大卖！",'div.sub-title--sE4U8')
        self.click('div.nav-list--Vcx9K>div:nth-child(1)>button')
        self.click('div.tabs--n6TGK>div:nth-child(2)>div')
        self.click('div.tabs--n6TGK>div:nth-child(3)>div')
        self.click('div.tabs--n6TGK>div:nth-child(4)>div')
        self.sleep(3)
    # def test_live_assistant(self):
    #     self.login("RESERVATION_DOMAIN", "reservation_account")
    #     if (self.is_element_visible('div.guide-btns--t5V9R>button:nth-child(1)')):
    #         self.click('div.guide-btns--t5V9R>button:nth-child(1)')
    #     self.click('div.headerWrap--XDNMe>ul>li:nth-child(3)>span')
    #     self.assert_text("未开播","div.info--TZQv_>span")
    #     if (self.is_element_visible('div.kpro-modal-footer>div>div:nth-child(2)>button:nth-child(1)')):
    #         self.click('div.kpro-modal-footer>div>div:nth-child(2)>button:nth-child(1)')
    #     self.click('div.btn-area--kupWE>button')
    #     # self.click('div.ant-dropdown.ant-dropdown-show-arrow.ant-dropdown-placement-bottomRight>ul>li>span>a')
    #     # self.click('div.ant-drawer.ant-drawer-right.ant-drawer-open.select-plan-dialog--_qwXN>div:nth-child(2)>div>div>div:nth-child(3)>button')
    #     self.click('div.ant-tabs-tab.ant-tabs-tab-active>div')
    #     self.click('div.ant-tabs-tab>div')
    #     comment_text = self.get_text('div.container--Kvg72')
    #     comment_text_list = ["已回复","已转客服","暂无评论"]
    #     for comment in comment_text_list:
    #         self.assert_in(comment,comment_text)
    #
    #     self.click('div.position--GBq5N>span:nth-child(1)')
    #     self.click('div.position--GBq5N>span:nth-child(3)')
    #     prompter_text = self.get_text('div.container--LygCr')
    #     prompter_text_list = ["提词器","打开主播看版","暂无提词的商品","留 言"]
    #     for prompter_text_element in prompter_text_list:
    #         self.assert_in(prompter_text_element,prompter_text)
    #     self.click('div.text-right--DSu5c')
    #     anchor_board = self.get_text('div.container--l9a5x')
    #     anchor_board_text_list = ["场助提醒 :","暂无提醒内容","当前暂无提词商品","商品售价","商品库存","成交件数","商品讲解时长","暂无数据"]
    #     for anchor_board_text in anchor_board_text_list:
    #         self.assert_in(anchor_board_text,anchor_board)
    #     self.switch_to_window(0)
    #     self.assert_text("实时数据","div.left--rhi4H>div")
    #     commercial_text = self.get_text('div.index-module__followerAssistantContainer___1ZMMl')
    #     commercial_text_list = ["磁力金牛流量推广","提升直播间卖货","提升直播间人气","提升直播间涨粉"]
    #     for commercial in commercial_text_list:
    #         self.assert_in(commercial,commercial_text)
    #     marketing_text = self.get_text('div.marketing--QwjvN')
    #     marketing_text_list = ["营销工具","数据详情","粉丝专属券","促涨粉"]
    #     for marketing in marketing_text_list:
    #         self.assert_in(marketing,marketing_text)
    #     shopping_car_text = self.get_text('div.container--lGkl_')
    #     shopping_car_text_list = ["待上车商品","添加待上车商品","批量操作","导入直播计划","可在直播前提前添加商品，或导入直播计划","一键上架直播商品"]
    #     for shopping in shopping_car_text_list:
    #         self.assert_in(shopping,shopping_car_text)
    #     self.click('div.container--1PJw7>div:nth-child(2)>div:nth-child(2)>div>div>div:nth-child(1)>div:nth-child(1)>div>div:nth-child(1)>button')
    #     self.click('div.footer-area--H3TZq>button')
    #     self.click('div.c2-empty-container--1qnFM>button')
    #     self.click('div.ant-drawer-footer>button:nth-child(1)')
    #     self.assert_text("管理计划",'div.btn-area--1EyF->button')
    #     # self.click('div.btn-area--1EyF->button')
    #     # self.click('div.ant-dropdown.ant-dropdown-show-arrow.ant-dropdown-placement-bottomRight>ul>li>span>a')
    #     # self.click('div.ant-drawer-footer>button:nth-child(1)')




