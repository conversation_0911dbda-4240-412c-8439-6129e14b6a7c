"""
# Author     ：author did<PERSON><PERSON>
# Description：
"""

import os
import sys


import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env

from .load_investment_activity import LoadInvestmentActivity




@ddt
class TestInvestmentActivity(LoadInvestmentActivity):













    # 系列活动列表链接
    @pytest.mark.p0
    def test_serial_activity_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_business_list()

    # 子活动列表链接
    @pytest.mark.p0
    def test_single_activity_List(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_business_singleList()

    # 已报活动链接

    @pytest.mark.p0
    def test_already_jijo_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_record_list()

    # 活动详情
    @pytest.mark.skip
    @pytest.mark.p0
    def test_already_detail(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_business_detail()

    # 商品托管链接

    @pytest.mark.p0
    def test_trusteeship_manage(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_productHost_manage()

    # 爆款竞价页面-爆款商品列表

    # @pytest.mark.skip
    def test_investment_comoeteCommodity_item_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_comoeteCommodity_item_list()

    # 爆款竞价页面-已报管理

    # @pytest.mark.skip
    def test_investment_comoeteCommodity_record_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_comoeteCommodity_record_list()

    @pytest.mark.skip("暂不执行")
    #提报列表tab
    #@pytest.mark.p0
    def test_sign_drawer_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_comoeteCommodity_drawer_list()

    #官方竞价

    @pytest.mark.skip
    @pytest.mark.p0
    def test_official_bidding_list(self):
            # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
            # 跳过新手引导
        self.skip_guider()
        self.check_investment_official_bidding()




    #官方竞价已报列表
    @pytest.mark.skip
    @pytest.mark.p0
    def test_bidding_registered_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()
        self.check_investment_bidding_registered()


    #添加托管商品页面展示
    @pytest.mark.p0
    def test_add_commodity(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_add_commodity()

    #查看托管详情
    @pytest.mark.p0
    def test_look_look_particulars(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_look_particulars()

    #商城必报活动（默认tab）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_mall_activities(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities()



    #商城必报活动（全部tab）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_mall_activities_all_tab(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities_all_tab()



    #商城必报活动页面（第三个tab）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_mall_activities_three_tab(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities_three_tab()

    #商城必报活动页面（第四个tab）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_mall_activities_four_tab(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities_four_tab()

    #商城必报活动页面（第五个tab）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_mall_activities_five_tab(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities_five_tab()



    #商城必报活动页面（第六个tab）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_mall_activities_six_tab(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities_six_tab()


    #商城必报活动页面（第七个tab）
    @pytest.mark.p0
    def test_mall_activities_seven_tab(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_mall_activities_seven_tab()



    #商城必报活动（提报记录页面）
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_commodity_record(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_commodity_record()




    # 爆款竞价页面-已报管理--撤回重新提报
    @pytest.mark.p0
    #@pytest.mark.skip
    def test_investment_already_apply_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_already_apply_list()


    #招商活动-已报管理--撤回重新提报
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_complex_activity(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_complex_activity()


#营销托管总览签署协议页面
    @pytest.mark.skip
    @pytest.mark.p0
    def test_hosting_overview(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_hosting_overview()



    #营销托管首页(整体商城数据)模块
    @pytest.mark.p0
    def test_hosting_overview_home_page(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_investment_hosting_overview_home_page()