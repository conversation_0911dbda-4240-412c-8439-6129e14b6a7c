from time import sleep

from seleniumbase import BaseCase
from utils.kwaixiaodianUtils import KwaiXiaoDianToolTest

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


# 获取账号信息
account_data = get_account_info("short_video")
host = get_domain("SHORT_VIDEO_ESC_DOMAIN")


class BaseTestCase(KwaiXiaoDianToolTest):

    def login(self, domain, account):

        # account_data = get_account_info(account)
        host = get_domain(domain)
        self.kwaixiaodian_login(account)
        self.open(host)
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        # sleep(0.1)
        # self.assert_text("扫码登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')  # div标签中的第一个元素
        # self.assert_text("手机号登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        # # sleep(0.1)
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        # # self.click("div.choseTab--1_6F7 > div:nth-child(1)")
        # self.type("input[placeholder='请输入手机号']", account_data['account'])
        # self.type("input[placeholder='请输入密码']", account_data['password'])
        # self.click("button[type='button']")

        # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        # self.click('//*[@id="username"]')
        # self.type('//*[@id="username"]', account_data['account'])
        # self.click('//*[@id="password"]')
        # self.type('//*[@id="password"]', account_data['password'])
        # self.click(
        #     '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')

        sleep(2)

    # def login_page(self, domain, account):
    #     host = get_domain(domain)
    #     self.open(host)
    #     tabs_text = self.get_text('#root > div > div.contentWrap--1G9Bm > div > div > div')  # 获取对应的text
    #     tab_list = ["我是店主", "我是员工", "0元开店"]
    #     for item in tab_list:
    #         self.assert_in(item, tabs_text)
    #     self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
    #     self.assert_text("扫码登录", "div.choseTab--12n2b > div:nth-child(1)")  # div标签中的第一个元素
    #     self.assert_text("手机号登录", "div.choseTab--12n2b > div:nth-child(2)")
    #
    #
    # def login_success(self):
    #     self.login_page(host,account_data)
    #     self.type("input[placeholder='请输入手机号']", account_data['account'])
    #     self.type("input[placeholder='请输入密码']", account_data['password'])
    #     self.click("button[type='button']")
    #     self.assert_title("快手小店")
