import datetime
import random
import time

from selenium.common import NoSuchElementException
from selenium.webdriver import ActionChains, Keys
from selenium.webdriver.remote.webelement import WebElement
from seleniumbase import BaseCase
import pytest
# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.kwaixiaodianUtils import KwaiXiaoDianToolTest
from seleniumwire import webdriver

BaseCase.main(__name__, __file__)

url = "https://eshop-s.prt.kwaixiaodian.com/zone/shop/decoration/list?tab=home"
decoration_url = "https://eshop-s.prt.kwaixiaodian.com/zone/shop/decoration/editV2?pageInstanceId=***********"
prt_domain = "https://eshop-login.prt.kwaixiaodian.com/?biz=zone&redirect_url=https%3A%2F%2Feshop-s.prt.kwaixiaodian.com%2Fzone%2Fhome"

url_online = "https://s.kwaixiaodian.com/zone/shop/decoration/list?tab=home"
decoration_url_online = "https://s.kwaixiaodian.com/zone/shop/decoration/editV2?pageInstanceId=***********"
online_domain = "https://s.kwaixiaodian.com/?biz=zone&redirect_url=https%3A%2F%2Feshop-s.prt.kwaixiaodian.com%2Fzone%2Fhome"


class TestDecorationPage(BaseCase):

    def into_shop_page(self, domain, account):
        # self.merchant_login(domain, account)

        # self.driver.quit()
        # self.driver = webdriver.Chrome()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        KwaiXiaoDianToolTest.kwaixiaodian_login(self, account)
        self.var1 = 'prt'
        self.open(url)
        try:
            for i in range(9):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(1)
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")

    def into_shop_page_prt(self, domain, account):
        account_data = get_account_info(account)
        host = domain
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        self.open(url)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()

    def merchant_login(self, domain, account):
        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        else:
            env = 'online'
        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        time.sleep(1)
        self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        time.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
        time.sleep(2)

    def get_domain_by_env(self, domain, env):
        return globals().get(domain)[env]

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    def test_decoration_bulletin_board(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        # self.assert_text("立即查看", "(//a[contains(text(),'立即查看')])[1]")

    @pytest.mark.p1
    def test_decoration_bulletin_home_page_control(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        self.assert_text("管理店铺入口", "//*[@id=\"root\"]/section/main/div/div[2]/div/div[1]/div[3]/div/div[2]/div")
    @pytest.mark.p1
    def test_decoration_bulletin_home_page_book(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("装修文档", "(//div[contains(text(),'装修文档')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'装修文档')])[1]")
        time.sleep(3)
        self.assert_url(
            "https://docs.qingque.cn/d/home/<USER>")
    # # 新增模版达人（种草带货型店铺）
    # @pytest.mark.p1
    # def test_decoration_add_home_page1(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     self.assert_text("新增模板", "//span[contains(text(),'新增模板')]")
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.click("//span[contains(text(),'新增模板')]")
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(1)
    #     # self.assert_text("适合种草带货型店铺","(//img)[41]")
    #     # self.click("(//img)[41]")
    #     self.assert_text("确 定", "(//span[contains(text(),'确 定')])[1]")
    #     self.click("(//span[contains(text(),'确 定')])[1]")
    #     time.sleep(2)
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.assert_element("(//div[contains(@class,'decoration-card-title')][contains(text(),'常购好物')])[1]")

    # # 删除首页达人模版
    # @pytest.mark.p1
    # def test_decoration_delete_home_page2(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #         self.assert_text("删除", "(//span[contains(text(),'删除')])[1]")
    #         self.click("(//span[contains(text(),'删除')])[1]")
    #         handles = self.driver.window_handles
    #         self.driver.switch_to.window(handles[-1])
    #         time.sleep(1)
    #         self.assert_text("确 认", "(//button[@class='ant-btn ant-btn-primary'])[1]")
    #         self.click("(//button[@class='ant-btn ant-btn-primary'])[1]")

    # 新增模版商家（自有货源型店铺）
    @pytest.mark.p1
    def test_decoration_add_home_page2(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        self.assert_text("新增模板", "//span[contains(text(),'新增模板')]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("//span[contains(text(),'新增模板')]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(1)
        # self.assert_text("适合自有货源型店铺","(//div[@class='lFz3ivYwnqCypZ5s0QTC'][contains(text(),'适合自有货源型店铺')])[1]")
        self.assert_text("确 定", "(//span[contains(text(),'确 定')])[1]")
        self.click("(//span[contains(text(),'确 定')])[1]")
        time.sleep(2)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_element("(//div[@class='decoration-card-title '])[1]")

    # 删除首页商家模版
    @pytest.mark.p1
    def test_decoration_delete_home_page1(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
            self.assert_text("删除", "(//span[contains(text(),'删除')])[1]")
            self.click("(//span[contains(text(),'删除')])[1]")
            handles = self.driver.window_handles
            self.driver.switch_to.window(handles[-1])
            time.sleep(1)
            self.assert_text("确 认", "(//button[@class='ant-btn ant-btn-primary'])[1]")
            self.click("(//button[@class='ant-btn ant-btn-primary'])[1]")

    # 装修视频课程
    @pytest.mark.p1
    def test_decoration_Video_Course(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("装修视频课程", "(//div[contains(text(),'装修视频课程')])[1]")
        self.click("(//div[contains(text(),'装修视频课程')])[1]")
        time.sleep(2)
        self.assert_url(
            "https://university.kwaixiaodian.com/kwaishop/courseDetail?courseId=50ab62fca33a4f90a068cc036181d79f&layoutType=4")

    # 已下架

    # @pytest.mark.p1
    # def test_decoration_plc_lesson(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     self.click("//div[@class='ZIXmkrwGiZ4V16DMcZqG']//div[1]//img[1]")
    #     time.sleep(8)
    #     self.assert_text("如何发布优质挂店短视频", "//div[contains(text(),'如何发布优质挂店短视频')]")
    #     # self.assert_equal("university.kwaixiaodian.com/kwaishop/lesson/" in self.get_current_url(), True)
    #
    # @pytest.mark.p1
    # def test_decoration_operations_lesson(self):
    #     self.into_shop_page_prt(prt_domain, 'pengshengpu')
    #     self.click("//div[@class='Hz7rasxT_7bUenuXB7vv']//div[2]//img[1]")
    #     time.sleep(8)
    #     self.assert_text("店铺整体运营方法论", "//div[contains(text(),'店铺整体运营方法论')]")
    #     # self.assert_equal("university.kwaixiaodian.com/kwaishop/lesson/" in self.get_current_url(), True)

    # @pytest.mark.p1
    # def test_decoration_lesson(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     self.click("//div[@class='wLq3m0BxSnvjGJM6iOCQ']//div[3]//img[1]")
    #     time.sleep(8)
    #     self.assert_text("如何装修店铺？", "//div[contains(text(),'如何装修店铺？')]")
    #     # self.assert_equal("university.kwaixiaodian.com/kwaishop/lesson/" in self.get_current_url(), True)
    #
    # @pytest.mark.p1
    # def test_decoration_lesson_more(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     try:
    #         for i in range(8):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.click("(//div[@class='wEYCaEeEh70A4JqkiYNW'][contains(text(),'查看更多')])[1]")
    #     self.assert_equal("docs.qingque.cn/d/home/<USER>" in self.get_current_url(), True)
    # 首页名称编辑笔(//div[@id='rcDialogTitle0'])[1]
    @pytest.mark.p1
    def test_decorationpc_edit_home_page(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_element("(//*[name()='svg'][@clip-rule='evenodd'])[10]")
        self.click("(//*[name()='svg'][@clip-rule='evenodd'])[10]")
        time.sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(3)
        self.assert_element("(//div[@id='rcDialogTitle0'])[1]")


    # 清单页点击"查看操作指南"
    @pytest.mark.p1
    def test_decoration_bulletin_page_instance(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        self.click('//*[@id="rc-tabs-0-tab-shared"]')
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_text("查看操作指南", "//*[@id='manage-template']/div[1]/div/span/div/div[1]/button/span")
        time.sleep(2)
        self.click("//*[@id='manage-template']/div[1]/div/span/div/div[1]/button/span")
        self.assert_equal("https://docs.qingque.cn/d/home/" in self.get_current_url(),
                          True)

    # 新增清单页
    @pytest.mark.p1
    def test_decoration_add_page_instance(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        self.click('//*[@id="rc-tabs-0-tab-shared"]')
        time.sleep(2)
        self.assert_text("新增清单", "//*[@id='manage-template']/div[1]/div/span/div/div[2]/div/div/button/span[2]")
        self.click('//*[@id="manage-template"]/div[1]/div/span/div/div[2]/div/div/button/span[2]')
        self.assert_text("清单标题", '//*[@id="template"]/div/div[1]/div[1]/div[1]/label/span')

    # 清单页面关联商品
    @pytest.mark.p1
    def test_decoration_add2_page_instance(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        self.assert_text("自定义商品清单页", '//*[@id="rc-tabs-0-tab-shared"]')
        self.click('//*[@id="rc-tabs-0-tab-shared"]')
        time.sleep(2)
        self.assert_text("新增清单", "//*[@id='manage-template']/div[1]/div/span/div/div[2]/div/div/button/span[2]")
        self.click('//*[@id="manage-template"]/div[1]/div/span/div/div[2]/div/div/button/span[2]')
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("添加商品", "(//span[contains(text(),'添加商品')])[1]")
        self.click("(//span[contains(text(),'添加商品')])[1]")
        time.sleep(1)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text('商品信息', "(//th[contains(text(),'商品信息')])[1]")
        self.click("(//th[@class='ant-table-cell ant-table-selection-column'])[1]")
        time.sleep(1)
        self.click("(//th[@class='ant-table-cell ant-table-selection-column'])[1]")

    # 清单页失效文案校验
    @pytest.mark.p1
    def test_decoration_add3_page_instance(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click('//*[@id="rc-tabs-0-tab-shared"]')
        time.sleep(2)
        self.assert_element("(//div[@role='alert'])[5]")

    # 编辑清单页
    @pytest.mark.p1
    def test_decoration_editor_page_instance(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        # self.assert_text("知道了", "(//button[contains(text(),'知道了')])[1]")
        # self.click("(//button[contains(text(),'知道了')])[1]")
        self.click('//*[@id="rc-tabs-0-tab-shared"]')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.assert_text("编辑", "(//a[@target='_blank'][contains(text(),'编辑')])[1]")
        self.click("(//a[@target='_blank'][contains(text(),'编辑')])[1]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("主题清单编辑页", "(//div[@id='rcDialogTitle0'])[1]")

    # 删除清单页
    @pytest.mark.p1
    def test_decoration_delete_page_instance(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click('//*[@id="rc-tabs-0-tab-shared"]')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.assert_text("删除", "(//span[contains(text(),'删除')])[1]")
        self.click("(//span[contains(text(),'删除')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("确定删除该模板？", "(//span[@class='ant-modal-confirm-title'])[1]")

    # 点击店铺设置按钮，触发“店铺设置”弹窗（2025.2.26下线）
    # @pytest.mark.p1
    # def test_decorationpc_pc_Store_Settings(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     self.assert_text('店铺设置', "(//div[@class='Q2k2rYIV0lHwBqKWFr0T'][contains(text(),'店铺设置')])[1]")
    #     time.sleep(2)
    #     self.click("(//div[@class='Q2k2rYIV0lHwBqKWFr0T'][contains(text(),'店铺设置')])[1]")
    #     time.sleep(2)
    #     self.assert_text('平台精选', "(//div[@class='YcLWPlPR4r0OdSzIk9MS'])[1]")
    #     time.sleep(2)
    #     # 点击hover
    #     self.click("//span[@aria-label='system-questionmark-circle-line']//*[name()='svg']")
    #     time.sleep(2)

    # 店铺设置视频种草页弹窗
    @pytest.mark.p1
    def test_decorationpc_pc_Store_Settings(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("店铺设置", "(//div[contains(text(),'店铺设置')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'店铺设置')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_element("(//div[@class='title___FdmNS'])[1]")
    #视频种草页hover
    @pytest.mark.skip
    @pytest.mark.p1
    def test_decorationpc_pc_Settings_hover(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("店铺设置", "(//div[contains(text(),'店铺设置')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'店铺设置')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_element("(//div)[198]")
        self.click("(//div)[198]")
        time.sleep(2)

    # 店铺设置视频种草说明手册
    @pytest.mark.p1
    def test_decorationpc_pc_recommend_document(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("店铺设置", "(//div[contains(text(),'店铺设置')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'店铺设置')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_text("说明手册", "(//a[contains(text(),'说明手册')])[1]")
        self.click("(//a[contains(text(),'说明手册')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_url(
            "https://docs.qingque.cn/d/home/<USER>")

    # 店铺设置视频种草开个按钮
    @pytest.mark.p1
    def test_decorationpc_pc_recommend_switch(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("店铺设置", "(//div[contains(text(),'店铺设置')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'店铺设置')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_element("(//button[@role='switch'])[1]")
        self.click("(//button[@role='switch'])[1]")

    #店铺平台智能装修开关-关闭
    @pytest.mark.p1
    def test_decorationpc_pc_intelligent_switch_close(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_element("(//div[@class='ant-switch-handle'])[1]")
        self.click("(//div[@class='ant-switch-handle'])[1]")
        time.sleep(1)

    # 店铺平台智能装修开关-打开
    @pytest.mark.p1
    def test_decorationpc_pc_intelligent_switch_open(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_element("(//button[@role='switch'])[1]")
        self.click("(//button[@role='switch'])[1]")
        time.sleep(1)

    #店铺平台智能装修功能文档
    @pytest.mark.p1
    def test_decorationpc_pc_intelligent_switch_word(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_element("(//a[contains(text(),'智能装修功能说明')])[1]")
        self.click("(//a[contains(text(),'智能装修功能说明')])[1]")
        time.sleep(1)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_url(
            "https://docs.qingque.cn/d/home/<USER>")

    # 店铺装修首页页面预览效果展示
    @pytest.mark.p1
    def test_decorationpc_pc_homepage(self):
        self.into_shop_page_prt(prt_domain, 'customer_merchant')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("首页", "(//div[@id='rc-tabs-0-tab-home'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-home'])[1]")
        time.sleep(2)
        self.assert_element("(//div)[30]")

    # 分类页管理店铺入口
    def test_decoration_Category_page_entrance(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(2)
        self.assert_text("管理店铺入口", "(//div[contains(text(),'管理店铺入口')])[1]")
        self.click("(//div[contains(text(),'管理店铺入口')])[1]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_text("关 闭", "(//span[contains(text(),'关 闭')])[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'关 闭')])[1]")

    # 分类页店铺设置点击  已下线
    # def test_decoration_Category_page_Set(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     try:
    #         for i in range(8):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     time.sleep(1)
    #     self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     time.sleep(2)
    #     self.assert_text("店铺设置", "(//div[contains(text(),'店铺设置')])[1]")
    #     self.click("(//div[contains(text(),'店铺设置')])[1]")
    #     time.sleep(1)
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(1)
    #     self.assert_text("确 定", "(//span[contains(text(),'确 定')])[1]")
    #     time.sleep(2)
    #     self.click("(//span[contains(text(),'确 定')])[1]")

    # 分类页装修指南
    def test_decoration_Category_Guide1(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(2)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(2)
        self.assert_text("装修文档", "(//div[contains(text(),'装修文档')])[1]")
        self.click("(//div[contains(text(),'装修文档')])[1]")
        time.sleep(3)
        self.assert_url(
            "https://docs.qingque.cn/d/home/<USER>")


        # 分类页模版删除

    # @pytest.mark.p1
    # def test_decoration_delete_Category_page2(self):
    #     self.into_shop_page("WB_DOMAIN", 'didengke')
    #     try:
    #         for i in range(8):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     time.sleep(1)
    #     self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     time.sleep(2)
    #     self.assert_text("删除", "(//span[contains(text(),'删除')])[1]")
    #     self.click("(//span[contains(text(),'删除')])[1]")
    #     time.sleep(2)
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     self.assert_text("确 认", "(//button[@class='ant-btn ant-btn-primary'])[1]")
    #     time.sleep(2)
    #     self.click("(//button[@class='ant-btn ant-btn-primary'])[1]")
    #     time.sleep(5)

    # 分类页
    # 新增分类页模版
    @pytest.mark.p1
    def test_decoration_add_Category_page(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(2)
        self.assert_text("新增分类页模板", "(//button[@class='ant-btn ant-btn-primary ant-btn-lg'])[1]")
        time.sleep(1)
        self.click("(//button[@class='ant-btn ant-btn-primary ant-btn-lg'])[1]")
        time.sleep(2)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(3)
        self.assert_text('新建商品分类', "(//div[@class='right-header__title'])[1]")

    # 分类页删除
    @pytest.mark.p1
    def test_decoration_add_Category_page2(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(2)
        self.assert_text("删除", "(//span[contains(text(),'删除')])[1]")
        time.sleep(1)
        self.click("(//span[contains(text(),'删除')])[1]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_text("确 认", "//span[contains(text(),'确 认')]")
        self.click("//span[contains(text(),'确 认')]")

    # #删除分类页
    # @pytest.mark.p1
    # def test_decoration_add_Category_pagedelete(self):
    #     self.into_shop_page("WB_DOMAIN", 'didengke')
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     time.sleep(1)
    #     self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     time.sleep(2)
    #     self.assert_text("删除", "(//span[contains(text(),'删除')])[1]")
    #     time.sleep(1)
    #     self.click("(//span[contains(text(),'删除')])[1]")
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(1)
    #     self.assert_text("确 认", "(//span[contains(text(),'确 认')])[1]")
    #     self.click("(//span[contains(text(),'确 认')])[1]")


    # 编辑分类页
    @pytest.mark.p1
    def test_decorationpc_pc_shop_decoration_profile(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.assert_text("编辑", "(//a[@target='_blank'][contains(text(),'编辑')])[1]")
        time.sleep(2)
        self.click("(//a[@target='_blank'][contains(text(),'编辑')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        # if self.is_element_visible("(//a[contains(text(),'编辑')])[1])"):
        #     self.click("(//a[contains(text(),'编辑')])[1]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(5)
        self.assert_text('新建商品分类', "(//div[@class='right-header__title'])[1]")

    # 分类页名称编辑笔
    @pytest.mark.p1
    def test_decorationpc_pc_shop2_decoration_profile(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.assert_element("(//*[name()='svg'][@clip-rule='evenodd'])[9]")
        time.sleep(2)
        self.click("(//*[name()='svg'][@clip-rule='evenodd'])[9]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(3)
        self.assert_element("(//div[@id='rcDialogTitle0'])[1]")

    # 分类页店铺分销商品tab
    @pytest.mark.p1
    def test_decorationpc_pc_shop_decoration_classify(self):
        self.into_shop_page("WB_DOMAIN", 'zengjingwen')
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
        time.sleep(1)
        self.assert_text("编辑", "(//a[contains(text(),'编辑')])[1]")
        self.click("(//a[contains(text(),'编辑')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(1)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("关联商品", "//div[@class='third-level__right']//button[@id='add-template']")
        self.click("//div[@class='third-level__right']//button[@id='add-template']")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(1)
        self.assert_text("店铺分销商品", "(//div[@id='rc-tabs-0-tab-DISTRIBUTE_ITEM'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-DISTRIBUTE_ITEM'])[1]")
        time.sleep(1)
        self.assert_element("(//label[@class='kwaishop-seller-micro-shop-classify-checkbox-wrapper'])[5]")
        self.click("(//label[@class='kwaishop-seller-micro-shop-classify-checkbox-wrapper'])[5]")

    # # 分类页删除创建类目
    # @pytest.mark.p1
    # def test_decorationpc_pc_delete_decoration_classify(self):
    #     self.into_shop_page("WB_DOMAIN", 'pengshengpu')
    #     self.assert_text('分类页', "(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     self.click("(//div[@id='rc-tabs-0-tab-category'])[1]")
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(1)
    #     self.assert_text("编辑", "(//a[contains(text(),'编辑')])[1]")
    #     self.click("(//a[contains(text(),'编辑')])[1]")
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.assert_text("一级分类", "(//div[@class='text'][contains(text(),'一级分类')])[19]")
    #     self.click("(//div[@class='text'][contains(text(),'一级分类')])[19]")
    #     time.sleep(2)
    #     self.assert_element("(//*[name()='path'])[3]")
    #     self.click("(//*[name()='path'])[3]")
    #     time.sleep(1)
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(1)
    #     self.assert_text("确 认", "(//span[contains(text(),'确 认')])[1]")
    #     self.click("(//span[contains(text(),'确 认')])[1]")

    # 分类页创建类目
    @pytest.mark.p1
    def test_decorationpc_pc_add_decoration_classify(self):
        self.into_shop_page("WB_DOMAIN", 'didengke')
        self.assert_text('分类页', '//*[@id="rc-tabs-0-tab-category"]/div')
        self.click('//*[@id="rc-tabs-0-tab-category"]/div')
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(1)
        self.assert_text("编辑", "(//a[contains(text(),'编辑')])[1]")
        self.click("(//a[contains(text(),'编辑')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("新建一级分类", "(//span[contains(text(),'新建一级分类')])[1]")
        self.click("(//span[contains(text(),'新建一级分类')])[1]")

    # @pytest.mark.p1
    # def test_decorationpc_shop_page_active(self):
    #     self.into_shop_page("WB_DOMAIN", 'zengjingwen')
    #     self.assert_text('大促活动页',"(//div[@id='rc-tabs-0-tab-activity'])[1]")
    #    # time.sleep(1)
    #     self.click("(//div[@id='rc-tabs-0-tab-activity'])[1]")
    #     time.sleep(1)
    #     self.assert_text('编辑',"(//a[contains(text(),'编辑')])[1]")
    #     self.click("(//a[contains(text(),'编辑')])[1]")

    @pytest.mark.p1
    def test_decoration_page_top_preview(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        if self.var1 == 'prt':
            self.open(decoration_url)
        else:
            self.open(decoration_url_online)

        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//span[contains(text(),'查看预览')])[1]")
        self.assert_element("(//canvas)[1]")

    # 首页模版发布
    @pytest.mark.p1
    def test_decoration_page_top_release(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        if self.var1 == 'prt':
            self.open(decoration_url)
        else:
            self.open(decoration_url_online)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//span[contains(text(),'立即发布')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(1)
        self.assert_text("确 认", "(//span[contains(text(),'确 认')])[1]")
        self.click("(//span[contains(text(),'确 认')])[1]")

    @pytest.mark.p1
    def test_decoration_top_draft(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        if self.var1 == 'prt':
            self.open(decoration_url)
        else:
            self.open(decoration_url_online)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("保存草稿", "(//span[contains(text(),'保存草稿')])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_shop_accredit_page(self):
        self.into_shop_page_prt(prt_domain, 'funds_account_01')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/manage/account/accredit")
        self.assert_text("查 询", "(//span[contains(text(),'查 询')])[1]")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.assert_text("形象号", "(//td[contains(text(),'形象号')])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_shop_accredit_page_add(self):
        self.into_shop_page("WB_DOMAIN", 'funds_account_01')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/manage/account/accredit")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("新增授权账号", "(//span[contains(text(),'新增授权账号')])[1]")
        self.click("(//span[contains(text(),'新增授权账号')])[1]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("请在快手APP登录想要授权的快手账号，扫码确认授权，30分钟内有效",
                         "(//div[@class='iZJsK_aJDIFthoChZthb'])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_shop_accredit_page_del_account(self):
        self.into_shop_page("WB_DOMAIN", 'funds_account_01')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/manage/account/accredit")
        self.click("(//span[contains(text(),'删除')])[1]")
        self.assert_text("确认将此账号从列表删除？",
                         "(//div[@class='kwaishop-seller-micro-shop-manage-pc-popover-message-title'])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_shop_decoration_hotspot_upload_pic(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_element("(//div[@class='decoration-preview-preview-mask'])[1]")
        self.click("(//div[@class='decoration-preview-preview-mask'])[1]")
        time.sleep(1)
        # self.hover("(//*[name()='svg'][@clip-rule='evenodd'])[9]")
        # self.assert_element("(//div[@class='material-icon'])[1]")
        self.click("(//div[@class='decoration-upload'])[1]")
        # self.click("/html[1]/body[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[2]/div[5]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/span[1]/div[1]/span[1]/div[1]/span[1]/*[name()='svg'][1]/*[name()='path'][1]")
        self.hover("/html[1]/body[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[2]/div[5]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/span[1]/div[1]/span[1]/div[1]/span[1]/*[name()='svg'][1]/*[name()='path'][1]")
        self.sleep(2)
        self.hover("/html[1]/body[1]/div[8]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/span[1]/*[name()='svg'][1]/*[name()='path'][1]")
        # /html[1]/body[1]/div[10]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/button[1]/span[1]
        # self.click("/html[1]/body[1]/div[3]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[2]/div[5]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/span[1]/div[1]/span[1]/div[1]/span[1]/*[name()='svg'][1]")
        # self.assert_element("/html[1]/body[1]/div[9]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/span[1]/*[name()='svg'][1]/*[name()='path'][1]")
        # self.click("/html[1]/body[1]/div[9]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/span[1]/*[name()='svg'][1]/*[name()='path'][1]")
        # self.assert_element("(//*[name()='svg'][@clip-rule='evenodd'])[9]")
        #self.click("//span[@aria-label='normal-exchange-line']//*[name()='svg']")
        #self.click("//*[@id=\"decoration-form-edit\"]/div[1]/form/div/div[3]/div/div/div/div/div/div[3]/div[2]")
        # try:
        #     for i in range(5):
        #         self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        # except:
        #     print()
        # self.click("(//*[name()='svg'])[9]")
        # time.sleep(2)
        # handles = self.driver.window_handles
        # self.driver.switch_to.window(handles[-1])
        # time.sleep(5)
        # self.assert_text("本地上传", "(//div[@class='material-icon'])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_shop_decoration_hotspot_upload_pic_material(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        time.sleep(2)
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//div[@class='decoration-preview-preview-mask'])[1]")
        self.click("//*[@id=\"decoration-form-edit\"]/div[1]/form/div/div[3]/div/div/div/div/div/div[3]/div[2]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//*[name()='svg'])[9]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.click("(//p[contains(text(),'素材中心导入')])[1]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(3)
        self.assert_element("(//span[contains(text(),'去上传素材')])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_shop_decoration_hotspot_pic_intelligence(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        time.sleep(2)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//div[@class='decoration-preview-preview-mask'])[1]")
        self.click("//div[@class='decoration-upload-mask']")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//*[name()='svg'][@clip-rule='evenodd'])[9]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.click("(//p[contains(text(),'智能作图')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_element("(//span[@class='kpro-store-poster-composition-modal-title'])[1]")

    @pytest.mark.p1
    def test_shop_decoration_hotspot_link(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        # self.click("(//div[@class='decoration-preview-box decoration-preview-box-active']//div[@class='decoration-preview-preview-mask']")
        # self.click("//*[name()='path' and contains(@d,'M896 846.0')]")
        # self.assert_text("自选商品","(//div[@id='rc-tabs-1-tab-0'])[1]")
        # self.assert_text("选择分类页","(//div[@id='rc-tabs-1-tab-1'])[1]")
        # self.assert_text("选择店铺营销活动", "(//div[@id='rc-tabs-1-tab-2'])[1]")
        # self.assert_text("选择商品清单页", "(//div[@id='rc-tabs-1-tab-3'])[1]")

    @pytest.mark.p1
    def test_shop_decoration_jingxuan_tab(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        time.sleep(5)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//div[@class='decoration-preview-preview-mask'])[2]")
        self.assert_text("平台推荐", "(//span[contains(text(),'平台推荐')])[1]")
        self.assert_text("自主选品", "(//span[contains(text(),'自主选品')])[1]")
        self.assert_text("双列", "(//div[contains(text(),'双列')])[1]")
        self.assert_text("单列", "(//div[contains(text(),'单列')])[1]")
        self.assert_text("三列", "(//div[contains(text(),'三列')])[1]")
        self.assert_text("选择已有的商品清单导入", "(//div[contains(text(),'选择已有的商品清单导入')])[1]")

    # 图片热区关联清单页
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_decorationpc_pc_ImageHotspot_ListPage(self):
        self.into_shop_page("WB_DOMAIN", 'zengjingwen')
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.assert_text("编辑", "(//a[contains(text(),'编辑')])[1]")
        self.click("(//a[contains(text(),'编辑')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(3)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click(
            "//div[@class='decoration-preview-box decoration-preview-box-active']//div[@class='decoration-preview-preview-mask']")
        self.assert_text("编辑热区 1/10", "(//span[contains(text(),'编辑热区 1/10')])[1]")
        self.click("(//span[contains(text(),'编辑热区 1/10')])[1]")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("已选清单商品：(别下架）", "(//div[@class='decoration-good-item'])[1]")
        self.click("(//div[@class='decoration-good-item'])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(3)
        self.assert_element("(//div[@id='rc-tabs-0-tab-3'])[1]")

    # 海报图关联清单页
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_decorationpc_pc_Posterimage_ListPage(self):
        self.into_shop_page("WB_DOMAIN", 'zengjingwen')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=12196410916")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//div[@class='decoration-preview-preview-mask'])[2]")
        self.assert_text("已选清单商品：(别下架）", "(//div[@class='decoration-good-item'])[1]")
        self.click("(//div[@class='decoration-good-item'])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(3)
        self.assert_element("(//div[@id='rc-tabs-0-tab-3'])[1]")

    @pytest.mark.p1
    def test_shop_decoration_jingxuan_command(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        time.sleep(5)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//div[@class='decoration-preview-preview-mask'])[2]")
        self.click("(//span[contains(text(),'平台推荐')])[1]")
        self.assert_element("(//span[contains(text(),'保 存')])[1]")
        self.click("(//span[contains(text(),'保 存')])[1]")
        #self.assert_element("(//span[contains(text(),'保存成功')])[1]")

    @pytest.mark.p1
    def test_shop_decoration_jingxuan_choose_item(self):
        self.into_shop_page_prt(prt_domain, 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/shop/decoration/editV2?pageInstanceId=***********")
        time.sleep(5)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        self.click("(//div[@class='decoration-preview-preview-mask'])[2]")
        self.click("(//span[contains(text(),'自主选品')])[1]")
        self.click("(//span[contains(text(),'添加商品(0/50)')])[1]")
        self.assert_text("添加商品", "(//div[contains(text(),'添加商品')])[1]")

    @pytest.mark.p1
    def test_material_tab_check(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/material-center/list/shop?tabCode=material_poster")
        self.assert_text("素材制作", "(//div[@id='rc-tabs-0-tab-material_design'])[1]")
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")

    @pytest.mark.p1
    def test_material_intel_content_page(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(4)
        self.assert_text("主体图片", "(//label[contains(text(),'主体图片')])[1]")
        self.assert_text("海报尺寸", "(//label[contains(text(),'海报尺寸')])[1]")
        self.assert_text("模版选择", "(//label[contains(text(),'模版选择')])[1]")

    @pytest.mark.skip
    @pytest.mark.p1
    def test_material_intel_pic(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
        self.click("(//div[@class='kpro-store-poster-composition-content-icon'])[1]")
        self.click("(//p[@class='material-text'])[1]")
        self.assert_text("选择图片", "(//div[@id='rcDialogTitle0'])[1]")

    @pytest.mark.p1
    def test_material_son_tab(self):
        self.into_shop_page_prt(prt_domain, 'customer_merchant')
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        time.sleep(4)
        try:
            for i in range(11):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        # self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
        time.sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("商品图片", "(//span[contains(text(),'商品图片')])[1]")
        self.assert_text("店铺图片", "//span[contains(text(),'店铺图片')]")
        self.assert_text("直播预告图", "(//span[contains(text(),'直播预告图')])[1]")
        self.assert_text("图文挂车创作", "(//span[contains(text(),'图文挂车创作')])[1]")
        time.sleep(2)
        self.click("//span[contains(text(),'店铺图片')]")

    @pytest.mark.p1
    def test_material_setting(self):
        self.into_shop_page_prt(prt_domain, 'customer_merchant')
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        time.sleep(4)
        try:
            for i in range(11):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        # self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
        time.sleep(4)
        self.click("(//a[contains(text(),'设置')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("素材中心设置", "(//div[@class='kwaishop-seller-micro-material-center-pc-drawer-title'])[1]")
        self.assert_text("自动同步设置", "(//label[contains(text(),'自动同步设置')])[1]")
        self.assert_text("商品图片", "(//span[contains(text(),'商品图片')])[2]")
        self.assert_text("店铺图片", "(//span[contains(text(),'店铺图片')])[2]")
        self.assert_text("直播预告图", "(//span[contains(text(),'直播预告图')])[2]")
        self.assert_text("图文挂车创作", "(//span[contains(text(),'图文挂车创作')])[2]")

    @pytest.mark.p1
    def test_material_doc(self):
        self.into_shop_page_prt(prt_domain, 'customer_merchant')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        try:
            for i in range(8):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(4)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(4)
        self.assert_text("帮助文档", "(//a[contains(text(),'帮助文档')])[1]")
        self.click("(//a[contains(text(),'帮助文档')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")

    # @pytest.mark.p1
    # def test_material_upload_pic(self):
    #     self.into_shop_page_prt(prt_domain, 'customer_merchant')
    #     self.open("https://eshop-s.prt.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
    #     self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
    #     if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
    #         self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
    #     time.sleep(2)
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.click("(//span[contains(text(),'+ 上传图片')])[1]")
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(4)
    #     self.assert_element("(//p[@class='kwaishop-seller-micro-material-center-pc-upload-text'])[1]")
    #     self.click("(//span[@title='店铺图片'][contains(text(),'店铺图片')])[1]")
    #     self.assert_text("图片素材","(//span[contains(text(),'图片素材')])[1]")

    # @pytest.mark.p1
    # def test_material_new_folder(self):
    #     self.into_shop_page_prt(prt_domain, 'customer_merchant')
    #     self.open("https://eshop-s.prt.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
    #     self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
    #     try:
    #         for i in range(5):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
    #     time.sleep(2)
    #     try:
    #         for i in range(10):
    #             self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
    #     except:
    #         print()
    #     self.click("(//span[contains(text(),'新建文件夹')])[1]")
    #     handles = self.driver.window_handles
    #     self.driver.switch_to.window(handles[-1])
    #     time.sleep(4)
    #     self.assert_text("上级文件夹","(//label[contains(text(),'上级文件夹')])[1]")
    #     self.assert_text("文件夹名称","(//label[contains(text(),'文件夹名称')])[1]")
    #     self.assert_text("新 建","(//span[contains(text(),'新 建')])[1]")
    # 素材空间，上传图片
    @pytest.mark.p1
    def test_material_upload_pic(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(3)
        self.assert_text("店铺图片", "(//span[contains(text(),'店铺图片')])[1]")
        self.click("(//span[contains(text(),'店铺图片')])[1]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        # self.assert_element("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        self.assert_text("+ 上传图片", "(//span[contains(text(),'+ 上传图片')])[1]")
        self.click("(//span[contains(text(),'+ 上传图片')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("店铺图片", "(//span[@title='店铺图片'][contains(text(),'店铺图片')])[1]")
        self.click("(//span[@title='店铺图片'][contains(text(),'店铺图片')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("图片素材", "(//span[contains(text(),'图片素材')])[1]")

    # 素材空间，新建文件夹
    @pytest.mark.p1
    def test_material_new_folder(self):
        self.into_shop_page("WB_DOMAIN", 'customer_merchant')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(3)
        self.assert_text("店铺图片", "(//span[contains(text(),'店铺图片')])[1]")
        self.click("(//span[contains(text(),'店铺图片')])[1]")
        self.assert_text("新建文件夹", "(//span[contains(text(),'新建文件夹')])[1]")
        self.click("(//span[contains(text(),'新建文件夹')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("上级文件夹", "(//label[contains(text(),'上级文件夹')])[1]")
        self.assert_text("文件夹名称", "(//label[contains(text(),'文件夹名称')])[1]")
        self.assert_text("新 建", "(//span[contains(text(),'新 建')])[1]")

    # 素材空间，编辑图片
    @pytest.mark.p1
    def test_material_edit(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(3)
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        time.sleep(1)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("编辑", "(//span[contains(text(),'编辑')])[1]")
        self.click("(//span[contains(text(),'编辑')])[1]")

    # 素材空间，复制图片
    @pytest.mark.p1
    def test_material_copy(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(2)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(3)
        time.sleep(2)
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("复制",
                         '//*[@id="rc-tabs-0-panel-material_space"]/div[2]/div/section/main/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[9]/button[2]/span')
        time.sleep(2)
        self.click(
            '//*[@id="rc-tabs-0-panel-material_space"]/div[2]/div/section/main/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[9]/button[2]/span')

    # 素材空间，删除图片
    @pytest.mark.p1
    def test_material_delete(self):
        self.into_shop_page_prt(prt_domain, 'pengshengpu')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(2)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        time.sleep(1)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        # self.assert_text("为你推荐 ","(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # time.sleep(1)
        # self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # time.sleep(1)
        self.assert_text("删除", '//*[@id="main-pc-atmosphere-layout"]')
        time.sleep(1)
        self.click(('//*[@id="main-pc-atmosphere-layout"]'))
        time.sleep(1)
        # handles = self.driver.window_handles
        # self.driver.switch_to.window(handles[-1])
        #
        # # self.assert_text("确定要删除文件?","//span[@class='kwaishop-seller-micro-material-center-pc-modal-confirm-title']")
        # self.assert_text("再想想","(//span[contains(text(),'再想想')])[1]")
        # time.sleep(1)
        # self.click("(//span[contains(text(),'再想想')])[1]")

    # 点击回收站，清空回收站
    @pytest.mark.p1
    def test_material_clear(self):
        self.into_shop_page("WB_DOMAIN", 'pengshengpu')
        self.assert_text("店铺素材", "(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(2)
        self.click("(//span[@id='menu_item_Qkmca0Hw9jM'])[1]")
        time.sleep(1)
        self.assert_text("素材空间", "(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
        try:
            for i in range(10):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        time.sleep(2)
        self.assert_text("跳过", "(//span[contains(text(),'跳过')])[1]")
        self.click("(//span[contains(text(),'跳过')])[1]")
        time.sleep(2)
        try:
            for i in range(5):
                self.click("//*[@id=\"driver-popover-item\"]/div[4]/button")
        except:
            print()
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(4)
        self.assert_text("回收站", '//*[@id="recycle-dir-button"]/div')
        self.click('//*[@id="recycle-dir-button"]/div')
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        time.sleep(2)
        self.assert_text("清空回收站",
                         '//*[@id="rc-tabs-0-panel-material_space"]/div[2]/div/section/main/div[1]/div/div[3]/div[2]/div/button/span')
        self.click(
            '//*[@id="rc-tabs-0-panel-material_space"]/div[2]/div/section/main/div[1]/div/div[3]/div[2]/div/button/span')
        # time.sleep(1)
        # self.assert_text("新建","(//span[contains(text(),'新 建')])[1]")

    # @pytest.mark.p1
    # def test_material_pic_edit(self):
    #     self.into_shop_page("WB_DOMAIN", 'customer_merchant')
    #     self.open("https://s.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
    #     self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
    #     self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
    #     time.sleep(2)
    #
    # @pytest.mark.p1
    # def test_material_pic_del(self):
    #     self.into_shop_page("WB_DOMAIN", 'customer_merchant')
    #     self.open("https://s.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
    #     self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
    #     self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
    #     time.sleep(2)
    #
    # @pytest.mark.p1
    # def test_material_pic_copy(self):
    #     self.into_shop_page("WB_DOMAIN", 'customer_merchant')
    #     self.open("https://s.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
    #     self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
    #     self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
    #     time.sleep(2)
    #
    # @pytest.mark.p1
    # def test_material_pic_recycleBin(self):
    #     self.into_shop_page("WB_DOMAIN", 'customer_merchant')
    #     self.open("https://s.kwaixiaodian.com/zone/material-center/list/shop?tabCode=material_poster")
    #     self.click("(//div[@id='rc-tabs-0-tab-material_space'])[1]")
    #     self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span")
    #     time.sleep(2)
