import json
import time
from unittest import skip

import requests
from selenium import webdriver
from selenium.webdriver import Chrome
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def get_options():
    chrome_options = Options()
    # chrome_options.add_argument('--headless')
    chrome_options.add_experimental_option("detach", True)
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])

    return chrome_options


def add_cookies(driver):
    # 获取动态tooken信息
    uid = '**********'
    response = requests.post(url='https://staging-infra-admin.corp.kuaishou.com/token/create?userId=' + uid + '&sid=kuaishou.api')
    res = "{" + str(response.content)[1:-1].split(':1,')[1]
    res_map = json.loads(res)  # res_map :{kuaishou.api_ph : xx, ssecurity:xx, kuaishou.api.at:xx, tokenClientSalt:xx, kuaishou.api_st:xx, did:xxx}

    # 倒入cooke信息
    did = res_map.get('did')
    api_st = res_map.get('kuaishou.api_st')
    cookies_str = '_did=' + did + '; hdige2wqwoino=th5j4YFi77C7yWDxarT77QjNy8SQF46Md2dffa07; result=1; kpf=ANDROID_PHONE; c=TEST; language=zh-cn; countryCode=CN; did_tag=0; thermal=10000; app=0; android_os=0; newOc=TEST; slh=0; country_code=cn; hotfix_ver=; keyconfig_state=2; max_memory=256; oc=TEST; sh=2400; deviceBit=0; X-KTrace-Id-Enabled=0; is_background=0; sw=1080; ftt=; abi=arm64; cl=0; userRecoBit=0; device_abi=arm64; grant_browse_type=AUTHORIZED; iuid=; darkMode=false; __NSWJ=; net=WIFI; ktrace-context=1|MS44NDczNjc0NTc4Njk4ODg0LjQ3NTM0Njc1LjE3MDA2MzU2MzgxNTAuMjY4MTk2|MS44NDczNjc0NTc4Njk4ODg0LjYyNDY0NDg1LjE3MDA2MzU2MzgxNTAuMjY4MTk3|0|kfx-static-next|webservice|true|src-Js; earphoneMode=1; did=ANDROID_6616070cc3c2f921; ver=11.11; appver=11.11.10.99999; sys=ANDROID_13; mod=vivo%28V2304A%29; deviceName=vivo%28V2304A%29; isp=CMCC; oDid=ANDROID_6616070cc3c2f921; boardPlatform=kalama; androidApiLevel=33; nbh=126; cdid_tag=5; ddpi=480; socName=Unknown; totalMemory=15258; trace-context=; rdid=ANDROID_9409958d97e65a58; sbh=96; kpn=NEBULA; userId=2197688905; ud=2197688905; egid=DFP5B2ADF95B606AD73AD8C889E103EC06D604387F728F1082FE45907C639199; kcv=1545; bottom_navigation=true; did_gt=1701401491626; cold_launch_time_ms=1701847003054; browseType=3; apptype=22; kuaishou.api_st=' + api_st + '; token=Cg9rdWFpc2hvdS5hcGkuc3QSoAG_zFdQ6RsQuCX9M9jzj6cCXkIlnOft0cpk49wiyYUsjRHT1CQgMNesMLl2WoyP0Ypo-b_uHTQkfF1lILdejzb_DdUkx7FcOhplNihNbEgQKUNe27GIw26pVwRj8cBKS9W6OT6O4JG7ysgAEyVD3urbFfSrWQIsKCATHgTQLhKR6j--Jd0yxxPkv12lsrwETLnSTo6TJUI5zQ_wWy0yDuhuGhJ0Iffd11DQSKB-JV8Bvl49xBYiIJuCsfVgYJxVd-RKndRzPhypG1CEgGZrztbtNY8mXkkyKA8wAQ; client_key=2ac2a76d; kuaishou.h5_st=Cg5rdWFpc2hvdS5oNS5zdBKQAXqOeJ8EsWnHqsBYknWNGeUhk9aFOjrXisgUjs6pBBhD7sc017iOsy7U-4Q5VO10IhrCdLKnAwYHjVklTRYN7ASo7kOzrqTfJtLbB9taDRIPSc4eaXKehcnl37IWK305M46EptXif2gvLqQD6oREJRFjs6WcnhKA_bGuLPAMroXVXQcA-eAmaFvif7q8J1uExBoShmuA04kZhU2bR6RcTY6Lcsr6IiBYfIbA2AVlhdVJgZA9Q2JDTWVLLNp7AgxAOzGrh3msMSgPMAE; apdid=3ac1c508-c112-4cb4-9a35-93c4c815a3347c5b5382d8861e6f72f64b0d5d3c2efc:1701847075:1; sid=86b6e3ec-6c81-49d4-b27e-a92b1e42efa1; ehid=06GlGketQbuiq0-R7oqBn1k4EXiiDU8vxdGeZ'
    cookie = {'name': 'cookie_name', 'value': 'cookie_value'}
    driver.add_cookie(cookie)
    for cookie in cookies_str.split('; '):
        # cookies_dict = {'domain': '.gifshow.com', 'expiry': **********, 'httpOnly': True, 'path': '/', 'secure': False}
        cookies_dict = {}
        cookies_dict['name'] = cookie.split('=')[0]
        cookies_dict['value'] = cookie.split('=')[-1]
        cookies_dict[cookie.split('=')[0]] = cookie.split('=')[-1]
        print(1, cookies_dict)
        driver.add_cookie(cookies_dict)
        #导入泳道
        #driver.add_cookie({"name": "trace-context", "value": "%7B%22laneId%22%3A%22STAGING.bank_logo%22%7D"})


def load_urls(driver: Chrome, url: str, self=None) -> None:
    driver.get(url)
    add_cookies(driver)
    driver.get(url)
    time.sleep(3)
    driver.maximize_window()  # 浏览器最大化
    time.sleep(2)


class Testqiyes():
    def test_qiyes(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（微信）  uid——**********    手机号：***********
        time.sleep(5)
        # 立即绑定
        driver.find_element(By.XPATH, '//span[text()=" 立即绑定 "]').click()
        time.sleep(3)
        # 获取验证码
        driver.find_element(By.CLASS_NAME, value='get-verify-code').click()
        time.sleep(4)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(2)
        # 主体信息修改
        driver.find_element(By.CLASS_NAME, 'btn-edit').click()
        time.sleep(4)
        # 页面下拉到最底地步
        driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
        time.sleep(3)
        # 提交修改资质信息
        driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
        time.sleep(5)
        driver.quit()


    def test_qiyess(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（微信） uid——**********    手机号：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, '//span[text()=" 立即绑定 "]').click()
        time.sleep(3)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)
        # 主体信息修改
        driver.find_element(By.CLASS_NAME, 'btn-edit').click()
        time.sleep(4)
        # 页面下拉到地步
        driver.execute_script("window.scrollBy(0, 600);")
        time.sleep(4)
        # 修改证件类型
        driver.find_element(By.XPATH, '//input[@placeholder="请选择证件类型"]').click()
        time.sleep(3)
        # 选择护照类型
        driver.find_element(By.XPATH, '//span[text()="其他国家或地区居民-护照"]').click()
        time.sleep(4)
        # 页面下拉到最底地步
        driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
        time.sleep(3)
        # 提交修改资质信息
        driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
        time.sleep(5)
        driver.quit()


    def test_qiyec(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（微信）  uid——**********    手机号：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, '//span[text()=" 立即绑定 "]').click()
        time.sleep(3)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)
        # 主体信息修改
        driver.find_element(By.CLASS_NAME, 'btn-edit').click()
        time.sleep(4)
        # 页面下拉到地步
        driver.execute_script("window.scrollBy(0, 600);")
        time.sleep(3)
        # 修改证件类型
        driver.find_element(By.XPATH, '//input[@placeholder="请选择证件类型"]').click()
        time.sleep(3)
        # 选择大陆—身份证类型
        driver.find_element(By.XPATH, '//span[text()="中国大陆居民-身份证"]').click()
        time.sleep(4)
        # 页面下拉到最底地步
        driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
        time.sleep(3)
        # 提交修改资质信息
        driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
        time.sleep(5)
        driver.quit()

    @skip("111")
    def test_qiyecc(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（微信）  uid——**********    shoujihao：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, '//span[text()=" 立即绑定 "]').click()
        time.sleep(3)
        # 获取验证码
        #driver.find_element(By.XPATH, '//*[@id="app"]/div/div/div[2]/div/div[1]/a').click()
        #time.sleep(4)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)

        # 输入银行卡号
        driver.find_element(By.XPATH, '//input[@placeholder="请输入银行卡号"]').send_keys('62283860203565513')
        time.sleep(3)
        # 页面滑动
        driver.execute_script("window.scrollBy(0, 150);")
        time.sleep(3)
        # 开户行
        driver.find_element(By.XPATH, '//input[@placeholder="请选择开户行"]').click()
        time.sleep(3)
        # 选择对应银行
        driver.find_element(By.XPATH, '//span[text()="招商银行"]').click()
        time.sleep(2)
        # 所在地区
        driver.find_element(By.XPATH, '//input[@placeholder="请选择开户行所在地区"]').click()
        time.sleep(3)
        driver.find_element(By.XPATH, '//span[text()="内蒙古自治区"]').click()
        time.sleep(3)
        driver.find_element(By.XPATH, '//span[text()="通辽市"]').click()
        time.sleep(3)
        driver.find_element(By.XPATH, '//span[text()="库伦旗"]').click()
        time.sleep(3)
        # 页面滑动
        driver.execute_script("window.scrollBy(0, 500);")
        time.sleep(3)
        # 管理员类型
        driver.find_element(By.XPATH, '//input[@placeholder="请选择管理员类型"]').click()
        time.sleep(2)
        # 经营者/法人
        driver.find_element(By.XPATH, '//span[text()="经营者/法人"]').click()
        time.sleep(2)
        # 管理员手机号
        driver.find_element(By.XPATH, '//input[@placeholder="请输入管理员手机号"]').send_keys('1503506511117')
        time.sleep(3)
        # 管理员邮箱
        driver.find_element(By.XPATH, '//input[@placeholder="请输入管理员邮箱"]').send_keys('<EMAIL>')
        time.sleep(3)
        # 勾选协议
        driver.find_element(By.CLASS_NAME, 'el-checkbox__inner').click()
        time.sleep(2)
        # 个体工商户进件流程提交
        driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
        time.sleep(5)
        driver.quit()

    @skip("111")
    #支付宝进件======================》》》》》》》
    def test_qiyezfbs(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)

        # 进件pc二期===================》》》》》》企业（支付宝）  uid——**********    手机号：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, "(//span[text()=' 立即绑定 '])[2]").click()
        time.sleep(3)
        # 获取验证码
        driver.find_element(By.XPATH, '//*[@id="app"]/div/div/div[2]/div/div[1]/a').click()
        time.sleep(4)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)
        # 主体信息修改
        driver.find_element(By.CLASS_NAME, 'btn-edit').click()
        time.sleep(4)
        # 页面下拉到最底地步
        driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
        time.sleep(3)
        # 法人居住地址
        # driver.find_element(By.XPATH, '//input[@placeholder="请输入法人地址"]').send_keys('北京数码科技')
        # time.sleep(3)
        # 提交修改资质信息
        driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
        time.sleep(5)
        driver.quit()

    @skip("111")
    def test_qiyezfbss(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（支付宝）  uid——**********    手机号：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, "(//span[text()=' 立即绑定 '])[2]").click()
        time.sleep(3)
        # 获取验证码
        driver.find_element(By.XPATH, '//*[@id="app"]/div/div/div[2]/div/div[1]/a').click()
        time.sleep(3)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(4)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)
        # 支付宝账号
        driver.find_element(By.XPATH, '//input[@placeholder="请输入支付宝账号"]').send_keys('13521147246')
        time.sleep(3)
        # 联系人姓名
        driver.find_element(By.XPATH, '//input[@placeholder="请输入联系人姓名"]').send_keys('阎洪萍')
        time.sleep(3)
        # 联系人手机号
        driver.find_element(By.XPATH, '//input[@placeholder="请输入联系人手机号"]').send_keys('188973647133')
        time.sleep(2)
        # 勾选协议
        driver.find_element(By.CLASS_NAME, 'el-checkbox__inner').click()
        time.sleep(2)
        # 进件个人流程提交
        driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
        time.sleep(5)
        driver.quit()







    def test_individual_agreement(self):
        """进件三期——支付宝协议展示、点击、跳转"""
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=ALIPAY_ZFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（支付宝）  uid——**********    手机号：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, "(//span[text()=' 立即绑定 '])[2]").click()
        time.sleep(3)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)
        agreement = driver.find_element(By.XPATH, '//*[@id="app"]/div/div/div[2]/div[1]/div[1]/h5/div/span[2]/img')
        if agreement:  # 判断协议按钮入口是否存在
            agreement.click()
            time.sleep(2)
        agreement_theme = driver.find_element(By.CLASS_NAME, 'el-dialog__title')
        if agreement_theme:
            print('协议主题正确')
        agreements = driver.find_element(By.XPATH, '//a[text()="企业支付宝账号注册指引"]')
        if agreements:  # 判断协议跳转入口是否存在
            agreements.click()
            time.sleep(5)
        driver.quit()


    def test_individual_agreements(self):
        """进件三期——支付宝协议弹窗打开、关闭"""
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=ALIPAY_ZFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        # 进件pc二期===================》》》》》》企业（支付宝）  uid——**********    手机号：***********——苗败
        # 立即绑定
        driver.find_element(By.XPATH, "(//span[text()=' 立即绑定 '])[2]").click()
        time.sleep(3)
        # 输入验证码
        driver.find_element(By.NAME, value='vcode').send_keys('666666')
        time.sleep(3)
        # 实名
        driver.find_element(By.NAME, value='realname').send_keys('苗败')
        time.sleep(3)
        # 下一步
        driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
        time.sleep(3)
        agreement = driver.find_element(By.XPATH, '//*[@id="app"]/div/div/div[2]/div[1]/div[1]/h5/div/span[2]/img')
        if agreement:  # 判断协议按钮入口是否存在
            agreement.click()
            time.sleep(2)
        got_it = driver.find_element(By.XPATH, '//span[text()="知道了"]')  # 点击我知道了，关闭协议
        got_it.click()
        time.sleep(2)
        agreement_theme = driver.find_element(By.CLASS_NAME, 'el-dialog__title')
        if not agreement_theme:
            print('关闭协议成功')
        agreement.click()
        time.sleep(2)
        close = driver.find_element(By.XPATH,'//*[@id="app"]/div/div/div[2]/div[2]/div/div[1]/button/i')  # 点击“X”，关闭协议
        close.click()
        time.sleep(2)
        if not agreement_theme:
            print('关闭协议成功')
        driver.quit()

















