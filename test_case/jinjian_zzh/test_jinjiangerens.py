import json
import time
from unittest import skip

# import pyautogui
import requests
from selenium.webdriver import ActionChains
import selenium
from selenium import webdriver
from selenium.webdriver import Chrome
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service



def get_options():
    chrome_options = Options()
    # chrome_options.add_argument('--headless')
    chrome_options.add_experimental_option("detach", True)
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])

    return chrome_options


"""
#本地查看图片
def load_file(path):
    time.sleep(1)
    pyautogui.click(700,700) # 鼠标点前端页面一下
    time.sleep(1)
    message_list = []
    for i in range(len(path)):
        message_list.append(path[i])

    for c in message_list:
        if len(c) > 1:
            c = c.lower()
        pyautogui.press(c, _pause=False)
        time.sleep(0.2)
        pyautogui.failSafeCheck()

    pyautogui.press('enter',2)
    time.sleep(2)
    pyautogui.press('enter', 1)
"""


def add_cookies(driver):
    # 获取动态tooken信息
    uid = '**********'
    response = requests.post(url='https://staging-infra-admin.corp.kuaishou.com/token/create?userId=' + uid + '&sid=kuaishou.api')
    res = "{" + str(response.content)[1:-1].split(':1,')[1]
    res_map = json.loads(res)  # res_map :{kuaishou.api_ph : xx, ssecurity:xx, kuaishou.api.at:xx, tokenClientSalt:xx, kuaishou.api_st:xx, did:xxx}

    # 倒入cooke信息
    did = res_map.get('did')
    api_st = res_map.get('kuaishou.api_st')
    cookies_str = '_did=' + did + '; hdige2wqwoino=th5j4YFi77C7yWDxarT77QjNy8SQF46Md2dffa07; result=1; kpf=ANDROID_PHONE; c=TEST; language=zh-cn; countryCode=CN; did_tag=0; thermal=10000; app=0; android_os=0; newOc=TEST; slh=0; country_code=cn; hotfix_ver=; keyconfig_state=2; max_memory=256; oc=TEST; sh=2400; deviceBit=0; X-KTrace-Id-Enabled=0; is_background=0; sw=1080; ftt=; abi=arm64; cl=0; userRecoBit=0; device_abi=arm64; grant_browse_type=AUTHORIZED; iuid=; darkMode=false; __NSWJ=; net=WIFI; ktrace-context=1|MS44NDczNjc0NTc4Njk4ODg0LjQ3NTM0Njc1LjE3MDA2MzU2MzgxNTAuMjY4MTk2|MS44NDczNjc0NTc4Njk4ODg0LjYyNDY0NDg1LjE3MDA2MzU2MzgxNTAuMjY4MTk3|0|kfx-static-next|webservice|true|src-Js; earphoneMode=1; did=ANDROID_6616070cc3c2f921; ver=11.11; appver=11.11.10.99999; sys=ANDROID_13; mod=vivo%28V2304A%29; deviceName=vivo%28V2304A%29; isp=CMCC; oDid=ANDROID_6616070cc3c2f921; boardPlatform=kalama; androidApiLevel=33; nbh=126; cdid_tag=5; ddpi=480; socName=Unknown; totalMemory=15258; trace-context=; rdid=ANDROID_9409958d97e65a58; sbh=96; kpn=NEBULA; userId=2197688905; ud=2197688905; egid=DFP5B2ADF95B606AD73AD8C889E103EC06D604387F728F1082FE45907C639199; kcv=1545; bottom_navigation=true; did_gt=1701401491626; cold_launch_time_ms=1701847003054; browseType=3; apptype=22; kuaishou.api_st=' + api_st + '; token=Cg9rdWFpc2hvdS5hcGkuc3QSoAG_zFdQ6RsQuCX9M9jzj6cCXkIlnOft0cpk49wiyYUsjRHT1CQgMNesMLl2WoyP0Ypo-b_uHTQkfF1lILdejzb_DdUkx7FcOhplNihNbEgQKUNe27GIw26pVwRj8cBKS9W6OT6O4JG7ysgAEyVD3urbFfSrWQIsKCATHgTQLhKR6j--Jd0yxxPkv12lsrwETLnSTo6TJUI5zQ_wWy0yDuhuGhJ0Iffd11DQSKB-JV8Bvl49xBYiIJuCsfVgYJxVd-RKndRzPhypG1CEgGZrztbtNY8mXkkyKA8wAQ; client_key=2ac2a76d; kuaishou.h5_st=Cg5rdWFpc2hvdS5oNS5zdBKQAXqOeJ8EsWnHqsBYknWNGeUhk9aFOjrXisgUjs6pBBhD7sc017iOsy7U-4Q5VO10IhrCdLKnAwYHjVklTRYN7ASo7kOzrqTfJtLbB9taDRIPSc4eaXKehcnl37IWK305M46EptXif2gvLqQD6oREJRFjs6WcnhKA_bGuLPAMroXVXQcA-eAmaFvif7q8J1uExBoShmuA04kZhU2bR6RcTY6Lcsr6IiBYfIbA2AVlhdVJgZA9Q2JDTWVLLNp7AgxAOzGrh3msMSgPMAE; apdid=3ac1c508-c112-4cb4-9a35-93c4c815a3347c5b5382d8861e6f72f64b0d5d3c2efc:1701847075:1; sid=86b6e3ec-6c81-49d4-b27e-a92b1e42efa1; ehid=06GlGketQbuiq0-R7oqBn1k4EXiiDU8vxdGeZ'
    cookie = {'name': 'cookie_name', 'value': 'cookie_value'}
    driver.add_cookie(cookie)
    for cookie in cookies_str.split('; '):
        # cookies_dict = {'domain': '.gifshow.com', 'expiry': **********, 'httpOnly': True, 'path': '/', 'secure': False}
        cookies_dict = {}
        cookies_dict['name'] = cookie.split('=')[0]
        cookies_dict['value'] = cookie.split('=')[-1]
        cookies_dict[cookie.split('=')[0]] = cookie.split('=')[-1]
        print(1, cookies_dict)
        driver.add_cookie(cookies_dict)
    # 导入泳道
    # driver.add_cookie({"name": "trace-context", "value": "%7B%22laneId%22%3A%22STAGING.bank_logo%22%7D"})


def load_urls4(driver: Chrome, url: str, self=None) -> None:
    driver.get(url)
    add_cookies(driver)
    time.sleep(3)
    driver.get(url)
    time.sleep(3)
    driver.maximize_window()  # 浏览器最大化
    time.sleep(3)
    # 进件pc二期===================》》》》》》个人（微信）uid——**********    手机号：***********——归虫
    # 立即绑定
    driver.find_element(By.XPATH, '//span[text()=" 立即绑定 "]').click()
    time.sleep(3)
    # 获取验证码
    driver.find_element(By.CLASS_NAME, value='get-verify-code').click()
    time.sleep(4)
    # 输入验证码
    driver.find_element(By.NAME, value='vcode').send_keys('666666')
    time.sleep(3)
    # 实名
    driver.find_element(By.NAME, value='realname').send_keys('归虫')
    time.sleep(3)
    # 下一步
    driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
    time.sleep(3)
    # 修改进件信息
    driver.find_element(By.XPATH, '//div[text()=" 修改 "]').click()
    time.sleep(3)
    # 页面滑动
    driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
    time.sleep(3)
    # 提交修改进件资质信息
    driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
    time.sleep(5)


def load_urls3(driver: Chrome, url: str, self=None) -> None:
    driver.get(url)
    time.sleep(5)
    add_cookies(driver)
    driver.get(url)
    time.sleep(3)
    driver.maximize_window()  # 浏览器最大化
    time.sleep(3)
    # 进件pc二期===================》》》》》》个人（微信）uid——**********    手机号：***********——归虫
    # 立即绑定
    driver.find_element(By.XPATH, '//span[text()=" 立即绑定 "]').click()
    time.sleep(3)
    # 获取验证码
    driver.find_element(By.CLASS_NAME, value='get-verify-code').click()
    time.sleep(4)
    # 输入验证码
    driver.find_element(By.NAME, value='vcode').send_keys('666666')
    time.sleep(3)
    # 实名
    driver.find_element(By.NAME, value='realname').send_keys('归虫')
    time.sleep(3)
    # 下一步
    driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
    time.sleep(3)
    # 银行卡
    driver.find_element(By.XPATH, '//input[@placeholder="请输入银行卡号"]').send_keys('6231560203565503')
    time.sleep(3)
    # 页面滑动
    driver.execute_script("window.scrollBy(0, 120);")
    time.sleep(3)
    # 开户行
    driver.find_element(By.XPATH, '//input[@placeholder="请选择开户行"]').click()
    time.sleep(3)
    # 选择对应银行
    driver.find_element(By.XPATH, '//span[text()="中信银行"]').click()
    time.sleep(2)
    # 所在地区
    driver.find_element(By.XPATH, '//input[@placeholder="请选择开户行所在地区"]').click()
    time.sleep(3)
    driver.find_element(By.XPATH, '//span[text()="山西省"]').click()
    time.sleep(3)
    driver.find_element(By.XPATH, '//span[text()="长治市"]').click()
    time.sleep(3)
    driver.find_element(By.XPATH, '//span[text()="长治县"]').click()
    time.sleep(3)
    # 页面滑动
    driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
    time.sleep(3)
    # 管理员类型
    driver.find_element(By.XPATH, '//input[@placeholder="请选择管理员类型"]').click()
    time.sleep(2)
    driver.find_element(By.XPATH, '//span[text()="经营者/法人"]').click()
    time.sleep(2)
    # 管理员手机号
    driver.find_element(By.XPATH, '//input[@placeholder="请输入管理员手机号"]').send_keys('1503506511117')
    time.sleep(3)
    # 管理员邮箱
    driver.find_element(By.XPATH, '//input[@placeholder="请输入管理员邮箱"]').send_keys('<EMAIL>')
    time.sleep(3)
    # 勾选协议
    driver.find_element(By.CLASS_NAME, 'el-checkbox__inner').click()
    time.sleep(2)
    # 进件个人流程提交
    driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
    time.sleep(5)


# 支付宝进件======================》》》》》》》
def load_urls2(driver: Chrome, url: str, self=None) -> None:
    driver.get(url)
    add_cookies(driver)
    driver.get(url)
    time.sleep(3)
    driver.maximize_window()  # 浏览器最大化
    time.sleep(3)
    # 进件pc二期===================》》》》》》个人（支付宝）uid——**********    手机号：***********——归虫
    # 立即绑定
    driver.find_element(By.XPATH, "(//span[text()=' 立即绑定 '])[2]").click()
    time.sleep(3)
    # 获取验证码
    driver.find_element(By.CLASS_NAME, value='get-verify-code').click()
    time.sleep(3)
    # 输入验证码
    driver.find_element(By.NAME, value='vcode').send_keys('666666')
    time.sleep(3)
    # 实名
    driver.find_element(By.NAME, value='realname').send_keys('归虫')
    time.sleep(3)
    # 下一步
    driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
    time.sleep(3)
    # 修改进件信息
    driver.find_element(By.XPATH, '//div[text()=" 修改 "]').click()
    time.sleep(3)
    # 页面滑动
    driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
    time.sleep(3)
    # 法人居住地址
    driver.find_element(By.XPATH, '//input[@placeholder="请输入法人地址"]').send_keys('北京数码科技')
    time.sleep(3)
    # 提交修改进件资质信息
    driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
    time.sleep(5)


def load_urls(driver: Chrome, url: str, self=None) -> None:
    driver.get(url)
    add_cookies(driver)
    driver.get(url)
    time.sleep(3)
    driver.maximize_window()  # 浏览器最大化
    time.sleep(3)
    # 进件pc二期===================》》》》》》个人（支付宝）uid——**********    手机号：***********——归虫
    # 立即绑定
    driver.find_element(By.XPATH, "(//span[text()=' 立即绑定 '])[2]").click()
    time.sleep(3)
    # 获取验证码
    driver.find_element(By.CLASS_NAME, value='get-verify-code').click()
    time.sleep(3)
    # 输入验证码
    driver.find_element(By.NAME, value='vcode').send_keys('666666')
    time.sleep(3)
    # 实名
    driver.find_element(By.NAME, value='realname').send_keys('归虫')
    time.sleep(3)
    # 下一步
    driver.find_element(By.XPATH, '//span[text()=" 下一步 "]').click()
    time.sleep(3)
    # 支付宝账号
    driver.find_element(By.XPATH, '//input[@placeholder="请输入支付宝账号"]').send_keys('13521147246')
    time.sleep(3)
    # 联系人姓名
    driver.find_element(By.XPATH, '//input[@placeholder="请输入联系人姓名"]').send_keys('阎洪萍')
    time.sleep(3)
    # 联系人手机号
    driver.find_element(By.XPATH, '//input[@placeholder="请输入联系人手机号"]').send_keys('15035065111')
    time.sleep(2)
    # 勾选协议
    driver.find_element(By.CLASS_NAME, 'el-checkbox__inner').click()
    time.sleep(2)
    # 进件个人流程提交
    driver.find_element(By.XPATH, '//span[text()=" 提交 "]').click()
    time.sleep(5)


class Testgerens():

    def test_gerenzfbs(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls(driver, url)
        time.sleep(5)
        driver.quit()

    def test_gerenzfb(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls2(driver, url)
        time.sleep(5)
        driver.quit()

    def test_jinjianss(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls3(driver, url)
        time.sleep(5)
        driver.quit()

    def test_jinjians(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay-staging.test.gifshow.com/pcservices/merchant-apply-v2/select-account-type?accountGroupKey=TEST_KUAISHOU_APP_DEMO&merchantId=testmerchant20180328000&status=1&isPingan=1&product=WECHAT_SFT&routeAfterVerify=select-subject-type&backUrl=http%3A%2F%2Fwww.kuaishou.com%2F&ticket=MOCK'
        load_urls4(driver, url)
        time.sleep(5)
        driver.quit()
