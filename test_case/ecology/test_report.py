"""
des: 保障-举报中心 PC
author: zhouchang03
date: 2023/07/02
"""
import time
import pytest
from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p2
class Report(BaseTestCase):
    def input_report(self):
        """ 进入保障-举报中心"""
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("举报中心", "//span[text()='举报中心']")
        self.click("//span[text()='举报中心']")
        time.sleep(2)
        self.close_merchant_assistant()

    def test_homepage(self):
        """ 保障-举报中心"""
        self.input_report()

        # 首页信息
        self.assert_text("创建举报", "//span[contains(text(),'创建举报')]")


    def test_create_report(self):
        """ 进入保障-举报中心-创建举报"""
        self.input_report()
        # 点击创建举报
        self.click("//span[contains(text(),'创建举报')]")
        time.sleep(2)

        # 检查静态内容
        # 选择您要举报的类别 异常评价 异常下单 异常售后 异常会话
        self.assert_text("选择您要举报的类别", "//h1[contains(text(),'选择您要举报的类别')]")
        self.assert_text("异常评价", "//span[contains(text(),'异常评价')]")
        self.assert_text("异常下单", "//span[contains(text(),'异常下单')]")
        self.assert_text("异常售后", "//span[contains(text(),'异常售后')]")
        self.assert_text("异常会话", "//span[contains(text(),'异常会话')]")
        # 按钮-取消 确定
        self.assert_text("取 消", "//span[contains(text(),'取 消')]")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")


    def test_create_report_step2(self):
        """ 进入保障-举报中心-创建举报-跳转链接下一步"""
        self.input_report()
        # 点击创建举报
        self.click("//span[contains(text(),'创建举报')]")
        time.sleep(2)

        # 点击异常会话进入下一页
        self.click("//span[contains(text(),'异常会话')]")
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(1)

        # 进入下一步,检查页面新内容
        # 检查静态内容
        # 举报类型 异常类型 异常会话 举报说明 聊天记录授权 为便于核实举报问题，授权平台查看与消费者的聊天记录
        self.assert_text("举报类型", "//label[contains(text(),'举报类型')]")
        self.assert_text("异常类型", "//label[contains(text(),'异常类型')]")
        self.assert_text("异常会话", "//label[contains(text(),'异常会话')]")
        self.assert_text("举报说明", "//label[contains(text(),'举报说明')]")
        self.assert_text("聊天记录授权", "//label[contains(text(),'聊天记录授权')]")
        self.assert_text("为便于核实举报问题，授权平台查看与消费者的聊天记录", "//span[contains(text(),'为便于核实举报问题，授权平台查看与消费者的聊天记录')]")

        # 按钮-取消 提交举报
        self.assert_text("取 消", "//span[contains(text(),'取 消')]")
        self.assert_text("提交举报", "//span[contains(text(),'提交举报')]")