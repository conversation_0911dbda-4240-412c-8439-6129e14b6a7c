"""
des: 店铺体验分 PC
author: wangxue12
date: 2023/04/12
"""

import pytest
import time
import logging
from ddt import ddt

from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p0
class KuaishopScore(BaseTestCase):
    def input_shopscorepage(self):
        """ 进入店铺体验分首页 """
        self.login("KWAIXIAODIAN", "themis")
        self.wait_for_ready_state_complete()
        self.sleep(2)
        self.shop_close_pop()
        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.click("//span[text()='店铺体验分']")
        self.sleep(5)
        if self.is_element_visible("//div[@class='bzOGdctFKyOOrUs7iCif']") or self.is_text_visible("店铺体验分升级通知"):
            self.click("//span[contains(text(),'我知道了')]")
        self.sleep(2)
        self.close_merchant_assistant()
        self.wait_for_ready_state_complete()
        logging.info("input store score succeed")

    def test_kwaishop_homepage(self):
        """ 店铺分首页 """
        self.input_shopscorepage()

        # 趋势图模块
        self.click("//span[contains(text(),'查看趋势')]")
        self.sleep(2)
        self.assert_text("店铺概况", "//div[text()='店铺概况']")
        self.assert_text("店铺体验分", "//span[text()='店铺体验分']")
        self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")

        self.click("(//span[contains(text(),'趋势')])[2]")
        self.sleep(2)
        self.assert_text("商品体验", "//div[text()='商品体验']")
        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")

        self.click("(//span[contains(text(),'趋势')])[3]")
        self.sleep(2)
        self.assert_text("物流体验", "//div[text()='物流体验']")
        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")

        self.click("(//span[contains(text(),'趋势')])[4]")
        self.sleep(2)
        self.assert_text("售后服务", "//div[text()='售后服务']")
        self.assert_text("售后服务", "//span[text()='售后服务']")
        self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")


    def test_homepage_rulehub_link(self):
        """ 右侧tab页内容"""
        self.input_shopscorepage()

        # 右侧规则中心
        self.assert_element_visible("//div[text()='快手小店店铺体验分管理规则']")
        self.click("//div[text()='快手小店店铺体验分管理规则']")
        time.sleep(1)
        self.switch_to_window(0)

        self.click("//span[text()='店铺体验分问题反馈']")
        time.sleep(1)
        self.type("input[placeholder='请输入手机号']", "19133301607")
        self.type("input[placeholder='请输入密码']", "test@123")
        self.click("//button[text()='登录']")
        self.assert_element_visible("//div[text()='[新版店铺体验分]满意度调研']")

    def test_kwaishop_consumer_feedback(self):
        """
        消费者端展示规则-反馈
        """
        self.input_shopscorepage()

        # 消费者侧展示规则
        self.refresh_page()
        self.assert_text('消费者端展示规则', "//span[text()='消费者端展示规则']")
        self.click("//span[text()='消费者端展示规则']")
        self.sleep(0.5)
        self.assert_text('消费者端展示规则', "//span[text()='消费者端展示规则']")
        self.assert_text('当前消费者端展示分数', "//span[contains(text(),'当前消费者端展示分数')]")
        self.assert_text('分数-星级对应', "//div[text()='分数-星级对应']")
        self.assert_text("展示规则", "//div[text()='展示规则']")

        self.click("//span[contains(text(),'点击反馈')]")
        self.switch_to_window(1)
        self.sleep(1)
        self.type("input[placeholder='请输入手机号']", "19133301607")
        self.type("input[placeholder='请输入密码']", "test@123")
        self.click("//button[text()='登录']")
        self.sleep(1)
        self.assert_element_visible("//p[contains(text(),'问题反馈')]")
        self.switch_to_window(0)
        self.click("//span[text()='我知道了']")

    def test_kwaishop_consumer_scene(self):
        """
        消费者端展示规则-场景
        """
        self.input_shopscorepage()

        # 消费者侧展示规则
        self.assert_text('消费者端展示规则', "//span[text()='消费者端展示规则']")
        self.click("//span[text()='消费者端展示规则']")
        self.sleep(0.5)
        self.assert_text('消费者端展示规则', "//span[text()='消费者端展示规则']")
        self.assert_text('当前消费者端展示分数', "//span[contains(text(),'当前消费者端展示分数')]")
        self.assert_text('分数-星级对应', "//div[text()='分数-星级对应']")
        self.assert_text("展示规则", "//div[text()='展示规则']")
        self.click("//div[text()='展示规则']")
        self.sleep(0.5)
        self.assert_text("店铺首页", "//span[text()='店铺首页']")
        self.click("//span[text()='店铺详情']")
        self.assert_text("店铺详情", "//span[text()='店铺详情']")
        self.click("//span[text()='商品详情']")
        self.assert_text("商品详情", "//span[text()='商品详情']")
        self.click("//span[text()='搜索结果']")
        self.assert_text("搜索结果", "//span[text()='搜索结果']")
        self.click("//span[text()='直播间小黄车']")
        self.assert_text("直播间小黄车", "//span[text()='直播间小黄车']")
        self.click("//span[text()='提单页']")
        self.assert_text("提单页", "//span[text()='提单页']")
        self.click("//span[text()='直播间信息页']")
        self.assert_text("直播间信息页", "//span[text()='直播间信息页']")
        self.click("//span[text()='个人页']")
        self.assert_text("个人页", "//span[text()='个人页']")
        self.click("//span[text()='我知道了']")

    def test_shop_scene_switch(self):
        """
        消费者端展示规则-场景下拉切换
        """
        self.input_shopscorepage()

        # 消费者侧展示规则
        self.assert_text('消费者端展示规则', "//span[text()='消费者端展示规则']")
        self.assert_text('店铺首页', "//span[text()='店铺首页']")
        self.click("//span[text()='店铺首页']")
        self.sleep(1)
        self.assert_text("店铺详情", "//div[text()='店铺详情']")
        self.assert_text("商品详情", "//div[text()='商品详情']")
        self.assert_text("搜索结果", "//div[text()='搜索结果']")
        self.assert_text("直播间小黄车", "//div[text()='直播间小黄车']")
        self.assert_text("提单页", "//div[text()='提单页']")
        self.assert_text("直播间信息页", "//div[text()='直播间信息页']")
        self.assert_text("个人页", "//div[text()='个人页']")
        self.click("//div[text()='店铺详情']")
        self.sleep(1)

    def test_core_equity(self):
        """
        核心权益
        """
        self.input_shopscorepage()

        # 首页-核心指标
        self.assert_text("核心权益","//span[text()='核心权益']")
        self.assert_text("允许广告投放","//span[text()='允许广告投放']")
        self.assert_text("快分销入驻","//span[text()='快分销入驻']")
        self.assert_text("预售功能","//span[text()='预售功能']")

        #权益详情
        self.click("//span[text()='权益详情']")
        self.sleep(1)
        self.assert_text("店铺体验分应用场景及权益","//span[text()='店铺体验分应用场景及权益']")
        self.switch_to_window(0)
        self.sleep(1)


    def test_kwaishop_goods(self):
        """
        商品体验--二级页面跳转正常
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("权重占比35%", "//span[text()='权重占比35%']")

        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.click("//span[text()='商品品质退款率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)
        self.assert_text("商品品质退款率", "//div[text()='商品品质退款率']")
        self.switch_to_window(0)
        self.sleep(1)

        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.click("//span[text()='商品质量差评率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)
        self.assert_text("商品质量差评率", "//div[text()='商品质量差评率']")
        self.switch_to_window(0)
        self.sleep(1)

        self.assert_text("商品违规", "//span[text()='商品违规']")
        self.click("//span[text()='商品违规' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)
        self.assert_text("商品违规", "//div[text()='商品违规']")

    def test_kwaishop_logistics(self):
        """
        物流体验--二级页面跳转正常
        """
        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")
        self.assert_text("承诺发货揽收及时率", "//span[contains(text(),'承诺发货揽收及时率')]")
        self.click("//span[text()='承诺发货揽收及时率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.switch_to_window(0)
        self.sleep(1)
        self.assert_text("发签配送时长", "//span[text()='发签配送时长']")
        self.click("//span[text()='发签配送时长' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.switch_to_window(0)
        self.sleep(1)
        self.assert_text("物流负向投诉率", "//span[text()='物流负向投诉率']")
        self.click("//span[text()='物流负向投诉率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.switch_to_window(0)
        self.sleep(1)
        self.assert_text("物流违规", "//span[text()='物流违规']")
        self.click("//span[text()='物流违规' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("物流违规", "//div[text()='物流违规']")

    def test_kwaishop_Aftersale(self):
        """
        售后服务--二级页面跳转正常
        """
        self.input_shopscorepage()

        self.assert_text("售后服务", "//span[text()='售后服务']")
        self.assert_text("权重占比35%", "//span[text()='权重占比35%']")
        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.click("//span[text()='人工客服满意率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("人工客服满意率", "//div[text()='人工客服满意率']")
        self.switch_to_window(0)
        self.sleep(1)
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.click("//span[text()='售后处理时长达成率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("售后处理时长达成率", "//div[text()='售后处理时长达成率']")
        self.switch_to_window(0)
        self.sleep(1)
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.click("//span[text()='平台求助率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("平台求助率", "//div[text()='平台求助率']")
        self.switch_to_window(0)
        self.sleep(1)
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.click("//span[text()='服务工具（加分项）' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("服务工具（加分项）", "//div[text()='服务工具（加分项）']")
        self.click("//span[text()='我知道了']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='售后服务违规' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(1)
        self.assert_text("售后服务违规", "//div[text()='售后服务违规']")
        self.switch_to_window(0)
        self.sleep(1)


    def test_AfterSales_tools_details(self):
        """
        售后服务--加分工具--详情
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='服务工具（加分项）' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")

        self.sleep(1)
        self.assert_text("服务工具（加分项）", "//div[text()='服务工具（加分项）']")
        self.assert_text("退货补运费", "//span[text()='退货补运费']")
        self.assert_text("破损包退", "//span[text()='破损包退']")
        self.click("//span[text()='我知道了']")
        self.sleep(1)

    def test_goods_quality_standard(self):
        """
        商品体验--商品优质标准
        """
        self.input_shopscorepage()

        # 商品优质标准
        self.assert_text("商品优质标准", "//span[text()='商品优质标准']")
        self.assert_text("达成更高标准，可获得", "//span[text()='达成更高标准，可获得']")

        if self.is_element_visible('//span[text()="已达标"]'):
            self.click("//span[text()='商品优质标准' and @class='kope-text']/ancestor::div[1]/following-sibling::div//*[contains(text(),'达标')]")
            self.sleep(0.5)
        else:
           self.is_element_visible("//span[text()='未达标']")
           self.click("//span[text()='商品优质标准' and @class='kope-text']/ancestor::div[2]/following-sibling::div//*[contains(text(),'未达标')]")
           self.sleep(0.5)
        self.assert_text("优质标准", "//div[text()='优质标准']")
        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")
        self.click("//span[text()='我知道了']")

    def test_kwaishop_qualityrefund_formula(self):
        """
        商品体验二级页面--商品品质退款率--公式
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品品质退款率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 商品品质退款率 二级页面
        self.assert_text("商品质量差评率", "//div[text()='商品质量差评率']")
        self.click("//div[text()='商品质量差评率']")
        self.assert_text("商品违规", "//div[text()='商品违规']")
        self.click("//div[text()='商品违规']")
        self.assert_text("商品品质退款率", "//div[text()='商品品质退款率']")
        self.click("//div[text()='商品品质退款率']")

        # 公式
        self.assert_text("商品品质退款率", "//div[text()='商品品质退款率']")
        self.assert_element_visible("//div[@class='kpro-data-formula-symbol-item']//div//*[name()='svg']//*[name()='rect']")
        self.assert_text("支付且签收的订单中因商品品质原因申请退款的订单量",
                         "//div[(text()='支付且签收的订单中因商品品质原因申请退款的订单量')]")
        self.assert_text("÷", "//div[contains(text(),'÷')]")
        self.assert_text("支付且签收的订单量",
                         "//div[(text()='支付且签收的订单量')]")

    def test_kwaishop_qualityrefund_diagnosis(self):
        """
        商品体验二级页--商品品质退款率--分析诊断
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品品质退款率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        #分析诊断
        self.assert_element_visible("//span[@class='titleText___QcSHG']")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")
        self.assert_text("退款原因分布（近30天）", "//span[contains(text(),'退款原因分布（近30天）')]")


    def test_kwaishop_qualityrefund_details(self):
        """
        商品体验二级页--商品品质退款率--详情
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品品质退款率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # Top商品品退详情
        self.assert_text("Top商品品退详情","//div[text()='Top商品品退详情']")
        self.assert_text("订单详情", "//div[text()='订单详情']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条","//div[contains(text(),'显示考核周期（近30天）的数据，最多显示200条')]")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("商品所属类目", "//th[text()='商品所属类目']")
        self.assert_text("品退订单数", "//th[text()='品退订单数']")
        self.assert_text("品退贡献度", "//th[text()='品退贡献度']")
        self.assert_text("Top品退原因", "//th[text()='Top品退原因']")

        # 订单详情
        self.click("//div[text()='订单详情']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条",
                         "//div[contains(text(),'显示考核周期（近30天）的数据，最多显示200条')]")
        self.assert_text("订单ID", "//th[text()='订单ID']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("买家ID", "//th[text()='买家ID']")
        self.assert_text("带货达人昵称", "//th[text()='带货达人昵称']")
        self.assert_text("品退原因", "//th[text()='品退原因']")


    def test_kwaishop_qualitybad_formula(self):
        """
        商品体验二级页面--商品质量差评率--公式
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品质量差评率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 公式
        self.assert_text("商品质量差评率", "//div[text()='商品质量差评率']")
        self.assert_element_visible("//div[@class='kpro-data-formula-symbol-item']//div//*[name()='svg']")
        self.assert_text("支付且签收的订单中商品质量评分<=2星的订单量", "//div[text()='支付且签收的订单中商品质量评分<=2星的订单量']")
        self.assert_text("支付且签收的订单量", "//div[text()='支付且签收的订单量']")
        self.assert_text("÷", "//div[text()='÷']")


    def test_kwaishop_qualitybad_diagnosis(self):
        """
        商品体验纬度二级页面--商品质量差评率--诊断
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品质量差评率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 分析诊断
        self.assert_element_visible("//span[@class='titleText___QcSHG']")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")

    def test_kwaishop_qualitybad_details(self):
        """
        商品体验纬度二级页面--商品质量差评率--详情
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品质量差评率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 商品质量差评详情
        self.assert_text("商品质量差评率", "//div[text()='商品质量差评率']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条",
                         "//div[contains(text(),'显示考核周期（近30天）的数据，最多显示200条')]")
        self.assert_text("订单详情", "//div[text()='订单详情']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("商品所属类目", "//th[text()='商品所属类目']")
        self.assert_text("质量差评数", "//th[text()='质量差评数']")
        self.assert_text("差评贡献度", "//th[text()='差评贡献度']")
        self.assert_text("物流签收订单量", "//th[text()='物流签收订单量']")
        self.assert_text("操作", "//th[text()='操作']")
        self.assert_text("评价管理", "//span[text()='评价管理']")
        self.click("//span[text()='评价管理']")
        self.sleep(1)
        self.assert_text("评价数据", "//div[text()='评价数据']")
        self.assert_text("评价管理", "//div[text()='评价管理']")
        self.assert_text("详细数据", "//div[text()='详细数据']")
        self.switch_to_window(1)
        self.sleep(1)

        # 订单详情
        self.click("//div[@id='rc-tabs-0-tab-1']")
        self.sleep(1)
        self.assert_text("订单ID", "//th[text()='订单ID']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("带货达人昵称", "//th[text()='带货达人昵称']")
        self.assert_text("操作", "//th[text()='操作']")
        self.assert_text("评价管理", "//span[text()='评价管理']")
        self.click("//span[text()='评价管理']")
        self.sleep(1)
        self.assert_text("评价数据", "//div[text()='评价数据']")
        self.assert_text("评价管理", "//div[text()='评价管理']")
        self.assert_text("详细数据", "//div[text()='详细数据']")
        self.switch_to_window(1)

        # # 类目标准
        # self.click("//div[@id='rc-tabs-0-tab-2']")
        # self.sleep(1)
        # self.assert_text("类目", "//th[text()='类目']")
        # self.assert_text("订单数", "//th[text()='订单数']")
        # self.assert_text("差评订单数", "//th[text()='差评订单数']")
        # self.assert_text("该类目当前值", "//th[text()='该类目当前值']")
        # self.assert_text("优秀标准", "//th[text()='优秀标准']")
        # self.assert_text("良好标准", "//th[text()='良好标准']")


    def test_kwaishop_Product_violation(self):
        """
        商品体验纬度二级页面--商品违规
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.click("//span[text()='商品违规' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")

        self.sleep(2)

        logging.info("进入商品违规二级页")
        #公式+详情
        self.assert_text("商品违规", "//div[text()='商品违规']")
        self.assert_text("违规积分", "//div[text()='违规积分']")
        self.assert_text("商品违规记录", "//div[text()='商品违规记录']")
        self.assert_text("(仅计入近30天的罚单，次日更新)最多显示200条", "//div[text()='(仅计入近30天的罚单，次日更新)最多显示200条']")

    def test_kwaishop_goodshover(self):
        """
        商品体验--hover文案
        """
        self.input_shopscorepage()

        self.assert_text("商品体验", "//span[text()='商品体验']")
        self.assert_text("商品品质退款率", "//span[text()='商品品质退款率']")
        self.assert_text("商品质量差评率", "//span[text()='商品质量差评率']")
        self.assert_text("商品违规", "//span[text()='商品违规']")

        self.hover("//span[contains(text(),'商品品质退款率')]")
        self.click("//span[contains(text(),'查看更多规则')]")
        self.assert_text("【商品体验】商品品质退款率", "//span[text()='【商品体验】商品品质退款率']")
        self.switch_to_window(0)
        self.sleep(1)

        self.hover("//span[contains(text(),'商品质量差评率')]")
        self.click("(//span[contains(@role,'button')][contains(text(),'查看更多规则')])[2]")
        self.assert_text("【商品体验】商品质量差评率", "//span[text()='【商品体验】商品质量差评率']")
        self.switch_to_window(0)
        self.sleep(1)

        self.hover("//span[contains(text(),'商品违规')]")
        self.click("(//span[contains(@role,'button')][contains(text(),'查看更多规则')])[3]")
        self.assert_text("店铺体验分-商品体验罚单场景", "//span[text()='店铺体验分-商品体验罚单场景']")
        self.switch_to_window(0)
        self.sleep(1)


    def test_kwaishop_logistics_timely_formula(self):
        """
        物流体验二级页--承诺发货揽收及时率--公式
        """
        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.click("//span[text()='承诺发货揽收及时率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 公式
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.assert_element_visible("//div[@class='kpro-data-formula-symbol-item']//div//*[name()='svg']")
        self.assert_text("承诺发货时效内发出且在规定时效内揽收的订单量", "//div[text()='承诺发货时效内发出且在规定时效内揽收的订单量']")
        self.assert_text("÷","//div[contains(text(),'÷')]")
        self.assert_text("应揽收订单量", "//div[text()='应揽收订单量']")

    def test_logistics_timely_diagnosis(self):
        """
        物流体验二级页--承诺发货揽收及时率--分析诊断
        """
        self.input_shopscorepage()

        self.click("//span[text()='承诺发货揽收及时率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")

        self.sleep(2)

        # 物流体验二级页
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")

        # 分析诊断
        self.assert_text('分析诊断',"//span[contains(text(),'分析诊断')]")
        self.assert_text("指标变化分析","//span[contains(text(),'指标变化分析')]")
        self.assert_text("不同发货时效占比（近30天）","//span[contains(text(),'不同发货时效占比（近30天）')]")


    def test_logistics_timely_details(self):
        """
        物流体验二级页--承诺发货揽收及时率--详情
        """
        self.input_shopscorepage()

        self.click("//span[text()='承诺发货揽收及时率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)
        # 物流体验二级页
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")

        # 发货超时订单详情
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条", "//div[text()='显示考核周期（近30天）的数据，最多显示200条']")
        self.assert_text("发货超时订单详情", "//div[text()='发货超时订单详情']")
        # self.assert_text("订单ID", "//th[text()='订单ID']")
        # self.assert_text("承诺发货时间", "//th[text()='承诺发货时间']")
        # self.assert_text("实际发货时间", "//th[text()='实际发货时间']")
        # self.assert_text("超时时长(小时)", "//th[text()='超时时长(小时)']")

        # 揽收超时订单详情
        self.click("//div[text()='揽收超时订单详情']")
        self.assert_text("揽收超时订单详情", "//div[text()='揽收超时订单详情']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条", "//div[text()='显示考核周期（近30天）的数据，最多显示200条']")
        self.assert_text("订单ID", "//th[text()='订单ID']")
        self.assert_text("应揽收时间", "//th[text()='应揽收时间']")
        self.assert_text("实际揽收时间", "//th[text()='实际揽收时间']")
        self.assert_text("超时时长(小时)", "//th[text()='超时时长(小时)']")


    def test_logistics_delivery_formula(self):
        """
        物流体验二级页--发签配送时长--公式
        """
        self.input_shopscorepage()

        self.click("//span[text()='发签配送时长' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")

        # 公式
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.assert_text("签收运单配送时长之和", "//div[text()='签收运单配送时长之和']")
        self.assert_text("签收运单量", "//div[text()='签收运单量']")
        self.assert_text("÷", "//div[text()='÷']")

        # 详情规则
        self.click("//div[contains(text(),'详细规则')]")
        self.assert_text("时长优质标准保护", "//div[text()='时长优质标准保护']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.assert_text("线路达成率", "//div[text()='线路达成率']")
        # 未达成路线明细
        self.assert_text("未达成路线明细", "//div[text()='未达成路线明细']")
        self.assert_text("运单ID", "//th[text()='运单ID']")
        self.assert_text("运单线路", "//th[text()='运单线路']")
        self.assert_text("标准时效", "//th[text()='标准时效']")
        self.assert_text("揽收时间", "//th[text()='揽收时间']")
        self.assert_text("实际揽收考核开始时间", "//th[text()='实际揽收考核开始时间']")
        self.assert_text("签收时间", "//th[text()='签收时间']")
        self.assert_text("超时时长", "//th[text()='超时时长']")
        self.click("//span[contains(text(),'知道了')]")

    def test_logistics_delivery_diagnosis(self):
        """
        物流体验二级页--发签配送时长--分析诊断
        """
        self.input_shopscorepage()

        self.click("//span[text()='发签配送时长' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")

        # 分析诊断
        self.assert_text('分析诊断', "//span[contains(text(),'分析诊断')]")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")
        self.assert_text("发签配送时长表现（近30天）", "//span[contains(text(),'发签配送时长表现（近30天）')]")
        self.assert_text('不同发货-签收时效订单占比', "//div[contains(text(),'不同发货-签收时效订单占比')]")
        self.assert_text("Top揽收-签收未达成线路", "//div[contains(text(),'Top揽收-签收未达成线路')]")
        self.click("//div[contains(text(),'Top揽收-签收未达成线路')]")

    def test_logistics_delivery_details(self):
        """
        物流体验二级页--发签配送时长--详情
        """
        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.click("//span[text()='发签配送时长' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")

        # 物流分析详情
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条", "//div[text()='显示考核周期（近30天）的数据，最多显示200条']")
        self.assert_text("物流分析详情", "//div[text()='物流分析详情']")
        self.assert_text("快递公司名称", "//th[text()='快递公司名称']")
        self.assert_element("//th[@class='kwaishop-seller-store-score-pc-table-cell']")
        self.assert_text("发货量", "//th[text()='发货量']")
        self.assert_text("发货量占比", "//th[text()='发货量占比']")

        # 订单详情
        self.click("//div[text()='订单详情']")
        self.assert_text("运单ID", "//th[text()='运单ID']")
        self.assert_text("发货时间", "//th[text()='发货时间']")
        self.assert_text("签收时间", "//th[text()='签收时间']")
        self.assert_text("配送时长（小时）", "//th[text()='配送时长（小时）']")

        # 配送时长异常商品明细
        self.click("//div[text()='配送时长异常商品明细']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("异常线路", "//th[text()='异常线路']")
        self.assert_text("异常订单量", "//th[text()='异常订单量']")
        self.assert_text("异常订单平均配送时长（小时）", "//th[text()='异常订单平均配送时长（小时）']")


    def test_logistics_complaint_formula(self):
        """
        物流体验二级页--物流负向投诉率--公式
        """
        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.click("//span[text()='物流负向投诉率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")

        # 公式
        self.assert_text("物流负向投诉率","//div[text()='物流负向投诉率']")
        self.assert_text("物流支付订单中产生物流问题负反馈订单数", "//div[text()='物流支付订单中产生物流问题负反馈订单数']")
        self.assert_text("支付订单量", "//div[text()='支付订单量']")
        self.assert_text("÷", "//div[text()='÷']")

    def test_logistics_complaint_diagnosis(self):
        """
        物流体验二级页--物流负向投诉率--分析诊断
        """

        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")
        self.click("//span[text()='物流负向投诉率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")

        # 分析诊断
        self.assert_text('分析诊断', "//span[contains(text(),'分析诊断')]")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")

    def test_logistics_complaint_details(self):
        """
       物流体验二级页--物流负向投诉率--详情
       """
        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")
        self.click("//span[text()='物流负向投诉率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")

        # Top物流负反馈商品详情
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条","//div[text()='显示考核周期（近30天）的数据，最多显示200条']")
        self.assert_text("Top物流负反馈商品详情", "//div[text()='Top物流负反馈商品详情']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("商品所属类目", "//th[text()='商品所属类目']")
        self.assert_text("物流负反馈量", "//th[text()='物流负反馈量']")
        self.assert_text("物流负反馈贡献度", "//th[text()='物流负反馈贡献度']")


    def test_Logistics_violations(self):
        """
        物流体验二级页--物流违规
        """
        self.input_shopscorepage()

        self.assert_text("物流体验", "//span[text()='物流体验']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")
        self.click("//span[text()='物流违规' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 物流体验二级页
        self.assert_text("物流违规", "//div[text()='物流违规']")
        self.click("//div[text()='承诺发货揽收及时率']")
        self.assert_text("承诺发货揽收及时率", "//div[text()='承诺发货揽收及时率']")
        self.click("//div[text()='发签配送时长']")
        self.assert_text("发签配送时长", "//div[text()='发签配送时长']")
        self.click("//div[text()='物流负向投诉率']")
        self.assert_text("物流负向投诉率", "//div[text()='物流负向投诉率']")
        self.click("//div[text()='物流违规']")

        self.assert_text("物流违规","//div[text()='物流违规']")
        self.assert_text("违规积分","//div[text()='违规积分']")
        self.assert_text("物流违规记录","//div[text()='物流违规记录']")
        self.assert_text("(仅计入近30天的罚单，次日更新)最多显示200条",'//div[text()="(仅计入近30天的罚单，次日更新)最多显示200条"]')

    def test_aftersales_service_formula(self):
        """
        售后服务二级页-人工客服满意率-公式
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='人工客服满意率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        self.assert_text("人工客服满意率", "//div[text()='人工客服满意率']")
        self.is_element_visible("//div[@class='kpro-data-formula-symbol-item']//div//*[name()='svg']")
        self.assert_text("获得好评的人工客服会话数", "//div[text()='获得好评的人工客服会话数']")
        self.is_element_visible("//div[contains(text(),'÷')]")
        self.assert_text("有评价的人工客服会话数", "//div[text()='有评价的人工客服会话数']")


    def test_aftersales_service_diagnosis(self):
        """
        售后服务二级页-人工客服满意率-诊断
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='人工客服满意率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        self.assert_text('分析诊断', "//span[contains(text(),'分析诊断')]")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")

    def test_aftersales_service_details(self):
        """
        售后服务二级页-人工客服满意率-详情
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='人工客服满意率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 客服详情
        self.assert_text("客服详情", "//div[text()='客服详情']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条", "//div[text()='显示考核周期（近30天）的数据，最多显示200条']")

        self.assert_text("客服名称-子账号名", "//th[text()='客服名称-子账号名']")
        self.assert_text("客服ID", "//th[text()='客服ID']")
        self.assert_text("满意率", "//th[text()='满意率']")
        self.assert_text("评价总数", "//th[text()='评价总数']")
        self.assert_text("不满意评价数", "//th[text()='不满意评价数']")
        self.assert_text("操作", "//th[text()='操作']")


        # Top物流负反馈商品详情
        self.assert_text("不满意会话详情", "//div[text()='不满意会话详情']")
        self.click("//div[text()='不满意会话详情']")

        self.assert_text("会话ID", "//th[text()='会话ID']")
        self.assert_text("咨询人", "//th[text()='咨询人']")
        self.assert_text("咨询人ID", "//th[text()='咨询人ID']")
        self.assert_text("客服名称-子账号名", "//th[text()='客服名称-子账号名']")

    def test_aftersales_achieve_formula(self):
        """
        售后服务二级页-售后处理时长达成率-公式
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='售后处理时长达成率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        self.assert_text("售后处理时长达成率", "//div[text()='售后处理时长达成率']")
        self.is_element_visible("//div[@class='kpro-data-formula-symbol-item']//div//*[name()='svg']")
        self.assert_text("达成售后处理时长要求的退款成功售后单数", "//div[text()='达成售后处理时长要求的退款成功售后单数']")
        self.is_element_visible("//div[contains(text(),'÷')]")
        self.assert_text("退款成功售后单数", "//div[text()='退款成功售后单数']")


    def test_aftersales_achieve_diagnosis(self):
        """
        售后服务二级页-售后处理时长达成率-诊断
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='售后处理时长达成率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        self.assert_text('分析诊断', "//span[contains(text(),'分析诊断')]")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")
        self.assert_text("退款时长分布（近30天）", "//span[text()='退款时长分布（近30天）']")
        self.assert_text("发货后仅退款时长分布", "//div[text()='发货后仅退款时长分布']")
        self.assert_text("发货后仅退款时长分布", "//div[text()='发货后仅退款时长分布']")
        self.click("//div[text()='退货退款时长分布']")

    def test_aftersales_achieve_details(self):
        """
        售后服务二级页-售后处理时长达成率-详情
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='售后处理时长达成率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 未达成发货后仅退款完结时长商品详情
        self.assert_text("未达成发货后仅退款完结时长商品详情", "//div[text()='未达成发货后仅退款完结时长商品详情']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条", "//div[text()='显示考核周期（近30天）的数据，最多显示200条']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("所属类目", "//th[text()='所属类目']")
        self.assert_text("达成率", "//th[text()='达成率']")
        self.assert_text("涉及单量", "//th[text()='涉及单量']")
        self.assert_text("未达成单量", "//th[text()='未达成单量']")
        self.assert_text("未达成贡献度", "//th[text()='未达成贡献度']")
        self.assert_text("Top仅退款原因", "//th[text()='Top仅退款原因']")

        # 未达成发货后仅退款完结时长订单详情
        self.assert_text("未达成发货后仅退款完结时长订单详情", "//div[text()='未达成发货后仅退款完结时长订单详情']")
        self.click("//div[text()='未达成发货后仅退款完结时长订单详情']")

        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("订单ID", "//th[text()='订单ID']")
        self.assert_text("退款单号", "//th[text()='退款单号']")
        self.assert_text("仅退款完结时长（小时）", "//th[text()='仅退款完结时长（小时）']")
        self.assert_text("达成门槛（小时）", "//th[text()='达成门槛（小时）']")

        # 未达成退货退款完结时长商品详情
        self.assert_text("未达成退货退款完结时长商品详情", "//div[text()='未达成退货退款完结时长商品详情']")
        self.click("//div[text()='未达成退货退款完结时长商品详情']")

        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("所属类目", "//th[text()='所属类目']")
        self.assert_text("达成率", "//th[text()='达成率']")
        self.assert_text("涉及单量", "//th[text()='涉及单量']")
        self.assert_text("未达成贡献度", "//th[text()='未达成贡献度']")
        self.assert_text("Top退货退款原因", "//th[text()='Top退货退款原因']")

        # 未达成退货退款完结时长订单详情
        self.assert_text("未达成退货退款完结时长订单详情", "//div[text()='未达成退货退款完结时长订单详情']")
        self.click("//div[text()='未达成退货退款完结时长订单详情']")

        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("订单ID", "//th[text()='订单ID']")
        self.assert_text("退款单号", "//th[text()='退款单号']")
        self.assert_text("退货退款完结时长（小时）", "//th[text()='退货退款完结时长（小时）']")
        self.assert_text("达成门槛（小时）", "//th[text()='达成门槛（小时）']")

    def test_AfterSales_recourse_formula(self):
        """
        售后服务二级页--平台求助--公式
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='平台求助率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        self.assert_text("平台求助率", "//div[text()='平台求助率']")
        self.assert_text("支付订单中产生转平台服务、商责纠纷、被平台接管的问题订单数", "//div[text()='支付订单中产生转平台服务、商责纠纷、被平台接管的问题订单数']")
        self.assert_text("支付订单数", "//div[text()='支付订单数']")
        self.assert_text("÷", "//div[text()='÷']")

    def test_AfterSales_recourse_diagonsis(self):
        """
        售后服务二级页--平台求助--诊断
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='平台求助率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 分析诊断
        self.assert_text('分析诊断', "//span[contains(text(),'分析诊断')]")
        self.assert_text("指标变化分析", "//span[contains(text(),'指标变化分析')]")

    def test_AfterSales_recourse_details(self):
        """
        售后服务二级页--平台求助--详情
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='平台求助率' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)

        # 求助商品详情
        self.assert_text("求助商品详情", "//div[text()='求助商品详情']")
        self.assert_text("显示考核周期（近30天）的数据，最多显示200条", "//div[text()='显示考核周期（近30天）的数据，最多显示200条']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("所属类目", "//th[text()='所属类目']")
        self.assert_text("涉及求助量", "//th[text()='涉及求助量']")
        self.assert_text("平台求助率贡献度", "//th[text()='平台求助率贡献度']")
        self.assert_text("Top售后原因", "//th[text()='Top售后原因']")

        # 订单详情
        self.assert_text("订单详情", "//div[text()='订单详情']")
        self.click("//div[text()='订单详情']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("订单ID", "//th[text()='订单ID']")
        self.assert_text("求助类型", "//th[text()='求助类型']")

    def test_test_AfterSales_Violation(self):
        """
        售后服务二级页--售后服务违规
        """
        self.input_shopscorepage()

        self.assert_text("人工客服满意率", "//span[text()='人工客服满意率']")
        self.assert_text("售后处理时长达成率", "//span[text()='售后处理时长达成率']")
        self.assert_text("平台求助率", "//span[text()='平台求助率']")
        self.assert_text("服务工具（加分项）", "//span[text()='服务工具（加分项）']")
        self.assert_text("售后服务违规", "//span[text()='售后服务违规']")
        self.click("//span[text()='售后服务违规' and @class='kope-text kope-text-inherit kope-text--overflow-hidden kope-text--singleline']/ancestor::div[4]/following-sibling::div//*[contains(text(),'详情')]")
        self.sleep(2)


        # 公式
        self.assert_text("售后服务违规", "//div[text()='售后服务违规']")
        self.assert_text("违规积分", "//div[text()='违规积分']")

        # 售后服务违规记录
        self.assert_text("售后服务违规", "//div[text()='售后服务违规']")
        self.assert_text("(仅计入近30天的罚单，次日更新)最多显示200条", "//div[text()='(仅计入近30天的罚单，次日更新)最多显示200条']")

