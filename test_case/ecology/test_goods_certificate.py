"""
des: 商品-商品资质管理 PC
author: zhouchang03
date: 2023/06/25
"""
import time
import pytest

from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p2
class GoodsCertificate(BaseTestCase):

    def test_goods_qualifications(self):
        """
        商品-进入商品资质管理首页
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()
        # 导航栏
        self.assert_text("商品", "//div[text()='商品']")
        self.assert_text("商品资质管理", "//span[text()='商品资质管理']")
        self.click("//span[text()='商品资质管理']")
        self.sleep(2)
        self.close_merchant_assistant()


    def test_goods_qualifications_homepage(self):
        """
        商品资质首页
        """
        self.test_goods_qualifications()
        self.sleep(2)

        # 首页信息
        self.assert_text("资质库", "//div[text()='资质库']")
        self.assert_text("资质元名称", "//label[contains(text(),'资质元名称')]")
        self.assert_text("资质审核状态", "//label[contains(text(),'资质审核状态')]")
        self.assert_text("资质分类", "//label[contains(text(),'资质分类')]")
        self.assert_text("资质状态", "//label[contains(text(),'资质状态')]")
        self.assert_text("商品ID", "//label[contains(text(),'商品ID')]")
        self.assert_text("重 置", "//span[contains(text(),'重 置')]")
        self.assert_text("查 询", "//span[contains(text(),'查 询')]")

        # 首页列表
        self.assert_text("资质ID", "//th[contains(text(),'资质ID')]")
        self.assert_text("资质分类", "//th[contains(text(),'资质分类')]")
        self.assert_text("资质元名称", "//th[contains(text(),'资质元名称')]")
        self.assert_text("资质证号", "//th[contains(text(),'资质证号')]")
        self.assert_text("资质状态", "//th[contains(text(),'资质状态')]")
        self.assert_text("已关联商品数量", "//th[contains(text(),'已关联商品数量')]")
        self.assert_text("资质审核状态", "//th[contains(text(),'资质审核状态')]")
        self.assert_text("资质更新时间", "//th[contains(text(),'资质更新时间')]")


    def test_goods_qualifications_details(self):
        """
        商品资质首页-详情
        """
        self.test_goods_qualifications()
        self.sleep(2)

        # 查看资质详情
        self.assert_text("详情", "//span[contains(text(),'详情')]")
        self.click("//span[contains(text(),'详情')]")
        self.sleep(2)

        # 基础信息
        self.assert_text("资质详情", "//div[contains(text(),'资质详情')]")
        self.assert_text("基础信息", "//div[contains(text(),'基础信息')]")
        self.assert_text("资质审核状态", "//span[contains(text(),'资质审核状态')]")
        self.assert_text("资质更新时间", "//span[contains(text(),'资质更新时间')]")
        self.assert_text("资质状态", "//span[contains(text(),'资质状态')]")

        # 资质元信息
        self.assert_text("资质元信息", "//div[contains(text(),'资质元信息')]")
        self.assert_text("资质元名称", "//span[contains(text(),'资质元名称')]")
        self.assert_text("资质分类", "//span[contains(text(),'资质分类')]")
        self.assert_text("是否共享", "//span[contains(text(),'是否共享')]")
        self.assert_text("校验官网", "//span[contains(text(),'校验官网')]")

        # 资质内容
        self.assert_text("资质内容", "//div[contains(text(),'资质内容')]")
        self.assert_text("资质文件", "//span[contains(text(),'资质文件')]")
        self.assert_text("资质开始时间", "//span[contains(text(),'资质开始时间')]")
        self.assert_text("资质结束时间", "//span[contains(text(),'资质结束时间')]")
        self.assert_text("备注", "//span[contains(text(),'备注')]")

        # 关联情况
        self.assert_text("商品ID", "//th[contains(text(),'商品ID')]")
        self.assert_text("商品名称", "//th[contains(text(),'商品名称')]")

