"""
des: pc页面切换
author: wb_qiangwenhui
date: 2025/01/02
"""

import pytest
import time
from ddt import ddt

from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.skip  #工作台改版，先skip
class SwitchPage(BaseTestCase):
    def input_report(self):
        """ """
        self.maximize_window()
        self.login("WB_DOMAIN", "themis")
        time.sleep(1)


    def test_guarantee_risk_center(self):
        """ 保障>>风险处理中心>>违规单管理 """
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心","//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 违约单管理
        self.assert_text("违约单管理","//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(0.5)
        self.assert_text("电商违规","//div[text()='电商违规']")
        self.assert_text("社区违规","//div[text()='社区违规']")

        # 切换风险处理中心
        self.click("//span[text()='风险处理中心']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")


    def test_guarantee_Violation_manage(self):
        """ 保障>>违规单管理>>管控单管理 """
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心","//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 违约单管理
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(0.5)
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("社区违规", "//div[text()='社区违规']")

        # 管控单管理
        self.assert_text("管控单管理", "//span[text()='管控单管理']")
        self.click("//span[text()='管控单管理']")
        self.assert_text("管控单管理", "//div[text()='管控单管理']")

        # 切回违约单管理
        self.click("//span[text()='违约单管理']")
        self.sleep(0.5)
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("社区违规", "//div[text()='社区违规']")

    def test_guarantee_Control_manage(self):
        """ 保障>>管控单管理>>赔付单管理 """
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 管控单管理
        self.assert_text("管控单管理", "//span[text()='管控单管理']")
        self.click("//span[text()='管控单管理']")
        self.sleep(0.5)
        self.assert_text("管控单管理", "//div[text()='管控单管理']")

        # 赔付单管理
        self.assert_text("赔付单管理", "//span[text()='赔付单管理']")
        self.click("//span[text()='赔付单管理']")
        self.sleep(0.5)
        self.assert_text("赔付单管理", "//div[text()='赔付单管理']")

        # 切回管控单管理
        self.assert_text("管控单管理", "//span[text()='管控单管理']")
        self.click("//span[text()='管控单管理']")
        self.sleep(0.5)
        self.assert_text("管控单管理", "//div[text()='管控单管理']")

    def test_guarantee_compensation_manage(self):
        """ 保障>>赔付单管理>>平台违规预警 """
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 赔付单管理
        self.assert_text("赔付单管理", "//span[text()='赔付单管理']")
        self.click("//span[text()='赔付单管理']")
        self.sleep(0.5)
        self.assert_text("赔付单管理", "//div[text()='赔付单管理']")

        # 平台违规预警
        self.assert_text("平台违规预警", "//span[text()='平台违规预警']")
        self.click("//span[text()='平台违规预警']")
        self.sleep(0.5)
        self.assert_text("违规预警", "//div[text()='违规预警']")
        self.assert_text("风险诊断预警", "//div[text()='风险诊断预警']")

        #切换赔付单管理
        self.assert_text("赔付单管理", "//span[text()='赔付单管理']")
        self.click("//span[text()='赔付单管理']")
        self.sleep(0.5)
        self.assert_text("赔付单管理", "//div[text()='赔付单管理']")

    def test_guarantee_early_warning(self):
        """ 保障>>平台违规预警>>经营风险管理 """
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 平台违规预警
        self.assert_text("平台违规预警", "//span[text()='平台违规预警']")
        self.click("//span[text()='平台违规预警']")
        self.sleep(0.5)
        self.assert_text("违规预警", "//div[text()='违规预警']")
        self.assert_text("风险诊断预警", "//div[text()='风险诊断预警']")

        # 经营风险管理
        self.assert_text("经营风险管理", "//span[text()='经营风险管理']")
        self.click("//span[text()='经营风险管理']")
        self.sleep(0.5)
        self.assert_text("经营风险预警与前置防控", "//div[text()='经营风险预警与前置防控']")
        self.assert_text("经营风险预警", "//span[text()='经营风险预警']")

        # 切回平台违规预警
        self.assert_text("平台违规预警", "//span[text()='平台违规预警']")
        self.click("//span[text()='平台违规预警']")
        self.sleep(0.5)
        self.assert_text("违规预警", "//div[text()='违规预警']")
        self.assert_text("风险诊断预警", "//div[text()='风险诊断预警']")


    def test_guarantee_operate_risk_manage(self):
        """ 保障>>经营风险管理>>举报中心 """
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 经营风险管理
        self.assert_text("经营风险管理", "//span[text()='经营风险管理']")
        self.click("//span[text()='经营风险管理']")
        self.sleep(0.5)
        self.assert_text("经营风险预警与前置防控", "//div[text()='经营风险预警与前置防控']")
        self.assert_text("经营风险预警", "//span[text()='经营风险预警']")

        # 举报中心
        self.assert_text("举报中心", "//span[text()='举报中心']")
        self.click("//span[text()='举报中心']")
        self.sleep(0.5)
        self.assert_text("举报中心", "//span[text()='举报中心']")

        # 切回经营风险管理
        self.assert_text("经营风险管理", "//span[text()='经营风险管理']")
        self.click("//span[text()='经营风险管理']")
        self.sleep(0.5)
        self.assert_text("经营风险预警与前置防控", "//div[text()='经营风险预警与前置防控']")
        self.assert_text("经营风险预警", "//span[text()='经营风险预警']")

    def test_guarantee_report_center(self):
        """ 保障>>举报中心>>申诉中心"""
        self.input_report()

        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 举报中心
        self.assert_text("举报中心", "//span[text()='举报中心']")
        self.click("//span[text()='举报中心']")
        self.sleep(0.5)
        self.assert_text("举报中心", "//span[text()='举报中心']")

        # 申诉中心
        self.assert_text("申诉中心", "//span[text()='申诉中心']")
        self.click("//span[text()='申诉中心']")
        self.sleep(0.5)
        self.assert_text("申诉中心", "//span[text()='申诉中心']")

        # 切回举报中心
        self.assert_text("举报中心", "//span[text()='举报中心']")
        self.click("//span[text()='举报中心']")
        self.sleep(0.5)
        self.assert_text("举报中心", "//span[text()='举报中心']")

    def test_guarantee_appeal_center(self):
        """ 保障>>申诉中心>>报备中心"""
        self.input_report()
        # 导航栏
        self.assert_text("保障", "//span[text()='保障']")
        self.click("//span[text()='保障']")
        self.sleep(0.5)
        self.assert_text("风险处理中心", "//span[text()='风险处理中心']")
        self.assert_text("待处理违规事项", "//span[text()='待处理违规事项']")
        self.assert_text("待处理保障事项", "//span[text()='待处理保障事项']")

        # 申诉中心
        self.assert_text("申诉中心", "//span[text()='申诉中心']")
        self.click("//span[text()='申诉中心']")
        self.sleep(0.5)
        self.assert_text("申诉中心", "//span[text()='申诉中心']")

        # 报备中心
        self.assert_text("报备中心", "//span[text()='报备中心']")
        self.click("//span[text()='报备中心']")
        self.sleep(0.5)
        self.assert_text("数据概览", "//span[text()='数据概览']")
        self.assert_text("发起报备", "//span[text()='发起报备']")
        self.assert_text("延迟发货报备记录", "//div[text()='延迟发货报备记录']")

        # 切回申诉中心
        self.assert_text("申诉中心", "//span[text()='申诉中心']")
        self.click("//span[text()='申诉中心']")
        self.sleep(0.5)
        self.assert_text("申诉中心", "//span[text()='申诉中心']")