"""
des: 保障-预警中心 PC
author: zhouchang03
date: 2023/07/02
"""
import time
import pytest

from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p1
class Remind(BaseTestCase):
    def input_remind(self):
        """ 进入保障-预警中心"""
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("平台违规预警", "//span[text()='平台违规预警']")
        self.click("//span[text()='平台违规预警']")
        self.sleep(2)

    @pytest.mark.skip
    def test_homepage(self):
        """ 保障-预警中心"""
        self.input_remind()

        # 首页信息
        self.assert_element_visible("//div[@title='店铺类 0']")
        self.assert_element_visible("//div[@title='商品类 0']")
        self.assert_element_visible("//div[@title='订单类 0']")
        self.assert_element_visible("//div[@title='行为类 0']")
        self.assert_element_visible("//div[@title='直播类 0']")

        # 点击
        self.click("//div[@id='rc-tabs-0-tab-1']")
        self.click("//div[@id='rc-tabs-0-tab-2']")

    def test_risk_remind(self):
        """ 保障-预警中心-风险诊断预警"""
        self.input_remind()

        # 点击风险诊断预警tab
        self.click("//div[2]/div[@role='tab']")

        # 首页信息
        self.assert_element_visible("//div[@title='店铺类 0']")
        self.assert_element_visible("//div[@title='商品类 0']")
        self.assert_element_visible("//div[@title='订单类 0']")
        self.assert_element_visible("//div[@title='行为类 0']")
        self.assert_element_visible("//div[@title='直播类 0']")

        # 筛选项-预警单号 预警标题 预警状态 关联罚单编号 预警开始时间 按钮-重 置 查 询
        self.assert_text("预警单号", "//label[contains(text(),'预警单号')]")
        self.assert_text("预警标题", "//label[contains(text(),'预警标题')]")
        self.assert_text("预警状态", "//label[contains(text(),'预警状态')]")
        self.assert_text("关联罚单编号", "//label[contains(text(),'关联罚单编号')]")
        self.assert_text("预警开始时间", "//label[contains(text(),'预警开始时间')]")
        self.assert_text("重 置", "//span[contains(text(),'重 置')]")
        self.assert_text("查 询", "//span[contains(text(),'查 询')]")

        # 展示筛选列表
        # 预警单号/罚单编号 预警标题 预警开始时间 预警状态 状态变更倒计时 操作
        self.assert_text("预警单号/罚单编号", "//th[contains(text(),'预警单号/罚单编号')]")
        self.assert_text("预警标题", "//th[contains(text(),'预警标题')]")
        self.assert_text("预警开始时间", "//th[contains(text(),'预警开始时间')]")
        self.assert_text("预警状态", "//th[contains(text(),'预警状态')]")
        self.assert_text("状态变更倒计时", "//th[contains(text(),'状态变更倒计时')]")
        self.assert_text("操作", "//th[contains(text(),'操作')]")

    def test_risk_details(self):
        """ 保障-预警中心-风险诊断预警详情"""
        self.input_remind()

        # 点击风险诊断预警tab
        self.click("//div[2]/div[@role='tab']")
        self.sleep(2)
        # 点击预警详情
        self.click("预警详情", by="link text")
        self.sleep(1)

        # 检查静态内容
        # 预警单号 预警类型 预警起始时间 预警内容 整改建议
        self.assert_text("预警单号", "//span[contains(text(),'预警单号：')]")
        self.assert_text("预警类型", "//span[contains(text(),'预警类型：')]")
        self.assert_text("预警起始时间", "//span[contains(text(),'预警起始时间：')]")
        self.assert_text("预警内容", "//span[contains(text(),'预警内容：')]")
        self.assert_text("整改建议", "//span[contains(text(),'整改建议：')]")

        # 检查动态内容(是否有数据传入并渲染)检查一两个即可,如果后续数据变动直接注释下面内容或者修改
        self.assert_text("店铺类", "//p[contains(text(),'店铺类')]")
