"""
des: 商品-带货商品报备 PC
author: zhouchang03
date: 2023/06/25
"""
import time
import pytest

from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p2

class GoodLicenceList(BaseTestCase):

    def test_goods_Reporting(self):
        """
        商品-进入带货商品报备首页
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("商品", "//div[text()='商品']")
        self.assert_text("带货商品报备", "//span[text()='带货商品报备']")
        self.click("//span[text()='带货商品报备']")
        self.sleep(2)

    def test_goods_Reporting_homepage(self):
        """
        商品-带货商品报备首页
        """
        self.test_goods_Reporting()
        self.sleep(2)

        # 首页信息
        self.assert_text("商品ID", "//label[contains(text(),'商品ID')]")
        self.assert_text("商品标题", "//label[contains(text(),'商品标题')]")
        self.assert_text("进审原因", "//label[contains(text(),'进审原因')]")
        self.assert_text("关联主播ID", "//label[contains(text(),'关联主播ID')]")
        self.assert_text("关联主播名称", "//label[contains(text(),'关联主播名称')]")
        self.assert_text("资质审核状态", "//label[contains(text(),'资质审核状态')]")
        self.assert_text("重 置", "//span[text()='重 置']")
        self.assert_text("查 询", "//span[text()='查 询']")

        # 列表详情
        self.assert_text("商品标题", "//th[contains(text(),'商品标题')]")
        self.assert_text("进审原因", "//th[@title='进审原因']")
        self.assert_text("关联主播", "//th[@title='关联主播']")
        self.assert_text("资质审核状态", "//th[contains(text(),'资质审核状态')]")
        self.assert_text("操作", "//th[contains(text(),'操作')]")

    def test_goods_Reporting_details(self):
        """
        商品-查看报备商品详情
        """
        self.test_goods_Reporting()
        self.sleep(2)

        self.click("(//span[contains(text(),'查看详情')])[1]")

        # 详情页信息
        self.assert_text("审核信息", "//h3[contains(text(),'审核信息')]")
        self.assert_text("当前状态：", "//div[contains(text(),'当前状态：')]")
        self.assert_text("审核意见：", "//div[contains(text(),'审核意见：')]")

        self.assert_text("商品信息", "//h3[contains(text(),'商品信息')]")
        self.assert_text("商品标题：", "//div[contains(text(),'商品标题：')]")
        self.assert_text("审核信息", "//h3[contains(text(),'审核信息')]")
        self.assert_text("商品ID：", "//div[contains(text(),'商品ID：')]")
        self.assert_text("商品备注：", "//div[contains(text(),'商品备注：')]")
        self.assert_text("关联主播：", "//div[contains(text(),'关联主播：')]")

        #返回
        self.click("//span[contains(text(),'返 回')]")

    def test_goods_reporting_create(self):
        """
        商品-主动申报
        """
        self.test_goods_Reporting()
        self.sleep(2)
        self.click("//span[contains(text(),'主动申报')]")
        self.sleep(2)
        self.assert_text("选择申报原因", "//h3[contains(text(),'选择申报原因')]")
        self.assert_text("选择申报商品", "//h3[contains(text(),'选择申报商品')]")
        self.assert_text("已选商品", "//div[contains(text(),'已选商品')]")
        self.assert_text("商品ID", "//th[contains(text(),'商品ID')]")
        self.assert_text("库存", "//th[contains(text(),'库存')]")
        self.assert_text("原价（元）", "//th[contains(text(),'原价（元）')]")

        self.assert_element("//input[@placeholder='搜索商品名称']")
        self.assert_element("//input[@placeholder='搜索商品ID']")

        self.click(".ui-form-horizontal .ui-form-item-control")
        self.sleep(1)
        self.assert_text("与特定主播PK带货", "//div[contains(text(),'与特定主播PK带货')]")

        self.assert_text("提 交", "//span[contains(text(),'提 交')]")
        self.assert_text("取 消", "//span[contains(text(),'取 消')]")
        self.assert_text("清空", "//span[contains(text(),'清空')]")

