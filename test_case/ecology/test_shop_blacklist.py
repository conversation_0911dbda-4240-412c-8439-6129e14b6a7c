"""
des: 店铺黑名单
author:
date: 2025/6
"""


import pytest
import time
from ddt import ddt
from test_case.ecology.base import BaseTestCase

@pytest.mark.p1
class ShopBlackList(BaseTestCase):

    def test_shopblack_homepage(self):
        """
        保障-店铺黑名单首页
        """
        self.login("WB_DOMAIN","themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障","//div[text()='保障']")
        self.assert_text("店铺黑名单","//span[text()='店铺黑名单']")
        self.click("//span[text()='店铺黑名单']")



    def test_shop_blacklist_help(self):
        """
        店铺黑名单-首页&帮助跳转
        """
        self.test_shopblack_homepage()
        self.sleep(2)

        self.assert_text("店铺黑名单", "//span[text()='店铺黑名单']")
        self.assert_element_visible('//*[@id="dilu_micro_root"]/div/div/div[1]/div/div[2]/div[1]/div[3]/span/span[1]')
        self.assert_element_visible('//*[@id="dilu_micro_root"]/div/div/div[1]/div/div[2]/div[2]/div[3]/span/span[1]')

        self.assert_text("买家昵称/ID", "//th[text()='买家昵称/ID']")
        self.assert_text("拉黑时间", "//th[text()='拉黑时间']")
        self.assert_text("操作人", "//th[text()='操作人']")
        self.assert_text("操作", "//th[text()='操作']")

        self.click("//span[text()='使用帮助']")
        self.assert_text("店铺黑名单使用帮助","//span[text()='店铺黑名单使用帮助']")
        self.sleep(2)
        self.switch_to_window(0)
