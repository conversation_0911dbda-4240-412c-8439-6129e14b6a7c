"""
des: 经营风险管理
author: wb_q<PERSON><PERSON><PERSON><PERSON>
date: 2025/01/02
"""

import pytest
import time
from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p1
class RiskOperate(BaseTestCase):
    def operatingrisk_logon(self):
        """ """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.shop_close_pop()
        self.assert_text("保障","//div[text()='保障']")
        self.click("//span[text()='经营风险管理']")
        self.close_merchant_assistant()


    def test_risk_management_front(self):
        """经营风险管理-前置"""
        self.operatingrisk_logon()
        self.sleep(2)
        self.assert_text("经营风险预警与前置防控", "//div[text()='经营风险预警与前置防控']")
        self.assert_text("01 事前：减少风险发生", "//div[text()='01 事前：减少风险发生']")
        self.assert_text("02 事中：经营风险预警", "//div[text()='02 事中：经营风险预警']")
        self.click('//*[@id="root"]/div/div[1]/div[2]/div[2]/div/div[2]/div/div/a')
        self.sleep(1)
        self.switch_to_window(0)
        self.assert_text("03 事后：异常行为举报", "//div[text()='03 事后：异常行为举报']")
        self.click('//*[@id="root"]/div/div[1]/div[2]/div[3]/div/div[2]/div/div/a')
        self.sleep(1)
        self.assert_text("举报中心", "//span[text()='举报中心']")
        self.switch_to_window(0)

    def test_risk_management_details(self):
        """经营风险-平台处理风险明细"""

        self.operatingrisk_logon()
        self.sleep(2)

        self.assert_text("经营风险预警与前置防控", "//div[text()='经营风险预警与前置防控']")
        self.assert_text("数据概览", "//p[text()='数据概览']")
        self.click("//a[text()='查看平台处理风险明细']")
        self.sleep(1)
        self.assert_text("已处理订单", "//span[text()='已处理订单']")
        self.assert_text("订单编号", "//span[text()='订单编号']")
        self.assert_text("订单创建时间", "//span[text()='订单创建时间']")
        self.assert_text("处理原因", "//span[text()='处理原因']")
        self.assert_text("处理手段", "//span[text()='处理手段']")
        self.assert_text("判定时间", "//span[text()='判定时间']")
        self.go_back()
