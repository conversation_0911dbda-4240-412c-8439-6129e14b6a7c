"""
    治理PC-UI自动化一些通用方法；
    eg: 登入快手小店；
"""
import time
from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain_by_env
from page_objects.trade_order.trade_order_page import TradeOrderPage
import logging


class BaseTestCase(BaseCase):
    def login(self, domain, account):
        """登入快手小店"""
        # 设置日志记录基本配置
        logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 账号
        account_data = get_account_info(account)
        # 环境 + URL
        if self.var1 and self.var1 == 'prt':
            self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
            host = get_domain_by_env(domain, self.var1)
            logging.info("current env prt with PRT.test")
        else:
            # 运行线上环境
            host = get_domain_by_env(domain, 'online')
            logging.info("current env:online")

        self.open(host)
        self.maximize_window()

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        self.wait_for_ready_state_complete()
        logging.info("input kuaishop succeed")

    def close_merchant_assistant(self):
        """关闭商家助手"""
        if self.is_element_visible(TradeOrderPage.merchant_assistant):
            merchant_assistant_close_button = self.find_elements(TradeOrderPage.merchant_assistant_close_button)
            if len(merchant_assistant_close_button) > 0:
                merchant_assistant_close_button[-1].click()
        self.wait_for_ready_state_complete()

    def close_notice(self):
        # 知道了弹窗
        while self.is_element_visible("#driver-popover-item > div.driver-clearfix.driver-popover-footer > button"):
            self.click("#driver-popover-item > div.driver-clearfix.driver-popover-footer > button")
            time.sleep(1)

    def close_pop_close(self):
        # 关闭拦截弹窗
        if self.is_element_visible("(//div[@class='driver-popover-title'])[1]"):
            self.click("//button[text()='知道了']")

    def shop_close_pop(self):
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[2]'):
            self.click("//button[text()='知道了']")
            if self.is_text_visible("经营诊断升级啦！"):
                self.click("//button[text()='知道了']")

