"""
des: 违规申诉 PC
author: zhouchang03
date: 2024/04/29
"""

import pytest
import time

from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p1
class ComplaintViolation(BaseTestCase):

    def test_master_Violation_homepage(self):
        """
        违约单管理-达人违规首页
        """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//div[text()='商家']")
        self.sleep(1)
        self.click("//div[text()='达人']")
        self.assert_text("违规记录", "//span[text()='违规记录']")
        self.assert_text("违规场景", "//th[text()='违规场景']")
        self.assert_text("严重程度", "//th[text()='严重程度']")
        self.assert_text("处置内容", "//th[text()='处置内容']")
        self.assert_text("当前状态", "//th[text()='当前状态']")
        self.assert_text("申诉和整改状态", "//div[contains(text(),'申诉和整改状态')]")
        self.assert_text("下发时间", "//th[text()='下发时间']")
        self.assert_text("操作", "//th[text()='操作']")

    def test_master_violation_details(self):
        """
        违约单管理-达人违规详情
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//a[contains(text(),'查看详情')][1]")
        self.sleep(1)
        self.assert_text("处置详情","//span[text()='处置详情']")
        self.click("//div[@role='button']//span[@class='kope-text']//span[1]")
        self.sleep(1)
        self.assert_text("处置标准","//span[text()='处置标准']")
        self.assert_text("违规场景","//span[text()='违规场景']")
        self.assert_text("严重程度","//span[text()='严重程度']")
        self.click("//span[text()='我知道了']")
        self.sleep(1)
        self.switch_to_window(0)

    def test_master_violation_record(self):
        """
        违约单管理-达人违规记录快速筛选
        """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[2]')
        self.sleep(1)
        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[3]')
        self.sleep(1)
        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[4]')
        self.sleep(1)
        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[5]')
        self.sleep(1)
        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[6]')
        self.sleep(1)
        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[1]')

    def test_master_violation_filter(self):
        """
        违约单管理-违规记录筛选枚举
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.assert_text("违约单编号", "//label[contains(text(),'违约单编号')]")
        self.assert_text("违约单状态", "//label[contains(text(),'违约单状态')]")
        self.assert_text("整改状态", "//label[contains(text(),'整改状态')]")
        self.assert_text("申诉状态", "//label[contains(text(),'申诉状态')]")
        self.assert_text("违约金状态", "//label[contains(text(),'违约金状态')]")
        self.assert_text("违规商品编号", "//label[contains(text(),'违规商品编号')]")
        self.assert_text("违规场景", "//label[contains(text(),'违规场景')]")
        self.assert_text("下发时间", "//label[contains(text(),'下发时间')]")

    def test_master_score_change(self):
        """
        违约单管理-达人分数变更记录
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.click("//span[text()='分数变更记录']")
        self.sleep(1)
        self.assert_text("分数变更记录", "//span[text()='分数变更记录']")
        self.click('//*[@id="root"]/section/main/div[2]/span[2]')
        self.sleep(1)
        self.switch_to_window(1)
        self.click("//div[@class='score-title']//span[text()='相关说明']")
        self.sleep(1)
        self.click("//span[text()='知道了']")
        self.sleep(1)
        self.switch_to_window(0)

    def test_merchant_Violation_homepage(self):
        """
        违约单管理-商家违规首页
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.assert_text("违规记录", "//span[text()='违规记录']")
        self.assert_text("违规场景", "//th[text()='违规场景']")
        self.assert_text("严重程度", "//th[text()='严重程度']")
        self.assert_text("处置内容", "//th[text()='处置内容']")
        self.assert_text("当前状态", "//th[text()='当前状态']")
        self.assert_text("申诉和整改状态", "//div[contains(text(),'申诉和整改状态')]")
        self.assert_text("下发时间", "//th[text()='下发时间']")
        self.assert_text("操作", "//th[text()='操作']")

    def test_merchant_Violation_details(self):
        """
        违规单管理-商家违约单详情
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//div[text()='商家']")
        self.sleep(2)
        self.assert_text("违规记录", "//span[text()='违规记录']")
        self.click("//a[contains(text(),'查看详情')][1]")
        self.sleep(1)
        self.assert_text("处置详情", "//span[text()='处置详情']")
        if self.assert_element_visible("//span[contains(text(),'历史违规记录')]"):
            self.assert_text("历史违规记录", "//span[text()='历史违规记录']")
            self.click("//span[text()='查看更多']")
            self.sleep(1)
            self.assert_text("历史违规记录", "//div[text()='历史违规记录']")
            self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")
            self.sleep(1)
        self.switch_to_window(0)

    def test_merchant_Violation_record(self):
        """
        违规单管理-商家违规快速筛选
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[2]')
        self.sleep(1)
        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[3]')
        self.sleep(1)
        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[4]')
        self.sleep(1)
        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[5]')
        self.sleep(1)
        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[6]')
        self.sleep(1)
        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div[4]/div[1]/div/form/div[1]/div[2]/div/div/div/div/div/div/div/div/div[1]')

    def test_merchant_Violation_filter(self):
        """
        违规单管理-商家违规筛选枚举
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.assert_text("违约单编号", "//label[contains(text(),'违约单编号')]")
        self.assert_text("违约单状态", "//label[contains(text(),'违约单状态')]")
        self.assert_text("整改状态", "//label[contains(text(),'整改状态')]")
        self.assert_text("申诉状态", "//label[contains(text(),'申诉状态')]")
        self.assert_text("违约金状态", "//label[contains(text(),'违约金状态')]")
        self.assert_text("违规商品编号", "//label[contains(text(),'违规商品编号')]")
        self.assert_text("违规场景", "//label[contains(text(),'违规场景')]")
        self.assert_text("下发时间", "//label[contains(text(),'下发时间')]")

    def test_merchant_score_change(self):
        """
        违约单管理-商家违规分数变更记录
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.click("//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.click("//span[text()='分数变更记录']")
        self.sleep(1)
        self.assert_text("分数变更记录", "//span[text()='分数变更记录']")
        self.click('//*[@id="root"]/section/main/div[2]/span[2]')
        self.sleep(1)
        self.switch_to_window(1)
        self.click("//div[@class='score-title']//span[text()='相关说明']")
        self.sleep(1)
        self.click("//span[text()='知道了']")
        self.sleep(1)
        self.switch_to_window(0)

    def test_Violation_study(self):
        """
        违约单管理-学规减分
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[@id='rc-tabs-0-tab-eshop']//div[1]")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.click("//span[contains(text(),'学规减分')]")
        self.sleep(1)
        self.switch_to_window(0)

    def test_Violation_rules(self):
        """
        违约单管理-规则中心
        """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        self.assert_text("保障", "//div[text()='保障']")
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("电商违规", "//div[text()='电商违规']")
        self.assert_text("达人", "//div[text()='达人']")
        self.assert_text("商家", "//div[text()='商家']")
        self.assert_text("违规记录", "//span[text()='违规记录']")

        self.click("//span[contains(text(),'规则中心')]")
        self.sleep(1)
        self.switch_to_window(0)

    def test_community_Violation_homepage(self):
        """ 违约单管理-社区违规 """
        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("保障", "//div[text()='保障']")
        self.click("//span[text()='违约单管理']")
        self.refresh_page()
        self.sleep(2)
        self.assert_text("违约单管理", "//span[text()='违约单管理']")
        self.click("//span[text()='违约单管理']")
        self.sleep(2)
        self.close_merchant_assistant()
        self.assert_text("社区违规", "//div[text()='社区违规']")
        self.click("//div[text()='社区违规']")
        self.sleep(1)

        # 违规记录
        self.assert_text("违规记录", "//div[text()='违规记录']")
        self.click("//div[text()='全部']")
        self.sleep(1)
        self.click("//div[text()='生效中']")
        self.assert_text("违规类型", "//th[text()='违规类型']")
        self.assert_text("违规对象", "//th[text()='违规对象']")
        self.assert_text("违规原因", "//th[text()='违规原因']")
        self.assert_text("处罚时间", "//th[text()='处罚时间']")
        self.assert_text("处理结果", "//th[text()='处理结果']")
        self.assert_text("判罚状态", "//th[text()='判罚状态']")
        self.assert_text("操作", "//th[text()='操作']")

        # 快手社区管理规范
        self.assert_text("《快手社区管理规范》", "//span[text()='《快手社区管理规范》']")
        self.click("//span[text()='《快手社区管理规范》']")
        time.sleep(1)
        self.assert_element_visible("//div/h2[text()='社区管理规范']")