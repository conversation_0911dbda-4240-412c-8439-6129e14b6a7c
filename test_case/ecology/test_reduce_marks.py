"""
des: 学规减分 PC
author: zhouchang03
date: 2024/04/25
"""

import pytest
import time

from ddt import ddt
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p1
class ReduceMarks(BaseTestCase):

    def test_minuspoints_master_homepage(self):
        """ 学规减分-带货达人tab切换-减分攻略 """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='带货达人']")
        self.sleep(1)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("带货达人", "//span[text()='带货达人']")

        self.assert_text("信息类", "//div[text()='信息类']")
        self.click("//div[text()='平台类']")
        self.assert_text("平台类", "//div[text()='平台类']")
        self.sleep(1)
        self.click("//div[text()='履约类']")
        self.assert_text("履约类", "//div[text()='履约类']")
        self.sleep(1)
        self.click("//div[text()='信息类']")
        self.sleep(1)
        self.click("//div[text()='减分攻略']")
        self.sleep(1)
        self.assert_text("学规减分攻略", "//span[text()='学规减分攻略']")
        self.switch_to_window(0)

    def test_minuspoints_master_record(self):
        """ 学规减分-带货达人-学习记录 """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='带货达人']")
        self.sleep(1)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("带货达人", "//span[text()='带货达人']")

        self.click("//span[contains(text(),'查看详情')]")
        self.assert_text("学考分详情", "//div[text()='学考分详情']")
        self.sleep(1)
        self.click("//span[text()='返 回']")
        self.sleep(1)
        self.click("//span[contains(text(),'查看全部')]")
        self.assert_text("学习记录", "//div[text()='学习记录']")
        self.sleep(1)
        self.assert_text("待完成", "//span[text()='待完成']")
        self.assert_text("已完成", "//span[text()='已完成']")
        self.click("//span[contains(text(),'已完成')]")
        self.sleep(1)
        self.click("//span[contains(text(),'待完成')]")
        self.sleep(1)
        self.click("//span[contains(text(),'返 回')]")

    def test_minuspoints_master_study(self):
        """学规减分-带货达人-学习页 """
        # todo:学规减分退出有问题，待修复后补充

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='带货达人']")
        self.sleep(1)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("带货达人", "//span[text()='带货达人']")

        self.click("//div[@class='rX3odVvB7Tp5bBvpxLAL'][contains(text(),'发布违禁信息')]")
        self.sleep(1)
        self.go_back()

    def test_minuspoints_master_identity(self):
        """学规减分-带货达人-身份切换"""

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='带货达人']")
        self.sleep(1)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("带货达人", "//span[text()='带货达人']")

        self.click("//span[contains(text(),'带货达人')]")
        self.sleep(1)
        self.click("//div[contains(text(),'商户')]")
        self.sleep(1)
        self.click("//span[contains(text(),'确 认')]")
        self.sleep(1)
        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("商户", "//span[text()='商户']")

    def test_minuspoints_Merchant_homepage(self):
        """ 学规减分-商户tab切换-减分攻略"""

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")
        self.sleep(2)

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='商户']")
        self.sleep(2)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("商户", "//span[text()='商户']")

        self.assert_text("信息类", "//div[text()='信息类']")
        self.click("//div[text()='平台类']")
        self.assert_text("平台类", "//div[text()='平台类']")
        self.sleep(2)
        self.click("//div[text()='履约类']")
        self.assert_text("履约类", "//div[text()='履约类']")
        self.sleep(2)
        self.click("//div[text()='信息类']")
        self.sleep(2)
        self.click("//div[text()='减分攻略']")
        self.sleep(2)
        self.assert_text("学规减分攻略", "//span[text()='学规减分攻略']")
        self.switch_to_window(0)

    def test_minuspoints_Merchant_record(self):
        """ 学规减分-商户-学习记录 """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")
        self.sleep(2)

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='商户']")
        self.sleep(2)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("商户", "//span[text()='商户']")

        self.click("//span[contains(text(),'查看详情')]")
        self.assert_text("学考分详情", "//div[text()='学考分详情']")
        self.sleep(2)
        self.click("//span[text()='返 回']")
        self.sleep(2)
        self.click("//span[contains(text(),'查看全部')]")
        self.assert_text("学习记录", "//div[text()='学习记录']")
        self.sleep(2)
        self.assert_text("待完成", "//span[text()='待完成']")
        self.assert_text("已完成", "//span[text()='已完成']")
        self.click("//span[contains(text(),'已完成')]")
        self.sleep(2)
        self.click("//span[contains(text(),'待完成')]")
        self.sleep(2)
        self.click("//span[contains(text(),'返 回')]")

    def test_minuspoints_Merchant_study(self):
        """ 学规减分-商户-学习页 """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='商户']")
        self.sleep(1)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("商户", "//span[text()='商户']")

        self.click("//div[@class='rX3odVvB7Tp5bBvpxLAL'][contains(text(),'发布违禁信息')]")
        self.sleep(1)
        self.go_back()

    def test_minuspoints_Merchant_identity(self):
        """ 学规减分-商户-切换身份 """

        self.login("WB_DOMAIN", "themis")
        self.sleep(5)
        self.close_pop_close()

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.assert_text("学规减分", "//span[text()='学规减分']")
        self.click("//span[text()='学规减分']")

        # 选择身份
        self.assert_text("带货达人", "//div[text()='带货达人']")
        self.assert_text("商户", "//div[text()='商户']")
        self.click("//div[text()='商户']")
        self.sleep(1)
        self.click("//span[text()='确 认']")

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("商户", "//span[text()='商户']")

        self.click("//span[contains(text(),'商户')]")
        self.sleep(1)
        self.click("//div[contains(text(),'带货达人')]")
        self.sleep(1)
        self.click("//span[contains(text(),'确 认')]")
        self.sleep(1)

        self.assert_text("学规减分·", "//span[text()='学规减分·']")
        self.assert_text("带货达人", "//span[text()='带货达人']")
