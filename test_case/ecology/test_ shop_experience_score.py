"""
des: 购物体验分，现在已经下掉了
author: wangxue12
date: 2023/05/05
"""

# import random
# from time import sleep
# import pytest

# from ddt import ddt
# from unittest import skip, skipIf
# from test_case.ecology.base import BaseTestCase


# @skip
# class ShopExpScore(BaseTestCase):
#     def test_homepage(self):
#         """ 购物体验分首页 """
#         self.login("WB_DOMAIN", "themis")
#         sleep(1)
#         self.hover_and_click("//*[@id='nav-menu-K0Js91FRnRw']", "//span[text()='店铺']")
#         sleep(1)
#         self.click("//span[text()='购物体验分']")
#         self.assert_link_status_code_is_not_404('https://s.kwaixiaodian.com/zone/store-score/master/home')  # 存在首页
#         self.click("//span[text()='规则说明']")
#         self.assert_element_visible("//div[@class='common-rich-text']//strong")
#         self.assert_link_status_code_is_not_404("https://ppg.viviv.com/doodle/pOLOjjha.html?hyId=jimu_pOLOjjha")
#         self.switch_to_window(0)
#
#         # 购物体验分概览
#         self.assert_element_visible("//div[@class='sc-hKMtZM bHLUWJ']")
#         self.assert_element_visible("//div[@id='root']/section[1]/main[1]/div[1]/div[1]/article[1]/section[1]/div[2]")
#
#         self.click("//span[text()='去提分']")
#         self.assert_link_status_code_is_not_404("https://edu.kwaixiaodian.com/bbs/web/article?id=7732&layoutType=4")
#         self.assert_element_visible("//div[@class='bbs-pc-layout-content comment-bbs-page']//div")
#         self.switch_to_window(0)
#
#         self.click("(//span[text()='去提分'])[2]")
#         self.assert_link_status_code_is_not_404("https://edu.kwaixiaodian.com/bbs/web/article?id=1117&layoutType=4")
#         self.assert_element_visible("//div[@class='bbs-pc-layout-content comment-bbs-page']//div")
#         self.switch_to_window(0)
#
#         self.click("(//span[text()='去提分'])[3]")
#         self.assert_link_status_code_is_not_404("https://edu.kwaixiaodian.com/bbs/web/article?id=452&layoutType=4")
#         self.assert_element_visible("//div[@class='bbs-pc-layout-content comment-bbs-page']//div")
#         self.switch_to_window(0)
#
#         # 指标细则模块
#         self.assert_element_visible("//span[text()='指标细则']")
#         self.assert_element_visible("//section[@class='sc-dkzDqf jzPApn']/following-sibling::section[1]")
#         self.click("//span[text()='商品品退率']")
#         self.assert_link_status_code_is_not_404("https://syt.kwaixiaodian.com/zones/service/quality")
#         self.assert_element_visible("//div[@class='suggest-con']/following-sibling::div[1]")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='商品绝对质量差评率']")
#         self.assert_link_status_code_is_not_404("https://syt.kwaixiaodian.com/zones/service/quality")
#         self.assert_element_visible("//div[@class='suggest-con']/following-sibling::div[1]")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='直播间举报率']")
#         self.assert_link_status_code_is_not_404("https://syt.kwaixiaodian.com/zone/service/report")
#         self.assert_element_visible("//div[@id='scroll-content']/div[3]")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='三分钟回复率']")
#         self.assert_link_status_code_is_not_404("https://syt.kwaixiaodian.com/zone/service/overview")
#         self.assert_element_visible("//div[@class='g-content multi-y-line-chart']")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='客服好评率']")
#         self.assert_link_status_code_is_not_404("https://s.kwaixiaodian.com/zone/im/dashboard")
#         self.assert_element_visible("//div[@class='buyer-consulting']//div")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='及时发货率']")
#         self.assert_link_status_code_is_not_404("https://syt.kwaixiaodian.com/zones/service/logistics")
#         self.assert_element_visible("//div[@id='rc-tabs-0-panel-new']//div")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='仅退款自主完结时长']")
#         if self.assert_link_status_code_is_not_404("https://kssyt.e.kuaishou.com/welcome"):
#             self.assert_element_visible("//div[text()='快手生意通 - 快手商家全生意链路数据服务平台']/following-sibling::a")
#         elif self.assert_link_status_code_is_not_404("https://kssyt.e.kuaishou.com/hm"):
#             self.assert_element_visible("//div[@id='home-real-time']//div")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='退货退款自主完结时长']")
#         if self.assert_link_status_code_is_not_404("https://kssyt.e.kuaishou.com/welcome"):
#             self.assert_element_visible("//div[text()='快手生意通 - 快手商家全生意链路数据服务平台']/following-sibling::a")
#         elif self.assert_link_status_code_is_not_404("https://kssyt.e.kuaishou.com/hm"):
#             self.assert_element_visible("//div[@id='home-real-time']//div")
#         self.switch_to_window(0)
#
#         self.click("//span[text()='商责纠纷率(门槛指标)']")
#         if self.assert_link_status_code_is_not_404("https://kssyt.e.kuaishou.com/welcome"):
#             self.assert_element_visible("//div[text()='快手生意通 - 快手商家全生意链路数据服务平台']/following-sibling::a")
#         elif self.assert_link_status_code_is_not_404("https://kssyt.e.kuaishou.com/hm"):
#             self.assert_element_visible("//div[@id='home-real-time']//div")
#         self.switch_to_window(0)
