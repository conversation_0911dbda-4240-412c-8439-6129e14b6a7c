"""
des: 带货口碑分 PC
author: wangxue12
date: 2023/04/12
"""
import time
import pytest
from ddt import ddt
import logging
from seleniumbase.common.exceptions import NoSuchElementException, TextNotVisibleException
from test_case.ecology.base import BaseTestCase


@ddt
@pytest.mark.p0
class MasterScore(BaseTestCase):
    def input_masterpage(self):
        # if self.var1 == 'prt' :
        """ 进入带货口碑分首页 """
        self.login("WB_DOMAIN", "themis")
        time.sleep(1)

        # 导航栏
        self.assert_text("店铺", "//div[text()='店铺']")
        self.click("//span[text()='带货口碑分']")
        time.sleep(2)
        self.assert_text("店铺体验分", "//span[text()='店铺体验分']")
        self.assert_text("带货口碑分", "//span[text()='带货口碑分']")
        self.click("//span[text()='带货口碑分']")
        self.sleep(2)

        # 处理进入达人分首页的所有公告等信息
        if self.is_element_visible("//span[text()='我知道了']"):
            self.click("//span[text()='我知道了']")
        self.close_merchant_assistant()
        logging.info("input masterpage succeed")

    def test_master_homepage(self):
        """ 带货口碑分（达人分）+维度分"""
        self.input_masterpage()

        # 趋势图模块
        self.assert_text("带货口碑分","//span[text()='带货口碑分']")
        self.click("//span[text()='查看趋势']")
        self.assert_text("店铺概况","//div[text()='店铺概况']")
        self.sleep(1)
        self.click("//span[@class='kwaishop-seller-governance-micro-sub-score-pc-modal-close-x']")
        self.sleep(0.5)
        self.click("//span[text()='商品力']")
        self.assert_text("分数详情","//div[text()='分数详情']")
        self.sleep(0.5)
        self.click("//span[@class='kwaishop-seller-governance-micro-sub-score-pc-modal-close-x']")
        self.sleep(1)
        self.click("//span[text()='内容力']")
        self.assert_text("分数详情", "//div[text()='分数详情']")
        self.sleep(0.5)
        self.click("//span[@class='kwaishop-seller-governance-micro-sub-score-pc-modal-close-x']")

    def test_master_homepage_rule(self):
        """
        右侧tab页内容/首页规则中心
        """
        self.input_masterpage()
        self.refresh_page()

        # 右侧规则中心
        self.assert_text("规则中心", "//span[text()='规则中心']")
        self.sleep(1)
        self.assert_element_visible("//div[text()='新版带货口碑分考核规则']")
        self.click("//div[text()='新版带货口碑分考核规则']")
        self.sleep(1)
        self.assert_element_visible("//span[text()='快手小店带货口碑分管理规则']")
        self.switch_to_window(0)
        self.sleep(1)
        self.click("//span[contains(text(),'带货口碑分体验反馈')]")
        self.sleep(1)
        self.type("input[placeholder='请输入手机号']", "19133301607")
        self.type("input[placeholder='请输入密码']", "test@123")
        self.click("//button[text()='登录']")
        self.sleep(1)
        self.assert_element_visible("//div[text()='「新版带货口碑分」满意度调查']")

    def test_Consumer_rules_feedback(self):
        """
        消费者侧展示规则-分数对应&反馈
        """
        self.input_masterpage()

        self.assert_text("消费者端展示规则", "//span[text()='消费者端展示规则']")
        self.click("//span[contains(text(),'消费者端展示规则')]")
        self.assert_text("消费者端展示规则", "//div[text()='消费者端展示规则']")
        self.assert_text("分数-星级对应", "//div[text()='分数-星级对应']")

        self.assert_text("分数-星级对应", "//div[text()='分数-星级对应']")
        self.click("//div[text()='分数-星级对应']")
        # todo：本地能跑通，线上报错，先注释
        # self.click("//a[contains(text(),'点击反馈')]")
        # time.sleep(0.5)
        # self.type("input[placeholder='请输入手机号']", "19133301607")
        # self.type("input[placeholder='请输入密码']", "test@123")
        # self.click("//button[text()='登录']")
        # self.assert_element_visible("//p[contains(text(),'问题反馈')]")
        # self.switch_to_window(0)
        # time.sleep(0.5)
        self.click("//span[text()='我知道了']")

    def test_Consumer_rules_scene(self):
        """
        消费者侧展示规则-场景查看
        """
        self.input_masterpage()

        self.assert_text("消费者端展示规则", "//span[text()='消费者端展示规则']")
        self.click("//span[contains(text(),'消费者端展示规则')]")
        self.assert_text("消费者端展示规则", "//div[text()='消费者端展示规则']")
        self.assert_text("分数-星级对应", "//div[text()='分数-星级对应']")

        # 展示场景
        self.assert_text("展示规则", "//div[text()='展示规则']")
        self.click("//div[text()='展示规则']")
        self.sleep(0.5)
        self.assert_text("店铺首页", "//span[text()='店铺首页']")
        self.click("//span[text()='店铺首页']")
        self.sleep(0.5)
        self.assert_text("店铺详情", "//span[text()='店铺详情']")
        self.click("//span[text()='店铺详情']")
        self.sleep(0.5)
        self.assert_text("商品详情", "//span[text()='商品详情']")
        self.click("//span[text()='商品详情']")
        self.sleep(0.5)
        self.assert_text("搜索结果", "//span[text()='搜索结果']")
        self.click("//span[text()='搜索结果']")
        self.sleep(0.5)
        self.assert_text("直播间小黄车", "//span[text()='直播间小黄车']")
        self.click("//span[text()='直播间小黄车']")
        self.sleep(0.5)
        self.assert_text("提单页", "//span[text()='提单页']")
        self.click("//span[text()='提单页']")
        self.sleep(0.5)
        self.assert_text("直播间信息页", "//span[text()='直播间信息页']")
        self.click("//span[text()='直播间信息页']")
        self.sleep(0.5)
        self.assert_text("个人页", "//span[text()='个人页']")
        self.click("//span[text()='个人页']")
        self.sleep(0.5)

        self.click("//span[text()='我知道了']")

    def test_Product_Strength_jump(self):
        """
        商品力-二级页跳转正常
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[1]")
        self.sleep(1)
        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.switch_to_window(0)

        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[2]")
        self.sleep(1)
        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.switch_to_window(0)

        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[3]")
        time.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.switch_to_window(0)
        time.sleep(1)

    def test_Content_power_jump(self):
        """
        内容力-二级页跳转正常
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[4]")

        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.switch_to_window(0)

        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[5]")
        self.sleep(1)
        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.switch_to_window(0)

        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[6]")
        time.sleep(1)
        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.switch_to_window(0)
        time.sleep(1)


    def test_Product_Strength_formula(self):
        """
        商品力-挂车前商品分-公式
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[1]")
        self.sleep(1)

        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)
        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)

        self.assert_text("挂车前商品体验分", "//div[text()='挂车前商品体验分']")
        self.assert_text("近30天直播和短视频带货产生的有效支付订单在挂车前店铺的商品体验分之和", "//div[text()='近30天直播和短视频带货产生的有效支付订单在挂车前店铺的商品体验分之和']")
        self.assert_text("近30天直播和短视频带货产生的有效支付订单总量", "//div[text()='近30天直播和短视频带货产生的有效支付订单总量']")
        self.assert_text("÷","//div[text()='÷']")

    def test_Product_Strength_diagnosis(self):
        """
        商品力-挂车前商品分-分析诊断
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[1]")
        self.sleep(1)

        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)
        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)

        self.assert_element_visible("//span[@class='titleText___zgzvt']")
        self.assert_text("30天口径","//span[text()='30天口径']")
        self.assert_text("30天口径","//div[text()='30天口径']")
        self.assert_text("单天口径","//div[text()='单天口径']")
        self.click("//div[text()='单天口径']")
        self.sleep(0.5)
        self.assert_text("单天口径","//span[text()='单天口径']")
        self.click("//div[text()='30天口径']")
        self.sleep(0.5)

    def test_Product_Strength_details(self):
        """
        商品力-挂车前商品分-详情
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[1]")
        self.sleep(1)

        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)
        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)

        self.assert_text("挂车前商家商品分详情", "//div[text()='挂车前商家商品分详情']")
        self.assert_text("最多显示200条", "//div[text()='最多显示200条']")
        # self.assert_text("商家信息", "//th[text()='商家信息']")
        # self.assert_text("商家商品分（按每天订单量加权）", "//th[text()='商家商品分（按每天订单量加权）']")
        # self.assert_text("带货商品数", "//th[text()='带货商品数']")
        # self.assert_text("订单数", "//th[text()='订单数']")

    def test_Product_Applause_Rate_formula(self):
        """
        商品力-挂车前好评率-公式
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[2]")
        self.sleep(1)

        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)
        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)

        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.assert_text("近30天直播和短视频带货产生的有效支付订单在挂车前商品的商品好评率之和", "//div[text()='近30天直播和短视频带货产生的有效支付订单在挂车前商品的商品好评率之和']")
        self.assert_text("近30天直播和短视频带货产生的有效支付订单总量", "//div[text()='近30天直播和短视频带货产生的有效支付订单总量']")

    def test_Product_Applause_Rate_diagnosis(self):
        """
        商品力-挂车前好评率-分析诊断
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[2]")
        self.sleep(1)

        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)
        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)

        self.assert_element_visible("//span[@class='titleText___zgzvt']")
        self.assert_text("30天口径", "//span[text()='30天口径']")
        self.assert_text("30天口径", "//div[text()='30天口径']")
        self.assert_text("单天口径", "//div[text()='单天口径']")
        self.click("//div[text()='单天口径']")
        self.sleep(1)
        self.assert_text("单天口径", "//span[text()='单天口径']")
        self.click("//div[text()='30天口径']")
        self.sleep(1)

    def test_Product_Applause_Rate_details(self):
        """
        商品力-挂车前好评率-详情
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[2]")
        self.sleep(1)

        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)
        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)
        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)

        self.assert_text("商品好评率详情", "//div[text()='商品好评率详情']")
        self.assert_text("最多显示200条", "//div[text()='最多显示200条']")
        self.assert_text("选择类目", "//span[text()='选择类目']")
        # todo：类目选择器中类目不固定
        # self.assert_text("商品信息", "//th[text()='商品信息']")
        # self.assert_text("商家信息", "//th[text()='商家信息']")
        # self.assert_text("所属类目", "//th[text()='所属类目']")
        # self.assert_text("商品好评率（按每天订单量加权）", "//th[text()='商品好评率（按每天订单量加权）']")
        # self.assert_text("支付订单数", "//th[text()='支付订单数']")

    def test_Product_order_feedback_formula(self):
        """
        商品力-带货订单负反馈率-公式
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[3]")
        self.sleep(1)

        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)
        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)
        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)

        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.assert_text("近30日有效支付订单中有带货负反馈的订单量（不含内容负反馈）", "//div[text()='近30日有效支付订单中有带货负反馈的订单量（不含内容负反馈）']")
        self.assert_text("近30日带货产生的有效支付订单总量", "//div[text()='近30日带货产生的有效支付订单总量']")

    def test_Product_order_feedback_diagnosis(self):
        """
        商品力-带货订单负反馈率-分析诊断
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[3]")
        self.sleep(1)

        self.assert_text("带货订单负反馈率", "//div[text()='带货订单负反馈率']")
        self.click("//div[text()='挂车前商家商品分']")
        self.sleep(1)
        self.assert_text("挂车前商家商品分", "//div[text()='挂车前商家商品分']")
        self.click("//div[text()='挂车前商品好评率']")
        self.sleep(1)
        self.assert_text("挂车前商品好评率", "//div[text()='挂车前商品好评率']")
        self.click("//div[text()='带货订单负反馈率']")
        self.sleep(1)

        self.assert_element_visible("//span[@class='titleText___zgzvt']")
        self.assert_text("30天口径", "//span[text()='30天口径']")
        self.assert_text("30天口径", "//div[text()='30天口径']")
        self.assert_text("单天口径", "//div[text()='单天口径']")
        self.assert_text("带货订单负反馈原因Top（最多展示10个）", "//span[text()='带货订单负反馈原因Top（最多展示10个）']")

        self.click("//div[text()='单天口径']")
        self.sleep(1)
        self.assert_text("单天口径", "//span[text()='单天口径']")
        self.click("//div[text()='30天口径']")
        self.sleep(1)

    def test_Product_order_feedback_details(self):
        """
        商品力-带货订单负反馈率-详情
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("商品力", "//span[text()='商品力']")
        self.assert_text("权重占比70%", "//span[text()='权重占比70%']")

        self.assert_text("挂车前商家商品分", "//span[text()='挂车前商家商品分']")
        self.assert_text("挂车前商品好评率", "//span[text()='挂车前商品好评率']")
        self.assert_text("带货订单负反馈率", "//span[text()='带货订单负反馈率']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[3]")
        self.sleep(1)

        self.assert_text("带货商品负反馈详情", "//div[text()='带货商品负反馈详情']")
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("商家信息", "//th[text()='商家信息']")
        self.assert_text("带货订单负反馈率", "//th[text()='带货订单负反馈率']")
        self.assert_text("负反馈订单数", "//th[text()='负反馈订单数']")
        self.assert_text("订单数", "//th[text()='订单数']")
        self.assert_element("//div[text()='商品负反馈原因']//span[text()='最多展示10个']")

    def test_Contentpower_feedback_formula(self):
        """
        内容力-订单内容负反馈率-公式
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[4]")

        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)
        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)

        self.assert_text("内容订单负反馈率", "//div[text()='内容订单负反馈率']")
        self.assert_text("近30日有效支付订单中有内容负反馈的订单量", "//div[text()='近30日有效支付订单中有内容负反馈的订单量']")
        self.assert_text("近30日带货产生的有效支付订单总量", "//div[text()='近30日带货产生的有效支付订单总量']")

    def test_Contentpower_feedback_diagnosis(self):
        """
        内容力-订单内容负反馈率-分析诊断
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[4]")
        self.sleep(1)

        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)
        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)

        self.assert_element_visible("//span[@class='titleText___zgzvt']")
        self.assert_text("30天口径", "//span[text()='30天口径']")
        self.assert_text("30天口径", "//div[text()='30天口径']")
        self.assert_text("单天口径", "//div[text()='单天口径']")
        self.assert_element_visible("//div[@class='title___e1_33']//span[text()='订单内容负反馈原因Top（最多展示10个）']")

        self.click("//div[text()='单天口径']")
        self.sleep(1)
        self.assert_text("单天口径", "//span[text()='单天口径']")
        self.click("//div[text()='30天口径']")
        self.sleep(1)

    def test_Contentpower_feedback_details(self):
        """
        内容力-订单内容负反馈率-详情
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[4]")
        self.sleep(1)

        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)
        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)

        self.assert_text("带货商品内容负反馈详情", "//div[text()='带货商品内容负反馈详情']")
        self.assert_text("最多显示200条", "//div[text()='最多显示200条']")

        # 带货商品内容反馈详情
        self.assert_text("商品信息", "//th[text()='商品信息']")
        self.assert_text("商家信息", "//th[text()='商家信息']")
        self.assert_text("订单内容负反馈率", "//th[text()='订单内容负反馈率']")
        self.assert_text("负反馈订单数", "//th[text()='负反馈订单数']")
        self.assert_text("订单数", "//th[text()='订单数']")
        # 右边商品负反馈原因
        self.assert_text("商品负反馈原因", "//div[text()='商品负反馈原因']")
        self.assert_text("最多展示10个", "//span[text()='最多展示10个']")

    def test_Contentpower_FieldReport_formula(self):
        """
        内容力-场域举报-公式
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[5]")
        self.sleep(1)

        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)
        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)

        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.assert_text("挂车短视频举报率超越比例", "//div[text()='挂车短视频举报率超越比例']")
        self.assert_text("近30日短视频带货GMV占比", "//div[text()='近30日短视频带货GMV占比']")
        self.assert_text("挂车直播间举报率超越比例", "//div[text()='挂车直播间举报率超越比例']")
        self.assert_text("近30日直播带货GMV占比", "//div[text()='近30日直播带货GMV占比']")

        self.click("//span[text()='展开公式']")
        self.assert_text("挂车短视频举报率", "//div[text()='挂车短视频举报率']")
        self.assert_text("近30天短视频的有效举报人数", "//div[text()='近30天短视频的有效举报人数']")
        self.assert_text("近30天短视频的有效观看人数", "//div[text()='近30天短视频的有效观看人数']")
        self.assert_text("直播举报率", "//div[text()='直播举报率']")
        self.assert_text("近30天挂车直播间的有效举报人数", "//div[text()='近30天挂车直播间的有效举报人数']")
        self.assert_text("近30天直播间的有效观看人数", "//div[text()='近30天直播间的有效观看人数']")

    def test_Contentpower_FieldReport_diagnosis(self):
        """
        内容力-场域举报-诊断
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[5]")
        self.sleep(1)

        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)
        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)

        self.assert_element_visible("//span[@class='titleText___zgzvt']")
        self.assert_text("短视频", "//div[text()='短视频']")
        self.assert_text("直播间", "//div[text()='直播间']")

        self.assert_text("30天口径", "//span[text()='30天口径']")
        self.assert_text("30天口径", "//div[text()='30天口径']")
        self.assert_text("单天口径", "//div[text()='单天口径']")

        self.click("//div[text()='单天口径']")
        self.sleep(1)
        self.assert_text("单天口径", "//span[text()='单天口径']")
        self.click("//div[text()='30天口径']")
        self.sleep(1)
        self.click("//div[text()='直播间']")
        self.sleep(1)
        self.assert_text("30天口径", "//span[text()='30天口径']")
        self.assert_text("30天口径", "//div[text()='30天口径']")
        self.assert_text("单天口径", "//div[text()='单天口径']")

        self.click("//div[text()='单天口径']")
        self.sleep(1)
        self.assert_text("单天口径", "//span[text()='单天口径']")
        self.click("//div[text()='30天口径']")
        self.sleep(1)

    def test_Contentpower_FieldReport_details(self):
        """
        内容力-场域举报-详情
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[5]")
        self.sleep(1)

        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)
        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)
        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)

        self.assert_text("挂车短视频被举报详情", "//div[text()='挂车短视频被举报详情']")
        self.assert_text("挂车直播间被举报详情", "//div[text()='挂车直播间被举报详情']")
        self.assert_text("最多显示200条", "//div[text()='最多显示200条']")
        self.sleep(1)
        self.click("//div[text()='挂车直播间被举报详情']")
        self.sleep(1)

    def test_Contentpower_Violationorder_formula(self):
        """
        内容力-违规订单占比-公式
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[6]")
        self.sleep(1)

        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)
        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)
        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)

        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.assert_text("近30天“达人内容层&带货商品层”的较为严重违规关联的支付订单量", "//div[text()='近30天“达人内容层&带货商品层”的较为严重违规关联的支付订单量']")
        self.assert_text("近30天带货产生的有效支付订单总量", "//div[text()='近30天带货产生的有效支付订单总量']")

    def test_Contentpower_Violationorder_details(self):
        """
        内容力-违规订单占比-详情
        """
        self.input_masterpage()

        self.assert_element_visible("//span[text()='具体指标']")
        self.assert_text("内容力", "//span[text()='内容力']")
        self.assert_text("权重占比30%", "//span[text()='权重占比30%']")

        self.assert_text("订单内容负反馈率", "//span[text()='订单内容负反馈率']")
        self.assert_text("内容场域举报", "//span[text()='内容场域举报']")
        self.assert_text("违规订单占比", "//span[text()='违规订单占比']")
        self.click("(//span[@class='kope-text kope-text-inherit'][contains(text(),'查看详情')])[6]")
        self.sleep(1)

        self.assert_text("订单内容负反馈率", "//div[text()='订单内容负反馈率']")
        self.click("//div[text()='订单内容负反馈率']")
        self.sleep(1)
        self.assert_text("内容场域举报", "//div[text()='内容场域举报']")
        self.click("//div[text()='内容场域举报']")
        self.sleep(1)
        self.assert_text("违规订单占比", "//div[text()='违规订单占比']")
        self.click("//div[text()='违规订单占比']")
        self.sleep(1)

        self.assert_text("违规订单详情", "//div[text()='违规订单详情']")
        self.assert_text("带货商品违规详情", "//div[text()='带货商品违规详情']")
        self.assert_text("最多显示200条", "//div[text()='最多显示200条']")
        self.click("//div[text()='带货商品违规详情']")
        self.sleep(1)

