import time

from constant.account import get_account_info
from constant.domain import get_domain
import logging
from utils.env_help import get_env
from selenium import webdriver
from time import sleep

from seleniumbase import BaseCase


from constant.domain import get_domain_by_env
from page_objects.trade_order.trade_order_page import TradeOrderPage

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseAITestCase(BaseCase):

    def distribute_login(self, domain, account):

        # self.driver.quit()
        # self.driver = webdriver.Chrome()
        # self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        else:
            env = 'online'

        self.maximize_window()

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(1)

        if self.is_element_visible("(//span[contains(text(),'密码登录')])[1]") or self.is_element_visible("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        else:
            self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
            self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
            self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
            self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            time.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("button[type='button']")
            self.sleep(5)
        self.sleep(3)
        self.refresh()




