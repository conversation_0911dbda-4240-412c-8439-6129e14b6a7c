import pytest

from test_case.ai.base import BaseAITestCase
import pytest
import cv2
import numpy as np
import requests
import time
import json
# from selenium import webdriver
# from selenium.webdriver.common.by import By

from test_case.growth.kuaishouxiaodian.base import BaseTestCase
from seleniumwire import webdriver
from utils.env_help import get_put_test
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
"""
天河AI
"""
actionMap = {"0":"query", "1":"click", "2":"selectAndQuery"}

class TestTianhe(BaseTestCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    def del_pop(self):
        while self.is_element_visible('//button[contains(text(),\'知道了\')]'):
            self.click('//button[contains(text(),\'知道了\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'下一步\')]'):
            self.click('//button[contains(text(),\'下一步\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'完成\')]'):
            self.click('//button[contains(text(),\'完成\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'暂不处理\')]'):
            self.click('//button[contains(text(),\'暂不处理\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'关闭\')]'):
            self.click('//button[contains(text(),\'关闭\')]')
            time.sleep(1)

    def common_page_solve(self, delay_time):
        time.sleep(5)
        self.refresh()
        time.sleep(delay_time)
        self.del_pop()


    def init_sso(self):
        # sso_host = "https://sso.corp.kuaishou.com/cas/login"
        # self.login3(sso_host, "zhanghongbin", "tfBW09#yiCQ3")
        apsso_jost = "https://apsso.corp.kuaishou.com/apsso"
        self.login4(apsso_jost, "<EMAIL>", "Collins0721")
        time.sleep(3)
    def init_login(self, url):
        self.init_sso()
        self.loginAI(url)

    def sendExecuteInfo(self, executeId, sampleId, actionType, xpath, diffRate):
        """ 消息发送处理 暂时换成api 调用"""
        # res_url = "https://merchant-lego.corp.kuaishou.com/gateway/qa/risk/sample/execute/finish"
        res_url = "https://legoprt.corp.kuaishou.com/gateway/qa/risk/sample/execute/finish"
        list_json = [
            {
                "sampleId": sampleId,
                "diffRate": diffRate,
                "actionType": actionType,
                "xpath": xpath
            }
        ]
        data = {
            "executeId":executeId,
            # "list": "[{\"sampleId\":" + sampleId+",\"diffRate\": "+diffRate+",\"actionType\": "+actionType+",\"xpath\":"+ xpath+"}]"
            "list":json.dumps(list_json)
            # "list": [
            #     {
            #         "sampleId": sampleId,
            #         "diffRate": diffRate,
            #         "actionType": actionType,
            #         "xpath": xpath
            #     }
            # ]
        }
        headers = {
            'trace-context': '{"laneId":"PRT.test"}'
        }
        response = requests.request("POST", res_url, headers=headers, data=data).json()
        # response = requests.request("POST", res_url, data=data).json()
        print (" response ", response)
        file_path = "C:\\Users\\<USER>\\Desktop\\tianhe\\log2.txt"
        with open(file_path, 'w+') as file:
            file.write(str(data) + '\n')
            file.write(str(response) + '\n')

    def updateActionSet(self, sampleId, actionSet):
        """ 更新actionSet 调用"""
        res_url = "https://legoprt.corp.kuaishou.com/gateway/qa/risk/update/valid/action/set"
        data = {
            "id":sampleId,
            "actionSet": json.dumps(actionSet)
        }

        headers = {
            'trace-context': '{"laneId":"PRT.test"}'
        }
        response = requests.request("POST", res_url, headers=headers, data=data).json()
        # response = requests.request("POST", res_url, data=data).json()
        print (" response ", response)

    def queryActionSetById(self, sampleId):
        """ 消息发送处理 暂时换成api 调用"""
        res_url = "https://legoprt.corp.kuaishou.com/gateway/qa/risk/queryActionSetbyId"
        data = {
            "id": sampleId
        }
        # headers = {
        #     'trace-context': '{"laneId":"PRT.zhb"}'
        # }
        response = requests.request("GET", res_url, data=data).json()
        print ("-----------response-----------")
        print (response)
        if response["data"] is not None and "actionSet" in response["data"]:
            return response["data"]["actionSet"]
        else:
            return {}

    def detect(self, src_img_path, dst_img_path):

        src_img = cv2.imread(src_img_path)
        dst_img = cv2.imread(dst_img_path)
        ori_dst_img = cv2.imread(dst_img_path)

        src_img = cv2.GaussianBlur(src_img, [5, 5], 0)
        dst_img = cv2.GaussianBlur(dst_img, [5, 5], 0)

        diff = cv2. absdiff(src_img, dst_img)

        gray = cv2. cvtColor(diff, cv2.COLOR_BGR2GRAY)

        _, result = cv2.threshold(gray, 20, 255, cv2.THRESH_BINARY)

        num_255_pixels = np.sum(result == 255)
        height, width = result.shape
        total_pixels = height * width
        result = cv2.dilate(result, np.ones([5, 5]))

        contours, _ = cv2.findContours(result, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        areas = []

        for c in contours:
            area = cv2.contourArea(c)
            areas.append(area)
        areas = np.array(areas)

        index = np.argsort(areas)[-5:]
        top5_contours = []
        rect_pos = []
        len = index.shape[0]
        for i in range(len):
            top5_contours.append(contours[index[i]])

        for c in top5_contours:
            rect_pos.append(cv2.boundingRect(c))

        for x,y,w,h in rect_pos:
            cv2.rectangle(ori_dst_img, [x, y], [x + w, y + h], [0, 0, 255], 3)

        return ori_dst_img, round((1.0 * num_255_pixels / total_pixels) * 100, 2)

    #  kcdn 平台文档 https://kcdn.corp.kuaishou.com/doc/#2_RESTfulapi_shangchuanwenjian
    def uploadMultiCdnFile(self, img_paths):
        try:
            url = 'https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/multiple'
            params = {
                'token' : '112363_767ee3ffce9f13f8f1612c42a8baafae'
            }
            data = {
                'pid' : 'tianheTestPaltform',
                'allowHash': 'false',
                'allowRewrite': 'false',
                'allowMD5': 'false',
                'requestInfo.uploaderType': 2,
                'requestInfo.serviceName':'public-uploadTest',
                'requestInfo.requestUri':'/rest/upolad1',
                'requestInfo.fileExt':'jpg,mp4,png'
            }
            files = [('files[]', open(file_path, 'rb')) for file_path in img_paths]
            response = requests.post(url, params=params, data=data, files=files).json()
            logger.info("kcdn 返回结果 {}".format(response))
            return response
        except Exception as e:
            print (e)
            return None

    def uploadSingleCdnFile(self, filename, img_path):
        url = 'https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/single?'
        params = {
            'token' : '112363_767ee3ffce9f13f8f1612c42a8baafae'
        }
        data = {
            'pid' : 'tianheTestPaltform',
            'filename' : filename,
            'allowHash': 'false',
            'allowRewrite': 'false',
            'allowMD5': 'false',
            'requestInfo.uploaderType': 2,
            'requestInfo.serviceName':'public-uploadTest',
            'requestInfo.requestUri':'/rest/upolad1',
            'requestInfo.fileExt':'jpg,mp4,png'
        }
        with open(img_path, 'rb') as f:
            files = {'file': f}  # 'file' 是表单字段的名称
            response = requests.post(url, params=params, data=data, files=files).json()
            print ("response, ", response)

    def get_excute_info(self):
        config_str = self.var1
        # config_str = config_str.replace("@@@@", "&")
        return config_str.split('@@@')

    def executeAction(self, action, xpath, inputData, submitXpath):
        """ 0 表示页面查询， 1表示组件点击 2表示条件筛选查询"""
        """页面查询 无需操作"""
        if actionMap[action] == "query":
            self.sleep(5)
        elif actionMap[action] == "click":
            self.click(xpath)
            self.sleep(5)
        elif actionMap[action] == "selectAndQuery":
            """ 先做内容填充 """
            self.click(xpath)
            self.type(xpath, inputData)
            self.sleep(5)
            """ 点击查询按钮 """
            self.click(submitXpath)
        return

    def get_img(self, url, actions, xpaths, basePath, inputDatas,
                submitXpaths, storeName, envType):
        if len(url) > 0:
            """  跳转 Url """
        self.open(url)
        self.common_page_solve(10)
        img_paths = []
        i = 0
        for action, xpath, inputData, submitXpath in zip(actions, xpaths, inputDatas, submitXpaths):
            """  执行操作  """
            print ("execute action {} {} {} {} ", action, xpath, inputData, submitXpath)
            self.executeAction(action, xpath, inputData, submitXpath)
            """  截图 """
            # prt_path = basePath + 'prt_' + storeName
            localPath = basePath  + envType + storeName + str(i) + ".png"
            self.save_screenshot(localPath)
            logger.info("保存图片到本地成功")
            img_paths.append(localPath)
            i = i + 1
        return img_paths


    def get_img_self_detection(self, url, actions, xpaths, inputDatas,
                               submitXpaths, basePath, storeName):
        if len(url) > 0:
            """  跳转 Url """
        # img_paths_before = []
        # img_paths = []
        test_img_paths = []
        target_img_paths = []
        i = 0
        """ 每次执行动作前先打开页面"""
        for action, xpath, inputData, submitXpath in zip(actions, xpaths, inputDatas,
                                                          submitXpaths):
            """  执行操作  """
            print ("execute action {} {}", action, xpath)
            self.open(url)
            self.common_page_solve(10)
            """ 执行前 截图"""
            testImgLocalPath = basePath  + "prt_" + storeName + str(i) + ".png"
            self.save_screenshot(testImgLocalPath)
            logger.info("保存图片到本地成功")
            test_img_paths.append(testImgLocalPath)
            if self.is_element_visible(xpath):
                self.executeAction(action, xpath, inputData, submitXpath)
            """  截图 """
            # prt_path = basePath + 'prt_' + storeName
            targetimgLocalPath = basePath  + "online_" + storeName + str(i) + ".png"
            self.save_screenshot(targetimgLocalPath)
            logger.info("保存图片到本地成功")
            target_img_paths.append(targetimgLocalPath)
            i = i + 1
        return test_img_paths, target_img_paths
    def check_component_exist(self, actions, xpaths, pytestCodes, inputDatas, submitXpaths):
        exist_xpaths = []
        exist_actions = []
        exist_pytest_codes = []
        exist_inputDatas = []
        exist_submitXpaths = []
        for i in range(len(xpaths)):
            # print (" current check component {}".format(xpaths[i]))
            if actionMap[actions[i]] == "query":
                exist_actions.append(actions[i])
                exist_xpaths.append(xpaths[i])
                exist_pytest_codes.append(pytestCodes[i])
                exist_inputDatas.append(inputDatas[i])
                exist_submitXpaths.append(submitXpaths[i])
            elif self.is_element_visible(xpaths[i]):
                # print (" current check exist component {}".format(xpaths[i]))
                exist_actions.append(actions[i])
                exist_xpaths.append(xpaths[i])
                exist_pytest_codes.append(pytestCodes[i])
                exist_inputDatas.append(inputDatas[i])
                exist_submitXpaths.append(submitXpaths[i])
            else:
                # print (" current check not exist component {}".format(xpaths[i]))
                pass
        return exist_actions, exist_xpaths, exist_pytest_codes, exist_inputDatas, exist_submitXpaths

    def setLaneId(self, laneId):
        if laneId != "":
            # self.driver = webdriver.Chrome()
            self.driver.header_overrides = {"trace-context": '{"laneId":"' + laneId + '"}'}
    # @pytest.mark.skip
    def test_tianhe_artificial_sample(self):
        """ AI 运行模板 普通用例"""
        isrun, accountNo, pwd, prtUrl, onlineUrl, storeName, executeId, sampleId, testLaneId, \
            targetLaneId = \
            self.get_excute_info()
        if (isrun == 'false'):
            return
        # accountNo = "***********"
        # pwd = "csr123456"
        # prtUrl = "https://baomai-data.prt.kuaishou.com/sop/data/seller/seller?changeId=1176508"
        # onlineUrl = "https://baomai-merchant.corp.kuaishou.com/page/industryTools/seller"
        # sampleId = "881"
        # storeName = "881_be5b6e35-85d0-4042-b49a-c0658980662a.png"
        # executeId = "be5b6e35-85d0-4042-b49a-c0658980662a"
        # laneId = "PRT.yctest"

        # 解析actionSet
        caseData = self.queryActionSetById(sampleId)
        caseData = json.dumps(caseData)
        caseData = json.loads(caseData)

        # 初始化两个数组
        actions = []
        xpaths = []
        inputDatas = []
        submitXpaths = []
        """ 需要从这边解析出actions 和  xpaths"""
        # 遍历字典并提取值
        for i in range(len(caseData)):
            actions.append(caseData[i]["actionType"])
            xpaths.append(caseData[i]["xpath"])
            inputDatas.append(caseData[i]["inputData"])
            submitXpaths.append(caseData[i]["submitXpath"])

        print ("actions", actions)
        self.init_login(prtUrl)
        self.sleep(5)
        prt_img_paths = []
        online_img_paths = []
        basePath = "C:\\Users\\<USER>\\Desktop\\kwaishopuiautotest\\tianhe\\"
        # basePath = "/Users/<USER>/Desktop/aitest/"
        if onlineUrl != "" and prtUrl != "":
            self.setLaneId(targetLaneId)
            online_img_paths = self.get_img(onlineUrl, actions, xpaths, basePath, inputDatas,
                                            submitXpaths, storeName, 'base_')
            self.setLaneId(testLaneId)
            prt_img_paths = self.get_img(prtUrl, actions, xpaths, basePath, inputDatas,
                                         submitXpaths, storeName, 'prt_')
            print (prt_img_paths)
            print (online_img_paths)
            for i in range(len(prt_img_paths)):
                detect_img, diffRate = self.detect(prt_img_paths[i], online_img_paths[i])
                """  截图 , 取名用onlline，历史逻辑"""
                detect_path = basePath + 'online_'  + storeName + str(i) + ".png"
                cv2.imwrite(detect_path, detect_img)
                upload_imgs = []
                upload_imgs.append(prt_img_paths[i])
                upload_imgs.append(detect_path)
                """ 每一个动作比对完后就回调测试平台，更新状态"""
                if (self.uploadMultiCdnFile(upload_imgs)):
                    """ todo 这里会有问题，比如有多个页面查询动作， action和xpath是一样的， 不是唯一的"""
                    self.sendExecuteInfo(executeId, sampleId, actions[i], xpaths[i], diffRate)
                else:
                    print("图片上传失败")


    def test_tianhe_ai_sample(self):
        """ AI 运行模板 智能用例"""
        isrun, accountNo, pwd, prtUrl, storeName, executeId, sampleId, testLaneId = self.get_excute_info()
        if (isrun == 'false'):
            return
        # accountNo = "***********"
        # pwd = "csr123456"
        # prtUrl = "https://baomai-data.prt.kuaishou.com/sop/data/seller/seller?changeId=1176508"
        # onlineUrl = "https://baomai-merchant.corp.kuaishou.com/page/industryTools/seller"
        # sampleId = "881"
        # storeName = "881_be5b6e35-85d0-4042-b49a-c0658980662a.png"
        # executeId = "be5b6e35-85d0-4042-b49a-c0658980662a"
        # laneId = "PRT.yctest"

        # 解析actionSet
        caseData = self.queryActionSetById(sampleId)
        caseData = json.dumps(caseData)
        caseData = json.loads(caseData)

        # 初始化两个数组
        actions = []
        xpaths = []
        inputDatas = []
        submitXpaths = []
        """ 需要从这边解析出actions 和  xpaths"""
        # 遍历字典并提取值
        for i in range(len(caseData)):
            actions.append(caseData[i]["actionType"])
            xpaths.append(caseData[i]["xpath"])
            inputDatas.append(caseData[i]["inputData"])
            submitXpaths.append(caseData[i]["submitXpath"])

        print ("actions", actions)
        self.init_login(prtUrl)
        self.sleep(5)
        basePath = "C:\\Users\\<USER>\\Desktop\\kwaishopuiautotest\\tianhe\\"
        # basePath = "/Users/<USER>/Desktop/aitest/"
        if prtUrl != "":
            self.setLaneId(testLaneId)
            test_img_paths, target_img_paths = self.get_img_self_detection(prtUrl, actions,
                                                                           xpaths, inputDatas,
                                                                           submitXpaths, basePath,
                                                                           storeName)
            for i in range(len(test_img_paths)):
                upload_imgs = []
                upload_imgs.append(test_img_paths[i])
                upload_imgs.append(target_img_paths[i])
                diffRate = 0.0
                """ 每一个动作比对完后就回调测试平台，更新状态"""
                if (self.uploadMultiCdnFile(upload_imgs)):
                    """ 返回动作前和动作后的信息 """
                    self.sendExecuteInfo(executeId, sampleId, actions[i], xpaths[i], diffRate)
                else:
                    print("图片上传失败")




    # @pytest.mark.skip
    @pytest.mark.p1
    def test_tianhe_ai_check(self):
        # return
        isrun, accountNo, pwd, prtUrl, sampleId, testLaneId = \
            self.get_excute_info()
        if (isrun == 'false'):
            return
        # prtUrl = "https://cps-kwaixiaodian.test.gifshow.com/pc/leader/tianhe/leader-apply/experience-Indicators	"
        # sampleId = "870"
        caseData = self.queryActionSetById(sampleId)
        caseData = json.dumps(caseData)
        caseData = json.loads(caseData)

        # 初始化两个数组
        actions = []
        xpaths = []
        pytestCodes = []
        inputDatas = []
        submitXpaths = []
        """ 需要从这边解析出actions 和  xpaths"""
        # 遍历字典并提取值
        for i in range(len(caseData)):
        # for i in range(2):
        #     curAction = caseData[i]
            actions.append(caseData[i]["actionType"])
            xpaths.append(caseData[i]["xpath"])
            pytestCodes.append(caseData[i]["pytestCode"])
            inputDatas.append(caseData[i]["inputData"])
            submitXpaths.append(caseData[i]["submitXpath"])
        print ("actions", actions)
        print ("xpaths", xpaths)
        print ("pytestCode", pytestCodes)
        if testLaneId != "":
            self.setLaneId(testLaneId)
        self.loginAI(prtUrl)
        self.sleep(5)
        exist_actions, exist_xpaths, exist_pytestCodes, exist_inputDatas, exist_submitDatas = \
            self.check_component_exist(actions, xpaths, pytestCodes, inputDatas, submitXpaths)
        solvedActionSets = []
        for i in range(len(exist_actions)):
            print ("exist_actions {} {}".format(i, exist_actions[i]))
            print ("exist_xpath {}".format(exist_xpaths[i]))
            current_actionSet = {"actionType":exist_actions[i], "xpath": exist_xpaths[i],
                                 "pytestCode":exist_pytestCodes[i], "inputData": exist_inputDatas[
                    i], "submitXpath":exist_submitDatas[i]}
            solvedActionSets.append(current_actionSet)
        """ 回调用天河测试平台 """
        self.updateActionSet(sampleId, solvedActionSets)


    @pytest.mark.skip
    def test_dd(self):
        """ """
        return
        """ 更新actionSet 调用"""
        actionSet = [{
            "actionType": "0",
            "xpath": "",
            "pytestCode": ""
        },
        {
            "actionType": "1",
            "xpath": "//span[contains(text(),'查看详情')]",
            "pytestCode": "self.click(\"//span[contains(text(),'查看详情')]\")"
        },
        {
            "actionType": "1",
            "xpath": "//*[contains(text(),'标题')]",
            "pytestCode": "self.click(\"//*[contains(text(),'标题')]\")"
        },
        {
            "actionType":"1",
            "xpath":"//input[@placeholder='开始时间']",
            "pytestCode":"self.click(\"//input[@placeholder='开始时间']\")"
        }
        ]

        self.updateActionSet(870, actionSet)