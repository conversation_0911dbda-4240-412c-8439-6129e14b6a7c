"""
 @Author: yin<PERSON><PERSON>
 @Date: 2022/12/13
"""
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase, get_driver
from selenium import webdriver
from time import sleep
from json import dumps
# from seleniumwire import webdriver

class TestMerchantRecruit(BaseCase):
    def setUp(self):
        super(TestMerchantRecruit, self).setUp()


    def login(self, account):

        account_data = get_account_info(account)
        host = get_domain('WB_DOMAIN')
        self.open(host)


        self.driver.maximize_window()
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

        # 同一手机号绑定多个账号时需要二次点击登录
        self.sleep(1)
        self.click_if_visible('//*[@id="root"]/div/div[2]/div/div[1]/div/div[2]/div[3]/div[1]')

        self.sleep(1)
        # 新手引导点击
        for i in range(1, 5):
            sleep(0.5)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
                sleep(0.5)

        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
