"""
 @Author: wb_zhuyingying
 @Date: 2024/1/16
"""
from ddt import ddt
from .base import TestMerchantRecruit
from selenium import webdriver
from selenium.webdriver.common.by import By
import pytest
from unittest import skip, skipIf

@ddt
class TestMerchantRecruit(TestMerchantRecruit):
    
    def openurl(self):
      if self.var2 == "prt":
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/store/create/guide")
      else:
        self.open("https://s.kwaixiaodian.com/zone/store/create/guide")

    def test_guandian_dianpu(self):
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营", '//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      #点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)


      #点击基础信息tab
      self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[1]/div[1]/div/div[1]/div")
      self.sleep(3)


      # 断言页面元素是否存在
      self.assert_text("店铺信息", "(//div[contains(text(),'店铺信息')])[1]")
      self.assert_text("店铺名称", "(//div[@class='zMIBGRRZRRGw6vdK1IFI'][contains(text(),'店铺名称')])[1]")
      self.assert_text("lihongmianl",  "(//div[@class='innertd using'])[1]")
      self.assert_text("店铺类型", "(//div[@class='zMIBGRRZRRGw6vdK1IFI'][contains(text(),'店铺类型')])[1]")
      self.assert_text("普通企业店", "(//span[contains(text(),'普通企业店')])[1]")
      self.sleep(2)


      self.assert_text("快手小店店铺链接", "(//div[contains(text(),'快手小店店铺链接')])[1]")
      self.assert_text("店铺二维码","(//div[contains(text(),'店铺二维码')])[1]")
      self.assert_text("账号信息","(//div[@class='Q1BEI_qbeBVRIEWHvEro'])[1]")
      self.assert_text("账号昵称","(//div[contains(text(),'账号昵称')])[1]")


      self.assert_text("管理员信息", "(//div[contains(text(),'管理员信息')])[1]")
      self.assert_text("管理员电话", "(//div[contains(text(),'管理员电话')])[1]")
      self.assert_text("管理员地址", "(//div[contains(text(),'管理员地址')])[1]")
      self.assert_text("客服电话", "(//div[contains(text(),'客服电话')])[1]")
      self.sleep(2)


      # 点击新手期管理
      self.click("(//div[@id='rc-tabs-0-tab-9'])[1]")
      self.sleep(3)


    def test_guandian_dianpuxinshouqi(self):
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)


      # 点击新手期管理
      self.click("(//div[@id='rc-tabs-0-tab-9'])[1]")
      self.sleep(3)
      self.assert_text("当前状态：新手期已毕业", "(//h3[contains(text(),'当前状态：新手期已毕业')])[1]")
      self.assert_text("经营限制", "(//h4[contains(text(),'经营限制')])[1]")
      self.assert_text("当前经营不受限制", "(//div[@class='BCTq63aNU6q8J9LgGJmA'])[1]")


      #点击更多说明
      self.click("(//a[contains(text(),'更多说明')])[1]")
      self.sleep(2)
      handles = self.driver.window_handles
      self.driver.switch_to.window(handles[-1])
      # 校验跳转后链接是否正确
      self.assertEqual("https://edu.kwaixiaodian.com/rule/web/detail?id=mXXJf2Cxni",self.get_current_url())
      self.switch_to_window(0)

    def test_guandian_zhuti(self):
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)


      # 点击主体变更
      self.click('//*[@id="rc-tabs-0-tab-10"]')
      self.sleep(3)
      self.assert_text("主体变更需知", '//*[@id="root"]/div/div[1]/h1')
      self.assert_text("升级条件", '//*[@id="root"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[1]')
      self.assert_text("当前状态", '//*[@id="root"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[2]')
      self.assert_text("操作", '//*[@id="root"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[3]')
      self.assert_text("店铺无异常", '//*[@id="root"]/div/div[2]/div/div/div/div/div/table/tbody/tr[1]/td[1]/span')
      self.assert_text("资金无异常", '//*[@id="root"]/div/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[1]/span')
      self.assert_text("无未开发票", '//*[@id="root"]/div/div[2]/div/div/div/div/div/table/tbody/tr[3]/td[1]/span')


    def test_guandian_zhuti02(self):
      #点击申请主体变更
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)

      # 点击主体变更
      self.click('//*[@id="rc-tabs-0-tab-10"]')
      self.sleep(3)
      
      # 点击申请主体变更
      self.click('//*[@id="root"]/div/div[3]/button/span')
      self.sleep(3)
      self.assert_text("选择变更类型", '//*[@id="root"]/div[1]/div[1]/div/div[3]/div')
      self.assert_text("企业主体信息", '//*[@id="root"]/div[1]/div[2]/div/div[3]/div')
      self.assert_text("店铺经营信息", '//*[@id="root"]/div[1]/div[3]/div/div[3]/div')
      self.assert_text("平台审核", '//*[@id="root"]/div[1]/div[4]/div/div[3]/div')
      self.assert_text("真实性认证", '//*[@id="root"]/div[1]/div[5]/div/div[3]/div')
      self.assert_text("更新收款方式", '//*[@id="root"]/div[1]/div[6]/div/div[3]/div')

      self.assert_text("1.注意事项", '//*[@id="root"]/div[2]/div[1]/h3')
      self.assert_text("2.选择变更类型", '//*[@id="root"]/div[2]/div[2]/h3')
      self.assert_text("3.验证账户", '//*[@id="root"]/div[2]/div[3]/h3')

      self.assert_text("取消变更", '//*[@id="root"]/div[3]/div/div[1]/button/span')
      self.assert_text("下一步", '//*[@id="root"]/div[3]/div/div[2]/button/span')

      #点击取消变更
      self.click("(//span[contains(text(),'取消变更')])[1]")
      # focus 弹窗
      self.switch_to_window(0)
      self.click("(//span[contains(text(),'取消变更')])[3]")
      self.sleep(3)
      self.assert_text("主体变更需知", '//*[@id="root"]/div/div[1]/h1')

    def test_guandian_upgrade(self):
      #店铺升级
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)

      # 点击店铺升级
      self.click('//*[@id="rc-tabs-0-tab-6"]')
      self.sleep(3)
      self.switch_to_window(0)
      self.assert_text("店铺升级", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[1]/div[1]/h2')
      self.assert_text("申请升级", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[1]/div[1]/div/button/span')
      self.assert_text("店铺类型介绍", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[1]/div[1]/p/a')
      self.assert_text("企业主体店铺升级指南", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[2]/div[1]/span/a')
      self.assert_text("升级流程", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[2]/div[1]/h2/span')
      self.assert_text("01 选择升级类型", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[2]/div[2]/div[1]/span[1]')
      self.assert_text("02 店铺经营信息", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[2]/div[2]/div[2]/span[1]')
      self.assert_text("03 平台审核", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[2]/div[2]/div[3]/span[1]')
      self.assert_text("注意事项", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[3]/div[1]/h2/span')

      self.assert_text("条件验证", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[4]/div[1]/h2/span')
      self.assert_text("变更条件", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[4]/div[2]/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')
      self.assert_text("当前状态", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[4]/div[2]/div/div/div/div/div/div/div/div/table/thead/tr/th[2]')
      self.assert_text("操作建议", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[4]/div[2]/div/div/div/div/div/div/div/div/table/thead/tr/th[3]')


      self.assert_text("当前店铺类型允许升级（普通企业店）", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/span')
      self.assert_text("店铺主体更新流程", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/span')
      self.assert_text("行业资质变更流程", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[1]/span')
      self.assert_text("品牌资质变更流程", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[1]/span')
      self.assert_text("店铺关店流程", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[5]/td[1]/span')
      self.assert_text("基础信息变更流程", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[6]/td[1]/span')
      self.assert_text("主体变更流程", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[7]/td[1]/span')
      self.assert_text("店铺形象号分销权限校验", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[8]/td[1]/span')
      self.assert_text("未在定邀类目审核流程中", '//*[@id="condition"]/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr[9]/td[1]/span')

    def test_guandian_upgrade02(self):
      #店铺升级点击申请升级
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)

      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)


      # 点击店铺升级
      self.click('//*[@id="rc-tabs-0-tab-6"]')
      self.sleep(3)
      self.switch_to_window(0)

      # 点击申请升级
      self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[4]/div/div/div/div[1]/div[1]/div/button/span')
      self.sleep(10)

      self.assert_text("选择店铺类型", '//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/div[1]/div/div[3]/div')
      self.assert_text("店铺经营信息", '//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/div[2]/div/div[3]/div')
      self.assert_text("平台审核", '//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/div[3]/div/div[3]/div')
      self.assert_text("店铺类型介绍", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[1]/a')

      self.assert_text("选择目标店铺类型", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[1]/h1')
      self.assert_text("专营店", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[2]/div/div/div[1]/div[1]')
      self.assert_text("专卖店", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[2]/div/div/div[2]/div[1]')
      self.assert_text("旗舰店", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[2]/div/div/div[3]/div[1]')
      self.assert_text("卖场型旗舰店", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[2]/div/div/div[4]/div[1]')

      self.assert_text("验证账户", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/form/div/h1')
      self.assert_text("管理员手机号", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/form/div/div/div[1]/div[1]/label')
      self.assert_text("验证码", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/form/div/div/div[2]/div[1]/label')

      self.assert_text("取消升级", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/button[1]/span')
      self.assert_text("下一步", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/button[2]/span')

      #点击取消升级按钮
      self.click('//*[@id="root"]/section/main/div/div/div[2]/div[2]/button[1]/span')
      self.switch_to_window(0)
      self.click("//*[text()='放弃升级']")
      self.sleep(2)

    
    def test_guandian_close(self):
      #关店页
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击店铺信息设置
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_7M3Lnv6qWj8_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)

      # 点击关店
      self.click("//div[contains(text(),'关店')]")
      self.sleep(3)
      
      self.assert_text("身份验证", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[1]/div[1]/div/div[3]/div')
      self.assert_text("申请关店", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[1]/div[2]/div/div[3]/div')
      self.assert_text("资金提现", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[1]/div[3]/div/div[3]/div')
      self.assert_text("关店成功", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[1]/div[4]/div/div[3]/div')
      self.assert_text("查看关店流程说明", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/a')

      self.assert_text("请阅读关店注意事项并验证身份", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[2]/div[1]')
      self.assert_text("1.请阅读关店注意事项", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[2]/div[2]/div')
      self.assert_text("2.请勾选您的关店原因(多选)", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[2]/div[3]/div[1]')
      self.assert_text("3.请验证您的账户", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[2]/div[4]/div[1]')



      self.assert_text("管理员手机号", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[2]/div[4]/div[2]/span')
      self.assert_text("短信验证码", '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[2]/div/div[5]/div/div/div/div[2]/div[4]/div[3]/span')

    def test_management_menu(self):
      #店铺管理-资质管理菜单
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)


      #点击资质管理
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_erJF2N6Tmhw_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)

      #点击主体信息
      self.click("(//div[@class='ant-tabs-tab-active ant-tabs-tab'])[1]")
      self.sleep(3)



      # 断言页面元素是否存在
      self.assert_text("主体信息", "(//div[@class='ant-tabs-tab-active ant-tabs-tab'])[1]")
      self.assert_text("达人主体信息", "(//div[contains(text(),'达人主体信息')])[1]")
      self.assert_text("品牌资质", "(//div[contains(text(),'品牌资质')])[1]")
      self.assert_text("行业资质", "(//div[contains(text(),'行业资质')])[1]")




    def test_zhuti_info(self):
      #店铺管理-资质管理菜单
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)

      #点击资质管理
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_erJF2N6Tmhw_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)


      #点击主体信息
      self.click('//*[@id="root"]/section/main/div/div[1]/div/div/div/div/div[1]/div[1]')
      self.sleep(3) 

      #断言主体信息页面元素是否存在
      self.assert_text("开具经营证明", '//*[@id="root"]/section/main/div/div[3]/div[1]/div[2]/div/div/div[2]/div/div[2]/button[1]')
      self.assert_text("编 辑", '//*[@id="root"]/section/main/div/div[3]/div[1]/div[2]/div/div/div[2]/div/div[2]/button[2]')


      self.assert_text("当前生效中主体信息", "(//div[@class='content-header-title'])[1]")
      self.assert_text("法定代表人信息", "(//div[contains(text(),'法定代表人信息')])[1]")
      self.assert_text("法定代表人信息", "(//div[contains(text(),'法定代表人信息')])[1]")
      self.assert_text("证件类型", "(//div[contains(text(),'证件类型')])[1]")
      self.assert_text("法定代表人证件号码", "(//div[contains(text(),'法定代表人证件号码')])[1]")
      self.assert_text("法定代表人证件有效期", "(//div[contains(text(),'法定代表人证件有效期')])[1]")
      self.assert_text("法定代表人证件", "(//div[@class='item-title'][contains(text(),'法定代表人证件')])[3]")

      self.assert_text("企业信息", "(//div[contains(text(),'企业信息')])[1]")
      self.assert_text("企业名称", "(//div[contains(text(),'企业名称')])[1]")
      self.assert_text("统一社会信用代码", "(//div[contains(text(),'统一社会信用代码')])[1]")
      self.assert_text("类型", "(//div[@class='item-title'][contains(text(),'类型')])[2]")
      self.assert_text("注册资本", "(//div[contains(text(),'注册资本')])[1]")
      self.assert_text("公司注册地址", "(//div[contains(text(),'公司注册地址')])[1]")
      self.assert_text("营业期限", "(//div[contains(text(),'营业期限')])[1]")
      self.assert_text("营业执照", "(//div[contains(text(),'营业执照')])[1]")




    def test_zhuti_manage(self):
      #店铺管理-资质管理菜单
      self.login("gd_login")
      self.sleep(2)
      self.openurl()
      # 断言页面元素是否存在
      self.assert_text("完成以下工作即可获得 新商体验分，开启店铺经营",'//*[@id="sellerPrepareShopHighlightPc#C1"]/div/h1')
      self.sleep(3)
      # 点击店铺
      self.click("//span[contains(text(),'店铺')]")
      self.sleep(3)

      #点击资质管理
      self.click("(//span[@id='pinned_menu_for_intersectionObserver_erJF2N6Tmhw_under_nB1uqX31G8Q'])[1]")
      self.sleep(3)

      #点击主体信息
      self.click('//*[@id="root"]/section/main/div/div[1]/div/div/div/div/div[1]/div[1]')
      self.sleep(3) 

      # 点击编辑
      self.click('//*[@id="root"]/section/main/div/div[3]/div[1]/div[2]/div/div/div[2]/div/div[2]/button[2]')

      # 断言页面元素是否存在
      self.assert_text("编辑主体信息", '//*[@id="root"]/section/main/div/div[2]/div[1]/div/div/div[1]/div/div[3]/div')
      self.assert_text("平台审核", '//*[@id="root"]/section/main/div/div[2]/div[1]/div/div/div[2]/div/div[3]/div')
      self.assert_text("真实性认证", '//*[@id="root"]/section/main/div/div[2]/div[1]/div/div/div[3]/div/div[3]/div')

      self.assert_text("法定代表人信息", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[1]/div')
      self.assert_text("证件归属地", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[1]/form/div/div/div[1]/div/div/span/div/div/div[1]/label')
      self.assert_text("上传中国大陆居民身份证", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[1]/form/div/div/div[2]/div[1]/label')
      self.assert_text("姓名", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[1]/form/div/div/div[3]/div[1]/label')
      self.assert_text("证件号", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[1]/form/div/div/div[4]/div[1]/label')
      self.assert_text("证件有效期", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[1]/form/div/div/div[5]/div[1]/label')

      self.assert_text("企业信息", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[2]/div')
      self.assert_text("营业执照", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/section[2]/form/div/div[1]/label')

      self.assert_text("取 消", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/div/button[1]/span')
      self.assert_text("提交申请", '//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/div/button[2]/span')

      #点击提交申请
      self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/div/button[2]/span')
      self.switch_to_window(0)
      self.click("//*[text()='取 消']")


      # 点击取消
      self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div/div/div/button[1]/span')
      self.switch_to_window(0)
      self.click("//*[text()='确 定']")







      


if __name__ == '__main__':
    pytest.main()