"""
 @Author: wb_zhuyingying
 @Date: 2024/1/16
"""
from ddt import ddt
from .base import TestMerchantRecruit
from selenium import webdriver
import pytest
from unittest import skip, skipIf


@ddt
class TestMerchantRecruit(TestMerchantRecruit):
    
    def openurl(self):
      if self.var2 == "prt":
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/store/create/guide")
      else:
        self.open("https://s.kwaixiaodian.com/zone/store/create/guide")


    # pc个人店后续要换一个账号加一个个人店的无实名认证case
    # 个人店无实名认证case，这个账号跟ui压力自动化公用一个账号，所以不要换账号页面
    def test_ruzhu_personal_01(self):
      self.login("rz_login02")
      self.sleep(2)
      self.openurl()
      

      # 判断个人店页面元素是否存在
      self.assert_text("证件类型", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/form/div[1]/div/div[1]/label')
      self.assert_text("证件照片", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/form/div[2]/div/div[1]/label')
      self.assert_text("店铺名称", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[2]/div[2]/form/div[1]/div/div[1]/label')
      

      # 这里文案配错了，等前端修复
      self.assert_text("经营者信息", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/div/span')
      self.assert_text("店铺命名", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[2]/div[1]/span')


    def test_ruzhu_01(self):
      self.login("rz_login")
      self.sleep(2)
      self.openurl()
      

      # # 判断个人店页面元素是否存在
      # 实名认证取消了
      # self.assert_text("姓名", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/form/div[3]/div[1]/div/div[1]/label')
      # self.assert_text("证件号", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/form/div[3]/div[2]/div/div[1]/label')
      # self.assert_text("人脸识别认证", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/form/div[4]/div/div[2]/div[1]')
      # self.assert_text("店铺名称", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[2]/div[2]/form/div/div/div[1]/label')
      

      # # 这里文案配错了，等前端修复
      # self.assert_text("经营者信息", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/div/span[1]')
      # self.assert_text("实名认证", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[1]/form/div[4]/div/div[1]/span')           
      # self.assert_text("店铺命名", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[2]/div[1]/span[1]')

      # 点击上一步与挽留弹窗
      self.click('//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[4]/button[1]')
      self.sleep(2)
      self.switch_to_window(0)
      self.click("//div[contains(text(),'放弃入驻')]")
      self.sleep(3)


      # 断言页面元素是否存在
      self.assert_text("欢迎入驻快手小店", '//*[@id="root"]/section/main/div/div/div[1]/span[1]')

      self.assert_text("入驻进度订阅", '//*[@id="header-item"]')
      self.assert_text("入驻类型说明", '//*[@id="root"]/section/main/div/div/div[1]/span[2]/a[2]')
      self.assert_text("入驻手册", '//*[@id="root"]/section/main/div/div/div[1]/span[2]/a[3]')

      self.assert_text("0元开店", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/div[1]/div[2]')
      self.assert_text("流量扶持", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/div[2]/div[2]')
      self.assert_text("运费险补贴", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/div[3]/div[2]')
      self.assert_text("商业化激励", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/div[4]/div[2]')
      self.assert_text("免费营销托管", '//*[@id="root"]/section/main/div/div/div[2]/div[2]/div[5]/div[2]')

      self.assert_text("请选择主体类型", '//*[@id="root"]/section/main/div/div/div[3]')
      self.assert_text("个人", '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[1]/div[2]')
      self.assert_text("个体工商户", '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[2]/div[2]')
      self.assert_text("企业店铺", '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[3]/div[2]')
      self.assert_text("跨境电商", '//*[@id="root"]/section/main/div/div/div[4]/div[3]/div[2]/div[2]')


      # 检验首页挽留弹窗
      # 点击个体工商店
      self.click('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[2]/div[4]/button')
      self.sleep(2)
      self.switch_to_window(0)

      self.assert_text("变更提醒", "//span[contains(text(),'变更提醒')]")
      self.assert_text("放弃修改", "//span[contains(text(),'放弃修改')]")
      self.assert_text("确认修改", "//span[contains(text(),'确认修改')]")

      self.click("//span[contains(text(),'放弃修改')]")

      # 检验个体工商店的页面内容
      self.click('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[2]/div[4]/button')
      self.sleep(2)

      self.click("//span[contains(text(),'确认修改')]")
      self.sleep(5)

      # 模块级别的改写
      self.assert_text("个体工商户营业证件信息", '//*[@id="enterpriseReason"]/div/span[1]')
      self.assert_text("经营者信息", '//*[@id="certificateReason"]/div/span[1]')
      self.assert_text("店铺命名", '//*[@id="shopReason"]/div[1]/span[1]')
      self.assert_text("管理员手机号", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[4]/div[1]/span[1]')

      #模块内具体内容
      self.assert_text("执照图片", '//*[@id="enterpriseReason"]/form/div[1]/div[1]/label')
      self.assert_text("证件类型", '//*[@id="certificateReason"]/form/div[1]/div/div[1]/label')
      self.assert_text("证件照片", '//*[@id="certificateReason"]/form/div[2]/div/div[1]/label')
      self.assert_text("店铺名称", '//*[@id="shopReason"]/div[2]/form/div/div/div[1]/label')
      self.assert_text("管理员手机号", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[4]/div[2]/form/div/div/div[1]/label')

      # 点击上一步与挽留弹窗
      self.click("//span[contains(text(),'上一步')]")
      self.sleep(2)
      self.switch_to_window(0)
      self.click("//div[contains(text(),'放弃入驻')]")
      self.sleep(3)

    # 普通企业店
      self.click("(//button[@type='button'])[3]")
      self.sleep(2)


      #校验
      self.assert_text("企业入驻", "(//div[contains(text(),'企业入驻')])[1]")
      self.assert_text("普通店", "(//div[contains(text(),'普通店')])[1]")
      self.assert_text("旗舰店", "(//div[contains(@class,'shop-name')][contains(text(),'旗舰店')])[1]")
      self.assert_text("专卖店", "(//div[contains(text(),'专卖店')])[1]")
      self.assert_text("专营店", "(//div[contains(text(),'专营店')])[1]")
      self.assert_text("卖场型旗舰店", "(//div[contains(text(),'卖场型旗舰店')])[1]")


      #点击普店店铺
      self.click("(//div[@class='shop-round'])[3]")
      self.sleep(2)

      #点击确认
      self.click("(//button[@class='ant-btn ant-btn-primary shopType_modal_btn_item___hbxVY'])[1]")
      self.sleep(2)

      # 校验普通企业店内容
      self.assert_text("普通企业店营业证件信息", '//*[@id="enterpriseReason"]/div/span[1]')
      self.assert_text("法人信息", '//*[@id="certificateReason"]/div/span[1]')
      self.assert_text("店铺命名", '//*[@id="shopReason"]/div[1]/span[1]')
      self.assert_text("管理员手机号", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[4]/div[1]/span[1]')

      #模块内具体内容
      self.assert_text("执照图片", '//*[@id="enterpriseReason"]/form/div[1]/div[1]/label')
      self.assert_text("证件类型", '//*[@id="certificateReason"]/form/div[1]/div/div[1]/label')
      self.assert_text("证件照片", '//*[@id="certificateReason"]/form/div[2]/div/div[1]/label')
      self.assert_text("店铺名称", '//*[@id="shopReason"]/div[2]/form/div/div/div[1]/label')
      self.assert_text("管理员手机号", '//*[@id="dilu-micro-root"]/div/div/div/div/div[2]/div/div[4]/div[2]/form/div/div/div[1]/label')


     # 点击上一步与挽留弹窗
      self.click("(//span[contains(text(),'上一步')])[1]")
      self.sleep(2)
      self.switch_to_window(0)
      self.click("(//div[@class='giveUpButton___Fwl5o'])[1]")
      self.sleep(3)

    # 校验一个旗舰店
      self.click("(//button[@type='button'])[3]")
      self.sleep(2)


      # 点击旗舰店
      self.click("(//div[@class='shop-round'])[4]")
      self.sleep(2)


      #点击确定
      self.click("(//button[contains(@class,'ant-btn ant-btn-primary shopType_modal_btn_item___hbxVY')])[1]")
      self.sleep(3)



      # 点击展开详情
      self.click("(//span[contains(text(),'展开详情')])[1]")
      self.sleep(3)


      # 点击收起
      self.click("(//span[contains(text(),'收起')])[1]")
      self.sleep(3)

      #校验旗舰店
      self.assert_text("仅接受中国大陆企业入驻", "(//span[@class='subTitle___N6ueB'])[1]")
      self.assert_text("执照图片", "(//label[contains(text(),'执照图片')])[1]")
      self.assert_text("法人信息", "(//div[@class='blockTitle___pBebY'])[1]")
      self.assert_text("法人证件归属地", "(//label[contains(text(),'法人证件归属地')])[1]")
      self.assert_text("上传中国大陆居民身份证", "(//label[contains(text(),'上传中国大陆居民身份证')])[1]")


      # 点击修改店铺类型
      self.click("(//span[@class='editShopType___OOTuV'])[1]")
      self.sleep(3)

      # 校验弹窗内容
      self.assert_text("个人店", "(// div[contains(text(), '个人店')])[1]")
      self.assert_text("个体工商户", "(//div[contains(text(),'个体工商户店')])[1]")
      self.assert_text("企业入驻", "(//div[contains(text(),'企业入驻')])[1]")
      self.assert_text("普通店", "(//div[contains(text(),'普通店')])[1]")
      self.assert_text("旗舰店", "(//div[contains(@class,'shop-name')][contains(text(),'旗舰店')])[1]")
      self.assert_text("专卖店", "(//div[contains(text(),'专卖店')])[1]")
      self.assert_text("专营店", "(//div[contains(text(),'专营店')])[1]")
      self.assert_text("卖场型旗舰店", "(//div[contains(text(),'卖场型旗舰店')])[1]")


      # 点击取消弹窗
      self.click("(//button[contains(@class,'ant-btn shopType_modal_btn_item___hbxVY')])[1]")
      self.sleep(3)

      # 点击修改店铺类型
      self.click("(//span[@class='editShopType___OOTuV'])[1]")
      self.sleep(3)

      # 点击修改专卖店
      self.click("(//div[contains(@class,'shop-round')])[5]")
      self.sleep(3)

      # 点击确认
      self.click("(//button[contains(@class,'ant-btn ant-btn-primary shopType_modal_btn_item___hbxVY')])[1]")
      self.sleep(3)

      #校验为专卖店
      self.assert_text("专卖店", "(//span[@class='shopTypeName___T_Whf'])[1]")

      # 点击修改店铺类型
      self.click("(//span[@class='editShopType___OOTuV'])[1]")
      self.sleep(3)

      # 点击修改专营店
      self.click("(//div[contains(@class,'shop-round')])[6]")
      self.sleep(3)


      # 点击确认
      self.click("(//button[contains(@class,'ant-btn ant-btn-primary shopType_modal_btn_item___hbxVY')])[1]")
      self.sleep(3)


      #校验为专营店
      self.assert_text("专营店", "(//span[@class='shopTypeName___T_Whf'])[1]")


      # 点击修改店铺类型
      self.click("(//span[@class='editShopType___OOTuV'])[1]")
      self.sleep(3)

      # 点击修改卖场旗舰店
      self.click("(//div[@class='shop-round'])[7]")
      self.sleep(3)


      # 点击确认
      self.click("(//button[contains(@class,'ant-btn ant-btn-primary shopType_modal_btn_item___hbxVY')])[1]")
      self.sleep(3)


      #校验为卖场旗舰店
      self.assert_text("卖场型旗舰店", "(//span[@class='shopTypeName___T_Whf'])[1]")


      # 点击修改店铺类型
      self.click("(//span[@class='editShopType___OOTuV'])[1]")
      self.sleep(3)


      # 点击个人店
      self.click("(//div[contains(@class,'shop-round')])[1]")
      self.sleep(2)
      # 点击确认
      self.click("(//button[contains(@class,'ant-btn ant-btn-primary shopType_modal_btn_item___hbxVY')])[1]")
      self.sleep(3)

      # 点击上一步返回首页
      # self.click("(//button[@class='ant-btn'])[1]")
      # self.sleep(2)

      # 返回个人店
      # self.click("//span[contains(text(),'立即入驻')]")
      # self.click("//span[contains(text(),'确认修改')]")
      # self.sleep(5)

    # 入驻页顶部菜单栏校验
    # 不知道为什么不能通过，先注释
    # def test_ruzhu_top_menu(self):
      
    #   self.login("rz_login")
    #   self.sleep(2)
    #   self.openurl()

    #   # 菜单栏收起
    #   self.assert_text("新商家专属权益", '//*[@id="root"]/section/main/div[1]/div/div[1]/div[1]')
    #   self.assert_text("0元开店", '//*[@id="root"]/section/main/div[1]/div/div[2]/div[1]/div[1]/div[2]')
    #   self.assert_text("流量扶持", '//*[@id="root"]/section/main/div[1]/div/div[2]/div[1]/div[2]/div[2]')
    #   self.assert_text("运费险补贴", '//*[@id="root"]/section/main/div[1]/div/div[2]/div[1]/div[3]/div[2]')
    #   self.assert_text("商业化激励", '//*[@id="root"]/section/main/div[1]/div/div[2]/div[1]/div[4]/div[2]')
    #   self.assert_text("免费营销托管", '//*[@id="root"]/section/main/div[1]/div/div[2]/div[1]/div[5]/div[2]')

    #   # 菜单栏展开
    #   self.click("(//span[contains(text(),'展开详情')])[1]")

    #   self.assert_text("部分品类支持入驻后免保证金试运营", '//*[@id="root"]/section/main/div[1]/div[2]/div[1]/div[2]/div')
    #   self.assert_text("首周开播最高可得15000流量", '//*[@id="root"]/section/main/div[1]/div[2]/div[2]/div[2]/div')
    #   self.assert_text("开通30天内最高免100单服务费", '//*[@id="root"]/section/main/div[1]/div[2]/div[3]/div[2]/div')
    #   self.assert_text("最高享受4万元磁力金牛奖励", '//*[@id="root"]/section/main/div[1]/div[2]/div[4]/div[2]/div')
    #   self.assert_text("官方免费智能代运营商品", '//*[@id="root"]/section/main/div[1]/div[2]/div[5]/div[2]/div')
   
    #   #收起菜单栏
    #   self.click("(//span[contains(text(),'收起')])[1]")

    # # 专业店第二页
    # def test_ruzhu_proshop(self):
    #    self.login("rz_login03")
    #    self.sleep(2)
    #    self.openurl()
    #    self.sleep(3)

    #    # 点击下一步
    #    self.click("//span[contains(text(),'下一步')]")
    #    self.sleep(2)
    #    # 提醒弹窗，校验内容
    #    self.assert_text("提醒", '//span[contains(text(),"提醒")]')
    #    # 点击返回修改
    #    self.click("//span[contains(text(),'返回修改')]")
    #    self.sleep(3)
    #    # 点击下一步
    #    self.click("//span[contains(text(),'下一步')]")
    #    self.sleep(2)
    #    # 点击继续提交
    #    self.click("//span[contains(text(),'继续提交')]")
    #    self.sleep(2)
    #    # 校验页面元素
    #    # 这一部分要修改，所以先不校验
    #    # self.assert_text("类目信息", '')
    #    self.assert_text("品牌资质信息",'//*[@id="shopBrand"]/div[1]')
    #    self.assert_text("品牌名称",'/html/body/div[1]/div/div/div/div[2]/div/div/div/div/div/section/main/div/div/div/div[3]/div[2]/div[3]/div[2]/div/div[2]/form/div[2]/div[1]/div[1]/div[1]/label')
    #    self.assert_text("品牌类型",'/html/body/div[1]/div/div/div/div[2]/div/div/div/div/div/section/main/div/div/div/div[3]/div[2]/div[3]/div[2]/div/div[2]/form/div[3]/div/div[1]/label')
    #    self.assert_text("店铺基础信息",'//*[@id="shopBaseInfo"]/div')
    #    self.assert_text("店铺名称",'//*[@id="shopNameForm"]/div/div[1]/label')
    #    self.assert_text("管理员信息",'//*[@id="shopManagerInfo"]/div')
    #    self.assert_text("管理员手机号",'//*[@id="shopManagerInfo"]/form/div/div/div[1]/label')
    #    # 点击主营类目
    #    # 校验主营类目弹窗
    #    # 选择主营类目
    #    # 选择经营类目
    #    # 输入品牌信息
    #    self.type("input[placeholder='请输入商标注册号/申请号']", "2357")
    #    self.click("//span[contains(text(),'确认')]")
    #    # 自有品牌
    #    # 一级授权品牌
    #    # 二级授权品牌
    #    # 三级授权品牌


       



if __name__ == '__main__':
    pytest.main()