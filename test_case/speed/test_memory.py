import pytest
import time
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime

BaseCase.main(__name__, __file__)


class TestCustomerHome(BaseCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_customer_home_speed(self):
        "https://im.kwaixiaodian.com/pc"
        "客服-首页-工作台"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        # 记录打开网页前的时间戳
        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://im.kwaixiaodian.com/pc")



