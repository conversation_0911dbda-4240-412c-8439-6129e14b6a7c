import pytest
import time
from seleniumbase import BaseCase
import logging

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime

BaseCase.main(__name__, __file__)

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 创建控制台处理器并设置级别
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
# 将处理器添加到日志记录器
logger.addHandler(console_handler)


class TestDistributionHome(BaseCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_distribute_home_speed(self):
        "https://cps.kwaixiaodian.com/zone-cps/home"
        "分销-首页-工作台"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        # 记录打开网页前的时间戳
        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://cps.kwaixiaodian.com/zone-cps/home")
        # 等待元素出现的时间戳

        try:
            self.wait_for_element_visible("(//span[contains(text(),'数据概览')])[1]", timeout=10)  # 等待最多10秒

            self.wait_for_element_visible("//li[2]//div[1]//span[1]//span[1]//span[1]", timeout=10)  # 等待最多10秒

            self.wait_for_element_visible("(//span[contains(text(),'分销计划')])[1]", timeout=10)  # 等待最多10秒

            self.wait_for_element_visible("(//span[@class='index-module__title--VJMqL'][contains(text(),'寄样管理')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            logger.info("test_distribute_home_speed:" + str(load_time))

            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_distribute_home_speed", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_distribute_product_speed(self):
        "cps.kwaixiaodian.com/pc/promoter/selection-center/detail"
        "分销-商品详情-商品"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(3)
        #
        # # 记录打开网页前的时间戳
        # self.open("https://cps.kwaixiaodian.com/zone-cps/home")

        self.click("(//span[@class='index-module__earn--q_p3q'][contains(text(),'赚')])[1]")
        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        # 等待元素出现的时间戳

        try:
            # 加入货架按钮
            self.wait_for_element_visible("(//span[contains(text(),'加入货架')])[1]", timeout=10)  # 等待最多10秒

            # 店铺体验分
            self.wait_for_element_visible("(//div[@class='index-module__title--SMjE3'])[1]", timeout=10)  # 等待最多10秒

            # 联系商家按钮
            # self.wait_for_element_visible("(//span[contains(text(),'联系商家')])[1]", timeout=10)  # 等待最多10秒

            # 推广数据tab
            self.wait_for_element_visible(
                "(//div[@id='rc-tabs-0-tab-DATA_STATISTICS'])[1]", timeout=10)  # 等待最多10秒

            # 加入货架按钮
            self.wait_for_element_visible("(//span[contains(text(),'进店选品')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            logger.info("test_distribute_product_speed:" + str(load_time))
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_distribute_product_speed", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_distribute_promoter_home_speed(self):
        "cps.kwaixiaodian.com/pc/promoter/selection-center/home"
        "分销-选品中心-首页"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        # 记录打开网页前的时间戳
        self.open("cps.kwaixiaodian.com/pc/promoter/selection-center/home")

        # 等待元素出现的时间戳
        try:
            # 快分销热销榜单
            self.wait_for_element_visible("(//span[@class='index-module__title--QtQRv'])[1]", timeout=10)  # 等待最多10秒

            # 展示侧边栏
            self.wait_for_element_visible("//li[@id='menu-DtlkwzNwsoQ']//span[@class='dilu-main-badge']//span[1]", timeout=10)  # 等待最多10秒

            # 展示商品卡
            self.wait_for_element_visible("(//span[@class='index-module__earn--q_p3q'][contains(text(),'赚')])[4]", timeout=10)  # 等待最多10秒

            # 展示搜索框
            self.wait_for_element_visible("(//input[@id='rc_select_0'])[1]", timeout=10)  # 等待最多10秒

            # 展示搜索按钮
            self.wait_for_element_visible("(//span[contains(text(),'搜索')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            logger.info("test_distribute_promoter_home_speed:" + str(load_time))
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_distribute_promoter_home_speed", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_distribute_promoter_detail_speed(self):
        "cps.kwaixiaodian.com/pc/promoter/selection-center/detail"
        "分销-达人详情-经营数据"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(3)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        # 记录打开网页前的时间戳
        self.open("https://cps.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId=**********")
        # 等待元素出现的时间戳

        try:
            # 快分销热销榜单
            self.wait_for_element_visible("(//div[@class='pkuxfaxKVgw4mKMr97Od'][contains(text(),'粉丝数')])[1]", timeout=10)  # 等待最多10秒

            # 展示侧边栏
            self.wait_for_element_visible("(//span[contains(text(),'直播带货数据')])[1]", timeout=10)  # 等待最多10秒

            # # 展示商品卡
            # self.wait_for_element_visible("(//span[@class='kwaishop-cps-daren-match-pc-tooltip-disabled-compatible-wrapper'])[1]", timeout=10)  # 等待最多10秒
            #
            # # 展示搜索框
            # self.wait_for_element_visible("(//span[@class='kwaishop-cps-daren-match-pc-tooltip-disabled-compatible-wrapper'])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            logger.info("test_distribute_promoter_detail_speed:" + str(load_time))
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_distribute_promoter_detail_speed", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)

        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)


