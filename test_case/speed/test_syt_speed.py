import pytest
import time
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime

BaseCase.main(__name__, __file__)


class TestDistributionHome(BaseCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_syt_home(self):
        "https://syt.kwaixiaodian.com/zones/home"
        "生意通-首页-经营数据"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        # 记录打开网页前的时间戳
        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://syt.kwaixiaodian.com/zones/home")

        # 等待元素出现的时间戳
        try:
            # 左上角图标
            self.wait_for_element_visible("(//div[@class='Yw3h7tF3AF08Tc7NK6HJ'])[1]", timeout=10)  # 等待最多10秒

            # 成交金额
            self.wait_for_element_visible("(//span[contains(text(),'成交金额')])[1]", timeout=10)  # 等待最多10秒

            # 店铺体验分
            self.wait_for_element_visible("(//div[@class='DdptWuGPnmybot5vxI5m'])[1]", timeout=10)  # 等待最多10秒

            self.wait_for_element_visible("(//span[contains(text(),'立即优化')])[2]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_syt_home", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_syt_realtime(self):
        "https://syt.kwaixiaodian.com/zones/realtime"
        "生意通-实时数据-经营数据"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://syt.kwaixiaodian.com/zones/realtime")
        # 记录打开网页前的时间戳

        # 等待元素出现的时间戳
        try:
            # 左上角图标
            self.wait_for_element_visible("(//div[@class='Yw3h7tF3AF08Tc7NK6HJ'])[1]", timeout=10)  # 等待最多10秒

            # 成交金额
            self.wait_for_element_visible("(//span[contains(text(),'今日成交金额')])[1]", timeout=10)  # 等待最多10秒

            # 直播间明细
            self.wait_for_element_visible("(//div[@id='rc-tabs-0-tab-livePayAmt'])[1]", timeout=10)  # 等待最多10秒

            self.wait_for_element_visible("(//th[contains(text(),'直播间信息')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_syt_realtime", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_syt_trade_overview(self):
        "https://syt.kwaixiaodian.com/zones/trade/overview"
        "生意通-交易数据-经营数据"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_PROMTER", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://syt.kwaixiaodian.com/zones/trade/overview")
        # 记录打开网页前的时间戳

        # 等待元素出现的时间戳
        try:
            # 左上角图标
            self.wait_for_element_visible("(//div[@class='Yw3h7tF3AF08Tc7NK6HJ'])[1]", timeout=10)  # 等待最多10秒

            # 实时按钮
            self.wait_for_element_visible("/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div[2]/div/div[1]", timeout=10)  # 等待最多10秒

            # 成交金额
            self.wait_for_element_visible("(//span[@class='mcbwBGeaAbctTkFMk1YW'][contains(text(),'成交金额')])[1]", timeout=10)  # 等待最多10秒

            # 整体成交
            self.wait_for_element_visible("(//span[contains(text(),'整体成交')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_syt_trade_overview", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)



