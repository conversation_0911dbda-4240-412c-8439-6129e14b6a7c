import pytest
import time
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime

BaseCase.main(__name__, __file__)


class TestShopSpeed(BaseCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_shop_home(self):
        "s.kwaixiaodian.com/zone/home"
        "快手小店-首页-工作台"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("s.kwaixiaodian.com/zone/home")

        # 等待元素出现的时间戳
        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//div[@id='menu_item_W5bfiXQszVg'])[1]", timeout=10)  # 等待最多10秒
            #
            # 直播经营
            self.wait_for_element_visible("(//span[contains(text(),'直播经营')])[1]", timeout=10)  # 等待最多10秒
            #
            # 核心数据
            self.wait_for_element_visible("(//span[contains(text(),'核心数据')])[1]", timeout=10)  # 等待最多10秒

            # 待付款数据
            self.wait_for_element_visible("(//span[contains(text(),'待付款')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_home", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_goods_list(self):
        "https://s.kwaixiaodian.com/zone/goods/v1/list"
        "快手小店-商品管理-商品"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("s.kwaixiaodian.com/zone/goods/v1/list")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//div[@class='KpzZ4m6Y4YdOCF_PqjUr'])[1]", timeout=10)  # 等待最多10秒
            #
            # 商品信息
            self.wait_for_element_visible("(//th[contains(text(),'商品信息')])[1]", timeout=10)  # 等待最多10秒
            #
            # # 营销
            # self.wait_for_element_visible("(//span[@class='B9NgneE3FP3JFNPVfI6n'])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_goods_list", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_goods_add(self):
        "https://s.kwaixiaodian.com/zone/goods/config/release/add"
        "快手小店-商品新增-商品"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/add")
        self.sleep(5)
        self.refresh()
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/add")
        # 等待元素出现的时间戳

        try:
            # 返回小店后台按钮
            self.wait_for_element_visible("(//span[contains(text(),'返回小店后台')])[1]", timeout=10)  # 等待最多10秒

            # 下一步按钮
            self.wait_for_element_visible("(//span[contains(text(),'下一步，完善商品信息')])[1]", timeout=10)  # 等待最多10秒
            #
            # 商品主图
            self.wait_for_element_visible("(//span[contains(text(),'商品主图')])[2]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_goods_add", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_goods_detail(self):
        "https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=**************"
        "快手小店-商品详情-商品"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        # self.open("https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=**************")
        # self.sleep(5)
        # self.refresh()
        # self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=**************")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//div[@id='menu_item_W5bfiXQszVg'])[1]", timeout=10)  # 等待最多10秒

            # 商品状态
            self.wait_for_element_visible("(//div[@class='IfFHzEc64K51cDr5U_UH'])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 商品标题
            self.wait_for_element_visible("(//div[@class='IfFHzEc64K51cDr5U_UH'])[5]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_goods_detail", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_goods_edit(self):
        "s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary"
        "快手小店-商品编辑-商品"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary?itemId=**************")
        self.sleep(5)
        self.refresh()
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary?itemId=**************")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//span[contains(text(),'返回小店后台')])[1]", timeout=10)  # 等待最多10秒

            # 基础信息
            self.wait_for_element_visible("(//span[@class='k5hmJVjXdWf95W52xcIw undefined'])[1]", timeout=10)  # 等待最多10秒

            # 商品标题
            self.wait_for_element_visible("(//span[contains(text(),'商品标题')])[2]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 提交审核
            self.wait_for_element_visible("(//span[contains(text(),'提交审核')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_goods_edit", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_order_list(self):
        "s.kwaixiaodian.com/zone/order/list"
        "快手小店-订单列表-正向"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        # self.open("https://s.kwaixiaodian.com/zone/order/list")
        # self.sleep(5)
        # self.refresh()
        # self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/order/list")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//span[@id='menu_item_2ouXrG7ixCY'])[1]", timeout=10)  # 等待最多10秒

            # 订单列表
            self.wait_for_element_visible("(//h1[contains(text(),'订单列表')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 订单编号
            self.wait_for_element_visible("(//span[contains(text(),'订单编号：')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_order_list", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_order_detail(self):
        "s.kwaixiaodian.com/zone/order/detail"
        "快手小店-订单详情-正向"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=****************")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//div[@id='menu_item_R553YgpLnhQ'])[1]", timeout=10)  # 等待最多10秒

            # 商品信息
            self.wait_for_element_visible("(//th[contains(text(),'商品')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 订单编号
            self.wait_for_element_visible("(//span[contains(text(),'订单编号')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_order_detail", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    # @pytest.mark.p0
    # def test_shop_refund_detail(self):
    #     "s.kwaixiaodian.com/zone/refund/detail"
    #     "快手小店-售后详情-逆向"
    #     self.maximize_window()
    #
    #     account_data = get_account_info("wb_huoyangyang")
    #     host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")
    #
    #     self.open(host)
    #     self.sleep(1)
    #     self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
    #     self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
    #     self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
    #     self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
    #     self.type("input[placeholder='请输入手机号']", account_data['account'])
    #     self.sleep(1)
    #     self.type("input[placeholder='请输入密码']", account_data['password'])
    #     self.click("button[type='button']")
    #     self.sleep(5)
    #
    #     self.open("https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=****************")
    #     start_time = int(time.time() * 1000)
    #     print("开始访问页面的时间：" + str(start_time))
    #     # 等待元素出现的时间戳
    #
    #     try:
    #         # 左上角图标
    #         self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒
    #
    #         # 侧边栏
    #         self.wait_for_element_visible("(//div[@id='menu_item_R553YgpLnhQ'])[1]", timeout=10)  # 等待最多10秒
    #
    #         # 商品信息
    #         self.wait_for_element_visible("(//th[contains(text(),'商品')])[1]", timeout=10)  # 等待最多10秒
    #         element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳
    #
    #         # 订单编号
    #         self.wait_for_element_visible("(//span[contains(text(),'订单编号')])[1]", timeout=10)  # 等待最多10秒
    #         element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳
    #
    #         print("元素全部可见的时间戳：" + str(element_visible_time))
    #
    #         # 计算元素加载时间
    #         load_time = element_visible_time - start_time
    #         print(f"Element loaded in {load_time} milliseconds.")
    #     except Exception as e:
    #         print("Element is not visible or did not appear in time.")
    #         print(e)

    @pytest.mark.p0
    def test_shop_fund_account(self):
        "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        "快手小店-账户中心-资金结算"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/fund/accounting/overview")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//span[@id='menu_item_b4bIF9HNkug'])[1]", timeout=10)  # 等待最多10秒

            # 商品信息
            self.wait_for_element_visible("(//span[contains(text(),'可用金额')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 订单编号
            self.wait_for_element_visible("(//span[contains(text(),'微信余额')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_fund_account", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_fund_bonus(self):
        "https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus"
        "快手小店-资金提现-资金结算"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//span[@id='menu_item_QkfZhkTFNEY'])[1]", timeout=10)  # 等待最多10秒

            # 商品信息
            self.wait_for_element_visible("(//b[contains(text(),'奖金管理规则')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 订单编号
            self.wait_for_element_visible("(//th[contains(text(),'流水单号')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_fund_bonus", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_marketing_home(self):
        "https://s.kwaixiaodian.com/zone/marketing/tools/v2"
        "快手小店-营销中心-营销"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        # self.open("https://s.kwaixiaodian.com/zone/marketing/tools/v2")
        # self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/marketing/tools/v2")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//span[@id='menu_item_ZVlQM_ey6g8'])[1]", timeout=10)  # 等待最多10秒

            # 商品信息
            self.wait_for_element_visible("(//span[contains(text(),'营销GMV')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 热门工具
            self.wait_for_element_visible("(//div[@class='fkxCaLYbI7IN96URIJOe Nm6F5WbNmtFZV9sHXAbI'])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_marketing_home", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_marketing_tools(self):
        "https://s.kwaixiaodian.com/zone/marketing/tools/all-tools"
        "快手小店-营销工具-营销"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/marketing/tools/all-tools")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//span[@id='menu_item_vfJkUODk1UQ'])[1]", timeout=10)  # 等待最多10秒

            # 商品信息
            self.wait_for_element_visible("(//span[contains(text(),'拼团')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 热门工具
            self.wait_for_element_visible("(//span[contains(text(),'买赠活动')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_marketing_tools", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)

    @pytest.mark.p0
    def test_shop_marketing_business(self):
        "https://s.kwaixiaodian.com/zone/business-activity/business/list"
        "快手小店-营销活动首页-营销"
        self.maximize_window()

        account_data = get_account_info("wb_huoyangyang")
        host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")

        self.open(host)
        self.sleep(1)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(1)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(5)

        start_time = int(time.time() * 1000)
        print("开始访问页面的时间：" + str(start_time))
        self.open("https://s.kwaixiaodian.com/zone/business-activity/business/list")
        # 等待元素出现的时间戳

        try:
            # 左上角图标
            self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒

            # 侧边栏
            self.wait_for_element_visible("(//span[@id='menu_item_yq9qDokiKiA'])[1]", timeout=10)  # 等待最多10秒

            # 商品信息
            self.wait_for_element_visible("(//span[contains(text(),'近7天活动商品总数')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            # 热门活动
            self.wait_for_element_visible("(//span[contains(text(),'继续提报')])[1]", timeout=10)  # 等待最多10秒
            element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳

            print("元素全部可见的时间戳：" + str(element_visible_time))

            # 计算元素加载时间
            load_time = element_visible_time - start_time
            print(f"Element loaded in {load_time} milliseconds.")
            sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date) VALUES ('{}','{}','{}')".
                          format("test_shop_marketing_business", load_time, datetime.now()))
            print(sql_insert)
            # 执行结果写入db
            conn_mysql(sql_insert)
        except Exception as e:
            print("Element is not visible or did not appear in time.")
            print(e)





