import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
from utils.env_help import get_put_test


class TestTiYan(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        time.sleep(2)

    @pytest.mark.p1
    def test_click_tiyan(self):
        """ 体验指标：判断是否进入该页面 """
        self.click("(//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap'])[4]")
        self.sleep(1)
        self.find_element("(//span[contains(text(),'品退率-整体')])[1]")
        self.find_element("(//span[contains(text(),'品退率-直播渠道')])[1]")
        self.find_element("(//span[contains(text(),'品退率-非直播渠道')])[1]")

    @pytest.mark.p1
    def test_tiyan_export(self):
        """ 体验指标：数据导出，页面展示正常 """
        self.click("(//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap'])[4]")
        self.sleep(1)
        self.click("(//button[@type='button'])[2]")
        self.find_element("(//span[contains(text(),'品退率-直播渠道')])[1]")
        self.find_element("(//span[contains(text(),'品退率-非直播渠道')])[1]")