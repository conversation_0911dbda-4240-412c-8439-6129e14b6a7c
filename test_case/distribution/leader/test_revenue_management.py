import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
收益管理
python3 -m pytest test_case/distribution/leader/test_revenue_management.py --headless -n=3
"""


class TestRevenueManagement(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_click_bill_management(self):
        """ 账单管理:判断是否进入该页面 """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        # 进入账单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        self.assert_text("一级团长", "//div[@role='tab'][contains(text(),'一级团长')]")
        self.assert_text("二级团长", "//div[@role='tab'][contains(text(),'二级团长')]")
        self.assert_text("机构服务商", "//div[contains(text(),'机构服务商')]")

    @pytest.mark.p0
    def test_click_bill_management_time(self):
        """ 账单管理（一级团长）:判断下单时间筛选项是否能正常查询 """
        """ 账单管理（一级团长）:判断结算时间筛选项是否能正常查询 """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        # 进入账单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        # 下单时间筛选
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.click("(//span[@class='kwaishop-cps-leader-base-pc-picker-prev-icon'])[1]")
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-picker-cell-inner'][normalize-space()='1'])[1]")
        self.click("(//span[contains(text(),'确 定')])[1]")
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-picker-cell-inner'][normalize-space()='2'])[1]")
        self.click("(//span[contains(text(),'确 定')])[1]")
        # 查询
        self.click("(// button[@ type='submit'])[1]")
        # 判断页面非白屏
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-pro-title-subtitle'])[1]")

        # 结算时间筛选
        # self.click("(//input[@placeholder='开始日期'])[2]")
        # self.click("(//span[@class='kwaishop-cps-leader-base-pc-picker-prev-icon'])[1]")
        # self.click("(//div[@class='kwaishop-cps-leader-base-pc-picker-cell-inner'][normalize-space()='1'])[1]")
        # self.click("(//span[contains(text(),'确 定')])[1]")
        # self.click("(//div[@class='kwaishop-cps-leader-base-pc-picker-cell-inner'][normalize-space()='2'])[1]")
        # self.click("(//span[contains(text(),'确 定')])[1]")
        # 查询
        self.click("(// button[@ type='submit'])[1]")
        # 判断页面非白屏
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-pro-title-subtitle'])[1]")

    @pytest.mark.p0
    def test_click_bill_management_id(self):
        """ 账单管理（一级团长）:判断订单id筛选项是否能正常查询 """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "liuxiaohui07")
        # 进入账单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(1)
        # 筛选
        self.type("(//input[@placeholder='请输入订单id'])[1]", '2429001714832732')
        time.sleep(1)
        # 查询
        self.click("(// button[@ type='submit'])[1]")
        time.sleep(2)
        # 判断列表非空
        self.click("(//td[normalize-space()='2429001714832732'])[1]")

    @pytest.mark.p1
    def test_click_bill_management_tab_skip(self):
        """
        账单管理-点击二级团长tab-判断切换是否正常
        账单管理:判断是否存在账单
        账单管理:判断是分页是否正常
        """
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("(//span[contains(text(),'出单明细')])[1]")
        self.scroll_down(3)
        self.click("(//span[contains(text(),'账单管理')])[1]")
        time.sleep(1)
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-tabs-tab'])[1]")
        time.sleep(1)
        self.assert_text("支付宝", "(//td[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'支付宝')])[2]")

    @pytest.mark.p0
    def test_click_withdraw(self):
        """ 去提现:判断是否进入该页面 """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        self.click("//li[@id='menu-OH4RQAoZOYE']//span[@class='dilu-main-badge']//span[1]")
        self.assert_text("请用快手APP扫描上图二维码，进入手动提现",
                         "//body//div//div[@data-name='kwaishop-cps-leader-base-pc']//div//div//div//div[2]")

    @pytest.mark.p0
    def test_click_bonus_management(self):
        """ 奖金管理:判断是否进入该页面 """
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("//li[@id='menu-BSnVskeOO3s']//span[@class='dilu-main-badge']//span[1]")
        self.assert_text("待处理金额（元）", "//div[contains(text(),'待处理金额（元）')]")
        self.assert_text("审核处理中金额（元）", "//div[contains(text(),'审核处理中金额（元）')]")
        self.assert_text("累计发放奖励金额（元）", "//div[contains(text(),'累计发放奖励金额（元）')]")
        # self.assert_text("合并提现", "//button[@class='ant-btn ant-btn-primary']")

    def test_bonus_tab_change(self):
        """ 奖金管理-待提现/重试&发放记录&合并提现记录tab切换 """
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        # 待提现/重试
        self.click("//li[@id='menu-BSnVskeOO3s']//span[@class='dilu-main-badge']//span[1]")
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))
        # 发放记录
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))
        # 合并提现记录
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))

    @pytest.mark.p1
    @pytest.mark.skip
    def test_bonus_merge_transfer(self):
        """奖金管理-待提现/重试-合并提现弹窗"""
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("(//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap'])[23]")
        self.sleep(1)
        self.double_click("div.a45f0vYKcq4jEaTXlv9M button.fund-transfer-pc-btn.fund-transfer-pc-btn-primary")
        self.sleep(1)
        self.assert_text("如对奖励金额确认无误， 请点击确认后提现", "(//div[@class='fund-transfer-pc-popover-message-title'])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_bonus_record_by_status(self):
        """奖金管理-发放记录-奖金单状态"""
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("li[id='menu-BSnVskeOO3s'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        self.sleep(2)
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.sleep(2)
        elements = self.find_elements("div.fund-transfer-pc-select-item-option-content")
        for expectedStr, element in zip(["全部", "审核中", "提取中", "提取失败", "提取成功"], elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='全部']")
        self.assert_true("true" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='审核中']")
        self.assert_true("true" == self.get_attribute("//div[@title='审核中']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='提取中']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取中']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='提取失败']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取失败']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='提取成功']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取成功']", "aria-selected"))

    @pytest.mark.p1
    def test_bonus_record_by_type(self):
        """奖金管理-发放记录-活动类型"""
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("//li[@id='menu-BSnVskeOO3s']//span[@class='dilu-main-badge']//span[1]")
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        elements = self.find_elements("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option")
        for expectedStr, element in zip(["全部", "现金奖励活动", "TR后返活动", "快币奖励活动", "粉条奖励活动", "金牛奖励活动", "钱包奖励活动"], elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='全部']")
        self.assert_true("true" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='现金奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='现金奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='TR后返活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='TR后返活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='快币奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='快币奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='粉条奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='粉条奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='金牛奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='金牛奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='钱包奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='钱包奖励活动']", "aria-selected"))

    @pytest.mark.p0
    @pytest.mark.skip
    def test_click_juli_invoice(self):
        """ 聚力计划发票:判断是否进入该页面 """
        self.merchant_login("DISTRIBUTION_LEADER_PROD", "wb_huoyangyang")
        self.click("//span[contains(text(),'聚力计划发票')]")
        time.sleep(3)
        self.find_element("//h4[contains(text(),'待开票账单金额')]")
        self.find_element("//h4[contains(text(),'可开票解冻金额')]")
        self.find_element("//h4[contains(text(),'累计开票解冻金额')]")
        #
        # self.click("(//span[contains(text(),'导出明细')])[1]")
        # self.click("//div[@class='ant-message']//div")
