from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time

from test_case.assistant.base import BaseCase


# pc达人选品消息页面
class TestDistributionLeaderMessagePage(DistributionToolTest):

	@pytest.mark.p2
	@pytest.mark.skip
	def test_Leader_MessagePage(self):
		self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
		self.driver.maximize_window()
		time.sleep(2)
		self.open('https://eshop-cps.prt.kwaixiaodian.com/pc/leader/notice-center/list?menuKey=0')
		time.sleep(2)
# 判断元素出现
		img_xpath = ['//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[1]/span[1]']
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "ngaLRMmbrAtOtfGTB31J" in outer_html, "缺少 ngaLRMmbrAtOtfGTB31J 属性"
		self.assert_text("通知中心",'//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[1]/span[1]')


	@pytest.mark.p2
	def test_Leader_MessagePage_Click(self):
		self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
		self.driver.maximize_window()
		time.sleep(2)
		self.open('https://eshop-cps.prt.kwaixiaodian.com/pc/leader/notice-center/list?menuKey=0')
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[1]/ul/li[2]/span')
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[1]/ul/li[3]')
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[1]/ul/li[4]')
		time.sleep(2)

	@pytest.mark.p2
	@pytest.mark.skip
	def test_Leader_MessagePage_Paging(self):
		self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
		self.driver.maximize_window()
		time.sleep(2)
		self.open('https://eshop-cps.prt.kwaixiaodian.com/pc/leader/notice-center/list?menuKey=0')
		time.sleep(2)
		self.scroll_down(1)
		self.click("//a[normalize-space()='2']")
		time.sleep(2)
		self.click("//a[normalize-space()='3']")
		time.sleep(2)
		self.click("//a[normalize-space()='4']")
		time.sleep(2)

	@pytest.mark.p2
	def test_Leader_MessagePage_Status(self):
		self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
		self.driver.maximize_window()
		time.sleep(2)
		self.open('https://eshop-cps.prt.kwaixiaodian.com/pc/leader/notice-center/list?menuKey=0')
		time.sleep(2)
		self.click("//span[@title='全部']")
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div/div[2]')
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[2]/div/div[1]/span[2]')
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div/div[3]/div')
		time.sleep(2)

	@pytest.mark.p2
	def test_Leader_MessagePage_StatusMarker(self):
		self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
		self.driver.maximize_window()
		time.sleep(2)
		self.open('https://eshop-cps.prt.kwaixiaodian.com/pc/leader/notice-center/list?menuKey=0')
		time.sleep(2)
		self.click("//span[@title='全部']")
		time.sleep(2)
		self.click('//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div/div[2]')
		time.sleep(2)
		# 判断未读状态标存在
		self.assert_element('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]/div[1]/div/div[1]/div[1]/span')
		# 判断按钮是否可点击
		button = self.find_element('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]/div[2]/a')
		self.assert_equal(button.is_enabled(), True)