import time

import pytest

from test_case.assistant.base import BaseCase
from test_case.distribution.distribution_base_case import DistributionToolTest
from seleniumwire import webdriver

"""
达人合作
-找达人
"""


@pytest.mark.p0
class TestReceiverCooperate(DistributionToolTest):
    """
    python3 -m pytest test_case/distribution/leader/test_receiver_cooperate.py
    """

    @pytest.mark.p0
    def test_click_receiver_square(self):
        """
        达人广场：判断是否进入该页面
        """
        self.leader_login("DISTRIBUTION_LEADER_PROD", "wb_caijinwei")
        self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
        # self.save_cookies("cookies.txt")
        # self.driver.quit()
        # self.driver = webdriver.Chrome()
        # self.driver.header_overrides = {"Host": "s.kwaixiaodian.com", "origin": "https://cps.kwaixiaodian.com",
        #                                 "referer": "https://cps.kwaixiaodian.com/pc/promoter/selection-center/home"}
        # self.open_url("https://www.kwaixiaodian.com/commander")
        # print("第一次: " + self.driver.session_id)
        # self.load_cookies("cookie.txt")
        # self.open_url("https://s.kwaixiaodian.com/pc/leader/zone-leader/my-sponsor-activity")
        # print("第二次: " + self.driver.session_id)
        # self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 查看是否有短视频达人的tab
        self.assert_element("(//div[@id='rc-tabs-0-tab-2'])[1]")

    @pytest.mark.p0
    def test_click_short_video_receiver(self):
        """
        达人广场：点击短视频达人，判断是否进入该页面
        """
        self.leader_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 点击短视频达人
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        self.assert_text("带货TOP商品", "(//div[contains(text(),'带货TOP商品')])[1]")

    @pytest.mark.p2
    def test_click_promoter_phone_jump(self):
        """达人广场： 在达人列表中点击达人头像 - 判断跳转是否正常"""
        # 进入达人广场
        self.leader_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.sleep(2)
        self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
        self.sleep(2)
        self.maximize_window()
        self.sleep(2)
        # 点击达人头像
        self.click("(//div)[396]")
        self.sleep(5)
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.sleep(1)
        # 判断跳转后元素是否出现
        self.assert_element("(//span[contains(text(),'在线沟通')])[1]")

    @pytest.mark.p2
    def test_click_check_detail_jump(self):
        """达人广场：在达人列表中点击查看详情 - 判断跳转是否正常"""
        # 进入达人广场
        self.leader_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 点击查看详情
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(5)
        self.driver.switch_to.window(self.driver.window_handles[-1])
        # 判断跳转后元素是否出现
        self.assert_element("(//div[@id='rc-tabs-0-tab-situation'])[1]")

    @pytest.mark.p2
    def test_click_spilt_page_normal(self):
        """达人广场：在达人列表中判断分页是否正常"""
        # 进入达人广场
        self.leader_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()

        # 点击第二页 查看是否能跳转
        self.click("(//a[normalize-space()='2'])[1]")
        time.sleep(2)
        # 判断第二页的第一条达人信息条是否展现
        self.assert_element("(//div[contains(text(),'带货TOP商品')])[1]")

    # @pytest.mark.p2
    # def test_click_goods_tab_elements_exist(self):
    #     """达人广场：点击商品维度tab - 判断相关元素是否存在"""
    #     # 进入达人广场
    #     self.login("DISTRIBUTION_LEADER", "wb_caijinwei")
    #     self.click("//li[@id='menu-eq8KZFjFxcU']//span[@class='dilu-main-badge']//span[1]")
    #     self.maximize_window()
    #     time.sleep(2)
    #     #点击商品纬度tab
    #     # self.click("//div[@class='index-module__header--JnEFz']//div//div[@class='cps-ant-tabs-tab']")
    #     time.sleep(2)
    #     #判断点击后相关元素是否出现
    #     self.assert_element("(//div[@class='index-module__goodLatitude--M1kTB'])[1]")

    @pytest.mark.p2
    def test_click_my_invite_change_tab_normal(self):
        """达人广场：点击我的邀约 - 点击 - 待处理tab切换是否正常"""
        # 进入达人广场
        self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//li[@id='menu-dRySOntRsaw']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 点击邀约中tab
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-tabs-tab kwaishop-cps-daren-match-pc-tabs-tab-active'])[1]")

        time.sleep(2)
        # tab-待处理
        self.click("(//label[@class='kwaishop-cps-daren-match-pc-radio-button-wrapper'])[1]")
        self.assert_text("查看达人详情", "(//span[contains(text(),'查看达人详情')])[1]")

    @pytest.mark.p2
    def test_click_my_invite_detail_jump_normal(self):
        """达人广场：点击我的邀约 - 点击查看详情 - 判断页面跳转是否正常"""
        # 进入达人广场
        self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//li[@id='menu-dRySOntRsaw']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 点击邀约中tab
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-tabs-tab kwaishop-cps-daren-match-pc-tabs-tab-active'])[1]")

        self.click("(//span[contains(text(),'查看达人详情')])[1]")

        self.sleep(5)
        self.driver.switch_to.window(self.driver.window_handles[-1])
        # 判断跳转后元素是否出现
        self.assert_element("(//div[@id='rc-tabs-0-tab-situation'])[1]")


