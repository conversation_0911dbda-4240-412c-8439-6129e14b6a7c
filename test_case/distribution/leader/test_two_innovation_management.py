import time
from datetime import datetime, timedelta

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
二创管理
-合作关系管理
pytest test_case/distribution/leader/test_two_innovation_management.py --headless -n=5
"""

@pytest.mark.skip
class TestTwoInnovationManagement(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.sleep(3)

    @pytest.mark.p1
    def test_i_have_an_invitation_from_my_talent(self):
        """ 合作关系管理:判断是否进入该页面 """
        self.click("//span[contains(text(),'合作关系管理')]")
        time.sleep(1)
        self.assert_text("我的达人邀约", "//div[contains(text(),'我的达人邀约')]")
        self.assert_text("我的二创作者", "//div[contains(text(),'我的二创作者')]")

    @pytest.mark.p0
    def test_new_creator(self):
        """ 合作关系管理:判断是否打开新增作者弹窗 """
        self.click("//span[contains(text(),'合作关系管理')]")
        self.sleep(1)
        self.click("//div[contains(text(),'我的二创作者')]")
        self.sleep(1)
        self.click("//span[contains(text(),'新增二创作者')]")
        self.input("//input[@placeholder='请输入0-100的整数']", "6")
        self.click("//input[@placeholder='请选择日期']")
        self.sleep(1)
        current_date = datetime.now().date()
        target_date = current_date + timedelta(days=1)
        xpath_expression = f"td[title='{target_date}'] div"
        self.click(xpath_expression)
        self.click("//span[contains(text(),'确 认')]")

    @pytest.mark.p1
    def test_whether_to_open_recruitment(self):
        """ 是否开启招募 """
        self.click("//span[contains(text(),'合作关系管理')]")
        self.sleep(1)
        self.click("//div[contains(text(),'我的二创作者')]")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-leader-base-pc-switch-handle']")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-leader-base-pc-switch-handle']")
        # self.find_element("//span[contains(text(),'您已成功开启招募，招募信息已发布至切片广场')]")

    @pytest.mark.p1
    def test_edit_recruitment_information(self):
        """ 编辑招募信息 """
        self.click("//span[contains(text(),'合作关系管理')]")
        self.sleep(1)
        self.click("//div[contains(text(),'我的二创作者')]")
        self.sleep(2)
        self.click("//a[contains(text(),'编辑')]")
        self.sleep(2)
        self.find_element("//label[contains(text(),'团队规模')]")
        self.find_element("//label[contains(text(),'所在地')]")
        self.find_element("//label[contains(text(),'微信号')]")
        self.find_element("//label[contains(text(),'团队介绍')]")
        self.find_element("//label[contains(text(),'招募要求')]")
        self.sleep(2)
        self.click("//span[contains(text(),'提 交')]")

    @pytest.mark.p1
    def test_three_tab_change_normal(self):
        """  点击待作者确认、已结束tab-判断切换是否正常 是否开启招募 """
        """ 点击查看授权商品-判断跳转是否正常 是否开启招募 """
        self.click("//li[@id='menu-m8d9E6bHYhM']//span[@class='dilu-main-badge']//span[1]")
        self.sleep(3)
        # 点击已结束
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-tabs-tab'])[3]")
        # 点击授权商品明细
        self.click("(//span[contains(text(),'查看授权商品')])[1]")
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_text("查询结果", "(//div[@class='kwaishop-cps-leader-base-pc-pro-title-subtitle'])[1]")

    @pytest.mark.p1
    def test_search_promoter_by_id(self):
        """ 通过快手ID搜索达人-判断结果是否符合预期 是否开启招募 """
        self.click("//span[contains(text(),'合作关系管理')]")
        time.sleep(1)
        self.input("(//input[@placeholder='请输入达人快手id'])[1]", "2337072173")
        time.sleep(1)
        self.click("(//button[@type='submit'])[1]")
        time.sleep(1)
        self.assert_text("快手ID：2337072173", "(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[1]")

    @pytest.mark.p1
    def test_click_create_author_tab_change(self):
        """ 点击我的二创作者tab-判断切换是否正常 是否开启招募 """
        self.click("//span[contains(text(),'合作关系管理')]")
        self.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-leader-base-pc-pro-page-item']//div[@class='kwaishop-cps-leader-base-pc-tabs-tab']")
        self.sleep(2)
        self.click("(//div[@id='rc-tabs-2-tab-3'])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'明细数据')])[1]")

    # ====================================================
    # 二创管理--授权商品管理
    # ====================================================

    # 查看页面元素是否存在
    @pytest.mark.p2
    def test_second_product_check_element_exist(self):
        # 点击授权商品管理
        self.click("(//span[@class='dilu-main-menu-title-content'])[25]")
        self.sleep(2)
        self.assert_element("(//div[@class='FXerXiPDtEQohuO2_3WC'])[1]")

    # 点击商品标题-判断右侧抽屉元素是否存在
    @pytest.mark.p2
    def test_second_product_click_title_appear_window(self):
        # 点击授权商品管理
        self.click("(//span[@class='dilu-main-menu-title-content'])[25]")
        self.sleep(2)
        # 点击商品标题
        self.click("(//div[@class='UBVG2T4OIIAa_imOJs9i'])[1]")
        self.sleep(2)
        # 判断抽屉元素不为空
        self.assert_element("(//div[@class='kwaishop-cps-leader-base-pc-drawer-content-wrapper'])[1]")

    @pytest.mark.p2
    def test_second_product_search_by_promoter_message(self):
        """ 根据达人信息查询 """
        self.click("(//span[@class='dilu-main-menu-title-content'])[25]")
        time.sleep(1)
        # 在输入框中输入指定达人id ;点击搜索 ;判断能否搜索出商品
        self.input("(//input[@placeholder='请输入达人快手ID'])[1]", "2337072173")
        self.click("(//button[@type='submit'])[1]")
        time.sleep(1)
        self.assert_text("2337072173", "(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[4]")

    @pytest.mark.p2
    def test_second_product_application_for_cooperation(self):
        """合作中申请"""
        self.click("//span[contains(text(),'合作关系管理')]")
        self.sleep(1)
        self.click("span.kwaishop-cps-leader-base-pc-badge")
        self.find_element("//*[@id='rc-tabs-2-tab-unProcess']")
        self.find_element("//*[@id='rc-tabs-2-tab-processed']")
