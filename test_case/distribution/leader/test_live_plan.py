import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长盘播
"""


class TestLeaderGrowth(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        self.sleep(2)
        self.click("//span[contains(text(), '我的盘播达人')]")

    @pytest.mark.p1
    def test_leader_live_promoter(self):
        """ 团长盘播：判断是否进入该页面 """
        self.find_element("(//span[@id='kwaishop-tianhe-leader-apply-pc-main-title'])[1]")
        self.find_element("(//span[contains(text(),'新建计划')])[1]")
        self.find_element("(//span[contains(text(),'计划场次')])[1]")

    @pytest.mark.p1
    def test_leader_live_promoter_new_plan(self):
        """ 团长盘播：点击新建计划，盘底抽屉页是否出现 """
        self.click("(//span[contains(text(),'新建计划')])[1]")
        self.find_element("(//span[contains(text(),'添加场次')])[1]")

    @pytest.mark.p1
    def test_leader_live_promoter_cancel(self):
        """ 团长盘播：点击解除合作，弹窗是否出现 """
        self.click("(//span[contains(text(),'解除合作')])[1]")
        self.find_element("(//span[contains(text(),'确 定')])[1]")

    @pytest.mark.p1
    def test_leader_live_promoter_new(self):
        """ 团长盘播：创建计划 """
        self.click("(//span[contains(text(),'创建计划')])[1]")
        self.find_element("(//div[@class='kwaishop-tianhe-leader-apply-pc-drawer-title'])[1]")

    # @pytest.mark.p1
    # def test_leader_live_promoter_new(self):
    #     """ 团长盘播：提报扶摇 """
    #     self.click("(//span[contains(text(),'提报扶摇')])[1]")
    #     self.find_element("(//th[contains(text(),'备注')])[1]")

    @pytest.mark.p1
    def test_leader_live_plan(self):
        """ 团长盘播计划：页面是否展示正常 """
        self.click("//span[contains(text(), '盘播计划')]")
        self.click("(//button[contains(text(),'知道啦')])[1]")
        self.click("(//div[@id='rc-tabs-0-tab-0'])[1]")
        self.find_element("(//span[contains(text(),'新建计划')])[1]")
        self.find_element("(//div[contains(text(),'计划GMV')])[1]")

    @pytest.mark.p1
    def test_leader_live_plan_new(self):
        """ 团长盘播计划：创建计划，页面是否展示正常 """
        self.click("//span[contains(text(), '盘播计划')]")
        self.click("(//button[contains(text(),'知道啦')])[1]")
        self.click("(//span[contains(text(),'新建计划')])[1]")
        self.find_element("(//label[contains(text(),'直播名称')])[1]")

    @pytest.mark.p1
    def test_leader_live_plan_skip1(self):
        """ 团长盘播计划：跳转我的盘播达人 """
        self.click("//span[contains(text(), '盘播计划')]")
        self.click("(//button[contains(text(),'知道啦')])[1]")
        self.click("(//a[contains(text(),'我的盘播达人')])[1]")
        self.find_element("(//span[contains(text(),'计划场次')])[1]")

    @pytest.mark.p1
    def test_leader_live_plan_skip(self):
        """ 团长盘播计划：点击修改计划、删除计划 """
        self.click("//span[contains(text(), '盘播计划')]")
        self.click("(//button[contains(text(),'知道啦')])[1]")
        self.click("(//span[contains(text(),'修改计划')])[1]")
        self.click("(//span[contains(text(),'取 消')])[1]")
        self.click("(//span[contains(text(),'删除计划')])[1]")
        self.click("(//span[contains(text(),'取 消')])[1]")




