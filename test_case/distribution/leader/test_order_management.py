import time

import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长
订单管理
pytest test_case/distribution/leader/test_order_management.py --headless -n=3
"""


class TestOrderManagement(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        time.sleep(2)

    @pytest.mark.p0
    def test_service_charge_revenue_order(self):
        """ 订单管理：判断是否进入服务费收入订单页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.find_element("//div[@aria-selected='true']")
        self.find_element("//div[contains(text(),'服务费支出订单')]")
        self.find_element("//div[contains(text(),'佣金收入订单')]")

    @pytest.mark.p0
    def test_service_charge_order_click(self):
        """ 订单管理：点击进入服务费支出订单Tab,判断是否进入该页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'服务费支出订单')]")
        time.sleep(2)
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(1)")
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(2)")
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(3)")

    @pytest.mark.p0
    def test_commission_order_click(self):
        """ 订单管理：点击 佣金收入订单Tab,判断是否进入该页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        self.click("//div[contains(text(),'佣金收入订单')]")
        time.sleep(2)
        self.find_element("//label[contains(text(),'订单ID')]")
        self.find_element("//label[contains(text(),'二创者ID')]")
        self.find_element("//label[contains(text(),'商家ID')]")

    @pytest.mark.p2
    @pytest.mark.skip
    def test_exist_order(self):
        """ 点击下单时间-选择最近一个月-查看是否存在订单元素 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-leader-base-pc-picker-range-separator']")
        self.click("//div[@class='kwaishop-cps-leader-base-pc-pro-form-layout-wrapper']//li[4]//span[1]")
        self.click("//button[@type='submit']")
        time.sleep(2)
        # 存在订单
        if self.assert_element("//div[@class='kwaishop-cps-leader-base-pc-pro-toolbar-total-content ']"):
            self.assert_element("//tbody/tr[2]/td[1]/div[1]/div[1]/div[1]")
    #
    # @pytest.mark.p0
    # def test_service_order_limit(self):
    #     """点击批量导出-查看是否存在导出成功弹窗"""
    #     # 点击订单管理tab
    #     self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
    #     time.sleep(2)
    #     self.click("//span[contains(text(),'批量导出')]")
    #     time.sleep(1)
    #     self.assert_element('//div[@class="kwaishop-cps-leader-base-pc-notification-notice-with-icon"]')

    # @pytest.mark.p0
    # def test_service_order_expect(self):
    #     """ 点击批量导出-查看是否存在导出成功弹窗 """
    #     # 点击订单管理tab
    #     self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
    #     self.sleep(2)
    #     self.click("(//span[contains(text(),'批量导出')])[1]")
    #     self.sleep(2)
    #     self.click("(//a[contains(text(),'点击查看申请导出订单列表')])[1]")

    @pytest.mark.p0
    def test_service_order_expected(self):
        """点击批量导出-点击导出列表-查看是否正常跳转
        点击批量导出-点击导出列表-查看列表元素是否存在"""
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'查看已导列表')])[1]")
        time.sleep(3)
        self.assert_text("已导出的订单列表", "(//div[@class='kwaishop-cps-leader-base-pc-pro-title-title'])[1]")

        # 查看列表元素是否存在
        self.click("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'下载完成')])[5]")

        # 点击批量导出-点击导出列表-查看列表tab能否正常切换
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.click("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'下载完成')])[2]")


    @pytest.mark.p0
    def test_service_Bill_ActivityID(self):
        """点击账单管理-判断活动id筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//input[@placeholder='请输入活动ID']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入活动ID']", '7620900434')
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("(//span[contains(text(),'蔡金伟')])[1]")

    @pytest.mark.p0
    def test_service_SecondTier_OrderID(self):
        """点击账单管理-（二级团长）:判断订单id筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[@role='tab'][contains(text(),'二级团长')]")
        time.sleep(2)
        self.click("//input[@placeholder='请输入订单id']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入订单id']", '2428502184969732')
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("//span[contains(text(),'蔡金伟')]")

    @pytest.mark.p0
    def test_service_SecondTier_OrderTime(self):
        """点击账单管理-（二级团长）:判断下单时间筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[@role='tab'][contains(text(),'二级团长')]")
        time.sleep(2)
        self.type("(//input[@placeholder='开始日期'])[1]", '2024-08-07 14:28:02')
        time.sleep(2)
        self.type("(//input[@placeholder='结束日期'])[1]", '2024-11-06 14:28:02')
        time.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("//span[contains(text(),'蔡金伟')]")


    @pytest.mark.p0
    def test_service_SecondTier_SettlementTime(self):
        """点击账单管理-（二级团长）:判断结算时间筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[@role='tab'][contains(text(),'二级团长')]")
        time.sleep(2)
        self.type("(//input[@placeholder='开始日期'])[2]", '2024-08-07 14:28:02')
        time.sleep(2)
        self.type("(//input[@placeholder='结束日期'])[2]", '2024-11-06 14:28:02')
        time.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("//span[contains(text(),'蔡金伟')]")

    @pytest.mark.p0
    def test_service_SecondTier_ActivityID(self):
        """点击账单管理二级团长-判断活动id筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[@role='tab'][contains(text(),'二级团长')]")
        time.sleep(2)
        self.click("//input[@placeholder='请输入活动ID']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入活动ID']", '**********')
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("(//span[contains(text(),'蔡金伟')])[1]")


    @pytest.mark.p0
    def test_service_ServiceProvider_OrderID(self):
        """点击账单管理-（服务商）:判断订单id筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'机构服务商')]")
        time.sleep(2)
        self.click("//input[@placeholder='请输入订单id']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入订单id']", '2429501778465732')
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("//td[@class='kwaishop-cps-leader-base-pc-table-cell kwaishop-cps-leader-base-pc-table-cell-fix-left kwaishop-cps-leader-base-pc-table-cell-fix-left-last']")


    @pytest.mark.p0
    def test_service_ServiceProvider_OrderTime(self):
        """点击账单管理-（服务商）:判断下单时间筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'机构服务商')]")
        time.sleep(2)
        self.type("(//input[@placeholder='开始日期'])[1]", '2024-08-07 14:28:02')
        time.sleep(2)
        self.type("(//input[@placeholder='结束日期'])[1]", '2024-11-06 14:28:02')
        time.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element("//td[@class='kwaishop-cps-leader-base-pc-table-cell kwaishop-cps-leader-base-pc-table-cell-fix-left kwaishop-cps-leader-base-pc-table-cell-fix-left-last']")

    @pytest.mark.p0
    def test_service_ServiceProvider_SettlementTime(self):
        """点击账单管理-（服务商）:判断结算时间筛选项是否能正常查询"""
        # 点击订单管理tab
        self.click("//li[@id='menu-lnESZId03N4']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[@role='tab'][contains(text(),'二级团长')]")
        time.sleep(2)
        self.type("(//input[@placeholder='开始日期'])[2]", '2024-08-07 14:28:02')
        time.sleep(2)
        self.type("(//input[@placeholder='结束日期'])[2]", '2024-11-06 14:28:02')
        time.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element(
            "//td[@class='kwaishop-cps-leader-base-pc-table-cell kwaishop-cps-leader-base-pc-table-cell-fix-left kwaishop-cps-leader-base-pc-table-cell-fix-left-last']")