import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长PC后台-二级团产品库
python3 -m pytest test_case/distribution/leader/test_two_leader.py --headless -n=5
"""


class TestTwoLeader(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")

    @pytest.mark.p0
    def test_two_leader_index(self):
        """ 二级团产品库-首页 """
        # 点击团长选品侧边栏
        self.click("//span[contains(text(), '团长选品库')]")
        time.sleep(3)
        self.sleep(2)
        self.refresh()
        self.sleep(2)
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        # 点击联系团长
        self.assert_element("(//span[contains(text(),'联系团长')])[1]")

    @pytest.mark.p0
    def test_two_leader_new_activity_skip(self):
        """ 二级团产品库-点击加入活动-点击创建活动-跳转正常 """
        # 点击团长选品侧边栏
        self.click("//span[contains(text(), '团长选品库')]")
        time.sleep(1)
        self.sleep(2)
        self.refresh()
        self.sleep(2)
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        # 点击加入活动
        self.click("(//a[contains(text(),'加入活动')])[1]")
        time.sleep(1)
        # 点击创建新活动
        self.click("(//a[contains(text(),'新建招商活动')])[1]")

    @pytest.mark.p0
    def test_two_leader_item_sku(self):
        """ 二级团产品库-点击商品规格，判断弹窗元素是否展示正常 """
        # 点击团长选品侧边栏
        self.click("//span[contains(text(), '团长选品库')]")
        time.sleep(2)
        self.sleep(2)
        self.refresh()
        # 点击商品规格
        self.click("(//a[contains(text(),'规格')])[1]")
        time.sleep(1)
        # 确认是否有弹窗元素
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-modal-header'])[1]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_two_leader_item_search(self):
        """ 二级团产品库-判断搜索功能是否正常 """
        # 点击团长选品侧边栏
        self.click("//span[contains(text(), '团长选品库')]")
        time.sleep(2)
        self.sleep(2)
        self.refresh()
        # 搜索
        self.type("(//input[@id='itemKeyword'])[1]", "衣")
        time.sleep(1)
        # 确认是否有搜索结果
        self.assert_element("(//span[contains(text(),'联系团长')])[1]")

    @pytest.mark.p0
    def test_two_leader_item_search_by_land(self):
        """ 二级团产品库-判断筛选功能是否正常 """
        # 点击团长选品侧边栏
        self.click("//span[contains(text(), '团长选品库')]")
        time.sleep(2)
        self.sleep(2)
        self.refresh()
        self.sleep(2)
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        # 筛选食品饮料的商品
        self.click("(//span[contains(text(),'全部筛选')])[1]")
        time.sleep(1)
        self.click("(//span[contains(text(),'食品饮料')])[1]")
        # 确认是否有筛选结果
        self.assert_element("(//span[contains(text(),'联系团长')])[1]")

    @pytest.mark.p2
    @pytest.mark.skip
    def test_product_and_cooperation_query_image_information(self):
        """ 商品主图和合作查询的图片信息 """
        self.click("//span[contains(text(), '团长选品库')]")
        self.click("//*[@id='menu-idmjgIe2d6c']/span/span/span/span[contains(text(),'团长商品投放')]")
        self.click("//*[@id='root']/section/main/div/div[3]/button/span[contains(text(),'立即使用')]")

        img_xpath = [
            '//*[@id="root"]/section/main/div[2]/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/img',
            '//*[@id="root"]/section/main/div[2]/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/img',
            '//*[@id="root"]/section/main/div[2]/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[5]/td[2]/div/img',
            '//*[@id="root"]/section/main/div[2]/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[7]/td[2]/div/img'
        ]

        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "60", "宽度不正确"
            assert img.get_attribute("height") == "60", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "AssetsImage" in outer_html, "outerHTML 中未找到 AssetsImage"
