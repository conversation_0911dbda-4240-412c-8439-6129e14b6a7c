import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

# from pyquery import PyQuery as pq

"""
经营数据
·经营概览
.出单明细
·撮合效果
python3 -m pytest test_case/distribution/leader/test_deal_performance_overview.py --headless -n=3
"""


class TestDealPerformanceOverview(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.leader_login("DISTRIBUTION_LEADER_PROD", 'wb_huoyangyang')
        self.sleep(3)

    def get_show_data(self, css_format, **kwargs):
        """
        根据css表达式，获取数值列表
        """
        numList = []
        for i in range(1, 11):
            css = css_format.format(key=i)
            isExist = self.is_element_present(css)
            # 若没有找到下一个，直接结束
            if isExist is False:
                break
            numStr = self.get_text(css)
            if kwargs.get("hasStr", False):
                numStr = numStr.replace(kwargs.get("strFlag", ""), "")
            numList.append(float(numStr))
        return numList

    def get_show_data_for_str(self, css_format):
        """
        根据css表达式，获取字符串列表
        """
        strList = []
        for i in range(1, 11):
            css = css_format.format(key=i)
            isExist = self.is_element_present(css)
            # 若没有找到下一个，直接结束
            if isExist is False:
                break
            text = self.get_text(css)
            strList.append(text)
        return strList

    @pytest.mark.p1
    def test_deal_performance_overview(self):
        """
        进入团长首页-点击经营数据-点击经营概览-判断是否进入经营概览页面
        """
        self.click("//*[@id='menu-TUsvx8b4i9w']/span/span/span/span")
        self.click("(//div[contains(text(),'支付GMV(元）')])[1]")

    @pytest.mark.p1
    def test_deal_performance_overview_data(self):
        """
        点击数据指标说明-判断弹窗是否出现
        """
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("(//div[@class='data-desc___BLnSW'])[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'知道了')])[1]")

    @pytest.mark.p1
    def test_deal_performance_overview_trend(self):
        """点击近一年tab-判断右侧是否出现趋势图"""
        self.click("//*[@id='menu-TUsvx8b4i9w']/span/span/span/span")
        time.sleep(2)
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)
        self.click("//*[@id='root']/section/main/div/div[2]/div[2]/div[2]/div/div/div/canvas")

    @pytest.mark.p1
    def test_deal_performance_overview_promoter_skip(self):
        """
        点击出单达人区块的明细-是否正常跳转
        """
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("(//div[@class='link___AJR_G'][contains(text(),'明细')])[1]")
        time.sleep(2)
        self.assert_text("达人", "//span[@title='达人']")

    @pytest.mark.p0
    def test_deal_performance_overview_item_skip(self):
        """ 点击出单商品数区块的明细-是否正常跳转 """
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("//body//div[@id='main_root']//div[@id='root']//div//div//div//div[2]//div[1]//div[2]")
        time.sleep(2)
        self.find_element("//label[@title='商品信息']")

    @pytest.mark.p1
    def test_deal_performance_overview_leader_skip(self):
        """
        点击出单团长数的明细-是否正常跳转
        """
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'明细')])[3]")
        time.sleep(2)
        self.assert_text("团长", "//span[@title='团长']")

    @pytest.mark.p0
    def test_click_over_business_change_normal(self):
        """订单概览模块 - 点击已结算订单量 - 能否正常切换趋势图"""
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)
        self.click(
            "//*[@id='root']/section/main/div/div[3]/div[2]/div[1]/div[2]/div[1]/div")
        time.sleep(2)
        self.assert_element(
            "//*[@id='root']/section/main/div/div[3]/div[2]/div[2]/div/div/div/canvas")

    @pytest.mark.p0
    def test_click_out_business_change_normal(self):
        """出单概览模块 - 点击出单商品数 - 能否正常切换趋势图"""
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("//span[contains(text(),'近90天')]")
        time.sleep(1)
        self.click(
            "(//div[contains(text(),'出单商品数')])[1]")
        time.sleep(1)
        self.assert_element(
            "(//canvas[@micro-creator='kwaishop-cps-leader-base-pc'])[3]")

    @pytest.mark.p0
    def test_click_detail_of_first_is_normal(self):
        """ 出单达人数 - 点击明细 - 能否正常跳转页面 """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        self.sleep(1)
        self.click("/html/body/div[1]/div/div[2]/div[2]/div/div/div/div/div/div/section/main/div/div/div/div[1]/div[1]/div[1]/div/div[2]/div")
        self.sleep(2)
        self.click("(//span[contains(text(),'近1年')])[2]")
        self.sleep(2)
        self.assert_element("(//th[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'商品信息')])[2]")
        self.assert_element("(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'出单达人数')])[2]")

    @pytest.mark.p2
    def test_order_detail_goods_image(self):
        """出单明细 - 出单商品的商品图片"""
        self.click('li[id="menu-DJZ25fgGBJs"] span[class="dilu-main-badge"] span:nth-child(1)')
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        # 点击-近90天
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-2"]/div/div[1]/div[2]/div/label[6]/span[contains(text(),"近1年")]')

        time.sleep(2)
        img_xpath = [
            '//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div/div[1]/img',
            '//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[1]/img',
            '//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[1]/div/div[1]/img'
        ]

        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "60", "宽度不正确"
            assert img.get_attribute("height") == "60", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "AssetsImage" in outer_html, "outerHTML 中未找到 AssetsImage"

    @pytest.mark.p2
    def test_the_promoter_avatar(self):
        """出单明细-推广者的头像"""
        self.click('li[id="menu-DJZ25fgGBJs"] span[class="dilu-main-badge"] span:nth-child(1)')
        self.click("//*[@id='rc-tabs-0-panel-1']/div/div[1]/div[2]/div/label[5]/span[2]")

        img_xpath = [
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div/div[1]/img',
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[1]/img'
        ]

        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "48", "宽度不正确"
            assert img.get_attribute("height") == "48", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "AssetsImage" in outer_html, "outerHTML 中未找到 AssetsImage"

    @pytest.mark.p2
    @pytest.mark.skip
    def test_order_detail_data(self):
        """进入出单明细-点击数据指标说明-判断弹窗是否出现"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//span[contains(text(),'数据指标说明')])[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'知道了')])[1]")

    @pytest.mark.p1
    def test_order_detail_year_tab(self):
        """进入出单明细-点击数据指标说明-判断弹窗是否出现"""
        self.click('li[id="menu-DJZ25fgGBJs"] span[class="dilu-main-badge"] span:nth-child(1)')
        self.click('//*[@id="root"]/section/main/div/div/div/div[2]/button/span[1]')
        # 断言弹窗是否出现
        self.find_element("(//span[contains(text(),'知道了')])[1]")

    @pytest.mark.p1
    def test_order_out_commodity_tab_change(self):
        """点击出单商品tab - 判断切换是否正常"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.find_element("(//th[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'商品信息')])[2]")

    @pytest.mark.p2
    def test_order_out_commodity_important_element(self):
        """点击出单商品tab - 判断出单商品tab关键元素是否存在"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.find_element(
            "(//th[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'商品信息')])[2]")

    @pytest.mark.p2
    def test_order_type_of_spreader_select(self):
        """进入出单明细-点击推广者类型 - 看下拉框元素是否正确（下拉框还未判断）"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("(//span[@class='kwaishop-cps-leader-base-pc-select-selection-item'])[1]")

    @pytest.mark.p2
    def test_order_month_tab_window(self):
        """
        进入出单明细-点击自然月tab - 判断是否出现日历弹窗
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//span[contains(text(),'自然月')])[1]")
        time.sleep(2)
        self.assert_text("1月",
                         "(//div[@class='kwaishop-cps-leader-base-pc-picker-cell-inner'][contains(text(),'1月')])[1]")

    @pytest.mark.p2
    def test_promoter_type_dropdown_box(self):
        """
        点击推广者类型-看下拉框元素是否正确
        预期：全部、达人、团长
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[@class='kwaishop-cps-leader-base-pc-select-selection-item']")
        time.sleep(2)
        elements = self.find_elements(
            "div.kwaishop-cps-leader-base-pc-select-item.kwaishop-cps-leader-base-pc-select-item-option")
        expectedStrList = ["全部", "达人", "团长"]
        for element, expectedStr in zip(elements, expectedStrList):
            assert element.text == expectedStr
        for element, expectedStr in zip(elements, expectedStrList):
            assert element.get_attribute("title") == expectedStr

    @pytest.mark.p2
    def test_promoter_type_for_promoter(self):
        """
        点击推广者类型-点击达人-判断下方列表推广者类型数据展示是否均为达人
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//span[contains(text(),'近1年')])[1]")
        time.sleep(2)
        self.click("//span[@class='kwaishop-cps-leader-base-pc-select-selection-item']")
        time.sleep(1)
        self.click(
            "div.kwaishop-cps-leader-base-pc-select-item.kwaishop-cps-leader-base-pc-select-item-option:nth-child(2)")
        time.sleep(0.5)
        self.click("(//button[@type='submit'])[1]")
        time.sleep(1)
        cssFormat = "tbody tr:nth-child({key}) td:nth-child(2)"
        strList = self.get_show_data_for_str(cssFormat)
        flag = all(item == "达人" for item in strList)
        assert flag == True

    @pytest.mark.p2
    def test_promoter_type_for_leader(self):
        """
        点击推广者类型-点击团长-判断下方列表推广者类型数据展示是否均为团长
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//span[contains(text(),'近1年')])[1]")
        time.sleep(2)
        self.click("//span[@class='kwaishop-cps-leader-base-pc-select-selection-item']")
        time.sleep(1)
        self.click(
            "div.kwaishop-cps-leader-base-pc-select-item.kwaishop-cps-leader-base-pc-select-item-option:nth-child(3)")
        time.sleep(0.5)
        self.click("(//button[@type='submit'])[1]")
        time.sleep(1)
        cssFormat = "tbody tr:nth-child({key}) td:nth-child(2)"
        strList = self.get_show_data_for_str(cssFormat)
        flag = all(item == "团长" for item in strList)
        assert flag == True

    @pytest.mark.p2
    def test_spreader_time_tab_change_normal(self):
        """推广者 - 昨天 - 近3天 - 近7天 - 近30天tab能否切换正常"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'昨天')]")
        time.sleep(1)
        self.click("//span[contains(text(),'近3天')]")
        time.sleep(1)
        self.click("//span[contains(text(),'近7天')]")
        time.sleep(1)
        self.click("//span[contains(text(),'近30天')]")
        time.sleep(1)
        self.click("//span[contains(text(),'近90天')]")
        time.sleep(1)
        self.assert_element("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[1]")
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(1)
        self.assert_element("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[1]")

    @pytest.mark.p2
    def test_spreader_search_promoter_id_is_right(self):
        """推广者 - 推广者信息搜ID能否正常搜到"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)
        self.input("//input[@placeholder='请输入推广者ID或昵称']", "2337072173")
        time.sleep(1)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_text("青青草原扛把子", "(//div[contains(text(),'青青草原扛把子')])[1]")

    @pytest.mark.p2
    def test_spreader_search_promoter_name_is_right(self):
        """推广者 - 推广者信息搜昵称能否正常搜到"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)
        self.input("//input[@placeholder='请输入推广者ID或昵称']", "青青草原扛把子")
        time.sleep(1)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_text("青青草原扛把子", "(//div[contains(text(),'青青草原扛把子')])[1]")

    @pytest.mark.p2
    def test_spreader_return_button_is_right(self):
        """推广者 - 重置按钮是否运行正常"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)
        self.input("//input[@placeholder='请输入推广者ID或昵称']", "青青草原扛把子")
        self.assert_text("青青草原扛把子", "//input[@placeholder='请输入推广者ID或昵称']")

        time.sleep(1)
        self.click("//span[contains(text(),'重 置')]")
        time.sleep(2)
        self.input("//input[@placeholder='请输入推广者ID或昵称']", "test")

        self.assert_text("test", "//input[@placeholder='请输入推广者ID或昵称']")

    @pytest.mark.p2
    def test_spreader_order_of_number_goods(self):
        """推广者 - 点击出单商品数 - 排序是否生效"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)

        text1 = self.find_element(
            "/html/body/div[1]/div/div[2]/div[2]/div/div/div/div/div/div/section/main/div/div/div/div[1]/div[2]/div/div[1]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[3]/button/span").text
        number1 = float(text1)
        self.click("//span[contains(text(),'出单商品数')]")
        time.sleep(1)
        text2 = self.find_element(
            "/html/body/div[1]/div/div[2]/div[2]/div/div/div/div/div/div/section/main/div/div/div/div[1]/div[2]/div/div[1]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/button/span").text
        number2 = float(text2)

        assert number1 >= number2

    @pytest.mark.p2
    def test_spreader_order_of_number_orders(self):
        """推广者 - 点击支付订单数 - 排序是否生效"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)

        text1 = self.find_element(
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]').text
        number1 = float(text1)
        self.click("//span[contains(text(),'支付订单量')]")
        time.sleep(1)
        text2 = self.find_element(
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]').text
        number2 = float(text2)

        assert number1 >= number2

    @pytest.mark.p2
    def test_spreader_order_of_number_GMV(self):
        """推广者 - 点击支付GMV - 排序是否生效"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("//span[contains(text(),'近1年')]")
        time.sleep(2)

        text1 = self.find_element(
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]').text
        number1 = float(text1.replace("￥", ""))
        self.click("//span[contains(text(),'支付GMV')]")
        time.sleep(1)
        text2 = self.find_element(
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]').text
        number2 = float(text1.replace("￥", ""))

        assert number1 >= number2

    @pytest.mark.p2
    def test_order_click_number_commodity_drawer_appear(self):
        """进入出单明细-点击出单商品数 - 判断右侧是否出现抽屉"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'近1年')])[1]")
        self.click(
            "//*[@id='rc-tabs-0-panel-1']/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[3]/button")

        self.find_element("//th[contains(text(),'商品信息')]")
        self.find_element(
            "th.kwaishop-cps-leader-base-pc-table-column-has-sorters:nth-child(2) > div:nth-child(1) > span:nth-child(1)")

    @pytest.mark.p2
    def test_order_click_number_commodity_drawer_exist(self):
        """
        进入出单明细-点击出单商品数 - 判断右侧抽屉元素是否存在
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        self.click("(//span[contains(text(),'近1年')])[1]")
        self.click(
            "(//span[contains(text(),'1')])[4]")
        self.assert_text("商品信息",
                         "(//div[@class='kwaishop-cps-leader-base-pc-drawer-body'])[1]")

    @pytest.mark.p2
    # 点击出单商品 - tab - 点击出单达人数 - 判断右侧是否出现抽屉
    def test_order_click_sellout_promoter_drawer_appear(self):
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")
        time.sleep(2)
        self.click(
            "/html/body/div[1]/div/div[2]/div[2]/div/div/div/div/div/div/section/main/div/div/div/div[1]/div[2]/div/div[2]/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/button/span")
        self.assert_text("出单达人详情", "(//div[@class='kwaishop-cps-leader-base-pc-drawer-title'])[1]")

    @pytest.mark.p2
    def test_order_click_sellout_seller_drawer_appear(self):
        """点击出单商品 - tab - 点击出单团长数 - 判断右侧是否出现抽屉"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.maximize_window()
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")
        self.click(
            "(//span[contains(text(),'0')])[6]")
        self.assert_text("出单团长详情", "(//div[contains(text(),'出单团长详情')])[1]")

    @pytest.mark.p2
    def test_order_click_sellout_promoter_drawer_exist(self):
        """点击出单商品 - tab - 点击出单达人数 - 判断抽屉元素是否存在"""
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")
        time.sleep(1)
        self.click(
            "(//span[contains(text(),'1')])[5]")
        self.find_element("(//th[contains(text(),'推广者信息')])[1]")

    @pytest.mark.p2

    def test_match_current_html_is_not_null(self):
        """关键元素是否存在，判断页面是否为白屏"""
        self.click("(//span[@class='dilu-main-menu-title-content'])[4]")
        time.sleep(2)
        self.maximize_window()
        # 点击 撮合效果
        self.find_element("(//canvas[@micro-creator='kwaishop-cps-leader-base-pc'])[1]")
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-title-title'])[1]")

    @pytest.mark.p2
    @pytest.mark.skip
    def test_match_tab_change_is_normal(self):
        """判断tab切换是否正常"""
        self.click("(//span[@class='dilu-main-menu-title-content'])[4]")
        time.sleep(2)
        # 切换到商家撮合
        self.click("(//div[@id='tab-seller'])[1]")
        self.find_element("(//div[@class='el-card__body'])[2]")
        # 切换到商品撮合
        self.click("(//div[@id='tab-item'])[1]")
        self.find_element("(//canvas)[1]")
        # 切换到达人撮合
        self.click("(//div[@id='tab-bigman'])[1]")
        self.find_element("(//div[@class='el-card__body'])[2]")
        self.find_element("(//div[@class='el-card__body'])[3]")
        # 切换到快赚客撮合
        self.click("(//div[@id='tab-kwaimoney'])[1]")
        self.find_element("(//th[contains(@class,'is-leaf')])[1]")

    @pytest.mark.p2
    def test_order_click_sellout_promoter_sort(self):
        """
        点击出单商品tab-点击出单达人数 -查看排序功能是否生效
        点击后变成升序，再次点击变成降序
        """
        self.click("(//span[contains(text(),'出单明细')])[1]")
        time.sleep(1)
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(0.5)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")

        # 点击出单达人排序，变成升序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'出单达人数')])[2]")
        time.sleep(1)

        cssFormat = "tbody tr:nth-child({key}) td:nth-child(2) button:nth-child(1) span:nth-child(1)"
        promoterNums = self.get_show_data(cssFormat)
        print(promoterNums)
        is_ascending = all(x <= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_ascending is True

        # 点击出单达人排序，变成将序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'出单达人数')])[2]")
        time.sleep(1)
        promoterNums = self.get_show_data(cssFormat)
        print(promoterNums)
        is_descending = all(x >= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_descending is True

    @pytest.mark.p2
    def test_order_click_sellout_activity_sort(self):
        """
        点击出单商品tab-点击出单团长数 -查看排序功能是否生效
        点击后变成升序，再次点击变成降序
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(1)
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")

        # 点击出单团长排序，变成升序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'出单团长数')])[2]")
        time.sleep(1)

        cssFormat = "tbody tr:nth-child({key}) td:nth-child(3) button:nth-child(1) span:nth-child(1)"
        promoterNums = self.get_show_data(cssFormat)
        print(promoterNums)
        is_ascending = all(x <= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_ascending is True

        # 点击出单团长排序，变成降序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'出单团长数')])[2]")
        time.sleep(1)
        promoterNums = self.get_show_data(cssFormat)
        print(promoterNums)
        is_descending = all(x >= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_descending is True

    @pytest.mark.p2
    def test_order_click_sellout_order_sort(self):
        """
        点击出单商品tab-点击支付订单量 -查看排序功能是否生效
        点击后变成升序，再次点击变成降序
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(1)
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")

        # 点击支付订单量排序，变成升序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'支付订单量')])[2]")
        time.sleep(1)

        # cssFormat = "tbody tr:nth-child({key}) td:nth-child(4)"
        cssFormat = "td.kwaishop-cps-leader-base-pc-table-cell.kwaishop-cps-leader-base-pc-table-column-sort"
        promoterElements = self.find_elements(cssFormat)
        promoterNums = [float(element.text) for element in promoterElements]
        print(promoterNums)
        is_ascending = all(x <= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_ascending is True

        # 点击支付订单量排序，变成降序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'支付订单量')])[2]")
        time.sleep(1)
        promoterElements = self.find_elements(cssFormat)
        promoterNums = [float(element.text) for element in promoterElements]
        print(promoterNums)
        is_descending = all(x >= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_descending is True

    @pytest.mark.p2
    @pytest.mark.skip
    def test_order_click_sellout_GMV_sort(self):
        """
        点击出单商品tab-点击支付GMV -查看排序功能是否生效
        点击后变成升序，再次点击变成降序
        """
        self.click("//li[@id='menu-DJZ25fgGBJs']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(1)
        # 点击 出单商品
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        # 选择近一年
        self.click("(//span[contains(text(),'近1年')])[2]")

        # 点击支付GMV排序，变成升序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'支付GMV')])[2]")
        time.sleep(1)

        # print(self.driver.page_source)
        cssFormat = "td.kwaishop-cps-leader-base-pc-table-cell.kwaishop-cps-leader-base-pc-table-column-sort"
        promoterElements = self.find_elements(cssFormat)
        # promoterNums = self.get_show_data(cssFormat, hasStr=True, strFlag="￥")
        promoterNums = [float(element.text.replace("￥", "")) for element in promoterElements]
        print(promoterNums)
        is_ascending = all(x <= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_ascending is True

        # 点击支付GMV排序，变成降序
        self.click(
            "(//span[@class='kwaishop-cps-leader-base-pc-table-column-title-no-align'][contains(text(),'支付GMV')])[2]")
        time.sleep(1)
        promoterElements = self.find_elements(cssFormat)
        promoterNums = [float(element.text.replace("￥", "")) for element in promoterElements]
        print(promoterNums)
        is_descending = all(x >= y for x, y in zip(promoterNums, promoterNums[1:]))
        assert is_descending is True

    @pytest.mark.p0
    def test_deal_performance_overview_date(self):
        """
        点击自然月tab-判断是否出现日历弹窗
        """
        self.click("(//span[contains(text(),'经营概览')])[1]")
        time.sleep(2)
        self.click("//span[contains(text(),'自然月')]")
        time.sleep(2)
        self.assert_text("1月",
                         '//*[@id="root"]/section/main/div/div[1]/div[1]/div/div[3]/div/div/div/div/div/div[2]/table/tbody/tr[1]/td[1]/div')