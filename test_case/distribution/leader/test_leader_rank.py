import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
from utils.env_help import get_put_test

"""
团长成长
-团长等级
-团长任务
python3 -m pytest test_case/distribution/leader/test_leader_rank.py --headless -n=3
"""


class TestLeaderGrowth(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        time.sleep(2)

    @pytest.mark.p1
    def test_click_leader_rank(self):
        """ 团长等级：判断是否进入该页面 """
        self.click("//li[@id='menu-DvhdThm-_XU']//span[@class='dilu-main-badge']//span[1]")
        self.find_element("//span[contains(text(),'查看详细规则')]")

        first_element = self.find_element("//span[contains(text(),'查看详细规则')]")
        XPATH = '/html/body/div[2]/div/div/div/div[3]/div[2]/div/div/div/div/div/section/main/div/div[3]/div[1]/button/span[1]'
        if self.is_element_visible(XPATH):
            second_element = self.find_element(XPATH)
        else:
            second_element = 'X'

        if first_element == second_element:
            print("两个XPath指向同一个元素")
        else:
            print("两个XPath不指向同一个元素")
        self.find_element("//span[contains(text(),'查看全部权益')]")

    @pytest.mark.p2
    def test_click_leader_rank_data(self):
        """  团长等级：点击指标说明-判断弹窗元素是否存在  """
        self.click("//li[@id='menu-DvhdThm-_XU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("(//div[@class='button___wynY8'])[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'知道了')])[1]")

    @pytest.mark.p2
    def test_click_leader_rank_all_right(self):
        """ 团长等级：点击查看全部权益-判断跳转是否正常 """
        self.click("//li[@id='menu-DvhdThm-_XU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//span[contains(text(),'查看全部权益')]")
        time.sleep(2)
        self.assert_text("产品功能", "(//div[contains(text(),'产品功能')])[1]")

    @pytest.mark.p2
    def test_click_leader_rank_detail(self):
        """团长等级：点击查看详细规则-判断跳转是否正常"""
        self.click("//li[@id='menu-DvhdThm-_XU']//span[@class='dilu-main-badge']//span[1]")
        s = get_put_test()
        print(s)
        time.sleep(2)
        self.click("//span[contains(text(),'查看详细规则')]")
        time.sleep(2)
        self.click("//div[@class='ArticleMCNAnnouncement_title__13c0n']")

    @pytest.mark.p2
    def test_click_leader_rank_drawer(self):
        """ 团长等级：点击任意权益-判断右边抽屉元素是否存在 """
        self.click("//li[@id='menu-DvhdThm-_XU']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'功能')])[1]")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-pc-micro-leader-drawer-title']")

    @pytest.mark.p1
    def test_click_leader_task(self):
        """ 团长活动：判断是否进入该页面 """
        self.click("//li[@id='menu--GdryYWjW9A']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.find_element("//span[contains(text(),'当前任务')]")
        self.find_element("//span[contains(text(),'更多任务')]")
