# import pytest
#
# from test_case.assistant.base import BaseCase
#
# """
# 经营数据
# -撮合效果
# """
#
#
# class TestMatchingResults(BaseCase):
#     """
#     python3 -m pytest test_case/distribution/leader/test_matching_results.py --html=report/leader_report.html --reruns=0 -n=3
#     """
#
#     @pytest.mark.p1
#     def test_matching_results(self):
#         """判断是否进入商家撮合页面"""
#         self.login("DISTRIBUTION_LEADER", "wb_caijinwei")
#         self.click('li[id="menu-V7wcPwbVnZ8"] span[class="kpro-workbench-layout-sider-v2__itemmenu-title"]')
#         self.assert_text("商家撮合", "//div[@id='tab-seller']")
#         self.assert_text("撮合趋势", "//h4[contains(text(),'撮合趋势')]")
#         self.assert_text("撮合明细", "//h4[contains(text(),'撮合明细')]")
