import time
from datetime import datetime

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长
达人招商页面
python3 -m pytest test_case/distribution/leader/test_promoter_investment.py --headless -n=5
"""


class TestPromoterInvestment(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.sleep(5)

    @pytest.mark.p0
    def test_promoter_investment_index(self):
        """侧边栏点击达人招商-查看页面元素是否存在"""
        self.click("//span[contains(text(),'达人招商')]")
        """断言"""
        self.find_element("//span[contains(text(),'智能推荐')]")
        self.find_element("//span[contains(text(),'达人粉丝数')]")
        self.find_element("//span[contains(text(),'发布时间')]")

    @pytest.mark.p0
    def test_promoter_investment_detail(self):
        """点击立即报名进入报名页-查看页面元素是否存在"""
        self.click("//span[contains(text(), '达人招商')]")
        self.click(
            "//body//div[@id='main_root']//div[@id='root']//div//div//div//div//div//div//div//div//div//div//div[1]//div[2]//div[3]//div[1]//div[1]//button[1]//span[contains(text(),'立即报名')]")
        self.sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        # 待报名商品&已报名商品
        self.find_element("//div[@aria-selected='true']")
        self.find_element("//div[@aria-selected='false']")
        # 招商要求&达人信息
        self.find_element("//*[@id='root']/section/main/div/div[1]/div[2]/div/div[1]")
        self.find_element("//*[@id='root']/section/main/div/div[1]/div[2]/div/div[2]")

    @pytest.mark.p0
    def test_promoter_investment_enrolment(self):
        """ 达人招商-已报名活动页面元素是否存在"""
        self.click("//span[contains(text(),'达人招商')]")
        time.sleep(2)
        # 点击已报名活动tab
        self.click("//div[@aria-selected='false']")
        self.assert_element("//span[contains(text(),'重 置')]")
        self.assert_element("//span[contains(text(),'查 询')]")
        self.assert_element('//thead[@class="kwaishop-cps-leader-base-pc-table-thead"]//tr')

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_investment_click_goods_classify(self):
        """商品类目：类目是否可以选中并点击"""
        self.click("//span[contains(text(),'达人招商')]")
        time.sleep(2)
        # 点击达人粉丝数 分辨判断下面是否会出现相应的元素
        self.click("(//span[contains(text(),'达人粉丝数')])[1]")
        time.sleep(2)

        # 下面 点击相应的类目 查看下面的list是否正确
        self.click("(//div[@title='食品饮料'])[1]")
        time.sleep(1)
        self.assert_text("食品饮料",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='家居百货'])[1]")
        time.sleep(1)
        self.assert_text("家居百货",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='女装女鞋'])[1]")
        time.sleep(1)
        self.assert_text("女装女鞋",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='美妆护肤'])[1]")
        time.sleep(1)
        self.assert_text("美妆护肤",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='个护清洁'])[1]")
        time.sleep(1)
        self.assert_text("个护清洁",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='医疗保健'])[1]")
        time.sleep(1)
        self.assert_text("医疗保健",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='母婴玩具'])[1]")
        time.sleep(1)
        self.assert_text("母婴玩具",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='茶酒生鲜'])[1]")
        time.sleep(1)
        self.assert_text("茶酒生鲜",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='男装男鞋'])[1]")
        time.sleep(1)
        self.assert_text("男装男鞋",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='运动户外'])[1]")
        time.sleep(1)
        self.assert_text("运动户外",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='数码家电'])[1]")
        time.sleep(1)
        self.assert_text("数码家电",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

        self.click("(//div[@title='珠宝配饰'])[1]")
        time.sleep(1)
        self.assert_text("珠宝配饰",
                         "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/section[1]/main[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]")

    @pytest.mark.p1
    def test_promoter_investment_click_promoter_level(self):
        """达人等级：等级是否可以选中并点击"""
        self.click("//span[contains(text(),'达人招商')]")
        self.sleep(1)
        # 点击达人粉丝数 分辨判断下面是否会出现相应的元素
        self.click("(//span[contains(text(),'达人粉丝数')])[1]")
        self.sleep(2)

        self.click("(//div[@title='L0'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

        self.click("(//div[@title='L1-'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

        self.click("(//div[@title='L1+'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

        self.click("(//div[@title='L2-'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

        self.click("(//div[@title='L2+'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

        self.click("(//div[@title='L3-'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

        self.click("(//div[@title='L3+'])[1]")
        self.sleep(1)
        self.find_element("(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

    @pytest.mark.p1
    def test_promoter_investment_click_promoter_following_numbers(self):
        """粉丝总数：粉丝数量是否可以选中并点击"""
        self.click("//span[contains(text(), '达人招商')]")
        time.sleep(1)
        # 点击达人粉丝数 分辨判断下面是否会出现相应的元素
        self.click("//div[@title='10-100万']")
        time.sleep(2)
        text = self.find_element("(//span[@class='promoterDesc___aGKwo'])[1]").text
        number = int(''.join(filter(str.isdigit, text)))
        assert number <= 100 and number >= 10

    @pytest.mark.p1
    def test_promoter_investment_search_promoter(self):
        """达人昵称搜索：搜索指定达人"""
        self.click("//span[contains(text(),'达人招商')]")
        time.sleep(1)
        self.input("(//input[@placeholder='请输入达人昵称'])[1]", "铜锣湾扛把子")
        self.click("(//span[@aria-label='system-search-line'])[1]")
        time.sleep(1)
        self.click("(//span[contains(text(),'达人粉丝数')])[1]")
        time.sleep(1)
        self.click("(//span[contains(text(),'达人粉丝数')])[1]")
        self.assert_text("铜锣湾扛把子", "(//div[@class='jkHc8WPVFvmkoZQWu0Ap'])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_the_button_of_number_click(self):
        """达人粉丝数排序：是否可点击"""
        self.click("//span[contains(text(), '达人招商')]")
        self.sleep(1)
        self.click("//div[@title='100-300万']")
        self.sleep(1)
        self.click(
            "//body/div[@id='main_root']/div[@id='main-pc-page-layout']/div[@class='kpro-workbench-layout__content-wrap']/div[@class='kpro-workbench-layout__content']/div[@id='__Kpro-workbench-scroll-content__']/div[@class='kpro-workbench-layout__route-container']/div[@class='kpro-workbench-layout-dlcontent__container']/div[@id='micro-viewport']/div[@id='__qiankun_microapp_wrapper_for_kwaishop_cps_leader_base_pc__']/div[@id='root']/section[@class='kwaishop-cps-leader-base-pc-layout layout___Y3Vgc']/main[@class='kwaishop-cps-leader-base-pc-layout-content layoutContentDetail___S14tp']/div[@class='kwaishop-cps-leader-base-pc-pro-page']/div[@class='kwaishop-cps-leader-base-pc-pro-page-item']/div[@class='kwaishop-cps-leader-base-pc-pro-list']/div[@class='kwaishop-cps-leader-base-pc-space kwaishop-cps-leader-base-pc-space-vertical']/div[@class='kwaishop-cps-leader-base-pc-space-item']/div[@class='kwaishop-cps-leader-base-pc-pro-toolbar']/div[@class='kwaishop-cps-leader-base-pc-space kwaishop-cps-leader-base-pc-space-vertical']/div[@class='kwaishop-cps-leader-base-pc-space-item']/div[@class='kwaishop-cps-leader-base-pc-pro-toolbar-table-title']/div[@class='listTitle____0azC']/div[@class='kwaishop-cps-leader-base-pc-radio-group kwaishop-cps-leader-base-pc-radio-group-outline kwaishop-cps-leader-base-pc-radio-group-small']/label[@class='kwaishop-cps-leader-base-pc-radio-button-wrapper kwaishop-cps-leader-base-pc-radio-button-wrapper-checked kwaishop-cps-leader-base-pc-radio-button kwaishop-cps-leader-base-pc-radio-button-text']/span[2]/span[1]")
        self.sleep(2)
        self.click("(//span[@class='itemLabel___YGLvC'][contains(text(),'商品类目')])[1]")

    @pytest.mark.p1
    def test_promoter_the_button_of_time_click(self):
        """发布时间排序：是否可点击"""
        self.click("//span[contains(text(),'达人招商')]")
        time.sleep(1)
        # 点击发布时间
        self.click(
            "(//label[@class='kwaishop-cps-leader-base-pc-radio-button-wrapper kwaishop-cps-leader-base-pc-radio-button kwaishop-cps-leader-base-pc-radio-button-text'])[2]")
        time.sleep(2)
        self.click("(//span[@class='itemLabel___YGLvC'][contains(text(),'商品类目')])[1]")

    @pytest.mark.p1
    def test_promoter_investment_search_promoter(self):
        """达人卡片信息：商品品牌、商品描述、商品类目、价格、佣金率、招商时间、粉丝数"""
        self.click("//span[contains(text(),'达人招商')]")
        time.sleep(1)
        self.assert_text("商品品牌", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")
        self.assert_text("商品描述", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")
        self.assert_text("商品类目", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")
        self.assert_text("价格", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")
        self.assert_text("佣金率", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")
        self.assert_text("招商时间", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")
        self.assert_text("粉丝数", "(//div[@class='kwaishop-cps-leader-base-pc-pro-list-row-wrapper'])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_change_page_is_normal(self):
        """翻页"""
        self.click("//span[contains(text(),'达人招商')]")

        time.sleep(3)

        """断言是否翻到对应的页面"""
        self.click('//*[@id="root"]/section/main/div/div[3]/div/div/div[2]/div/div/div/div/ul/li[@title="2"]')
        time.sleep(3)
        page_number_two = 2  # 期望的页码
        page_number = self.get_text(
            '//*[@id="root"]/section/main/div/div[3]/div/div/div[2]/div/div/div/div/ul/li[@title="2"]')
        assert str(page_number_two) == page_number, "翻页到第2页失败"

        self.click('//*[@id="root"]/section/main/div/div[3]/div/div/div[2]/div/div/div/div/ul/li[@title="3"]')
        time.sleep(3)
        page_number_three = 3  # 期望的页码
        page_number = self.get_text(
            '//*[@id="root"]/section/main/div/div[3]/div/div/div[2]/div/div/div/div/ul/li[@title="3"]')
        assert str(page_number_three) == page_number, "翻页到第2页失败"

        self.click('//*[@id="root"]/section/main/div/div[3]/div/div/div[2]/div/div/div/div/ul/li[@title="4"]')
        time.sleep(3)
        page_number_four = 4  # 期望的页码
        page_number = self.get_text(
            '//*[@id="root"]/section/main/div/div[3]/div/div/div[2]/div/div/div/div/ul/li[@title="4"]')
        assert str(page_number_four) == page_number, "翻页到第2页失败"

    @pytest.mark.p2
    def test_images_of_the_registered_products(self):
        """ 已报名的活动商品图片 """
        self.click("//span[contains(text(), '达人招商')]")
        self.click("//*[@id='rc-tabs-0-tab-2']")

        img_xpath = [
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/img',
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[3]/div/img',
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/table/tbody/tr[6]/td[3]/div/img',
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/table/tbody/tr[8]/td[3]/div/img'
        ]


    @pytest.mark.p2
    def test_promoter_investment_CustomizeFans(self):
        """侧边栏点击达人招商-查看页面元素是否存在"""
        self.click("//span[contains(text(),'达人招商')]")
        self.sleep(2)
        self.click("//div[@title='自定义']")
        self.sleep(2)
        self.type("(//input[@placeholder='请输入'])[1]", '1')
        self.sleep(2)
        self.type("(//input[@placeholder='请输入'])[2]", '3')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.find_element("//body//div//div[@class='kwaishop-cps-leader-base-pc-spin-container']//div//div//div[1]//div[3]//div[2]")




