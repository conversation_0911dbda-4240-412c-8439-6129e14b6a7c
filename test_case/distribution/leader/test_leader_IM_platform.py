import pytest
from test_case.distribution.distribution_base_case import DistributionToolTest

"""
商达团沟通平台——团长工作台入口
"""

class TestLeaderIMPlatform(DistributionToolTest):

    def setUp(self):
        super().setUp()
        self.maximize_window()
        self.leader_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.sleep(3)

    @pytest.mark.p1
    def test_leader_home_page_entrance(self):
        self.click("//span[@class='dilu-main-badge']//img")
        # 判断是否进入客服消息系统
        self.assert_url("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")

    # 线索商品
    @pytest.mark.p1
    def test_leader_clue_item_entrance(self):
        self.click("//span[contains(text(), '团长选品库')]")
        self.sleep(2)
        # 点掉弹窗
        self.click("(//div[@id='driver-page-overlay'])[1]")
        self.sleep(2)
        self.click("(//div[contains(text(),'线索商品')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")

    # 投放商品
    @pytest.mark.p1
    def test_leader_item_pool_entrance(self):
        self.click("//span[contains(text(), '团长选品库')]")
        self.sleep(2)
        # 点掉弹窗
        self.click("(//div[@id='driver-page-overlay'])[1]")
        self.sleep(2)
        self.click("(//div[contains(text(),'投放商品')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")

    # 达人广场
    @pytest.mark.p1
    def test_leader_promoter_square_entrance(self):
        self.click("//span[contains(text(), '达人广场')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")

    # 达人详情页
    @pytest.mark.p1
    def test_leader_promoter_detail_page_entrance(self):
        self.click("//span[contains(text(), '达人广场')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-cps.prt.kwaixiaodian.com/pc/leader/zone/daren-match/daren-detail?promoterId=")
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")

    # 团长详情页
    @pytest.mark.p1
    def test_leader_leader_datail_page_entrance(self):
        self.click("//span[contains(text(), '团长招商')]")
        self.sleep(2)
        # 进入团长详情页
        self.click("(//div[@class='index-module__row_user_summary--ZM4QW'])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")

    # 团长招商
    @pytest.mark.p1
    def test_leader_leader_investment_entrance(self):
        self.click("//span[contains(text(), '团长招商')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=leader")
