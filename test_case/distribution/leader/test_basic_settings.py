# import time
#
# import pytest
#
# from test_case.distribution.distribution_base_case import DistributionToolTest
#
# """
# 基础设置菜单
# pytest test_case/distribution/leader/test_basic_settings.py --headless -n=5
# """
#
#
# class TestBasicSettings(DistributionToolTest):
#
#     def setUp(self, **kwargs):
#         super().setUp()
#         self.maximize_window()
#         self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
#         self.sleep(3)
#
#     @pytest.mark.p0
#     def test_click_collection_account(self):
#         """ 收款账号：判断是否进入收款账户页面 """
#         self.click("//span[contains(text(),'收款账号')]")
#         self.assert_element('//div[@id="root"]//section//main//div')
#
#     @pytest.mark.p2
#     def test_picture_information_of_the_receiving_account(self):
#         """ 收款账户的二维码图片信息 """
#         self.click("//span[contains(text(),'收款账号')]")
#         img = self.find_element("//*[@id='root']/section/main/div/img")
#
#         # 校验 width 和 height
#         assert img.get_attribute("width") == "200", "宽度不正确"
#         assert img.get_attribute("height") == "200", "高度不正确"
#
#         # 校验 data-component-name
#         outer_html = img.get_attribute("outerHTML")
#         assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
#         assert "AssetsImage" in outer_html, "outerHTML 中未找到 AssetsImage"
#
#     @pytest.mark.p0
#     def test_click_contact_information(self):
#         """ 联系方式：判断是否进入联系方式页面 """
#         self.click("//span[contains(text(),'联系方式')]")
#         time.sleep(3)
#         self.assert_text('编辑', "//span[contains(text(),'编辑')]")
#         time.sleep(3)
#         self.assert_text('删除', "//span[contains(text(),'删除')]")
#
#     @pytest.mark.p0
#     def test_click_sub_account_settings(self):
#         """ 子账号设置：判断是否进入该页面 """
#         self.click("//span[contains(text(),'子账号设置')]")
#         time.sleep(3)
#         self.assert_text("子账号管理", "//div[@aria-selected='true']")
#         time.sleep(3)
#         self.assert_text("岗位管理", "//div[@aria-selected='false']")
#         time.sleep(3)
#         self.assert_text("新建子账号", "//span[contains(text(),'新建子账号')]")
#
#     @pytest.mark.p0
#     def test_click_job_management(self):
#         """ 子账号设置：点击"岗位管理"，判断是否进入该页面 """
#         self.click("//span[contains(text(),'我的团长')]")
#         self.click('//div[@role="tab"][contains(text(),"我邀请的")]')
#         self.assert_element('//span[contains(text(),"邀请团长")]')
#
#     @pytest.mark.p2
#     def test_create_new_sub_account(self):
#         """子账号设置：点击新建子账号 - 判断右边抽屉元素是否存在"""
#         self.click("//span[contains(text(),'子账号设置')]")
#         time.sleep(2)
#         # 点击新建子账号
#         self.click("(//button[@type='button'])[4]")
#         time.sleep(1)
#         self.assert_element("(//input[@id='accountForm_nickName'])[1]")
#
#     @pytest.mark.p2
#     def test_job_manage_create_new_job(self):
#         """子账号设置：点击岗位管理tab，点击新疆岗位，判断右边抽屉元素是否存在"""
#         self.click("//span[contains(text(),'子账号设置')]")
#         time.sleep(2)
#         # 点击岗位管理
#         self.click("//div[@id='rc-tabs-0-tab-2']")
#         time.sleep(1)
#         self.click("//span[contains(text(),'新建岗位')]")
#         time.sleep(1)
#         self.assert_element("(//input[@id='accountForm_roleName'])[1]")
#
#     @pytest.mark.p2
#     def test_click_into_juli_plan(self):
#         """聚力计划协议 判断是否进入该页面"""
#         self.click("//li[@id='menu-U_-r4Gcay5Y']//span[@class='dilu-main-menu-title-content']")
#         time.sleep(2)
#         self.click("(//h2[contains(text(),'什么是聚力计划？')])[1]")
#
#     @pytest.mark.p2
#     def test_my_invite_is_exist(self):
#         """我邀请的 - 判断是否进入该页面"""
#         self.click("//span[contains(text(),'我的团长')]")
#         time.sleep(2)
#         self.assert_element("//td[normalize-space()='**********']")
#
#     @pytest.mark.p2
#     def test_invite_me_is_exist(self):
#         """邀请我的 - 判断是否进入该页面"""
#         self.click("//span[contains(text(),'我的团长')]")
#         time.sleep(2)
#         self.click("//div[@id='rc-tabs-0-tab-inviteBy']")
#         self.assert_element("//div[@class='kwaishop-cps-leader-base-pc-pro-page']//div[1]//div[1]//div[1]//div[2]")
