import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长侧--团长选品
pytest test_case/distribution/leader/test_seller_selection_library.py --headless -n=3
"""

@pytest.mark.skip
class TestSellerSelectionLibrary(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")

    @pytest.mark.p1
    def test_selection_library_click_menu(self):
        """ 点击菜单「团长选品」"""
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 判断相应的元素是否有出现
        self.assert_text("团长选品库", "//div[@class='F8WolK1LCijjMa_lmg7Y']")
        self.assert_element("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[1]")
        time.sleep(1)
        # 点击规格判断是否出现相关弹窗
        self.click("(//a[contains(text(),'规格')])[1]")
        time.sleep(1)
        self.assert_element("(//div[@class='kwaishop-cps-leader-base-pc-modal-body'])[1]")

    @pytest.mark.p1
    def test_selection_library_appear_specification(self):
        """ 规格弹窗 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 判断相应的元素是否有出现
        self.assert_text("团长选品库", "//div[@class='F8WolK1LCijjMa_lmg7Y']")
        self.assert_element("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[1]")
        time.sleep(1)
        # 点击规格判断是否出现相关弹窗
        self.click("(//a[contains(text(),'规格')])[1]")
        time.sleep(1)
        self.assert_element("(//div[@class='kwaishop-cps-leader-base-pc-modal-body'])[1]")

    @pytest.mark.p1
    def test_selection_library_appear_specification(self):
        """ 筛选排序「商品价格 & 佣金率 & 服务费率 & 30日分销销量 & 上架达人数」 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 判断相应的元素是否有出现
        self.assert_text("团长选品库", "//div[@class='F8WolK1LCijjMa_lmg7Y']")
        self.assert_element("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'])[1]")
        time.sleep(1)
        # 点击全部筛选
        self.click("//span[@class='anticon anticon-system-arrow-large-down-line']//*[name()='svg']")
        time.sleep(1)

        # 判断价格范围
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-select-selector'])[1]")
        time.sleep(1)

        # 判断佣金范围
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-select-selector'])[2]")
        time.sleep(1)

        # 判断服务费范围
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-select-selector'])[3]")
        time.sleep(1)

        # 判断可推广时间
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-select-selector'])[4]")
        time.sleep(1)

        # 判断30日销量
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-select-selector'])[5]")
        time.sleep(1)

        # 判断店铺体验分
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-select-selector'])[6]")
        time.sleep(1)

    @pytest.mark.p1
    def test_selection_library_click_contact_commander(self):
        """ 点击「联系团长」 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        self.click("(//span[contains(text(),'联系团长')])[1]")
        time.sleep(1)
        self.click("//span[contains(text(),'复 制')]")
        time.sleep(1)
        self.assert_element(
            "(//div[@class='kwaishop-cps-leader-base-pc-message-custom-content kwaishop-cps-leader-base-pc-message-success'])[1]")

    # 点击「加入活动」
    @pytest.mark.p1
    def test_selection_library_click_attend_activity(self):
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        self.click("(//a[contains(text(),'加入活动')])[1]")
        time.sleep(1)
        self.assert_element("//div[@class='OUJ_vqAM0yoUAgTN0OqG']")

    # 点击「全部筛选」
    @pytest.mark.p1
    @pytest.mark.skip
    def test_selection_library_select_for_all(self):
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 点击全部筛选
        self.click("//span[@class='anticon anticon-system-arrow-large-down-line']//*[name()='svg']")
        time.sleep(1)
        # 判断是否出现商瓶类目的字端
        self.assert_text("商品类目",
                         "//body/div/div/div[contains(@class,'kwaishop-cps-leader-base-pc-popover kwaishop-cps-leader-base-pc-popover-placement-bottomLeft')]/div[@class='kwaishop-cps-leader-base-pc-popover-content']/div[@role='tooltip']/div[@class='kwaishop-cps-leader-base-pc-popover-inner-content']/div[@class='kpro-cps-filter-popup-drop-popup']/div[1]/div[1]")
        time.sleep(1)
        self.assert_element("//span[contains(text(),'女装女鞋')]")
        self.assert_element(
            "//body/div/div/div[contains(@class,'kwaishop-cps-leader-base-pc-popover kwaishop-cps-leader-base-pc-popover-placement-bottomLeft')]/div[@class='kwaishop-cps-leader-base-pc-popover-content']/div[@role='tooltip']/div[@class='kwaishop-cps-leader-base-pc-popover-inner-content']/div[@class='kpro-cps-filter-popup-drop-popup']/div[1]/div[1]")
        time.sleep(1)
        self.assert_element("//div[contains(text(),'佣金范围')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_selection_library_select_for_category(self):
        """ 类目筛选 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 点击全部筛选
        self.click("//span[@class='anticon anticon-system-arrow-large-down-line']//*[name()='svg']")
        time.sleep(1)
        # 判断是否有商品类目字样
        self.assert_text("商品类目",
                         "//body/div/div/div[contains(@class,'kwaishop-cps-leader-base-pc-popover kwaishop-cps-leader-base-pc-popover-placement-bottomLeft')]/div[@class='kwaishop-cps-leader-base-pc-popover-content']/div[@role='tooltip']/div[@class='kwaishop-cps-leader-base-pc-popover-inner-content']/div[@class='kpro-cps-filter-popup-drop-popup']/div[1]/div[1]")
        time.sleep(1)
        self.assert_element("//span[contains(text(),'女装女鞋')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_selection_library_select_for_commission(self):
        """ 佣金筛选 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 点击全部筛选
        self.click("//span[@class='anticon anticon-system-arrow-large-down-line']//*[name()='svg']")
        time.sleep(1)
        self.assert_element("//div[contains(text(),'佣金范围')]")

    @pytest.mark.p2
    def test_product_and_spec_images(self):
        """ 商品和规格图片 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()

        img_xpath = [
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div/img',
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[1]/div/img',
            '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[5]/td[1]/div/img'
        ]

        # for xpath in img_xpath:
        #     img = self.find_element(xpath)
        #
        #     # 校验 width 和 height
        #     assert img.get_attribute("width") == "60", "宽度不正确"
        #     assert img.get_attribute("height") == "60", "高度不正确"
        #
        #     # 校验 data-component-name
        #     outer_html = img.get_attribute("outerHTML")
        #     assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
        #     assert "AssetsImage" in outer_html, "outerHTML 中未找到 AssetsImage"

    @pytest.mark.p1
    @pytest.mark.skip
    def test_selection_library_select_for_sell_number_monthly(self):
        """ 30日销量筛选 """
        self.click("//li[@id='menu-d4vqRteJMIk']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(3)
        self.refresh()
        time.sleep(2)
        # 点击全部筛选
        self.click("//span[@class='anticon anticon-system-arrow-large-down-line']//*[name()='svg']")
        time.sleep(1)
        self.assert_element("//div[contains(text(),'30日销量')]")
