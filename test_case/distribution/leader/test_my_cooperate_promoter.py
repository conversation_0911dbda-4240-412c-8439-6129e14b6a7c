import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长成长
-团长等级
-团长任务
python3 -m pytest test_case/distribution/leader/test_my_cooperate_promoter.py
"""


class TestMyCooperatePromoter(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.sleep(5)

    @pytest.mark.p2
    def test_click_leader_cooperate_data(self):
        """
        我的合作达人：点击数据指标说明-查看弹窗元素是否存在
        """
        self.click("//li[@id='menu-QJQml_4UPkA']//span[@class='dilu-main-menu-title-content']")
        self.sleep(2)
        self.click("//span[contains(text(),'数据指标说明')]")
        self.sleep(2)
        self.assert_text("数据指标说明", "//div[@class='cps-data-desc-modal-title']")

    @pytest.mark.p1
    def test_click_leader_cooperate_search(self):
        """
        我的合作达人：在搜索框输入内容-判断搜索结果是否正常 // 2428997147
        """
        self.click("//li[@id='menu-QJQml_4UPkA']//span[@class='dilu-main-menu-title-content']")
        self.type("//input[@id='keyWord']", "2428997147")
        self.click(
            "//button[@class='kwaishop-cps-leader-base-pc-btn kwaishop-cps-leader-base-pc-btn-icon-only kwaishop-cps-leader-base-pc-input-search-button']")
        self.assert_text("共1条数据", "//li[@class='kwaishop-cps-leader-base-pc-pagination-total-text']")

    @pytest.mark.p1
    def test_click_leader_cooperate_skip_detail(self):
        """
        我的合作达人：点击出单数据-判断跳转是否正常
        """
        self.click("//li[@id='menu-QJQml_4UPkA']//span[@class='dilu-main-menu-title-content']")
        self.type("//input[@id='keyWord']", "2428997147")
        self.click("//span[contains(text(),'出单数据')]")
        time.sleep(2)
        self.assert_text("推广者", "//div[@id='rc-tabs-0-tab-1']")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_click_leader_cooperate_skip_name(self):
        """
        我的合作达人：点击达人昵称-判断跳转是否正常
        """
        self.click("//li[@id='menu-QJQml_4UPkA']//span[@class='dilu-main-menu-title-content']")
        self.sleep(2)
        self.type("//input[@id='keyWord']", "2428997147")
        self.click("//div[@class='BC7bBdBq0f11ZICWgEGS']")
        self.sleep(2)
        self.assert_text("概况", "(//div[@id='rc-tabs-0-tab-situation'])[1]")

    @pytest.mark.p1
    def test_click_leader_cooperate_choice(self):
        """
        我的合作达人：点击全部筛选按钮-判断筛选弹窗元素是否存在
        """
        self.sleep(5)
        self.click("//li[@id='menu-QJQml_4UPkA']//span[@class='dilu-main-menu-title-content']")
        time.sleep(2)
        self.click("//span[contains(text(),'全部筛选')]")
        time.sleep(2)
        self.assert_text("直播型达人", "//span[contains(text(),'直播型达人')]")

