import time
from datetime import datetime

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
"""
团长
体验指标页面
python3 -m pytest test_case/distribution/leader/test_promoter_investment.py --headless -n=5
"""


class TestExperienceIndexManagement(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.sleep(2)

    @pytest.mark.p0
    def test_Experience_index_management(self):
        """侧边栏点击体验指标管理-查看页面元素是否存在"""
        self.click('//*[@id="menu-FY9AOyLeAkI"]/span/span/span/span')
        self.sleep(2)
        """断言标题"""
        self.find_element("//li[@id='menu-FY9AOyLeAkI']//span[@class='dilu-main-menu-title-content']")
        self.sleep(2)
        """断言tab1"""
        self.find_element("//div[@id='rc-tabs-0-tab-1']")
        self.sleep(2)
        """断言tab2"""
        self.find_element("//div[@id='rc-tabs-0-tab-2']")

    @pytest.mark.p1
    def test_Tab_Toggle(self):
        """侧边栏点击体验指标管理-切换tab"""
        self.click('//*[@id="menu-FY9AOyLeAkI"]/span/span/span/span')
        self.sleep(2)
        """点击tab2"""
        self.click("//div[@id='rc-tabs-0-tab-2']")
        self.sleep(2)
        """点击tab1"""
        self.click("//div[@id='rc-tabs-0-tab-1']")
        self.sleep(2)

    @pytest.mark.p1
    def test_Tab1_fields(self):
        """侧边栏点击体验指标管理-判断tab1字段"""
        self.click('//*[@id="menu-FY9AOyLeAkI"]/span/span/span/span')
        self.sleep(2)
        self.find_element("//th[contains(text(),'授权二创作者')]")
        self.find_element("//th[contains(text(),'出让二创作者佣金比例')]")
        self.find_element("//th[contains(text(),'授权周期')]")
        self.find_element("//div[contains(text(),'短视频举报率')]")
        self.find_element("//div[contains(text(),'短视频负向率')]")
        self.find_element("//div[contains(text(),'商品品退率')]")
        self.find_element("//div[contains(text(),'商品差评率')]")



    @pytest.mark.p1
    def test_Tab2_fields(self):
        """侧边栏点击体验指标管理-判断tab1字段"""
        self.click('//*[@id="menu-FY9AOyLeAkI"]/span/span/span/span')
        self.sleep(2)
        """点击tab2"""
        self.click("//div[@id='rc-tabs-0-tab-2']")
        self.sleep(2)
        self.find_element("//th[contains(text(),'授权二创作者')]")
        self.find_element("//th[contains(text(),'出让二创作者佣金比例')]")
        self.find_element("//th[contains(text(),'授权周期')]")
        self.find_element("//div[contains(text(),'短视频片段')]")
        self.find_element("//div[contains(text(),'短视频举报率')]")
        self.find_element("//div[contains(text(),'短视频负向率')]")
        self.find_element("//div[contains(text(),'商品品退率')]")
        self.find_element("//div[contains(text(),'商品差评率')]")
        self.find_element(
            "//th[@class='kwaishop-cps-leader-base-pc-table-cell kwaishop-cps-leader-base-pc-table-cell-fix-right kwaishop-cps-leader-base-pc-table-cell-fix-right-first']")



