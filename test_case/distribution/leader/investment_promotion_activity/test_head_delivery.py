import re
import time
from datetime import date

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

# 获取当前日期  获取日期中的日
current_date = date.today()

day = current_date.day

current_timestamp = int(time.time())
"""
团长商品投放
"""


class TestHeadDelivery(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    def test_entry_main_page_is_normal(self):
        """进入团长商品投放页面"""
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(1)
        self.assert_element("//div[@class='kwaishop-cps-leader-base-pc-pro-toolbar-table-actions']")
        self.assert_element("//label[contains(text(),'商品投放状态')]")


    # @pytest.mark.p1
    # def test_add_a_good_delivery(self):
    #     """添加一个商品投放"""
    #     self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
    #     self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
    #     time.sleep(2)
    #     self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
    #     time.sleep(1)
    #
    #
    # @pytest.mark.p1
    # def test_delete_a_good_delivery(self):
    #     """删除一个已结束的投放"""
    #     self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
    #     self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
    #     time.sleep(2)
    #     self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
    #     time.sleep(1)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_entry_main_page_select_tab(self):
        """进入团长商品投放页面，筛选商品投放状态"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(2)

        # 筛选投放已结束的品
        self.click("(//span[@title='不限'])[1]")
        self.sleep(2)
        self.click("div.kwaishop-cps-leader-base-pc-select-item.kwaishop-cps-leader-base-pc-select-item-option:nth-child(3)")
        self.sleep(1)
        self.click("(//button[@type='submit'])[1]")
        self.sleep(1)

        # 翻页
        self.click("(//a[normalize-space()='2'])[1]")
        self.sleep(1)

        # 根据商品id对列表进行筛选
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21751781025434")
        self.click("(//button[@type='submit'])[1]")
        self.sleep(1)

        # # 对比列表的元素数量是否和后端传的数量一致
        # num = self.find_element("(//li[@class='kwaishop-cps-leader-base-pc-pagination-total-text'])[1]").text
        # num = re.search(r'\d+', num).group()
        # if self.is_element_visible("(//td[@class='kwaishop-cps-leader-base-pc-table-cell'][contains(text(),'投放已结束')])[" + str(int(num) + 1) + "]") :
        #     raise Exception("Element found, throwing error")

    @pytest.mark.p1
    def test_entry_main_page_UnderWayList(self):
        """进入团长商品投放页面，字段判断"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(2)
        self.click("(//span[@title='不限'])[1]")
        self.sleep(2)
        self.click(
            "//div[contains(@title,'投放中')]//div[1]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'查 询')]")
        self.sleep(1)
        self.assert_text("商品信息", "//th[contains(text(),'商品信息')]")
        self.assert_text("商家信息", "//th[contains(text(),'商家信息')]")
        self.assert_text("投放合作费率", "//div[contains(text(),'投放合作费率')]")
        self.assert_text("投放合作时间", "//div[contains(text(),'投放合作时间')]")
        self.assert_text("商品投放状态", "//th[contains(text(),'商品投放状态')]")
        self.assert_text("所属活动信息", "//th[contains(text(),'所属活动信息')]")
        self.assert_text("活动类型", "//th[contains(text(),'活动类型')]")
        self.assert_text("操作", "//th[contains(text(),'操作')]")
        self.sleep(2)

    @pytest.mark.p1
    def test_entry_main_page_UnlimitedList(self):
        """进入团长商品投放页面，商家联系方式弹窗"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(2)
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21733256706434")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.click("//span[@aria-label='share-phone-line']")
        self.sleep(2)
        img_xpath = ["//div[@class='kwaishop-cps-leader-base-pc-modal-content']"]
        # 判断联系人弹窗元素出现
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "kwaishop-cps-leader-base-pc-modal-content" in outer_html, "缺少 kwaishop-cps-leader-base-pc-modal-content 属性"


    @pytest.mark.p1
    def test_entry_main_page_OperationButton(self):
        """进入团长商品投放页面，操作列按钮可点击"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        time.sleep(2)
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21781560530147")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        DeleteButton = self.find_element("//tbody/tr[1]/td[9]/div[1]/button[1]/span[1]")
        CooperationButton = self.find_element("//span[contains(text(),'合作查询')]")
        # 判断按钮是否可点击
        self.assert_equal(DeleteButton.is_enabled(), True)
        self.assert_equal(CooperationButton.is_enabled(), True)
        self.assert_text("开启投放", "//span[@class='kwaishop-cps-leader-base-pc-tooltip-disabled-compatible-wrapper']")

    @pytest.mark.p1
    def test_entry_main_page_SidePopupWindow(self):
        """进入团长商品投放页面，点击合作查询按钮-侧边栏弹出"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        time.sleep(2)
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21781560530147")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click("//span[contains(text(),'合作查询')]")
        img = self.find_element(
            "//div[@class='kwaishop-cps-leader-base-pc-drawer-content-wrapper']")
        # 校验 data-component-name
        outer_html = img.get_attribute("outerHTML")
        assert "kwaishop-cps-leader-base-pc-drawer-content-wrapper" in outer_html, "缺少kwaishop-cps-leader-base-pc-drawer-content-wrapper 属性"


    @pytest.mark.p1
    def test_entry_main_page_tap(self):
        """进入团长商品投放页面，点击合作查询按钮-侧边栏弹出-字段正常展示"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.sleep(2)
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(2)
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21781560530147")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.click("//span[contains(text(),'合作查询')]")
        self.assert_text("合作查询", "//div[@class='kwaishop-cps-leader-base-pc-drawer-title']")
        self.assert_text("合作二级团长信息", "//th[contains(text(),'合作二级团长信息')]")
        self.assert_text("活动名称", "//th[contains(text(),'活动名称')]")
        self.assert_text("商品二级团合作周期", "//th[contains(text(),'商品二级团合作周期')]")



    @pytest.mark.p1
    def test_entry_main_page_picture(self):
        """进入团长商品投放页面，点击合作查询按钮-侧边栏弹出-图片正常展示"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(2)
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21781560530147")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.click("//span[contains(text(),'合作查询')]")
        img = self.find_element(
            '/html/div/div/div[2]/div/div/div[2]/div[1]/img')
        # 校验 width 和 height
        assert img.get_attribute("width") == "60", "宽度不正确"
        assert img.get_attribute("height") == "60", "高度不正确"
        assert img.get_attribute('src') != "none" or ''

    @pytest.mark.p1
    def test_entry_main_page_click(self):
        """进入团长商品投放页面，点击合作查询按钮-侧边栏弹出-点击我知道了按钮"""
        # 进入团长商品投放页面
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.sleep(2)
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'团长商品投放')]")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[3]/button/span')
        self.sleep(2)
        self.type("//input[contains(@placeholder,'请输入商品名称或商品id')]", "21781560530147")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.click("//span[contains(text(),'合作查询')]")
        self.sleep(2)
        self.click("//span[contains(text(),'我知道了')]")
        self.sleep(2)
