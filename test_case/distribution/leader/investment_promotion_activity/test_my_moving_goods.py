import pytest

from test_case.distribution.distribution_base_case import *

"""
招商活动
-我的活动商品
pytest test_case/distribution/leader/investment_promotion_activity/test_my_moving_goods.py --headless -n=3
"""


class TestMyMovingGoods(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    @pytest.mark.skip
    def test_commodity_information_inquiry(self):
        """
        通过商品信息查询
        输入商品ID，点击查询，判断商品ID是否存在下面商品卡片中
        """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        self.click("//span[contains(text(), '我的活动商品')]")
        self.type("input[placeholder='请输入商品名称或商品ID']", "21306385599434")
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_text("21306385599434", "div[class='ptI9WdfCB6mEn6dcBT0S'] span:nth-child(1)")
        self.assert_text("分销UI测试商品勿拍20231024144249", "div[class='ptI9WdfCB6mEn6dcBT0S'] span:nth-child(1)")

    # 点击我报名的招商活动tab-判断切换是否正常
    @pytest.mark.p2
    def test_check_tab_change_normal(self):
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        # 点击 我的活动商品
        self.click("//span[contains(text(), '我的活动商品')]")
        time.sleep(2)
        # 关闭弹窗
        self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        # 点击 我报名的招商活动
        self.click("(//div[@class='kwaishop-cps-pc-micro-leader-tabs-tab'])[1]")
        time.sleep(2)
        self.assert_element(
            "(//div[@class='kwaishop-cps-pc-micro-leader-row kwaishop-cps-pc-micro-leader-pro-form-layout-row'])[1]")

    # 点击商品标题-判断右边抽屉元素是否存在
    @pytest.mark.p2
    def test_check_drawer_appear(self):
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        time.sleep(1)
        # 点击 我的活动商品
        self.click("//span[contains(text(), '我的活动商品')]")
        # 弹窗点击
        try:
            self.is_element_visible("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
            self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        except:
            self.is_element_visible("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
            self.click("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
        time.sleep(2)
        # 搜索固定商品 点击后判断
        self.input("(//input[@placeholder='请输入商品名称或商品ID'])[1]", "21463825982434")
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(1)
        # 点击商品标题
        self.click("(//span[@class='itemLink___AJB30'])[1]")
        time.sleep(1)
        # 判断右侧抽屉元素
        self.assert_element("(//div[@class='kwaishop-cps-pc-micro-leader-drawer-body'])[1]")

    @pytest.mark.p2
    @pytest.mark.skip
    def test_activity_information_jump_normal(self):
        """ 点击活动信息-我发起的招商活动-判断跳转是否正常 """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        # 点击 我的活动商品
        self.click("//span[contains(text(), '我的活动商品')]")
        time.sleep(2)
        # 弹窗点击
        try:
            self.is_element_visible("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
            self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        except:
            self.is_element_visible("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
            self.click("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")

        time.sleep(2)
        self.input("(//input[@placeholder='请输入商品名称或商品ID'])[1]", "21463825982434")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        # 点击具体的活动信息
        self.click("//a[contains(text(),'下单验证-勿动')]")
        self.assert_url_contains("/pc/leader/zone-leader/my-sponsor-activity-detail?activityId")
        self.find_element("#__Kpro-workbench-scroll-content__")

    @pytest.mark.p0
    def test_commodity_history_deatil(self):
        """点击处理合作中申请按钮-判断抽屉元素是否存在"""
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        self.click("//span[contains(text(), '我的活动商品')]")
        """ 关闭商品投放弹窗 """
        self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        time.sleep(2)
        self.click("(//a[contains(text(),'合作中申请')])[1]")
        self.assert_element("(//span[contains(text(),'查 询')])[2]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_commodity_history_deatil_tab_skip(self):
        """点击处理合作中申请按钮-点击历史处理tab-判断tab切换是否正常"""
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        self.click("//span[contains(text(), '我的活动商品')]")
        """ 关闭商品投放弹窗 --> 关闭 我知道了 """
        self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        time.sleep(1)
        # 弹窗点击
        if self.is_element_visible("(//button[@class='driver-close-btn driver-close-only-btn'])[1]"):
            self.click("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
        time.sleep(2)
        self.click("//a[contains(text(),'合作中申请')]")
        # 弹窗点击
        if self.is_element_visible("(//button[@class='driver-close-btn driver-close-only-btn'])[1]"):
            self.click("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'历史处理')]")
        self.assert_element("//th[contains(text(),'合作方信息')]")

    @pytest.mark.p0
    def test_commodity_order_detail(self):
        """点击出单数据-判断跳转是否正常"""
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        self.click("//span[contains(text(), '我的活动商品')]")
        time.sleep(2)
        # 弹窗点击
        if self.is_element_visible("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]"):
            self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        time.sleep(1)
        if self.is_element_visible("(//button[@class='driver-close-btn driver-close-only-btn'])[1]"):
            self.click("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
        self.click("(//span[contains(text(),'出单数据')])[1]")
        time.sleep(3)
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-tabs-tab'])[1]")

    @pytest.mark.p0
    def test_commodity_tab_skip(self):
        """点击我报名的招商活动tab-判断元素是否存在"""
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        self.click("//span[contains(text(), '我的活动商品')]")
        time.sleep(2)
        # 弹窗点击
        if self.is_element_visible("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]"):
            self.click("(//span[@class='kwaishop-cps-pc-micro-leader-modal-close-x'])[1]")
        time.sleep(1)
        if self.is_element_visible("(//button[@class='driver-close-btn driver-close-only-btn'])[1]"):
            self.click("(//button[@class='driver-close-btn driver-close-only-btn'])[1]")
        self.click("(//div[@class='kwaishop-cps-pc-micro-leader-tabs-tab'])[1]")
        time.sleep(3)
        self.click("(//th[contains(text(),'活动信息')])[1]")

    @pytest.mark.p0
    def test_my_lose_commodity(self):
        """
        我的活动商品-跳转至30天内失效的活动商品
        """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        self.click("//span[contains(text(), '我的活动商品')]")
        self.click("(//a[contains(text(),'去查看')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'失效原因')])[1]")

    @pytest.mark.p0
    def test_my_lose_commodity_list(self):
        """
        我的活动商品-跳转至30天内失效的活动商品-查看商品列表是否正常展示
        """
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        self.click("//span[contains(text(), '我的活动商品')]")
        self.click("(//a[contains(text(),'去查看')])[1]")
        time.sleep(2)
        self.click("(//div[contains(text(),'已失效')])[1]")
