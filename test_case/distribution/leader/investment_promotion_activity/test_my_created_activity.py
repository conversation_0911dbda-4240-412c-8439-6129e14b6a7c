import time
from datetime import date
from datetime import datetime

import pytest
from selenium.webdriver.common.by import By

from test_case.distribution.distribution_base_case import DistributionToolTest

# 获取当前日期  获取日期中的日
current_date = date.today()

day = current_date.day

# 获取当前日期
today = datetime.now()
now_day = today.day

"""
招商活动
-我发起的活动
python3 -m pytest test_case/distribution/leader/investment_promotion_activity/test_my_created_activity.py --headless -n=5
"""
domain = "DISTRIBUTION_LEADER_PRT"


class TestMyCreatedActivity(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        time.sleep(3)

    def test_create_activity_page(self):
        """
        发起活动页面，判断三种活动类型都有展示
        """
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("(//span[contains(text(),'发起活动')])[1]")
        self.assert_text("普通招商", "(//div[contains(text(),'普通招商')])[1]")
        self.assert_text("专属招商", "(//div[contains(text(),'专属招商')])[1]")
        self.assert_text("合作招商", "(//div[contains(text(),'合作招商')])[1]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_create_normal_investment(self):
        """我发起的活动-创建一个普通招商活动"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("div[class='kwaishop-cps-pc-micro-leader-tabs-extra-content'] span")
        self.sleep(1)
        self.click("div[class='normal-activity'] button[type='button']")
        self.sleep(1)
        current_timestamp = int(time.time())
        self.input("input[placeholder='活动标题对达人可见，请谨慎填写']", f"快手分销测试普通招商活动{current_timestamp}")
        self.sleep(2)
        self.click(".el-input__icon.el-range__icon.el-icon-date")
        self.sleep(1)

        # 选择开始日期 & 结束日期
        self.click("//button[contains(text(),'未来一周')]")
        self.sleep(1)
        self.click("button[class='el-button el-button--primary'] span")
        self.sleep(1)
        self.click("button[class='el-button el-button--default el-button--small el-button--primary '] span")
        self.sleep(1)
        """ 断言并关闭活动 """
        self.sleep(1)
        self.assert_element('//div[@class="kpro-workbench-layout-header__container"]')
        self.sleep(3)
        self.click("//tbody/tr[1]/td[6]/div[1]/div[3]/button[1]/span[1]")
        self.sleep(1)
        self.click('//span[contains(text(),"确 定")]')

    @pytest.mark.p0
    def test_create_unpublished_normal_investment(self):
        """我发起的活动-创建一个未发布的普通招商活动"""
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.sleep(5)
        self.click("//span[contains(text(),'发起活动')]")
        self.sleep(5)
        self.click("(//button[@type='button'])[1]")
        self.sleep(1)
        current_timestamp = int(time.time())
        self.input("input[placeholder='活动标题对达人可见，请谨慎填写']", f"快手分销测试普通招商活动{current_timestamp}")
        self.sleep(2)
        self.click("//input[@id='normalInvestmentForm_time']")

        # 选择开始日期 & 结束日期
        self.click("//span[contains(text(),'未来一周')]")

        self.sleep(1)
        self.click("//span[contains(text(),'保存草稿')]")
        self.sleep(1)
        self.click("//span[contains(text(),'保 存')]")
        self.sleep(2)
        self.sleep(5)
        self.assert_element("(//th[contains(text(),'活动信息')])[1]")

        # 删除
        self.click("//span[contains(text(),'未发布')]")
        self.click(
            "//*[@id='root']/section/main/div/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[6]/div/div[2]/button/span[contains(text(),'删除')]")
        self.click("//span[contains(text(),'确 定')]")

    # @pytest.mark.p0
    # def test_create_unpublished_exclusive_investment(self):
    #     """我发起的活动-创建一个未发布的专属招商活动"""
    #     self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
    #     self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
    #     """ 选择专属招商活动 """
    #     self.click("//span[contains(text(),'发起活动')]")
    #     time.sleep(5)
    #     self.click("body > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(3) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(3) > div:nth-child(2) > button:nth-child(4) > span:nth-child(1)")
    #     self.input("input[placeholder='活动标题对达人可见，请谨慎填写']", f"分销UI自动化测试专属招商{current_timestamp}")
    #
    #     """ 选择开始日期 & 结束日期 """
    #     time.sleep(5)
    #     self.click('.el-input__icon.el-range__icon.el-icon-date')
    #     self.click("//button[contains(text(),'未来一周')]")
    #     # self.click("//span[contains(text(),'未来一周')]")
    #     time.sleep(1)
    #     self.input(".cps-materials-multi-input-input", f"2428997147")
    #     self.click("//div[@role='tooltip']")
    #     self.sleep(1)
    #     self.click("//span[contains(text(),'保存草稿')]")
    #     time.sleep(1)
    #     self.click("//span[contains(text(),'保 存')]")
    #     time.sleep(1)
    #     self.assert_element(".kwaishop-cps-pc-micro-leader-card-body")
    #
    #     """ 断言并关闭活动 """
    #     time.sleep(1)
    #     self.assert_element('//div[@class="kpro-workbench-layout-header__container"]')
    #     time.sleep(3)
    #     self.click("//tbody/tr[1]/td[6]/div[1]/div[3]/button[1]/span[1]")
    #     self.click('//span[contains(text(),"确 定")]')
    #     self.find_element('//span[contains(text(),"操作成功")]')

    # @pytest.mark.p0
    # def test_create_exclusive_investment(self):
    #     """我发起的活动-创建一个专属招商活动"""
    #     self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
    #     self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
    #     self.click("div[class='kwaishop-cps-pc-micro-leader-tabs-extra-content'] span")
    #     self.click("div[class='special-activity'] button[type='button']")
    #     '''填写标题-确认日期'''
    #     time_str = datetime.datetime.now().strftime('%Y年%m月%d日%H:%M:%S')
    #     self.input("input[placeholder='活动标题对达人可见，请谨慎填写']", f"分销UI自动化测试招商活动-请勿报名{time_str}")
    #     self.click(".el-input__icon.el-range__icon.el-icon-date")
    #     self.click("//button[contains(text(),'未来一周')]")
    #     self.click("button[class='el-button el-button--primary'] span")
    #     '''确认专属达人'''
    #     self.click("//input[@class='el-select__input']")
    #     self.input("//input[@class='el-select__input']", "2995700434")
    #     self.click(".el-checkbox__inner")
    #     self.click("//button[@class='el-button el-button--primary']")
    #     self.click("button[class='el-button el-button--default el-button--small el-button--primary '] span")
    #
    #     self.click("//div[@class='kwaishop-cps-pc-micro-leader-tabs-tab']")
    #     self.assert_text("UI自动化测试招商活动-请勿报名", "//div[@class='kwaishop-cps-pc-micro-leader-pro-table']")

    @pytest.mark.p0
    def test_delete_exclusive_investment(self):
        """ 我发起的活动-删除已结束的普通招商活动 """
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("//span[contains(text(),'已结束')]")
        time.sleep(2)

        self.click("(//span[contains(text(),'删除')])[1]")
        self.click("//body//div//button[2]")

    @pytest.mark.p0
    def test_copy_master_registration_link(self):
        """我发起的活动-团长复制链接"""
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("//tbody/tr[1]/td[6]/div[1]/div[1]/button[1]/span[1]")
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[1]/button[1]/span[1]").click()
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[1]/button[1]/span[1]").click()
        self.assert_element("//span[contains(text(),'已复制活动链接')]")

    @pytest.mark.p0
    def test_copy_merchant_registration_link(self):
        """我发起的活动-复制商家报名链接"""
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("//tbody/tr[1]/td[6]/div[1]/div[2]/button[1]/span[1]")
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[2]/button[1]/span[1]").click()
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[2]/button[1]/span[1]").click()
        self.assert_element("//span[contains(text(),'已复制活动链接')]")

    # @pytest.mark.p0
    # def test_create_two_normal_investment(self):
    #     """我发起的活动-创建一个普通招商活动"""
    #     self.merchant_login("DISTRIBUTION_LEADER", "liuxiaohui07")
    #     self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
    #     self.click("div[class='kwaishop-cps-pc-micro-leader-tabs-extra-content'] span")
    #     self.click("div[class='normal-activity'] button[type='button']")
    #
    #     time_str = datetime.datetime.now().strftime('%Y年%m月%d日%H:%M:%S')
    #     self.input("input[placeholder='活动标题对达人可见，请谨慎填写']",
    #                f"分销UI自动化测试二级招商活动-真实商品请勿报名{time_str}")
    #     time.sleep(2)
    #     self.click(".el-input__icon.el-range__icon.el-icon-date")
    #     self.click("//button[contains(text(),'未来一周')]")
    #     self.click("button[class='el-button el-button--primary'] span")
    #     self.click("button[class='el-button el-button--default el-button--small el-button--primary '] span")
    #     time.sleep(2)
    #     self.assert_text("分销UI自动化测试二级招商活动-真实商品请勿报名", "//div[@class='kwaishop-cps-pc-micro-leader-pro-table']")
    #     # 保存创建的普通活动ID  给二级团长报名使用
    #     current_dir = os.path.dirname(os.path.abspath(__file__))
    #     parent_dir = os.path.dirname(current_dir)
    #     parent_dir = os.path.dirname(parent_dir)
    #     file_path = os.path.join(parent_dir, 'leader/TwoGeneralInvestmentActivities.txt')
    #     self.sleep(2)
    #     text = self.get_text(
    #         "/html/body/div[1]/div/div/div/div[3]/div[2]/div/div/div/div/div/section/main/div/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div[1]")
    #     text = text.replace("ID：", "")
    #     time.sleep(2)
    #     with open(file_path, "a") as f:
    #         f.write(f"{text}" + "\n")

    @pytest.mark.p0
    def test_my_create_activity_information_filters(self):
        """
        我发起的活动 进入一个招商活动详情，查询商品或团长信息
        查询达人ID，断言 达人ID 是否存在列表的商品卡中
        """
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.type("input[placeholder='请输入活动ID或名称']", "5925250008")
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click("//a[contains(text(),'分销UI自动化测试招商活动-请勿报名2023年11月23日11:22:37')]")
        self.click("//*[@id='root']/section/main/div/div[1]/div[1]/div/div[2]")
        time.sleep(2)
        self.type("input[placeholder='请输入商品ID']", "21422914767434")
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click("//span[contains(text(),'重 置')]")
        self.type("input[placeholder='请输入商家ID']", "2428997147")
        self.click("//span[contains(text(),'查 询')]")
        # time.sleep(2)
        # print("断言 达人ID 是否存在商品卡中")
        # self.assert_text("2428997147", "//tbody/tr[2]")

    @pytest.mark.p0
    def test_exclusive_investment_tab_skip(self):
        """点击专属招商tab-判断tab切换是否正常"""
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(2)
        # 点击已结束tab
        self.click("(//label[@class='kwaishop-cps-pc-micro-leader-radio-button-wrapper'])[2]")
        time.sleep(2)
        self.click(
            "//span[@class='kwaishop-cps-pc-micro-leader-radio-button kwaishop-cps-pc-micro-leader-radio-button-checked']")
        self.click("(//span[contains(text(),'专属招商')])[1]")

    # 进入一个招商活动详情，查看推广效果元素是否正常
    @pytest.mark.p2
    def test_activity_detail_spread_element_normal(self):
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)
        self.click("tbody tr:nth-child(1) td:nth-child(1) a:nth-child(1)")
        self.assert_element("(//div[@class='kwaishop-cps-pc-micro-leader-card-body'])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_manage_activity_promoter(self):
        """ 专属招商 管理活动达人 """
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("//div[@id='rc-tabs-0-tab-2']")
        time.sleep(2)
        xpath = "(//button[@type='button'])"
        buttons = self.find_elements(xpath, By.XPATH)
        if len(buttons) > 0:
            for button in buttons:
                outer_html = button.get_attribute("outerHTML")
                if ("disabled" in outer_html or
                        "kwaishop-cps-pc-micro-leader-btn kwaishop-cps-pc-micro-leader-btn-link" not in outer_html): # 按钮禁用
                    continue
                button.click()
                break
        self.assert_element("//div[@class='kwaishop-cps-pc-micro-leader-drawer-body']")

    # 搜索活动，判断搜索结果是否正确
    @pytest.mark.p2
    @pytest.mark.skip
    def test_search_activity_is_normal(self):
        self.merchant_login(domain, "huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)
        self.input("(//input[@placeholder='请输入活动ID或名称'])[1]", "7823522008")
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(1)
        self.find_element("(//span[contains(text(),'删除')])[1]")

    @pytest.mark.p0
    def test_investment_not_open(self):
        """点击未发布tab-判断tab切换是否正常"""
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("(//label[@class='kwaishop-cps-pc-micro-leader-radio-button-wrapper'])[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'未发布')])[2]")

    @pytest.mark.p0
    def test_investment_close(self):
        """点击未发布tab-判断tab切换是否正常"""
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click("(//label[@class='kwaishop-cps-pc-micro-leader-radio-button-wrapper'])[2]")
        time.sleep(2)
        self.click("(//span[contains(text(),'已结束')])[2]")

    @pytest.mark.p0
    def test_delete_exclusive_investment_promo_ended(self):
        """ 我发起的活动-删除已结束的专属招商活动 """
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click('//div[contains(text(),"专属招商")]')
        self.click('//span[contains(text(),"已结束")]')
        """ 删除一个结束的专属招商活动 """
        self.click('(//span[contains(text(),"删除")])[1]')
        self.click('//span[contains(text(),"确 定")]')

    @pytest.mark.p0
    # @pytest.mark.skip
    def test_create_cooperative_investment_promotion_activities(self):
        """我发起的活动-创建一个合作招商活动"""
        self.merchant_login("DISTRIBUTION_LEADER_PROD", "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.sleep(5)
        self.click('//span[contains(text(),"发起活动")]')
        self.click("(//span[contains(text(),'选择该类型')])[3]")
        self.sleep(5)
        """ 填写信息 """
        current_timestamp = int(time.time() * 1000)
        self.input("input[placeholder='活动标题对达人可见，请谨慎填写']", f"快手分销测试合作招商活动{current_timestamp}")
        self.click('//input[@placeholder="开始日期"]')
        self.click("//span[contains(text(),'未来一周')]")
        self.sleep(2)
        self.click("//span[contains(text(),'完 成')]")
        self.sleep(2)
        self.click("//span[contains(text(),'保 存')]")
        self.assert_element(".kwaishop-cps-pc-micro-leader-card-body")

        """删除"""
        self.click("(//div[@id='rc-tabs-1-tab-3'])[1]")
        self.click("(//span[contains(text(),'关闭活动')])[1]")
        self.sleep(1)
        self.click("(//span[contains(text(),'确 定')])[1]")
        self.sleep(2)

    @pytest.mark.p1
    def test_official_hosting_exist(self):
        """官方托管tab存在"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        element = self.find_element("//div[@id='rc-tabs-0-tab-4']")
        self.assert_true('官方托管' in element.text)

    @pytest.mark.p1
    def test_official_hosting_selected(self):
        """官方托管tab可以被正常选中"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.assert_true("false" == self.get_attribute("//div[@id='rc-tabs-0-tab-4']", "aria-selected"))
        self.click("//div[@id='rc-tabs-0-tab-4']")
        self.assert_true("true" == self.get_attribute("//div[@id='rc-tabs-0-tab-4']", "aria-selected"))

    @pytest.mark.p1
    def test_official_hosting_info(self):
        """点击官方托管tab-点击已结束tab 官方托管活动信息正常展示"""
        self.merchant_login(domain, "huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("//div[@id='rc-tabs-0-tab-4']")
        self.sleep(1)
        self.click("(//span[contains(text(),'已结束（7）')])[1]")
        self.sleep(1)

        element = self.find_element("td:nth-child(1)")
        self.assert_true(element.text != "")

    @pytest.mark.p1
    def test_official_hosting_search(self):
        """点击官方托管tab-点击已结束tab 搜索活动信息"""
        self.merchant_login(domain, "huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("//div[@id='rc-tabs-0-tab-4']")
        self.sleep(0.3)
        self.click("(//span[contains(text(),'已结束（7）')])[1]")
        self.sleep(0.3)

        self.input("(//input[@placeholder='请输入活动ID或名称'])[1]", "7850901008")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.1)
        self.assert_text("已结束",
                         "(//span[@class='kwaishop-cps-pc-micro-leader-pro-field-status__text'][contains(text(),'已结束')])[1]")
        self.assert_text("官方托管", "(//span[contains(text(),'官方托管')])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_investment_normal_activity_search(self):
        """点击普通招商tab-点击已结束tab 搜索活动信息"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("(//span[contains(text(),'已结束')])[1]")
        self.sleep(0.2)

        self.input("(//input[@placeholder='请输入活动ID或名称'])[1]", "7917943434")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.1)
        self.assert_text("已结束",
                         "(//span[@class='kwaishop-cps-pc-micro-leader-pro-field-status__text'][contains(text(),'已结束')])[1]")
        self.assert_text("普通招商", "(//td[@class='kwaishop-cps-pc-micro-leader-table-cell'])[5]")

    @pytest.mark.p1
    def test_investment_exclusive_activity_search(self):
        """点击专属招商tab-点击已结束tab 搜索活动信息"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("(//div[@class='kwaishop-cps-pc-micro-leader-tabs-tab'])[1]")
        self.sleep(0.1)
        self.click("(//span[contains(text(),'已结束')])[1]")
        self.sleep(0.2)

        self.input("(//input[@placeholder='请输入活动ID或名称'])[1]", "7720703434")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.1)
        self.assert_text("查 询", "(//span[contains(text(),'查 询')])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_investment_cooperation_activity_search(self):
        """点击合作招商tab-点击已结束tab 搜索活动信息"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("(//div[@class='kwaishop-cps-pc-micro-leader-tabs-tab'])[2]")
        self.sleep(0.1)
        self.click("(//span[contains(text(),'已结束')])[1]")
        self.sleep(0.2)

        self.input("(//input[@placeholder='请输入活动ID或名称'])[1]", "7260310434")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.1)
        self.assert_text("已结束",
                         "(//span[@class='kwaishop-cps-pc-micro-leader-pro-field-status__text'][contains(text(),'已结束')])[1]")
        self.assert_text("合作招商", "(//td[@class='kwaishop-cps-pc-micro-leader-table-cell'])[5]")

    @pytest.mark.p1
    def test_investment_normal_activity_next_page(self):
        """点击普通招商tab-点击已结束tab-切换下一页"""
        self.merchant_login(domain, "wb_caijinwei")
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("(//span[contains(text(),'已结束')])[1]")
        self.click("(//a[normalize-space()='2'])[1]")
        self.find_element("(//span[contains(text(),'删除')])[1]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_create_unpublished_cooperative_investment_promotion_activities(self):
        """ 我发起的活动-创建一个未发布的合作招商 """
        self.merchant_login(domain, "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click('//span[contains(text(),"发起活动")]')
        self.click("(//span[contains(text(),'选择该类型')])[3]")
        """ 填写信息 """
        current_timestamp = int(time.time() * 1000)
        self.input("input[placeholder='活动标题对达人可见，请谨慎填写']",
                   f"快手分销测试合作招商活动{current_timestamp}")
        self.click('//input[@placeholder="开始日期"]')
        self.click('//span[contains(text(),"未来一周")]')
        time.sleep(3)
        self.click('//span[contains(text(),"保存草稿")]')
        self.click('//span[contains(text(),"保 存")]')
        time.sleep(3)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_delete_to_be_published_cooperative_investment_promotion_activities(self):
        """ 我发起的活动-删除待发布的合作招商活动 """
        self.merchant_login('DISTRIBUTION_LEADER', "wb_huoyangyang")
        self.click("//span[contains(text(), '我发起的活动')]")
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        self.click("//span[contains(text(),'未发布')]")
        self.click("(//span[contains(text(),'删除')])[1]")
        self.click("//span[contains(text(),'确 定')]")
        self.find_element("//span[contains(text(),'操作成功')]")
