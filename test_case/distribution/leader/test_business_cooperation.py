import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
商家合作
寄样管理
店铺托管
pytest test_case/distribution/leader/test_business_cooperation.py --headless -n=3
"""


class TestLeaderMerchant(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        time.sleep(2)

    @pytest.mark.p0
    def test_click_sample_management(self):
        """寄样管理-判断是否进入寄样管理页面"""
        self.click("//*[@id='menu-9IVVL-g53nM']/span/span/span/span")
        self.find_element("//span[contains(text(),'设置规则')]")
        self.find_element("//span[contains(text(),'查 询')]")
        self.find_element("//span[contains(text(),'重 置')]")

    @pytest.mark.p0
    def test_click_store_hosting(self):
        """店铺托管-判断是否进入店铺托管页面"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.find_element("//span[contains(text(),'复制商家报名链接')]")
        self.find_element("//span[contains(text(),'设置联系方式')]")
        self.click("(//div[contains(text(),'使用手册')])[1]")
        time.sleep(1)
        self.assert_text("团长侧-店铺托管使用手册", "(//div[@class='ArticleMCNAnnouncement_title__13c0n'])[1]")

    @pytest.mark.p0
    def test_click_store_hosting_phone(self):
        """店铺托管-判断店铺托管跳转至联系方式页面是否正常"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.click("(//span[contains(text(),'设置联系方式')])[1]")
        time.sleep(1)
        self.click("(//th[contains(text(),'联系人')])[1]")

    @pytest.mark.p0
    def test_click_managed_goods(self):
        """店铺托管-点击托管商品-判断是否进入该页面"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.click("(//div[@class='index-module__tab-item--ndFcw'])[1]")
        self.find_element("(//span[contains(text(),'设置联系方式')])[1]")

    @pytest.mark.p2
    @pytest.mark.skip
    def test_click_managed_goods_limit(self):
        """店铺托管-点击托管商品-判断分页是否正常"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.sleep(2)
        """ 点击 已结束Tab --> 终止合作 """
        self.click("//div[@id='rc-tabs-0-tab-3']")
        self.refresh()
        time.sleep(2)
        self.click("//div[@id='rc-tabs-0-tab-3']")
        self.click("//div[@class='index-module__filter-form-contain--tBacO']//label[1]")
        self.click("//span[contains(text(),'终止合作')]")
        """ 翻页 """
        self.scroll_down(3)
        self.assert_element("//a[normalize-space()='2']")
        self.click("//a[normalize-space()='2']")
        self.assert_element("//a[normalize-space()='3']")
        self.click("//a[normalize-space()='3']")

    @pytest.mark.p2
    def test_click_set_contact_method(self):
        """店铺托管 - 点击设置联系方式 - 判断跳转是否正常面"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(1)
        self.click("(//span[contains(text(),'设置联系方式')])[1]")
        time.sleep(2)
        self.assert_text("联系人","//th[contains(text(),'联系人')]")


    @pytest.mark.p2
    def test_click_user_book_jump(self):
        """店铺托管 - 点击使用手册 - 判断跳转是否正常"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(1)
        self.click("//div[contains(text(),'使用手册')]")
        time.sleep(1)
        self.assert_text("团长侧","//div[@class='ArticleMCNAnnouncement_title__13c0n']")

    @pytest.mark.p2
    def test_click_copy_seller_link(self):
        """店铺托管 - 点击复制商家报名链接 - 判断是否复制成功"""
        self.click("li[id='menu-y2CpGO6iKLw'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(1)
        self.click("//span[contains(text(),'复制商家报名链接')]")








