# import time
#
#
# from seleniumwire import webdriver
#
# # 进入浏览器设置
# options = webdriver.ChromeOptions()
# # 设置中文
# # options.add_argument('lang=zh_CN.UTF-8')
# # 更换头部
# # options.add_argument('--trace-context={"laneId":"PRT.ycdctest"}')
# # options.add_argument('--user-agent=iphone')
# browser = webdriver.Chrome(options=options)
# browser.header_overrides = {"trace-context": '{"laneId":"PRT.ycdctest"}'}
#
# url = "https://cps-kwaixiaodian.test.gifshow.com/pc/promoter/base/order-manage"
# browser.get(url)
#
# # i = 1
# # for request in browser.requests:
# #             print("请求头header信息：" + str(i))
# #             print(request.headers)
# #             i = i + 1
# time.sleep(10000)
#
# browser.quit()