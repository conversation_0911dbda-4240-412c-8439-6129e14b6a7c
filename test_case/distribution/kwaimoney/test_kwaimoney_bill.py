import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
账单管理
'''

@pytest.mark.p1
class TestKwaimoneyBill(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'huoyangyang')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'huoyangyang')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//span[contains(text(),'收益管理')])[1]")
        time.sleep(0.2)
        self.click("(//a[contains(text(),'账单管理')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_bill_page(self):
        self.baseLogin()
        element = self.find_element("p.src-Modu<PERSON>-<PERSON>waiMoney-Pages-Earnings-Bill-index-module__accountTip--3g7Xx")
        assert element.text == "打开 快手App 扫一扫，可查看推广佣金账单并提现"

