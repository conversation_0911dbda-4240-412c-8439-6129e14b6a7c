import time
import re
from selenium.webdriver.common.keys import Keys
from utils.convert_help import str2number
# from seleniumwire import webdriver


import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
快赚客推广专区-商品推广
'''

class TestKwaimoneyLivePromotion(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//span[contains(text(),'推广专区')])[1]")
        time.sleep(0.5)
        self.click("(//a[contains(text(),'商品推广')])[1]")
        time.sleep(2)


    def getNumberFormStr(self, str):
        numbers = re.findall(r'\d+', str)
        return int(numbers[0])

    def test_promotion_good_field(self):
        """
        商品推广页面元素判断
        """
        self.baseLogin()
        # 检验筛选项
        self.assert_text("类目","(//span[@class='src-Components-FilterSelection-index-module__filterSelectionTitle--tNAYp'])[1]")
        self.assert_text("计划","(//span[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-FilterList-PlanFilters-index-module__planFiltersTitle--2K0XB'])[1]")
        self.assert_text("高级","(//span[contains(text(),'高级')])[1]")
        self.assert_text("筛选","(//span[contains(text(),'筛选')])[1]")

        # 检验表格字段
        self.assert_text("综合排序", "(//span[contains(text(),'综合排序')])[1]")
        self.assert_text("佣金比例", "(//span[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-FilterTool-index-module__sort_name--1fDbA'][contains(text(),'佣金比例')])[1]")
        self.assert_text("商品价格", "(//span[contains(text(),'商品价格')])[1]")
        self.assert_text("商品销量", "(//span[contains(text(),'商品销量')])[1]")


    def test_promotion_good_item_info(self):
        """
        商品推广页面-商品推广信息判断
        """
        self.baseLogin()
        # print(self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_action--5NOBu"))
        self.assert_text("查看详情", "(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb'][contains(text(),'查看详情')])[1]")
        self.assert_text("立即推广", "(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button_color--30oiV'][contains(text(),'立即推广')])[1]")
        # 商品图片
        self.find_element("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_img--18Oov")
        # 商品名称
        itemName = self.get_text("p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true(itemName is not None and itemName != "")
        # 推广信息
        price = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_info--2yoHh div:nth-child(1) span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        commissionRate = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_info--2yoHh div:nth-child(2) span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        commissionPrice = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_info--2yoHh div:nth-child(3) span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        self.assert_true(price != "" and commissionRate != "" and commissionPrice != "")
        # 店铺信息
        shopName = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_head_title--KTY17")
        self.assert_true(shopName is not None and shopName != "")
        # 销量
        stockNum = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__detail_right--zqm-2")
        self.assert_true("总销量" in stockNum)

    def test_promotion_good_search_type_selected(self):
        """
        商品推广不同的筛选类别进行点击
        """
        self.baseLogin()
        # 类目
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'首页')])[1]", "class")))
        self.click("(//div[contains(text(),'首页')])[1]")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'首页')])[1]", "class")))

        # 计划
        self.assert_true(
            False == ("planItemSelectio" in self.get_attribute("(//div[contains(text(),'专属计划')])[1]", "class")))
        self.click("(//div[contains(text(),'专属计划')])[1]")
        time.sleep(0.5)
        self.assert_true(
            True == ("planItemSelectio" in self.get_attribute("(//div[contains(text(),'专属计划')])[1]", "class")))


    def test_promotion_good_search_by_category(self):
        """
        商品推广根据类目进行搜索
        """
        self.baseLogin()
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleOld = [str(element.text) for element in itemElements]

        self.click("(//div[contains(text(),'食品饮料')])[1]")
        time.sleep(2)

        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleNew = [str(element.text) for element in itemElements]
        # print(itemTitleNew)
        notDiff = (len(itemTitleOld) != len(itemTitleNew) and all(
            a == b for a, b in zip(itemTitleOld, itemTitleNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)


    def test_promotion_good_search_by_plan_type(self):
        """
        商品推广根据计划类型进行搜索
        """
        self.baseLogin()
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        itemTitleOld = [str(element.text) for element in itemElements]

        self.click("(//div[contains(text(),'普通招商')])[1]")
        time.sleep(2)

        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleNew = [str(element.text) for element in itemElements]
        # print(itemTitleNew)
        notDiff = (len(itemTitleOld) != len(itemTitleNew) and all(
            a == b for a, b in zip(itemTitleOld, itemTitleNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)


    def test_promotion_good_search_by_filter(self):
        """
        商品推广进行筛选
        价格1-9 佣金比例1-9
        """
        self.baseLogin()
        self.input("(//input[@type='text'])[2]", 1)
        self.input("(//input[@type='text'])[3]", 9)
        self.input("(//input[@type='text'])[4]", 1)
        self.input("(//input[@type='text'])[5]", 9)
        self.click("(//span[contains(text(),'确 定')])[1]")
        time.sleep(2)

        elements = self.find_elements("span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        allNum = [element.text for element in elements] # 价格 佣金率 佣金
        priceList = [allNum[i] for i in range(0, len(allNum), 3)]
        commissionRateList = [allNum[i+1] for i in range(0, len(allNum), 3)]

        all_price_in_range = all(1 <= float(x) <= 9 for x in priceList)
        all_commissionRate_in_range = all(1 <= float(x) <= 9 for x in commissionRateList)
        self.assert_true(all_price_in_range is True and all_commissionRate_in_range is True)

    @pytest.mark.p1
    def test_promotion_good_sort_by_commissionRate(self):
        """
        商品推广排序-佣金比例
        """
        self.baseLogin()
        self.click("(//span[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-FilterTool-index-module__sort_name--1fDbA'][contains(text(),'佣金比例')])[1]")
        time.sleep(1)
        # # 升序
        # elements = self.find_elements(
        #     "span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        # allNum = [element.text for element in elements]  # 价格 佣金率 佣金
        # commissionRateList = [allNum[i + 1] for i in range(0, len(allNum), 3)]
        # commissionRateList = list(map(float, commissionRateList))
        # self.assert_true(all(commissionRateList[i] <= commissionRateList[i + 1] for i in range(len(commissionRateList) - 1)) is True)

        # 降序
        self.click("(//span[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-FilterTool-index-module__sort_name--1fDbA'][contains(text(),'佣金比例')])[1]")
        time.sleep(1)
        elements = self.find_elements(
            "span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        allNum = [element.text for element in elements]  # 价格 佣金率 佣金
        commissionRateList = [allNum[i + 1] for i in range(0, len(allNum), 3)]
        commissionRateList = list(map(float, commissionRateList))
        self.assert_true(
            all(commissionRateList[i] >= commissionRateList[i + 1] for i in range(len(commissionRateList) - 1)) is True)

    def test_promotion_good_sort_by_sale(self):
        """
        商品推广排序-销量
        """
        self.baseLogin()
        self.click("(//span[contains(text(),'商品销量')])[1]")
        time.sleep(2)
        # 升序
        elements = self.find_elements("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__detail_right--zqm-2")
        sales = [self.getNumberFormStr(element.text) for element in elements]
        # print(sales)
        self.assert_true(all(sales[i] <= sales[i + 1] for i in range(len(sales) - 1)) is True)


    def test_promotion_good_sort_by_price(self):
        """
        商品推广排序-价格
        """
        # self.driver = webdriver.Chrome()
        # self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.sdtest"}'}

        self.baseLogin()
        self.click("(//span[contains(text(),'商品价格')])[1]")
        time.sleep(1)
        # 升序
        elements = self.find_elements(
            "span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        allNum = [element.text for element in elements]  # 价格 佣金率 佣金
        commissionRateList = [allNum[i] for i in range(0, len(allNum), 3)]
        commissionRateList = list(map(float, commissionRateList))
        self.assert_true(all(
            commissionRateList[i] <= commissionRateList[i + 1] for i in range(len(commissionRateList) - 1)) is True)

        # 降序
        self.click("(//span[contains(text(),'商品价格')])[1]")
        time.sleep(1)
        elements = self.find_elements(
            "span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        allNum = [str2number(element.text.replace(",", "")) for element in elements]  # 价格 佣金率 佣金
        commissionRateList = [allNum[i] for i in range(0, len(allNum), 3)]
        commissionRateList = list(map(float, commissionRateList))
        self.assert_true(all(
            commissionRateList[i] >= commissionRateList[i + 1] for i in range(len(commissionRateList) - 1)) is True)



    def test_promotion_good_list_next_page(self):
        """
        商品推广-翻页下一页
        """
        self.baseLogin()
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        itemTitleOld = [str(element.text) for element in itemElements]

        # 下一页
        self.click("(//button[@type='button'])[3]")
        time.sleep(1)
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" in self.get_attribute("(//li[@title='2'])[1]", "class"))
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleNew = [str(element.text) for element in itemElements]
        notDiff = (len(itemTitleOld) != len(itemTitleNew) and all(
            a == b for a, b in zip(itemTitleOld, itemTitleNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)

        # 上一页
        self.click("(//button[@type='button'])[2]")
        time.sleep(1)
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" not in self.get_attribute("(//li[@title='2'])[1]", "class"))
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleNewNew = [str(element.text) for element in itemElements]
        notDiff = (len(itemTitleNewNew) != len(itemTitleNew) and all(
            a == b for a, b in zip(itemTitleOld, itemTitleNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)

    def test_promotion_good_list_by_appoint_page(self):
        """
        商品推广-指定页
        """
        self.baseLogin()
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        itemTitleOld = [str(element.text) for element in itemElements]

        self.click("(//a[normalize-space()='3'])[1]")
        time.sleep(1)
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" in self.get_attribute("(//li[@title='3'])[1]", "class"))
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleNew = [str(element.text) for element in itemElements]
        notDiff = (len(itemTitleOld) != len(itemTitleNew) and all(
            a == b for a, b in zip(itemTitleOld, itemTitleNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)


    def test_promotion_good_list_by_jump_page(self):
        """
        商品推广-跳转到指定页
        """
        self.baseLogin()
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        itemTitleOld = [str(element.text) for element in itemElements]

        self.input("(//input[@type='text'])[6]", 4)
        input = self.find_element("(//input[@type='text'])[6]")
        input.send_keys(Keys.ENTER)
        time.sleep(3)
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" in self.get_attribute("(//li[@title='4'])[1]", "class"))
        itemElements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        itemTitleNew = [str(element.text) for element in itemElements]
        notDiff = (len(itemTitleOld) != len(itemTitleNew) and all(a == b for a, b in zip(itemTitleOld, itemTitleNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)

    # 目前存在过滤问题，无法放开
    @pytest.mark.skip
    def test_promotion_good_list_change_item_num(self):
        """
        商品推广-修改页面数量
        """
        self.baseLogin()
        # 默认为10
        self.assert_text("10 条/页", "(//span[@title='10 条/页'])[1]")
        elements = self.find_elements("p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true(len(elements) == 10)
        # 切换为20
        self.click("(//div[@class='ant-select-selector'])[1]")
        time.sleep(0.2)
        self.click("div.ant-select-item.ant-select-item-option:nth-child(2)")
        time.sleep(1)
        elements = self.find_elements(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true(len(elements) == 20)



    def test_promotion_good_search_by_name(self):
        """
        商品推广根据商品名称搜索
        """
        self.baseLogin()
        firstItemTitle = self.get_text(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")

        self.input("(//input[@placeholder='请输入商品名称或者ID搜索'])[1]", "测试")
        self.click("(//span[@aria-label='system-search-line'])[1]")
        time.sleep(1)

        firstItemTitleNew = self.get_text(
            "p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true(firstItemTitleNew != firstItemTitle)

    def test_promotion_good_detail(self):
        """
        查看商品推广详情,元素符合预期
        """
        self.baseLogin()
        self.click("(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb'][contains(text(),'查看详情')])[1]")
        time.sleep(2)
        self.find_element("//span[contains(text(),'价格')]")
        self.find_element("(//span[contains(text(),'佣金率')])[1]")
        self.find_element("(//span[contains(text(),'预估佣金')])[1]")
        self.find_element("(//span[contains(text(),'总销量')])[1]")
        self.find_element("(//span[contains(text(),'总库存')])[1]")

    def test_promotion_good_generate_url(self):
        """
        生成商品推广物料
        """
        self.baseLogin()
        self.click("(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button_color--30oiV'][contains(text(),'立即推广')])[1]")
        time.sleep(2)
        self.find_element("(//div[@role='alert'])[1]")
        self.assert_text("关联推广位", "(//div[contains(text(),'关联推广位')])[1]")
        self.assert_true("" != self.get_text("(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__text_cont--UQaPO'])[1]"))
        self.assert_text("图片素材", "(//div[contains(text(),'图片素材')])[1]")
        self.assert_text("下载图片", "(//span[contains(text(),'下载图片')])[1]")
        self.assert_text("分享素材", "(//div[contains(text(),'分享素材')])[1]")
        self.assert_true("" != self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))
        self.assert_text("一键复制", "(//span[contains(text(),'一键复制')])[1]")

    def test_promotion_live_generate_tip_close(self):
        """
        商品推广物料关闭提示
        """
        self.baseLogin()
        self.click("(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button_color--30oiV'][contains(text(),'立即推广')])[1]")
        time.sleep(1.5)
        self.assert_text(
            "可将商品链接分享至快手站外，用户点击后可唤起快手APP进入商品详情页，后续15天内用户在该店铺产生的交易会归因至该链接对应的推广位。",
            "(//div[@role='alert'])[1]")
        self.click("//button[@class='ant-alert-close-icon']//span[@aria-label='system-close-medium-line']//*[name()='svg']")
        self.assert_text("", "(//div[@role='alert'])[1]")

    def test_promotion_good_generate_url_copy(self):
        """
        生成商品推广物料-一键复制，弹窗正常出现
        """
        self.baseLogin()
        self.click(
            "(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button_color--30oiV'][contains(text(),'立即推广')])[1]")
        time.sleep(1.5)
        self.click("(//span[contains(text(),'一键复制')])[1]")
        time.sleep(0.1)
        self.assert_text("物料已复制到剪贴板！", "(//span[contains(text(),'物料已复制到剪贴板！')])[1]")


    def test_promotion_good_generate_url_switch(self):
        """
        生成商品推广物料-切换物料
        """
        self.baseLogin()
        self.click(
            "(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button_color--30oiV'][contains(text(),'立即推广')])[1]")
        time.sleep(1.5)
        self.click("(//span[contains(text(),'短链接')])[1]")
        time.sleep(1)
        self.assert_true("" != self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'快口令')])[1]")
        time.sleep(1)
        self.assert_true("" != self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'长链接')])[1]")
        time.sleep(1)
        self.assert_true("" == self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'海报')])[1]")
        time.sleep(1)
        self.find_element("(//canvas)[1]")
        self.assert_text("下载海报", "(//span[contains(text(),'下载海报')])[1]")
        self.assert_text("下载小程序码", "(//span[contains(text(),'下载小程序码')])[1]")

