import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
收款账号
'''

@pytest.mark.p1
class TestKwaimoneyAccountNumber(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'huoyangyang')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'huoyangyang')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//span[contains(text(),'收益管理')])[1]")
        time.sleep(0.2)
        self.click("(//a[contains(text(),'收款账号')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_account_number_page(self):
        self.baseLogin()
        self.find_element("(//p[contains(text(),'可查看当前绑定的收款账户')])[1]")

