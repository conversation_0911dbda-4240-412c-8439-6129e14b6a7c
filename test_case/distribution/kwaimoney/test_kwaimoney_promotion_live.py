import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
快赚客推广专区-直播推广
'''

class TestKwaimoneyLivePromotion(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//span[contains(text(),'推广专区')])[1]")
        time.sleep(0.5)
        self.click("(//a[contains(text(),'直播推广')])[1]")
        time.sleep(2)


    def test_promotion_live_field(self):
        """
        直播推广页面元素判断
        """
        self.baseLogin()
        # 检验筛选项
        self.assert_text("带货类目","(//span[@class='src-Components-FilterSelection-index-module__filterSelectionTitle--tNAYp'])[1]")
        self.assert_text("商品数量","(//span[contains(text(),'商品数量')])[1]")
        self.assert_text("平均佣金","(//span[contains(text(),'平均佣金')])[1]")
        self.assert_text("出让佣金","(//span[contains(text(),'出让佣金')])[1]")
        self.assert_text("出让货款","(//span[contains(text(),'出让货款')])[1]")
        self.assert_text("推广类型","(//span[@class='src-Components-FilterSingle-index-module__filterSelectionTitle--2s8xK'])[1]")

        # 检验表格字段
        self.assert_text("直播带货信息", "(//th[contains(text(),'直播带货信息')])[1]")
        self.assert_text("粉丝数量", "(//span[contains(text(),'粉丝数量')])[1]")
        self.assert_text("预计直播时间", "(//th[contains(text(),'预计直播时间')])[1]")
        self.assert_text("商品数量", "(//span[@class='ant-table-column-title'][contains(text(),'商品数量')])[1]")
        self.assert_text("平均佣金率", "(//span[contains(text(),'平均佣金率')])[1]")
        self.assert_text("出让佣金", "(//span[@class='ant-table-column-title'][contains(text(),'出让佣金')])[1]")
        self.assert_text("出让货款", "(//span[@class='ant-table-column-title'][contains(text(),'出让货款')])[1]")
        self.assert_text("跟单时间", "(//div[contains(text(),'跟单时间')])[1]")
        self.assert_text("操作", "(//th[contains(text(),'操作')])[1]")


    @pytest.mark.p1
    def test_promotion_live_search_type_selected(self):
        """
        直播推广不同的筛选类别进行点击
        """
        self.baseLogin()
        # 带货类目
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'首页')])[1]", "class")))
        self.click("(//div[contains(text(),'首页')])[1]")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'首页')])[1]", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) >= 0
        self.click("(//div[contains(@class,'src-Components-FilterSelection-index-module__filterItem--29QFB')][contains(text(),'全部')])[1]")

        # 商品数量
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'10以下')])[1]", "class")))
        self.click("(//div[contains(text(),'10以下')])[1]")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'10以下')])[1]", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) > 0
        self.click("(//div[@class='src-Components-FilterRange-index-module__filterItem--3UrPc '][contains(text(),'全部')])[1]")

        # 平均佣金
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(3) div:nth-child(2) div:nth-child(2)", "class")))
        self.click("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(3) div:nth-child(2) div:nth-child(2)")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(3) div:nth-child(2) div:nth-child(2)", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) > 0
        self.click("(//div[contains(@class,'src-Components-FilterRange-index-module__filterItem--3UrPc')][contains(text(),'全部')])[2]")

        # 出让佣金
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(4) div:nth-child(2) div:nth-child(2)", "class")))
        self.click("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(4) div:nth-child(2) div:nth-child(2)")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(4) div:nth-child(2) div:nth-child(2)", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) == 0
        self.click("(//div[contains(@class,'src-Components-FilterRange-index-module__filterItem--3UrPc')][contains(text(),'全部')])[3]")

        # 出让贷款
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(5) div:nth-child(2) div:nth-child(2)", "class")))
        self.click("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(5) div:nth-child(2) div:nth-child(2)")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(5) div:nth-child(2) div:nth-child(2)", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) > 0
        self.click("(//div[contains(@class,'src-Components-FilterRange-index-module__filterItem--3UrPc')][contains(text(),'全部')])[4]")

    def test_promotion_live_search_by_type(self):
        """
        直播推广根据推广类型进行搜索
        """
        self.baseLogin()
        text1 = self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-Live-DataList-DataTable-index-module__live_title_text--3wcMZ'])[1]")
        assert len(text1) > 0

        self.click("(//div[contains(text(),'全直播间推广')])[1]")
        time.sleep(0.2)
        self.assert_text("暂无数据", "(//div[@class='ant-empty-description'])[1]")

        self.click("(//div[contains(text(),'分销商品推广')])[1]")
        time.sleep(0.2)
        text2 = self.get_text(
            "(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-Live-DataList-DataTable-index-module__live_title_text--3wcMZ'])[1]")
        assert len(text2) > 0

    def test_promotion_live_search_switch_keyword(self):
        """
        直播推广切换搜索类型，点击能够出现下拉框
        """
        self.baseLogin()
        self.assert_text("搜快手号/ID", "(//span[@title='搜快手号/ID'])[1]")
        self.click("(//span[@title='搜快手号/ID'])[1]")
        time.sleep(0.2)
        self.assert_true("false" == self.get_attribute("div.rc-virtual-list-holder-inner > div:nth-child(1)", "aria-selected"))
        self.assert_true("true" == self.get_attribute("div.rc-virtual-list-holder-inner > div:nth-child(2)", "aria-selected"))

        self.click("div.rc-virtual-list-holder-inner > div:nth-child(1)")
        time.sleep(0.2)
        self.assert_true("true" == self.get_attribute("div.rc-virtual-list-holder-inner > div:nth-child(1)", "aria-selected"))
        self.assert_true("false" == self.get_attribute("div.rc-virtual-list-holder-inner > div:nth-child(2)", "aria-selected"))



    def test_promotion_live_search_by_promoterId(self):
        """
        直播推广根据达人Id搜索 1275802949
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.assert_true("true" == self.get_attribute("input.ant-input", "aria-expanded"))
        # print(self.driver.page_source)
        self.assert_true("快手号:1275802949" in self.get_text("div.ant-select-item-option-content"))
        self.click("span.ant-input-suffix")
        time.sleep(0.5)
        self.assert_true("" == self.get_attribute("input.ant-input", "value"))
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.assert_text("长期有效自动化", "(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-Live-DataList-DataTable-index-module__live_title_text--3wcMZ'])[1]")


    def test_promotion_live_detail(self):
        """
        查看直播推广详情,元素符合预期
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.click("//span[contains(text(),'查看详情')]")
        time.sleep(1)
        self.assert_text("类目分布", "(//span[contains(text(),'类目分布')])[1]")
        self.assert_text("品牌分布", "(//span[contains(text(),'品牌分布')])[1]")
        self.assert_text("商品类型", "(//span[contains(text(),'商品类型')])[1]")
        self.assert_text("商品(共1个商品)", "(//th[@class='ant-table-cell'])[1]")
        self.assert_text("商品价格", "(//span[contains(text(),'商品价格')])[1]")
        self.assert_text("佣金率", "(//span[contains(text(),'佣金率')])[1]")
        self.assert_text("ID:3590238893196", "(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-LiveDetail-index-module__itemId--lIHhp'])[1]")

    def test_promotion_live_detail_search_by_type(self):
        """
        查看直播推广详情,根据商品类型搜索
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.click("//span[contains(text(),'查看详情')]")
        time.sleep(1)
        self.click("(//div[contains(text(),'分销商品')])[1]")
        time.sleep(0.5)
        self.assert_text("ID:3590238893196", "(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-LiveDetail-index-module__itemId--lIHhp'])[1]")
        self.click("(//div[contains(text(),'非分销商品')])[1]")
        time.sleep(0.5)
        self.assert_text("暂无数据", "(//div[@class='ant-empty-description'])[1]")


    def test_promotion_live_generate_url(self):
        """
        生成直播推广物料
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.click("//span[contains(text(),'立即推广')]")
        time.sleep(1.5)
        self.assert_text("可将直播间链接分享至快手站外，用户点击后可唤起快手APP进入主播直播间，若未开播则会进入主播的个人页，后续产生的交易会归因至该链接对应的推广位。", "(//div[@class='ant-alert-message'])[1]")
        self.assert_text("关联推广位", "(//div[contains(text(),'关联推广位')])[1]")
        self.assert_true("" != self.get_text("(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__text_cont--UQaPO'])[1]"))
        self.assert_text("图片素材", "(//div[contains(text(),'图片素材')])[1]")
        self.assert_text("下载图片", "(//span[contains(text(),'下载图片')])[1]")
        self.assert_text("分享素材", "(//div[contains(text(),'分享素材')])[1]")
        self.assert_true("" != self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))
        self.assert_text("一键复制", "(//span[contains(text(),'一键复制')])[1]")

    def test_promotion_live_generate_tip_close(self):
        """
        商品推广物料关闭提示
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.click("//span[contains(text(),'立即推广')]")
        time.sleep(1.5)
        self.assert_text(
            "可将直播间链接分享至快手站外，用户点击后可唤起快手APP进入主播直播间，若未开播则会进入主播的个人页，后续产生的交易会归因至该链接对应的推广位。",
            "(//div[@class='ant-alert-message'])[1]")
        self.click("//button[@class='ant-alert-close-icon']//span[@aria-label='system-close-medium-line']//*[name()='svg']")
        self.assert_text("", "(//div[@class='ant-alert-message'])[1]")

    def test_promotion_live_generate_url_copy(self):
        """
        生成直播推广物料-一键复制，弹窗正常出现
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.click("//span[contains(text(),'立即推广')]")
        time.sleep(1.5)
        self.click("(//span[contains(text(),'一键复制')])[1]")
        time.sleep(0.1)
        self.assert_text("物料已复制到剪贴板！", "(//span[contains(text(),'物料已复制到剪贴板！')])[1]")


    def test_promotion_live_generate_url_switch(self):
        """
        生成直播推广物料-切换物料
        """
        self.baseLogin()
        self.input("input.ant-input", "1275802949")
        time.sleep(0.5)
        self.click("div.ant-select-item-option-content")
        time.sleep(0.2)
        self.click("//span[contains(text(),'立即推广')]")
        time.sleep(1.5)
        self.click("(//span[contains(text(),'短链接')])[1]")
        time.sleep(0.3)
        self.assert_true("" != self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'快口令')])[1]")
        time.sleep(1)
        self.assert_true("" != self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'预热作品链接')])[1]")
        time.sleep(1)
        self.assert_true("" == self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'长链接')])[1]")
        time.sleep(1)
        self.assert_true("" == self.get_text("(//p[@class='src-Modules-KwaiMoney-Pages-Promotion-PromotionDrawer-index-module__material_cont--3BFf7'])[1]"))

        self.click("(//span[contains(text(),'海报')])[1]")
        time.sleep(1)
        self.find_element("(//canvas)[1]")
        self.assert_text("下载海报", "(//span[contains(text(),'下载海报')])[1]")
        self.assert_text("下载小程序码", "(//span[contains(text(),'下载小程序码')])[1]")