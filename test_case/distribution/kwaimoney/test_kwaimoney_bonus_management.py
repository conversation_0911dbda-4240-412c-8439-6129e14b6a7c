import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
from seleniumwire import webdriver

"""
奖金管理
"""


class TestKwaimoneyBonusManagement(DistributionToolTest):

    def baseLogin(self):
        self.driver.quit()
        self.driver = webdriver.Chrome()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}

        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.2)
        self.click("(//span[contains(text(),'收益管理')])[1]")
        time.sleep(0.2)
        self.click("(//a[contains(text(),'奖金管理')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_click_bonus_management(self):
        """奖金管理:判断是否进入该页面"""
        self.baseLogin()
        # 进入账单管理tab
        self.assert_text("奖金管理奖励规则请参考《奖励金提现大全》", "(//h3[contains(text(),'奖金管理')])[1]")
        self.find_element("(//div[contains(text(),'待处理金额（元）')])[1]")
        self.find_element("(//div[@class='fund-transfer-pc-statistic-content'])[1]")
        self.find_element("(//div[contains(text(),'审核处理中金额（元）')])[1]")
        self.find_element("(//div[@class='fund-transfer-pc-statistic-content'])[2]")
        self.find_element("(//div[contains(text(),'累计发放奖励金额（元）')])[1]")
        self.find_element("(//div[@class='fund-transfer-pc-statistic-content'])[3]")

    # 不知道为啥ui自动化自动展开了
    @pytest.mark.skip
    @pytest.mark.p1
    def test_kwaimoney_bonus_rule_tip(self):
        """奖金管理:判断奖金管理规则tip能否正常操作"""
        self.baseLogin()
        # 奖金管理规则
        self.find_element("//div[contains(text(),'1.部分奖金单需要在申请提现时，上传您为快手开具的发票，提现审核通过后，奖金会直接打款到您绑定的提现账户中。请在申请提现前确保绑定账户正确无误。')]")
        self.click("(//span[contains(text(),'展开查看更多↓')])[1]")
        assert "小店客服" in self.get_text("div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-closable")
        self.click("(//span[contains(text(),'收起↑')])[1]")
        assert "小店客服" not in self.get_text("div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-closable")
        self.click("(//*[name()='svg'])[3]")
        assert "false" == self.get_attribute("div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-closable", "data-show")


    # 不知道为啥ui自动化自动展开了
    @pytest.mark.skip
    @pytest.mark.p1
    def test_kwaimoney_withdrawal_rule_tip(self):
        """奖金管理:合并提现规则tip能否正常操作"""
        self.baseLogin()
        # assert "true" == self.get_attribute(
        #     "div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-with-description", "data-show")
        self.find_element(
            "(//div[contains(text(),'1、活动类型要求：支持合并提现的活动类型有现金奖励活动/TR后返活动，快币等其他奖励活动暂不支持。')])[1]")
        self.click("//div[@class='alertBox___j3fBr']//span[contains(text(),'展开查看更多↓')]")
        print(self.get_text("div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-with-description"))
        assert "资金模式需要是“免开票-免审核”or“需开票”才可以合并提现" in self.get_text(
            "div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-with-description")
        self.click("(//span[contains(text(),'收起↑')])[1]")
        assert "资金模式需要是“免开票-免审核”or“需开票”才可以合并提现" not in self.get_text(
            "div.fund-transfer-pc-alert.fund-transfer-pc-alert-info.fund-transfer-pc-alert-with-description")

    @pytest.mark.p1
    def test_kwaimoney_bonus_tab_change(self):
        """ 奖金管理-待提现/重试&发放记录&合并提现记录tab切换 """
        self.baseLogin()
        # 待提现/重试
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))
        # 发放记录
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))
        # 合并提现记录
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))

    @pytest.mark.p1
    def test_kwaimoney_bonus_pending_withdrawal(self):
        """奖金管理:待提现/重试tab下元素显示正常"""
        self.baseLogin()
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        time.sleep(2)
        self.find_element("(//label[contains(text(),'活动类型')])[1]")
        self.find_element("//label[contains(text(),'角色')]")
        self.find_element("(//label[contains(text(),'活动名称')])[1]")
        self.find_element("(//label[contains(text(),'奖金单状态')])[1]")

        # 数据内容
        self.find_element("(//th[contains(text(),'创建时间')])[1]")
        self.find_element("(//th[contains(text(),'活动信息')])[1]")
        self.find_element("(//th[contains(text(),'奖金（元）')])[1]")
        elements = self.find_elements("tbody.fund-transfer-pc-table-tbody tr.fund-transfer-pc-table-row.fund-transfer-pc-table-row-level-0 td.fund-transfer-pc-table-cell")
        assert len(elements) > 0
        assert "TR后返活动" == elements[4].text

        self.find_element("(//button[@class='fund-transfer-pc-btn fund-transfer-pc-btn-link'])[1]")
        self.find_element("(//li[@class='fund-transfer-pc-pagination-total-text'])[1]")

        # 第一页已选中
        assert "fund-transfer-pc-pagination-item-active" in self.get_attribute("(//li[@title='1'])[1]", "class")


    @pytest.mark.p1
    def test_bonus_pending_withdrawal_by_type(self):
        """奖金管理-待提现/重试-活动类型"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        elements = self.find_elements("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option")
        for expectedStr, element in zip(
                ["现金奖励", "TR后返活动", "快币奖励", "磁力金牛", "钱包奖励"],
                elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='现金奖励']")
        self.assert_true("true" == self.get_attribute("//div[@title='现金奖励']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='TR后返活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='TR后返活动']", "aria-selected"))
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.find_element("(//li[@class='fund-transfer-pc-pagination-total-text'])[1]")

        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='快币奖励']")
        self.assert_true("true" == self.get_attribute("//div[@title='快币奖励']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='磁力金牛']")
        self.assert_true("true" == self.get_attribute("//div[@title='磁力金牛']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='钱包奖励']")
        self.assert_true("true" == self.get_attribute("//div[@title='钱包奖励']", "aria-selected"))
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.assert_text("您暂无待提现的奖金单", "(//div[@class='fund-transfer-pc-empty-description'])[1]")

    @pytest.mark.p1
    def test_bonus_pending_withdrawal_by_role(self):
        """奖金管理-待提现/重试-角色"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        elements = self.find_elements("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option")
        for expectedStr, element in zip(
                ["商户", "达人", "团长", "快赚客", "消费者"],
                elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='商户']")
        self.assert_true("true" == self.get_attribute("//div[@title='商户']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='达人']")
        self.assert_true("true" == self.get_attribute("//div[@title='达人']", "aria-selected"))
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.find_element("(//li[@class='fund-transfer-pc-pagination-total-text'])[1]")

        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='团长']")
        self.assert_true("true" == self.get_attribute("//div[@title='团长']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='快赚客']")
        self.assert_true("true" == self.get_attribute("//div[@title='快赚客']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='消费者']")
        self.assert_true("true" == self.get_attribute("//div[@title='消费者']", "aria-selected"))
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.assert_text("您暂无待提现的奖金单", "(//div[@class='fund-transfer-pc-empty-description'])[1]")

    @pytest.mark.p1
    def test_bonus_pending_withdrawal_by_status(self):
        """奖金管理-待提现/重试-状态"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        elements = self.find_elements("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option")
        for expectedStr, element in zip(
                ["待商户处理", "提取失败"],
                elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[4]")
        self.click("//div[@title='待商户处理']")
        self.assert_true("true" == self.get_attribute("//div[@title='待商户处理']", "aria-selected"))
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.assert_text("共计 1 条", "(//li[@class='fund-transfer-pc-pagination-total-text'])[1]")

        self.click("(//div[@class='fund-transfer-pc-select-selector'])[4]")
        self.click("//div[@title='提取失败']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取失败']", "aria-selected"))
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.assert_text("共计 1 条", "(//li[@class='fund-transfer-pc-pagination-total-text'])[1]")

    @pytest.mark.p1
    def test_kwaimoney_bonus_switch_order_num(self):
        """奖金管理:待提现/重试tab下页面元素条数切换"""
        self.baseLogin()

        # 切换为20
        self.click("(//span[@title='10 条/页'])[1]")
        time.sleep(0.1)
        self.assert_true(
            self.get_attribute("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(2)", "aria-selected") == "false")
        self.click("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(2)")
        self.assert_true(
            self.get_attribute("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(2)",
                               "aria-selected") == "true")
        self.assert_true("20 条/页", "(//span[@title='20 条/页'])[1]")
        time.sleep(0.1)

        # # 切换为30
        # self.click("(//span[@title='20 条/页'])[1]")
        # time.sleep(0.1)
        # self.assert_true(
        #     self.get_attribute("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(3)",
        #                        "aria-selected") == "false")
        # self.click("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(3)")
        # self.assert_true(
        #     self.get_attribute("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(3)",
        #                        "aria-selected") == "true")
        # time.sleep(0.1)
        #
        # # 切换为50
        # self.click("(//span[@title='30 条/页'])[1]")
        # time.sleep(0.1)
        # self.assert_true(
        #     self.get_attribute("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(4)",
        #                        "aria-selected") == "false")
        # self.click("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(4)")
        # self.assert_true(
        #     self.get_attribute("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option:nth-child(4)",
        #                        "aria-selected") == "true")


    @pytest.mark.p1
    def test_bonus_merge_transfer(self):
        """奖金管理-待提现/重试-合并提现弹窗"""
        self.baseLogin()

        self.click("(//span[contains(text(),'申请提现')])[1]")
        time.sleep(0.5)
        self.click("(//span[contains(text(),'申请提现')])[1]")
        # self.click("div.a45f0vYKcq4jEaTXlv9M button.fund-transfer-pc-btn.fund-transfer-pc-btn-primary")
        self.assert_text("如对奖励金额确认无误， 请点击确认后提现", "(//div[@class='fund-transfer-pc-popover-message-title'])[1]")

    @pytest.mark.p1
    def test_bonus_record_by_status(self):
        """奖金管理-发放记录-奖金单状态"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(0.5)
        elements = self.find_elements("div.fund-transfer-pc-select-item-option-content")
        for expectedStr, element in zip(["全部", "审核中", "提取中", "提取失败", "提取成功"], elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='全部']")
        self.assert_true("true" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='审核中']")
        self.assert_true("true" == self.get_attribute("//div[@title='审核中']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='提取中']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取中']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='提取失败']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取失败']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[1]")
        self.click("//div[@title='提取成功']")
        self.assert_true("true" == self.get_attribute("//div[@title='提取成功']", "aria-selected"))

    @pytest.mark.p1
    def test_bonus_record_by_type(self):
        """奖金管理-发放记录-活动类型"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        elements = self.find_elements("div.fund-transfer-pc-select-item.fund-transfer-pc-select-item-option")
        for expectedStr, element in zip(["全部", "现金奖励活动", "TR后返活动", "快币奖励活动", "粉条奖励活动", "金牛奖励活动", "钱包奖励活动"], elements):
            self.assert_true(expectedStr == element.text)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='全部']")
        self.assert_true("true" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='现金奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='现金奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='TR后返活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='TR后返活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='快币奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='快币奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='粉条奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='粉条奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='金牛奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='金牛奖励活动']", "aria-selected"))
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[@title='钱包奖励活动']")
        self.assert_true("true" == self.get_attribute("//div[@title='钱包奖励活动']", "aria-selected"))

    @pytest.mark.p1
    def test_bonus_record_search(self):
        """奖金管理-发放记录-全部搜索"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.sleep(0.3)
        self.find_element("//tr[@class='fund-transfer-pc-table-row fund-transfer-pc-table-row-level-0']//td[4]")
        self.find_element("//tr[@class='fund-transfer-pc-table-row fund-transfer-pc-table-row-level-0']//td[7]")
        self.find_element("//tr[@class='fund-transfer-pc-table-row fund-transfer-pc-table-row-level-0']//td[9]")

    @pytest.mark.p2
    def test_bonus_fund_transfer_record(self):
        """奖金管理-合并提现记录"""
        self.baseLogin()

        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.sleep(0.3)
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-3'])[1]", "aria-selected"))
        self.assert_text("发起时间", "(//th[contains(text(),'发起时间')])[1]")
        self.assert_text("合并打款单号", "(//th[contains(text(),'合并打款单号')])[1]")
        self.assert_text("提现状态", "(//th[contains(text(),'提现状态')])[1]")
        self.assert_text("合并奖金信息", "(//th[contains(text(),'合并奖金信息')])[1]")
        self.assert_text("收款信息", "(//th[contains(text(),'收款信息')])[1]")
        self.assert_text("操作", "(//th[contains(text(),'操作')])[1]")



