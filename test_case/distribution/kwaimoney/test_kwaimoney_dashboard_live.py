import time

import pytest
from datetime import datetime, timedelta
from utils.convert_help import str2number

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys


from test_case.distribution.distribution_base_case import DistributionToolTest

'''
数据参谋-直播推广
'''

@pytest.mark.p1
class TestKwaimoneyDashboardLive(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.2)
        self.click("(//span[contains(text(),'数据参谋')])[1]")
        time.sleep(0.2)
        self.click("(//a[contains(text(),'直播推广')])[1]")

    def get_yesterday(self):
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        return yesterday.strftime('%Y-%m-%d')

    def get_day_before(self, day):
        today = datetime.now()
        yesterday = today - timedelta(days=day)
        return yesterday.strftime('%Y-%m-%d')


    def select_time(self):
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.select_time_for_kwaimoney_v2(2024, 11, 10, 0)
        # self.click("(//span[contains(text(),'确 定')])[1]")

        self.click("(//input[@placeholder='结束日期'])[1]")
        self.select_time_for_kwaimoney_v2(2024, 12, 5, 1)
        # self.click("(//span[contains(text(),'确 定')])[1]")


    def asset_data(self):
        """
        断言部分通用的数据
        """
        # 访问量
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(1)")
        assert str2number(text) > 0

        # 独立访客
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(2)")
        assert str2number(text) > 0

        # 支付订单数
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(3)")
        assert str2number(text) > 0

        # 支付总金额
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(4)")
        assert str2number(text) > 0

        # 预估佣金
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(5)")
        assert str2number(text) > 0

    @pytest.mark.p0
    def test_kwaimoney_into_dashboard_live(self):
        """
        快赚客数据参谋-直播推广：页面元素正常展示
        """
        self.baseLogin()
        yesterday, beforeYesterday = self.get_day_before(1), self.get_day_before(2)

        self.assert_text("直播推广数据", "(//p[@class='src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-QueryForm-index-module__query_title--2WXrb'])[1]")
        self.find_element("(//label[contains(text(),'商品名称')])[1]")
        self.find_element("(//label[normalize-space()='PID'])[1]")
        self.find_element("(//label[contains(text(),'达人账号')])[1]")
        self.find_element("(//button[@class='ant-btn'])[1]")
        self.find_element("(//button[@type='submit'])[1]")

        self.find_element("(//span[contains(text(),'推广数据')])[1]")
        text = self.get_text("(//span[@class='src-Modules-KwaiMoney-Pages-Dashboard-UpdateTime-index-module__title_tip--hU5u5'][contains(text(),'数据更新于:')])[1]")
        assert yesterday in text or beforeYesterday in text
        self.find_element("(//div[contains(text(),'访问量（PV）')])[1]")
        self.find_element("(//div[contains(text(),'独立访客（UV）')])[1]")
        self.find_element("(//div[contains(text(),'支付订单数')])[1]")
        self.find_element("(//div[contains(text(),'支付总金额（元）')])[1]")
        self.find_element("(//div[contains(text(),'预估佣金（元）')])[1]")
        self.assert_text("数据趋势", "(//p[@class='src-Modules-KwaiMoney-Pages-Dashboard-ChartLine-index-module__chart_title--269Ys'])[1]")

        self.find_element("(//span[contains(text(),'推广明细')])[1]")
        self.find_element("(//span[contains(text(),'支付总金额')])[1]")
        self.find_element("(//th[contains(text(),'实际结算金额')])[1]")
        self.find_element("(//span[contains(text(),'佣金比例')])[1]")
        self.find_element("(//span[contains(text(),'预估佣金')])[1]")


    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data(self):
        """
        快赚客数据参谋-直播推广：推广数据正常展示
        """
        self.baseLogin()
        self.select_time()

        # 访问量
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(1)",
                   "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(1)")
        assert str2number(text) > 0

        # 独立访客
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" not in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(2)",
                   "class")
        self.click("div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(2)")
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(2)",
                   "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(2)")
        assert str2number(text) > 0

        # 支付订单数
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" not in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(3)",
                   "class")
        self.click("div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(3)")
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(3)",
                   "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(3)")
        assert str2number(text) > 0

        # 支付总金额
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" not in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(4)",
                   "class")
        self.click("div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(4)")
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(4)",
                   "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(4)")
        assert str2number(text) > 0

        # 预估佣金
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" not in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(5)",
                   "class")
        self.click("div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(5)")
        assert "src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item_active" in \
               self.get_attribute(
                   "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(5)",
                   "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(5)")
        assert str2number(text) > 0


    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_search_by_date(self):
        """
        快赚客数据参谋-直播推广：根据日期进行筛选
        并对访问量进行对比
        """
        pvList = []
        self.baseLogin()

        # 访问量
        time.sleep(1)
        assert "checked" in self.get_attribute("label.ant-radio-button-wrapper:nth-child(1)", "class")
        text = self.get_text( "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(1)")
        pvList.append(str2number(text))  # 可能会添加None

        assert "checked" not in self.get_attribute("label.ant-radio-button-wrapper:nth-child(2)", "class")
        self.click("(//span[contains(text(),'近30天')])[1]")
        time.sleep(0.5)
        assert "checked" in self.get_attribute("label.ant-radio-button-wrapper:nth-child(2)", "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(1)")
        pvList.append(str2number(text))

        assert "checked" not in self.get_attribute("label.ant-radio-button-wrapper:nth-child(3)", "class")
        self.click("(//span[contains(text(),'近90天')])[1]")
        time.sleep(0.5)
        assert "checked" in self.get_attribute("label.ant-radio-button-wrapper:nth-child(3)", "class")
        text = self.get_text(
            "div.src-Modules-KwaiMoney-Pages-Dashboard-ChartTabs-index-module__tab_item--PjYhK:nth-child(1)")
        pvList.append(str2number(text))

        print(pvList)
        assert all(pvList[i] <= pvList[i+1] for i in range(len(pvList)-1)) is True


    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_search_by_itemName(self):
        """
        快赚客数据参谋-直播推广：根据商品名称进行筛选
        """
        itemName = "快赚客+平台佣金托管专用商品-勿动！！！"
        cssPath = "div.ant-col.ant-col-8:nth-child(1) input.ant-input.ant-select-selection-search-input"
        self.baseLogin()

        self.assert_attribute_not_present(cssPath, "aria-expanded")
        # elements = self.find_elements(f"div[role='listbox'][id='{input_id}_list'] div")
        elements = self.find_elements("div.rc-virtual-list-holder-inner div.ant-select-item-option-content")
        assert len(elements) == 0

        # 输入商品名称
        self.input(cssPath, itemName)
        assert "true" == self.get_attribute(cssPath, "aria-expanded")
        time.sleep(1)
        elements = self.find_elements("div.rc-virtual-list-holder-inner div.ant-select-item-option-content")
        assert len(elements) > 0  # 下拉框出现
        for element in elements:
            assert element.text != ''

        # 点击第一个查询
        self.click("div.rc-virtual-list-holder-inner div.ant-select-item-option-content:nth-child(1)")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.5)
        self.select_time()
        time.sleep(1)

        self.asset_data()

        # 比较商品名称
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            actualItemName = order.find_element(By.CSS_SELECTOR, "td.ant-table-cell:nth-child(3) p.src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-DataTable-index-module__info_data_title--1rQlH")
            assert actualItemName.text == itemName

    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_search_by_PID(self):
        """
        快赚客数据参谋-直播推广：根据PID进行筛选
        """
        cpsPid = "ks_2995700434_102_1OOQOrvGP-A"
        cssPath = "div.ant-col.ant-col-8:nth-child(2) input.ant-input.ant-select-selection-search-input"
        self.baseLogin()

        self.assert_attribute_not_present(cssPath, "aria-expanded")
        # elements = self.find_elements(f"div[role='listbox'][id='{input_id}_list'] div")
        elements = self.find_elements("div.rc-virtual-list-holder-inner div.ant-select-item-option-content")
        assert len(elements) == 0

        # 输入cpsPid
        self.input(cssPath, cpsPid)
        assert "true" == self.get_attribute(cssPath, "aria-expanded")
        time.sleep(1)
        elements = self.find_elements("div.rc-virtual-list-holder-inner div.ant-select-item-option-content")
        assert len(elements) > 0  # 下拉框出现
        for element in elements:
            assert element.text != ''

        # 点击第一个查询
        self.click("div.rc-virtual-list-holder-inner div.ant-select-item-option-content:nth-child(1)")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.5)
        self.select_time()
        time.sleep(1)

        self.asset_data()

        # 比较cpsPid
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            actualCpsPid = order.find_element(By.CSS_SELECTOR, "td.ant-table-cell:nth-child(2)")
            assert actualCpsPid.text == cpsPid

    @pytest.mark.p2
    def test_kwaimoney_live_promotion_data_search_by_account(self):
        """
        快赚客数据参谋-直播推广：根据达人账号进行筛选
        暂时无法搜索
        """
        promoterId = "**********"
        cssPath = "div.ant-col.ant-col-8:nth-child(3) input.ant-input.ant-select-selection-search-input"

        self.baseLogin()

        self.assert_attribute_not_present(cssPath, "aria-expanded")
        # elements = self.find_elements(f"div[role='listbox'][id='{input_id}_list'] div")
        elements = self.find_elements("div.rc-virtual-list-holder-inner div.ant-select-item-option-content")
        assert len(elements) == 0

        # 输入cpsPid
        self.input(cssPath, promoterId)
        assert "true" == self.get_attribute(cssPath, "aria-expanded")
        time.sleep(1)
        elements = self.find_elements("div.rc-virtual-list-holder-inner div.ant-select-item-option-content")
        assert len(elements) >= 0  # 下拉框出现

        # 点击第一个查询
        self.click("//p[@class='src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-QueryForm-index-module__query_title--2WXrb']") # 锚定下其他位置，取消弹窗
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(0.5)
        self.select_time()
        time.sleep(1)

        self.asset_data()

        # 比较数据
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            tds = order.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            for td in tds:
                assert td.text is not None and td.text != ''

    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_search_reset(self):
        """
        快赚客数据参谋-直播推广：重置筛选项
        """
        self.baseLogin()

        itemName = "快赚客+平台佣金托管专用商品-勿动！！！"
        itemName_cssPath = "div.ant-col.ant-col-8:nth-child(1) input.ant-input.ant-select-selection-search-input"
        cpsPid = "ks_2995700434_102_1OOQOrvGP-A"
        cpsPid_cssPath = "div.ant-col.ant-col-8:nth-child(2) input.ant-input.ant-select-selection-search-input"
        promoterId = "**********"
        promoterId_cssPath = "div.ant-col.ant-col-8:nth-child(3) input.ant-input.ant-select-selection-search-input"

        self.assert_text("", itemName_cssPath)
        self.assert_text("", cpsPid_cssPath)
        self.assert_text("", promoterId_cssPath)

        self.input(itemName_cssPath, itemName)
        self.input(cpsPid_cssPath, cpsPid)
        self.input(promoterId_cssPath, promoterId)

        self.assert_text(itemName, itemName_cssPath)
        self.assert_text(cpsPid, cpsPid_cssPath)
        self.assert_text(promoterId, promoterId_cssPath)

        # 重置
        self.click("(//button[@class='ant-btn'])[1]")
        self.assert_text("", itemName_cssPath)
        self.assert_text("", cpsPid_cssPath)
        self.assert_text("", promoterId_cssPath)

    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_detail(self):
        """
        快赚客数据参谋-直播推广：推广明细数据
        """
        self.baseLogin()
        self.select_time()

        yesterday, beforeYesterday = self.get_day_before(1), self.get_day_before(2)
        dataDate = self.get_text("(//div[@class='src-Modules-KwaiMoney-Pages-Dashboard-UpdateTime-index-module__chart_title--3sEO5'])[2]")
        assert yesterday in dataDate or beforeYesterday in dataDate

        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")

        # 日期
        for order in orderList:
            date = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(1)')
            assert date.text is not None and date.text != ''

        # PID
        for order in orderList:
            pid = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(2)')
            assert len(pid.text) > 0

        # 商品信息
        for order in orderList:
            itemInfo = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(3)')
            itemPic = itemInfo.find_element(By.CSS_SELECTOR, 'div.src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-DataTable-index-module__info_img--3DgGM')
            assert 'url' in itemPic.get_attribute('style')
            itemTitle = itemInfo.find_element(By.CSS_SELECTOR, 'p.src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-DataTable-index-module__info_data_title--1rQlH')
            assert itemTitle.text is not None and len(itemTitle.text) > 0
            itemId = itemInfo.find_element(By.CSS_SELECTOR, 'div.src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-DataTable-index-module__info_data_id--2fVwT')
            assert str2number(itemId.text) > 0
            itemPrice = itemInfo.find_element(By.CSS_SELECTOR, 'p.src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-DataTable-index-module__info_data_value--U1s2o')
            assert str2number(itemPrice.text) > 0

        # 支付订单数
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(4)')
            assert orderNum.text is not None and orderNum.text != ''

        # 支付总金额
        for order in orderList:
            orderPrice = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(5)')
            assert str2number(orderPrice.text) is not None and str2number(orderPrice.text) >= 0

        # 实际结算金额
        for order in orderList:
            orderSettle = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(6)')
            assert str2number(orderSettle.text) is not None and str2number(orderSettle.text) >= 0

        # 佣金比例 > 0
        for order in orderList:
            commissionRate = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(7)')
            assert str2number(commissionRate.text) is not None and str2number(commissionRate.text) > 0

        # 预估佣金
        for order in orderList:
            estimate = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(8)')
            assert str2number(estimate.text) is not None and str2number(estimate.text) >= 0


    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_detail_sort_by_num(self):
        """
        快赚客数据参谋-直播推广：推广明细根据支付订单数排序
        """
        self.baseLogin()
        self.select_time()

        # originData = []
        # orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        # for order in orderList:
        #     orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(4)')
        #     originData.append(int(orderNum.text))

        # 升序
        self.click("(//span[contains(text(),'支付订单数')])[1]")
        time.sleep(0.5)
        ascData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(4)')
            ascData.append(int(orderNum.text))
        self.assert_true(all(ascData[i] <= ascData[i + 1] for i in range(len(ascData) - 1)) is True)  # 升序

        # isEqual = all(a == b for a, b in zip(originData, ascData))  # 判断是否发生变化

        # 降序
        self.click("(//span[contains(text(),'支付订单数')])[1]")
        time.sleep(0.5)
        descData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(4)')
            descData.append(int(orderNum.text))
        self.assert_true(all(descData[i] >= descData[i + 1] for i in range(len(descData) - 1)) is True)  # 降序


    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_detail_sort_by_price(self):
        """
        快赚客数据参谋-直播推广：推广明细根据支付总金额排序
        """
        self.baseLogin()
        self.select_time()

        # 升序
        self.click("(//span[contains(text(),'支付总金额')])[1]")
        time.sleep(0.5)
        ascData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(5)')
            ascData.append(str2number(orderNum.text))
        print(ascData)
        self.assert_true(all(ascData[i] <= ascData[i + 1] for i in range(len(ascData) - 1)) is True)  # 升序


        # 降序
        self.click("(//span[contains(text(),'支付总金额')])[1]")
        time.sleep(0.5)
        descData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(5)')
            descData.append(str2number(orderNum.text))
        print(descData)
        self.assert_true(all(descData[i] >= descData[i + 1] for i in range(len(descData) - 1)) is True)  # 降序

    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_detail_sort_by_rate(self):
        """
        快赚客数据参谋-直播推广：推广明细根据佣金比例排序
        """
        self.baseLogin()
        self.select_time()

        # 升序
        self.click("(//span[contains(text(),'佣金比例')])[1]")
        time.sleep(0.5)
        ascData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(7)')
            ascData.append(str2number(orderNum.text))
        print(ascData)
        self.assert_true(all(ascData[i] <= ascData[i + 1] for i in range(len(ascData) - 1)) is True)  # 升序
        self.assert_true(all(ascData[i] > 0 for i in range(len(ascData))) is True)

        # 降序
        self.click("(//span[contains(text(),'佣金比例')])[1]")
        time.sleep(0.5)
        descData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(7)')
            descData.append(str2number(orderNum.text))
        print(descData)
        self.assert_true(all(descData[i] >= descData[i + 1] for i in range(len(descData) - 1)) is True)  # 降序
        self.assert_true(all(descData[i] > 0 for i in range(len(ascData))) is True)

    @pytest.mark.p1
    def test_kwaimoney_live_promotion_data_detail_sort_by_income(self):
        """
        快赚客数据参谋-直播推广：推广明细根据预估佣金排序
        """
        self.baseLogin()
        self.select_time()

        # 升序
        self.click("(//span[contains(text(),'预估佣金')])[1]")
        time.sleep(0.5)
        ascData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(8)')
            ascData.append(str2number(orderNum.text))
        print(ascData)
        self.assert_true(all(ascData[i] <= ascData[i + 1] for i in range(len(ascData) - 1)) is True)  # 升序

        # 降序
        self.click("(//span[contains(text(),'预估佣金')])[1]")
        time.sleep(0.5)
        descData = []
        orderList = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for order in orderList:
            orderNum = order.find_element(By.CSS_SELECTOR, 'td.ant-table-cell:nth-child(8)')
            descData.append(str2number(orderNum.text))
        print(descData)
        self.assert_true(all(descData[i] >= descData[i + 1] for i in range(len(descData) - 1)) is True)  # 降序



    # todo: 补充翻页的逻辑，现在是因为这个账号没有翻页