import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
from selenium.webdriver.common.keys import Keys


'''
获取菜单Tab
'''


class TestKwaimoneyPidList(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'huoyangyang')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'huoyangyang')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//a[contains(text(),'推广位管理')])[1]")


    def test_kwaimoney_pid_list_field(self):
        """
        推广位管理列表-各元素显示正常
        """
        self.baseLogin()
        self.assert_text("推广位名称", "(//th[contains(text(),'推广位名称')])[1]")
        self.assert_text("PID", "(//th[normalize-space()='PID'])[1]")
        self.assert_text("操作", "(//th[contains(text(),'操作')])[1]")
        self.find_element("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        self.find_element("tr.ant-table-row.ant-table-row-level-0 td:nth-child(2)")
        self.find_element("tr.ant-table-row.ant-table-row-level-0 td:nth-child(3)")
        self.assert_text("新建推广位", "(//span[contains(text(),'新建推广位')])[1]")
        self.assert_text("导出当前数据", "(//button[@class='ant-btn'])[1]")
        self.assert_text("10 条/页", "(//span[@title='10 条/页'])[1]")


    def test_kwaimoney_pid_list_next_page(self):
        """
        推广位管理列表-翻页下一页
        """
        self.baseLogin()
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNames = [str(element.text) for element in elements]

        # 下一页
        self.click("(//button[@type='button'])[14]")
        time.sleep(1)
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" in self.get_attribute("(//li[@title='2'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNamesNew = [str(element.text) for element in elements]
        notDiff = (len(pidNamesNew) != len(pidNames) and all(a == b for a, b in zip(pidNamesNew, pidNames)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)

        # 上一页
        self.click("(//button[@type='button'])[13]")
        time.sleep(1)
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" not in self.get_attribute("(//li[@title='2'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNamesNewNew = [str(element.text) for element in elements]
        notDiff = (len(pidNamesNewNew) != len(pidNamesNew) and all(
            a == b for a, b in zip(pidNamesNewNew, pidNamesNew)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)

    def test_kwaimoney_pid_list_by_appoint_page(self):
        """
        推广位管理列表-指定页
        """
        self.baseLogin()
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNames = [str(element.text) for element in elements]

        self.click("(//li[@title='3'])[1]")
        time.sleep(1)
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" in self.get_attribute("(//li[@title='3'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNamesNew = [str(element.text) for element in elements]
        notDiff = (len(pidNamesNew) != len(pidNames) and all(
            a == b for a, b in zip(pidNamesNew, pidNames)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)


    def test_kwaimoney_pid_list_by_jump_page(self):
        """
        推广位管理列表-跳转到指定页
        """
        self.baseLogin()
        self.assert_true("active" in self.get_attribute("(//li[@title='1'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNames = [str(element.text) for element in elements]

        self.input("(//input[@type='text'])[1]", 4)
        input = self.find_element("(//input[@type='text'])[1]")
        input.send_keys(Keys.ENTER)
        time.sleep(3)
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        self.assert_true("active" in self.get_attribute("(//li[@title='4'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        pidNamesNew = [str(element.text) for element in elements]
        notDiff = (len(pidNamesNew) != len(pidNames) and all(
            a == b for a, b in zip(pidNamesNew, pidNames)))  # 只要有一个不等，就返回false
        self.assert_true(notDiff is False)


    def test_kwaimoney_pid_list_change_item_num(self):
        """
        推广位管理列表-修改页面数量
        """
        self.baseLogin()
        # 默认为10
        self.assert_text("10 条/页", "(//span[@title='10 条/页'])[1]")
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        self.assert_true(len(elements) == 10)
        # 切换为20
        self.click("(//div[@class='ant-select-selector'])[1]")
        time.sleep(0.2)
        self.click("div.ant-select-item.ant-select-item-option:nth-child(2)")
        time.sleep(1)
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        self.assert_true(len(elements) == 20)


    def test_kwaimoney_pid_list_edit_cancel(self):
        """
        推广位管理列表-推广位弹窗正常展示并可关闭
        """
        self.baseLogin()
        pidName = self.get_text("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")

        self.click("(//span[contains(text(),'编辑')])[1]")
        time.sleep(0.3)
        self.assert_text(pidName, "(//input[@id='promotionBitName'])[1]")
        self.assert_text("取 消", "(//span[contains(text(),'取 消')])[1]")
        self.assert_text("确 定", "(//span[contains(text(),'确 定')])[1]")

        self.click("(//span[@class='ant-modal-close-x'])[1]")
        self.assert_false(self.is_element_present("(//input[@id='promotionBitName'])[1]"))


    def test_kwaimoney_pid_list_edit_pid(self):
        """
        推广位管理列表-编辑推广位
        """
        self.baseLogin()
        pidName = self.get_text("tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")
        newPidName = pidName+"更新"

        self.click("(//span[contains(text(),'编辑')])[1]")
        time.sleep(0.5)
        self.assert_text(pidName, "(//input[@id='promotionBitName'])[1]")
        self.input("(//input[@id='promotionBitName'])[1]", newPidName)
        self.click("(//span[contains(text(),'确 定')])[1]")
        time.sleep(1.5)
        self.assert_text(newPidName, "tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")

        self.click("(//span[contains(text(),'编辑')])[1]")
        time.sleep(0.5)
        self.assert_text(newPidName, "(//input[@id='promotionBitName'])[1]")
        self.input("(//input[@id='promotionBitName'])[1]", pidName)
        self.click("(//span[contains(text(),'确 定')])[1]")
        time.sleep(1.5)
        self.assert_text(pidName, "tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)")






