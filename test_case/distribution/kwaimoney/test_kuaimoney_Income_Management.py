import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
获取菜单Tab
'''

@pytest.mark.p1
class TestKwaimoneyIncomeManagement(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        self.sleep(2)
        self.driver.maximize_window()
        self.sleep(0.2)
        self.click("(//span[contains(text(),'收益管理')])[1]")


    @pytest.mark.p0
    def test_kwaimoney_ReceivablesAccount_page(self):
        """
        点击收款账号-进入收款账号页面-判断二维码正常展示
        """
        self.baseLogin()
        self.click("(//a[contains(text(),'收款账号')])[1]")
        self.sleep(2)
        self.assert_text("可查看当前绑定的收款账户", "(//p[contains(text(),'可查看当前绑定的收款账户')])[1]")
        # 判断元素存在
        img_xpath = ['//*[@id="root"]/section/main/section/main/div/div']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "src-Components-KwaiQRCode-index-module__qrCodeContainer--21xXq" in outer_html, "缺少 src-Components-KwaiQRCode-index-module__qrCodeContainer--21xXq 属性"
            img_xpath = ['//*[@id="root"]/section/main/section/main/div/div/img']
            for xpath in img_xpath:
                img = self.find_element(xpath)
                outer_html = img.get_attribute("outerHTML")
                # 校验 width 和 height
                assert img.get_attribute("width") == "176", "宽度不正确"
                assert img.get_attribute("height") == "176", "高度不正确"


    @pytest.mark.p0
    def test_kwaimoney_BillManagement_page(self):
        """
        点击账单管理-进入账单管理页面-判断二维码正常展示
        """
        self.baseLogin()
        self.click("//a[contains(text(),'账单管理')]")
        self.sleep(2)
        self.assert_text("打开 快手App 扫一扫，可查看推广佣金账单并提现", "(//p[@class='src-Modules-KwaiMoney-Pages-Earnings-Bill-index-module__accountTip--3g7Xx'])[1]")
        # 判断元素存在
        img_xpath = ['//*[@id="root"]/section/main/section/main/div/div[2]/div']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "src-Components-KwaiQRCode-index-module__qrCodeContainer--21xXq" in outer_html, "缺少 src-Components-KwaiQRCode-index-module__qrCodeContainer--21xXq 属性"
            img_xpath = ['//*[@id="root"]/section/main/section/main/div/div[2]/div/img']
            for xpath in img_xpath:
                img = self.find_element(xpath)
                outer_html = img.get_attribute("outerHTML")
                # 校验 width 和 height
                assert img.get_attribute("width") == "176", "宽度不正确"
                assert img.get_attribute("height") == "176", "高度不正确"

    @pytest.mark.p0
    def test_kwaimoney_PromotionOrder_TimeScreening(self):
        """
        点击推广订单-进入推广订单页面-时间状态选择
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        self.sleep(1)
        self.click("//span[@title='支付时间']")
        self.sleep(2)
        self.click("//div[@title='结算时间']//div[1]")
        self.sleep(2)
        self.click('//*[@id="timeRange"]')
        element1 = self.find_element('//*[@id="timeRange"]')
        self.sleep(2)
        element1.clear()
        self.sleep(2)
        self.type("//input[@placeholder='开始日期']", '2024-07-01 00:00:00')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//input[@placeholder='结束日期']")
        element2 = self.find_element("//input[@placeholder='结束日期']")
        element2.clear()
        self.type("//input[@placeholder='结束日期']", '2024-09-26 23:59:59')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")


    @pytest.mark.p0
    def test_kwaimoney_BillManagement_page(self):
        """
        点击账单管理-进入账单管理页面-判断二维码正常展示
        """
        self.baseLogin()
        self.click("//a[contains(text(),'账单管理')]")
        self.sleep(2)
        self.assert_text("打开 快手App 扫一扫，可查看推广佣金账单并提现", "(//p[@class='src-Modules-KwaiMoney-Pages-Earnings-Bill-index-module__accountTip--3g7Xx'])[1]")
        # 判断元素存在
        img_xpath = ['//*[@id="root"]/section/main/section/main/div/div[2]/div']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "src-Components-KwaiQRCode-index-module__qrCodeContainer--21xXq" in outer_html, "缺少 src-Components-KwaiQRCode-index-module__qrCodeContainer--21xXq 属性"
            img_xpath = ['//*[@id="root"]/section/main/section/main/div/div[2]/div/img']
            for xpath in img_xpath:
                img = self.find_element(xpath)
                # 校验 width 和 height
                assert img.get_attribute("width") == "176", "宽度不正确"
                assert img.get_attribute("height") == "176", "高度不正确"

    @pytest.mark.p0
    def test_kwaimoney_PromotionOrder_ListField(self):
        """
        点击推广订单-进入推广订单页面-判断字段返回正常
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        self.sleep(2)
        self.assert_text("订单编号", "//th[contains(text(),'订单编号')]")
        self.assert_text("支付时间", "//th[contains(text(),'支付时间')]")
        self.assert_text("PID", "//th[normalize-space()='PID']")
        self.assert_text("商品信息", "//th[@title='商品信息']")
        self.assert_text("货款基数(元)", "//th[contains(text(),'货款基数(元)')]")
        self.assert_text("计佣金额(元)", "//th[contains(text(),'计佣金额(元)')]")
        self.assert_text("实付金额(元)", "//th[contains(text(),'实付金额(元)')]")
        self.assert_text("技术服务费(元)", "//span[contains(text(),'技术服务费(元)')]")
        self.assert_text("佣金率", "//span[contains(text(),'佣金率')]")
        self.assert_text("货款率", "//div[contains(text(),'货款率')]")
        self.assert_text("分成比例", "//span[contains(text(),'分成比例')]")
        self.assert_text("预估收入(元)", "//div[contains(text(),'预估收入(元)')]")
        self.assert_text("结算时间", "//th[contains(text(),'结算时间')]")
        self.assert_text("订单来源", "//th[contains(text(),'订单来源')]")
        self.assert_text("订单状态", "//th[contains(text(),'订单状态')]")
        self.assert_text("备注", "//th[contains(text(),'备注')]")

    @pytest.mark.p0
    def test_kwaimoney_PromotionOrder_IDSearch(self):
        """
        点击推广订单-进入推广订单页面-商品ID筛选正常
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        self.sleep(2)
        self.click('//*[@id="timeRange"]')
        element1 = self.find_element('//*[@id="timeRange"]')
        element1.clear()
        self.sleep(2)
        self.type("//input[@placeholder='开始日期']", '2024-07-01 00:00:00')
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/section/main/div/div[3]/form/div/div[1]/div/div[2]/div/div/span/div[2]/div/div/div/div/div[3]/input')
        element2 = self.find_element('//*[@id="root"]/section/main/section/main/div/div[3]/form/div/div[1]/div/div[2]/div/div/span/div[2]/div/div/div/div/div[3]/input')
        element2.clear()
        self.type('//*[@id="root"]/section/main/section/main/div/div[3]/form/div/div[1]/div/div[2]/div/div/span/div[2]/div/div/div/div/div[3]/input', '2024-09-26 23:59:59')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.type("//input[@placeholder='请输入商品ID']", '22455087709916')
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.assert_element(
            '//*[@id="root"]/section/main/section/main/div/div[4]/div[2]/div/div/div/div/div/div/table/tbody/tr[2]')
        img_xpath = ['//*[@id="root"]/section/main/section/main/div/div[4]/div[2]/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            # 校验 width 和 height
            assert img.get_attribute("width") == "60", "宽度不正确"
            assert img.get_attribute("height") == "60", "高度不正确"


    @pytest.mark.p0
    def test_kwaimoney_PromotionOrder_OrderStatus(self):
        """
        点击推广订单-进入推广订单页面-订单状态筛选正常
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        self.sleep(2)
        self.click('//*[@id="timeRange"]')
        element1 = self.find_element('//*[@id="timeRange"]')
        element1.clear()
        self.sleep(2)
        self.type("//input[@placeholder='开始日期']", '2024-07-01 00:00:00')
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//input[@placeholder='结束日期']")
        element2 = self.find_element("//input[@placeholder='结束日期']")
        element2.clear()
        self.type("//input[@placeholder='结束日期']", '2024-09-26 23:59:59')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.click("//span[@title='全部']")
        self.click("//div[contains(@title,'已付款')]//div[1]")
        self.sleep(2)
        self.click("//span[contains(@title,'已付款')]")
        self.click("//div[@title='已收货']//div[1]")
        self.sleep(2)
        self.click("//span[@title='已收货']")
        self.click("//div[contains(@title,'已结算')]//div[1]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        # 判断返回数量为50条
        self.assert_element("//span[normalize-space()='50']")






    @pytest.mark.p0
    def test_kwaimoney_PromotionOrder_PIDSearch(self):
        """
        点击推广订单-进入推广订单页面-PID筛选正常
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        self.sleep(2)
        self.click('//*[@id="timeRange"]')
        element1 = self.find_element('//*[@id="timeRange"]')
        element1.clear()
        self.sleep(2)
        self.type("//input[@placeholder='开始日期']", '2024-07-01 00:00:00')
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//input[@placeholder='结束日期']")
        element2 = self.find_element("//input[@placeholder='结束日期']")
        element2.clear()
        self.type("//input[@placeholder='结束日期']", '2024-09-26 23:59:59')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.type("//input[@placeholder='请输入PID']", 'ks_2995700434_102_1OOQOrvGP-A')
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        # 判断筛选结果为5条
        self.assert_element("//span[normalize-space()='5']")

    @pytest.mark.p0
    def test_kwaimoney_PromotionOrder_Paging(self):
        """
        点击推广订单-进入推广订单页面-订单分页正常
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        self.sleep(2)
        self.click('//*[@id="timeRange"]')
        element1 = self.find_element('//*[@id="timeRange"]')
        element1.clear()
        self.sleep(2)
        self.type("//input[@placeholder='开始日期']", '2024-07-01 00:00:00')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//input[@placeholder='结束日期']")
        element2 = self.find_element("//input[@placeholder='结束日期']")
        element2.clear()
        self.type("//input[@placeholder='结束日期']", '2024-09-26 23:59:59')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
    #     分页正常
        self.click("//a[normalize-space()='14']")
        self.assert_element(
            '//body[1]/div[2]/section[1]/main[1]/section[1]/main[1]/div[1]/div[4]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]')
    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_RulePresentation(self):
        """
        点击推广订单-进入奖金管理页面-规则展示正常
        """
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//span[contains(text(),'展开查看更多↓')]")
        self.sleep(2)
        self.assert_element(
            "//a[@href='https://ppg.m.etoote.com/doodle/XGRNaHTG.html']")

    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_AmountDisplay(self):
        """
        点击推广订单-进入奖金管理页面-金额字段展示正常
        """
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.assert_text("待处理金额（元）", "//div[@type='flex']//div[1]//div[1]//div[1]")
        self.assert_text("审核处理中金额（元）", "//div[@type='flex']//div[2]//div[1]//div[1]")
        self.assert_text("累计发放奖励金额（元）", "//div[@type='flex']//div[3]//div[1]//div[1]")

    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_TAB1ListField(self):
        """
        点击推广订单-进入奖金管理页面-待处理列表字段展示正常
        """
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.assert_text("创建时间", "//th[contains(text(),'创建时间')]")
        self.assert_text("流水单号", "//th[contains(text(),'流水单号')]")
        self.assert_text("活动信息", "//th[contains(text(),'活动信息')]")
        self.assert_text("活动类型", "//th[contains(text(),'活动类型')]")
        self.assert_text("奖金（元）", "//th[contains(text(),'奖金（元）')]")
        self.assert_text("发放备注", "//th[contains(text(),'发放备注')]")
        self.assert_text("操作", "//th[contains(text(),'操作')]")
    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_TAB2ListField(self):
        """
        点击推广订单-进入奖金管理页面-发放记录列表字段展示正常
        """
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//div[@aria-selected='false']")
        self.sleep(2)
        self.assert_text("创建时间", "//th[@class='fund-transfer-pc-table-cell fund-transfer-pc-table-cell-fix-left']")
        self.assert_text("申请提现时间", "//th[@class='fund-transfer-pc-table-cell fund-transfer-pc-table-cell-fix-left fund-transfer-pc-table-cell-fix-left-last']")
        self.assert_text("流水单号", "//th[contains(text(),'流水单号')]")
        self.assert_text("活动信息", "//th[contains(text(),'活动信息')]")
        self.assert_text("活动类型", "//th[contains(text(),'活动类型')]")
        self.assert_text("合并打款单号", "//th[contains(text(),'合并打款单号')]")
        self.assert_text("奖金（元）", "//th[contains(text(),'奖金（元）')]")
        self.assert_text("税费（元）", "//th[contains(text(),'税费（元）')]")
        self.assert_text("实际到账（元）", "//th[contains(text(),'实际到账（元）')]")
        self.assert_text("税费承担方", "//th[contains(text(),'税费承担方')]")
        self.assert_text("驳回原因", "//th[contains(text(),'驳回原因')]")
        self.assert_text("撤销原因", "//th[contains(text(),'撤销原因')]")
        self.assert_text("提现状态", "//th[contains(text(),'提现状态')]")
        self.assert_text("操作", "//th[@class='fund-transfer-pc-table-cell fund-transfer-pc-table-cell-fix-right fund-transfer-pc-table-cell-fix-right-first']")

    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_BonusStatus_Screen(self):
        """
        点击推广订单-进入奖金管理页面-奖金单状态筛选
        """
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//div[@aria-selected='false']")
        self.sleep(2)
        self.click('//*[@id="searchForm"]/div[1]/div[1]/div/div[2]/div/div/div/div/span[2]')
        self.sleep(2)
        self.click("//div[contains(text(),'审核中')]")
        self.sleep(2)
        self.click("//span[contains(@title,'审核中')]")
        self.sleep(2)
        self.click("//div[contains(text(),'提取中')]")
        self.sleep(2)
        self.click("//span[contains(@title,'提取中')]")
        self.sleep(2)
        self.click("//div[contains(text(),'提取成功')]")
        self.sleep(2)
        self.click("//span[contains(@title,'提取成功')]")
        self.sleep(2)
        self.click("//div[contains(@class,'fund-transfer-pc-select-item-option-content')][contains(text(),'提取失败')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)
        self.assert_element("//td[normalize-space()='2022-10-28 13:45:57']")

    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_ActivityType_Screen(self):
        """
		点击推广订单-进入奖金管理页面-活动类型筛选
		"""
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//div[@aria-selected='false']")
        self.sleep(2)
        self.click("//div[@role='tabpanel']//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//span[2]")
        self.sleep(2)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[contains(text(),'现金奖励活动')]")
        self.sleep(2)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[contains(text(),'TR后返活动')]")
        self.sleep(2)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[contains(text(),'快币奖励活动')]")
        self.sleep(2)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[contains(text(),'粉条奖励活动')]")
        self.sleep(2)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[contains(text(),'金牛奖励活动')]")
        self.sleep(2)
        self.click("(//div[@class='fund-transfer-pc-select-selector'])[2]")
        self.click("//div[contains(text(),'钱包奖励活动')]")
        self.sleep(2)

    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_ActivityType(self):
        """
    		点击推广订单-进入奖金管理页面-时间类型筛选
		"""
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//div[@aria-selected='false']")
        self.sleep(2)
        self.type("//input[@placeholder='开始日期']", '2024-07-01 00:00:00')
        self.sleep(2)
        self.type("//input[@placeholder='结束日期']", '2024-10-30 00:00:00')
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(2)


    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_WithdrawalRetry(self):
        """
		点击推广订单-进入奖金管理页面-提现重试按钮
		"""
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//div[@aria-selected='false']")
        self.sleep(2)
        self.click("//span[contains(text(),'提现重试')]")
        self.sleep(2)
        self.assert_text("提现重试", "//div[@class='fund-transfer-pc-modal-title']")
        img_xpath = ["//div[@class='fund-transfer-pc-modal-content']"]
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "fund-transfer-pc-modal-content" in outer_html, "缺少 fund-transfer-pc-modal-content 属性"
        self.assert_text("请输入收款的支付宝账号，发起重试","(//div[@class='formTitle___gsd8I'])[1]")
        self.sleep(2)
        self.click("//span[contains(text(),'取 消')]")


    @pytest.mark.p0
    def test_kwaimoney_BonusManagement_ViewDetails(self):
        """
		点击推广订单-进入奖金管理页面-发放记录-点击查看详情按钮
		"""
        self.baseLogin()
        self.click("//a[contains(text(),'奖金管理')]")
        self.sleep(2)
        self.click("//div[@aria-selected='false']")
        self.sleep(2)
        self.click("//span[contains(text(),'查看详情')]")
        self.sleep(2)
        img_xpath = ["//div[@class='fund-transfer-pc-modal-content']"]
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "fund-transfer-pc-modal-content" in outer_html, "缺少 fund-transfer-pc-modal-content 属性"
        self.assert_text("提现详情", "//div[@class='fund-transfer-pc-modal-title']")
        self.sleep(2)
        self.click("//span[contains(text(),'关 闭')]")
