import datetime
import random
import time
import re

import pyautogui
import requests
from selenium.webdriver import <PERSON><PERSON><PERSON><PERSON>, Keys
from selenium.webdriver.remote.webelement import WebElement
from seleniumbase import BaseCase
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from seleniumbase import BaseCase
from selenium.webdriver.support.events import EventFiringWebDriver, AbstractEventListener
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os  # 新增os模块处理路径


# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env

BaseCase.main(__name__, __file__)


from seleniumwire import webdriver

# class UrlListener(AbstractEventListener):
#     def __init__(self):
#         self.visited_urls = []
#
#     def after_navigate_to(self, url, driver):
#         self._add_url(url)
#
#     def after_navigate_forward(self, driver):
#         self._add_url(driver.current_url)
#
#     def after_navigate_back(self, driver):
#         self._add_url(driver.current_url)
#
#     def _add_url(self, url):
#         if url not in self.visited_urls:
#             self.visited_urls.append(url)


class DistributionToolTest(BaseCase):

    # def setUp(self):
    #     super().setUp()
    #     original_driver = self.driver
    #     self.listener = UrlListener()
    #     self.driver = EventFiringWebDriver(original_driver, self.listener)
    #
    # def open(self, url):
    #     super().open(url)
    #     self._record_current_url()
    #
    # def click(self, selector, by="css selector", timeout=None):
    #     original_url = self.driver.current_url
    #     super().click(selector, by=by, timeout=timeout)
    #     # 显式等待 URL 发生变化（适用于同步/异步跳转）
    #     try:
    #         WebDriverWait(self.driver, timeout or 10).until(
    #             EC.url_changes(original_url)
    #         )
    #     except Exception:
    #         pass  # 超时后继续执行，但记录当前 URL
    #     self._record_current_url()
    #
    # def _record_current_url(self):
    #     current_url = self.driver.current_url
    #     self.listener._add_url(current_url)
    #
    # def tearDown(self):
    #     """测试结束后自动保存URL到文件"""
    #     super().tearDown()  # 确保父类清理工作
    #
    #     # 获取当前测试方法名
    #     test_name = self._testMethodName
    #
    #     # 创建日志目录（如果不存在）
    #     log_dir = "url_logs"
    #     os.makedirs(log_dir, exist_ok=True)
    #
    #     # 定义日志文件路径（例如：url_logs/test_example_urls.log）
    #     log_file = os.path.join(log_dir, f"{test_name}_urls.log")
    #
    #     # 写入文件（每行一个URL）
    #     with open(log_file, "w", encoding="utf-8") as f:
    #         for url in self.listener.visited_urls:
    #             f.write(f"{url}\n")
    #
    #     # 可选：控制台输出提示（可注释掉）
    #     print(f"\n[INFO] URLs已保存至：{log_file}")

    # 分销商家登录
    def merchant_login(self, domain, account):

        # self.driver.quit()
        # self.driver = webdriver.Chrome()
        # self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        else:
            env = 'online'

        self.maximize_window()

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(1)

        if self.is_element_visible("(//span[contains(text(),'密码登录')])[1]") or self.is_element_visible("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        else:
            self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
            # self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
            # self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
            # self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
            self.sleep(1)
            self.click("//div[@class='choseTab___okqX0']//div[2]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            time.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("button[type='button']")
            self.sleep(1)
        self.sleep(2)
        self.refresh()

    def talent_login(self, domain, account):
        """分销 达人 登录"""
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        flag = "kwaixiaodian.com" in host

        if flag:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
        else:
            self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]/div[1]/div[2]')
            self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
            self.type("//input[@placeholder='请输入手机号']", account_data['account'])
            self.type("//input[@placeholder='请输入密码']", account_data['password'])
            self.click("//button[@type='button']")

        self.sleep(2)

    def kwaimoney_login(self, domain, account):
        """快赚客 登录"""
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        try:
            self.click("(//div[contains(text(),'手机号登录')])[1]")
        except:
            # 频繁登陆，可能出现报错页面
            self.click("(//span[contains(text(),'返回重试')])[1]")
            time.sleep(1)
            self.click("(//div[contains(text(),'手机号登录')])[1]")
        self.type("//input[@placeholder='请输入手机号']", account_data['account'])
        self.type("//input[@placeholder='请输入密码']", account_data['password'])
        self.click("//button[@type='button']")

    def leader_login(self, domain, account):
        """团长 登录"""
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]/div[1]/div[2]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("//input[@placeholder='请输入手机号']", account_data['account'])
        self.type("//input[@placeholder='请输入密码']", account_data['password'])
        self.click("//button[@type='button']")
        self.sleep(3)
        self.refresh()

    def bypass_account_login(self, domain, account):
        """分销商家 子账号登录"""
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        time.sleep(1)
        self.click("body div[id='root'] div div div div div div div:nth-child(2) div:nth-child(1) div:nth-child(2)")
        self.click("//body/div[@id='root']/div/div/div/div/div/div/div[2]")
        self.type("//input[@placeholder='请输入手机号']", account_data['account'])
        self.type("//input[@placeholder='请输入短信验证码']", account_data['password'])
        time.sleep(1)
        self.click("//button[@type='button']")
        self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div[2]")
        self.click("/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div[3]")
        time.sleep(2)

    def leader_sub_account_login(self, domain, account):
        """分销团长 子账号登录"""
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        time.sleep(1)
        self.click("div[class='container__content'] div:nth-child(2) div:nth-child(1) div:nth-child(2)")
        time.sleep(1)
        self.type("//input[@placeholder='请输入手机号']", account_data['account'])
        self.type("//input[@placeholder='请输入短信验证码']", account_data['password'])
        time.sleep(2)
        self.click("//button[@type='button']")
        time.sleep(3)

    def kxd_login(self):
        """ 小店登录 """
        self.open("https://s.kwaixiaodian.com/zone/goods/v1/list")
        self.driver.maximize_window()
        self.sleep(3)
        self.click('//span[contains(text(),"密码登录")]')
        self.type('//input[@id="username"]', "***********")
        self.type('//input[@id="password"]', "test123456")
        self.click('//span[contains(text(),"登 录")]')
        self.sleep(3)

    def is_element_clickable(self, element: WebElement, **kwargs) -> bool:
        """
        判断元素是否可点击
        :param element: WebElement对象
        :return: 元素是否可点击，可点击返回True，否则返回False
        """
        try:
            if element.is_enabled() and element.is_displayed():
                return True
            else:
                return False
        except Exception:
            return False

    def scroll_down(self, times):
        """
        模拟向下滑动页面
        :param times: 向下滑动的次数
        """
        for i in range(times):
            ActionChains(self.driver).send_keys(Keys.PAGE_DOWN).perform()
            time.sleep(3)

    def add_general_plan(self):
        """ 新建普通计划 """
        self.click("//span[contains(text(),'新建普通计划')]")
        self.click("//span[contains(text(),'跳 过')]")
        self.click("//tbody/tr/td[1]/label[1]")
        # self.click('//span[contains(text(),"取 消")]')
        time.sleep(2)
        self.click("//span[contains(text(),'下一步')]")
        self.input("//input[@placeholder='请输入0-50的整数']", '22')
        self.click("//span[contains(text(),'完 成')]")

    def get_random_activity_id(self, file_path):
        """
           从指定路径的txt文件中读取活动ID，并返回一个随机的活动ID

           参数:
           - file_path: 文件路径，包含活动ID的文本文件路径

           返回值:
           - 随机选择的活动ID

           异常:
           - FileNotFoundError: 文件不存在时引发该异常
           - IOError: 读取文件时发生错误时引发该异常
           - ValueError: 文件为空时引发该异常
           """
        try:
            with open(file_path, 'r') as file:
                activity_ids = file.read().splitlines()
                if activity_ids:
                    random_id = random.choice(activity_ids)
                    return random_id
                else:
                    raise ValueError("文件为空")
        except FileNotFoundError:
            raise FileNotFoundError(f"文件“{file_path}”不存在")
        except IOError:
            raise IOError(f"读取文件“{file_path}”时出错")

    def should_run_test_on_date(self):
        """
        判断是否应该在当前日期执行测试用例。

        返回值:
        - True：如果当前日期为双数
        - False：如果当前日期为单数
        """
        current_date = datetime.date.today()
        day = current_date.day
        if day % 2 == 0:  # 判断日期是否为双数
            return True
        else:
            return False

    def select_option(self, num_options, direction='down'):
        """
        使用方向键选择下拉框中的选项

        参数：
        - num_options：选择的次数
        - direction：选择的方向，可选值为 'down'、'up'、'left' 和 'right'，默认为 'down'

        注意：在调用该函数之前，请确保下拉框已经被打开

        """

        time.sleep(2)  # 等待2秒钟，确保下拉框已经打开

        # 确定选择的方向键
        if direction not in ['down', 'up', 'left', 'right']:
            raise ValueError("Invalid direction. Valid directions are 'down', 'up', 'left', and 'right'.")

        # 使用方向键选择选项
        for _ in range(num_options):
            pyautogui.press(direction)
            time.sleep(0.5)  # 等待0.5秒钟，给下拉框反应时间

        # 模拟按下回车键选择选项
        pyautogui.press('enter')

    def is_element_expected(self, element, attrsMap):
        """
        判断元素是否符合预期
        """
        try:
            for attrExpectedKey, attrExpectedValue in attrsMap.items():
                actualValue = element.get_attribute(attrExpectedKey).strip()
                if actualValue != attrExpectedValue:
                    return False
            return True
        except Exception:
            return False

    def get_hitokoto(self) -> object:
        """
        调用接口获取一句名言
        :return: 返回接口响应的名言，如果请求失败则返回None
        """
        # 接口URL
        url = 'https://v1.hitokoto.cn/?c=b'

        try:
            # 发送GET请求
            response = requests.get(url)

            # 检查请求是否成功
            if response.status_code == 200:
                # 解析响应的JSON数据
                data = response.json()
                # 返回名言
                return data.get('hitokoto', None)
            else:
                print('请求失败，状态码：', response.status_code)
                return None
        except Exception as e:
            print('请求过程中发生错误：', str(e))
            return None


    def find_first_number(self, text):
        match = re.search(r'\d+', text)
        if match:
            first_number = match.group()
            return int(first_number)
        return -1

    def select_time_for_kwaimoney(self, year, month, day):
        """
        快赚客时间选择器-订单列表样式的时间选择器
        """
        # 判断当前的年月日
        yearDiff, monthDiff = 0, 0
        nowYear = self.find_first_number(self.get_text("button.ant-picker-year-btn"))
        if nowYear != -1:
            yearDiff = year - nowYear
        nowMonth = self.find_first_number(self.get_text("button.ant-picker-month-btn"))
        if nowMonth != -1:
            monthDiff = month - nowMonth
        # 选择年
        while yearDiff > 0:   # 需要右移时间x次
            self.click("button.ant-picker-header-super-next-btn")
            yearDiff -= 1
        while yearDiff < 0:   # 需要左移时间x次
            self.click("button.ant-picker-header-super-prev-btn")
            yearDiff += 1

        # 选择月
        while monthDiff > 0:   # 需要右移时间x次
            self.click("button.ant-picker-header-next-btn")
            monthDiff -= 1
        while monthDiff < 0:   # 需要左移时间x次
            self.click("button.ant-picker-header-prev-btn")
            monthDiff += 1

        # 选择日
        self.click(f"(//div[@class='ant-picker-cell-inner'][normalize-space()='{day}'])[1]")

    def select_time_for_kwaimoney_v2(self, year, month, day, index):
        """
        快赚客时间选择器-数据参谋样式的时间选择器
        index:0-开始时间，1-结束时间
        """
        # 判断当前的年月日
        yearDiff, monthDiff = 0, 0
        elements = self.find_elements("button.ant-picker-year-btn")
        nowYears = [self.find_first_number(e.text) for e in elements]
        if len(nowYears) >= 2:
            yearDiff = year - nowYears[index]

        elements = self.find_elements("button.ant-picker-month-btn")
        nowMonths = [self.find_first_number(e.text) for e in elements]
        if len(nowMonths) >= 2:
            monthDiff = month - nowMonths[index]

        # month右移时间存在神奇的问题，html显示元素存在，但是却找不到
        if yearDiff == -1 and monthDiff > 0:
            monthDiff = monthDiff-12*abs(yearDiff)
            yearDiff = 0

        # print(f"index:{index}, yearDiff:{yearDiff}, monthDiff:{monthDiff}")

        # 选择年
        while yearDiff > 0:   # 需要右移时间x次
            self.click("button.ant-picker-header-super-next-btn")
            yearDiff -= 1
        while yearDiff < 0:   # 需要左移时间x次
            self.click("button.ant-picker-header-super-prev-btn")
            yearDiff += 1

        # 选择月
        while monthDiff > 0:   # 需要右移时间x次
            # button = WebDriverWait(self.driver, 5).until(
            #     EC.element_to_be_clickable((By.CSS_SELECTOR, "button.ant-picker-header-next-btn"))
            # )
            # button.click()
            self.click("button.ant-picker-header-next-btn")
            monthDiff -= 1
        while monthDiff < 0:   # 需要左移时间x次
            self.click("button.ant-picker-header-prev-btn")
            monthDiff += 1

        # 选择日
        self.click(f"(//div[@class='ant-picker-cell-inner'][normalize-space()='{day}'])[{index+1}]")

# 使用方法
if __name__ == "__main__":
    tool_test = DistributionToolTest()
    PhotoKit = tool_test.get_hitokoto()
    if PhotoKit:
        print("获取的名言：", PhotoKit)
