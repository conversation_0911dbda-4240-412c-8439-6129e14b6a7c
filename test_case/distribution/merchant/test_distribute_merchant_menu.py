# import time
#
# import pytest
#
# from test_case.distribution.distribution_base_case import DistributionToolTest
#
# """
# 获取一级菜单下的二级菜单&Tab
# pytest test_case/distribution/merchant/index/test_distribution_menu.py --headless -n=5
# """
#
#
# class TestDistributionHome(DistributionToolTest):
#
#     def setUp(self, **kwargs):
#         super().setUp()
#         self.maximize_window()
#
#     @pytest.mark.p0
#     def test_go_black_promoter(self):
#         """
#         点击基本设置-达人黑名单，判断是否进入黑名单页面
#         """
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click("li[id='menu-RTkyd20IuiA'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap']")
#         self.assert_text("达人黑名单", "(//div[@class='kwaishop-cps-merchant-base-pc-pro-title-title'])[1]")
#         self.assert_text("添加时间", "(//th[contains(text(),'添加时间')])[1]")
#         self.assert_text("达人ID", "(//th[contains(text(),'达人ID')])[1]")
#         self.assert_text("达人昵称", "(//th[contains(text(),'达人昵称')])[1]")
#
#     # @pytest.mark.p0
#     # @pytest.mark.skip
#     # def test_go_contact_the_official_waiter(self):
#     #     """
#     #     此tab已下线
#     #     点击基本设置-联系官方小二，判断是否进入联系官方小二页面
#     #     """
#     #     self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#     #     time.sleep(2)
#     #     self.click("//li[@id='menu-fe7igSiTnUk']//span[@class='cps-main-badge']//span[1]")
#     #     time.sleep(0.5)
#     #     self.assert_text(
#     #         "快分销业务会帮助优质的品牌和货品进行主播的撮合，如果您有店铺开通、商品发布等问题，请优先咨询小店客服：4001260088;",
#     #         "(//span[@class='ant-typography ant-typography-ellipsis ant-typography-single-line ant-typography-ellipsis-single-line'])[1]")
#     #     self.assert_text("行业名称", "(//th[contains(text(),'行业名称')])[1]")
#     #     self.assert_text("邮箱", "(//th[contains(text(),'邮箱')])[1]")
#
#     @pytest.mark.p0
#     def test_go_contact_seller(self):
#         """
#         点击基本设置-联系方式，判断是否进入联系方式页面
#         """
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(2)
#         self.click("(//span[contains(text(),'联系方式')])[1]")
#         time.sleep(0.5)
#         self.assert_text("绑定手机号后，达人和商家可在选货中心、招商广场等地方获取到您的联系方式并与您对接",
#                          "(//div[@class='index-module__headerRemark--tgJgJ'])[1]")
#         self.assert_text("联系方式", "(//th[contains(text(),'联系方式')])[1]")
#         self.assert_text("联系人", "(//th[contains(text(),'联系人')])[1]")
#
#     @pytest.mark.p0
#     def test_go_marketing_setting(self):
#         """
#         点击基本设置-营销设置，判断是否进入营销设置页面
#         """
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(2)
#         self.click("//li[@id='menu-8Gu7woD3T7Y']//span[@class='cps-main-menu-title-content']")
#         time.sleep(0.5)
#         self.assert_text("授权达人创建主播券", "(//span[contains(text(),'授权达人创建主播券')])[1]")
#
#     @pytest.mark.p0
#     def test_distribution_home(self):
#         """登录成功-进入首页-判断首页元素是否存在"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.assert_text('首页', "(//span[contains(text(),'首页')])[1]")
#
#     @pytest.mark.p0
#     def test_into_the_general_program(self):
#         """登录成功-进入首页-点击计划管理-判断是否进入计划管理页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click("//*[@id='menu-alIq_t8NvXM']/span/span/span/span")
#         time.sleep(1)
#         self.click("//*[@id='driver-popover-item']/div[4]/button")
#         time.sleep(3)
#         self.assert_text("新建普通计划", "div[class='kwaishop-cps-merchant-plan-pc-pro-toolbar-table-actions'] span")
#
#     @pytest.mark.p0
#     def test_orientation_of_incoming_goods(self):
#         """登录成功-进入首页-点击计划管理-点击商品定向-判断是否进入商品定向页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click("//*[@id='menu-isZ-CoLA9js']/span/span/span/span")
#         time.sleep(1)
#         self.assert_text('添加商品定向', "(//span[contains(text(),'添加商品定向')])[1]")
#
#     @pytest.mark.p0
#     def test_entry_orientation(self):
#         """登录成功-进入首页-点击计划管理-点击店铺定向-判断是否进入店铺定向页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-sg07k1_aqCM"]/span/span/span/span')
#         self.assert_text("添加店铺计划", '//span[contains(text(),"添加店铺计划")]')
#
#     @pytest.mark.p0
#     def test_access_to_exclusive_programs(self):
#         """登录成功-进入首页-点击计划管理-点击专属计划-判断是否进入专属计划页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
#         time.sleep(1)
#         self.assert_text("添加专属计划",
#                          "//button[@class='kwaishop-cps-merchant-plan-pc-btn kwaishop-cps-merchant-plan-pc-btn-primary']")
#         time.sleep(1)
#         self.click(
#             "//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/section[1]/main[1]/div[3]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[8]/div[1]/div[3]/a[1]")
#         time.sleep(1)
#         self.click("//div[@class='kwaishop-cps-merchant-plan-pc-modal-root']//button[1]")
#
#     @pytest.mark.p0
#     def test_access_ladder_program(self):
#         """登录成功-进入首页-点击计划管理-点击阶梯计划-判断是否进入阶梯计划页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click("//*[@id='menu-askewjM_o1o']/span/span/span/span")
#         self.assert_text("添加阶梯佣金", "//*[@id='root']/section/main/div/div[4]/div[1]/button/span[2]")
#
#     @pytest.mark.p0
#     def test_access_Optimization_assistant(self):
#         """登录成功-进入首页-点击计划管理-点击阶梯计划-判断是否进入商品优化助手页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
#         time.sleep(3)
#         self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
#         self.assert_text("商品优化助手",
#                          "//span[@class='kwaishop-cps-merchant-plan-pc-breadcrumb-link'][contains(text(),'商品优化助手')]")
#
#     @pytest.mark.p0
#     def test_enter_danta_square(self):
#         """登录成功-进入首页-点击找达人-点击达人广场-判断是否进入达人广场页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click("//*[@id='menu-BAdGHi1s8Cg']/span/span/span/span")
#         self.assert_element("//label[contains(text(),'内容标签')]")
#
#     @pytest.mark.p0
#     def test_distribution_qualification_list(self):
#         """登录成功-进入首页-点击找达人-点击达人排行榜-判断是否进入达人排行榜页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-B0gbqb0Da8g"]/span/span/span/span')
#         self.assert_text("月榜", '//span[contains(text(),"月榜")]')
#
#     @pytest.mark.p0
#     def test_enter_the_talent_investment(self):
#         """登录成功-进入首页-点击找达人-点击达人招商-判断是否进入达人招商页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-o_3hHY6QiF0"]/span/span/span/span')
#         self.assert_text("立即报名", '(//span[contains(text(),"立即报名")])[1]')
#
#     @pytest.mark.p0
#     def test_enter_the_head_of_investment(self):
#         """登录成功-进入首页-点击找团长-点击团长招商-判断是否进入团长招商页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-eK8dgZWJGXc"]/span/span/span/span')
#         self.assert_text('T3', '//span[normalize-space()="T3"]')
#
#     @pytest.mark.p0
#     def test_receiving_account_number(self):
#         """登录成功-进入首页-点击找团长-点击团长招商-判断是否进入我报名的活动页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span/span')
#         self.assert_in('查 询', "//span[contains(text(),'查 询')]")
#
#     @pytest.mark.p0
#     def test_store_trusteeship(self):
#         """登录成功-进入首页-点击找团长-点击店铺托管-判断是否进入店铺托管页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-UEXI0hgkOIU"]/span/span/span/span')
#         time.sleep(2)
#         self.assert_text("查看合作历史 >", '.index-module__button--TZEML')
#
#     @pytest.mark.p0
#     def test_contact_the_official(self):
#         """登录成功-进入首页-点击效果看板-点击合作达人-判断是否进入合作达人页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         self.sleep(3)
#         self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
#         self.assert_text('近7天', "(//span[contains(text(),'近7天')])[1]")
#
#     @pytest.mark.p0
#     def test_entry_promotion_effect(self):
#         """登录成功-进入首页-点击效果看板-点击推广效果-判断是否进入推广效果页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
#         self.assert_text('普通计划', '//*[@id="tab-normalPlan"]')
#
#     @pytest.mark.p0
#     def test_go_promotion_trend(self):
#         """登录成功-进入首页-点击效果看板-点击推广趋势-判断是否进入推广趋势页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
#         self.assert_text('推广趋势图(合计)', '//span[@class="title"]')
#
#     @pytest.mark.p0
#     def test_go_sample_delivery_management(self):
#         """登录成功-进入首页-点击寄样管理-判断是否进入寄样管理页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
#         self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')
#
#     @pytest.mark.p0
#     def test_go_distribution_qualification(self):
#         """登录成功-进入首页-点击分销资质-判断是否进入分销资质页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click("(//span[contains(text(),'资质列表')])[1]")
#         time.sleep(1)
#         self.assert_text('特殊达人带货审核', '//*[@id="tab-1"]')
#
#     @pytest.mark.p0
#     def test_go_gathering_plan(self):
#         """登录成功-进入首页-点击基本设置-点击聚力计划-判断是否进入聚力计划页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-FVonyCdfpr4"]/span/span/span/span')
#         self.assert_text('《快手聚力计划服务协议》', '//*[@id="root"]/section/main/div/div[3]/div[3]/a/span')
#
#     @pytest.mark.p0
#     def test_go_receivables_account(self):
#         """登录成功-进入首页-点击基本设置-点击收款账号-判断是否进入收款账号页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-_QG9hlHbBIM"]/span/span/span/span')
#         self.assert_text("请用快手APP扫描上图二维码查看", "//*[@id='root']/section/main/div[2]/p")
#
#     @pytest.mark.p0
#     def test_go_recover_account(self):
#         """登录成功-进入首页-点击基本设置-点击恢复设置-判断是否进入恢复设置页面"""
#         self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
#         time.sleep(3)
#         self.click('//*[@id="menu-eASD8q2RveA"]/span/span/span/span')
#         self.assert_text("开通设置", "//div[@class='kwaishop-cps-merchant-base-pc-pro-title-title']")