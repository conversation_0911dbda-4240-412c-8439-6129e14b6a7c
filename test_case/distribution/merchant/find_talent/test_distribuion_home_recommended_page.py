import pytest

from test_case.distribution.distribution_base_case import *


class TestHomeRecommend(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    def test_IntelligentRecommendation_tab(self):
        """小店-达人带货智能推荐"""
        self.merchant_login("MERCHANT_HOME_DOMAIN", 'wb_luyuanhong')
        self.click("(//span[contains(text(),'达人带货')])[1]")
        self.sleep(3)
        self.assert_text("海量达人为你匹配， 助力带货成单","(//span[@class='NDJGmkXVHNtXuZxKLqtv'])[1]")

    @pytest.mark.p1
    def test_IntelligentRecommendation_task(self):
        """跳转分销地址验证"""
        self.merchant_login("MERCHANT_HOME_DOMAIN", 'wb_luyuanhong')
        self.click("(//span[contains(text(),'达人带货')])[1]")
        self.sleep(1)
        self.click("(//div[@class='ZxH_mzP6wJLyp6VCRtcj'][contains(text(),'查看')])[1]")
        self.sleep(3)
        # 校验跳转后的链接是否正确
        self.assertEqual(
            "https://cps.kwaixiaodian.com/zone/daren-match/my-daren?coopType=1", self.get_current_url())
        self.switch_to_window(0)
        self.sleep(3)
        self.click("(//div[@class='cvvMbFgwIguHru3TiOGz'])[1]")
        self.assertEqual(
            "https://cps.kwaixiaodian.com/zone/daren-match/daren-recommend", self.get_current_url())
        self.switch_to_window(0)
        self.click("(//span[@class='cq3PpNNscfSB2dxfmIn8'][contains(text(),'快手号：')])[1]")
        self.sleep(3)
        self.assertIn("https://cps.kwaixiaodian.com/zone/daren-match/daren-detail", self.get_current_url())
        self.switch_to_window(0)
        self.click("(//a[contains(text(),'在线联系')])[1]")
        self.sleep(3)
        self.assertIn("https://im.kwaixiaodian.com/workbench/zone/cooperation", self.get_current_url())


    @pytest.mark.p1
    def test_IntelligentRecommendation_push(self):
        """无达人分销权限"""
        self.merchant_login("MERCHANT_HOME_DOMAIN", 'csr')
        self.click("(//span[contains(text(),'达人带货')])[1]")
        self.sleep(1)
        self.assert_text("暂不满足以下开通条件，改进后可开通","(//div[contains(text(),'暂不满足以下开通条件，改进后可开通')])[1]")
        self.sleep(1)
        self.click("(//div[@class='XnHVipphICn11ZO39DRO'])[1]")
        self.assertEqual("https://cps.kwaixiaodian.com/zone/cps/base/verification", self.get_current_url())



