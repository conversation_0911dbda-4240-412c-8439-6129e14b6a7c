import time

import pytest

from test_case.assistant.base import BaseCase
from test_case.distribution.distribution_base_case import DistributionToolTest

"""
找达人
找达人-达人招商
找达人-达人排行榜
python3 -m pytest test_case/distribution/merchant/find_talent/test_distribution_talent_promotion.py --headless -n=5
"""


class TestTalentPromotion(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    def test_talent_promotion_page(self):
        """ 进入首页-点击找达人-点击达人招商-判断是否进入达人招商页面 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click('//span[contains(text(),"达人招商")]')
        self.find_element("//span[contains(text(),'达人粉丝数')]")
        self.find_element("//span[contains(text(),'发布时间')]")

    @pytest.mark.p1
    def test_talent_list(self):
        """ 进入首页-点击找达人-点击达人排行榜-判断是否进入达人排行榜页面 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//*[@id='menu-B0gbqb0Da8g']/span/span/span/span")
        self.assert_text("带货销售榜", "//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[1]")
        self.assert_text("带货成长榜", "//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[2]")
        self.assert_text("单场峰值榜", "//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[3]")

    @pytest.mark.p1
    def test_weekly_list_talent_information(self):
        """ 进入首页-点击找达人-点击达人排行榜-判断是否存在-带货销售榜-带货成长榜-单场峰值榜 的周榜达人信息 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//*[@id='menu-B0gbqb0Da8g']/span/span/span/span")
        '''带货销售榜-周榜达人信息'''
        self.assert_true("//*[@id='rc-tabs-6-panel-1']/div[3]/div/div/div/div/div/div/table/tbody/tr[1]")
        '''带货成长榜-周榜达人信息'''
        self.click("//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[2]")
        self.assert_true("//*[@id='rc-tabs-6-panel-1']/div[3]/div/div/div/div/div/div/table/tbody/tr[1]")
        '''单场峰值榜-周榜达人信息'''
        self.click("//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[3]")
        self.assert_true("//*[@id='rc-tabs-6-panel-1']/div[3]/div/div/div/div/div/div/table/tbody/tr[1]")

    @pytest.mark.p1
    def test_monthly_list_of_talent_information(self):
        """ 达人排行榜-判断 带货销售榜-带货成长榜-单场峰值榜 是否存在月榜达人信息 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//*[@id='menu-B0gbqb0Da8g']/span/span/span/span")
        '''带货销售榜-月榜达人信息'''
        self.click("//span[contains(text(),'月榜')]")
        self.assert_true("//*[@id='rc-tabs-6-panel-1']/div[3]/div/div/div/div/div/div/table/tbody/tr[1]")
        '''带货成长榜-月榜达人信息'''
        self.click("//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[2]")
        self.click("//div[@class='cps-ant-tabs-tabpane cps-ant-tabs-tabpane-active']//span[contains(text(),'月榜')]")
        self.sleep(2)
        self.assert_true("//*[@id='rc-tabs-6-panel-1']/div[3]/div/div/div/div/div/div/table/tbody/tr[1]")
        '''单场峰值榜-月榜达人信息'''
        self.click("//*[@id='root']/section/main/div/div/div/div/div[1]/div[1]/div/div[3]")
        self.click("//div[@class='cps-ant-tabs-tabpane cps-ant-tabs-tabpane-active']//span[contains(text(),'月榜')]")
        self.sleep(2)
        self.assert_true("//*[@id='rc-tabs-6-panel-1']/div[3]/div/div/div/div/div/div/table/tbody/tr[1]")

    @pytest.mark.p1
    def test_category_click(self):
        """ 达人排行榜-选择经营类目之后清空 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//*[@id='menu-B0gbqb0Da8g']/span/span/span/span")
        time.sleep(3)
        self.click('//div[@class="cps-ant-dropdown-trigger"][contains(text(),"个护清洁")]')
        self.click('//div[contains(@class,"cps-ant-dropdown cps-ant-dropdown-placement-bottomLeft")]//li[1]')

    @pytest.mark.p1
    def test_the_talent_list_turns_the_page(self):
        """ 进入首页-点击找达人-点击达人排行榜-判断分页是否可以点击 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//*[@id='menu-B0gbqb0Da8g']/span/span/span/span")
        time.sleep(3)
        self.scroll_down(2)
        self.click("li[title='2'] a[rel='nofollow']")
        time.sleep(3)
        self.click("li[title='3'] a[rel='nofollow']")
        time.sleep(3)
        self.click("li[title='4'] a[rel='nofollow']")
        self.click("li[title='5'] a[rel='nofollow']")

    @pytest.mark.p1
    # @pytest.mark.skip
    def test_the_all_talent_list(self):
        """
        达人广场-查看全部直播推荐优秀达人
        点击进入达人推荐列表页-达人卡片是否存在
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click("//*[@id='menu-BAdGHi1s8Cg']/span/span/span/span")
        time.sleep(3)
        self.click("img[width='80'][height='142']")
        self.assert_element("(//div[@id='rc-tabs-1-tab-live'])[1]")
        time.sleep(2)
        self.click("(//div[@id='rc-tabs-1-tab-live'])[1]")
        self.assert_element("//video[@class='comp-video']")
