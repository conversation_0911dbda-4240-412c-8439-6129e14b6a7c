import pytest

from test_case.distribution.distribution_base_case import *


"""
找达人-同行合作参考

"""

class TestDistributionPeerReferences(DistributionToolTest):
    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        """进入同行合作参考页面"""
        self.click("//*[@id='menu-jKfaFSXK3b8']/span/span/span/span")
        time.sleep(3)



    @pytest.mark.p1
    def test_distribution_peer_references_page(self):
        """ 进入首页-点击找达人-点击同行合作参考-判断是否进入同行合作参考页面 """
        self.assert_text("近30日分销销售额", "(//div[contains(text(),'近30日分销销售额')])[1]")

    @pytest.mark.p1
    def test_peer_references_page_select_the_category(self):
        """ 同行合作参考页面-带货类目选择正常-清空已选内容 """
        self.click("//*[@id='pro-tag-form-wrapper']/div[1]/div[2]/div/div/div/div/div[2]")
        time.sleep(3)
        self.click("//*[@id='pro-tag-form-wrapper']/div[1]/div[2]/div/div/div/div/div[3]")
        time.sleep(3)
        self.click("//*[@id='pro-tag-form-wrapper']/div[1]/div[2]/div/div/div/div/div[4]")
        time.sleep(3)
        self.click("(//div[@title='清空'])[1]")
        time.sleep(3)

    @pytest.mark.p1
    def test_peer_references_page_list_sorting(self):
        """ 同行合作参考页面-同行店铺列表排序-排序切换 """
        self.click("(//span[contains(text(),'近30日合作达人数')])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'近30日合作商品数')])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'近30日分销销售额')])[1]")
        time.sleep(3)

    @pytest.mark.p1
    def test_peer_references_page_turn_the_page(self):
        """ 同行合作参考页面-同行店铺列表翻页正常 """
        self.click("(//a[normalize-space()='2'])[1]")
        time.sleep(3)
        self.click("(//a[normalize-space()='3'])[1]")
        time.sleep(3)
        self.click("(//a[normalize-space()='4'])[1]")
        time.sleep(3)

    @pytest.mark.p1
    def test_peer_references_page_page_size_switching(self):
        """ 同行合作参考页面-同行店铺列表页面大小切换正常 """
        self.click("(//span[@title='10 条/页'])[1]")
        time.sleep(3)
        self.click("//*[@id='root']/section/main/div/div/form/ul/li[10]/div/div[2]/div/div/div/div[2]/div/div/div/div[2]/div")
        time.sleep(3)
        self.click("(//span[@title='20 条/页'])[1]")
        time.sleep(3)
        self.click("//*[@id='root']/section/main/div/div/form/ul/li[10]/div/div[2]/div/div/div/div[2]/div/div/div/div[3]/div")
        time.sleep(3)
        self.assert_text("50 条/页", "(//span[@title='50 条/页'])[1]")

    @pytest.mark.p1
    def test_peer_references_page_turn_to_page_100(self):
        """ 同行合作参考页面-同行店铺列表最大页码100页 """
        self.click("(// a[normalize-space() = '100'])[1]")
        time.sleep(3)
        self.assert_text("99", "(//a[normalize-space()='99'])[1]")

    @pytest.mark.p1
    def test_peer_references_page_store_name_search(self):
        """ 同行合作参考页面-同行店铺列表店铺名称精确搜索正常 """
        self.type("(//input[@id='keyword'])[1]", "青青草原扛把子-内部测试")
        self.click("(//span[contains(text(),'搜索')])[1]")
        time.sleep(3)
        self.find_element("(//span[contains(text(),'青青草原扛把子-内部测试')])[1]")

    @pytest.mark.p1
    def test_peer_references_page_jump_to_the_details_page(self):
        """ 同行合作参考页面-同行店铺列表跳转店铺详情页面正常 """
        self.click("(//span[contains(text(),'查看详情')])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        time.sleep(3)
        self.assert_element("(//span[contains(text(),'统计时间')])[1]")

    @pytest.mark.p1
    def test_peer_references_details_page_switch_time(self):
        """ 同行合作参考店铺详情页面-切换时间 """
        self.click("(//span[contains(text(),'查看详情')])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        time.sleep(3)
        self.click("(//span[contains(text(),'近30天')])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'近7天')])[1]")
        time.sleep(3)

    @pytest.mark.p1
    def test_peer_references_details_page_jump_to_the_influencer_details_page(self):
        """ 同行合作参考店铺详情页面-跳转达人详情页面正常 """
        self.click("(//span[contains(text(),'查看详情')])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        time.sleep(3)
        self.click("(//div[@class='cooperationDarenItem___GoMbZ'])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        time.sleep(3)
        self.assert_element("(//span[contains(text(),'邀请带货')])[1]")

    @pytest.mark.p1
    def test_peer_references_details_page_search_for_influencer_nicknames(self):
        """ 同行合作参考店铺详情页面-搜索达人昵称 """
        self.type("(//input[@id='keyword'])[1]", "青青草原扛把子-内部测试")
        self.click("(//span[contains(text(),'搜索')])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        time.sleep(3)
        self.type("(//input[@id='keyword'])[1]", "铜锣湾扛把子自动化账号号")
        self.click("(//span[contains(text(),'搜索')])[1]")
        time.sleep(3)
        self.assert_element("(//div[@class='cooperationDarenItemInfoName___jg4am'])[1]")



