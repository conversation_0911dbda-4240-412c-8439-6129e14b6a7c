import time

import pytest

from test_case.assistant.base import BaseCase
from selenium.webdriver.common.by import By

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
达人广场
-达人详情页
pytest test_case/distribution/merchant/find_talent/test_distribution_master_details_page.py --headless -n=5
"""

class TestMasterDetailsPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    def test_sum_up(self):
        """
        概括
        粉丝数、推广商品数、合作店铺数、总销售额；
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[1]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[2]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[3]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[4]")
        # self.assert_text("数据更新于：" + datetime.datetime.now().strftime('%Y-%m-%d'), "//div[@class='index-module__detail_top--Q9kZu']//div[1]")

    @pytest.mark.p1
    def test_summarize_live_delivery_data(self):
        """
        概括-直播带货数据
        带货直播次数、场均销售额、进入直播间人数、直播GPM
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.find_element("//div[contains(text(),'带货直播次数')]")
        self.find_element("(//div[contains(text(),'场均销售额')])[1]")
        self.find_element("//div[contains(text(),'进入直播间人数')]")
        self.find_element("//div[contains(text(),'直播GPM')]")

    # @pytest.mark.p1
    # def test_summarize_short_band_cargo_data(self):
    #     """ 带货视频数量、带货视频播放量、带货短视频销售额、单视频平均观看人数、单视频平均销售额、视频GPM """
    #     self.login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(3)
    #     self.click("//body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/section[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[5]/div[1]/div[1]/div[2]/div[1]/div[2]/div[3]/button[1]/span[1]")
    #     time.sleep(3)
    #     self.find_element("//div[contains(text(),'带货视频数量')]")
    #     self.find_element("//div[contains(text(),'带货视频播放量')]")
    #     self.find_element("//div[contains(text(),'带货短视频销售额')]")
    #     self.find_element("//div[contains(text(),'单视频平均观看人数')]")
    #     self.find_element("//div[contains(text(),'单视频平均销售额')]")
    #     self.find_element("//div[contains(text(),'视频GPM')]")

    @pytest.mark.p1
    def test_summarize_master_matching_degree(self):
        """
        概括-达人匹配度
        商品匹配度、粉丝匹配度、历史带货匹配
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "231800340")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.find_element("//div[contains(text(),'商品匹配度')]")
        self.find_element("//div[contains(text(),'粉丝匹配度')]")
        self.find_element("//div[contains(text(),'历史带货匹配')]")

    @pytest.mark.p1
    def test_summarize_carrying_capacity_analysis(self):
        """
        概括-带货能力分析
        类目、均价、销售额、佣金参考；品牌、均价、销售额
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[3]/div[2]/div[1]/div[2]/div[1]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[3]/div[2]/div[2]/div[2]/div[1]")

    @pytest.mark.p1
    def test_summary_fan_characteristics_analysis(self):
        """ 性别、年龄、城市、地域、客单价 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//span[contains(text(),'近30日数据')]")
        time.sleep(3)
        self.scroll_down(1)
        self.find_element("//span[contains(text(),'性别')]")
        time.sleep(1)
        self.find_element("//span[contains(text(),'年龄')]")
        time.sleep(1)
        self.find_element("//span[contains(text(),'城市')]")
        time.sleep(1)
        self.find_element("//span[contains(text(),'地域')]")
        self.find_element("//span[contains(text(),'客单价')]")

    @pytest.mark.p1
    def test_summary_similar_people(self):
        """ 达人卡片存在 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(5)
        ''' 向下滑动1页 '''
        self.click("//span[contains(text(),'近30日数据')]")
        time.sleep(3)
        self.scroll_down(1)
        ''' 断言是否存在相似达人卡片元素 '''
        self.find_element("(//div[@class='talent_card___IzHM7'])[1]")

    @pytest.mark.p0
    def test_more_and_change_batch_of_buttons(self):
        """ “更多”、“换一批”按钮是否可点击 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(5)
        ''' 向下滑动1页 '''
        self.click("//span[contains(text(),'近30日数据')]")
        time.sleep(3)
        self.is_element_enabled("//span[@class='index-module__more--dQXmc']")
        self.is_element_enabled("//span[contains(text(),'换一批')]")

    @pytest.mark.p1
    def test_live_analysis_for_nearly_90_days(self):
        """ 直播分析 近7天、近30天、近90天 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'直播分析')]")
        time.sleep(5)
        self.click("//span[contains(text(),'近7天')]")
        self.click("//span[contains(text(),'近30天')]")
        time.sleep(2)
        self.click("//span[contains(text(),'近90天')]")
        self.find_element("//div[contains(text(),'直播场次')]")
        self.find_element("//div[contains(text(),'直播天数')]")

    @pytest.mark.p1
    def test_live_analytics_live_data(self):
        """  直播场次、直播天数、直播观看人数、场均直播时长、平均在线人数、在线人数峰值、互动率 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'直播分析')]")
        time.sleep(2)
        self.find_element("//div[contains(text(),'直播观看人数')]")
        self.find_element("//div[contains(text(),'场均直播时长')]")
        self.find_element("//div[contains(text(),'平均在线人数')]")
        self.find_element("//div[contains(text(),'在线人数峰值')]")
        self.find_element("//div[contains(text(),'互动率')]")

    @pytest.mark.p1
    def test_live_analytics_data_trends(self):
        """  场观人数、在线人数、峰值、直播时长、点赞数、评论数、分享数；走势图是否存在 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'直播分析')]")
        time.sleep(2)
        self.click("//span[contains(text(),'在线人数峰值')]")
        time.sleep(2)
        self.find_element("//span[contains(text(),'场观人数')]")
        self.find_element("//span[contains(text(),'在线人数峰值')]")
        self.find_element("//span[contains(text(),'直播时长')]")
        self.find_element("//span[contains(text(),'点赞数')]")
        self.find_element("//span[contains(text(),'评论数')]")
        self.find_element("//span[contains(text(),'分享数')]")

    @pytest.mark.p1
    def test_live_stream_analysis_live_stream_listings(self):
        """ 直播信息、直播曝光人数、人数峰值、成交订单量、成交销售额、带货商品数量、操作 (元素是否存在)"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'直播分析')]")
        self.scroll_down(1)
        time.sleep(2)
        self.find_element("//th[contains(text(),'直播信息')]")
        self.find_element("//th[contains(text(),'直播曝光人数')]")
        self.find_element("//th[contains(text(),'人数峰值')]")
        self.find_element("//th[contains(text(),'成交订单量')]")
        time.sleep(2)
        self.find_element("//th[contains(text(),'成交销售额')]")
        self.find_element("//th[contains(text(),'带货商品数量')]")
        self.find_element("//th[contains(text(),'操作')]")

    @pytest.mark.p1
    def test_live_list_details(self):
        """
        直播分析-直播列表-详情
        详情、进入直播详情、翻页
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(2)
        self.click("//div[contains(text(),'直播分析')]")
        time.sleep(5)
        self.click("//span[contains(text(),'近90天')]")
        time.sleep(3)
        self.scroll_down(3)
        """ 打开详情 翻页之后关闭 """
        self.click("tbody tr:nth-child(1) td:nth-child(7) div:nth-child(1)")
        """ 翻页 """
        self.click("(//button[@type='button'])[8]")
        time.sleep(3)
        self.click("(//button[@type='button'])[8]")
        time.sleep(1)
        self.click("button[aria-label='Close']")


    @pytest.mark.p1
    def test_short_video_analysis_cargo_video_data(self):
        """
        短视频分析-带货视频数据
        视频数量、播放量、成交金额(元)、点赞数、评论数、转发数
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'短视频分析')]")

        number_of_delivery_videos = self.get_text("//*[@id='rc-tabs-0-panel-video']/div/div[1]/div/div[1]")
        self.assert_in("视频数量", number_of_delivery_videos)

        self.find_element("//*[@id='rc-tabs-0-panel-video']/div/div[1]/div/div[2]")
        self.find_element("//*[@id='rc-tabs-0-panel-video']/div/div[1]/div/div[3]")
        self.find_element("//*[@id='rc-tabs-0-panel-video']/div/div[1]/div/div[4]")
        self.find_element("//*[@id='rc-tabs-0-panel-video']/div/div[1]/div/div[5]")
        self.find_element("//*[@id='rc-tabs-0-panel-video']/div/div[1]/div/div[6]")

    @pytest.mark.p1
    def test_short_video_analysis_data_trends(self):
        """
        短视频分析-数据趋势
        播放量、点赞数、评论数、转发数；走势图是否存在
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'短视频分析')]")
        """ 断言 """
        # self.is_element_clickable("//span[contains(text(),'播放量')]")
        # self.is_element_clickable("//span[contains(text(),'点赞数')]")
        # self.is_element_clickable("//span[contains(text(),'评论数')]")
        # self.is_element_clickable("//span[contains(text(),'转发数')]")
        """ 趋势图 """
        self.assert_element("//canvas[@micro-creator='kwaishop-cps-daren-match-pc']")

    @pytest.mark.p1
    def test_short_video_analysis_list_of_short_video_with_cargo(self):
        """
        短视频分析-带货短视频列表
        按发布时间、按播放量、按带货销售额；商品卡是否存在
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        time.sleep(3)
        self.click("//div[@id='searchInput']//div[3]")
        self.click("//div[contains(text(),'短视频分析')]")
        self.scroll_down(2)
        """ 断言 """
        # self.is_element_clickable("//span[contains(text(),'按发布时间')]")
        # self.is_element_clickable("//span[contains(text(),'按播放量')]")
        # self.is_element_clickable("//span[contains(text(),'按带货销售额')]")
        self.assert_element("//*[@id='rc-tabs-0-panel-video']/div/div[3]/div/div/div[2]/div/div")

    # @pytest.mark.p1
    # def test_short_video_analysis_list_with_cargo_turn_the_page(self):
    #     """
    #     短视频分析-带货短视频列表-翻页
    #     """
    #     self.login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     self.type("#rc_select_0", "403082302")
    #     time.sleep(3)
    #     self.click("//div[@id='searchInput']//div[3]")
    #     self.click("//div[contains(text(),'短视频分析')]")
    #     self.scroll_down(5)
    #     self.click("//a[normalize-space()='2']")
    #     self.click("//a[normalize-space()='3']")
    #     self.click("//a[normalize-space()='4']")

    @pytest.mark.p1
    def test_fan_analysis_fan_characteristics_analysis(self):
        """
        粉丝分析-粉丝特性分析
        性别、年龄、城市、地域、客单价
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//div[contains(text(),'粉丝分析')]")
        self.assert_element("//span[contains(text(),'性别')]")
        self.assert_element("//span[contains(text(),'年龄')]")
        time.sleep(3)
        self.assert_element("//span[contains(text(),'城市')]")
        self.assert_element("//span[contains(text(),'地域')]")
        self.assert_element("//span[contains(text(),'客单价')]")

    @pytest.mark.p1
    def test_fan_analysis_fan_trends(self):
        """
        粉丝分析-粉丝趋势
        近7天、近30天、近90天；走势图是否存在
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "339933349")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(5)
        """ 断言 """
        self.click("//div[contains(text(),'粉丝分析')]")
        time.sleep(3)
        self.assert_element("//span[contains(text(),'近7天')]")
        self.assert_element("//span[contains(text(),'近30天')]")
        self.assert_element("//span[contains(text(),'近90天')]")
        time.sleep(3)
        self.click("//span[contains(text(),'近90天')]")
        self.assert_element("//div[@class='trend___RqNsG']//div//div//canvas[@micro-creator='kwaishop-cps-daren-match-pc']")

    @pytest.mark.p1
    def test_fan_analysis_fan_portraits(self):
        """
        粉丝分析-粉丝画像
        粉丝活跃时间、性别、年龄、省份
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "50204684")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(5)
        """ 断言 """
        self.click("//div[contains(text(),'粉丝分析')]")
        time.sleep(3)
        self.scroll_down(3)
        self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[2]/div[1]")
        self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[2]/div[2]")
        self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[2]/div[3]")
        self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[2]/div[4]")

    # @pytest.mark.p1
    # def test_fan_analysis_fan_portrait_icon(self):
    #     """
    #     粉丝分析-粉丝画像图标
    #     粉丝活跃时间、粉丝喜好、性别分布、年龄分布、粉丝城市线分布、粉丝地域分布
    #     """
    #     self.login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     self.type("#rc_select_0", "40300141")
    #     self.click("//div[@id='searchInput']//div[3]")
    #     time.sleep(5)
    #     """ 断言 """
    #     self.click("//div[contains(text(),'粉丝分析')]")
    #     time.sleep(3)
    #     self.scroll_down(4)
    #     time.sleep(5)
    #     self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[3]/div[1]")
    #     self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[3]/div[2]")
    #     self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[3]/div[3]")
    #     self.assert_element("//*[@id='rc-tabs-0-panel-fan']/div/div[3]/div[3]/div[4]")
    #     time.sleep(3)
    #     self.assert_element(".index-module__fan-region--elQ2p")

    @pytest.mark.p1
    def test_shipment_analysis_shipment_analysis_data(self):
        """
        带货分析-带货分析数据
        总销售额、推广商品数量、合作店铺数量、平均客单价、场均观看人数、场均销售额
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "256480142")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//div[contains(text(),'带货分析')]")
        self.assert_element("//div[contains(text(),'分销销售额')]")
        self.assert_element("//div[contains(text(),'推广商品数量')]")
        self.assert_element("//div[contains(text(),'合作店铺数量')]")
        time.sleep(3)
        self.assert_element("//div[contains(text(),'平均客单价')]")
        self.assert_element("//div[contains(text(),'场均销售额')]")

    @pytest.mark.p1
    def test_shipment_analysis_shipment_list(self):
        """
        带货分析-带货商品列表
        商品信息、店铺、售价、销售额、关联直播场次、关联短视频数量；商品卡片
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "256480142")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        """ 断言 """
        self.click("//div[contains(text(),'带货分析')]")
        self.assert_element("//th[contains(text(),'商品信息')]")
        self.assert_element("//th[contains(text(),'店铺')]")
        self.assert_element("//th[contains(text(),'售价')]")
        time.sleep(3)
        self.assert_element("//th[contains(text(),'关联直播场次')]")
        self.assert_element("//th[contains(text(),'关联短视频数量')]")
        self.assert_element("tbody tr:nth-child(1)")

    @pytest.mark.p1
    def test_shipment_analysis_shipment_of_goods(self):
        """
        带货分析-带货商品
        类目、品牌、展开、收起
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "349694754")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        """ 断言 """
        self.is_element_enabled("//*[@id='items']/span[2]")
        self.is_element_enabled("//*[@id='rc-tabs-1-panel-1']/div/div[1]/div[3]/span")
        time.sleep(3)
        self.is_element_enabled("//*[@id='items']/div")

    @pytest.mark.p1
    def test_shipment_analysis_list_of_partner_stores(self):
        """
        带货分析-合作店铺列表
        店铺信息、推广商品、推广GMV
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "18541124")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//div[contains(text(),'带货分析')]")
        self.click("//div[@role='tab'][contains(text(),'合作店铺')]")
        """ 断言 """
        self.assert_element("//th[contains(text(),'店铺信息')]")
        self.assert_element("//th[contains(text(),'推广商品')]")
        self.assert_element("//th[contains(text(),'推广GMV')]")

    @pytest.mark.p1
    def test_shipment_analysis_promotion_drawer(self):
        """
        带货分析-推广商品抽屉
        点击“商品图片”校验抽屉是否展开；商品价格、成交销售额
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "40300141")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//div[contains(text(),'带货分析')]")
        self.click("//div[@role='tab'][contains(text(),'合作店铺')]")
        self.is_element_clickable("(//img[@class='itemImg___FoWG1'])[1]")
        self.click("(//img[@class='itemImg___FoWG1'])[1]")
        """ 断言 """
        time.sleep(3)
        self.assert_element("//div[contains(text(),'推广商品列表')]")
        self.assert_element("//span[contains(text(),'商品价格')]")
        self.assert_element("//th[contains(text(),'成交销售额')]")

    @pytest.mark.p1
    def test_shipment_analysis_list_turning_page(self):
        """
        带货分析-列表翻页;
        带货商品列表翻页-合作店铺列表翻页
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "403082302")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//div[contains(text(),'带货分析')]")
        time.sleep(2)
        self.click("//span[contains(text(),'近90天')]")
        time.sleep(2)
        self.click("//div[@role='tab'][contains(text(),'合作店铺')]")
        """ 断言 """
        self.scroll_down(1)
        self.is_element_clickable("//*[@id='rc-tabs-1-panel-2']/div/div[2]/div[2]/div/div/ul/li[3]")
        self.click("//*[@id='rc-tabs-1-panel-2']/div/div[2]/div[2]/div/div/ul/li[3]")
        self.is_element_clickable("//*[@id='rc-tabs-1-panel-2']/div/div[2]/div[2]/div/div/ul/li[6]")
        self.click("//*[@id='rc-tabs-1-panel-2']/div/div[2]/div[2]/div/div/ul/li[6]")

    @pytest.mark.p1
    def test_match_analysis_match_of_talent(self):
        """
        匹配度分析-达人匹配度
        综合匹配度、商品匹配度、粉丝匹配度、历史带货匹配
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "40300141")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(3)
        self.click("//div[contains(text(),'匹配度分析')]")
        self.assert_element("//span[contains(text(),'达人匹配度')]")
        # 粉丝匹配度
        self.assert_element("(//div[@class='title___cuAqL'])[1]")
        # 商品匹配度
        self.assert_element("(//div[@class='title___cuAqL'])[2]")
        # 历史带货匹配度
        self.assert_element("(//div[@class='title___cuAqL'])[3]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_matching_analysis_match_of_talent_for_all(self):
        """
        匹配度分析-匹配度分析
         粉丝匹配度、店铺消费者、达人粉丝、商品匹配度、店铺商品、达人历史带货商品
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "40300141")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(2)
        self.click("//div[contains(text(),'匹配度分析')]")

        # 粉丝匹配度（店铺消费者、达人粉丝）
        self.assert_element("(//span[contains(text(),'店铺消费者')])[1]")
        self.assert_element("(//span[contains(text(),'达人粉丝')])[1]")
        elements = self.find_elements(
            "div.index-module__match-card--qQ9lc:nth-child(2) div.index-module__analysis-card-row--hM_ET div.index-module__label-container--KfIq8 div.index-module__label-text--Ga0g8")
        for expected, element in zip(['性别', '年龄', '城市', '地域', '客单价'], elements):
            self.assert_true(element.text == expected)
        # 左右元素各五个
        elements = self.find_elements(
            "div.qQ9lc2tOOaQDGCYyexdX:nth-child(2) div.dEkwvmCzVDT2qn2M8jdy")
        self.assert_true(len(elements) == 5)
        elements = self.find_elements(
            "div.qQ9lc2tOOaQDGCYyexdX:nth-child(2) div.UM79pcwAGMGaMDMZlG2f")
        self.assert_true(len(elements) == 5)

        # 商品匹配度（店铺商品、达人历史带货商品）
        self.assert_element("(//span[contains(text(),'店铺商品')])[1]")
        self.assert_text("达人历史带货商品", "(//span[@class='X_opXu7yzatCPnWHidM0'])[1]")
        elements = self.find_elements(
            "div.index-module__match-card--qQ9lc:nth-child(3) div.index-module__analysis-card-row--hM_ET div.index-module__label-container--KfIq8 div.index-module__label-text--Ga0g8")
        for expected, element in zip(['类目', '价格带', '品牌'], elements):
            self.assert_true(element.text == expected)
        # 左右元素各三个
        elements = self.find_elements(
            "div.qQ9lc2tOOaQDGCYyexdX:nth-child(3) div.UM79pcwAGMGaMDMZlG2f")
        self.assert_true(len(elements) == 3)
        elements = self.find_elements(
            "div.qQ9lc2tOOaQDGCYyexdX:nth-child(3) div.UM79pcwAGMGaMDMZlG2f")
        self.assert_true(len(elements) == 3)

    @pytest.mark.p1
    def test_matching_analysis_match_of_talent_for_shop_count(self):
        """
        匹配度分析-历史带货匹配度
        达人历史合作店铺、对比项、历史合作店铺 (近90天)、店铺30日GMV、对比结果
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "40300141")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(2)
        self.click("//div[contains(text(),'匹配度分析')]")

        # 对比项（店铺消费者、达人粉丝）
        self.assert_element("(//th[@class='kwaishop-cps-daren-match-pc-table-cell'][contains(text(),'对比项')])[1]")
        self.assert_element("(//td[contains(text(),'历史合作店铺数')])[1]")
        self.assert_element("(//td[contains(text(),'店铺30日GMV')])[1]")
        self.assert_element("(//th[@class='kwaishop-cps-daren-match-pc-table-cell'][contains(text(),'对比结果')])[1]")

        element = self.find_element("div.kwaishop-cps-daren-match-pc-table-content:nth-child(1)")
        elements = element.find_elements(by=By.CSS_SELECTOR,
                                         value="tr.kwaishop-cps-daren-match-pc-table-row.kwaishop-cps-daren-match-pc-table-row-level-0 td.kwaishop-cps-daren-match-pc-table-cell:nth-child(4)")
        self.assert_true(len(elements) == 2)
        self.assert_true(all(item.text != "" for item in elements) is True)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_matching_analysis_match_of_talent_for_promoter_count(self):
        """
        匹配度分析-历史合作过的达人
        历史合作过的达人：对比项、历史合作达人、粉丝量、场均GMV、场均观看人数、单视频平均销售额、单视频平均观看量
        """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.type("#rc_select_0", "40300141")
        self.click("//div[@id='searchInput']//div[3]")
        time.sleep(2)
        self.click("//div[contains(text(),'匹配度分析')]")

        # 对比项
        self.assert_element("(//th[@class='kwaishop-cps-daren-match-pc-table-cell'][contains(text(),'对比项')])[2]")
        self.assert_element("(//td[contains(text(),'粉丝量')])[1]")
        self.assert_element("(//td[contains(text(),'场均GMV')])[1]")
        self.assert_element("(//td[contains(text(),'场均观看人数')])[1]")
        self.assert_element("(//td[contains(text(),'单视频平均销售额')])[1]")
        self.assert_element("(//td[contains(text(),'单视频平均观看量')])[1]")

        elements = self.find_elements("div.kwaishop-cps-daren-match-pc-table-content")
        elements = elements[1].find_elements(by=By.CSS_SELECTOR,
                                         value="tr.kwaishop-cps-daren-match-pc-table-row.kwaishop-cps-daren-match-pc-table-row-level-0 td.kwaishop-cps-daren-match-pc-table-cell:nth-child(4)")
        self.assert_true(len(elements) == 5)
        self.assert_true(all(item.text != "" for item in elements) == True)
