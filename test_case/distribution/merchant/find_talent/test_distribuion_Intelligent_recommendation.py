
import pytest

from test_case.distribution.distribution_base_case import *

"""
找达人-达人智能推荐
"""


class TestExpertIntelligentRecommendation(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)



    @pytest.mark.p1
    def test_Expert_Recommended_page(self):
        """ 成功进入首页-点击找达人-点击达人智能推荐-判断是否进入达人智能推荐页面 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(3)
        self.assert_text("达人智能推荐",
                         "//div[@class='kwaishop-cps-daren-match-pc-pro-title-title']")



    @pytest.mark.p1
    def test_Recommended_Tab_Toggle(self):
        """ 成功进入首页-点击找达人-点击达人智能推荐-切换推荐达人tab """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[2]')
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[3]/div')
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div')
        time.sleep(2)

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_Tabtoggle(self):
        """ 进入首页-点击找达人-点击达人智能推荐-每日达人推荐下——切换类目 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'同类商家青睐')]")
        time.sleep(2)
        self.click("//div[contains(text(),'近期数据飙升')]")
        time.sleep(2)
    @pytest.mark.p1
    def test_Recommended_EverydayPerson_Screen(self):
        """ 进入首页-点击找达人-点击达人智能推荐-每日达人推荐下——家居百货精选筛选 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click("//div[@title='有联系方式']")
        time.sleep(2)
        self.click("//div[@title='无坑位费']")
        time.sleep(2)
        self.click("//div[@title='招商中达人']")
        time.sleep(2)
        self.click("//div[@title='达人开启邀约']")
        time.sleep(2)
        self.click("//div[@title='短视频达人']")
        time.sleep(2)
        self.click("//div[@title='直播达人']")
        time.sleep(2)

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_List(self):
        """ 进入首页-点击找达人-点击达人智能推荐-每日达人推荐下——家居百货精选  字段 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.assert_text("快手号",
                         '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.assert_text("粉丝数",
                         "//body//div//div[@class='kwaishop-cps-daren-match-pc-spin-container']//div//div//div[1]//div[1]//div[1]//div[1]//div[1]//div[2]//div[1]//div[2]//div[2]//div[2]//div[1]")
        self.assert_text("带货TOP商品",
                         "(//div[@class='topItemTitle___neMgZ'][contains(text(),'带货TOP商品')])[1]")
        self.assert_text("带货TOP商家",
                         '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]')
        self.assert_text("30日直播场均看播人数",
                         '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[2]/div/div/div[1]/div[2]')
        self.assert_text("30日直播场均销售额(元)",
                         '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[2]/div/div/div[2]/div[2]')
        self.assert_text("千次曝光成交额(元)",
                         '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[2]/div/div/div[3]/div[2]')
        self.assert_text("商品均价(元)",
                         '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[2]/div/div/div[4]/div[2]')

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_Player(self):
        """ 进入首页-点击找达人-点击达人智能推荐-每日达人推荐下——直播达人列表，播放器组件 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        img_xpath = ['//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div[1]/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "data-component-version" in outer_html, "缺少 data-component-version 属性"
            assert img.get_attribute('data-component-name') == 'AssetsImage'
            assert img.get_attribute('data-component-version') == "1.1.3"
            assert img.get_attribute('src') != "none" or ''

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_Avatar(self):
        """ 进入首页-点击找达人-点击达人智能推荐-每日达人推荐下——直播达人列表，头像 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        img = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div[2]/div[1]/div[1]/span/img')
        # 校验 width 和 height
        assert img.get_attribute('src') != "none"
        assert img.get_attribute("width") == "52", "宽度不正确"
        assert img.get_attribute("height") == "52", "高度不正确"
        assert img.get_attribute('src') != "none" or ''

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_Picture(self):
        """ 进入首页-点击找达人-点击达人智能推荐-每日达人推荐下——直播达人列表，商品图片 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        img = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div[2]/div[2]/div[1]/div[2]/picture[1]/img')
        # 校验 width 和 height
        assert img.get_attribute("width") == "44", "宽度不正确"
        assert img.get_attribute("height") == "44", "高度不正确"
        assert img.get_attribute('src') != "none" or ''

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_CommoditySwitching(self):
        """ 进入首页-点击找达人-点击达人智能推荐-商品推荐达人下—点击切换推荐商品 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[2]')
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div[2]/div/div/div[1]/div[1]')
        time.sleep(2)
        self.click(
            '//*[@id="pro-form-wrapper"]/div/div[1]/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div[2]/div/div/div[2]/div[1]/img')
        time.sleep(2)
        self.click(
            '//*[@id="pro-form-wrapper"]/div/div[1]/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div[2]/div/div/div[3]/div[1]/img')
        self.assert_element('//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div')

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_CommodityPicture(self):
        """ 进入首页-点击找达人-点击达人智能推荐-商品推荐达人下—推荐商品图片 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[2]')
        time.sleep(2)
        img = self.find_element(
            '//*[@id="pro-form-wrapper"]/div/div[1]/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div[2]/div/div/div[1]/div[1]/img')
        # 校验 width 和 height
        assert img.get_attribute("width") == "64", "宽度不正确"
        assert img.get_attribute("height") == "64", "高度不正确"
        assert img.get_attribute('src') != "none" or ''

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_CommodityRecommend(self):
        """ 进入首页-点击找达人-点击达人智能推荐-商品推荐达人tab-点击商品"""
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[2]')
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div[2]/div/div/div[1]')
        time.sleep(2)
        self.assert_element('//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div')

    @pytest.mark.p1
    def test_Recommended_EverydayPerson_SimilaritySearch(self):
        """ 进入首页-点击找达人-点击达人智能推荐-相似推荐达人下—搜索相似达人 """
        self.click("//li[3]//ul[1]//li[2]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="root"]/div/div[2]/div/div[3]/div/div/div[1]')
        time.sleep(2)
        self.type("//input[@role='combobox']",2359894008)
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-daren-match-pc-select-item-option-content']//div[1]")
        time.sleep(2)
        self.assert_element('//*[@id="root"]/div/div[4]/div/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div')


