import pytest

from test_case.distribution.distribution_base_case import *

"""
找达人
-达人广场
pytest test_case/distribution/merchant/find_talent/test_distribution_find_talent.py --headless -n=5
"""

class TestFindATalent(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)

    @pytest.mark.p1
    def test_find_a_talent_page(self):
        """ 成功进入首页-点击找达人-点击达人广场-判断是否进入达人广场页面 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.assert_element("//div[@class='kwaishop-cps-daren-match-pc-pro-title-title']")

    def test_selection_box(self):
        """ 成功进入首页-点击找达人-点击达人广场-判断是否存在搜索框"""
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.assert_text("请输入达人ID或达人名称","div[class='kwaishop-cps-daren-match-pc-select kwaishop-cps-daren-match-pc-select-lg "
                                                  "kwaishop-cps-daren-match-pc-select-single kwaishop-cps-daren-match-pc-select-allow-clear "
                                                  "kwaishop-cps-daren-match-pc-select-show-arrow kwaishop-cps-daren-match-pc-select-show-search'] "
                                                  "div[class='kwaishop-cps-daren-match-pc-select-selector']")

    @pytest.mark.p1
    def test_live_talent_square(self):
        """ 成功进入达人广场-点击直播达人-判断是否进入直播达人广场页面 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        self.assert_text("30日直播场均看播人数","(//div[contains(text(),'30日直播场均看播人数')])[1]")
        self.assert_text("30日直播场均销售额(元)","(//div[contains(text(),'30日直播场均销售额(元)')])[1]")

    @pytest.mark.p1
    def test_short_video_master_square(self):
        """ 成功进入达人广场-点击短视频达人-判断是否进入短视频达人广场页面 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.assert_text("短视频平均销售额(元)","(//div[contains(text(),'短视频平均销售额(元)')])[1]")
        self.assert_text("短视频平均播放量","(//div[contains(text(),'短视频平均播放量')])[2]")

    @pytest.mark.p1
    def test_short_all_video_master_square(self):
        """ 成功进入达人广场-点击短视频达人-判断是否进入短视频达人广场页面 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(3)
        self.click("(//div[@class='videoWrap___A9L9g'])[1]")


    @pytest.mark.p1
    def test_square_page_turning(self):
        """ 成功进入达人广场-点击直播达人-向下滑动页面点击翻页，判断翻页功能是否异常 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        self.scroll_down(3)
        self.click("li[title='2'] a[rel='nofollow']")
        time.sleep(3)
        self.click("li[title='3'] a[rel='nofollow']")
        time.sleep(2)
        self.click("li[title='4'] a[rel='nofollow']")
        time.sleep(1)
        self.click("li[title='5'] a[rel='nofollow']")

    @pytest.mark.p1
    def test_response_tag(self):
        """ 成功进入达人广场-点击直播达人-向下滑动页面点击"招商中达人"，判断达人列表是否有火热招商中标签 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        self.scroll_down(3)
        self.click("(//div[@title='招商中达人'])[1]")
        time.sleep(2)
        self.assert_text("火热招商中", "(//div[contains(text(),'火热招商中')])[1]")

    @pytest.mark.p1
    def test_live_talent_category_screening(self):
        """ 成功进入达人广场-点击直播达人-经营类目-点击个护清洁，判断达人列表类目是否包含个护清洁 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-daren-match-pc-pro-field-label-light-labelLeft'][contains(text(),'个护清洁')]")
        time.sleep(2)
        self.click("//li[@title='全部']//div[1]")
        time.sleep(2)
        # 筛选器是否有个护清洁
        self.assert_text("个护清洁",".kwaishop-cps-daren-match-pc-pro-tagForm-result-item-label-left")
        # 达人列表的带货类目是否包含个护清洁
        self.assert_text("个护清洁","(//div[@class='kwaishop-cps-daren-match-pc-pro-list-row-wrapper listItem'])[1]")

    @pytest.mark.p1
    def test_every_master(self):
        """ 成功进入达人广场-点击直播达人-校验本页每条达人中的查看详情按钮是否可以点击 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        n = 2
        # 检验本页10个达人的查看详情按钮
        for i in range(10):
            btn = self.find_element("(//span[contains(text(),'查看详情')])[1]".format(n))
            flag = btn.is_enabled()
            assert flag == True
            n += 3

    @pytest.mark.p1
    def test_send_an_invitation(self):
        """
        成功进入达人广场-点击直播达人-校验本页每条达人中的发送邀请按钮是否可以点击
        """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        self.click("(//div[@id='rc-tabs-0-tab-1'])[1]")
        flag = False
        n = 3
        while not flag:
            # 当前页没有可以邀约的达人，点下一页
            if n == 33:
                n = 3
                self.click("//li[@title='下一页']//button[@type='button']")
            btn = self.find_element("(//button[@type='button'])[{}]".format(n))
            # 直到找到可以发送邀请的达人
            if btn.is_enabled():
                flag = True
                break
            n += 3

    # @pytest.mark.p1
    # def test_intelligent_recommendation(self):
    #     """
    #     成功进入达人广场-点击直播达人-点击商品维度-判断是否切换到该维度筛选
    #     """
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     self.click('//span[contains(text(),"直播达人")]')
    #     time.sleep(2)
    #     self.click('//div[@class="index-module__header--JnEFz"]//div//div[@class="cps-ant-tabs-tab"]')
    #
    #     self.assert_text("类目-价格带", '.index-module__filterName--uFpK2.index-module__catePriceTitle--Zg26G')

    # @pytest.mark.p1
    # def test_talent_square_live_delivery_talent(self):
    #     """ 点击“直播我的商品”、“上架我的商品” """
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(3)
    #     self.click("//div[contains(text(),'个达人正在直播售卖我的商品')]")
    #     self.click("//body/div/div/div/div/div/div/div/img[1]")
    #     self.click("//div[contains(text(),'个达人已上架我的商品')]")
    #     self.click("//body/div/div/div/div/div/div/div/img[1]")

    # @pytest.mark.p1
    # def test_talent_square_live_excellent_talent_recommendation(self):
    #     """ 达人广场-直播优秀达人推荐 直播优秀4个达人Card """
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(3)
    #     self.find_element(".index-module__goodPromoters--PTgS7")

    @pytest.mark.p0
    def test_search_talent_id(self):
        """ 达人广场-搜索达人ID """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.type("#rc_select_0", "2359894008")
        time.sleep(3)
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-select-item kwaishop-cps-daren-match-pc-select-item-option'])[1]")
        self.assert_url("https://cps.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId=2359894008")

    @pytest.mark.p0
    def test_search_talent_name(self):
        """ 达人广场-搜索达人名称"""
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.type("#rc_select_0", "铜锣湾扛把子自动化")
        time.sleep(3)
        self.click(
            "(//div[@class='kwaishop-cps-daren-match-pc-select-item kwaishop-cps-daren-match-pc-select-item-option'])[1]")
        self.assert_url("https://cps.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId=2359894008")

    @pytest.mark.p1
    def test_daren_plaza_daren_main_categories(self):
        """ 达人广场-达人主营类目 经营类目 是否存在"""
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.assert_element("//div[contains(text(),'食品饮料')]")
        self.assert_element("//div[contains(text(),'家居百货')]")
        self.assert_element("//div[contains(text(),'女装女鞋')]")

    @pytest.mark.p1
    def test_daren_plaza_daren_main_category_screening(self):
        """ 达人广场-达人主营类目筛选 类目按钮点击 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.scroll_down(2)
        # 循环点击1到12的元素,即12个经营类目
        for i in range(1, 12):
            # 构建相对XPath选择器
            xpath = (
                f"(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default "
                f"kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[{i}]")
            self.click(xpath)
            time.sleep(2)

    @pytest.mark.p1
    def test_master_main_categories_cargo_data(self):
        """
        达人主营类目-带货数据
        合作商家数、三十日场均观看人数、场均销售额、商品均价、千次曝光成交金额
        """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[13]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[14]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[15]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[16]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[17]")

    @pytest.mark.p1
    def test_master_main_categories_master_attributes(self):
        """
        达人主营类目-达人信息
        达人地域、达人性别、粉丝年龄、粉丝性别、粉丝城市划分
        """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[18]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[19]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[20]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[21]")
        self.is_element_enabled("(//div[@class='kwaishop-cps-daren-match-pc-pro-checkableTag kwaishop-cps-daren-match-pc-pro-checkableTag__default kwaishop-cps-daren-match-pc-pro-checkableTag__middle kwaishop-cps-daren-match-pc-pro-field-label-light__noBorder'])[22]")

    @pytest.mark.p1
    def test_master_main_content_labels(self):
        """ 达人主营类目-内容标签 内容标签点击 """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.is_element_enabled("(//div[@title='三农'])[1]")
        self.is_element_enabled("(//div[@title='二次元'])[1]")
        self.is_element_enabled("(//div[@title='亲子'])[1]")
        self.is_element_enabled("(//div[@title='随手拍'])[1]")

    @pytest.mark.p1
    def test_master_main_coop_info(self):
        """
        达人主营类目-合作信息
        有联系方式 无坑位费 招商中达人 达人开启邀约
        """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.is_element_enabled("(//div[@title='有联系方式'])[1]")
        self.is_element_enabled("(//div[@title='无坑位费'])[1]")
        self.is_element_enabled("(//div[@title='招商中达人'])[1]")
        self.is_element_enabled("(//div[@title='达人开启邀约'])[1]")

    # @pytest.mark.p1
    # def test_master_main_content_match(self):
    #     """ 匹配方式切换 """
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(3)
    #     # 切换匹配方式
    #     self.click("(//span[contains(text(),'按照店铺商品匹配')])[1]")
    #     time.sleep(1)
    #     self.find_element(".index-module__historySeller--JSsD2")

    # @pytest.mark.p1
    # def test_master_main_content_fans_appearance(self):
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(3)
    #     # 判断元素存在
    #     self.assert_element("(//div[@class='index-module__mainMore--Vtq3n'])[1]")
    #     # 点击收起
    #     self.click("//div[@class='kpro-expandcollapseExpandBtn']")
    #     time.sleep(1)
    #     self.assert_text("展开更多", "//div[@class='kpro-expandcollapseExpandBtn']")
    #     self.click("//div[@class='kpro-expandcollapseExpandBtn']")
    #     time.sleep(1)
    #     self.assert_element("(//div[@class='index-module__mainMore--Vtq3n'])[1]")
    #     self.assert_text("收起更多", "//div[@class='kpro-expandcollapseExpandBtn']")

    # @pytest.mark.p1
    # def test_goods_main_content_prices(self):
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(1)
    #     self.click(
    #         '/html/body/div[1]/div/div/div/div[3]/div[2]/div/div/div[2]/div/div/section/main/div/div/div[1]/div/div[2]/div/div[1]/div[4]/div[1]/div[1]/div/div[1]/div[1]/div/div[2]/div')
    #     time.sleep(3)
    #     self.assert_element("//span[contains(text(),'类目-价格带')]")
    #     self.assert_element("//span[contains(text(),'30日成交金额')]")
    #     self.assert_element("//span[contains(text(),'30日成交订单')]")

    # @pytest.mark.p1
    # def test_goods_main_content_promoter(self):
    #     """ 达人地域、是否有联系方式、是否有坑位费、达人性别、粉丝数量 """
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(1)
    #     self.click(
    #         '/html/body/div[1]/div/div/div/div[3]/div[2]/div/div/div[2]/div/div/section/main/div/div/div[1]/div/div[2]/div/div[1]/div[4]/div[1]/div[1]/div/div[1]/div[1]/div/div[2]/div')
    #     time.sleep(3)
    #     self.assert_element("(//span[@class='cps-ant-select-selection-placeholder'][contains(text(),'达人地域')])[2]")
    #     self.assert_element(
    #         "(//span[@class='cps-ant-select-selection-placeholder'][contains(text(),'是否有联系方式')])[2]")
    #     self.assert_element(
    #         "(//span[@class='cps-ant-select-selection-placeholder'][contains(text(),'是否有坑位费')])[2]")
    #     self.assert_element("(//span[@class='cps-ant-select-selection-placeholder'][contains(text(),'达人性别')])[2]")
    #     self.assert_element("(//span[@class='cps-ant-select-selection-placeholder'][contains(text(),'粉丝数量')])[2]")

    # @pytest.mark.p1
    # def test_goods_main_content_labels(self):
    #     self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
    #     time.sleep(1)
    #     self.click(
    #         '/html/body/div[1]/div/div/div/div[3]/div[2]/div/div/div[2]/div/div/section/main/div/div/div[1]/div/div[2]/div/div[1]/div[4]/div[1]/div[1]/div/div[1]/div[1]/div/div[2]/div')
    #     time.sleep(3)
    #     self.click(
    #         "(//span[@class='cps-ant-tag cps-ant-tag-checkable index-module__item--iwLHE'][contains(text(),'随手拍')])[2]")
    #     self.assert_element("//span[@class='cps-ant-tag cps-ant-tag-middle']")
    #     time.sleep(2)
    #     self.click(
    #         "(//span[@class='cps-ant-tag cps-ant-tag-checkable index-module__item--iwLHE'][contains(text(),'情感')])[2]")
    #     self.assert_text("情感", "//span[@class='cps-ant-tag cps-ant-tag-middle']")
    #     time.sleep(2)
    #     self.click(
    #         "(//span[@class='cps-ant-tag cps-ant-tag-checkable index-module__item--iwLHE'][contains(text(),'运动')])[2]")
    #     self.assert_text("运动", "//span[@class='cps-ant-tag cps-ant-tag-middle']")
    #     time.sleep(2)


    @pytest.mark.p1
    def test_Short_video_switch_live(self):
        """
        达人广场 - 达人视频卡片
        是否弹出达人卡片；点击查看下一个达人
        """
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.click("img[width='80'][height='142']")
        # 主营类目
        self.find_element("(//div[@class='detailLabel___AXxP1'])[1]")
        time.sleep(2)
        self.find_element("//div[contains(text(),'直播片段')]")
        self.click("//div[contains(text(),'直播片段')]")
        time.sleep(2)

    @pytest.mark.p0
    def test_expert_square_expert_list(self):
        """达人广场-达人列表;达人列表是否可以点击进入达人详情页"""
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        element = self.find_element("body div[id='main_root'] div[class='kwaishop-cps-daren-match-pc-spin-container'] "
                                    "div div div:nth-child(1) div:nth-child(1) div:nth-child(1) div:nth-child(1) div:nth-child(1) "
                                    "div:nth-child(2) div:nth-child(1) div:nth-child(2) div:nth-child(2) div:nth-child(1) div:nth-child(2)")
        # 获取达人ID
        promoterId = element.text
        url = f"https://cps.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId={promoterId}"
        self.click("(//button[@type='button'])[2]")
        self.assert_url(url=url)
        time.sleep(3)
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[1]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[2]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[3]")
        self.find_element("//*[@id='rc-tabs-0-panel-situation']/div/div[1]/div[1]/div[4]")

    @pytest.mark.p1
    def test_expert_square_expert_video_card(self):
        """达人广场 - 达人视频卡片;是否弹出达人卡片；点击查看下一个达人"""
        self.click('//*[@id="menu-BAdGHi1s8Cg"]/span/span/span/span')
        time.sleep(3)
        self.click("img[width='80'][height='142']")
        # 主营类目
        self.find_element("(//div[@class='detailLabel___AXxP1'])[1]")
        self.find_element("//span[contains(text(),'查看下一个达人')]")
        self.find_element("//span[contains(text(),'查看上一个达人')]")