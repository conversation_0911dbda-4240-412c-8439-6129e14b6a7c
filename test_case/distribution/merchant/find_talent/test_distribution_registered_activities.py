import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
找达人
达人广场-已报名活动页面
python3 -m pytest test_case/distribution/merchant/find_talent/test_distribution_registered_activities.py --headless -n=3
"""


class TestRegisteredActivityPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')

    @pytest.mark.p1
    def test_registered_activity_page(self):
        """判断是否进入已报名活动页面"""
        self.click("(//span[@class='cps-main-menu-title-content'])[14]")
        time.sleep(2)
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")
        self.assert_element('button[type="submit"] span')
        self.assert_element('button[class="cps-ant-btn"]')

    @pytest.mark.p1
    def test_registered_activity_page_limit(self):
        """
        达人广场-是否进入已报名活动页面
        """
        self.click("(//span[@class='cps-main-menu-title-content'])[14]")
        time.sleep(2)
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")
        self.sleep(3)
        self.assert_element("//thead[@class='cps-ant-table-thead']//tr")
        self.scroll_down(3)
        self.is_element_enabled("//ul[@class='cps-ant-pagination index-module__pagi--QGnNP']//a[@rel='nofollow'][normalize-space()='2']")
        self.is_element_enabled("//ul[@class='cps-ant-pagination index-module__pagi--QGnNP']//a[@rel='nofollow'][normalize-space()='3']")

