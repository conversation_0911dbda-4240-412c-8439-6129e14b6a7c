import pytest

from test_case.distribution.distribution_base_case import *

"""
找达人
-我的达人
pytest test_case/distribution/merchant/find_talent/test_distribution_my_promoter.py --headless -n=5
"""

class TestMyPromoter(DistributionToolTest):
    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)

    @pytest.mark.p1
    def test_my_promoter_page(self):
        """ 成功进入首页-点击找达人-我的达人-判断是否进入我的达人页面"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.assert_text("我的达人",".kwaishop-cps-daren-match-pc-pro-title-title")

    @pytest.mark.p1
    def test_live_promoter_jump(self):
        """ 点击正在直播带货的达人"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("//span[contains(text(),'个达人正在直播带货我的商品')]")
        time.sleep(3)
        # 判断当前选中的是带货中
        tab = self.find_element(".kwaishop-cps-daren-match-pc-tabs-tab.kwaishop-cps-daren-match-pc-tabs-tab-active")
        assert tab.text == "带货中"
        # 判断当前选中的是直播达人
        sub_tab = self.find_element(
            ".kwaishop-cps-daren-match-pc-radio-button-wrapper.kwaishop-cps-daren-match-pc-radio-button-wrapper-checked")
        assert sub_tab.text == "直播带货"

    @pytest.mark.p1
    def test_video_promoter_jump(self):
        """ 点击正在短视频带货的达人"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("//span[contains(text(),'个达人正在短视频推广我的商品')]")
        time.sleep(3)
        # 判断当前选中的是带货中
        tab = self.find_element(".kwaishop-cps-daren-match-pc-tabs-tab.kwaishop-cps-daren-match-pc-tabs-tab-active")
        assert tab.text == "带货中"
        # 判断当前选中的是短视频达人
        sub_tab = self.find_element(
            ".kwaishop-cps-daren-match-pc-radio-button-wrapper.kwaishop-cps-daren-match-pc-radio-button-wrapper-checked")
        assert sub_tab.text == "短视频带货"

    @pytest.mark.p1
    def test_shelf_promoter_jump(self):
        """ 点击正在橱窗带货的达人"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("//span[contains(text(),'个达人已上架我的商品')]")
        time.sleep(3)
        # 判断当前选中的是带货中
        tab = self.find_element(".kwaishop-cps-daren-match-pc-tabs-tab.kwaishop-cps-daren-match-pc-tabs-tab-active")
        assert tab.text == "带货中"
        # 判断当前选中的是上架达人
        sub_tab = self.find_element(
            ".kwaishop-cps-daren-match-pc-radio-button-wrapper.kwaishop-cps-daren-match-pc-radio-button-wrapper-checked")
        assert sub_tab.text == "橱窗带货"

    @pytest.mark.p1
    def test_live_promoter_data(self):
        """ 直播达人列表"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(2)
        # 展示直播相关指标
        self.assert_element("(//div[@class='itemName___p3NC9'][contains(text(),'30日直播场均看播人数')])[1]")
        self.assert_element("(//div[@class='itemName___p3NC9'][contains(text(),'30日直播场均销售额(元)')])[1]")

    @pytest.mark.p1
    def test_video_promoter_data(self):
        """ 短视频达人列表"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'短视频达人')])[1]")
        time.sleep(2)
        # 展示短视频相关指标
        self.assert_element("(//div[@class='itemName___p3NC9'][contains(text(),'短视频平均销售额(元)')])[1]")
        self.assert_element("(//div[@class='itemName___p3NC9'][contains(text(),'短视频平均播放量')])[1]")

    @pytest.mark.p1
    def test_view_promoter_info(self):
        """ 收藏达人-查看达人详情"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        # 获取达人列表第一个达人的ID
        promoter_id = self.find_element("(//div[@class='promoterInfo___ux0g3'])[1]").text.split("\n")[2]
        time.sleep(2)
        self.click("(//span[contains(text(),'查看达人详情')])[1]")
        self.assert_url(f"https://cps.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId={promoter_id}")

    @pytest.mark.p1
    def test_search_my_promoter_by_id(self):
        """ 我的达人-搜索框使用"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        # 收藏达人搜索框
        self.type("#rc_select_0","2428997147")
        time.sleep(2)
        self.assert_text("2428997147","//div[@class='kwaishop-cps-daren-match-pc-pro-form-layout-wrapper']//div[3]")
        self.click("//div[@class='kwaishop-cps-daren-match-pc-select-item kwaishop-cps-daren-match-pc-select-item-option']")
        time.sleep(2)
        # 邀约中搜索框
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        self.type("#rc_select_2", "2428997147")
        time.sleep(2)
        self.assert_text("2428997147", "//div[@class='kwaishop-cps-daren-match-pc-pro-form-layout-wrapper']//div[3]")
        self.click("//div[@class='kwaishop-cps-daren-match-pc-select-item kwaishop-cps-daren-match-pc-select-item-option']")
        time.sleep(2)
        # 带货中搜索框
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.type("#rc_select_3", "2428997147")
        time.sleep(2)
        self.assert_text("2428997147", "//div[@class='kwaishop-cps-daren-match-pc-pro-form-layout-wrapper']//div[3]")
        self.click("//div[@class='kwaishop-cps-daren-match-pc-select-item kwaishop-cps-daren-match-pc-select-item-option']")

    @pytest.mark.p1
    def test_my_promoter_invited(self):
        """ 我的达人页面-点击邀约中tab"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("//span[contains(text(),'邀约中')]")
        # 列表数据是否正常
        self.assert_element("//th[contains(text(),'带货达人')]")
        self.assert_element("//th[contains(text(),'最高佣金')]")
        self.assert_element("//th[contains(text(),'创建时间')]")
        self.assert_element("//th[contains(text(),'达人反馈')]")
        self.assert_element("//th[contains(text(),'操作')]")
        time.sleep(2)
        self.click("(//label[@class='kwaishop-cps-daren-match-pc-radio-button-wrapper'])[1]")
        self.assert_element("//th[contains(text(),'备注')]")

    @pytest.mark.p1
    def test_view_invited_promoter_info(self):
        """ 邀约中-查看达人详情"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("//span[contains(text(),'邀约中')]")
        # 获取达人列表第一个达人的ID
        promoter_id = self.find_element("tbody tr:nth-child(1) td:nth-child(1) div:nth-child(1) div:nth-child(2) div:nth-child(2)").text.split(" ")[1]
        time.sleep(2)
        self.click("(//span[contains(text(),'查看达人详情')])[1]")
        self.assert_url(f"https://cps.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId={promoter_id}")

    @pytest.mark.p1
    def test_view_invitation_info(self):
        """ 邀约中-查看邀约详情"""
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("//span[contains(text(),'邀约中')]")
        # 获取达人ID
        promoter_id_1 = self.find_element("tbody tr:nth-child(1) td:nth-child(1) div:nth-child(1) div:nth-child(2) div:nth-child(2)").text.split(" ")[1]
        time.sleep(2)
        self.click("(//span[contains(text(),'查看邀约详情')])[1]")
        time.sleep(2)
        # 获取邀约详情页里的达人ID
        n = len(self.find_element(".location-info").text.split(" "))
        promoter_id_2 = self.find_element(".location-info").text.split(" ")[n - 1]
        assert promoter_id_1 == promoter_id_2

    def test_live_promoter(self):
        pass

    @pytest.mark.p1
    def test_video_promoter(self):
        """ 带货中-短视频带货"""
        driver = self.get_new_driver()
        self.driver.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("//span[contains(text(),'短视频带货')]")
        time.sleep(2)
        # 展示个性模块——短视频推广中的商品
        self.assert_text("短视频推广中的商品","//div[@class='desc___YPpPL']")
        # 展示相关指标
        self.assert_element("(//div[contains(text(),'30日短视频成交订单数')])[1]")
        self.assert_element("(//div[contains(text(),'30日短视频成交金额')])[1]")

    @pytest.mark.p1
    def test_shelf_promoter(self):
        """ 带货中-橱窗带货"""
        self.get_new_driver()
        self.driver.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'liuxiaohui07')
        self.click("(//li[@id='menu-nAEAYen4vuk'])[1]")
        time.sleep(3)
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("//span[contains(text(),'橱窗带货')]")
        time.sleep(2)
        # 展示个性模块——上架中的商品
        self.assert_text("上架中的商品","(//div[@class='desc___YPpPL'])[1]")
        # 展示相关指标
        element = self.find_element("(//td[@class='kwaishop-cps-daren-match-pc-table-cell'])[2]")
        assert "30日货架成交订单数" in element.text and "30日货架成交金额" in element.text


