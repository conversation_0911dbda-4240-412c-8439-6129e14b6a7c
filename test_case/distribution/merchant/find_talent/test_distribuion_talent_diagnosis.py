import pytest

from test_case.distribution.distribution_base_case import *


"""
找达人-达人建联诊断

"""

class TestInfluencerConnectionDiagnosis(DistributionToolTest):
    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        """进入达人建联诊断页面"""
        self.click("//*[@id='menu-Bjurj6e7gY8']/span/span/span/span")
        time.sleep(3)

    def test_influencer_connection_diagnosis(self):
        """ 进入首页-点击找达人-点击达人建联诊断-判断是否进入达人建联诊断页面 """
        self.assert_text("在线沟通", "(//div[@id='rc-tabs-0-tab-IMConnection'])[1]")
        self.assert_text("发起建联人数", "(//div[contains(text(),'发起建联人数')])[1]")
        self.assert_text("建联成功达人数", "(//div[contains(text(),'建联成功达人数')])[1]")
        self.assert_text("带货达人数", "(//div[contains(text(),'带货达人数')])[1]")
        self.assert_text("动销达人数", "(//div[contains(text(),'动销达人数')])[1]")



    def test_influencer_connection_diagnosis_time_period_switching(self):
        """ 达人建联诊断页面-时间周期切换 """
        self.click("(//span[contains(text(),'昨天')])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'最近30天')])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'最近7天')])[1]")
        time.sleep(3)

    def test_influencer_connection_diagnosis_convert_link_diagnosis_tab_switching(self):
        """ 达人建联诊断页面-转化链路诊断tab切换 """
        self.click("(//div[@id='rc-tabs-0-tab-ProductInvitation'])[1]")
        time.sleep(3)
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-tabs-tab'])[2]")
        time.sleep(3)
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-tabs-tab'])[1]")
        time.sleep(3)

    def test_influencer_connection_diagnosis_more_recommendations_from_experts(self):
        """ 达人建联诊断页面-点击更多达人推荐-跳转达人智能推荐页面 """
        self.click("(//a[@href='https://cps.kwaixiaodian.com/zone/daren-match/daren-recommend'])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        self.assert_text("直播达人", "(//div[@title='直播达人'])[1]")
