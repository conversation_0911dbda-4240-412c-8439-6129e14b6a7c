import pytest

from test_case.distribution.distribution_base_case import *

"""
计划管理
计划管理-阶梯计划
pytest test_case/distribution/merchant/plan_management/test_mall_hosting_plan.py --headless -n=3
"""


class TestMallHostingPlan(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    @pytest.mark.skip
    def test_click_on_mall_management_menu(self):
        """ 点击菜单「商城托管计划」 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        self.assert_element('//div[@class="kwaishop-cps-merchant-plan-pc-pro-page iFpzIpgojFRgSfrzUrfp"]')
        self.assert_element('//div[@class="kwaishop-cps-merchant-plan-pc-pro-page-item"]')
        self.assert_element('//thead[@class="kwaishop-cps-merchant-plan-pc-table-thead"]')

    @pytest.mark.p1
    def test_tab_switch(self):
        """ Tab切换「推广商品&已退出商品」 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        """ Tab切换 """
        self.click('//div[@aria-selected="false"]')
        self.assert_element('//th[contains(text(),"关闭时间")]')
        self.click('//div[@role="tab"][contains(text(),"推广商品")]')
        self.assert_element('//div[contains(text(),"7日曝光量")]')

    @pytest.mark.p1
    def test_view_detailed_data_click(self):
        """ 「查看明细数据」点击 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        self.is_element_enabled('//a[contains(text(),"查看明细数据")]')
        self.click('//a[contains(text(),"查看明细数据")]')
        self.assert_url('https://syt.kwaixiaodian.com/zones/data-goods-card-manage/overview')

    @pytest.mark.p1
    @pytest.mark.skip
    def test_expand_and_collapse(self):
        """ 商城托管计划 展开和收起 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        time.sleep(1)
        self.click('//div[@class="hJgsnbXDBMlZaYoQJfRe"]')
        time.sleep(1)
        self.assert_element('//a[contains(text(),"了解更多 >>")]')
        time.sleep(1)
        self.click('//span[contains(text(),"收起")]')

    @pytest.mark.p1
    @pytest.mark.skip
    def test_custody_plan_list_pagination(self):
        """ 翻页 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        self.click('//div[@class="hJgsnbXDBMlZaYoQJfRe"]')
        self.click('//a[normalize-space()="2"]')
        self.click('//a[normalize-space()="3"]')
        time.sleep(3)
        self.click('//a[normalize-space()="5"]')

    @pytest.mark.p1
    def test_id_query(self):
        """ 「商品ID」查询 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        time.sleep(2)
        self.click("//input[@placeholder='请输入商品ID']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入商品ID']", '121212')
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_text("暂无数据", "//div[@class='kwaishop-cps-merchant-plan-pc-empty-description']")

    @pytest.mark.p1
    def test_commodity_name_search(self):
        """ 「商品名称」查询 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//span[contains(text(),"商城推广计划")]')
        time.sleep(2)
        self.click("//input[@placeholder='请输入商品名称']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入商品名称']", '测试')
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_text("测试",
                         '//*[@id="root"]/section/main/div/div[4]/div[4]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div/div/a')
