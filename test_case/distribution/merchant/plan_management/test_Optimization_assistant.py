import os

import pytest

from test_case.distribution.distribution_base_case import *
"""
计划管理
计划管理-优化助手
python3 -m pytest test_case/distribution/merchant/plan_management/test_store_orientation.py --headless -n=3
"""


class TestShopOrientationPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(2)


    @pytest.mark.p1
    def test_shop_Orientation_Page(self):
        """进入首页-点击计划管理-点击商品优化助手-判断是否进入商品优化助手页面"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.assert_element("//div[@class='kwaishop-cps-merchant-plan-pc-breadcrumb']")
        self.assert_element("//div[@class='kwaishop-cps-merchant-plan-pc-pro-title']")
        self.sleep(2)


    @pytest.mark.p1
    def test_shop_Propaganda_Module(self):
        """进入首页-点击计划管理-点击商品优化助手-判断宣导模块是否存在"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click("//a[contains(text(),'展开')]")
        self.sleep(2)
        # 判断组件元素出现
        img_xpath = ['//*[@id="root"]/section/main/div[2]']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("outerHTML")
            assert "description___ydAaK" in outer_html, "缺少 description___ydAaK 属性"
        time.sleep(2)

    def test_shop_PropagandaModule_Picture(self):
        """进入首页-点击计划管理-点击商品优化助手-判断宣导模块图片展示是否正常"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click("//a[contains(text(),'展开')]")
        self.sleep(2)
        # 判断宣导图片，第一张图
        img_xpath = ['//*[@id="root"]/section/main/div[2]/div[2]/div[1]/div[3]/div/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            # 校验 width 和 height
            assert img.get_attribute("width") == "300", "宽度不正确"
            assert img.get_attribute("height") == "180", "高度不正确"
            assert img.get_attribute('src') != "none" or ''
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "kwaishop-cps-merchant-plan-pc-image-img" in outer_html, "缺少kwaishop-cps-merchant-plan-pc-image-img 属性"

    def test_shop_PropagandaModule_Pictureone(self):
        """进入首页-点击计划管理-点击商品优化助手-判断宣导模块图片展示是否正常"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click("//a[contains(text(),'展开')]")
        self.sleep(2)
        # 判断宣导图片，第三张图
        img_xpath = ['//*[@id="root"]/section/main/div[2]/div[2]/div[2]/div[3]/div[2]/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            # 校验 width 和 height
            assert img.get_attribute("width") == "285", "宽度不正确"
            assert img.get_attribute("height") == "180", "高度不正确"
            assert img.get_attribute('src') != "none" or ''
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "kwaishop-cps-merchant-plan-pc-image-img" in outer_html, "缺少kwaishop-cps-merchant-plan-pc-image-img 属性"
    def test_shop_PropagandaModule_Picturetwo(self):
        """进入首页-点击计划管理-点击商品优化助手-判断宣导模块图片展示是否正常"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click("//a[contains(text(),'展开')]")
        self.sleep(2)
        # 判断宣导图片，第二张图
        img_xpath = ['//*[@id="root"]/section/main/div[2]/div[2]/div[2]/div[3]/div[1]/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            # 校验 width 和 height
            assert img.get_attribute("width") == "288", "宽度不正确"
            assert img.get_attribute("height") == "180", "高度不正确"
            assert img.get_attribute('src') != "none" or ''
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "kwaishop-cps-merchant-plan-pc-image-img" in outer_html, "缺少kwaishop-cps-merchant-plan-pc-image-img 属性"

    def test_shop_PropagandaModule_DataBoard(self):
        """进入首页-点击计划管理-点击商品优化助手-判断数据看板模块"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        time.sleep(2)
        # 判断字段是否正常
        self.assert_text("待优化商品数", "//div[contains(text(),'待优化商品数')]")
        self.assert_text("奖励中商品数", "//div[contains(text(),'奖励中商品数')]")
        self.assert_text("已奖励商品数", "//div[contains(text(),'已奖励商品数')]")
        self.assert_text("奖励期间总曝光数", "//div[contains(text(),'奖励期间总曝光数')]")
        self.assert_text("奖励期间总上架量", "//div[contains(text(),'奖励期间总上架量')]")

    def test_shop_PropagandaModule_checkone(self):
        """进入首页-点击计划管理-点击商品优化助手-点击去查看按钮-跳转到已优化商品tab下  奖励中列表内"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div[3]/div[2]/div[2]/div[3]/a')

    def test_shop_PropagandaModule_checktwo(self):
        """进入首页-点击计划管理-点击商品优化助手-点击去查看按钮-跳转到已优化商品tab下  已奖励列表内"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div[3]/div[2]/div[3]/div[3]/a')

    def test_shop_PropagandaModule_deoptimization(self):
        """进入首页-点击计划管理-点击商品优化助手-点击去查看按钮-跳转到已优化商品tab下  已奖励列表内"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div[3]/div[2]/div[3]/div[3]/a')
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div[3]/div[2]/div[1]/div[3]/a')

    def test_shop_PropagandaModule_optimizedinglist(self):
        """进入首页-点击计划管理-点击商品优化助手-待优化tab下字段"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        time.sleep(2)
        # 判断字段是否正常
        self.assert_text("商品信息", "//th[contains(text(),'商品信息')]")
        self.assert_text("单价", "//th[contains(text(),'单价')]")
        self.assert_text("推广佣金率", "//th[contains(text(),'推广佣金率')]")
        self.assert_text("推广计划", "//th[contains(text(),'推广计划')]")
        self.assert_text("同款参考", "//th[contains(text(),'同款参考')]")
        self.assert_text("优化建议", "//th[contains(text(),'优化建议')]")
        self.assert_text("操作", "//th[contains(text(),'操作')]")



    def test_shop_PropagandaModule_ProductPicture(self):
        """进入首页-点击计划管理-点击商品优化助手-点击已优化商品tab-判断列表内商品图片"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-yiyouhua"]')
        # 判断字段是否正常
        self.sleep(2)
        img_xpath = ["//tbody/tr[1]/td[1]/div[1]/div[1]/img[1]"]
        for xpath in img_xpath:
            img = self.find_element(xpath)
            # 校验 width 和 height
            assert img.get_attribute("width") == "48", "宽度不正确"
            assert img.get_attribute("height") == "48", "高度不正确"
            assert img.get_attribute('src') != "none" or ''

    def test_shop_PropagandaModule_optimizedingTab_switching(self):
        """进入首页-点击计划管理-点击商品优化助手-待优化商品tab下切换tab"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click("//span[contains(text(),'待优化佣金率')]")
        self.sleep(2)
        self.click("//span[contains(text(),'转成全量达人可带')]")
        self.sleep(2)

    def test_shop_PropagandaModule_optimizedTab_switching(self):
        """进入首页-点击计划管理-点击商品优化助手-已优化商品tab下切换tab"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-yiyouhua"]')
        self.sleep(2)
        self.click("//span[contains(text(),'待开始奖励')]")
        self.sleep(2)
        self.click("//div[@id='itemList']//div[3]//span[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'奖励中止')])[1]")
        self.sleep(2)
        self.click("//span[contains(text(),'已奖励')]")
        self.sleep(2)

    def test_shop_PropagandaModule_ViewdataTab(self):
        """进入首页-点击计划管理-点击商品优化助手-已优化商品tab-点击查看数据按钮"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-yiyouhua"]')
        self.sleep(2)
        self.click("//tbody/tr[1]/td[7]/a[1]")
        self.sleep(2)
        img_xpath = ["//div[@class='kwaishop-cps-merchant-plan-pc-modal-content']"]
        for xpath in img_xpath:
            img = self.find_element(xpath)
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "kwaishop-cps-merchant-plan-pc-modal-content" in outer_html, "缺少kwaishop-cps-merchant-plan-pc-modal-content 属性"

    def test_shop_PropagandaModule_Viewdatafields(self):
        """进入首页-点击计划管理-点击商品优化助手-已优化商品tab-点击查看数据按钮-弹窗字段"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-yiyouhua"]')
        self.sleep(2)
        self.click("//tbody/tr[1]/td[7]/a[1]")
        self.sleep(2)
        self.assert_text("商品曝光次数", "//span[contains(text(),'商品曝光次数')]")
        self.assert_text("商品上架次数", "//span[contains(text(),'商品上架次数')]")

    def test_shop_PropagandaModule_paging(self):
        """进入首页-点击计划管理-点击商品优化助手-已优化商品tab-分页"""
        self.click('//*[@id="menu-DCPD9gBTyj4"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-yiyouhua"]')
        self.sleep(2)
        self.click("//a[normalize-space()='2']")
        self.click("//a[normalize-space()='3']")
        self.click("//a[normalize-space()='4']")
