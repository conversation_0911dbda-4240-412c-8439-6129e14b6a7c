import os
import random
import time

import pytest
from seleniumbase.common.exceptions import NoSuchElementException

from test_case.distribution.distribution_base_case import DistributionToolTest

# 生成 30 到 90 之间的随机整数
random_number = random.randint(30, 90)
# 从指定文件中 随机取一个达人ID 输入并确认
script_directory = os.path.dirname(os.path.abspath(__file__))
relative_path = "./ShopOrientationPage.txt"
file_path = os.path.join(script_directory, relative_path)

"""
计划管理
计划管理-商品定向
pytest test_case/distribution/merchant/plan_management/test_commodity_orientation.py --headless -n=3
"""


@pytest.mark.skip
class TestDistributionGeneralPlan(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)

    @pytest.mark.p0
    def test_commodity_orientation_page(self):
        """进入首页-点击计划管理-点击商品定向-判断是否进入商品定向页面"""
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'商品定向')]")
        self.sleep(3)
        self.assert_element(
            "//button[@class='kwaishop-cps-merchant-plan-pc-btn kwaishop-cps-merchant-plan-pc-btn-primary']")
        self.assert_text("添加达人", "(//div[@class='NYS_kfUDg6XEc081qe5w'])[1]")

    @pytest.mark.p0
    def test_search_input_can_be_used(self):
        """进入首页-点击计划管理-点击商品定向-判断是否进入商品定向页面"""
        self.click("//*[@id='menu-isZ-CoLA9js']/span/span/span/span")
        self.input("//input[@placeholder='请输入商品ID']", "21743947551434")
        time.sleep(1)
        self.click("//button[@type='submit']")
        time.sleep(1)
        self.assert_text("21743947551434",
                         "//td[@class='kwaishop-cps-merchant-plan-pc-table-cell _5xh8DOo3bwytm8RhA4M']//span//span[1]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_add_product_orientation(self):
        """添加一个商品定向计划"""
        self.click("//*[@id='menu-isZ-CoLA9js']/span/span/span/span")
        time.sleep(0.5)
        self.click("//span[contains(text(),'添加商品定向')]")
        time.sleep(0.5)
        # 先跳转2页，避免对测试造成影响
        self.click("//li[@title='下一页']//button[@type='button']")
        self.click("//li[@title='下一页']//button[@type='button']")
        # 如果当前页没有可以添加商品定向的推广计划，则跳转到下一页添加商品定向计划
        count = 3
        isNow = self.product_orientation_is_available()
        while isNow is None and count > 0:
            count -= 1
            self.click("//li[@title='下一页']//button[@type='button']")
            time.sleep(2)
            isNow = self.product_orientation_is_available()
            if isNow is not None:
                break

        # 判断选择当前页面可以选择的元素进行勾选
        allElements = self.find_elements("span.kwaishop-cps-merchant-plan-pc-pro-field-status__text")
        # for index, element in enumerate(allElements):
        #     print(index)
        #     if element.text == "可推广":
        #         self.click(f"(//input[@class='kwaishop-cps-merchant-plan-pc-checkbox-input'])[19]")  # 目前无法找到元素
        #         time.sleep(0.5)
        #         break

        # 继续执行其他操作
        self.click("//span[contains(text(),'批量添加推广')]")
        time.sleep(1)
        self.type("(//div[@class='cps-materials-multi-input-container'])[1]", "2337072173")
        time.sleep(1)
        self.click("(//div[@class='cps-materials-multi-input-userSelect'])[1]")
        # 选择达人,输入佣金率 并 确认
        self.type("//input[@placeholder='请输入1-90的整数']", str(random_number))
        self.click("//span[contains(text(),'确 定')]")
        self.find_element("//span[contains(text(),'操作成功')]")

    @pytest.mark.p0
    def test_modify_commodity_orientation_commission(self):
        """ 修改定向商品的佣金率 """
        self.click("//*[@id='menu-isZ-CoLA9js']/span/span/span/span")
        self.type("//input[@placeholder='请输入商品ID']", '21743947551434')
        self.click("//button[@type='submit']")
        self.click("//*[@id='operationNode']/a[contains(text(),'编辑')]")
        self.type('//*[@id="commissionRateMap_2337072173"]', str(random_number))
        self.click("//span[contains(text(),'确 定')]")
        self.find_element("//span[contains(text(),'操作成功')]")

    @pytest.mark.p0
    def test_close_the_product_targeting_program(self):
        """关闭开启的商品定向计划"""
        self.click("//*[@id='menu-isZ-CoLA9js']/span/span/span/span")
        self.click("//*[@id='pro-form-wrapper']/div[2]/div[1]/div/div[2]/div/div/div")
        self.click("div[title='已开启']")
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(3)
        self.click("//tbody/tr[7]/td[9]/div[1]/a[2][contains(text(),'关闭推广')]")
        self.sleep(3)
        self.click("//span[contains(text(),'关闭推广')]")
        self.find_element("//span[contains(text(),'关闭成功')]")

    @pytest.mark.p0
    def test_add_targeting_promoter(self):
        """商品定向计划添加达人-校验弹窗是否正常弹出"""
        self.click("//*[@id='menu-isZ-CoLA9js']/span/span/span/span")
        self.click("//*[@id='pro-form-wrapper']/div[2]/div[1]/div/div[2]/div/div/div")
        self.click("div[title='已开启']")
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(3)
        self.click("(//a[contains(text(),'添加达人')])[1]")
        self.sleep(3)
        self.click("(//div[@class='kwaishop-cps-merchant-plan-pc-drawer-title'])[1]")

