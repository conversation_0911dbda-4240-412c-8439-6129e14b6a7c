import os

import pytest

from test_case.distribution.distribution_base_case import *

"""
计划管理
计划管理-店铺定向
python3 -m pytest test_case/distribution/merchant/plan_management/test_store_orientation.py --headless -n=3
"""

@pytest.mark.skip
class TestShopOrientationPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(2)

    @pytest.mark.p1
    def test_shop_orientation_page(self):
        """进入首页-点击计划管理-点击店铺定向-判断是否进入店铺定向页面"""
        self.click('//*[@id="menu-sg07k1_aqCM"]/span/span/span/span')
        self.assert_element("(//span[contains(text(),'添加店铺计划')])[1]")

    @pytest.mark.p1
    def test_store_creation_plan(self):
        """指向一个达人创建一个店铺定向"""
        self.click('//*[@id="menu-sg07k1_aqCM"]/span/span/span/span')
        self.click('//span[contains(text(),"添加店铺计划")]')
        script_directory = os.path.dirname(os.path.abspath(__file__))
        relative_path = "./ShopOrientationPage.txt"
        file_path = os.path.join(script_directory, relative_path)
        random_activity_id = self.get_random_activity_id(file_path)
        self.type("//div[@class='cps-materials-multi-input-container']", f"{random_activity_id}")
        time.sleep(2)
        self.type(
            "(//input[@id='commissionRate'])[1]",
            "36")
        self.click("(//span[contains(text(),'确 定')])[1]")
        self.find_element("(//span[contains(text(),'添加成功')])[1]")

    @pytest.mark.p1
    def test_store_closure_program(self):
        """ 关闭一个开启的店铺计划 """
        self.click('//*[@id="menu-sg07k1_aqCM"]/span/span/span/span')
        time.sleep(2)
        """ 选择开启的查询 """
        self.click("(//div[@class='kwaishop-cps-merchant-plan-pc-select-selector'])[1]")
        self.click('/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/section[1]/main[1]/div[3]/div[2]/div[1]/form[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]')
        self.click("(//span[contains(text(),'查 询')])[1]")
        """ 去关闭1个 """
        self.click("(//div[@class='kwaishop-cps-merchant-plan-pc-space-item'])[10]")
        self.click("(//span[contains(text(),'确 定')])[1]")
        """ 断言 关闭提示"""
        self.sleep(0.5)
        self.assert_element("(//span[contains(text(),'关闭成功')])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_store_delete_program(self):
        """ 删除一个店铺计划 """
        self.click('//*[@id="menu-sg07k1_aqCM"]/span/span/span/span')
        time.sleep(2)
        self.click("//tbody/tr/td[8]/div[1]/div[1]/div[2]/button[1]//span[contains(text(),'删除计划')]")
        self.sleep(3)
        self.click(
            "//body/div[@class='el-dialog__wrapper']/div[@aria-label='确认删除计划']/div[@class='el-dialog__footer']/div/button[2]")
        self.sleep(0.5)
        self.find_element("//p[@class='el-message__content']")
