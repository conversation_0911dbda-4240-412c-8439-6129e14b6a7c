import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
商家侧-样品管理
"""


class TestDistributionEffect(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_sample_delivery_management_audit(self):
        """0元申样-待审核tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')

    @pytest.mark.p0
    def test_sample_delivery_management_fahuo(self):
        """0元申样-待发货tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//div[contains(text(),'待发货')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')

    @pytest.mark.p0
    def test_sample_delivery_management_fahuoed(self):
        """0元申样-已发货tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//div[contains(text(),'已发货')])[1]")
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_sample_delivery_management_lvyue(self):
        """0元申样-待履约tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//div[contains(text(),'待履约')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')

    @pytest.mark.p0
    def test_sample_delivery_management_lvyueed(self):
        """0元申样-已完成tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//div[contains(text(),'已完成')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_sample_delivery_management_cancel(self):
        """0元申样-已取消tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//div[contains(text(),'已取消')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_sample_delivery_management_all(self):
        """0元申样-全部tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//div[contains(text(),'全部')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_buy_sample_delivery_management_audit(self):
        """买样后返-待审核tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')

    @pytest.mark.p0
    def test_buy_sample_delivery_management_fahuo(self):
        """买样后返-待发货tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.click("(//div[contains(text(),'待发货')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')

    @pytest.mark.p0
    def test_buy_sample_delivery_management_fahuoed(self):
        """买样后返-已发货tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.click("(//div[contains(text(),'已发货')])[1]")
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_buy_sample_delivery_management_fahuoed(self):
        """买样后返-待履约tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.click("(//div[contains(text(),'待履约')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')

    @pytest.mark.p0
    def test_buy_sample_delivery_management_lvyueed(self):
        """买样后返-已完成tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.click("(//div[contains(text(),'已完成')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_buy_sample_delivery_management_cancel(self):
        """买样后返-已取消tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.click("(//div[contains(text(),'已取消')])[1]")
        self.assert_text("0元申样", '//span[contains(text(),"0元申样")]')
        self.click("(//div[contains(text(),'详情')])[1]")

    @pytest.mark.p0
    def test_buy_ample_delivery_management_all(self):
        """买样后返-全部tab-页面是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        # 进入样品tab
        self.click('//*[@id="menu-YWSlqum1WCI"]/span/span/span/span')
        self.click("(//span[contains(text(),'买样后返')])[1]")
        self.click("(//div[contains(text(),'全部')])[1]")
        self.click("(//div[contains(text(),'详情')])[1]")






