import pytest
from test_case.distribution.distribution_base_case import DistributionToolTest

"""
商达团沟通平台——商家工作台入口
"""

class TestMerchantIMPlatform(DistributionToolTest):

    def setUp(self):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN_PRT", "wb_huoyangyang")
        self.sleep(3)

    @pytest.mark.p1
    def test_merchant_home_page_entrance(self):
        self.click("//span[@class='cps-main-badge']//img")
        # 判断是否进入客服消息系统
        self.assert_url("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

    # 达人智能推荐
    @pytest.mark.p1
    def test_merchant_intelligent_recommend_entrance(self):
        self.click("(//span[@class='cps-main-menu-title-content'])[12]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

    # 达人广场
    @pytest.mark.p1
    def test_merchant_promoter_square_entrance(self):
        self.click("(//span[@class='cps-main-menu-title-content'])[11]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

    # 达人详情页
    @pytest.mark.p1
    def test_merchant_promoter_detail_page_entrance(self):
        self.click("(//span[@class='cps-main-menu-title-content'])[11]")
        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-cps.prt.kwaixiaodian.com/zone/daren-match/daren-detail?promoterId=")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

    # 达人排行榜
    @pytest.mark.p1
    def test_merchant_promoter_rank_list_entrance(self):
        self.click("(//span[@class='cps-main-menu-title-content'])[13]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

    # 团长招商
    @pytest.mark.p1
    def test_merchant_leader_investment_entrance(self):
        self.click("(//span[@class='cps-main-menu-title-content'])[17]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

    # 团长详情页
    @pytest.mark.p1
    def test_merchant_leader_detail_page_entrance(self):
        self.click("(//span[@class='cps-main-menu-title-content'])[17]")
        self.sleep(2)
        self.click("(//div[@class='index-module__item_left--KfFHi'])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.sleep(2)
        self.assert_url_contains("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=merchant")

