import random
import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest


class TestDistributionMinimumCommission(DistributionToolTest):
    def test_Merchant_Minimum_Commission_Judgment(self):
        """商家维度0佣金判断，一旦报错可能是有人更改该商家的最低佣金配置了 """
        self.merchant_login("DISTRIBUTION_DOMAIN", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(5)
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//span[contains(text(),'新建普通计划')]")
        self.click("//span[contains(text(),'跳 过')]")
        self.click("//input[@placeholder='请输入商品ID']")
        self.type("//input[@placeholder='请输入商品ID']", '23369632399008')
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click("//td[@class='kwaishop-cps-merchant-plan-pc-table-cell kwaishop-cps-merchant-plan-pc-table-selection-column']//span[@class='kwaishop-cps-merchant-plan-pc-checkbox-inner']")
        time.sleep(2)
        self.click("//span[contains(text(),'下一步')]")
        self.click("input[placeholder='请输入0-50的整数']")
        self.input("input[placeholder='请输入0-50的整数']", '55')
        self.assert_text("请输入0-50的整数",
                         "//div[@class='kwaishop-cps-merchant-plan-pc-form-item-explain-error']")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_Product_Minimum_Commission_Judgment(self):
        """商品维度0佣金判断，一旦报错可能是有人更改了该商品的最低佣金配置了 """
        self.merchant_login("DISTRIBUTION_DOMAIN", "liuxiaohui07")
        self.driver.maximize_window()
        time.sleep(5)
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//span[contains(text(),'新建普通计划')]")
        self.click("//span[contains(text(),'跳 过')]")
        self.click("//input[@placeholder='请输入商品ID']")
        self.type("//input[@placeholder='请输入商品ID']", '23643553367147')
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click("//td[@class='kwaishop-cps-merchant-plan-pc-table-cell kwaishop-cps-merchant-plan-pc-table-selection-column']//span[@class='kwaishop-cps-merchant-plan-pc-checkbox-inner']")
        time.sleep(2)
        self.click("//span[contains(text(),'下一步')]")
        self.click("input[placeholder='请输入0-50的整数']")
        self.input("input[placeholder='请输入0-50的整数']", '55')
        self.assert_text("请输入0-50的整数",
                         "//div[@class='kwaishop-cps-merchant-plan-pc-form-item-explain-error']")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_Category_Minimum_Commission_Judgment(self):
        """商家+类目维度0佣金判断，一旦报错可能是有人更改了该商家该类目下的最低佣金配置了 """
        self.merchant_login("DISTRIBUTION_DOMAIN", "maliya")
        self.driver.maximize_window()
        time.sleep(5)
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//span[contains(text(),'新建普通计划')]")
        self.click("//span[contains(text(),'跳 过')]")
        self.click("//input[@placeholder='请输入商品ID']")
        self.type("//input[@placeholder='请输入商品ID']", '22939752259173')
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click("//td[@class='kwaishop-cps-merchant-plan-pc-table-cell kwaishop-cps-merchant-plan-pc-table-selection-column']//span[@class='kwaishop-cps-merchant-plan-pc-checkbox-inner']")
        time.sleep(2)
        self.click("//span[contains(text(),'下一步')]")
        self.click("input[placeholder='请输入0-50的整数']")
        self.input("input[placeholder='请输入0-50的整数']", '55')
        self.assert_text("请输入0-50的整数",
                         "//div[@class='kwaishop-cps-merchant-plan-pc-form-item-explain-error']")