import time

import pytest
from selenium.webdriver.common.by import By

from test_case.distribution.distribution_base_case import *

"""
分销首页
pytest test_case/distribution/merchant/index/test_distribution_index.py --headless -n=5
"""


class TestDistributionIndex(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        self.open("https://cps.kwaixiaodian.com/zone-cps/home")
        time.sleep(3)

    @pytest.mark.p1
    def test_back_to_ks_store(self):
        """登录成功-进入首页-点击返回快手小店-判断是否进入快手小店页面"""
        self.click("(//div[@class='index-module__go_back--_F_ka'])[1]")
        windows = self.driver.window_handles
        self.driver.switch_to.window(windows[-1])
        self.assert_title('快手小店')

    @pytest.mark.p1
    def test_DataoVerview_datadays_toggle(self):
        """进入首页-数据概览模块-点击切换时间范围按钮--判断按钮可点击"""
        self.click("//span[contains(text(),'近7天')]")
        self.sleep(2)
        self.click("//span[contains(text(),'近30天')]")
        promotion_data_seven_days = self.get_element("//span[contains(text(),'近7天')]")
        promotion_data_thirty_days = self.get_element("//span[contains(text(),'近30天')]")
        self.assert_true(self.is_element_clickable(promotion_data_seven_days))
        self.assert_true(self.is_element_clickable(promotion_data_thirty_days))

    @pytest.mark.p1
    def test_DataoVerview_fields(self):
        """进入首页-数据概览模块-字段判断"""
        self.assert_text('数据概览', "//span[contains(text(),'数据概览')]")
        self.assert_text('订单量', "//span[contains(text(),'订单量')]")
        self.assert_text('成交金额', "//span[contains(text(),'成交金额')]")
        self.assert_text('佣金率', "//span[contains(text(),'佣金率')]")
        self.assert_text('预估佣金支出', "//span[contains(text(),'预估佣金支出')]")
        self.assert_text('平均单价', "//span[contains(text(),'平均单价')]")

    @pytest.mark.p1
    def test_DataoVerview_Seemore(self):
        """进入首页-数据概览模块-点击查看更多按钮-跳转至“合作达人”页面"""
        See_more_tab = self.get_element('//*[@id="root"]/section/main/div/div[2]/div[1]/div[1]/div/div/div[1]/div[2]/div[2]')
        self.assert_true(self.is_element_clickable(See_more_tab))
        self.click('//*[@id="root"]/section/main/div/div[2]/div[1]/div[1]/div/div/div[1]/div[2]/div[2]')
        self.assert_text('直播达人', '//*[@id="rc-tabs-0-tab-1"]')


    @pytest.mark.p1
    def test_index_head_investment_promotion_activity(self):
        """招商活动卡片信息是否存在，点击去报名是否可以点击；查看更多是否可以点击"""
        self.find_element(
            "(//div[@class='index-module__table_container--vjQon'])[1]")
        self.is_element_enabled("(//span[contains(text(),'去报名')])[1]")

    @pytest.mark.p1
    def test_index_talent_investment_activities(self):
        """达人招商活动卡片信息是否存在，点击去报名是否可以点击；查看更多是否可以点击"""
        self.find_element(
            '//*[@id="root"]/section/main/div/div[2]/div[1]/div[4]/div[2]')
        self.find_element(
            '//*[@id="root"]/section/main/div/div[2]/div[1]/div[4]/div[3]')
        self.find_element(
            '//*[@id="root"]/section/main/div/div[2]/div[1]/div[4]/div[4]')
        self.is_element_enabled(
            '//*[@id="root"]/section/main/div/div[2]/div[1]/div[3]/div[2]/div[1]/button')
        self.is_element_enabled(
            '//*[@id="root"]/section/main/div/div[2]/div[1]/div[3]/div[1]/div[2]/div')

    @pytest.mark.p1
    def test_index_distribution_plan(self):
        """5个计划按钮能够正常点击；查看更多是否可以点击"""
        self.is_element_enabled("//*[@id='root']/section/main/div/div[2]/div[2]/div[2]/div[2]/div[1]")
        self.is_element_enabled("//*[@id='root']/section/main/div/div[2]/div[2]/div[2]/div[2]/div[2]")
        self.is_element_enabled("//*[@id='root']/section/main/div/div[2]/div[2]/div[2]/div[2]/div[3]")
        self.is_element_enabled("//*[@id='root']/section/main/div/div[2]/div[2]/div[2]/div[2]/div[4]")
        self.is_element_enabled("//*[@id='root']/section/main/div/div[2]/div[2]/div[2]/div[2]/div[5]")

    @pytest.mark.p1
    def test_index_sample_delivery_management(self):
        """ 待审核、待发货、待履约元素是否存在；查看更多是否可以点击 """
        self.find_element("//div[contains(text(),'待审核')]")
        self.find_element("//div[contains(text(),'待发货')]")
        self.find_element("//div[contains(text(),'待履约')]")
        self.is_element_enabled("(//div[contains(text(),'查看更多')])[6]")

    @pytest.mark.p1
    def test_index_my_people_see_more(self):
        """ 首页-达人智能推荐-点击查看更多，查看跳转是否正常 """
        # 点击查看更多
        self.click("(//div[@class='index-module__more--qHBB7'][contains(text(),'查看更多')])[2]")
        self.sleep(2)
        self.assert_text("达人智能推荐", "//div[@class='kwaishop-cps-daren-match-pc-pro-title-title']")

    @pytest.mark.p1
    def test_index_my_people_More_Livestreaming_talent(self):
        """ 首页-达人智能推荐-点击正在直播达人头像，查看跳转是否正常并判断是否定位到带货中tab"""
        # 点击查看更多
        self.click("//div[contains(text(),'个达人正在直播售卖我的商品')]")
        self.sleep(2)
        self.assert_text("我的达人", "//div[@class='kwaishop-cps-daren-match-pc-pro-title-title']")
        #判断是否定位到带货中tab
        isChecked = self.find_elements('kwaishop-cps-daren-match-pc-tabs-tab-btn', By.CLASS_NAME)[2].get_attribute('aria-selected')
        assert isChecked == 'true'

    @pytest.mark.p1
    def test_index_my_people_More_Already_talent(self):
        """ 首页-达人智能推荐-点击已上架达人头像，查看跳转是否正常并判断是否定位到带货中tab"""
        # 点击查看更多
        self.click("//div[contains(text(),'个达人已上架我的商品')]")
        self.sleep(2)
        self.assert_text("我的达人", "//div[@class='kwaishop-cps-daren-match-pc-pro-title-title']")
        # 判断是否定位到带货中tab
        isChecked = self.find_elements('kwaishop-cps-daren-match-pc-tabs-tab-btn', By.CLASS_NAME)[2].get_attribute(
            'aria-selected')
        assert isChecked == 'true'
    @pytest.mark.p1
    def test_index_my_people_More_extend_talent(self):
        """ 首页-达人智能推荐-点击正在推广达人头像，查看跳转是否正常并判断是否定位到带货中tab"""
        # 点击查看更多
        self.click("//div[contains(text(),'个达人正在短视频推广我的商品')]")
        self.sleep(2)
        self.assert_text("我的达人", "//div[@class='kwaishop-cps-daren-match-pc-pro-title-title']")
        isChecked = self.find_elements('kwaishop-cps-daren-match-pc-tabs-tab-btn', By.CLASS_NAME)[2].get_attribute(
            'aria-selected')
        assert isChecked == 'true'

    @pytest.mark.p1
    def test_index_head_investment_promotion_tab_Toggle(self):
        """达人智能推荐模块，点击各个tab切换"""
        self.click("//div[@class='cps-ant-tabs-nav-wrap']//div[2]")
        self.sleep(2)
        self.click("//div[@class='cps-ant-tabs-nav-wrap']//div[3]")
        self.sleep(2)
        self.click("(//div[contains(text(),'快手号：')])[1]")
        self.sleep(2)

    def test_index_head_investment_promotion_tab1_avatar(self):
        """达人智能推荐模块，家居百货精选tab  达人头像不为空"""
        img_xpath = ['//*[@id="root"]/section/main/div/div[2]/div[1]/div[2]/div[4]/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div/div/div[1]/span/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("src")
            assert outer_html != ''or None

    def test_index_head_investment_promotion_tab2_avatar(self):
        """达人智能推荐模块，同类商家青睐tab  达人头像不为空"""
        self.click("//div[@class='cps-ant-tabs-nav-wrap']//div[2]")
        img_xpath = ['//*[@id="root"]/section/main/div/div[2]/div[1]/div[2]/div[4]/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div/div/div[1]/span/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("src")
            assert outer_html != ''or None

    def test_index_head_investment_promotion_tab_fields(self):
        """达人智能推荐模块，同类商家青睐字段展示正常"""
        self.click("//div[@class='cps-ant-tabs-nav-wrap']//div[2]")
        time.sleep(2)
        self.assert_text("快手号",
                         "//body//div[@id='main_root']//div[@class='cps-ant-spin-container']//div//div//div[1]//div[1]//div[1]//div[1]//div[1]//div[1]//div[1]//div[2]//div[2]//div[1]//div[1]")
        self.assert_text("粉丝数",
                         "//body//div[@id='main_root']//div[@class='cps-ant-spin-container']//div//div//div[1]//div[1]//div[1]//div[1]//div[1]//div[1]//div[1]//div[2]//div[2]//div[2]//div[1]")
        self.assert_text("30日直播场均看播人数",
                         "(//section[contains(text(),'30日直播场均看播人数')])[1]")
        self.assert_text("30日直播场均销售额(元)",
                         "(//section[contains(text(),'30日直播场均销售额(元)')])[1]")


    def test_index_head_investment_promotion_tab3_avatar(self):
        """达人智能推荐模块，近期数据飙升tab  达人头像不为空"""
        self.click("//div[@class='cps-ant-tabs-nav-wrap']//div[3]")
        img_xpath = ['//*[@id="root"]/section/main/div/div[2]/div[1]/div[2]/div[4]/div/div/div/div/div/div/div/div[1]/div/div[1]/div/div/div/div/div[1]/span/img']
        for xpath in img_xpath:
            img = self.find_element(xpath)
            outer_html = img.get_attribute("src")
            assert outer_html != ''or None

    @pytest.mark.p1
    @pytest.mark.skip
    def test_index_huodong_see_more(self):
        """ 首页-团长招商活动-点击查看更多，查看跳转是否正常 """
        # 点击查看更多
        self.click('//*[@id="root"]/section/main/div/div[2]/div[1]/div[3]/div[1]/div[2]/div')
        self.sleep(2)
        self.assert_text("团长等级", "//div[@class='index-module__tag_filter--Edzgv']//div[4]//div[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_index_talent_see_more(self):
        """ 首页-达人招商-点击查看更多，查看跳转是否正常 """
        # 点击查看更多
        self.click('//*[@id="root"]/section/main/div/div[2]/div[1]/div[4]/div[1]/div[2]/div')
        self.sleep(2)
        self.assert_text("达人招商列表", "(//div[@class='index-module__list-title--i4UXI'])[1]")

    @pytest.mark.p1
    def test_index_sample_see_more(self):
        """ 首页-寄养管理-点击查看更多，查看跳转是否正常 """
        # 点击查看更多
        self.click("(//div[@class='index-module__more--qHBB7'][contains(text(),'查看更多')])[5]")
        self.sleep(2)
        self.assert_text("寄样管理", "(//div[contains(text(),'寄样管理')])[1]")

    @pytest.mark.p1
    def test_index_help_see_more(self):
        """ 首页-帮助中心-点击查看更多，查看跳转是否正常 """
        # 点击查看更多
        self.open('https://cps-kwaixiaodian.test.gifshow.com/zone-cps/home')
        self.click("(//div[contains(text(),'去查看')])[1]")
        self.sleep(2)
        self.assert_text("【快分销入驻】教程", "(//div[@class='XVOmorqHozbCYvhrkzYr'])[1]")
