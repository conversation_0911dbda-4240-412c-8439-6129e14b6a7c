import pytest

from test_case.distribution.distribution_base_case import *

"""
找团长
找团长-团长招商
pytest test_case/distribution/merchant/find_regimental_commander/test_head_inviting_investment.py --headless -n=5
"""


class TestFindTheRegimentalLeaderPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)

    @pytest.mark.p1
    def test_find_activity(self):
        """进入团长招商-判断是否进入找团长页面"""
        self.click('//span[contains(text(),"团长招商")]')
        self.assert_text('T3', "//span[normalize-space()='T3']")
        self.assert_text('T4', "//span[normalize-space()='T4']")
        self.assert_text('T5', "//span[normalize-space()='T5']")

    @pytest.mark.p1
    def test_search_leader(self):
        """进入团长招商-输入团长ID搜索-判断它的类目元素和名称是否一致"""
        self.sleep(1)
        self.click('//span[contains(text(),"团长招商")]')
        self.sleep(1)
        self.input('input[placeholder="请输入团长名称或ID"]', '705396035')
        self.sleep(1)
        self.click('span[aria-label="system-search-line"]')
        self.sleep(1)
        self.assert_element("//*[@id='rc-tabs-1-panel-1']/div/div[2]/div[2]/div[1]")

    @pytest.mark.p0
    def test_head_list_turn_page(self):
        """进入团长招商-向下滑动点击翻页是否异常"""
        self.click('//span[contains(text(),"团长招商")]')
        self.scroll_down(3)
        self.click("li[title='2'] a[rel='nofollow']")
        self.click("li[title='3'] a[rel='nofollow']")
        self.click("li[title='4'] a[rel='nofollow']")
        self.click("li[title='5'] a[rel='nofollow']")

    @pytest.mark.p0
    def test_activity_screening(self):
        """进入首页-点击找活动-经营类目->点击筛选条件-依次点击经营类目-清空筛选"""
        self.click("//span[contains(text(),'团长招商')]")
        self.click("(//div[@id='rc-tabs-1-tab-2'])[1]")
        business_category = self.find_elements("//*[@id='rc-tabs-1-panel-2']/div/div[1]/div[1]/div[2]/span")
        for element in business_category:
            element.click()
            self.click("(//span[contains(text(),'清空筛选')])[2]")

    @pytest.mark.p0
    def test_commission_rate(self):
        """进入首页-点击找活动-佣金率->点击筛选条件-依次点击佣金率-判断佣金率是否可选中"""
        self.sleep(3)
        self.click('//span[contains(text(),"团长招商")]')
        self.click("(//div[@id='rc-tabs-1-tab-2'])[1]")
        commission_rate = self.find_elements('//*[@id="rc-tabs-1-panel-2"]/div/div[1]/div[2]/div[2]/span')
        for element in commission_rate:
            element.click()

    @pytest.mark.p0
    def test_click_on_activity_service_rates(self):
        """进入首页-点击找活动-服务费率->点击筛选条件-依次点击服务费率-判断服务费率是否可选中"""
        self.click('//span[contains(text(),"团长招商")]')
        time.sleep(1)
        self.click("(//div[@id='rc-tabs-1-tab-2'])[1]")
        service_rates = self.find_elements('//*[@id="rc-tabs-1-panel-2"]/div/div[1]/div[3]/div[2]/span')
        for element in service_rates:
            element.click()

    @pytest.mark.p1
    def test_colonels_rank(self):
        """ 团长等级 T3、T4、T5 按钮是否可以操作 """
        self.click('//span[contains(text(),"团长招商")]')
        self.sleep(3)
        self.find_element("//span[normalize-space()='T3']")
        self.is_element_enabled("//span[normalize-space()='T3']")
        self.find_element("//span[normalize-space()='T4']")
        self.is_element_enabled("//span[normalize-space()='T4']")
        self.find_element("//span[normalize-space()='T5']")
        self.is_element_enabled("//span[normalize-space()='T5']")

    @pytest.mark.p1
    def test_last_30_days_sales(self):
        """ 5000-1W、1W-10W、10W-50W、50W-100W、100W-1000W、1000W以上 按钮是否存在 """
        self.click('//span[contains(text(),"团长招商")]')
        self.sleep(3)
        self.find_element("//span[normalize-space()='5000-1W']")
        self.find_element("//span[normalize-space()='1W-10W']")
        self.find_element("//span[normalize-space()='10W-50W']")
        self.find_element("//span[normalize-space()='50W-100W']")
        self.find_element("//span[normalize-space()='100W-1000W']")
        self.find_element("//span[normalize-space()='1000W以上']")

    @pytest.mark.p1
    def test_head_merchants_commission_rate(self):
        """ 1%-3% 3%-5% 5%-10% 10%-20% 20%以上 按钮是否可以操作 """
        self.click('//span[contains(text(),"团长招商")]')
        self.sleep(3)
        self.click("//span[normalize-space()='1%-3%']")
        self.sleep(3)
        self.click("//span[normalize-space()='3%-5%']")
        self.sleep(3)
        self.click("//span[normalize-space()='5%-10%']")
        self.sleep(3)
        self.click("//span[normalize-space()='10%-20%']")

    @pytest.mark.p1
    def test_head_of_investment_business_category(self):
        """ 食品饮料 3%-5% 5%-10% 10%-20% 20%以上 按钮是否可以操作 """
        self.click('//span[contains(text(),"团长招商")]')
        self.sleep(3)
        self.click('//span[contains(text(),"食品饮料")]')
        self.sleep(3)
        self.click('//span[contains(text(),"女装女鞋")]')

        self.sleep(3)
        self.click('//span[contains(text(),"男装男鞋")]')
        self.sleep(3)
        self.click('//span[contains(text(),"珠宝配饰")]')
