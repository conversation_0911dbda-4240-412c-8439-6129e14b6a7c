import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
找团长-
找团长-我报名的活动
pytest test_case/distribution/merchant/find_regimental_commander/test_i_signed_up_for_the_event.py --headless -n=3
"""


class TestGeneralInvestmentPage(DistributionToolTest):
    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)

    @pytest.mark.p0
    def test_ordinary_investment(self):
        """进入首页-点击找团长-点击我报名的活动-判断是否进入我报名的活动页面"""
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        self.assert_text('普通招商', "#rc-tabs-0-tab-normal")
        self.assert_text('专属招商', "#rc-tabs-0-tab-exclusive")

    @pytest.mark.p0
    def test_exclusive_investment(self):
        """进入首页-点击找团长-点击我报名的活动-专属招商-判断是否进入专属招商页面"""
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        time.sleep(3)
        self.click('#rc-tabs-0-tab-exclusive')
        time.sleep(3)
        self.assert_text('活动信息', "//th[contains(text(),'活动信息')]")
        self.assert_text('团长信息', "//th[contains(text(),'团长信息')]")

    @pytest.mark.p0
    def test_registered_goods(self):
        """点击我报名的活动-点击已结束-点击活动标题-判断是否进入活动详情页面"""
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        self.click("(//label[@class='kwaishop-cps-merchant-investment-pc-radio-button-wrapper'])[1]")
        self.click('//tbody/tr[1]/td[1]/div[1]/button[1]')
        time.sleep(2)
        self.assert_text('活动详情', "//div[@aria-selected='true']")
        self.assert_text('报名商品', "//div[@role='tab'][contains(text(),'报名商品')]")
        self.assert_text('推广效果', "//div[contains(text(),'推广效果')]")

    @pytest.mark.p2
    def test_promotion_effect(self):
        """点击我报名的活动-点击已结束-点击推广效果-判断是否进入推广效果页面"""
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        self.click("(//label[@class='kwaishop-cps-merchant-investment-pc-radio-button-wrapper'])[1]")
        self.click('(//span[contains(text(),"推广效果")])[2]')
        time.sleep(3)
        self.assert_text('效果数据汇总', "//div[contains(text(),'效果数据汇总')]")
        self.assert_text('各商品效果数据', "//div[contains(text(),'各商品效果数据')]")

    @pytest.mark.p0
    def test_exclusive_investment_activities_details_page(self):
        """点击我报名的活动-点击专属招商-点击已结束-点击推广效果-判断是否进入专属推广效果页面"""
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        time.sleep(1)
        self.click('#rc-tabs-0-tab-exclusive')
        time.sleep(1)
        self.click("(//label[@class='kwaishop-cps-merchant-investment-pc-radio-button-wrapper'])[1]")
        self.click('(//span[contains(text(),"推广效果")])[2]')
        time.sleep(3)
        self.assert_text('效果数据汇总', "//div[contains(text(),'效果数据汇总')]")
        self.assert_text('各商品效果数据', "//div[contains(text(),'各商品效果数据')]")

    @pytest.mark.p0
    def test_exclusive_promotion_effect(self):
        """复制手机号按钮是否存在，是否可点击 6735089008 """
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        self.sleep(3)
        self.type("#activityKeyword", "5550963714")
        self.click("//span[contains(text(),'查 询')]")
        self.sleep(3)
        self.click("//span[contains(text(),'联系团长')]")
        self.sleep(3)
        self.find_element("//span[contains(text(),'复 制')]")
        self.is_element_enabled("//span[contains(text(),'复 制')]")
        self.find_element("//span[contains(text(),'取 消')]")
        self.is_element_enabled("//span[contains(text(),'复 制')]")

    @pytest.mark.p0
    def test_view_all_events(self):
        """ 判断是否进入已报名活动页面 """
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        self.sleep(3)
        self.click("//tbody/tr[1]/td[1]/div[1]/button[1]/span[1]")
        self.is_element_enabled("div[id='rc-tabs-0-panel-1'] div div div div div div span")

    @pytest.mark.p0
    def test_activity_basic_information(self):
        """ 判断活动基本信息是否存在 """
        self.click('//*[@id="menu-6Vrk5FTENHM"]/span/span/span')
        self.sleep(1)
        self.click("//tbody/tr[1]/td[1]/div[1]/button[1]/span[1]")
        self.sleep(2)
        self.find_element("//div[contains(text(),'活动标题：')]")
        self.find_element("//div[contains(text(),'创建时间：')]")
        self.find_element("div[class='FVWgA5nmfDqwcG_4g7yw']")


