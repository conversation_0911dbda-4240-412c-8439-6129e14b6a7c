import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
效果看板
"""


@pytest.mark.skip
class TestDistributionEffect(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_promotion_effect_summary(self):
        """登录成功-进入首页-点击效果看板-点击推广效果-判断效果概览是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_normal(self):
        """推广效果-点击-普通计划tab-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击普通计划tab
        self.click("(//div[@id='tab-normalPlan'])[1]")
        time.sleep(1)
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_product(self):
        """推广效果-点击-商品定向计划tab-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击商品定向计划tab
        self.click("(//div[@id='tab-goodPlan'])[1]")
        time.sleep(1)
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_shop(self):
        """推广效果-点击-店铺定向计划tab-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击店铺定向计划tab
        self.click("(//div[@id='tab-shopPlan'])[1]")
        time.sleep(1)
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_huodong(self):
        """推广效果-点击-招商活动计划tab-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击普通计划tab
        self.click("(//div[@id='tab-investmentPlan'])[1]")
        time.sleep(1)
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_step(self):
        """推广效果-点击-阶梯佣金计划tab-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击普通计划tab
        self.click("(//div[@id='tab-stepCommission'])[1]")
        time.sleep(1)
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_summary_time(self):
        """登录成功-进入首页-点击效果看板-点击推广效果-点击时间选择器-判断效果概览是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击时间选择器
        self.click("(//input[@placeholder='开始时间'])[1]")
        self.click("(//span[contains(text(),'1')])[14]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[14]")
        # 判断页面是否正常
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_normal_time(self):
        """推广效果-点击-普通计划tab-点击时间选择器-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击普通计划tab
        self.click("(//div[@id='tab-normalPlan'])[1]")
        time.sleep(1)
        # 点击时间选择器
        self.click("(//input[@placeholder='开始时间'])[1]")
        self.click("(//span[contains(text(),'1')])[14]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[14]")
        # 判断页面是否正常
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_product_time(self):
        """推广效果-点击-商品定向计划tab-点击时间选择器-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击商品定向计划tab
        self.click("(//div[@id='tab-goodPlan'])[1]")
        time.sleep(1)
        # 点击时间选择器
        self.click("(//input[@placeholder='开始时间'])[1]")
        self.click("(//span[contains(text(),'1')])[14]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[14]")
        # 判断页面是否正常
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_shop_time(self):
        """推广效果-点击-店铺定向计划tab-点击时间选择器-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击店铺定向计划tab
        self.click("(//div[@id='tab-shopPlan'])[1]")
        time.sleep(1)
        # 点击时间选择器
        self.click("(//input[@placeholder='开始时间'])[1]")
        self.click("(//span[contains(text(),'1')])[14]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[14]")
        # 判断页面是否正常
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_huodong_time(self):
        """推广效果-点击-招商活动计划tab-点击时间选择器-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击普通计划tab
        self.click("(//div[@id='tab-investmentPlan'])[1]")
        time.sleep(1)
        # 点击时间选择器
        self.click("(//input[@placeholder='开始时间'])[1]")
        self.click("(//span[contains(text(),'1')])[14]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[14]")
        # 判断页面是否正常
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_effect_step_time(self):
        """推广效果-点击-阶梯佣金计划tab-点击时间选择器-判断是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        time.sleep(1)
        # 点击普通计划tab
        self.click("(//div[@id='tab-stepCommission'])[1]")
        time.sleep(1)
        # 点击时间选择器
        self.click("(//input[@placeholder='开始时间'])[1]")
        self.click("(//span[contains(text(),'1')])[14]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[14]")
        # 判断页面是否正常
        self.click("(//div[contains(text(),'指标列表')])[1]")

    @pytest.mark.p0
    def test_promotion_trend_pv(self):
        """推广趋势-PVtab是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_uv(self):
        """推广趋势-UVtab是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[1]")
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_order(self):
        """推广趋势-支付订单数tab是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[2]")
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_gmv(self):
        """推广趋势-支付金额数tab是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[3]")
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_commission(self):
        """推广趋势-预估佣金tab是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[4]")
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_pv_time(self):
        """推广趋势-PVtab-点击时间选择器-是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 点击时间选择器
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.click("(//span[contains(text(),'1')])[3]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[3]")
        # 判断页面是否正常
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_uv_time(self):
        """推广趋势-UVtab-点击时间选择器-是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[1]")
        # 点击时间选择器
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.click("(//span[contains(text(),'1')])[3]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[3]")
        # 判断页面是否正常
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_order_time(self):
        """推广趋势-支付订单数tab-点击时间选择器-是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[2]")
        # 点击时间选择器
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.click("(//span[contains(text(),'1')])[3]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[3]")
        # 判断页面是否正常
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_gmv_time(self):
        """推广趋势-支付金额数tab-点击时间选择器-是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[3]")
        # 点击时间选择器
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.click("(//span[contains(text(),'1')])[3]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[3]")
        # 判断页面是否正常
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_promotion_trend_commission_time(self):
        """推广趋势-预估佣金tab-点击时间选择器-是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)
        self.click('//*[@id="menu-5xGD5jMX7s4"]/span/span/span/span')
        # 切换tab
        time.sleep(1)
        self.click("(//div[@class='card-item'])[4]")
        # 点击时间选择器
        self.click("(//input[@placeholder='开始日期'])[1]")
        self.click("(//span[contains(text(),'1')])[3]")
        time.sleep(1)
        self.click("(//span[contains(text(),'1')])[3]")
        # 判断页面是否正常
        self.assert_text('推广趋势图(合计)', '//span[@class="title"]')

    @pytest.mark.p0
    def test_the_official_live(self):
        """合作达人-直播达人tab-页面展示是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.assert_text("关联直播场次", "(//div[contains(text(),'关联直播场次')])[1]")

    @pytest.mark.p0
    def test_the_official_live_talent(self):
        """合作达人-直播达人tab-合作达人详情模块是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.assert_text("合作达人详情", "(//div[@class='index-module__title--is3SZ'])[1]")

    @pytest.mark.p0
    def test_the_official_live_talent_product(self):
        """合作达人-直播达人tab-合作达人详情模块-点击商品详情-抽屉是否展示"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.click("(//span[contains(text(),'详情')])[2]")
        self.click("(//div[@class='cps-ant-drawer-title'])[1]")

    @pytest.mark.p0
    def test_the_official_live_talent_live_times(self):
        """合作达人-直播达人tab-合作达人详情模块-点击管理直播场次详情-抽屉是否展示"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.click("(//span[contains(text(),'详情')])[3]")
        self.click("//div[@class='cps-ant-drawer cps-ant-drawer-right cps-ant-drawer-open']//div[@class='cps-ant-drawer-header-title']//div[1]")

    @pytest.mark.p0
    def test_the_official_live_talent_swap(self):
        """合作达人-直播达人tab-潜力达人挖掘模块是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.click("(//div[@class='index-module__left--LTPLB'])[1]")

    @pytest.mark.p0
    def test_the_official_live_talent_swap_see_more(self):
        """合作达人-直播达人tab-潜力达人挖掘模块-点击查看更多-跳转是否正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.click("(//div[@class='index-module__right--egGqZ'])[1]")
        self.click("//div[@id='__Kpro-workbench-scroll-content__']//span[1]//span[1]")

    @pytest.mark.p0
    def test_the_official_live_time(self):
        """合作达人-直播达人tab-切换时间范围"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        self.click("(//span[contains(text(),'近30天')])[1]")
        self.click("(//span[contains(text(),'详情')])[2]")

    @pytest.mark.p0
    def test_the_official_short_video(self):
        """合作达人-短视频达人tab-页面展示是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")
        self.assert_text("我的商品", "(//div[contains(text(),'我的商品')])[1]")

    @pytest.mark.p0
    def test_the_official_short_video_talent(self):
        """合作达人-短视频达人tab-合作达人详情模块是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")
        self.assert_text("合作达人详情", "(//div[@class='index-module__title--is3SZ'])[1]")

    @pytest.mark.p0
    def test_the_official_short_video_talent_product(self):
        """合作达人-短视频达人tab-合作达人详情模块-点击商品详情-抽屉是否展示"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")

        self.click("(//span[contains(text(),'详情')])[2]")
        self.click("(//div[@class='cps-ant-drawer-title'])[1]")

    @pytest.mark.p0
    def test_the_official_short_video_talent_live_times(self):
        """合作达人-短视频达人tab-合作达人详情模块-点击管理直播场次详情-抽屉是否展示"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")

        self.click("(//span[contains(text(),'详情')])[3]")
        self.click("//div[@class='cps-ant-drawer cps-ant-drawer-right cps-ant-drawer-open']//div[@class='cps-ant-drawer-header-title']//div[1]")

    @pytest.mark.p0
    def test_the_official_short_video_talent_swap(self):
        """合作达人-短视频达人tab-潜力达人挖掘模块是否展示正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")
        self.click("(//div[@class='index-module__left--LTPLB'])[1]")

    @pytest.mark.p0
    def test_the_official_short_video_talent_swap_see_more(self):
        """合作达人-短视频达人tab-潜力达人挖掘模块-点击查看更多-跳转是否正常"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")

        self.click("(//div[@class='index-module__right--egGqZ'])[1]")
        self.click("//div[@id='__Kpro-workbench-scroll-content__']//span[1]//span[1]")

    @pytest.mark.p0
    def test_the_official_short_video_time(self):
        """合作达人-短视频达人tab-切换时间范围"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(3)
        self.click("(//div[@class='index-module__moreBtn--KsMpO'])[1]")
        # 切换tab
        self.click("(//div[@class='cps-ant-tabs-tab'])[1]")

        self.click("(//span[contains(text(),'近30天')])[1]")
        self.click("(//span[contains(text(),'详情')])[2]")



