import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
找团长-
找团长-我报名的活动
pytest test_case/distribution/merchant/show_effect_dashboard/test_overview_effects.py --headless -n=3
"""


class TestGeneralInvestmentPage(DistributionToolTest):
    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')

    @pytest.mark.p1
    def test_overview_effects(self):
        """ 效果概览 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click('//input[@placeholder="结束时间"]')
        self.click('//button[contains(text(),"最近三个月")]')
        self.click('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"重置")]')

    @pytest.mark.p1
    def test_display_standard_plan_dashboard(self):
        """ 普通计划的效果看板 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click('//div[@id="tab-normalPlan"]')
        self.click('//input[@placeholder="结束时间"]')
        self.click('//button[contains(text(),"最近一个月")]')
        self.click('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"重置")]')

    @pytest.mark.p1
    def test_display_product_targeting_dashboard(self):
        """ 商品定向的效果看板 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click('//div[@id="tab-goodPlan"]')
        self.click('//input[@placeholder="结束时间"]')
        self.click('//button[contains(text(),"最近一个月")]')
        self.click('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"重置")]')

    @pytest.mark.p1
    def test_display_investment_campaign_dashboard(self):
        """ 店铺定向计划的效果看板 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click("//div[@aria-controls='pane-shopPlan']")
        self.click('//input[@placeholder="结束时间"]')
        self.click('//button[contains(text(),"最近一个月")]')
        self.click('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"重置")]')


    @pytest.mark.p1
    def test_display_Investment_promotion_plan(self):
        """ 招商活动计划的效果看板 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        self.click('//div[@id="tab-investmentPlan"]')
        self.click('//input[@placeholder="结束时间"]')
        self.click('//button[contains(text(),"最近一个月")]')
        self.click('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"重置")]')


    @pytest.mark.p1
    def test_display_Ladder_commission(self):
        """ 阶梯佣金计划的效果看板 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        self.click("//div[@aria-controls='pane-stepCommission']")
        self.click('//input[@placeholder="结束时间"]')
        self.click('//button[contains(text(),"最近一个月")]')
        self.click('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"查询")]')
        self.find_element('//span[contains(text(),"重置")]')

    # 分销
    @pytest.mark.p1
    @pytest.mark.skip
    def test_overview_effects_List_field(self):
        """ 效果概览 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        self.assert_text("日期","//div[contains(@class,'el-table__fixed-header-wrapper')]//div[@class='cell'][contains(text(),'日期')]")
        self.assert_text("PV", '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[2]/div/span[1]')
        self.assert_text("UV", '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[3]/div/span[1]')
        self.assert_text("支付笔数", "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付笔数')]")
        self.assert_text("支付GMV", "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付GMV')]")
        self.assert_text("佣金比例", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'佣金比例')]")
        self.assert_text("预估佣金", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'预估佣金')]")
        self.assert_text("技术服务费", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'技术服务费')]")
        self.assert_text("商品ID", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品ID')]")
        self.assert_text("商品名称", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品名称')]")
        self.assert_text("达人ID", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'达人ID')]")
        # self.assert_text("达人昵称", "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'达人昵称')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_General_plan_List_field(self):
        """ 普通计划 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')
        self.click("//div[contains(@role,'tab')][contains(text(),'普通计划')]")
        self.assert_text("日期",
                         "//div[contains(@class,'el-table__fixed-header-wrapper')]//div[@class='cell'][contains(text(),'日期')]")
        self.assert_text("计划类别",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[2]/div')
        self.assert_text("PV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[3]/div/span[1]')
        self.assert_text("UV", '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[4]/div/span[1]')
        # self.assert_text("支付笔数", "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付笔数')]")
        self.assert_text("支付GMV",
                         "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付GMV')]")
        self.assert_text("佣金比例",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'佣金比例')]")
        self.assert_text("预估佣金",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'预估佣金')]")
        self.assert_text("技术服务费",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'技术服务费')]")
        self.assert_text("商品ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品ID')]")
        self.assert_text("商品名称",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品名称')]")
        self.sleep(2)
        self.assert_text("达人ID",
                         "(//div[contains(@class,'cell')][contains(text(),'达人ID')])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_Commodity_orientation_List_field(self):
        """ 商品定向的列表字段 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click('//div[@id="tab-goodPlan"]')
        self.assert_text("日期",
                         "//div[contains(@class,'el-table__fixed-header-wrapper')]//div[@class='cell'][contains(text(),'日期')]")
        self.assert_text("计划类别",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[2]/div')
        self.assert_text("PV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[3]/div/span[1]')
        self.assert_text("UV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[4]/div/span[1]')
        # self.assert_text("支付笔数", "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付笔数')]")
        self.assert_text("支付GMV",
                         "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付GMV')]")
        self.assert_text("佣金比例",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'佣金比例')]")
        self.assert_text("预估佣金",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'预估佣金')]")
        self.assert_text("技术服务费",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'技术服务费')]")
        self.assert_text("商品ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品ID')]")
        self.assert_text("商品名称",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品名称')]")
        self.assert_text("达人ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'达人ID')]")


    @pytest.mark.p1
    @pytest.mark.skip
    def test_Store_orientation_List_field(self):
        """ 店铺定向的列表字段 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click("//div[@aria-controls='pane-shopPlan']")
        self.assert_text("日期",
                         "//div[contains(@class,'el-table__fixed-header-wrapper')]//div[@class='cell'][contains(text(),'日期')]")
        self.assert_text("计划类别",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[2]/div')
        self.assert_text("PV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[3]/div/span[1]')
        self.assert_text("UV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[4]/div/span[1]')
        # self.assert_text("支付笔数", "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付笔数')]")
        self.assert_text("支付GMV",
                         "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付GMV')]")
        self.assert_text("佣金比例",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'佣金比例')]")
        self.assert_text("预估佣金",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'预估佣金')]")
        self.assert_text("技术服务费",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'技术服务费')]")
        self.assert_text("商品ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品ID')]")
        self.assert_text("商品名称",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品名称')]")
        self.assert_text("达人ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'达人ID')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_Investment_promotion_activity_List_field(self):
        """ 招商活动的列表字段 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click("//div[@role='tab'][contains(text(),'招商活动计划')]")
        self.assert_text("日期",
                         "//div[contains(@class,'el-table__fixed-header-wrapper')]//div[@class='cell'][contains(text(),'日期')]")
        self.assert_text("计划类别",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[2]/div')
        self.assert_text("PV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[3]/div/span[1]')
        self.assert_text("UV",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[4]/div/span[1]')
        # self.assert_text("支付笔数", "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付笔数')]")
        self.assert_text("支付GMV",
                         "//thead[contains(@class,'has-gutter')]//span[contains(text(),'支付GMV')]")
        self.assert_text("佣金比例",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'佣金比例')]")
        self.assert_text("预估佣金",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'预估佣金')]")
        self.assert_text("技术服务费",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'技术服务费')]")
        self.assert_text("商品ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品ID')]")
        self.assert_text("商品名称",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'商品名称')]")
        self.assert_text("达人ID",
                         "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'达人ID')]")
        # self.assert_text("达人昵称",
        #                  "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'达人昵称')]")
        # self.assert_text("招商团长ID",
        #                  "//div[@class='el-table__header-wrapper']//th[14]//div[1]")
        # self.assert_text("招商团长昵称",
        #                  "//thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'招商团长昵称')]")
        # self.assert_text("招商活动ID",
        #                  //thead[contains(@class,'has-gutter')]//div[contains(@class,'cell')][contains(text(),'招商活动ID')])

    @pytest.mark.p1
    def test_Ladder_commission_List_field(self):
        """ 阶梯佣金的列表字段 """
        self.click('//*[@id="menu-iLNuO9FvUoY"]/span/span/span/span')

        self.click("//div[contains(@aria-controls,'pane-stepCommission')]")
        self.assert_text("日期",
                         "//div[contains(@class,'el-table__fixed-header-wrapper')]//div[@class='cell'][contains(text(),'日期')]")
        self.assert_text("计划类别",
                         '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/table/thead/tr/th[2]/div')
        self.assert_text("支付笔数",
                         "//div[@class='el-table__header-wrapper']//th[3]//div[1]//span[1]")
        self.assert_text("支付GMV",
                         "//div[contains(@class,'el-table__header-wrapper')]//span[contains(text(),'支付GMV')]")
        self.assert_text("佣金比例",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'佣金比例')]")
        self.assert_text("预估佣金",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'预估佣金')]")
        self.assert_text("技术服务费",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'技术服务费')]")
        self.assert_text("商品ID",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'商品ID')]")
        self.assert_text("商品名称",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'商品名称')]")
        self.assert_text("达人ID",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'达人ID')]")
        self.assert_text("达人昵称",
                         "//div[contains(@class,'el-table__header-wrapper')]//div[contains(@class,'cell')][contains(text(),'达人昵称')]")
