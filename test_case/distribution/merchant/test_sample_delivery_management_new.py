import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
寄样管理
-0元申样
-买样后返
"""


class TestSampleDeliveryManagement(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    @pytest.mark.skip
    def test_purchase_sample_order_inquiry(self):
        """ 点击 寄样管理-买样后返,搜索的订单ID是否在列表中 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//li[@role='menuitem']//span//span//span//span[contains(text(),'寄样管理')]")
        self.click("//span[contains(text(),'买样后返')]")
        """ 断言 搜索的订单ID在列表中 """
        time.sleep(3)
        self.type("//input[@id='oid']", "2308600058545147")
        self.click("//span[contains(text(),'查 询')]")
        self.click("//div[contains(text(),'已完成')]")
        text = self.get_text("//div[@class='index-module__row_left_box--tjuX_']")
        self.assert_in("2308600058545147", text)

    @pytest.mark.p1
    def test_enter_the_yuan_request_sample_new(self, ):
        """ 点击 寄样管理-是否进入0元申样页 new """

        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click("//li[@role='menuitem']//span//span//span//span[contains(text(),'寄样管理')]")
        """ 断言 """
        self.find_element("(//span[contains(text(),'0元申样')])[1]")

    @pytest.mark.p1
    def test_enter_and_buy_samples_before_returning(self):
        """ 点击 寄样管理-买样后返后，判断是否进入该页面 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//li[@role='menuitem']//span//span//span//span[contains(text(),'寄样管理')]")
        """ 断言 """
        self.click("//span[contains(text(),'买样后返')]")
        self.find_element("#sample_table_container")

    @pytest.mark.p1
    def test_click_set_rules(self):
        """ 点击 寄样管理-设置规则,判断按钮是否可点击 """
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//li[@role='menuitem']//span//span//span//span[contains(text(),'寄样管理')]")
        """ 断言 """
        self.is_element_enabled("//span[contains(text(),'设置规则')]")
        self.click("//span[contains(text(),'设置规则')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_click_sample_details_new(self):
        """ 点击 寄样管理-样品详情,判断相关字段是否存在"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click("//li[@role='menuitem']//span//span//span//span[contains(text(),'寄样管理')]")
        self.click("//div[contains(text(),'全部')]")
        """ 断言 """
        time.sleep(3)
        self.click("//div[@class='index-module__table_body--SYUfb']//div[1]//div[1]//div[1]//div[1]//div[3]")
        text = self.get_text("div[class='index-module__card_desc_info--N_Ad4'] div:nth-child(1)")
        self.assert_in("商品ID", text)
        time.sleep(3)
        text = self.get_text(".index-module__sample_price--CixxW")
        self.assert_in("样品价", text)