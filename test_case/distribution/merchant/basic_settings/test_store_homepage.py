import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
基本设置-店铺主页

"""

class TestStoreHomepage(DistributionToolTest):
    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)
        """进入店铺主页"""
        self.click("//*[@id='menu-xqq1hUijrwA']/span/span/span/span")
        time.sleep(3)

    def test_store_homepage(self):
        """进入首页-点击基本设置-店铺主页-判断是否进入店铺主页页面"""
        self.assert_element("(//a[contains(text(),'我的主页')])[1]")

    def test_store_homepage_close_the_homepage(self):
        """进入首页-点击基本设置-店铺主页-点击关闭-判断是否关闭店铺主页"""
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-switch-handle'])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'确认关闭')])[1]")
        time.sleep(3)
        self.click("(//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap'])[14]")
        time.sleep(3)
        self.assert_element("(//span[contains(text(),'即刻启用')])[1]")
        time.sleep(3)
        self.click("//*[@id='menu-xqq1hUijrwA']/span/span/span/span")
        time.sleep(3)
        self.click("(//a[contains(text(),'我的主页')])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        self.assert_element("(//span[contains(text(),'去看看')])[1]")

    def test_store_homepage_open_the_homepage(self):
        """进入首页-点击基本设置-店铺主页-点击开启-判断是否开启店铺主页"""
        self.click("(//div[@class='kwaishop-cps-daren-match-pc-switch-handle'])[1]")
        time.sleep(3)
        self.click("(//a[contains(text(),'我的主页')])[1]")
        time.sleep(3)
        self.switch_to_newest_window()
        self.assert_element("(//span[contains(text(),'粉丝数')])[1]")