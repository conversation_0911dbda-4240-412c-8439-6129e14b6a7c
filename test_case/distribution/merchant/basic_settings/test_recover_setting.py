import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
from utils.apiUtils import ApiTools

"""
基本设置-恢复设置
"""

api = ApiTools()


class TestRecoverSetting(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    def test_into_recover(self):
        """进入恢复设置页面，页面不白屏"""
        self.merchant_login("DISTRIBUTION_DOMAIN_PRT", 'wb_huoyangyang')
        self.sleep(2)
        self.click("//li[@id='menu-eASD8q2RveA']//span[@class='cps-main-badge']//span[1]")
        self.sleep(2)
        self.click("(//div[@class='kwaishop-cps-merchant-base-pc-pro-title-title'])[1]")
        # # 请求api的请求体
        # data = {
        #     "offset": 0,
        #     "limit": 10,
        #     "status": 3
        # }
        # res = api.api_for_app('wb_huoyangyang', 'https://prt-eshop-app.test.gifshow.com/gateway/distribute/app/recreate/promoter/cooperation/list', data)
        # print("运行结果: " + str(res))

    def test_into_recover_edit(self):
        """进入恢复设置页面，点击设置按钮"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.sleep(2)
        self.click("//li[@id='menu-eASD8q2RveA']//span[@class='cps-main-badge']//span[1]")
        self.sleep(2)
        self.click("(//button[@role='switch'])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'取 消')])[1]")

