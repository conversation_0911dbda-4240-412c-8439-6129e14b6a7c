import time
import re

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
达人黑名单页面测试用例
pytest test_case/distribution/merchant/index/test_black_promoter_page.py --headless -n=5
"""

class TestBlackPromoterPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    def base_login_and_into(self):
        """进入达人黑名单页面"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)
        self.click("li[id='menu-RTkyd20IuiA'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap']")
        time.sleep(1.5)

    @pytest.mark.p1
    def test_black_promoter_query_button(self):
        """
        判断查询按钮是否可点击
        """
        self.base_login_and_into()
        element = self.find_element("//span[contains(text(),'查 询')]")
        self.assert_true(self.is_element_clickable(element) == True)

    @pytest.mark.p1
    def test_black_promoter_reset_button(self):
        """
        达人ID输入内容后-点击重置-输入框被清空
        """
        self.base_login_and_into()
        inputE = self.find_element("input.kwaishop-cps-merchant-base-pc-input")
        self.assert_true("" == inputE.get_attribute("value"))
        self.assert_true("请输入达人ID" == inputE.get_attribute("placeholder"))

        self.type("input[placeholder='请输入达人ID']", "1275826823")

        self.assert_true(self.is_element_present("(//span[@class='kwaishop-cps-merchant-base-pc-input-suffix'])[1]"))
        self.assert_true("1275826823" == inputE.get_attribute("value"))

        self.click("(//span[contains(text(),'重 置')])[1]")
        time.sleep(1)
        inputE = self.find_element("input.kwaishop-cps-merchant-base-pc-input")
        self.assert_true("" == inputE.get_attribute("value"))

    @pytest.mark.p1
    def test_black_promoter_query_result(self):
        """
        输入达人ID后搜索-正常展示搜索达人
        """
        self.base_login_and_into()
        self.type("input[placeholder='请输入达人ID']", "1275826823")
        time.sleep(0.5)
        self.click("button.kwaishop-cps-merchant-base-pc-btn.kwaishop-cps-merchant-base-pc-btn-primary:nth-child(1)")
        time.sleep(2)
        # self.assert_text("共 1 条", "(//li[@class='kwaishop-cps-merchant-base-pc-pagination-total-text'])[1]")
        # self.assert_text("1275826823", "td:nth-child(2)")
        totalStr = self.get_text("(//li[@class='kwaishop-cps-merchant-base-pc-pagination-total-text'])[1]")
        pattern = r'共 \d+ 条'
        match = re.search(pattern, totalStr)
        self.assert_true(match, "数据不符合预期")
        self.find_element("(//td[normalize-space()='1275826823'])[1]")


    @pytest.mark.p1
    def test_black_promoter_add_black(self):
        """
        点击添加黑名单-弹出输入框
        """
        self.base_login_and_into()
        self.click("//span[contains(text(),'+ 添加黑名单')]")
        time.sleep(0.5)
        self.assert_text("新增黑名单", "//div[@id='rcDialogTitle0']")
        self.assert_text("请填写达人快手号或快手ID，支持批量填写，请用英文,隔开，每次最多20个",
                         "//div[@class='cps-materials-multi-input-placeholderTip cps-materials-multi-input-grayFont']")

    @pytest.mark.p1
    def test_black_promoter_tips(self):
        """
        黑名单横幅
        """
        self.base_login_and_into()
        self.assert_text("达人加入黑名单后，将无法上架推广店铺所有商品；若达人已经上架或正在推广某些店铺商品，系统将自动下架这些商品",
                         "//div[@class='kwaishop-cps-merchant-base-pc-alert-message']")
