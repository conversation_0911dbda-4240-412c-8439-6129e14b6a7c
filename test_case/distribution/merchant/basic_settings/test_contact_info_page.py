import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
基本设置-联系方式页面测试用例
pytest test_case/distribution/merchant/index/test_contact_info_page.py --headless -n=5
"""

class TestContactInfoPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    def base_login_and_into(self):
        """进入联系方式页面"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(2)
        self.click("(//span[contains(text(),'联系方式')])[1]")
        time.sleep(0.5)

    def test_contact_info_edit(self):
        """
        点击联系方式编辑-编辑弹窗
        """
        self.base_login_and_into()
        self.click("(//span[contains(text(),'编辑')])[1]")
        self.find_element("(//span[contains(text(),'获取验证码')])[1]")
        self.find_element("(//button[@class='cps-ant-btn cps-ant-btn-primary'])[1]")

