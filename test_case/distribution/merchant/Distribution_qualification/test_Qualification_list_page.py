import pytest

from test_case.distribution.distribution_base_case import *

"""
分销资质
分销资质-资质列表
pytest test_case/distribution/merchant/find_regimental_commander/test_head_inviting_investment.py --headless -n=5
"""


class TestFindTheRegimentalLeaderPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        time.sleep(3)

    @pytest.mark.p1
    def test_Qualification_list_page(self):
        """进入资质列表-判断是否进入资质列表页面"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.assert_text('资质列表', "//div[@class='inru-page__title']")


    @pytest.mark.p1
    def test_Qualification_audit_page(self):
        """进入资质列表-判断是否进入特殊达人带货审核页面"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="tab-1"]')
        self.sleep(2)
        self.assert_text('请到快手小店查看审核明细', "//div[@class='title']")

    @pytest.mark.p1
    def test_Skip_to_Fast_Hand_shop(self):
        """进入资质列表-进入特殊达人带货审核页面-点击去快手小店按钮-跳转页面正常"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="tab-1"]')
        self.sleep(2)
        self.click("//span[contains(text(),'去快手小店')]")
        self.sleep(2)
        self.assert_text('主动申报', "//span[contains(text(),'主动申报')]")

    @pytest.mark.p1
    def test_Qualification_View_data(self):
        """进入资质列表-点击'查看资料'按钮-判断是否跳转正常"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.click('//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div[2]/div[2]/div/div[4]/div[2]/table/tbody/tr[1]/td[8]/div/a')
        self.sleep(2)
        self.assert_text('资质上传', "//span[contains(@class,'el-breadcrumb__item disabled')]//span[1]")
        # 校验提交按钮不可点击
        buttpn = self.find_element(
            "//button[@class='el-button el-button--primary el-button--small is-disabled']")
        assert buttpn.get_attribute('class') == "el-button el-button--primary el-button--small is-disabled"
        assert buttpn.get_attribute('disabled') == "true"

    @pytest.mark.p1
    def test_Qualification_reupload(self):
        """进入资质列表-点击'重新上传'按钮-判断是否跳转正常"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.click(
            '//*[@id="__qiankun_microapp_wrapper_for_kwaishop_cps_pc_micro_merchant__"]/div/section/section/main/div/div[2]/div[2]/div[2]/div/div[4]/div[2]/table/tbody/tr[4]/td[8]/div/a')
        self.sleep(2)
        self.assert_text('资质上传', "//span[contains(@class,'el-breadcrumb__item disabled')]//span[1]")
        #校验提交按钮可点击
        button = self.find_element(
            "//button[@class='el-button el-button--primary el-button--small']")
        assert button.get_attribute('class') == "el-button el-button--primary el-button--small"
        assert button.get_attribute('disabled') is None


    @pytest.mark.p1
    def test_Qualification_Category_screening(self):
        """进入资质列表-根据类目筛选，是否列表展示正常"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.click(
            "//div[@path='categoryId']//input[@placeholder='请选择']")
        self.sleep(2)
        self.click("//span[contains(text(),'鲜花')]")
        self.sleep(2)

    @pytest.mark.p1
    def test_Qualification_status_screening(self):
        """进入资质列表-根据状态筛选，是否列表展示正常"""
        self.click('//*[@id="menu-SKFlK5I9eyE"]/span/span/span/span')
        self.sleep(2)
        self.click(
            "//div[@path='auditStatus']//input[@placeholder='请选择']")
        self.sleep(2)
        self.click("//div[@x-placement='bottom-start']//li[1]")
        self.sleep(2)
