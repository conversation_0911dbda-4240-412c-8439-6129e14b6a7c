"""
针对分销商家所有页面进行视觉回归检测
将最新版本的网页与现有基线进行比较

page_url.txt -> 当前窗口的 URL
baseline.png -> 基线屏幕截图 （PNG）
tags_level1.txt -> HTML 标签
tags_level2.txt -> HTML 标签 + 属性名称
tags_level3.txt -> HTML 标签 + 属性名称 + 值 【最严格的模式，不建议使用，因为有些值是变化的会报错】
"""
import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest


class TestMerchantVisualBaseline(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        time.sleep(3)

    @pytest.mark.p1
    def test_distribution_index_visual_baseline(self):
        """ 首页 """
        self.check_window(name="home_page", level=2)
