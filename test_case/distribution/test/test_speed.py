from selenium import webdriver
import time

# 创建一个新的WebDriver实例，使用之前下载的ChromeDriver
driver = webdriver.Chrome()

# 打开一个网页
driver.get('https://zhuanlan.zhihu.com/p/584767348')

# 执行一些操作

# 获取并打印页面加载时间
start_time = driver.execute_script("return performance.timing.fetchStart")

end_time = int(time.time() * 1000)
print("Page startLoad time: ",end_time)
print("Page startLoad time: ", start_time)
print("Page load time: ", end_time - start_time)

# 关闭浏览器窗口
driver.quit()
