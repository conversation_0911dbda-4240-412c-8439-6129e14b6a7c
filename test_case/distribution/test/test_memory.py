# from selenium import webdriver
# import json
#
# # 启动 Chrome 浏览器并启用 DevTools
# options = webdriver.ChromeOptions()
# options.add_argument("--auto-open-devtools-for-tabs")
# driver = webdriver.Chrome(options=options)
#
# # 访问网页
# driver.get('https://www.baidu.com')
#
# # 执行 DevTools 命令以获取内存使用情况
# def get_memory_usage(driver):
#     driver.execute_cdp_cmd('Performance.enable', {})
#     metrics = driver.execute_cdp_cmd('Performance.getMetrics', {})
#     memory_usage = metrics['metrics']
#     return memory_usage
#
# memory_data = get_memory_usage(driver)
# print(json.dumps(memory_data, indent=2))
#
# # 关闭浏览器
# driver.quit()





# from selenium import webdriver
# import json
# import time
#
# # 启动 Chrome 浏览器
# options = webdriver.ChromeOptions()
# driver = webdriver.Chrome(options=options)
#
# # 访问多个页面
# pages = [
#     'https://www.baidu.com',
#     'https://www.baidu.com',
#     'https://zhuanlan.zhihu.com'  # 最后一个页面
# ]
#
# for page in pages:
#     driver.get(page)
#     time.sleep(2)  # 等待页面加载
#     driver.execute_cdp_cmd('Performance.enable', {})  # 启用性能监控
#
# # 获取最后一个页面的内存使用情况
# metrics = driver.execute_cdp_cmd('Performance.getMetrics', {})
# js_heap_used_size = next((m['value'] for m in metrics['metrics'] if m['name'] == 'JSHeapUsedSize'), None)
#
# print(f"最后访问页面的 JSHeapUsedSize: {js_heap_used_size} 字节")
#
# # 关闭浏览器
# driver.quit()


# from selenium import webdriver
# import time
# import json
#
# # 启动 Chrome 浏览器
# options = webdriver.ChromeOptions()
# options.add_argument("--auto-open-devtools-for-tabs")  # 可选：自动打开 DevTools
# driver = webdriver.Chrome(options=options)
#
# # 启用性能监控
# driver.execute_cdp_cmd('Performance.enable', {})
#
# # 访问目标页面
# driver.get("https://baidu.com")
#
# # 等待页面加载
# time.sleep(5)  # 根据需要调整等待时间
#
# # 获取性能数据
# performance_data = driver.execute_cdp_cmd('Performance.getMetrics', {})
# js_heap_memory = None
#
# # 从性能数据中提取 JavaScript 堆内存使用情况
# for metric in performance_data['metrics']:
#     if metric['name'] == 'JSHeapUsedSize':
#         js_heap_memory = metric['value']
#
# # 输出 JavaScript 堆内存使用量
# print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
#
# # 关闭浏览器
# driver.quit()


from seleniumbase import BaseCase
import time

class MyTestClass(BaseCase):
    def test_memory_usage(self):
        # 启动 Chrome 浏览器
        self.open("https://baidu.com")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(5)  # 根据需要调整等待时间

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")

# 运行测试
if __name__ == "__main__":
    import unittest
    unittest.main()