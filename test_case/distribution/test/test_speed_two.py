from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

# 启动浏览器
options = webdriver.ChromeOptions()
options.add_argument("--headless")  # 无头模式
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

# 增加脚本超时时间（单位为毫秒）
driver.set_script_timeout(3000)

# 定义要访问的 URL
url = 'https://www.baidu.com'

# 执行 JavaScript 获取 LCP 和 onload
driver.get(url)

# 添加 JavaScript 代码来监测 LCP 和 onload
try:
    result = driver.execute_script("""
        return new Promise((resolve) => {
            let lcp;
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                lcp = entries[entries.length - 1] ? entries[entries.length - 1].startTime : null;
            }).observe({ type: 'largest-contentful-paint', buffered: true });

            // 监测 onload 事件
            window.onload = function() {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                resolve({ lcp: lcp, onload: loadTime });
            };
        });
    """)

    # 等待一段时间以确保页面加载完成
    time.sleep(5)  # 根据需要调整等待时间

    # 输出结果
    print(f'LCP: {result["lcp"]} ms')
    print(f'Onload: {result["loadTime"]} ms')

except Exception as e:
    print(f"Error: {e}")

# 关闭浏览器
driver.quit()