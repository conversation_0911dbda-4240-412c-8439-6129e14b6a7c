import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest


def select_option(self, param, direction):
    pass


class TestInvoiceReview(DistributionToolTest):

    @pytest.mark.p1
    def test_promter_InvoiceCenter(self):
        """
        发票中心：判断是否进入商家发票审核页面
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-UU-EHo1Z4ao'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(3)
        self.assert_element("//div[@class='invoice-mui-modal-body']")
        self.click("//span[contains(text(),'知道了')]")
        time.sleep(3)
        self.assert_text("商家发票审核", '//*[@id="root"]/section/main/div[1]/div[1]/div/span')

    @pytest.mark.p1
    def test_promter_InvoiceReviewTab(self):
        """
        发票中心：判断商家发票审核页面的“待处理发票”tab下面的“查询结果”tab切换是否成功
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-UU-EHo1Z4ao'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(3)
        self.assert_element("//div[@class='invoice-mui-modal-body']")
        self.click("//span[contains(text(),'知道了')]")
        time.sleep(2)
        self.click("//span[contains(text(),'待处理发票账单')]")
        time.sleep(2)
        self.assert_text("账单ID", "//th[contains(text(),'账单ID')]")

    @pytest.mark.p1
    def test_promter_MakeAnOffer(self):
        """
        发票中心：判断商家开票在线邀约页面渲染是否成功
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-te1rgOZEpMY'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.assert_text("商家开票邀约", '//*[@id="root"]/section/main/div[1]/div')

    @pytest.mark.p1
    def test_promter_PlatformBilling(self):
        """
        发票中心：判断平台开票页面渲染是否成功
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-8bxBT7AIttU'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.assert_text("发票管理", "//h2[contains(text(),'发票管理')]")

    @pytest.mark.p1
    def test_promter_PlatformBillingTab(self):
        """
        发票中心：判断平台开票页面tab切换
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-8bxBT7AIttU'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@aria-selected='false']")
        time.sleep(2)
        self.assert_text("申请时间", "//th[contains(text(),'申请时间')]")
        self.assert_text("开票主体", "//div[@class='invoice-mui-table invoice-mui-table-empty']//th[@class='invoice-mui-table-cell'][contains(text(),'开票主体')]")
        self.assert_text("业务类型", "//div[@class='invoice-mui-table invoice-mui-table-empty']//th[@class='invoice-mui-table-cell'][contains(text(),'业务类型')]")
        self.assert_text("操作类型", "//th[contains(text(),'操作类型')]")
        self.assert_text("金额(元)", "//div[@class='invoice-mui-table invoice-mui-table-empty']//th[@class='invoice-mui-table-cell'][contains(text(),'金额(元)')]")
        self.assert_text("发票类型", "//th[contains(text(),'发票类型')]")
        self.assert_text("接收方式", "//th[contains(text(),'接收方式')]")
        self.assert_text("发票状态", "//th[contains(text(),'发票状态')]")
        self.assert_text("操作", '//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[9]')


