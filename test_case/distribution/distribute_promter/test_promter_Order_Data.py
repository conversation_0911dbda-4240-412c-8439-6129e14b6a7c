import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest


def select_option(self, param, direction):
    pass


class TestOrderData(DistributionToolTest):

    def go_to_orderPage(self):
        """
        进入订单列表页
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(1)
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)




    @pytest.mark.p1
    def test_promter_OrderDataTab(self):
        """
        订单数据：点击二创授权推广tab，页面渲染是否正常
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click("#rc-tabs-0-tab-2")
        time.sleep(2)
        self.assert_text("订单列表", "//div[@class='kwaishop-cps-pc-micro-promoter-base-pro-title-subtitle']")

    @pytest.mark.p1
    def test_promoter_order_tab_change(self):
        """
        订单数据：验证普通推广订单和二创授权推广订单tab切换正确
        """
        self.go_to_orderPage()
        elementCommon = self.find_element("(//div[@id='rc-tabs-0-tab-1'])[1]")
        elementClip = self.find_element("(//div[@id='rc-tabs-0-tab-2'])[1]")
        # 默认在普通推广下
        # print(element.get_attribute("outerHTML"))
        self.assert_true(self.is_element_expected(elementCommon, {"aria-selected": "true"}))
        self.assert_true(self.is_element_expected(elementClip, {"aria-selected": "false"}))
        self.assert_text("奖励收入", "th:nth-child(24)")
        # 切换到二创授权推广tab
        self.click("#rc-tabs-0-tab-2")
        time.sleep(2)
        self.assert_true(self.is_element_expected(elementCommon, {"aria-selected": "false"}))
        self.assert_true(self.is_element_expected(elementClip, {"aria-selected": "true"}))
        self.assert_element_absent("th:nth-child(24)")
        # 再切回普通推广
        self.click("#rc-tabs-0-tab-1")
        time.sleep(2)
        self.assert_true(self.is_element_expected(elementCommon, {"aria-selected": "true"}))
        self.assert_true(self.is_element_expected(elementClip, {"aria-selected": "false"}))
        self.assert_text("奖励收入", "th:nth-child(24)")

    @pytest.mark.p1
    def test_promoter_field_common(self):
        """
        订单数据：检查达人普通推广订单列表的字段
        """
        self.go_to_orderPage()
        maps = {
            "下单时间": "th:nth-child(1)",
            "付款时间": "th:nth-child(2)",
            "是否发货": "th:nth-child(3)",
            "发货时间": "th:nth-child(4)",
            "收货时间": "th:nth-child(5)",
            "结算时间": "th:nth-child(6)",
            "结算金额": "th:nth-child(7)",
            "实付金额": "th:nth-child(8)",
            "订单ID": "th:nth-child(9)",
            "PID": "th:nth-child(10)",
            "商品ID": "th:nth-child(11)",
            "商品信息": "th:nth-child(12)",
            "商家名称": "th:nth-child(13)",
            "订单状态": "//div[contains(text(),'订单状态')]",
            "订单金额(元)": "th:nth-child(15)",
            "计佣金额(元)": "th:nth-child(16)",
            "佣金比例": "th:nth-child(17)",
            "分成比例": "th:nth-child(18)",
            "MCN机构抽成": "th:nth-child(19)",
            "预估收入(元)": "//div[contains(text(),'预估收入(元)')]",
            "订单类型": "th:nth-child(21)",
            "技术服务费(元)": "th:nth-child(22)",
            "接单收入": "//div[@class='kwaishop-cps-pc-micro-promoter-base-pro-table-column-tip-title-left'][contains(text(),'接单收入')]",
            "奖励收入": "//div[contains(text(),'奖励收入')]"
        }
        for title, locate in maps.items():
            self.assert_text(title, locate)

    @pytest.mark.p1
    def test_promoter_field_clip(self):
        """
        订单数据：检查达人二创授权推广订单列表的字段
        """
        self.go_to_orderPage()
        # 切换到二创授权推广tab
        self.click("#rc-tabs-0-tab-2")
        time.sleep(2)
        maps = {
            "下单时间": "th:nth-child(1)",
            "付款时间": "th:nth-child(2)",
            "是否发货": "th:nth-child(3)",
            "发货时间": "th:nth-child(4)",
            "收货时间": "th:nth-child(5)",
            "结算时间": "th:nth-child(6)",
            "结算金额": "th:nth-child(7)",
            "实付金额": "th:nth-child(8)",
            "订单ID": "th:nth-child(9)",
            "PID": "th:nth-child(10)",
            "商品ID": "th:nth-child(11)",
            "商品信息": "th:nth-child(12)",
            "商家名称": "th:nth-child(13)",
            "订单状态": "//div[contains(text(),'订单状态')]",
            "订单金额(元)": "th:nth-child(15)",
            "计佣金额(元)": "th:nth-child(16)",
            "分佣比例": "//div[contains(text(),'分佣比例')]",
            "MCN机构抽成": "th:nth-child(18)",
            "预估收入(元)": "//div[contains(text(),'预估收入(元)')]",
            "订单类型": "th:nth-child(20)",
            "技术服务费(元)": "th:nth-child(21)",
        }
        for title, locate in maps.items():
            self.assert_text(title, locate)

    @pytest.mark.p1
    def test_promter_OrderTimeElement(self):
        """
        订单数据:下单时间组件的弹出
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-input kwaishop-cps-pc-micro-promoter-base-picker-input-active']")
        time.sleep(2)
        self.assert_element('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div/div/div/span/div[3]/div/div')

    @pytest.mark.p1
    def test_promter_OrderOrderInquiry(self):
        """
        订单数据:选择下单时间最近一个月查询订单
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-input kwaishop-cps-pc-micro-promoter-base-picker-input-active']")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-footer']//li[4]")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.assert_element(
            '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]')

    @pytest.mark.p1
    def test_promter_OrderStatusScreening(self):
        """
        订单数据:判断订单状态是否可以选择或点击
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-input kwaishop-cps-pc-micro-promoter-base-picker-input-active']")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-footer']//li[4]")
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div/div[1]')
        time.sleep(2)
        self.click("(//div[@title='已付款'])[1]")
        time.sleep(3)
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div/div[1]')
        time.sleep(2)
        self.click("//div[@title='已失效']")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        if self.is_element_visible('//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]'):
            self.assert_element(
                '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]')
        else:
            if self.is_element_visible("//div[contains(@class,'kwaishop-cps-pc-micro-promoter-base-empty-description')]"):
                self.assert_text("暂无数据", "//div[contains(@class,'kwaishop-cps-pc-micro-promoter-base-empty-description')]")



    @pytest.mark.p1
    def test_promter_PromotionTypeScreening(self):
        """
        订单数据:判断推广类型是否可以选择或点击
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-input kwaishop-cps-pc-micro-promoter-base-picker-input-active']")
        time.sleep(2)
        self.click("//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-footer']//li[4]")
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]')
        time.sleep(2)
        self.click("//div[@title='普通推广']")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]')
        time.sleep(2)
        self.click("//div[@title='快赚客推广']")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]')
        time.sleep(2)
        self.click("//div[@title='二创授权推广']")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]')
        time.sleep(2)
        self.click("//div[@title='内容授权推广']")
        time.sleep(2)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(2)

    @pytest.mark.p1
    def test_promoter_is_deliver(self):
        """
        订单数据-普通推广订单:判断是否发货是否可以选择或点击
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        # self.click(
        #     "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-input kwaishop-cps-pc-micro-promoter-base-picker-input-active']")
        # time.sleep(2)
        # self.click("//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-footer']//li[4]")
        # time.sleep(1)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-row kwaishop-cps-pc-micro-promoter-base-pro-form-layout-row']//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//span[2]")
        time.sleep(0.5)
        elements = self.find_elements("div.kwaishop-cps-pc-micro-promoter-base-select-item")
        for expectedStr, element in zip(["全部", "是", "否"], elements):
            self.assert_true(expectedStr == element.text)
        self.click("//div[@title='全部']")
        self.assert_true("true" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-row kwaishop-cps-pc-micro-promoter-base-pro-form-layout-row']//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//span[2]")
        time.sleep(0.5)
        self.click("//div[@title='是']")
        self.assert_true("true" == self.get_attribute("//div[@title='是']", "aria-selected"))
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-row kwaishop-cps-pc-micro-promoter-base-pro-form-layout-row']//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//span[2]")
        time.sleep(0.5)
        self.click("//div[@title='否']")
        self.assert_true("true" == self.get_attribute("//div[@title='否']", "aria-selected"))

    @pytest.mark.p1
    def test_promter_DerivedList(self):
        """
        订单数据:查看导出列表
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click("//span[contains(text(),'查看导出列表')]")
        time.sleep(2)
        self.assert_text("已导出的订单列表", "//div[@class='kwaishop-cps-pc-micro-promoter-base-pro-title-title']")
        time.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div/div[3]/div')

    @pytest.mark.p1
    def test_promoter_DerivedList_order_type(self):
        """
        订单数据-查看导出列表-类型切换:判断类型可正常切换
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click("//span[contains(text(),'查看导出列表')]")
        time.sleep(2)
        self.assert_text("下单时间", "(//th[contains(text(),'下单时间')])[1]")
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        self.assert_text("结算时间", "(//th[contains(text(),'结算时间')])[1]")
        self.assert_true("false" == self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected"))
        self.assert_true("true" == self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected"))

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_DerivedList_next_page(self):
        """
        订单数据-查看导出列表-下一页:判断是否可以正常翻页
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.sleep(2)
        self.click("//span[contains(text(),'查看导出列表')]")
        self.sleep(2)
        firstPage = self.find_element("(//li[@title='1'])[1]")
        firstPageClass = firstPage.get_attribute("class")
        self.assert_true("kwaishop-cps-pc-micro-promoter-base-pagination-item-active" in firstPageClass)
        firstPageStr = self.get_text("//tbody/tr[1]/td[2]")

        self.click("(//button[@type='button'])[14]")
        self.sleep(3)
        secondPage = self.find_element("(//li[@title='2'])[1]")
        secondPageClass = secondPage.get_attribute("class")
        self.assert_true("kwaishop-cps-pc-micro-promoter-base-pagination-item-active" in secondPageClass)
        secondPageStr = self.get_text("//tbody/tr[1]/td[2]")
        self.assert_true(firstPageStr != secondPageStr)

        firstPage = self.find_element("(//li[@title='1'])[1]")
        firstPageClass = firstPage.get_attribute("class")
        self.assert_true("kwaishop-cps-pc-micro-promoter-base-pagination-item-active" not in firstPageClass)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_DerivedList_appoint_page(self):
        """
        订单数据-查看导出列表-指定页:判断是否可以正常翻页
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.sleep(2)
        self.click("//span[contains(text(),'查看导出列表')]")
        self.sleep(2)
        firstPage = self.find_element("(//li[@title='1'])[1]")
        firstPageClass = firstPage.get_attribute("class")
        self.assert_true("kwaishop-cps-pc-micro-promoter-base-pagination-item-active" in firstPageClass)
        firstPageStr = self.get_text("//tbody/tr[1]/td[2]")

        self.click("(//li[@title='3'])[1]")
        self.sleep(0.5)
        nextPage = self.find_element("(//li[@title='3'])[1]")
        nextPageClass = nextPage.get_attribute("class")
        self.assert_true("kwaishop-cps-pc-micro-promoter-base-pagination-item-active" in nextPageClass)
        nextPageStr = self.get_text("//tbody/tr[1]/td[2]")
        self.assert_true(firstPageStr != nextPageStr)

        firstPage = self.find_element("(//li[@title='1'])[1]")
        firstPageClass = firstPage.get_attribute("class")
        self.assert_true("kwaishop-cps-pc-micro-promoter-base-pagination-item-active" not in firstPageClass)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promter_OrderBulkExport(self):
        """
        订单数据:批量导出，判断是否弹出「申请导出成功」
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click("//span[contains(text(),'批量导出')]")
        time.sleep(2)
        self.assert_text("申请导出成功",
                         "//div[@class='kwaishop-cps-pc-micro-promoter-base-notification kwaishop-cps-pc-micro-promoter-base-notification-topRight']//div")
        time.sleep(2)
        self.assert_element("//div[@class='kwaishop-cps-pc-micro-promoter-base-notification-notice-message']")
