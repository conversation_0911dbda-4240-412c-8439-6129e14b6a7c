import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
"""
快手优选专题页面
python3 -m pytest test_case/distribution/distribute_promter/test_promter_Talent_Deposit.py --headless -n=3
"""


def select_option(self, param, direction):
    pass


class TestTalentDeposit(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.sleep(3)



    @pytest.mark.p1
    def test_promter_ToggleTab(self):
        """
        类目切换
        """
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/div[1]/div[1]')
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-9"]')
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-1"]')
        time.sleep(2)
