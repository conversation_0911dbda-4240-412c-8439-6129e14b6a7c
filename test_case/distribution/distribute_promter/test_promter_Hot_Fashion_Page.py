import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
import time

"""
爆款榜页面
"""

@pytest.mark.skip
class TestpromterShortVideoPage(DistributionToolTest):



	def setUp(self, **kwargs):
		super().setUp()
		self.maximize_window()
		self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
		self.click("(//span[@class='index-module__goRank--Wetp5'])[1]")
		self.driver.switch_to.window(self.driver.window_handles[-1])
		self.assert_text("热销榜单",
		                 "//span[contains(text(),'热销榜单')]")
		time.sleep(3)

	"""
	判断榜单页面tab加载正常
	"""
	@pytest.mark.p1
	def test_hotfashionpage(self):
		self.assert_text("日榜","//div[contains(text(),'日榜')]")
		self.assert_text("周榜", "//div[contains(text(),'周榜')]")
		self.assert_text("月榜", "//div[contains(text(),'月榜')]")


	"""
	判断榜单页面tab切换正常
	"""
	@pytest.mark.p1
	def test_hotfashion_Toggle(self):
		self.click("//div[contains(text(),'周榜')]")
		time.sleep(2)
		self.assert_element('//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[1]')
		self.click("//div[contains(text(),'月榜')]")
		time.sleep(2)
		self.assert_element('//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[1]/div')


	"""
	卡片销量元素展示正常
	"""
	@pytest.mark.p1
	def test_Sales_Volume(self):
		self.assert_text("昨日销量",
		                 '//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[2]/div/div[2]/div[2]/div[1]/span')

	"""
	商品卡火苗图片展示正常
	"""
	@pytest.mark.p1
	def test_Flame_Picture(self):
		self.assert_text("昨日销量",'//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[2]/div/div[2]/div[2]/div[1]/span')
		img_xpath = [
			"//div[@class='index-module__content--jdLEy']//div[2]//div[1]//div[2]//div[2]//div[1]//img[1]"
		]
		for xpath in img_xpath:
			img = self.find_element(xpath)

			# 校验 width 和 height
			assert img.get_attribute("width") == "14", "宽度不正确"
			assert img.get_attribute("height") == "14", "高度不正确"

			# 校验 data-component-name
			outer_html = img.get_attribute("outerHTML")
			assert "cps-materials-picture" in outer_html, "缺少 data-component-name 属性"


			"""
			商品卡top标图片展示正常
			"""
	@pytest.mark.p1
	def test_TOP_Mark(self):
		img_xpath = ['//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[1]/div/div[1]/div/img']
		for xpath in img_xpath:
			img = self.find_element(xpath)

			# 校验 width 和 height
			assert img.get_attribute("width") == "28", "宽度不正确"
			assert img.get_attribute("height") == "39", "高度不正确"

			# 校验 data-component-name
			outer_html = img.get_attribute("outerHTML")
			assert "cps-materials-picture" in outer_html, "缺少 data-component-name 属性"


			"""
			榜单页top标展示正常
			"""
	@pytest.mark.p1
	def test_Decision_Page_TOP(self):
		self.click('//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[1]/div/div[2]')
		time.sleep(2)
		self.assert_element("//div[@class='index-module__rankInfo--_UvnE']")


		"""
		榜单页top标图片展示正常
		"""
	@pytest.mark.p1
	def test_Decision_Page_TOP_Mark(self):
		self.click('//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[1]/div/div[2]')
		time.sleep(2)
		self.assert_element('//*[@id="root"]/section/main/div/div[3]/div[2]/div[6]/div/img[1]')
		img_xpath = ['//*[@id="root"]/section/main/div/div[3]/div[2]/div[6]/div/img[1]']
		for xpath in img_xpath:
			img = self.find_element(xpath)

			# 校验 width 和 height
			assert img.get_attribute("width") == "16", "宽度不正确"
			assert img.get_attribute("height") == "16", "高度不正确"

			# 校验 data-component-name
			outer_html = img.get_attribute("outerHTML")
			assert "index-module__rankIcon--fR52o" in outer_html, "缺少 index-module__rankIcon--fR52o 属性"
