import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest


def select_option(self, param, direction):
    pass


class TestActivitySquare(DistributionToolTest):
    @pytest.mark.p1
    def test_promter_ActivitySquare(self):
        """
        活动广场：整体页面加载是否正常
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-Lawaj7_wGpM'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.assert_text("抢先参与", '//*[@id="rc-tabs-0-tab-1"]')

    @pytest.mark.p2
    def test_promter_ActivitySquareTab(self):
        """
        活动广场：点击tab判断切换是否正常
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-Lawaj7_wGpM'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-1"]')
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        time.sleep(2)
        self.assert_text("抢先参与", '//*[@id="rc-tabs-0-tab-1"]')

