import pytest
import time
from test_case.assistant.base import BaseCase
from test_case.distribution.distribution_base_case import DistributionToolTest

def select_option(self, param, direction):
    pass


class TestScheduledInvoice(DistributionToolTest):

	@pytest.mark.p1
	@pytest.mark.skip
	def test_promter_Invoice(self):
		"""
		聚力计划发票：聚力计划发票页面渲染是否正常
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
		self.driver.maximize_window()
		time.sleep(2)
		self.click("li[id='menu-X_5OJGwQwDE'] span[class='dilu-main-badge'] span:nth-child(1)")
		time.sleep(2)
		self.assert_text("发票管理", "//h3[contains(text(),'发票管理')]")


