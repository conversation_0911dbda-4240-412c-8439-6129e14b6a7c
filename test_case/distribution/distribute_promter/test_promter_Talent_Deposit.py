import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
达人保证金页面测试
python3 -m pytest test_case/distribution/distribute_promter/test_promter_Talent_Deposit.py --headless -n=3
"""


def select_option(self, param, direction):
    pass


class TestTalentDeposit(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.sleep(3)

    @pytest.mark.p0
    def test_promter_SecurityDeposit(self):
        """
        达人保证金页面渲染
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.assert_text("达人保证金", '//*[@id="root"]/div/div/div[1]/div[1]/span[1]')

    @pytest.mark.p0
    def test_promter_RechargeButton(self):
        """
        达人保证金：点击充值按钮
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'充 值')]")
        time.sleep(2)
        self.assert_text("达人保证金充值", "//h3[contains(text(),'达人保证金充值')]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_promter_scan_to_pay(self):
        """
        达人保证金：选择微信/支付宝扫码支付充值1元
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'充 值')]")
        time.sleep(2)
        self.click("//input[@placeholder='请输入金额']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入金额']", '1')
        time.sleep(2)
        self.click("//span[contains(text(),'微信/支付宝扫码支付')]")
        time.sleep(2)
        self.click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        time.sleep(2)
        self.assert_element(".ebank-content.svelte-15wz89r")
        self.assert_text("支付", "//h2[contains(text(),'支付')]")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_promter_BankingRecharge(self):
        """
        达人保证金：选择网银支付充值1元
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'充 值')]")
        time.sleep(2)
        self.click("//input[@placeholder='请输入金额']")
        time.sleep(2)
        self.type("//input[@placeholder='请输入金额']", '1')
        time.sleep(2)
        self.click("//span[contains(text(),'网银支付')]")
        time.sleep(2)
        self.click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        time.sleep(2)
        self.assert_text("支付", "//h2[contains(text(),'支付')]")

    @pytest.mark.p1
    def test_promter_BusinessType(self):
        """
        达人保证金：业务类型的展开和点击
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("(//span[@title='全部'][contains(text(),'全部')])[1]")
        time.sleep(0.3)
        self.click("//div[@title='资金转账']")
        time.sleep(0.3)
        self.click("(//span[@title='资金转账'][contains(text(),'资金转账')])[1]")
        time.sleep(0.3)
        self.click("//div[@title='资金冻结']")
        time.sleep(0.3)
        self.click("(//span[@title='资金冻结'][contains(text(),'资金冻结')])[1]")
        time.sleep(0.3)
        self.click("//div[@title='资金扣减']")
        time.sleep(0.3)
        self.click("(//span[@title='资金扣减'][contains(text(),'资金扣减')])[1]")
        time.sleep(0.3)
        self.click("//div[@title='提现']")
        time.sleep(0.3)
        self.click("(//span[@title='提现'][contains(text(),'提现')])[1]")
        time.sleep(0.3)
        self.click("//div[@title='充值']")
        time.sleep(0.3)

    @pytest.mark.p1
    def test_promter_FinancialDirection(self):
        """
        达人保证金：财务方向的展开和点击
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//div[@class='zone-fund-accounting-pro-form-layout-wrapper']//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//span[2]")
        time.sleep(2)
        self.click("//div[contains(text(),'收入')]")
        time.sleep(2)
        self.click("//div[@class='zone-fund-accounting-pro-form-layout-wrapper']//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//span[2]")
        time.sleep(2)
        self.click("//div[@title='支出']")
        time.sleep(2)

    @pytest.mark.p1
    def test_promter_TradingBours(self):
        """
        达人保证金：业务类型的展开和点击
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("(//span[@title='全部'][contains(text(),'全部')])[1]")
        time.sleep(2)
        # 这边以后可以考虑放全类型
        self.assert_element("(//div[@class='zone-fund-accounting-select-item-option-content'][contains(text(),'充值')])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promter_ExportBill(self):
        """
        达人保证金：导出月账单
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'导 出')]")
        time.sleep(2)
        self.click("//span[contains(text(),'月账单')]")
        time.sleep(2)
        self.type("//input[@id='time']", '2024-04')
        time.sleep(2)
        self.click("//div[@class='zone-fund-accounting-picker-input']//input[@id='time']")
        time.sleep(2)
        self.click("//td[@title='2024-04']//div[1]")
        time.sleep(2)
        self.assert_element("//div[@class='zone-fund-accounting-modal-content']")
        self.click("//span[contains(text(),'确认导出')]")

    @pytest.mark.p1
    def test_promter_DerivedRecord(self):
        """
        达人保证金：点击导出记录
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'导出记录')]")
        time.sleep(2)
        self.assert_text("已导出的报表", '//*[@id="root"]/div/div/div[1]')
        self.assert_element('//*[@id="root"]/div/div')

    @pytest.mark.p1
    def test_promter_DownloadButton(self):
        """
        达人保证金：点击导出记录
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'导出记录')]")
        time.sleep(2)
        self.click("//div[@id='__Kpro-workbench-scroll-content__']//div[2]//button[1]//span[1]")
        time.sleep(2)
        self.assert_text("已导出的报表", '//*[@id="root"]/div/div/div[1]')
        self.assert_element('//*[@id="root"]/div/div')

    @pytest.mark.p1
    def test_promter_ClickPaging(self):
        """
        达人保证金：点击导出记录，分页
        """
        self.click("li[id='menu-9wwA5jhGmzg'] span[class='kpro-workbench-layout-sider-v2__itemmenu-title']")
        time.sleep(2)
        self.click("//span[contains(text(),'导出记录')]")
        time.sleep(2)
        self.click("//div[@id='__Kpro-workbench-scroll-content__']")
        self.scroll_down(1)
        time.sleep(2)
        self.click("//a[normalize-space()='2']")
        time.sleep(2)
