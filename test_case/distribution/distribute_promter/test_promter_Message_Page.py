from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time

from test_case.assistant.base import BaseCase


# pc达人选品消息页面
@pytest.mark.skip
class TestDistributionPromterMessagePage(DistributionToolTest):

	@pytest.mark.p2
	def test_Message_Page(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.sleep(2)
		self.click('//*[@id="main_root"]/div/div/div/div[2]/div/div/div[2]/div/div[1]/span')
		self.sleep(3)
		if self.is_element_visible(
				"/html/body/div[3]/div/div/div"):
			img_xpath = ["/html/body/div[3]/div/div/div"]
			# 判断元素出现
			for xpath in img_xpath:
				img = self.find_element(xpath)
				outer_html = img.get_attribute("outerHTML")
				assert "azGh8yxZpUgBcK6NToVE" in outer_html, "缺少azGh8yxZpUgBcK6NToVE属性"
		else:
			self.click('//*[@id="main_root"]/div/div/div/div[2]/div/div/div[2]/div/div[1]/span')
			img_xpath = ["/html/body/div[3]/div/div/div"]
			# 判断元素出现
			for xpath in img_xpath:
				img = self.find_element(xpath)
				outer_html = img.get_attribute("outerHTML")
				assert "azGh8yxZpUgBcK6NToVE" in outer_html, "缺少azGh8yxZpUgBcK6NToVE属性"



	@pytest.mark.p2
	def test_Message_Copywriting1(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.sleep(2)
		self.click('//*[@id="main_root"]/div/div/div/div[2]/div/div/div[2]/div/div[1]/span')
		self.sleep(3)
		img_xpath = ["//body/div[3]/div[1]/div[1]"]
		# 判断元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "Yhp3w6DhfUP7gw64or7H" in outer_html, "缺少Yhp3w6DhfUP7gw64or7H属性"
		self.assert_text("消息公告",'/html/body/div[3]/div/div/div/div[1]/span[1]')


	@pytest.mark.p2
	def test_Message_Copywriting2(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.sleep(2)
		self.click('//*[@id="main_root"]/div/div/div/div[2]/div/div/div[2]/div/div[1]/span')
		self.sleep(3)
		img_xpath = ["/html/body/div[3]/div/div/div/div[1]/span[2]"]
		# 判断元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "tvSpn7oFkkg_RvpWnZsR" in outer_html, "缺少tvSpn7oFkkg_RvpWnZsR属性"
		self.assert_text("消息管理",'/html/body/div[3]/div/div/div/div[1]/span[2]')


	@pytest.mark.p2
	def test_Message_Copywriting3(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.sleep(2)
		self.click('//*[@id="main_root"]/div/div/div/div[2]/div/div/div[2]/div/div[1]/span')
		self.sleep(2)
		img_xpath = ["/html/body/div[3]/div/div/div/div[3]/a"]
		# 判断元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "HTjFEPJySSAth4zBdNPP" in outer_html, "缺少HTjFEPJySSAth4zBdNPP属性"
		self.assert_text("查看全部",'/html/body/div[3]/div/div/div/div[3]/a')



	@pytest.mark.p2
	def test_Message_Notification_Center(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.sleep(2)
		self.click('//*[@id="main_root"]/div/div/div/div[2]/div/div/div[2]/div/div[1]/span')
		self.sleep(2)
		self.click("/html/body/div[3]/div/div/div/div[3]")
		self.sleep(2)
		self.assert_text("通知中心", '//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[1]/span[1]')

	@pytest.mark.p2
	def test_Message_Page_click(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.sleep(2)
		self.click("//span[@aria-label='normal-remind-line']//*[name()='svg']")
		self.sleep(2)
		self.click('/html/body/div[3]/div/div/div/div[3]/a')
		self.sleep(2)
		self.click('//*[@id="root"]/div/div[1]/ul/li[2]')
		self.sleep(2)
		self.click('//*[@id="root"]/div/div[1]/ul/li[3]')
		self.sleep(2)
		self.click('//*[@id="root"]/div/div[1]/ul/li[4]')
