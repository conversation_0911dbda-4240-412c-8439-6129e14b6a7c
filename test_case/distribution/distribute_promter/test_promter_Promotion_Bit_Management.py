import pytest
import time
from test_case.assistant.base import BaseCase
from test_case.distribution.distribution_base_case import DistributionToolTest

def select_option(self, param, direction):
    pass


class TestPromotionBitManagement(DistributionToolTest):

	@pytest.mark.p1
	def test_promter_ExtensionManagement(self):
		"""
		推广位管理:判断是否进入推广位管理
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge']")
		time.sleep(2)
		self.assert_text("推广位管理", "//div[@class='kwaishop-cps-pc-micro-promoter-base-pro-title-subtitle']")
		self.assert_text("下载当前页Excel", "//span[contains(text(),'下载当前页Excel')]")
		self.assert_text("新建推广位", "//span[contains(text(),'新建推广位')]")

	@pytest.mark.p1
	def test_promoter_DownloadExcel(self):
		"""推广位管理：下载当前页excel"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge']")
		time.sleep(2)
		button = self.find_element("//button[@class='kwaishop-cps-pc-micro-promoter-base-btn']")
		# 判断按钮是否可以点击
		assert button.is_enabled() is True

	@pytest.mark.p1
	def test_promter_ClickToEdit(self):
		"""
		推广位管理:点击编辑，功能、页面渲染正常
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge']")
		time.sleep(2)
		self.click("//tbody/tr[1]/td[3]/div[1]/div[1]/button[1]/span[1]")
		time.sleep(2)
		self.assert_element("//div[@role='dialog']")
		self.assert_text("修改推广位", "//div[@class='kwaishop-cps-pc-micro-promoter-base-modal-header']")

	@pytest.mark.p1
	@pytest.mark.skip
	def test_promter_SwitchPage(self):
		"""
		推广位管理:分页
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge']")
		time.sleep(2)
		self.click("//li[@title='2']")
		time.sleep(2)
		self.click("//a[normalize-space()='3']")
		time.sleep(2)
		self.click("//a[normalize-space()='4']")
		time.sleep(2)
		self.click("//a[normalize-space()='5']")
		time.sleep(2)
		self.assert_element("//tbody/tr[3]/td[2]")

	@pytest.mark.p1
	def test_promoter_pid_page(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("(//li[@id='menu-7629rfg0wgU'])[1]")
		time.sleep(1.5)
		firstName = self.get_text("tbody tr:nth-child(1) td:nth-child(1)")
		self.click("li[title='下一页'] button[type='button']")
		time.sleep(0.5)
		secondName = self.get_text("tbody tr:nth-child(1) td:nth-child(1)")
		self.assert_true(firstName != secondName)

	@pytest.mark.p1
	def test_promter_NewPromotionBit(self):
		"""
		推广位管理:新建推广位,页面渲染正常
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge']")
		time.sleep(2)
		self.click("//span[contains(text(),'新建推广位')]")
		time.sleep(2)
		self.assert_element("//div[@role='dialog']")
		self.assert_text("新建推广位", "//div[@class='kwaishop-cps-pc-micro-promoter-base-modal-header']")
