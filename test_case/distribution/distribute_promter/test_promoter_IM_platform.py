import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
商达团沟通平台——达人工作台入口
"""

class TestPromoterIMPlatform(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.sleep(3)

    @pytest.mark.p1
    def test_promoter_home_page_entrance(self):
        self.click("//span[@class='dilu-main-badge']//img")
        # 判断是否进入客服消息系统
        self.assert_url("https://eshop-im.prt.kwaixiaodian.com/workbench/zone/cooperation?role=promoter")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_decision_page_entrance(self):
        # 进入选品首页
        self.click("(//span[@class='dilu-main-menu-title-content'])[1]")
        self.sleep(2)
        self.type("(//input[@id='rc_select_0'])[1]", "22137963403147")
        self.sleep(2)
        self.click("(//span[contains(text(),'搜索')])[1]")
        self.sleep(2)
        self.click("(//div[@class='index-module__goodsContainer--KRgmD'])[4]")
        self.sleep(2)
        self.click("(//span[contains(text(),'在线沟通')])[1]")
        self.click("(//div[@class='iconBody___rbEj5'][contains(text(),'会话')])[1]")

