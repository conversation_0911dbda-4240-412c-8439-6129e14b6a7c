from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time

from test_case.assistant.base import BaseCase

@pytest.mark.skip
# pc达人选品中心弹窗
class TestDistributionPromterCenterPopup(DistributionToolTest):

	@pytest.mark.p0
	def test_StrongControl_Popup(self):
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '杜蕾斯')
		self.sleep(2)
		self.click(
			"//span[contains(text(),'搜索')]")
		self.sleep(2)
# 加架按钮的三次元素定位
		if self.is_element_visible('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span'):
			self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[4]"):
				self.click("(//span[contains(text(),'加货架')])[4]")
			else:
				self.click("//div[@class='index-module__goodsContainer--vPEzH']//div[1]//div[1]//div[2]//div[2]//div[5]//button[1]//span[1]")
		self.sleep(2)
# 判断弹窗元素出现
		self.click("//span[contains(text(),'知道了')]")
		self.assert_text("该商品属于平台强管控类目，您当前无权限推广该商品，非常抱歉！",
		                 "//div[@class='cps-ant-modal-confirm-content']")
		img_xpath = ["/html/body/div[3]/div/div[2]/div/div[2]/div"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-body" in outer_html, "缺少 cps-ant-modal-body 属性"

	@pytest.mark.p0
	def test_promter_SecurityDeposit_Popup(self):
		"""
			达人保证金弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "zhudong")
		self.driver.maximize_window()
		self.click('//*[@id="rc_select_0"]')
		self.type('//*[@id="rc_select_0"]', '分销')
		self.sleep(2)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(2)
# 加架按钮的三次元素定位
		if self.is_element_visible('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span'):
			self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[4]"):
				self.click("(//span[contains(text(),'加货架')])[4]")
			else:
				self.click("//div[@class='index-module__goodsContainer--vPEzH']//div[1]//div[1]//div[2]//div[2]//div[5]//button[1]//span[1]")
		self.sleep(2)
# 判断弹窗元素出现
		self.assert_text("当前您的达人保证金余额小于标准额度，请及时缴纳后可继续操作。",
		                 "//div[contains(text(),'当前您的达人保证金余额小于标准额度，请及时缴纳后可继续操作。')]")
		img_xpath = ["/html/body/div[4]/div/div[2]/div/div[2]"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.click("//span[contains(text(),'去缴纳')]")
		self.assert_text("达人保证金",
		                 '//*[@id="root"]/div/div/div[1]/div[1]/span[1]')

	@pytest.mark.p0
	def test_promter_RiskAnchor_Popup(self):
		"""
			风险主播弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "maliya")
		self.driver.maximize_window()
		self.scroll_down(1)
		self.sleep(2)
# 加架按钮的三次元素定位
		if self.is_element_visible('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span'):
			self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[4]"):
				self.click("(//span[contains(text(),'加货架')])[4]")
			else:
				self.click("//div[@class='index-module__goodsContainer--vPEzH']//div[1]//div[1]//div[2]//div[2]//div[5]//button[1]//span[1]")
# 弹窗的三种情况，重点判断风险主播弹窗
		self.sleep(2)
		if self.is_element_visible("//div[@class='main-cont']"):
			self.assert_text(
				"「粉丝数大于等于50万」的达人仅可推广开通了“退货补运费”功能的商品哦，可联络供应商开通","//div[@class='main-cont']")
		else:
			if self.is_element_visible("//div[@class='cps-ant-modal-confirm-content']"):
				self.assert_text(
					"您选择商品的货主尚未签署《快手聚力计划服务协议》，因此您暂时无法上架该商品。您可联系货主，提示其尽快完成协议签署。",
					"//div[@class='cps-ant-modal-confirm-content']")
			else:
				if self.is_element_visible("//div[@class='title']"):
					self.assert_text(
						"商品加入分销货架后，需要货主将商品提交平台审核。审核通过后才能在直播间上车。请提醒货主送审商品。",
						"//div[@class='title']")
					img_xpath = ["//div[@class='cps-ant-modal-content']"]
# 判断弹窗元素出现
					for xpath in img_xpath:
						img = self.find_element(xpath)
						outer_html = img.get_attribute("outerHTML")
						assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
					self.click("//span[contains(text(),'取 消')]")





	@pytest.mark.p0
	def test_OrientationInvite_Popup(self):
		"""
			定向邀约类目弹窗(邮票)
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '23036959536492')
		time.sleep(2)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(2)
# 加架按钮的三次元素定位
		if self.is_element_visible('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[3]/button/span'):
			self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[3]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[3]"):
				self.click("(//span[contains(text(),'加货架')])[3]")
			else:
				self.click("//div[@class='index-module__flexColumn--sXz5x']//span[contains(text(),'加货架')]")
# 判断弹窗元素出现
		img_xpath = ["/html/body/div[3]/div/div[2]/div/div[2]"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		time.sleep(2)
		self.click("//span[contains(text(),'我知道了')]")
		self.assert_text("该类目分销推广仅向类目定向邀约达人开放，您暂未满足，无法推广。如想开通该类目售卖权限，请发邮箱咨询***************************",
		                 "//div[@class='cps-ant-modal-confirm-content']")

	@pytest.mark.p0
	def test_VeterinaryMedicine_Popup(self):
		"""
			不具备兽药资质弹窗弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "taoheliang")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '22921016433492')
		time.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(3)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[4]"):
				self.click("(//span[contains(text(),'加货架')])[4]")
			else:
				self.click("//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
		time.sleep(2)
# 判断弹窗元素出现
		img_xpath = ["/html/body/div[3]/div/div[2]/div/div[2]"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.click("//span[contains(text(),'知道了')]")
		self.assert_text("您当前不具备兽药带货资质，请添加官方微信：kuaishounongzi666申请资质",
		                 "//div[contains(text(),'您当前不具备兽药带货资质，请添加官方微信：kuaishounongzi666申请资质')]")
	@pytest.mark.p0
	@pytest.mark.skip
	def test_CommitmentLetter_Popup(self):
		"""
			医用承诺函弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "liuxiaohui07")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '美瞳')
		time.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(3)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[2]"):
				self.click("(//span[contains(text(),'加货架')])[2]")
			else:
				self.click(
					"//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
		time.sleep(2)
# 判断弹窗元素出现
		self.assert_text("承诺函",
		                 '//*[@id="rcDialogTitle0"]/div')
		img_xpath = ["//div[@role='document']"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"


	@pytest.mark.p0
	def test_FreightInsurance_Popup(self):
		"""
			50万粉丝运费险弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "maliya")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '23326982934133')
		time.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(3)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[3]"):
				self.click("(//span[contains(text(),'加货架')])[3]")
			else:
				self.click(
					"//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
		time.sleep(2)
# 判断弹窗元素出现
		img_xpath = ["//div[@class='cps-ant-modal-content']"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.assert_text("「粉丝数大于等于50万」的达人仅可推广开通了“退货补运费”功能的商品哦，可联络供应商开通",
		                 "//div[@class='main-cont']")

	@pytest.mark.p0
	def test_Ratepaying_Popup(self):
		"""
			纳税弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "nashui")
		self.driver.maximize_window()
		time.sleep(2)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[2]/div[5]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[4]"):
				self.click("(//span[contains(text(),'加货架')])[4]")
			else:
				self.click(
					"//div[@class='index-module__goodsContainer--vPEzH']//div[1]//div[1]//div[2]//div[2]//div[5]//button[1]//span[1]")
		time.sleep(2)
		self.assert_text("依法纳税是每个企业、公民的基本义务，快手小店在此郑重提醒您，您需要就带货佣金所得依法合规进行纳税申报。如您对纳税申报有任何疑问，请您咨询主管税务机关或者12366纳税服务热线!",
		                 "/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[1]/div/div")
# 判断弹窗元素出现
		img_xpath = ["//div[@class='cps-ant-modal-body']"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-body" in outer_html, "缺少 cps-ant-modal-body 属性"

	@pytest.mark.p0
	def test_VeterinaryDrugConsent_Popup(self):
		"""
			兽药承诺函弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '22921016433492')
		self.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[3]"):
				self.click("(//span[contains(text(),'加货架')])[3]")
			else:
				self.click(
					"//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
		time.sleep(2)
# 判断弹窗元素出现
		img_xpath = ["//div[@class='cps-ant-modal-content']"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.sleep(2)
		self.assert_text("【本承诺函为线上签约，我方在页面勾选即表示我方认可本承诺函内容】",
		                 "//div[contains(text(),'【本承诺函为线上签约，我方在页面勾选即表示我方认可本承诺函内容】')]")
		self.sleep(2)
		self.click("//span[contains(text(),'取 消')]")
		self.sleep(2)

	@pytest.mark.p0
	@pytest.mark.skip
	def test_PetMedicine_Popup(self):
		"""
			宠物药品弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
		self.driver.maximize_window()
		self.sleep(3)
		self.click('//*[@id="rc_select_0"]')
		time.sleep(3)
		self.type("//input[@placeholder='请输入商品名称']", '22962875328492')
		self.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		self.sleep(3)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[3]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[3]/button/span')
		else:
			if self.is_element_visible("//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]"):
				self.click("//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
			else:
				self.click(
					"//span[contains(text(),'加货架')]")
		time.sleep(2)
		img_xpath = ["//div[@class='cps-ant-modal-body']"]
# 判断弹窗元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-body" in outer_html, "缺少 cps-ant-modal-body 属性"
		self.assert_text("分销宠物药品需要达人满足历史带货【宠物用品】类目下商品，30日累计大于1万成交额且订单数量大于100单才可上架",
		                 "//div[@class='cps-ant-modal-confirm-content']")
		self.click("//span[contains(text(),'我知道了')]")


	@pytest.mark.p0
	@pytest.mark.skip
	def test_NoviceStage_Popup(self):
		"""
			成长期限单弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '23004844105492')
		self.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		self.sleep(3)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[3]"):
				self.click("(//span[contains(text(),'加货架')])[3]")
			else:
				self.click(
					"//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
		self.sleep(3)
		self.assert_text("该店铺正处于电商新手期，每日支付订单量上限约为1000单，请确认是否继续添加",
		                 "//div[contains(text(),'该店铺正处于电商新手期，每日支付订单量上限约为1000单，请确认是否继续添加')]")
# 判断弹窗元素出现
		img_xpath = ["//div[@role='dialog']"]
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-wrap" in outer_html, "缺少 cps-ant-modal-wrap 属性"
		self.click("//span[contains(text(),'取 消')]")

	@pytest.mark.p0
	@pytest.mark.skip
	def test_NutritionCommitmentLetter_Popup(self):
		"""
			保健品售卖承诺函弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '23004844105492')
		self.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		self.sleep(3)
		# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[2]"):
				self.click("(//span[contains(text(),'加货架')])[2]")
			else:
				self.click(
					"//div[@class='index-module__flexColumn--sXz5x']//span[contains(text(),'加货架')]")
		time.sleep(2)
		img_xpath = ["//div[@class='cps-ant-modal-content']"]
		# 判断弹窗元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.assert_text("保健品售卖承诺函",
		                 '//*[@id="rcDialogTitle0"]/div')

	@pytest.mark.p0
	def test_RestrictedNutrition_Popup(self):
		"""
			口碑分弹窗（营养健康类目）弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//input[@placeholder='请输入商品名称']")
		self.type("//input[@placeholder='请输入商品名称']", '23004844105492')
		time.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(2)
		self.scroll_down(1)
# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]/div[2]/div[4]/button/span')
		else:
			if self.is_element_visible("//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]"):
				self.click("//button[@cps-ant-click-animating-without-extra-node='false']//span[contains(text(),'加货架')]")
			else:
				self.click(
					"(//span[contains(text(),'加货架')])[2]")
		time.sleep(2)
		img_xpath = ["//div[@class='cps-ant-modal-content']"]
# 判断弹窗元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.assert_text("尊敬的带货达人/商家，按照平台规则，对于带货口碑分低于（含）4.0的达人（或者商家自播时的达人身份），限制带货营养健康类目。请注意带货口碑分的运营提升。",
		                 '/html/body/div[4]/div/div[2]/div/div[2]/div[2]/div')

	@pytest.mark.p0
	def test_BlackList_Popup(self):
		"""
			达人被加黑弹窗
			"""
		self.talent_login("DISTRIBUTION_PROMTER", "lizhiqiang06")
		self.driver.maximize_window()
		self.click('//*[@id="rc_select_0"]')
		self.type('//*[@id="rc_select_0"]', '24010735319133')
		time.sleep(3)
		self.click(
			"//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
		time.sleep(2)
		self.scroll_down(1)
		# 加架按钮的三次元素定位
		if self.is_element_visible(
				'//*[@id="root"]/section/main/div/div[1]/div/div[3]/div[2]/div/div/div/div/div/div[2]/div[2]/div[3]/button/span'):
			self.click(
				'//*[@id="root"]/section/main/div/div[1]/div/div[3]/div[2]/div/div/div/div/div/div[2]/div[2]/div[3]/button/span')
		else:
			if self.is_element_visible("(//span[contains(text(),'加货架')])[1]"):
				self.click("(//span[contains(text(),'加货架')])[1]")
			else:
				self.click(
					"//span[contains(text(),'加货架')]")
		time.sleep(2)
		img_xpath = ["//div[@class='cps-ant-modal-content']"]
		# 判断弹窗元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "cps-ant-modal-content" in outer_html, "缺少 cps-ant-modal-content 属性"
		self.assert_text("此商家限制你带货，请选择其他商家合作",
		                 "//div[contains(text(),'此商家限制你带货，请选择其他商家合作')]")