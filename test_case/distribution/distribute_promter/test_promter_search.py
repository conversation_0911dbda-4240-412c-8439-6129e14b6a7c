from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time

from test_case.assistant.base import BaseCase


# pc达人选品中心搜索场景及决策页场景
class TestDistributionPromterCenterSearch(DistributionToolTest):


    @pytest.mark.p0
    def test_distribution_search(self):
        self.driver.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')  # 打开达人登录地址，登录账号
        time.sleep(3)
        self.assert_title('分销达人')  # 判断登录页面标头，确认登录成功
        time.sleep(2)
        self.click('//*[@id="rc_select_0"]')
        self.type('//*[@id="rc_select_0"]', '男装')
        time.sleep(3)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(3)
        self.scroll_down(4)
        time.sleep(3)
        self.assert_element('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]') #判断这个位置的元素不为空  确保搜索返回数据
        self.click("//*[@id='root']/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]")
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_element('//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]/div[2]/div[1]')
        self.click("//div[contains(text(),'推广数据')]")
        self.click("//div[contains(text(),'用户评价')]")
        self.click("//div[contains(text(),'TOP销售榜')]")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]/div[3]/button/span[2]')
        time.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]')

    @pytest.mark.p1
    @pytest.mark.skip
    def test_distribution_search_by_id(self):
        """选品中心-根据商品id搜索"""
        self.driver.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')
        self.sleep(2)
        self.assert_title('分销达人') # 确认是否登录成功
        self.sleep(2)
        self.click('//*[@id="rc_select_0"]')
        self.sleep(2)
        self.type('//*[@id="rc_select_0"]', '23004844105492')
        self.sleep(2)
        self.click("//span[contains(text(),'搜索')]")
        self.sleep(2)
        self.scroll_down(1)
        self.sleep(2)
        self.click("//div[@class='index-module__flexColumn--sXz5x']//img")
        self.sleep(2)
        self.click("//div[contains(text(),'推广数据')]")
        self.sleep(2)
        self.click("//div[contains(text(),'用户评价')]")
        self.sleep(2)
        self.click("//div[contains(text(),'TOP销售榜')]")
        self.sleep(2)
        self.scroll_down(1)
        self.sleep(2)
        self.click("(//button[@class='cps-ant-btn cps-ant-btn-link index-module__button--GmPp9'])[1]")



class TestMyCooperatePromoter(DistributionToolTest):

    @pytest.mark.p0
    @pytest.mark.skip
    def test_promter_AddGoods(self):
        """
        pc达人选品中心加架
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click('//*[@id="rc_select_0"]')
        self.type('//*[@id="rc_select_0"]', '冬季')
        time.sleep(2)
        # 模拟按下回车键
        self.press_enter("//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(3)
        self.scroll_down(4)
        time.sleep(3)
        self.click("(//span[contains(text(),'加货架')])[14]")
        time.sleep(3)
        self.assert_text("加架成功")

    @pytest.mark.p1
    def test_promter_FullList(self):
        """
        选品中心-点击全部榜单
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("(//span[@class='index-module__goRank--Wetp5'])[1]")
        time.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[2]/div[3]/div/div[1]')

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promter_CategorySwitching(self):
        """
        选品中心-商品类目切换
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("//span[@aria-label='system-arrow-small-down-line']//*[name()='svg']")
        time.sleep(2)
        self.click("//span[contains(text(),'男装男鞋')]")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")
        self.click("//span[contains(text(),'食品饮料')]")
        time.sleep(2)
        self.scroll_down(1)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")
        self.click("//span[contains(text(),'珠宝配饰')]")
        time.sleep(2)
        self.scroll_down(1)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")

    @pytest.mark.p1
    def test_promter_AttributeCheck(self):
        """
        选品中心-商品属性勾选
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[4]/div[2]/div[2]/label[1]/span[2]/div')
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")
        self.click("//div[contains(text(),'品牌')]")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")

    @pytest.mark.p1
    def test_promter_PriceScreen(self):
        """
        选品中心-其他筛选-券后价筛选
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(2)
        self.type("(//input[@type='text'])[1]", "11")
        time.sleep(2)
        self.type("(//input[@type='text'])[2]", "22")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")

    @pytest.mark.p1
    def test_promter_CommissionScreen(self):
        """
        选品中心-其他筛选-佣金范围筛选
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(2)
        self.type("(//input[@type='text'])[3]", "10")
        time.sleep(2)
        self.type("(//input[@type='text'])[4]", "30")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")

    @pytest.mark.p1
    def test_promter_SalesScreen(self):
        """
        选品中心-其他筛选-销量筛选
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(2)
        self.type("(//input[@type='text'])[5]", "1")
        time.sleep(2)
        self.type("(//input[@type='text'])[6]", "50")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")

    @pytest.mark.p1
    def test_promter_SortScreen(self):
        """
        选品中心-排序规则
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[1]/div/label[2]/span[1]')
        time.sleep(2)
        self.click("//span[contains(text(),'佣金率')]")
        time.sleep(2)
        self.click("//span[contains(text(),'上架达人数')]")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.assert_element("//div[@class='cps-ant-spin-nested-loading']//div[6]")






