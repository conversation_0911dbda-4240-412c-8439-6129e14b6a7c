from test_case.distribution.distribution_base_case import DistributionToolTest

'''
获取一级菜单下的二级菜单&Tab
python3 -m pytest test_case/distribution/distribute_promter/test_promter_menu.py --headless -n=5
'''


class TestDistributionMenu(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.sleep(3)

    def test_distribution_menu(self):
        """ 登录成功-进入首页-判断首页元素菜单是否存在 """
        self.assert_title('分销达人')  # 判断登录页面标头，确认登录成功
        self.assert_text("搜索", "//span[contains(text(),'搜索')]")
        self.assert_text("活动广场", "//span[contains(text(),'活动广场')]")
        self.assert_text("效果看板", "//li[3]//div[1]//span[1]//span[1]//span[1]")
        self.assert_text("成交概览", "li[id='menu-wIl6Eg5saoc'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("发货情况", "li[id='menu-mH56d4yIHqY'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("订单数据", "li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("氛围图工具", "//li[5]//span[1]//span[1]//span[1]//span[1]")
        self.assert_text("推广位管理", "li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("转链工具", "li[id='menu-g8TQ4TGKyN4'] span[class='dilu-main-badge'] span:nth-child(1)")
        # self.assert_text("聚力计划发票", "li[id='menu-X_5OJGwQwDE'] span[class='dilu-main-badge'] span:nth-child(1)")
        # self.assert_text("发票中心",
        #                  "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/ul[1]/li[8]/div[1]/span[1]")
        self.assert_text("商家发票审核", "li[id='menu-UU-EHo1Z4ao'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("商家开票在线邀约",
                         "li[id='menu-te1rgOZEpMY'] span[class='dilu-main-badge'] span:nth-child(1)")
        # self.assert_text("平台开票", "li[id='menu-8bxBT7AIttU'] span[class='dilu-main-badge'] span:nth-child(1)")
        # self.assert_text("达人保证金", "li[id='menu-9wwA5jhGmzg'] span[class='dilu-main-badge'] span:nth-child(1)")
