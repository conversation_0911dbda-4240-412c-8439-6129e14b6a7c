import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
import time
"""
决策页元素校验
python3 -m pytest test_case/distribution/talent/test_distribution_topics.py --headless -n=3
"""


class TestDecisionPage(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.sleep(2)
        self.scroll_down(1)
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[1]/span')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_element('//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]/div[2]/div[1]')
        time.sleep(3)



#校验决策页主图大小
    @pytest.mark.p2
    def test_Product_decision_page(self):
        img_xpath = [
            '//*[@id="root"]/section/main/div/div[3]/div[1]/div[1]/picture/img'
        ]
        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "480", "宽度不正确"
            assert img.get_attribute("height") == "480", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "BizImage" in outer_html, "outerHTML 中未找到 BizImage"




    # 校验决策页小图大小
    @pytest.mark.p2
    def test_Product_decision_page1(self):
        img_xpath = [
            '//*[@id="root"]/section/main/div/div[3]/div[1]/div[2]/div[2]/div/div[1]/picture/img'
        ]
        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "70", "宽度不正确"
            assert img.get_attribute("height") == "70", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "BizImage" in outer_html, "outerHTML 中未找到 BizImage"


#校验店铺图片大小
    @pytest.mark.p2
    def test_Product_decision_Shop_picture(self):
        img_xpath = [
            '//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]/div[1]/img'
        ]
        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "44", "宽度不正确"
            assert img.get_attribute("height") == "44", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "AssetsImage" in outer_html, "outerHTML 中未找到 AssetsImage"
#标签元素存在
    def test_Product_decision_page_tag(self):
        self.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[3]/div[2]/div[3]/div[1]')
#加架按钮存在
    def test_Product_decision_page_Mounting_button(self):
        self.sleep(2)
        self.assert_element("//span[contains(text(),'加入货架')]")

# 商品价格存在
    def test_Product_decision_page_Price(self):
        self.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[3]/div[2]/div[4]/div[2]/div[2]')

# 商品佣金率存在
    def test_Product_decision_page_Commission_Rate(self):
        self.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[3]/div[2]/div[4]/div[3]/div[2]/div[1]')

# 商品物流存在
    def test_Product_decision_page_Material_Flow(self):
        self.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[3]/div[2]/div[4]/div[4]/div[2]/div[1]')

# 商品物流存在
    def test_Product_decision_page_Favorable_Rate(self):
        self.sleep(2)
        self.assert_element("(//div[@class='index-module__messageLine--OnrEn index-module__express--wSSXl'])[1]")

# 商品已售存在
    def test_Product_decision_page_Sold_Out(self):
        self.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[3]/div[2]/div[4]/div[4]/div[2]/div[1]')

# 商品上架达人数存在
    def test_Product_decision_page_Stacking_Personnel(self):
        self.sleep(2)
        if self.is_element_visible(
                "(//div[@class='index-module__text--DQ9rQ'])[5]"):
            self.assert_element("(//div[@class='index-module__text--DQ9rQ'])[5]")
        else:
            if self.is_element_visible("//div[@class='index-module__commonCard--VEPpb index-module__aggregationCard--u5w8V']//div[3]"):
                self.assert_element("//div[@class='index-module__commonCard--VEPpb index-module__aggregationCard--u5w8V']//div[3]")
    time.sleep(2)
# 商家名称存在
    def test_Product_decision_page_Merchant_Name(self):
        self.sleep(2)
        self.assert_element("(//div[@class='index-module__shopName--WRqJH'])[1]")

# 商家分销量存在
    def test_Product_decision_page_Subsales_Volume(self):
        self.assert_element("(//div[@class='index-module__text--feRTe'])[4]")

# 商家体验分存在
    def test_Product_decision_page_Experience_Score(self):
        self.sleep(2)
        self.assert_element("(//div[@class='index-module__title--uPzBR'])[1]")

class TestDecisionPageSpecialShow(DistributionToolTest):

    @pytest.mark.p1
    @pytest.mark.skip
    def test_GlobalMask_CommissionEscrow(self):
        """
			 店铺打全域屏蔽标        报平台托管+报快手优选活动，展示快手优选 跳到专题页
			 佣金托管商品
			"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("//input[@placeholder='请输入商品名称']")
        self.type("//input[@placeholder='请输入商品名称']", '23545104401288')
        time.sleep(2)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[1]/span')
        time.sleep(2)
        self.assert_text("快手优选", "//div[@class='index-module__shopName--fMsZm']")
        self.click("//span[contains(text(),'进店选品')]")
        self.assert_text("专题页", "//span[contains(text(),'专题页')]")


    @pytest.mark.p1
    @pytest.mark.skip
    def test_GlobalMask_EscrowAtPrice(self):
        """
			 店铺打全域屏蔽标        报平台托管+报快手优选活动，展示快手优选 跳到专题页
			 定价托管商品
			"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("//input[@placeholder='请输入商品名称']")
        self.type("//input[@placeholder='请输入商品名称']", '23545131851288')
        time.sleep(2)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[1]/span')
        time.sleep(2)
        self.assert_text("快手优选", "//div[@class='index-module__shopName--fMsZm']")
        self.click("//span[contains(text(),'进店选品')]")
        self.assert_text("专题页", "//span[contains(text(),'专题页')]")


    @pytest.mark.p1
    @pytest.mark.skip
    def test_NonglobalMask_EscrowAtPrice(self):
        """
			 不打全域屏蔽标   报平台托管品  ，展示货主信息
			 定价托管商品
			"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("//input[@placeholder='请输入商品名称']")
        self.type("//input[@placeholder='请输入商品名称']", '23317117128732')
        time.sleep(2)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[1]/span')
        time.sleep(2)
        self.assert_text("李飞飞-店铺", '//*[@id="root"]/section/main/div/div[4]/div[1]/div/div[1]/div/div[1]')
        self.click("//span[contains(text(),'进店选品')]")
        self.assert_text("李飞飞-店铺", "//span[contains(text(),'李飞飞-店铺')]")


    @pytest.mark.p1
    def test_NonglobalMask_CommissionEscrow(self):
        """
			 不打全域屏蔽标   报平台托管品  ，展示货主信息
			 佣金托管商品
			"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("//input[@placeholder='请输入商品名称']")
        self.type("//input[@placeholder='请输入商品名称']", '23317138546732')
        time.sleep(2)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[1]/span')
        time.sleep(2)
        self.assert_text("李飞飞-店铺", '//*[@id="root"]/section/main/div/div[4]/div[1]/div/div[1]/div/div[1]')
        self.click("//span[contains(text(),'进店选品')]")
        self.assert_text("李飞飞-店铺", "//span[contains(text(),'李飞飞-店铺')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_BigLinkPlatformProduct(self):
        """
			 大链接：平台品   展示快手优选跳动专题页
			"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        self.click("//input[@placeholder='请输入商品名称']")
        self.type("//input[@placeholder='请输入商品名称']", '23317188605637')
        self.sleep(2)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[2]/div[1]/span')
        self.sleep(2)
        self.assert_text("快手优选", "//div[@class='index-module__shopName--fMsZm']")
        self.click("//span[contains(text(),'进店选品')]")
        self.assert_text("专题页", "//span[contains(text(),'专题页')]")