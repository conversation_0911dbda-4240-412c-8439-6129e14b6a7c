import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
import time

"""
短视频页面
"""
class TestpromterShortVideoPage(DistributionToolTest):



	def setUp(self, **kwargs):
		super().setUp()
		self.maximize_window()
		self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
		self.click('//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/div[4]')
		self.driver.switch_to.window(self.driver.window_handles[-1])
		time.sleep(5)

	"""
	判断短视频页面分页和加载正常
	"""
	@pytest.mark.p1
	@pytest.mark.skip
	def test_shortvideopage(self):
		self.click("//div[contains(text(),'食品饮料')]")
		time.sleep(2)
		self.click("//div[contains(text(),'家居百货')]")
		img_xpath = ['//*[@id="root"]/section/main/div/div/div[2]/div[2]/div[3]/div[1]/div/div[1]']
		# 判断商品卡片元素出现
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "index-module__goodsContainer--fECeR" in outer_html, "缺少 index-module__goodsContainer--fECeR 属性"


	"""
	判断短视频弹窗正常弹出
	"""
	@pytest.mark.p1
	@pytest.mark.skip
	def test_shortvideo_popup(self):
		self.click('//*[@id="root"]/section/main/div/div[2]/div/div[2]/div[2]/div[2]/div/div[1]/div[1]/img')
		time.sleep(2)
		self.assert_element("//video[@class='xplayer-video']")
		time.sleep(2)
		# 判断商品卡片元素出现
		img_xpath = ['//*[@id="player-video"]/div']
		for xpath in img_xpath:
			img = self.find_element(xpath)
			outer_html = img.get_attribute("outerHTML")
			assert "xplayer-container" in outer_html, "缺少 xplayer-container 属性"
		self.click("//*[name()='path' and contains(@d,'M109.1 799')]")