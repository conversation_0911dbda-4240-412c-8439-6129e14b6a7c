import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest
"""
氛围图工具页面
python3 -m pytest test_case/distribution/distribute_promter/test_promter_Talent_Deposit.py --headless -n=3
"""


def select_option(self, param, direction):
    pass


class TestTalentDeposit(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.sleep(3)

    @pytest.mark.p1
    def test_promter_AtmosphereMap(self):
        """
        页面渲染
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.assert_text("主图合图", "//div[@role='tab']")
        self.assert_text("达人合图公告", "//div[@class='ant-pro-alert-carousel-header-title']")
        self.assert_text("合图模板", '//*[@id="rc-tabs-0-panel-main_image_composite"]/div[2]/div/div[1]')
        self.assert_text("合图素材", '//*[@id="rc-tabs-0-panel-main_image_composite"]/div[2]/div/div[2]')
        self.assert_text("模板类型", "//label[contains(text(),'模板类型')]")
        self.assert_text("使用模板", '//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button/span')

    @pytest.mark.p1
    def test_promter_AtmosphereMap_Picture(self):
        """
        合图模版页面-图片展示正常
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        img = self.find_element('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/picture/img')
        # 校验 width 和 height
        assert img.get_attribute("width") == "210", "宽度不正确"
        assert img.get_attribute("height") == "210", "高度不正确"
        # 校验 data-component-name
        outer_html = img.get_attribute("outerHTML")
        assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
        assert "data-component-version" in outer_html, "outerHTML 中未找到 data-component-version"




    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplatePopup(self):
        """
        点击使用模版按钮-弹窗展示正常
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible(
                '/html/body/div[3]/div/div[2]/div/div[2]'):
            self.assert_text("免责声明", '/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[1]/span[2]')
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[1]/span')
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.assert_text("选择商品", '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()')
                img = self.find_element('//*[@id="root"]/div/div/div[3]/div[2]/picture/img')
                # 校验 width 和 height
                assert img.get_attribute("width") == "240", "宽度不正确"
                assert img.get_attribute("height") == "240", "高度不正确"
                # 校验 data-component-name
                outer_html = img.get_attribute("outerHTML")
                assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
                assert "data-component-version" in outer_html, "outerHTML 中未找到 data-component-version"
    @pytest.mark.p1
    def test_promter_AtmosphereMap_SidePopupWindow(self):
        """
        点击使用模版按钮-点击同意并继续-侧边弹窗正常
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible("//div[@class='ant-modal-body']"):
            self.click("//span[contains(text(),'同意并继续')]")
            time.sleep(2)
            img = self.find_element("//div[@class='ant-drawer-content-wrapper']")
            outer_html = img.get_attribute("outerHTML")
            assert "ant-drawer-content" in outer_html, "缺少 ant-drawer-content-wrapper 属性"
        else:
            if self.is_element_visible("//div[@class='ant-drawer-content-wrapper']"):
                img = self.find_element("//div[@class='ant-drawer-content-wrapper']")
                outer_html = img.get_attribute("outerHTML")
                assert "ant-drawer-content" in outer_html, "缺少 ant-drawer-content-wrapper 属性"



    @pytest.mark.p1
    def test_promter_AtmosphereMap_SidePopupWindow_Toggle(self):
        """
        点击使用模版按钮-点击同意并继续-侧边弹窗图层管理切换
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible("//div[@class='ant-modal-body']"):
            self.click("//span[contains(text(),'同意并继续')]")
            time.sleep(2)
            self.click("//div[@class='ant-collapse-content ant-collapse-content-active']//div[2]")
            time.sleep(2)
            self.click('/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[3]')
            time.sleep(2)
            self.click('/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[4]')
            time.sleep(2)
            self.assert_text("文案1", "//div[@class='ant-collapse-content ant-collapse-content-active']//div[2]")
            self.assert_text("文案2", '/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[3]')
            self.assert_text("文案3",
                             '/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[4]')
        else:
            if self.is_element_visible("//div[@class='ant-drawer-content-wrapper']"):
                time.sleep(2)
                self.click("//div[@class='ant-collapse-content ant-collapse-content-active']//div[2]")
                time.sleep(2)
                self.click('/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[3]')
                time.sleep(2)
                self.click('/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[4]')
                time.sleep(2)
                self.assert_text("文案1", "//div[@class='ant-collapse-content ant-collapse-content-active']//div[2]")
                self.assert_text("文案2",
                                 '/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[3]')
                self.assert_text("文案3",
                                 '/html/div/div/div[2]/div/div/div[2]/div/div/div[1]/div[1]/div/div/div[2]/div/div[4]')


    @pytest.mark.p1
    def test_promter_AtmosphereMap_SidePopupWindow_Avatar(self):
        """
        点击使用模版按钮-点击同意并继续-侧边弹窗图层点击
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible("//div[@class='ant-modal-body']"):
            self.click("//span[contains(text(),'同意并继续')]")
            time.sleep(2)
            self.click('/html/div/div/div[2]/div/div/div[2]/div/div/div[2]/form/div/div[1]/div[2]/div[1]/div/div')
            time.sleep(2)
            img = self.find_element("//img[@class='ant-image-preview-img']")
            # 校验 width 和 height
            assert img.get_attribute("width") == "338", "宽度不正确"
            assert img.get_attribute("height") == "431", "高度不正确"
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "ant-image-preview-img" in outer_html, "缺少 ant-image-preview-img 属性"
        else:
            if self.is_element_visible("//div[@class='ant-drawer-content-wrapper']"):
                time.sleep(2)
                self.click('/html/div/div/div[2]/div/div/div[2]/div/div/div[2]/form/div/div[1]/div[2]/div[1]/div/div')
                time.sleep(2)
                img = self.find_element("//img[@class='ant-image-preview-img']")
                # 校验 width 和 height
                assert img.get_attribute("width") == "338", "宽度不正确"
                assert img.get_attribute("height") == "431", "高度不正确"
                # 校验 data-component-name
                outer_html = img.get_attribute("outerHTML")
                assert "ant-image-preview-img" in outer_html, "缺少 ant-image-preview-img 属性"

    @pytest.mark.p1
    def test_promter_AtmosphereMap_SidePopupWindow_MasterMap(self):
        """
        点击使用模版按钮-点击同意并继续-侧边弹窗主图图层
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible("//div[@class='ant-modal-body']"):
            self.click("//span[contains(text(),'同意并继续')]")
            time.sleep(2)
            img = self.find_element("//div[@class='ant-spin-nested-loading']//picture//img")
            # 校验 width 和 height
            assert img.get_attribute("width") == "414", "宽度不正确"
            assert img.get_attribute("height") == "414", "高度不正确"
        else:
            if self.is_element_visible("//div[@class='ant-drawer-content-wrapper']"):
                time.sleep(2)
                img = self.find_element("//div[@class='ant-spin-nested-loading']//picture//img")
                # 校验 width 和 height
                assert img.get_attribute("width") == "414", "宽度不正确"
                assert img.get_attribute("height") == "414", "高度不正确"

    @pytest.mark.p1
    # @pytest.mark.skip
    def test_promter_AtmosphereMap_SidePopupWindow_Thumbnail(self):
        """
        点击使用模版按钮-点击同意并继续-侧边弹窗小图图层
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'使用模板')])[1]")
        time.sleep(2)
        if self.is_element_visible("//div[@class='ant-modal-body']"):
            self.click("//span[contains(text(),'同意并继续')]")
            time.sleep(2)
            img = self.find_element("//img[@class='ant-image-img']")
            # 校验 width 和 height
            assert img.get_attribute("width") == "323", "宽度不正确"
            assert img.get_attribute("height") == "126", "高度不正确"
        else:
            if self.is_element_visible("//div[@class='ant-drawer-content-wrapper']"):
                time.sleep(2)
                img = self.find_element("//img[@class='ant-image-img']")
                # 校验 width 和 height
                assert img.get_attribute("width") == "323", "宽度不正确"
                assert img.get_attribute("height") == "126", "高度不正确"
    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplateListField(self):
        """
        点击使用模版按钮-点击添加商品按钮-列表字段
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible(
                '/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            self.click("//div[@class='ant-dropdown-trigger']")
            self.assert_text("商品信息", "//th[contains(text(),'商品信息')]")
            self.assert_text("主图", "/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[3]/div/div/div/div/div/div/div/div/div[1]/table/thead/tr/th[3]")
            self.assert_text("白底图", "//th[contains(text(),'白底图')]")
            self.assert_text("使用主图合成", "//span[contains(text(),'使用主图合成')]")
            self.assert_text("使用白底图合成", "//span[contains(text(),'使用白底图合成')]")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                self.click("//div[@class='ant-dropdown-trigger']")
                self.assert_text("商品信息", "//th[contains(text(),'商品信息')]")
                self.assert_text("主图",
                                 "/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[3]/div/div/div/div/div/div/div/div/div[1]/table/thead/tr/th[3]")
                self.assert_text("白底图", "//th[contains(text(),'白底图')]")
                self.assert_text("使用主图合成", "//span[contains(text(),'使用主图合成')]")
                self.assert_text("使用白底图合成", "//span[contains(text(),'使用白底图合成')]")
        time.sleep(2)

    @pytest.mark.p1
    def test_promter_AtmosphereMap_CraftChoose(self):
        """
		点击使用模版按钮-点击添加商品按钮-底图合成选择
		"""
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click(
            '//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible(
                '/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            self.click("//div[@class='ant-dropdown-trigger']")
            self.click("//span[contains(text(),'使用白底图合成')]")
            time.sleep(2)
            self.assert_text("确定批量白底图合成？", "//span[@class='ant-modal-confirm-title']")
            img = self.find_element("//div[@class='ant-modal ant-modal-confirm ant-modal-confirm-confirm']//div[@class='ant-modal-body']")
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "ant-modal-body" in outer_html, "缺少 ant-modal-body 属性"
            assert "ant-modal-confirm-body-wrapper" in outer_html, "outerHTML 中未找到 ant-modal-confirm-body-wrapper"

            self.click("//span[contains(text(),'确 认')]")
            self.click("//div[@class='ant-dropdown-trigger']")
            self.click("//span[contains(text(),'使用主图合成')]")
            self.assert_text("确定批量主图合成？", "//span[@class='ant-modal-confirm-title']")
            img = self.find_element(
                "//div[@class='ant-modal ant-modal-confirm ant-modal-confirm-confirm']//div[@class='ant-modal-content']")
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "ant-modal-body" in outer_html, "缺少 ant-modal-body 属性"
            assert "ant-modal-confirm-body-wrapper" in outer_html, "outerHTML 中未找到 ant-modal-confirm-body-wrapper"
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                self.click("//div[@class='ant-dropdown-trigger']")
                self.click("//span[contains(text(),'使用白底图合成')]")
                time.sleep(2)
                self.assert_text("确定批量白底图合成？", "//span[@class='ant-modal-confirm-title']")
                img = self.find_element(
                    "//div[@class='ant-modal ant-modal-confirm ant-modal-confirm-confirm']//div[@class='ant-modal-body']")
                # 校验 data-component-name
                outer_html = img.get_attribute("outerHTML")
                assert "ant-modal-body" in outer_html, "缺少 ant-modal-body 属性"
                assert "ant-modal-confirm-body-wrapper" in outer_html, "outerHTML 中未找到 ant-modal-confirm-body-wrapper"

                self.click("//span[contains(text(),'确 认')]")
                self.click("//div[@class='ant-dropdown-trigger']")
                self.click("//span[contains(text(),'使用主图合成')]")
                self.assert_text("确定批量主图合成？", "//span[@class='ant-modal-confirm-title']")
                img = self.find_element(
                    "//div[@class='ant-modal ant-modal-confirm ant-modal-confirm-confirm']//div[@class='ant-modal-content']")
                # 校验 data-component-name
                outer_html = img.get_attribute("outerHTML")
                assert "ant-modal-body" in outer_html, "缺少 ant-modal-body 属性"
                assert "ant-modal-confirm-body-wrapper" in outer_html, "outerHTML 中未找到 ant-modal-confirm-body-wrapper"

        time.sleep(2)
    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplateAddGoods(self):
        """
        点击使用模版按钮-点击添加商品按钮-弹窗正常
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible(
                '/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            img_xpath = ["//div[@class='ant-modal-content']"]
            for xpath in img_xpath:
                img = self.find_element(xpath)
                outer_html = img.get_attribute("outerHTML")
                assert "ant-modal-content" in outer_html, "缺少 ant-modal-content 属性"
            self.assert_text("添加商品","//div[@class='ant-modal-title']")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                img_xpath = ["//div[@class='ant-modal-content']"]
                for xpath in img_xpath:
                    img = self.find_element(xpath)
                    outer_html = img.get_attribute("outerHTML")
                    assert "ant-modal-content" in outer_html, "缺少 ant-modal-content 属性"
                self.assert_text("添加商品", "//div[@class='ant-modal-title']")
        time.sleep(2)

    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplateAddGoodsID(self):
        """
        点击使用模版按钮-点击添加商品按钮-商品ID筛选
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            self.type("//input[@placeholder='请输入商品id']", '23004844105492')
            time.sleep(2)
            self.click("//span[contains(text(),'查 询')]")
            self.assert_text("暂无数据", "//div[@class='ant-empty-description']")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                self.type("//input[@placeholder='请输入商品id']", '23004844105492')
                time.sleep(2)
                self.click("//span[contains(text(),'查 询')]")
                self.assert_text("暂无数据", "//div[@class='ant-empty-description']")
        time.sleep(2)

    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplateAddGoodsName(self):
        """
        点击使用模版按钮-点击添加商品按钮-商品名称筛选
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            self.type("//input[@placeholder='请输入名称']", '分销')
            time.sleep(2)
            self.click("//span[contains(text(),'查 询')]")
            self.assert_text("分销", "/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[3]/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[2]/div/div/span")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                self.type("//input[@placeholder='请输入名称']", '分销')
                time.sleep(2)
                self.click("//span[contains(text(),'查 询')]")
                self.assert_text("分销",
                                 "/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[3]/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[2]/div/div/span")
        time.sleep(2)
    @pytest.mark.p1
    def test_promter_AtmosphereMap_AddedProductPopupChooseAll(self):
        """
        点击使用模版按钮-点击添加商品按钮-商品选择-全选-清空
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            time.sleep(2)
            self.click("//div[@class='ant-table-selection']//span[@class='ant-checkbox-inner']")
            self.click("//span[contains(text(),'清空')]")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                time.sleep(2)
                self.click("//div[@class='ant-table-selection']//span[@class='ant-checkbox-inner']")
                self.click("//span[contains(text(),'清空')]")
        time.sleep(2)


    @pytest.mark.p1
    def test_promter_AtmosphereMap_AddedProductPopupSingleChoice(self):
        """
        点击使用模版按钮-点击添加商品按钮-商品选择-单选-清空
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            time.sleep(2)
            self.click("//tbody/tr[2]/td[1]/label[1]/span[1]/span[1]")
            self.click("//span[contains(text(),'清空')]")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                time.sleep(2)
                self.click("//tbody/tr[2]/td[1]/label[1]/span[1]/span[1]")
                self.click("//span[contains(text(),'清空')]")
        time.sleep(2)


    @pytest.mark.p1
    def test_promter_AtmosphereMap_AddedProductPopupChooseAll(self):
        """
        点击使用模版按钮-点击添加商品按钮-商品选择-全选-单个删除
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            time.sleep(2)
            self.click("//div[@class='ant-table-selection']//span[@class='ant-checkbox-inner']")
            self.click("//body/div/div[@class='ant-modal-root']/div[@role='dialog']/div[@role='document']/div[@class='ant-modal-content']/div[@class='ant-modal-body']/div[@class='_71QL6z7OsUHlgibjVoQA']/div[@class='Nue7i9xCtZ9bqQXOBhAl']/ul[@class='gvYDSbbG2oEwfD1Pw8aA']/li[1]/div[1]/div[1]/div[2]/span[1]//*[name()='svg']")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                time.sleep(2)
                self.click("//div[@class='ant-table-selection']//span[@class='ant-checkbox-inner']")
                self.click("//span[contains(text(),'清空')]")
        time.sleep(2)
    @pytest.mark.p1
    def test_promter_AtmosphereMap_Composite_Material(self):
        """
        合图素材页面页面渲染正常
        """
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[2]/div/div[2]')
        time.sleep(2)
        self.assert_text("合图素材","//div[@class='ant-pro-title-title']")
        self.assert_text("商品信息", "//th[contains(text(),'商品信息')]")
        self.assert_text("合图素材", "//th[contains(text(),'合图素材')]")
        self.assert_text("商品状态", "//th[contains(text(),'商品状态')]")
        self.assert_text("商家开放使用状态", "//th[contains(text(),'商家开放使用状态')]")
        self.assert_text("平台开放使用状态", "//th[contains(text(),'平台开放使用状态')]")
        self.assert_text("合图方式", "//th[contains(text(),'合图方式')]")
        self.assert_text("合图时间", "//th[contains(text(),'合图时间')]")
        self.assert_text("操作", "//th[@class='ant-table-cell ant-table-cell-fix-right ant-table-cell-fix-right-first']")










class TestEffectBoard(DistributionToolTest):

    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplate(self):
        """
        点击使用模版按钮-判断页面图片展示正常
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible(
                '/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            img = self.find_element('//*[@id="root"]/div/div/div[3]/div[2]/picture/img')
            # 校验 width 和 height
            assert img.get_attribute("width") == "240", "宽度不正确"
            assert img.get_attribute("height") == "240", "高度不正确"
            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "data-component-version" in outer_html, "outerHTML 中未找到 data-component-version"
        else:
            if self.is_element_visible('/html/div/div/div[2]/div/div/div[2]/div/div/div[2]/form/div/div[1]/div[1]'):
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.assert_text("选择商品", '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()')
                img = self.find_element('//*[@id="root"]/div/div/div[3]/div[2]/picture/img')
                # 校验 width 和 height
                assert img.get_attribute("width") == "240", "宽度不正确"
                assert img.get_attribute("height") == "240", "高度不正确"
                # 校验 data-component-name
                outer_html = img.get_attribute("outerHTML")
                assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
                assert "data-component-version" in outer_html, "outerHTML 中未找到 data-component-version"


    @pytest.mark.p1
    def test_promter_AtmosphereMap_UseTemplateAddGoodsPaging(self):
        """
        点击使用模版按钮-点击添加商品按钮-分页
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("//li[5]//span[1]//span[1]//span[1]//span[1]")
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-main_image_composite"]/div[3]/div/div/div[2]/div/div/div[1]/div[1]/div/button')
        time.sleep(2)
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
            self.click("//span[contains(text(),'添加商品0 / 50')]")
            self.click("//li[@title='2']")
        else:
            if self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div/text()'):
                self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
                self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/button/span')
                self.click("//span[contains(text(),'添加商品0 / 50')]")
                self.click("//li[@title='2']")
        time.sleep(2)
