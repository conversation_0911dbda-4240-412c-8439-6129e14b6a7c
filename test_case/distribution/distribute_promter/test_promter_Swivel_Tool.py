import pytest
import time
from test_case.assistant.base import BaseCase
from test_case.distribution.distribution_base_case import DistributionToolTest
from selenium.webdriver.common.keys import Keys
def select_option(self, param, direction):
    pass


class TestSwivelTool(DistributionToolTest):

	@pytest.mark.p1
	def test_promter_SwivelTool(self):
		"""
		转链工具:判断是否进入转链工具页面
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//li[@id='menu-g8TQ4TGKyN4']//span[@class='dilu-main-badge']//span[1]")
		time.sleep(2)
		self.assert_text("转链工具", '//*[@id="root"]/section/main/div/div')


	@pytest.mark.p1
	def test_promoter_SwivelTool_copy_link(self):
		"""
		转链工具-复制链接:判断是否提示复制成功
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//li[@id='menu-g8TQ4TGKyN4']//span[@class='dilu-main-badge']//span[1]")
		time.sleep(1.5)
		self.click("(//div[@class='kwaishop-cps-pc-micro-promoter-base-select-selector'])[2]")
		time.sleep(0.5)
		self.click("div.kwaishop-cps-pc-micro-promoter-base-select-item.kwaishop-cps-pc-micro-promoter-base-select-item-option:nth-child(2)")
		self.click("(//button[@type='button'])[2]")
		time.sleep(1)
		self.click("(//span[contains(text(),'确 定')])[1]")

	@pytest.mark.p1
	def test_promoter_SwivelTool_copy_code(self):
		"""
        转链工具-复制口令:判断是否提示复制成功
        """
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//li[@id='menu-g8TQ4TGKyN4']//span[@class='dilu-main-badge']//span[1]")
		time.sleep(1.5)
		self.click("(//div[@class='kwaishop-cps-pc-micro-promoter-base-select-selector'])[2]")
		time.sleep(0.5)
		self.click(
			"div.kwaishop-cps-pc-micro-promoter-base-select-item.kwaishop-cps-pc-micro-promoter-base-select-item-option:nth-child(2)")
		self.click("(//button[@type='button'])[2]")
		time.sleep(1)
		self.click("//div[@id='rc-tabs-0-tab-2']")
		time.sleep(0.2)
		self.click("(//span[contains(text(),'确 定')])[1]")


	@pytest.mark.p1
	def test_promoter_SwivelTool_create_pid(self):
		"""
		转链工具-新建推广位:功能、页面渲染正常
		"""
		self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
		self.driver.maximize_window()
		self.click("//li[@id='menu-g8TQ4TGKyN4']//span[@class='dilu-main-badge']//span[1]")
		time.sleep(2)
		self.click("(//span[contains(text(),'新建推广位')])[1]")
		time.sleep(0.5)
		self.assert_text("推广位名称", "(//label[contains(text(),'推广位名称')])[1]")
		self.find_element("(//span[contains(text(),'确 定')])[1]")

	# @pytest.mark.p1
	# def test_promter_SelectSwivelTool(self):
	#     """
	#     转链工具:选择推广位转链，判断是否提示转链成功
	#     """
	#     self.talent_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
	#     self.driver.maximize_window()
	#     self.click("//li[@id='menu-g8TQ4TGKyN4']//span[@class='dilu-main-badge']//span[1]")
	#     time.sleep(2)
	#     self.click('//*[@id="root"]/section/main/div/form/div[2]/div[2]/div/div/div')
	#     time.sleep(2)
	#     self.send_keys(Keys.ARROW_DOWN)
	#     # self.select_option(1, direction='down')
	#     time.sleep(4)
	#     self.click("//span[contains(text(),'确认转链')]")
	#     time.sleep(2)
	#     self.assert_element("//div[@class='kwaishop-cps-pc-micro-promoter-base-modal-content']")
	#     self.assert_text("转链成功", "//span[contains(text(),'转链成功')]")
