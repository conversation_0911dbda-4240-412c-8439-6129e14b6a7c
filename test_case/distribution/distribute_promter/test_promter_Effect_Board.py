import time

import pytest
import re

from test_case.distribution.distribution_base_case import DistributionToolTest


def select_option(self, param, direction):
    pass


class TestEffectBoard(DistributionToolTest):

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promter_TransactionOverview(self):
        """
        成交概览：点击时间选择框，判断组件是否能正常拉起，判断整体页面是否正常加载
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-wIl6Eg5saoc'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker kwaishop-cps-pc-micro-promoter-base-picker-range']")
        time.sleep(2)
        self.assert_element(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-panels']")  # 判断这个位置的元素不为空 确保时间选择器组件拉起正常
        time.sleep(2)
        self.click('.VT41mo5Gof9mmzm823ej.AjI6UYgJt0DaCSjMLpNy')
        time.sleep(2)
        self.assert_text("商家合作情况（TOP20）", '//*[@id="root"]/section/main/div/div[3]/div[1]/div[1]')
        self.assert_text("商品推广情况（TOP20）", '//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]')

    @pytest.mark.p1
    def test_promoter_TransactionOverview_index(self):
        """成交概览-累计成交销售额/累计支付单量/预估佣金"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("//li[@id='menu-wIl6Eg5saoc']//span[@class='dilu-main-menu-title-content']")
        sale = self.get_text("(//div[@class='cps-materials-number-range'])[1]")
        order = self.get_text("(//div[@class='cps-materials-number-range'])[2]")
        money = self.get_text("(//div[@class='cps-materials-number-range'])[3]")
        self.assert_true(sale != "" and order != "" and money != "")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_TransactionOverview_business_cooperation_situation(self):
        """成交概览-商家合作情况：切换图表/列表展示方式"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("//li[@id='menu-wIl6Eg5saoc']//span[@class='dilu-main-menu-title-content']")
        time.sleep(1)
        self.find_element("(//canvas)[1]")
        self.click("//div[@class='kpro-workbench-layout__content-wrap']//div[3]//div[1]//div[2]//label[2]")
        time.sleep(0.5)
        self.assert_text("商家ID", "(//th[contains(text(),'商家ID')])[1]")
        self.assert_text("商家名称", "(//th[contains(text(),'商家名称')])[1]")
        self.assert_text("预估佣金（元）", "(//span[contains(text(),'预估佣金（元）')])[1]")
        self.assert_text("合作销售成交额（元）", "(//span[contains(text(),'合作销售成交额（元）')])[1]")


    # 若case失败，可先跳过，或者替换一个有数据的账号
    # 后期换成使用时间筛选器筛选
    @pytest.mark.p1
    def test_promoter_TransactionOverview_product_promotion_situation(self):
        """成交概览-商品推广情况：切换图表/列表展示方式"""
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        time.sleep(2)
        self.click("//li[@id='menu-wIl6Eg5saoc']//span[@class='dilu-main-menu-title-content']")
        self.click("//div[4]//div[1]//div[2]//label[2]//span[2]")
        time.sleep(2)
        self.assert_text("商品ID", "(//th[contains(text(),'商品ID')])[1]")
        self.assert_text("商品名称", "(//th[contains(text(),'商品名称')])[1]")
        self.assert_text("商家ID", "(//th[contains(text(),'商家ID')])[1]")
        self.assert_text("商家名称", "(//th[contains(text(),'商家名称')])[1]")
        self.assert_text("预估佣金（元）", "(//span[contains(text(),'预估佣金（元）')])[1]")
        self.assert_text("合作销售成交额（元）", "(//span[contains(text(),'合作销售成交额（元）')])[1]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promter_DeliveryStatus(self):
        """
        发货情况：点击时间选择框，判断组件是否能正常拉起，判断整体页面是否正常加载
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        time.sleep(2)
        self.click("li[id='menu-mH56d4yIHqY'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(2)
        self.click(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker kwaishop-cps-pc-micro-promoter-base-picker-range']")
        time.sleep(2)
        self.assert_element(
            "//div[@class='kwaishop-cps-pc-micro-promoter-base-picker-panels']")  # 判断这个位置的元素不为空 确保时间选择器组件拉起正常
        time.sleep(2)
        self.click('.VT41mo5Gof9mmzm823ej.AjI6UYgJt0DaCSjMLpNy')
        time.sleep(3)
        self.assert_text("成交概览", '//*[@id="root"]/section/main/div/div[2]/div[1]')
        self.assert_text("商家发货时效", "//div[@class='kwaishop-cps-pc-micro-promoter-base-pro-title-subtitle']")

    @pytest.mark.p1
    def test_promter_DeliveryStatusView(self):
        """
        发货情况：点击"查看"按钮，是否正常跳转
        """
        self.talent_login("DISTRIBUTION_PROMTER", "wb_caijinwei")
        self.driver.maximize_window()
        self.sleep(3)
        self.click("li[id='menu-mH56d4yIHqY'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.sleep(3)
        self.click("//span[contains(text(),'查看')]")
        self.sleep(3)
        self.assert_text("普通推广订单", "(//div[@id='rc-tabs-0-tab-1'])[1]")

    @pytest.mark.p1
    def test_promoter_TransactionOverview_delivery_search_sellerId(self):
        """发货情况：通过商家ID查询"""
        self.talent_login("DISTRIBUTION_PROMTER", "maliya")
        self.driver.maximize_window()
        self.sleep(2)
        self.click("(//span[contains(text(),'发货情况')])[1]")
        self.sleep(2)
        self.input("(//input[@placeholder='请输入商家ID'])[1]", 2359894008)
        self.click("(//button[@type='submit'])[1]")
        self.sleep(2)
        try:
            recordsStr = self.get_text("(//li[@class='kwaishop-cps-pc-micro-promoter-base-pagination-total-text'])[1]")
            pattern = r'共 \d+ 条'
            match = re.search(pattern, recordsStr)
            self.assert_true(match, "数据不符合预期")
        # 断言失败后进行二次尝试
        except:
            self.assert_text("暂无数据", "//div[@class='kwaishop-cps-pc-micro-promoter-base-empty-description']")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_promoter_TransactionOverview_delivery_search_sellerName(self):
        """发货情况：商家名称输入名称，弹窗下拉框"""
        self.talent_login("DISTRIBUTION_PROMTER", "maliya")
        self.driver.maximize_window()
        self.sleep(1.5)
        self.click("(//span[contains(text(),'发货情况')])[1]")
        self.sleep(1)
        self.input("(//span[contains(@class,'kwaishop-cps-pc-micro-promoter-base-select-selection-item')])[1]", "快手优选官方")
        self.sleep(1)
        self.find_element("//div[@class='Fd0hBqI7zgnfUOr9DZDd']//div[1]//div[1]")

    @pytest.mark.p1
    def test_promoter_TransactionOverview_delivery_search_reset(self):
        """发货情况：商家ID输入内容搜索后，重置"""
        self.talent_login("DISTRIBUTION_PROMTER", "maliya")
        self.driver.maximize_window()
        self.sleep(1.5)
        self.click("(//span[contains(text(),'发货情况')])[1]")
        self.sleep(1)
        self.input("(//input[@placeholder='请输入商家ID'])[1]", 2359894008)
        self.assert_text("2359894008", "(//input[@placeholder='请输入商家ID'])[1]")
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.sleep(1)
        self.assert_text("", "(//input[@placeholder='请输入商家ID'])[1]")
