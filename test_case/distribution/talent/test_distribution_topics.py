import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
图片校验
python3 -m pytest test_case/distribution/talent/test_distribution_topics.py --headless -n=3
"""


class TestDistributionTopics(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", 'wb_caijinwei')

    @pytest.mark.p2
    def test_adaptable_navigation_bar(self):
        """选品中心：金刚位的图片"""
        img = self.find_element("//*[@id='root']/section/main/div/div[1]/div/div[2]/div/div[1]/div[2]/div[1]/picture/img")

        # 校验 width 和 height
        assert img.get_attribute("width") == "56", "宽度不正确"
        assert img.get_attribute("height") == "56", "高度不正确"

        # 校验 data-component-name
        assert "data-component-name" in img.get_attribute("outerHTML"), "缺少 data-component-name 属性"
        assert "BizImage" in img.get_attribute("outerHTML"), "outerHTML 中未找到 BizImage"

    @pytest.mark.p2
    def test_thematic_pictures(self):
        """选品中心：专题的图片"""
        img_xpath = [
            '//*[@id="root"]/section/main/div/div[1]/div/div[3]/div/div/div[2]/div[1]/div/div[1]/picture/img',
            '//*[@id="root"]/section/main/div/div[1]/div/div[3]/div/div/div[2]/div[2]/div/div[1]/picture/img',
            '//*[@id="root"]/section/main/div/div[1]/div/div[3]/div/div/div[2]/div[3]/div/div[1]/picture/img'
        ]

        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "120", "宽度不正确"
            assert img.get_attribute("height") == "120", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "BizImage" in outer_html, "outerHTML 中未找到 BizImage"

    @pytest.mark.p2
    def test_image_product_listing(self):
        """ 选品中心-列表的商品图片 """
        img_xpath = [
            '//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]/div/div[1]/picture/img',
            '//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[3]/div/div[1]/picture/img',
            '//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[5]/div/div[1]/picture/img'
        ]

        for xpath in img_xpath:
            img = self.find_element(xpath)

            # 校验 width 和 height
            assert img.get_attribute("width") == "210", "宽度不正确"
            assert img.get_attribute("height") == "210", "高度不正确"

            # 校验 data-component-name
            outer_html = img.get_attribute("outerHTML")
            assert "data-component-name" in outer_html, "缺少 data-component-name 属性"
            assert "BizImage" in outer_html, "outerHTML 中未找到 BizImage"
