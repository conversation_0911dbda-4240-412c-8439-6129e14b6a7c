import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
python3 -m pytest test_case/distribution/talent/test_talent_home.py --html=test_data/leader_report.html --headless -n=3
"""


class TestDistributionHome(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p1
    def test_selection_center_home_page(self):
        """选品中心：判断是否进入PC选品中心首页"""
        self.talent_login("DISTRIBUTION_PROMTER", 'wb_huoyangyang')
        self.assert_element("//input[@placeholder='请输入商品名称']")
        self.assert_element("//span[contains(text(),'快分销爆款榜')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_enter_the_activity_square(self):
        """ 活动广场：判断是否进入活动广场 """
        self.talent_login("DISTRIBUTION_PROMTER", 'wb_huoyangyang')
        self.click("//span[contains(text(),'活动广场')]")
        self.find_element("//div[@aria-selected='true']")
        self.find_element("//div[contains(text(),'抢先参与')]")
        self.find_element("//div[contains(text(),'已参与')]")

    @pytest.mark.p1
    def test_enter_the_transaction_overview(self):
        """成交概览：判断是否进入成交概览"""
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')
        self.click("li[id='menu-wIl6Eg5saoc'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.find_element("//*[@id='root']/section/main/div/div[2]/div[1]")
        self.find_element("//*[@id='root']/section/main/div/div[3]/div[1]/div[1]")

    @pytest.mark.p1
    def test_enter_the_delivery_situation(self):
        """发货情况：判断是否进入发货情况"""
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')
        self.click("//span[contains(text(),'发货情况')]")
        self.find_element("//*[@id='root']/section/main/div/div[2]/div[1]")
        self.find_element("//*[@id='root']/section/main/div/div[3]/div[2]/div[1]/div[1]/div")

    @pytest.mark.p1
    def test_incoming_order_data(self):
        """订单数据：判断是否进入订单数据"""
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')
        self.click("li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.find_element(".kwaishop-cps-pc-micro-promoter-base-pro-title-subtitle")

    @pytest.mark.p1
    def test_enter_invoice_management(self):
        """聚力计划发票：判断是否进入聚力计划发票"""
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("li[id='menu-X_5OJGwQwDE'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.find_element("//div[contains(text(),'发票申请')]")
        self.find_element("//div[contains(text(),'申请记录')]")

    @pytest.mark.p1
    def test_merchant_invoice_review(self):
        """商家发票审核：判断是否进入商家发票审核"""
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("li[id='menu-UU-EHo1Z4ao'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.click("//span[contains(text(),'知道了')]")
        self.find_element("//span[contains(text(),'待审核金额(元)')]")
        self.find_element("//span[contains(text(),'已审核金额(元)')]")

    @pytest.mark.p1
    def test_merchants_invoice_online_invitation(self):
        """商家开票在线邀约：判断是否进入商家开票在线邀约"""
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("//*[@id='menu-te1rgOZEpMY']/span/span/span/span")
        self.find_element("//*[@id='root']/section/main/div[1]/div")

    @pytest.mark.p1
    def test_platform_billing(self):
        """平台开票：判断是否进入平台开票"""
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("//*[@id='menu-8bxBT7AIttU']/span/span/span/span")
        self.find_element("div[role='tab'][aria-selected='true']")
        self.find_element("div[role='tab'][aria-selected='false']")

    @pytest.mark.skip
    def test_platform_fund_page(self):
        """
        平台开盘-发票申请翻页:判断发票申请 是否可以翻页
        """
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')
        self.click("//*[@id='menu-8bxBT7AIttU']/span/span/span/span")
        # 获取当前页的第一条数据
        firstMonth = self.get_text("tbody tr:nth-child(1) td:nth-child(2)")
        firstPrice = self.get_text("tbody tr:nth-child(1) td:nth-child(6)")
        print(firstMonth)
        print(firstPrice)
        self.click("li[title='下一页'] button[type='button']")
        time.sleep(0.5)
        # 获取当前页的第一条数据
        secondMonth = self.get_text("tbody tr:nth-child(1) td:nth-child(2)")
        secondPrice = self.get_text("tbody tr:nth-child(1) td:nth-child(6)")
        print(secondMonth)
        print(secondPrice)
        self.assert_false(firstMonth != secondMonth and firstPrice != secondPrice)

    @pytest.mark.p1
    def test_platform_fund_apply_record(self):
        """
        申请记录tab切换:判断进入申请记录tab
        """
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("//*[@id='menu-8bxBT7AIttU']/span/span/span/span")
        time.sleep(0.5)
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(0.5)
        self.assert_true(self.get_attribute("(//div[@id='rc-tabs-0-tab-1'])[1]", "aria-selected") == "false")
        self.assert_true(self.get_attribute("(//div[@id='rc-tabs-0-tab-2'])[1]", "aria-selected") == "true")
        self.assert_text("申请时间", "(//th[contains(text(),'申请时间')])[1]")
        self.assert_text("开票主体", "(//th[@class='invoice-mui-table-cell'][contains(text(),'开票主体')])[2]")
        self.assert_text("业务类型", "(//th[@class='invoice-mui-table-cell'][contains(text(),'业务类型')])[2]")
        self.assert_text("操作类型", "(//th[contains(text(),'操作类型')])[1]")
        self.assert_text("金额(元)", "(//th[@class='invoice-mui-table-cell'][contains(text(),'金额(元)')])[2]")
        self.assert_text("发票类型", "(//th[contains(text(),'发票类型')])[1]")
        self.assert_text("接收方式", "(//th[contains(text(),'接收方式')])[1]")
        self.assert_text("发票状态", "(//th[contains(text(),'发票状态')])[1]")
        self.assert_text("操作", "(//th[@class='invoice-mui-table-cell'][contains(text(),'操作')])[3]")

    @pytest.mark.p1
    def test_platform_fund_export_records(self):
        """
        申请记录-查看导出记录:判断导出记录弹窗弹出
        """
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("//*[@id='menu-8bxBT7AIttU']/span/span/span/span")
        self.sleep(1)
        self.click("(//span[contains(text(),'查看导出记录')])[1]")
        self.sleep(1)
        self.assert_text("申请导出时间", "(//th[contains(text(),'申请导出时间')])[1]")
        self.assert_text("开票主体", "(//th[@class='invoice-mui-table-cell'][contains(text(),'开票主体')])[3]")
        self.assert_text("业务类型", "(//th[@class='invoice-mui-table-cell'][contains(text(),'业务类型')])[3]")
        self.assert_text("导出操作人", "(//th[contains(text(),'导出操作人')])[1]")
        self.assert_text("金额", "(//th[@class='invoice-mui-table-cell'][contains(text(),'金额')])[3]")
        self.assert_text("操作", "(//th[@class='invoice-mui-table-cell'][contains(text(),'操作')])[5]")
        self.find_element("(//button[@type='button'])[13]")
        self.sleep(2)

    @pytest.mark.p1
    def test_platform_fund_normal_qa(self):
        """
        申请记录—常见问题:判断进入常见问题页面
        """
        self.talent_login("DISTRIBUTION_PROMTER", 'liuxiaohui07')
        self.click("//*[@id='menu-8bxBT7AIttU']/span/span/span/span")
        self.sleep(1)
        self.click("(//span[contains(text(),'常见问题')])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'平台给达人开票操作指南')])[1]")

