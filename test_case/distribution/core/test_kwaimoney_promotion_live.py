import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
快赚客推广专区-直播推广
'''

class TestKwaimoneyLivePromotion(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//span[contains(text(),'推广专区')])[1]")
        time.sleep(0.5)
        self.click("(//a[contains(text(),'直播推广')])[1]")
        time.sleep(2)


    def test_promotion_live_field_core(self):
        """
        直播推广页面元素判断
        """
        self.baseLogin()
        # 检验筛选项
        self.assert_text("带货类目","(//span[@class='src-Components-FilterSelection-index-module__filterSelectionTitle--tNAYp'])[1]")
        self.assert_text("商品数量","(//span[contains(text(),'商品数量')])[1]")
        self.assert_text("平均佣金","(//span[contains(text(),'平均佣金')])[1]")
        self.assert_text("出让佣金","(//span[contains(text(),'出让佣金')])[1]")
        self.assert_text("出让货款","(//span[contains(text(),'出让货款')])[1]")
        self.assert_text("推广类型","(//span[@class='src-Components-FilterSingle-index-module__filterSelectionTitle--2s8xK'])[1]")

        # 检验表格字段
        self.assert_text("直播带货信息", "(//th[contains(text(),'直播带货信息')])[1]")
        self.assert_text("粉丝数量", "(//span[contains(text(),'粉丝数量')])[1]")
        self.assert_text("预计直播时间", "(//th[contains(text(),'预计直播时间')])[1]")
        self.assert_text("商品数量", "(//span[@class='ant-table-column-title'][contains(text(),'商品数量')])[1]")
        self.assert_text("平均佣金率", "(//span[contains(text(),'平均佣金率')])[1]")
        self.assert_text("出让佣金", "(//span[@class='ant-table-column-title'][contains(text(),'出让佣金')])[1]")
        self.assert_text("出让货款", "(//span[@class='ant-table-column-title'][contains(text(),'出让货款')])[1]")
        self.assert_text("跟单时间", "(//div[contains(text(),'跟单时间')])[1]")
        self.assert_text("操作", "(//th[contains(text(),'操作')])[1]")


    @pytest.mark.p1
    def test_promotion_live_search_type_selected_core(self):
        """
        直播推广不同的筛选类别进行点击
        """
        self.baseLogin()
        # 带货类目
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'首页')])[1]", "class")))
        self.click("(//div[contains(text(),'首页')])[1]")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'首页')])[1]", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) >= 0
        self.click("(//div[contains(@class,'src-Components-FilterSelection-index-module__filterItem--29QFB')][contains(text(),'全部')])[1]")

        # 商品数量
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'10以下')])[1]", "class")))
        self.click("(//div[contains(text(),'10以下')])[1]")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("(//div[contains(text(),'10以下')])[1]", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) > 0
        self.click("(//div[@class='src-Components-FilterRange-index-module__filterItem--3UrPc '][contains(text(),'全部')])[1]")

        # 平均佣金
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(3) div:nth-child(2) div:nth-child(2)", "class")))
        self.click("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(3) div:nth-child(2) div:nth-child(2)")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(3) div:nth-child(2) div:nth-child(2)", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) > 0
        self.click("(//div[contains(@class,'src-Components-FilterRange-index-module__filterItem--3UrPc')][contains(text(),'全部')])[2]")

        # 出让佣金
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(4) div:nth-child(2) div:nth-child(2)", "class")))
        self.click("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(4) div:nth-child(2) div:nth-child(2)")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(4) div:nth-child(2) div:nth-child(2)", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) == 0
        self.click("(//div[contains(@class,'src-Components-FilterRange-index-module__filterItem--3UrPc')][contains(text(),'全部')])[3]")

        # 出让贷款
        self.assert_true(
            False == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(5) div:nth-child(2) div:nth-child(2)", "class")))
        self.click("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(5) div:nth-child(2) div:nth-child(2)")
        self.assert_true(
            True == ("filterItemSelection" in self.get_attribute("div.src-Modules-KwaiMoney-Pages-Promotion-Live-FilterList-index-module__filterListBox--3a0VS div:nth-child(5) div:nth-child(2) div:nth-child(2)", "class")))
        assert len(self.find_elements("tr.ant-table-row.ant-table-row-level-0")) > 0
        self.click("(//div[contains(@class,'src-Components-FilterRange-index-module__filterItem--3UrPc')][contains(text(),'全部')])[4]")