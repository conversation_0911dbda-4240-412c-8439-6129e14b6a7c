from datetime import datetime, timedelta

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
计划管理
计划管理-阶梯计划
pytest test_case/distribution/merchant/plan_management/test_ladder_plan.py --headless -n=3
"""


class TestStepCommissionScheme(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_step_commission_scheme_core(self):
        """进入首页-点击计划管理-点击阶梯计划-判断是否进入阶梯计划页面"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.click('//*[@id="menu-askewjM_o1o"]/span/span/span/span')
        self.assert_text('添加阶梯佣金', '//span[contains(text(),"添加阶梯佣金")]')

    @pytest.mark.p0
    def test_add_ladder_commission_core(self):
        """新增一个阶梯计划"""
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')
        self.click('//*[@id="menu-askewjM_o1o"]/span/span/span/span')
        self.click('//span[contains(text(),"添加阶梯佣金")]')
        self.click("//tbody/tr[1]/td[1]/label[1]/span[1]/span[1]")

        self.sleep(2)
        self.click('//span[contains(text(),"去推广设置")]')
        self.input('//input[@placeholder="请输入阶梯门槛订单数"]', '6')
        self.input('//input[@placeholder="请输入附加佣金率"]', '6')
        self.sleep(2)
        self.click('//input[@placeholder="请选择日期"]')
        current_date = datetime.now().date()
        target_date = current_date + timedelta(days=1)
        xpath_expression = f"td[title='{target_date}'] div"
        self.click(xpath_expression)
        self.click('//ul//span[contains(text(),"确 定")]')
        self.sleep(2)
        self.click("//div[@id='root']//section//main//div//div//span[contains(text(),'确 定')]")
        self.click("//div[@class='cps-ant-modal-confirm-btns']//span[contains(text(),'确 定')]")
