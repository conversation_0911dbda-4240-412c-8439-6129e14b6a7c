import os
import random
import time
import re

import pyautogui
import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

# 生成 30 到 90 之间的随机整数
random_number = random.randint(30, 90)
# 获取当前脚本目录
script_directory = os.path.dirname(os.path.abspath(__file__))
relative_path = "./ShopOrientationPage.txt"
file_paths = os.path.join(script_directory, relative_path)

"""
计划管理
计划管理-专属计划
python3 -m pytest test_case/distribution/merchant/plan_management/test_exclusive_plan.py --headless -n=5
"""


class TestExclusivePlan(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", "wb_huoyangyang")
        self.sleep(2)

    @pytest.mark.p1
    def test_exclusive_plan_page_core(self):
        """进入首页-点击计划管理-点击专属计划-判断是否进入专属计划页面"""
        self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
        time.sleep(2)
        self.assert_text('添加专属计划', '//span[contains(text(),"添加专属计划")]')

    @pytest.mark.p1
    def test_batch_to_normal_plan_core(self):
        """批量转换为已关闭的普通计划"""
        self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
        """ 选择 已关闭 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(1)
        self.click("//div[@title='已下架']")
        self.click("//span[contains(text(),'查 询')]")
        # 判断关闭的数量是否是0
        try:
            self.assert_text("暂无数据", "(//div[@class='kwaishop-cps-merchant-plan-pc-empty-description'])[1]", timeout=2)
            return
        except:
            pass
        # totalStr = self.find_element("(//li[@class='kwaishop-cps-merchant-plan-pc-pagination-total-text'])[1]")
        # total = re.findall(r'\d+', str(totalStr))
        # if total == 0:
        #     return
        """ 去转为普通计划 """
        self.click(
            "//*[@id='root']/section/main/div[3]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[8]/div/div[3]")
        time.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.type("//input[@id='commissionRate']", '22')
        self.click("//*[@id='hasOpen']/label[2]/span[contains(text(),'关闭')]")
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(0.5)
        self.find_element("//div[@class='kwaishop-cps-merchant-plan-pc-message-notice-content']")

    @pytest.mark.p1
    def test_agreement_settings_core(self):
        """ 进入专属计划-达人管理详情-判断是否存在商达协议按钮 """
        self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
        self.click("(//a[contains(text(),'详情')])[1]")
        self.assert_element("//a[contains(text(),'商达协议设置')]")

    @pytest.mark.p0
    def test_close_the_open_exclusive_plan_core(self):
        """ 关闭1个开启的专属计划 """
        self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
        time.sleep(3)
        """ 选择 已开启 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(3)
        self.click("//div[@title='已开启']")
        self.click("//span[contains(text(),'查 询')]")
        """ 去关闭 """
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[8]/div/div[1]/a')
        self.assert_text("确定关闭推广计划？", "//div[@class='kwaishop-cps-merchant-plan-pc-popover-message-title']")

    @pytest.mark.p1
    def test_batch_close_open_dedicated_plans_core(self):
        """ 批量关闭开启的专属计划 """
        self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
        time.sleep(2)
        """ 选择 已开启 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(3)
        self.click("//div[@title='已开启']")
        self.click("//span[contains(text(),'查 询')]")
        """ 去关闭 """
        self.click(
            "//div[@class='kwaishop-cps-merchant-plan-pc-table-selection']//span[@class='kwaishop-cps-merchant-plan-pc-checkbox-inner']")
        self.click("//span[contains(text(),'批量关闭')]")
        time.sleep(2)
        self.assert_text("确认批量关闭推广计划？", "//span[@class='kwaishop-cps-merchant-plan-pc-modal-confirm-title']")

    @pytest.mark.p0
    def test_add_influencers_to_exclusive_plan_core(self):
        """ 给专属计划添加达人 """
        self.click('//*[@id="menu-IABrvxxaYMQ"]/span/span/span/span')
        self.click('//tbody/tr[1]/td[8]/div[1]/div[2]/a[contains(text(),"添加达人")]')
        """ 输入专属达人ID，并确认达人信息 """
        random_activity_id = self.get_random_activity_id(file_paths)
        self.input("//input[@class='cps-materials-multi-input-input']", f'{random_activity_id}')
        self.click("//div[@class='cps-materials-multi-input-userSelect']")
        """ 输入佣金并确认 """
        self.input("//*[@id='commissionRate']", f'{random_number}')
        self.click("//span[contains(text(),'确 定')]")
        self.assert_element("//span[contains(text(),'操作成功')]")
