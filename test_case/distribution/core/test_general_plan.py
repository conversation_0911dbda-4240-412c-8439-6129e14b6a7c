import random
import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

# 生成 1 到 50 之间的随机整数 --> 佣金
random_number = random.randint(1, 50)

tool_test = DistributionToolTest()
PhotoKit = tool_test.get_hitokoto()

"""
计划管理
计划管理-普通计划
python3 -m pytest test_case/distribution/merchant/plan_management/test_general_plan.py -n=1
"""


class TestDistributionGeneralPlan(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_huoyangyang')

    def tearDown(self):
        super().tearDown()

    @pytest.mark.p0
    def test_general_plan_page_turning_core(self):
        """ 普通计划 翻页 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(3)
        self.click("//body//div[@id='main_root']//form[@id='pro-form-wrapper']//div//div//div//div//div[1]//button[1]")
        self.scroll_down(2)
        self.click("//a[normalize-space()='2']")
        self.click("//a[normalize-space()='3']")
        self.click("//a[normalize-space()='4']")
        self.click("//a[normalize-space()='5']")

    @pytest.mark.p0
    def test_hand_card_commodity_basic_information_core(self):
        """ 手卡-商品基础信息 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(3)
        self.click("//tbody/tr[1]/td[7]/div[1]/div[4]/a[1]/span[1]//*[name()='svg']")
        self.click("//a[contains(text(),'编辑带货任务手卡')]")
        """ 断言 """
        self.assert_element("//div[contains(text(),'手卡预览区域')]")
        self.assert_element("//div[@class='price-wrapper']")
        self.assert_element("//div[@class='info-wrapper']")
        self.assert_element("//div[@class='sale-points-card']")

    @pytest.mark.p0
    def test_batch_open_schedule_core(self):
        """ 开启关闭的计划 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        time.sleep(1)
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(1)
        """ 选择 已关闭 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(0.5)
        self.click("//div[@title='已关闭']")
        time.sleep(1)
        self.click("//span[contains(text(),'查 询')]")
        time.sleep(1)
        """ 勾选 去开启关闭的普通计划"""
        self.click(
            "//*[@id='root']/section/main/div[4]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div/label")
        self.click("//span[contains(text(),'批量开启')]")
        time.sleep(1)
        self.click("//span[contains(text(),'确 定')]")
        """ 断言 提示开启推广成功"""
        time.sleep(1)
        try:
            self.assert_element('//span[contains(text(),"开启推广成功")]', timeout=0.5)
        except:
            # 存在商品被下架的可能
            self.assert_element("(//span[contains(text(),'商品已下架，不支持开启分销计划')])[1]", timeout=1)

    @pytest.mark.p0
    def test_chang_money_hired_is_normal_core(self):
        """修改佣金率 """
        self.click("//*[@id='menu-alIq_t8NvXM']/span/span/span/span")
        self.click('//*[@id="driver-popover-item"]/div[4]/button')
        self.click("(//button[@type='submit'])[1]")
        time.sleep(2)
        self.click("//div[@class='V3zVt1Q111K4BDZwaI3V']/div")
        self.click("div[class='V3zVt1Q111K4BDZwaI3V'] div")
        self.click("//span[contains(text(),'修改')]")
        time.sleep(1)
        self.input("//input[@id='commissionRate']", f"{random_number}")
        self.click("//span[contains(text(),'确 定')]")
        self.find_element('//span[contains(text(),"佣金率修改成功")]')

    @pytest.mark.p0
    def test_share_goods_copy_link_and_ling_core(self):
        """ 分享商品-复制口令-复制链接 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        time.sleep(1)
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(5)
        # 点击分享商品
        self.click("//a[@id='normal_plan_list_guide_2']")
        self.click("(//a[contains(text(),'复制口令')])[1]")
        time.sleep(1)
        self.assert_element("(//span[contains(text(),'复制口令成功')])[1]")
        self.click("(//a[contains(text(),'复制链接')])[1]")
        time.sleep(1)
        self.assert_element("(//span[contains(text(),'复制链接成功')])[1]")

    # 单个设置申样规则
    # 若case失效，先检查商品是否没有普通计划了
    @pytest.mark.p0
    def test_set_single_ask_sample_core(self):
        itemId = "23643846762008"
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'普通计划')]")
        time.sleep(1)
        # 搜索固定的商品
        self.input("//input[@placeholder='请输入商品ID']", itemId)
        time.sleep(1)
        self.click('//*[@id="driver-popover-item"]/div[4]/button')
        self.click("(//button[@type='submit'])[1]")
        time.sleep(1.5)
        #     点击申样规则
        self.click("(//a[contains(text(),'设置申样规则')])[1]")
        time.sleep(0.5)
        self.assert_element("(//div[@class='k0MEeKrfY4PAYq2z3zxo'])[1]")

    @pytest.mark.p1
    def test_plan_management_click_to_copy_core(self):
        """ 点击复制 商品ID 断言提示复制成功"""
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(5)
        self.click('//tbody/tr[1]/td[2]/div[1]/div[2]/div[2]/a[1]/span[1]//*[name()="svg"]')
        self.assert_element('//span[contains(text(),"复制成功")]')

    @pytest.mark.p1
    def test_general_program_introduction_core(self):
        """ 普通计划-说明收起和展开；常见问题链接跳转 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        self.sleep(1)
        """ """
        self.click('//a[contains(text(),"展开")]')
        self.sleep(1)
        self.click('//a[contains(text(),"收起")]')
        self.is_element_enabled('//a[contains(text(),"达人广场")]')

    @pytest.mark.p0
    def test_inquire_selective_state_core(self):
        """ 普通计划筛选 是否有筛选结果 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        """ 使用商品名称 筛选 """
        self.input('input[placeholder="请输入商品名称"]', '分销测试')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')
        time.sleep(3)
        text = self.get_text("//tbody/tr[1]/td[2]")
        self.assert_in("分销测试", text)

    @pytest.mark.p0
    def test_Product_id_query_core(self):
        """ 普通计划筛选 是否有筛选结果 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        """ 使用商品名称 筛选 """
        self.input("//input[@placeholder='请输入商品ID']", '11111')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')
        time.sleep(3)
        self.assert_text("暂无数据", "//div[@class='kwaishop-cps-merchant-plan-pc-empty-description']")

    @pytest.mark.p0
    def test_plan_management_batch_delete_core(self):
        """ 删除一个已关闭的计划 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(5)
        """ 选择 已关闭 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(3)
        self.click("//div[@title='已关闭']")
        self.click("//span[contains(text(),'查 询')]")
        """ 去删除 已关闭的计划 """
        self.click("//tbody/tr[1]/td[7]/div[1]/div[4]/a[1]/span[1]//*[name()='svg']")
        self.click("//a[contains(text(),'删除计划')]")
        self.click("//span[contains(text(),'确 定')]")
        self.assert_element("//span[contains(text(),'删除成功')]")

    @pytest.mark.p0
    def test_batch_shutdown_and_deletion_core(self):
        """ 批量删除 已关闭的计划 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(5)
        """ 选择 已关闭 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(3)
        self.click("//div[@title='已关闭']")
        self.click("//span[contains(text(),'查 询')]")
        """ 去批量删除关闭的计划 """
        self.click(
            "//*[@id='root']/section/main/div[4]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div/label")
        self.click("//span[contains(text(),'批量删除')]")
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(1)
        self.find_element("//span[contains(text(),'删除成功')]")

    @pytest.mark.p0
    def test_general_plan_page_turning_core(self):
        """ 普通计划 翻页 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(3)
        self.click("//body//div[@id='main_root']//form[@id='pro-form-wrapper']//div//div//div//div//div[1]//button[1]")
        self.scroll_down(2)
        self.click("//a[normalize-space()='2']")
        self.click("//a[normalize-space()='3']")
        self.click("//a[normalize-space()='4']")
        self.click("//a[normalize-space()='5']")

    @pytest.mark.p0
    def test_edit_delivery_task_card_core(self):
        """ 编辑带货任务手卡 """
        try:
            self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
            self.click("//*[@id='driver-popover-item']/div[4]/button")
            time.sleep(3)
            """ 点击 编辑带货任务手卡 """
            self.click("//tbody/tr[1]/td[7]/div[1]/div[4]/a[1]/span[1]//*[name()='svg']")
            time.sleep(3)
            self.click("//a[contains(text(),'编辑带货任务手卡')]")
            self.click("//input[contains(@placeholder,'请输入卖点信息')]")
            self.type("//input[contains(@placeholder,'请输入卖点信息')]", f"{PhotoKit}")
            """ 确认提交 """
            self.click("//span[contains(text(),'确 定')]")
            self.find_element("//span[contains(text(),'保存成功')]")
        except:
            self.click("//div[@role='document']//div//div//div//div//span[contains(text(),'确 定')]")
            self.find_element("//span[contains(text(),'保存成功')]")

    @pytest.mark.p0
    def test_hand_card_commodity_basic_information_core(self):
        """ 手卡-商品基础信息 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(3)
        self.click("//tbody/tr[1]/td[7]/div[1]/div[4]/a[1]/span[1]//*[name()='svg']")
        self.click("//a[contains(text(),'编辑带货任务手卡')]")
        """ 断言 """
        self.assert_element("//div[contains(text(),'手卡预览区域')]")
        self.assert_element("//div[@class='price-wrapper']")
        self.assert_element("//div[@class='info-wrapper']")
        self.assert_element("//div[@class='sale-points-card']")

    @pytest.mark.p0
    def test_general_plan_page_core(self):
        """进入首页-点击计划管理-点击普通计划-点击跳过-判断是否进入普通计划页面"""
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        self.assert_text('新建普通计划', "div[class='kwaishop-cps-merchant-plan-pc-pro-toolbar-table-actions'] span")

    @pytest.mark.p0
    def test_add_regular_plan_core(self):
        """ 新建一个普通计划 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.sleep(1)
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        self.sleep(1)
        self.add_general_plan()

    @pytest.mark.p0
    def test_inquire_selective_state_core(self):
        """ 普通计划筛选 是否有筛选结果 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        """ 使用商品名称 筛选 """
        self.input('input[placeholder="请输入商品名称"]', '分销测试')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')
        time.sleep(3)
        text = self.get_text("//tbody/tr[1]/td[2]")
        self.assert_in("分销测试", text)

    @pytest.mark.p0
    def test_Product_id_query_core(self):
        """ 普通计划筛选 是否有筛选结果 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        """ 使用商品名称 筛选 """
        self.input("//input[@placeholder='请输入商品ID']", '11111')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')
        time.sleep(3)
        self.assert_text("暂无数据", "//div[@class='kwaishop-cps-merchant-plan-pc-empty-description']")

    @pytest.mark.p0
    def test_plan_management_batch_delete_core(self):
        """ 删除一个已关闭的计划 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(5)
        """ 选择 已关闭 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(3)
        self.click("//div[@title='已关闭']")
        self.click("//span[contains(text(),'查 询')]")
        """ 去删除 已关闭的计划 """
        self.click("//tbody/tr[1]/td[7]/div[1]/div[4]/a[1]/span[1]//*[name()='svg']")
        self.click("//a[contains(text(),'删除计划')]")
        self.click("//span[contains(text(),'确 定')]")
        self.assert_element("//span[contains(text(),'删除成功')]")

    @pytest.mark.p0
    def test_batch_shutdown_and_deletion_core(self):
        """ 批量删除 已关闭的计划 """
        self.click('//*[@id="menu-alIq_t8NvXM"]/span/span/span/span')
        self.click("//*[@id='driver-popover-item']/div[4]/button")
        time.sleep(5)
        """ 选择 已关闭 --> 去查询 """
        self.click("//*[@id='pro-form-wrapper']/div/div[3]/div/div[2]/div/div/div/div[1]/span[1]")
        time.sleep(3)
        self.click("//div[@title='已关闭']")
        self.click("//span[contains(text(),'查 询')]")
        """ 去批量删除关闭的计划 """
        self.click(
            "//*[@id='root']/section/main/div[4]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div/label")
        self.click("//span[contains(text(),'批量删除')]")
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(1)
        self.find_element("//span[contains(text(),'删除成功')]")