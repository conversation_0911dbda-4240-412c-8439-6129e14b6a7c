import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
招商活动
-团长招商
pytest test_case/distribution/leader/investment_promotion_activity/test_leader_head_inviting_investment.py --headless -n=5
"""


class TestHeadInvitingInvestment(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER", "wb_huoyangyang")
        self.sleep(2)

    @pytest.mark.p0
    def test_leader_merchant_core(self):
        """团长招商-判断是否进入团长招商页面"""
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        time.sleep(2)
        self.click("(//div[contains(text(),'推广商品数')])[1]")

    @pytest.mark.p0
    def test_click_find_activity_core(self):
        """团长招商-点击找活动-判断是否进入找活动页面"""
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        time.sleep(2)
        self.click("//div[@class='cps-ant-tabs-tab']")
        time.sleep(2)
        self.assert_text("专属招商", "//span[contains(text(),'专属招商')]")
        self.assert_text("普通招商", "//span[contains(text(),'普通招商')]")

    @pytest.mark.p0
    def test_click_find_activity_click_activity_core(self):
        """ 团长招商-团长的招商活动列表 """
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        self.click("//*[@id='rc-tabs-0-panel-1']/div/div[2]/div[2]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]")
        self.sleep(2)
        self.assert_element('//*[@id="rc-tabs-0-panel-1"]/div[1]/label[1]/span[1]')
        self.assert_element('//*[@id="rc-tabs-0-panel-1"]/div[1]/label[2]/span[1]')

    @pytest.mark.p0
    def test_click_find_activity_click_item_core(self):
        """
        点击团长列的热卖商品-判断跳转是否正常
        点击团长-点击热卖商品-查看tab是否可点击
        """
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        time.sleep(3)
        self.click("//*[@id='rc-tabs-0-panel-1']/div/div[2]/div[2]/div[1]/div[1]/div[2]/div[3]/div[2]/div[1]")
        self.sleep(2)
        self.assert_element("(//th[contains(text(),'商品信息')])[1]")

    @pytest.mark.p0
    def test_click_find_leader_limit_core(self):
        """ 判断找团长tab分页是否正常 """
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        time.sleep(3)
        self.scroll_down(4)
        self.assert_element("//li[@title='下一页']//button[@type='button']")
        self.click("//li[@title='下一页']//button[@type='button']")
        self.assert_element("//a[normalize-space()='4']")
        self.click("//a[normalize-space()='4']")

    @pytest.mark.p2
    def test_click_contact_seller_appear_window_core(self):
        """ 点击联系团长-查看是否有弹窗元素 找团长tab分页 翻页 """
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        time.sleep(1)
        self.input("(//input[@placeholder='请输入团长名称或ID'])[1]", "1968563075")
        self.click("(//button[@class='cps-ant-btn cps-ant-btn-icon-only cps-ant-input-search-button'])[1]")
        time.sleep(1)
        self.click("//span[contains(text(),'联系团长')]")
        time.sleep(1)
        self.assert_element("(//div[@class='cps-ant-modal-header'])[1]")

    @pytest.mark.p2
    def test_activity_instant_attend_jump_core(self):
        """ 点击找活动tab - 点击立即报名 - 判断跳转是否正常 找团长 跳转 """
        self.click("li[id='menu-7Nj3gCKIDOM'] span span span span")
        time.sleep(3)
        self.click("#rc-tabs-0-tab-2")  # 点击找活动
        self.click("//*[@id='rc-tabs-0-panel-2']/div/div[2]/div[2]/div[1]/div[2]/div[3]/div[contains(text(),'立即报名')]")
        time.sleep(3)
        windows = self.driver.window_handles
        self.driver.switch_to.window(windows[-1])
        self.assert_url_contains("/pc/leader/zone-leader/join-activity?activityId")
        self.find_element('//span[contains(text(),"重 置")]')
        self.find_element('//span[contains(text(),"查 询")]')

    @pytest.mark.p2
    def test_click_find_activity_jump(self):
        """ 点击找活动 - 判断跳转是否正常 """
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        # 点击找活动
        self.click("(//div[@id='rc-tabs-0-tab-2'])[1]")
        time.sleep(1)
        self.click("(//a[@rel='nofollow'][normalize-space()='2'])[2]")
        self.sleep(1)
        self.click("(//div[contains(text(),'营销要求')])[1]")

    @pytest.mark.p2
    def test_click_search_seller_right_core(self):
        """ 点击搜索团长 - 判断结果是否符合预期  """
        self.click("(//span[@class='dilu-main-menu-title-content'])[8]")
        time.sleep(1)
        self.input("(//input[@placeholder='请输入团长名称或ID'])[1]", "2359894008")
        self.click("(//button[@class='cps-ant-btn cps-ant-btn-icon-only cps-ant-input-search-button'])[1]")
        time.sleep(1)
        self.assert_text("铜锣湾扛把子自动化账号", "(//div[@class='index-module__user_name--NJrxV'])[1]")
