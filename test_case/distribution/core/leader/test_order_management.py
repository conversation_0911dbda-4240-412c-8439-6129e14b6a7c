import time

import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
团长
订单管理
pytest test_case/distribution/leader/test_order_management.py --headless -n=3
"""


class TestOrderManagement(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        time.sleep(2)

    @pytest.mark.p0
    def test_service_charge_revenue_order_core(self):
        """ 订单管理：判断是否进入服务费收入订单页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.find_element("//div[@aria-selected='true']")
        self.find_element("//div[contains(text(),'服务费支出订单')]")
        self.find_element("//div[contains(text(),'佣金收入订单')]")

    @pytest.mark.p0
    def test_service_charge_order_click_core(self):
        """ 订单管理：点击进入服务费支出订单Tab,判断是否进入该页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'服务费支出订单')]")
        time.sleep(2)
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(1)")
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(2)")
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(3)")

    @pytest.mark.p0
    def test_commission_order_click_core(self):
        """ 订单管理：点击 佣金收入订单Tab,判断是否进入该页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        self.click("//div[contains(text(),'佣金收入订单')]")
        time.sleep(2)
        self.find_element("//label[contains(text(),'订单ID')]")
        self.find_element("//label[contains(text(),'二创者ID')]")
        self.find_element("//label[contains(text(),'商家ID')]")

    @pytest.mark.p0
    def test_service_order_limit_core(self):
        """点击批量导出-查看是否存在导出成功弹窗"""
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//span[contains(text(),'批量导出')]")
        time.sleep(1)
        self.assert_element('//div[@class="kwaishop-cps-leader-base-pc-notification-notice-with-icon"]')

    @pytest.mark.p0
    def test_service_order_expect_core(self):
        """ 点击批量导出-查看是否存在导出成功弹窗 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'批量导出')])[1]")
        time.sleep(1)
        self.click("(//div[@class='kwaishop-cps-leader-base-pc-notification-notice-message'])[1]")