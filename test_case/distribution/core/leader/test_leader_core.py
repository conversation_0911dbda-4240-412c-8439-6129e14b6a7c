from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time
import os


class TestLeaderCore(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        # self.load_cookies(name="cookie.txt")
        # self.open("https://cps.kwaixiaodian.com/pc/leader/zone-leader/my-sponsor-activity")
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_huoyangyang")
        # self.save_cookies(name="/Users/<USER>/pcUI/cookie.txt")
        # cookies = self.driver.get_cookies()
        # print(cookies)
        # print(os.path.abspath("/Users/<USER>/pcUI/cookie.txt"))  # 查看文件保存路径
        # print(os.access("/Users/<USER>/pcUI/cookie.txt", os.W_OK))  # 检查是否可写
        self.sleep(3)

    def test_create_activity_page_core(self):
        """
        发起活动页面，判断三种活动类型都有展示
        """
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("(//span[contains(text(),'发起活动')])[1]")
        self.assert_text("普通招商", "(//div[contains(text(),'普通招商')])[1]")
        self.assert_text("专属招商", "(//div[contains(text(),'专属招商')])[1]")
        self.assert_text("合作招商", "(//div[contains(text(),'合作招商')])[1]")


    @pytest.mark.p0
    def test_copy_master_registration_link_core(self):
        """我发起的活动-团长复制链接"""
        self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.click("//tbody/tr[1]/td[6]/div[1]/div[1]/button[1]/span[1]")
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[1]/button[1]/span[1]").click()
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[1]/button[1]/span[1]").click()
        self.assert_element("//span[contains(text(),'已复制活动链接')]")

    @pytest.mark.p0
    def test_copy_merchant_registration_link_core(self):
        """我发起的活动-复制商家报名链接"""
        self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.click("//tbody/tr[1]/td[6]/div[1]/div[2]/button[1]/span[1]")
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[2]/button[1]/span[1]").click()
        self.find_element("//tbody/tr[1]/td[6]/div[1]/div[2]/button[1]/span[1]").click()
        self.assert_element("//span[contains(text(),'已复制活动链接')]")

    @pytest.mark.p2
    def test_activity_detail_spread_element_normal_core(self):
        self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
        time.sleep(1)
        self.click("tbody tr:nth-child(1) td:nth-child(1) a:nth-child(1)")
        self.assert_element("(//div[@class='kwaishop-cps-pc-micro-leader-card-body'])[1]")

    @pytest.mark.p0
    def test_investment_not_open_core(self):
        """点击未发布tab-判断tab切换是否正常"""
        self.click("li[id='menu-bSahZ7f4Tls'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.click("(//label[@class='kwaishop-cps-pc-micro-leader-radio-button-wrapper'])[1]")
        time.sleep(2)
        self.click("(//span[contains(text(),'未发布')])[2]")

    @pytest.mark.p1
    def test_official_hosting_info_core(self):
        """点击官方托管tab-点击已结束tab 官方托管活动信息正常展示"""
        self.click("//span[contains(text(), '我发起的活动')]")
        time.sleep(1)

        self.click("//div[@id='rc-tabs-0-tab-4']")
        self.sleep(1)
        self.click("(//span[contains(text(),'已结束')])[1]")
        self.sleep(1)

        element = self.find_element("td:nth-child(1)")
        self.assert_true(element.text != "")

    @pytest.mark.p0
    def test_service_charge_revenue_order_core(self):
        """ 订单管理：判断是否进入服务费收入订单页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.find_element("//div[@aria-selected='true']")
        self.find_element("//div[contains(text(),'服务费支出订单')]")
        self.find_element("//div[contains(text(),'佣金收入订单')]")

    @pytest.mark.p0
    def test_service_charge_order_click_core(self):
        """ 订单管理：点击进入服务费支出订单Tab,判断是否进入该页面 """
        # 点击订单管理tab
        self.click("//li[@id='menu-VwAyt8HQyX0']//span[@class='dilu-main-badge']//span[1]")
        time.sleep(2)
        self.click("//div[contains(text(),'服务费支出订单')]")
        time.sleep(2)
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(1)")
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(2)")
        self.assert_element("#pro-form-wrapper > div:nth-child(1) > div:nth-child(3)")