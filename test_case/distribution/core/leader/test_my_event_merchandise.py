import time

import pytest
from seleniumbase.common.exceptions import NoSuchElementException

from test_case.distribution.distribution_base_case import DistributionToolTest

"""
招商活动
-我报名的活动
pytest test_case/distribution/leader/investment_promotion_activity/test_my_event_merchandise.py
"""


class TestMyEventMerchandise(DistributionToolTest):

    @pytest.mark.p0
    def test_tab_switching_core(self):
        """普通/专属Tab切换"""
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("(//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap'])[8]")
        self.click("//*[@id='root']/section/main/div/div/div[1]/div[1]/div[1]/div/div[2]")
        self.click("//*[@id='root']/section/main/div/div/div[1]/div[1]/div[1]/div/div[1]")
        self.assert_element("#pro-form-wrapper")

    @pytest.mark.p0
    def test_sub_tab_switching_core(self):
        """推广中/已结束Tab切换"""
        self.merchant_login("DISTRIBUTION_LEADER", "wb_caijinwei")
        self.click("(//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title-wrap'])[7]")
        # 已结束
        self.click("label.kwaishop-cps-pc-micro-leader-radio-button-wrapper:nth-child(2) span.kwaishop-cps-pc-micro-leader-radio-button")
        # 推广中
        self.click("label.kwaishop-cps-pc-micro-leader-radio-button-wrapper:nth-child(1) span.kwaishop-cps-pc-micro-leader-radio-button")
        self.assert_text("推广中", "(//span[@class='kwaishop-cps-pc-micro-leader-pro-field-status__text'][contains(text(),'推广中')])[1]")

    @pytest.mark.p2
    def test_activity_detail_element_normal_core(self):
        """进入一个招商活动详情，查看元素是否正常"""
        self.merchant_login("DISTRIBUTION_LEADER_PRT", "wb_caijinwei")
        self.click("//li[@id='menu-ko6KbIvBx_Q']//span[@class='dilu-main-badge']//span[1]")
        self.click("//tbody/tr[1]/td[1]/a[1]")
        time.sleep(1)
        self.assert_element("(//div[@class='kwaishop-cps-pc-micro-leader-card-body'])[1]")
