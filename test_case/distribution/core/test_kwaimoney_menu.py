import time

import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
获取菜单Tab
'''

@pytest.mark.p1
class TestKwaimoneyPCMenu(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.2)
        self.click("(//span[contains(text(),'推广专区')])[1]")
        time.sleep(0.2)
        self.click("(//a[contains(text(),'商品推广')])[1]")
        time.sleep(0.2)
        self.click("(//span[contains(text(),'数据参谋')])[1]")
        time.sleep(0.2)
        self.click("(//span[contains(text(),'收益管理')])[1]")
        time.sleep(2)

    @pytest.mark.p0
    def test_kwaimoney_check_menu_core(self):
        """
        登录成功-进入首页-判断菜单元素显示正确
        """
        self.baseLogin()
        self.assert_title('快手快分销-快赚客')  # 判断登录页面标头，确认登录成功

        self.assert_text("推广专区", "(//span[contains(text(),'推广专区')])[1]")
        self.assert_text("直播推广", "(//a[@href='/page/kwaimoney/promotion/live'])[1]")
        self.assert_text("商品推广", "(//a[@href='/page/kwaimoney/promotion/goods'])[1]")

        self.assert_text("数据参谋", "(//span[contains(text(),'数据参谋')])[1]")
        self.assert_text("直播推广", "(//a[@href='/page/kwaimoney/dashboard/live'])[1]")
        self.assert_text("商品推广", "(//a[@href='/page/kwaimoney/dashboard/goods'])[1]")
        self.assert_text("拉新推广", "(//a[contains(text(),'拉新推广')])[1]")

        self.assert_text("推广位管理", "(//a[contains(text(),'推广位管理')])[1]")

        self.assert_text("收益管理", "(//span[contains(text(),'收益管理')])[1]")
        self.assert_text("收款账号", "(//a[contains(text(),'收款账号')])[1]")
        self.assert_text("账单管理", "(//a[contains(text(),'账单管理')])[1]")
        self.assert_text("推广订单", "(//a[contains(text(),'推广订单')])[1]")
        self.assert_text("奖金管理", "(//a[contains(text(),'奖金管理')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_live_promotion_page_core(self):
        """
        点击直播推广-判断进入直播推广
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/promotion/live'])[1]")
        time.sleep(1)
        self.assert_text("直播带货信息", "(//th[contains(text(),'直播带货信息')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_goods_promotion_page_core(self):
        """
        点击直播推广-判断进入商品推广
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/promotion/goods'])[1]")
        time.sleep(1)
        self.assert_text("类目", "(//span[@class='src-Components-FilterSelection-index-module__filterSelectionTitle--tNAYp'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_dashboard_live_page_core(self):
        """
        点击数据参谋直播推广-判断进入直播推广数据
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/dashboard/live'])[1]")
        time.sleep(1)
        self.assert_text("直播推广数据",
                         "(//p[@class='src-Modules-KwaiMoney-Pages-Dashboard-LiveDashboard-QueryForm-index-module__query_title--2WXrb'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_dashboard_good_page_core(self):
        """
        点击数据参谋直播推广-判断进入商品推广数据
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/dashboard/goods'])[1]")
        time.sleep(1)
        self.assert_text("商品推广数据",
                         "(//p[@class='src-Modules-KwaiMoney-Pages-Dashboard-GoodsDashboard-QueryForm-index-module__query_title--2oxnV'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_dashboard_invite_page_core(self):
        """
        点击数据参谋直播推广-判断进入拉新推广数据
        """
        self.baseLogin()
        self.click("(//a[contains(text(),'拉新推广')])[1]")
        time.sleep(1)
        self.assert_text("拉新推广数据",
                         "(//p[@class='src-Modules-KwaiMoney-Pages-Dashboard-CpaDashboard-QueryForm-index-module__query_title--1qIcE'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_pid_mange_page_core(self):
        """
        点击推广位管理-判断进入推广位管理页
        """
        self.baseLogin()
        self.click("(//a[contains(text(),'推广位管理')])[1]")
        time.sleep(1)
        self.assert_text("新建推广位", "(//span[contains(text(),'新建推广位')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_collection_account_page_core(self):
        """
        点击收款账号-判断进入收款账号
        """
        self.baseLogin()
        self.click("(//a[contains(text(),'收款账号')])[1]")
        time.sleep(1)
        self.assert_text("可查看当前绑定的收款账户", "(//p[contains(text(),'可查看当前绑定的收款账户')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_bill_mange_page_core(self):
        """
        点击账单管理-判断进入账单管理
        """
        self.baseLogin()
        self.click("(//a[contains(text(),'账单管理')])[1]")
        time.sleep(1)
        self.assert_text("打开 快手App 扫一扫，可查看推广佣金账单并提现", "(//p[@class='src-Modules-KwaiMoney-Pages-Earnings-Bill-index-module__accountTip--3g7Xx'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_order_list_page_core(self):
        """
        点击推广订单-判断进入推广订单
        """
        self.baseLogin()
        self.click("(//a[@href='/page/kwaimoney/earnings/order/list'])[1]")
        time.sleep(1)
        self.assert_text("订单编号", "(//th[contains(text(),'订单编号')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_into_bonus_page_core(self):
        """
        点击奖金管理-判断进入奖金管理
        """
        self.baseLogin()
        self.click("(//a[contains(text(),'奖金管理')])[1]")
        time.sleep(1)
        self.assert_text("奖金管理规则", "(//div[contains(text(),'奖金管理规则')])[1]")

