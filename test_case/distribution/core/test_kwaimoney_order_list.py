import time

import pytest

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys


from test_case.distribution.distribution_base_case import DistributionToolTest

'''
收款账号
'''

@pytest.mark.p1
class TestKwaimoneyOrderList(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.2)
        self.click("(//span[contains(text(),'收益管理')])[1]")
        time.sleep(0.2)
        self.click("(//a[contains(text(),'推广订单')])[1]")

    def select_time(self):
        self.click("(//input[@id='timeRange'])[1]")
        self.select_time_for_kwaimoney(2024, 7, 1)
        self.click("(//span[contains(text(),'确 定')])[1]")

        self.click("(//input[@placeholder='结束日期'])[1]")
        self.select_time_for_kwaimoney(2024, 9, 26)
        self.click("(//span[contains(text(),'确 定')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_order_list_page_core(self):
        self.baseLogin()
        # 验证筛选器
        self.find_element("(//label[contains(text(),'时间选择')])[1]")
        self.find_element("(//label[contains(text(),'商品ID')])[1]")
        self.find_element("(//label[contains(text(),'订单状态')])[1]")
        self.find_element("(//span[@title='全部'])[1]")
        self.find_element("(//label[contains(text(),'备注')])[1]")
        self.find_element("(//label[normalize-space()='PID'])[1]")
        self.assert_text("重 置", "(//button[@type='button'])[1]")
        self.find_element("(//span[contains(text(),'查 询')])[1]")

        # 验证表头
        self.find_element("(//div[@class='src-Modules-KwaiMoney-Pages-Earnings-Order-List-index-module__label--bp6aw'])[1]")
        self.find_element("(//th[contains(text(),'订单编号')])[1]")
        self.find_element("(//th[contains(text(),'支付时间')])[1]")
        self.find_element("(//th[normalize-space()='PID'])[1]")
        self.find_element("(//th[@title='商品信息'])[1]")
        self.find_element("(//th[contains(text(),'货款基数(元)')])[1]")
        self.find_element("(//th[contains(text(),'计佣金额(元)')])[1]")
        self.find_element("(//span[contains(text(),'订单金额(元)')])[1]")
        self.find_element("(//th[contains(text(),'实付金额(元)')])[1]")
        self.find_element("(//span[contains(text(),'技术服务费(元)')])[1]")
        self.find_element("(//span[contains(text(),'佣金率')])[1]")
        self.find_element("(//div[contains(text(),'货款率')])[1]")
        self.find_element("(//span[contains(text(),'分成比例')])[1]")
        self.find_element("(//div[contains(text(),'预估收入(元)')])[1]")
        self.find_element("(//th[contains(text(),'结算时间')])[1]")
        self.find_element("(//th[contains(text(),'订单来源')])[1]")
        self.find_element("(//th[contains(text(),'订单状态')])[1]")
        self.find_element("(//th[contains(text(),'备注')])[1]")

        # 其他
        self.find_element("(//span[contains(text(),'导出数据')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_order_time_search_core(self):
        """
        时间选择器面板
        """
        self.baseLogin()
        self.click("(//input[@id='timeRange'])[1]")
        time.sleep(0.1)
        self.find_element("(//div[contains(text(),'00:00:00')])[1]")
        self.click("(//button[@class='ant-picker-header-prev-btn'])[1]")
        self.click("(//button[@class='ant-picker-header-super-next-btn'])[1]")
        self.click("(//div[@class='ant-picker-time-panel-cell-inner'][normalize-space()='03'])[1]")
        self.assert_text("确 定", "(//span[contains(text(),'确 定')])[1]")

    @pytest.mark.p0
    def test_kwaimoney_order_search_by_pay_time_core(self):
        """
        快赚客订单列表-订单信息无缺失
        """
        self.baseLogin()

        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.find_element("(//span[@title='10 条/页'])[1]")
        assert "ant-pagination-item-active" in self.get_attribute("(//li[@title='1'])[1]", "class")
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0:nth-child(2) td")
        for element in elements[0:-1]:
            assert element.text is not None and element.text != ""
        text = self.get_text("(//span[@class='src-Modules-KwaiMoney-Pages-Earnings-Order-List-index-module__data_total--1XMYh'])[1]")
        assert self.find_first_number(text) > 0

    @pytest.mark.p0
    def test_kwaimoney_order_search_by_settlement_time(self):
        """
        快赚客订单列表-按结算时间搜索-订单信息无缺失
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        assert "ant-pagination-item-active" in self.get_attribute("(//li[@title='1'])[1]", "class")
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0:nth-child(2) td")
        for element in elements[0:-1]:
            assert element.text is not None and element.text != ""

    @pytest.mark.p0
    def test_kwaimoney_order_search_by_status(self):
        """
        快赚客订单列表-按状态搜索-订单信息无缺失
        """
        self.baseLogin()
        self.select_time()
        time.sleep(2)

        self.click("(//span[@title='全部'])[1]")
        status_elements = self.find_elements("div.ant-select-item.ant-select-item-option")
        for expectedStr, element in zip(["全部", "已付款", "已收货", "已结算"], status_elements):
            self.assert_true(expectedStr == element.text)

        # 全部查询
        self.click("(//span[contains(text(),'查 询')])[1]")
        self.assert_true("true" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        self.assert_true("false" == self.get_attribute("//div[@title='已付款']", "aria-selected"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0:nth-child(2) td")
        for element in elements[0:-1]:  # 不验证备注
            assert element.text is not None and element.text != ""

        # 已付款查询
        self.click("(//span[@title='全部'])[1]")
        self.click("//div[@title='已付款']")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.assert_true("true" == self.get_attribute("//div[@title='已付款']", "aria-selected"))
        self.assert_true("false" == self.get_attribute("//div[@title='已收货']", "aria-selected"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        if len(elements) == 0:
            self.assert_text("暂无数据", "(//div[@class='ant-empty-description'])[1]")
        else:
            for element in elements:
                tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
                assert "已付款" == tds[-2].text

        # 已收货查询
        self.click("(//span[@title='已付款'])[1]")
        self.click("//div[@title='已收货']")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.assert_true("true" == self.get_attribute("//div[@title='已收货']", "aria-selected"))
        self.assert_true("false" == self.get_attribute("//div[@title='已结算']", "aria-selected"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        if len(elements) == 0:
            self.assert_text("暂无数据", "(//div[@class='ant-empty-description'])[1]")
        else:
            for element in elements:
                tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
                assert "已收货" == tds[-2].text

        # 已结算查询
        self.click("(//span[@title='已收货'])[1]")
        self.click("//div[@title='已结算']")
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.assert_true("true" == self.get_attribute("//div[@title='已结算']", "aria-selected"))
        self.assert_true("false" == self.get_attribute("//div[@title='全部']", "aria-selected"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        if len(elements) == 0:
            self.assert_text("暂无数据", "(//div[@class='ant-empty-description'])[1]")
        else:
            for element in elements:
                tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
                assert "已结算" == tds[-2].text


    @pytest.mark.p0
    def test_kwaimoney_order_search_by_itemId(self):
        """
        快赚客订单列表-按商品id搜索-订单信息无缺失
        """
        itemId = "22530692004916"
        self.baseLogin()
        self.select_time()
        self.input("(//input[@id='itemID'])[1]", itemId)
        self.click("(//span[contains(text(),'查 询')])[1]")
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        time.sleep(1)
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            itemElement = tds[3]
            itemInfo = itemElement.find_element(By.CSS_SELECTOR, "div.src-Modules-KwaiMoney-Pages-Earnings-Order-List-index-module__goodsId--1nTuD")
            assert itemId == str(self.find_first_number(itemInfo.text))


    @pytest.mark.p0
    def test_kwaimoney_order_search_by_pid(self):
        """
        快赚客订单列表-按pid搜索-订单信息无缺失
        """
        pid = "ks_2995700434_102_rsEUO9Kj71I"
        self.baseLogin()
        self.select_time()
        self.input("(//input[@id='pid'])[1]", pid)
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(2)
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            assert pid == tds[2].text


    @pytest.mark.p0
    def test_kwaimoney_order_cross(self):
        """
        快赚客订单列表-跨店跟单
        """
        itemId = "22648854424147"
        self.baseLogin()
        self.select_time()
        self.input("(//input[@id='itemID'])[1]", itemId)
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.assert_text("跨店收益", "(//span[@class='src-Modules-KwaiMoney-Pages-Earnings-Order-List-index-module__link_tag--cP22R'])[1]")
        self.click("(//span[@class='src-Modules-KwaiMoney-Pages-Earnings-Order-List-index-module__link_tag--cP22R'])[1]")
        time.sleep(0.2)
        self.find_element("(//div[contains(text(),'什么是跨店收益')])[1]")
        self.find_element("(//div[contains(text(),'跨店收益举例')])[1]")
        self.click("(//button[@class='ant-btn ant-btn-primary'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_order_with_gift(self):
        """
        快赚客订单列表-赠品订单
        """
        itemId = "22649273322008"
        self.baseLogin()
        self.select_time()
        self.input("(//input[@id='itemID'])[1]", itemId)
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.assert_text("送赠品 >", "(//div[contains(text(),'送赠品 >')])[1]")
        self.click(
            "(//div[@class='src-Modules-KwaiMoney-Pages-Earnings-Order-List-index-module__gift--1o-_T'])[1]")
        time.sleep(0.2)
        self.assert_text("赠品详情", "(//div[@id='rcDialogTitle0'])[1]")
        self.click("(//span[@class='ant-modal-close-x'])[1]")

    @pytest.mark.p0
    def test_kwaimoney_order_next_page(self):
        """
        快赚客订单列表-翻页
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)
        self.find_element("(//span[@title='10 条/页'])[1]")
        assert "ant-pagination-item-active" in self.get_attribute("(//li[@title='1'])[1]", "class")
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        assert len(elements) == 10
        firstOIds = []
        for element in elements:
            td = element.find_element(By.CSS_SELECTOR, "td.ant-table-cell")
            firstOIds.append(td.text)

        # 下一页
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        assert "ant-pagination-item-active" in self.get_attribute("(//li[@title='2'])[1]", "class")
        assert "ant-pagination-item-active" not in self.get_attribute("(//li[@title='1'])[1]", "class")
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        assert len(elements) == 10
        secondOIds = []
        for element in elements:
            td = element.find_element(By.CSS_SELECTOR, "td.ant-table-cell")
            secondOIds.append(td.text)

        isEqual = all(a == b for a, b in zip(firstOIds, secondOIds))  # 只要有一个不等，就返回false
        self.assert_true(isEqual is False)

    @pytest.mark.p0
    def test_kwaimoney_order_appoint_page(self):
        """
        快赚客订单列表-指定页
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)

        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        firstOIds = []
        for element in elements:
            td = element.find_element(By.CSS_SELECTOR, "td.ant-table-cell")
            firstOIds.append(td.text)

        self.input("(//input[@type='text'])[4]", 2)
        input = self.find_element("(//input[@type='text'])[4]")
        input.send_keys(Keys.ENTER)
        self.assert_true("active" in self.get_attribute("(//li[@title='2'])[1]", "class"))
        self.assert_true("active" not in self.get_attribute("(//li[@title='1'])[1]", "class"))
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        secondOIds = []
        for element in elements:
            td = element.find_element(By.CSS_SELECTOR, "td.ant-table-cell")
            secondOIds.append(td.text)

        isEqual = all(a == b for a, b in zip(firstOIds, secondOIds))  # 只要有一个不等，就返回false
        self.assert_true(isEqual is False)


    @pytest.mark.p0
    def test_kwaimoney_order_switch_order_num(self):
        """
        快赚客订单列表-翻页
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)

        # 切换为20
        self.click("(//span[@title='10 条/页'])[1]")
        time.sleep(0.1)
        self.assert_true(self.get_attribute("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(2)", "aria-selected") == "false")
        self.click("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(2)")
        self.assert_true(self.get_attribute("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(2)", "aria-selected") == "true")
        time.sleep(0.5)
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        assert len(elements) == 20

        # 切换为30
        self.click("(//span[@title='20 条/页'])[1]")
        time.sleep(0.1)
        self.assert_true(self.get_attribute("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(3)", "aria-selected") == "false")
        self.click("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(3)")
        self.assert_true(self.get_attribute("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(3)", "aria-selected") == "true")
        time.sleep(0.5)
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        assert len(elements) == 30

        # 切换为40
        self.click("(//span[@title='30 条/页'])[1]")
        time.sleep(0.1)
        self.assert_true(self.get_attribute("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(4)", "aria-selected") == "false")
        self.click("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(4)")
        self.assert_true(self.get_attribute("li.ant-pagination-options div.ant-select-item.ant-select-item-option:nth-child(4)", "aria-selected") == "true")
        time.sleep(0.5)
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        assert len(elements) == 40


    @pytest.mark.p1
    def test_kwaimoney_order_order_by_price_next_page_asc(self):
        """
        快赚客订单列表-根据价格排序后翻页,增序
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)

        # 未排序前
        preOIds = []
        prePrices = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            preOIds.append(tds[0].text)
            prePrices.append(float(tds[6].text.replace("¥", '')))

        self.click("(//span[contains(text(),'订单金额(元)')])[1]")
        time.sleep(0.5)

        # 升序
        nowOIds = []
        nowPrices = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nowOIds.append(tds[0].text)
            nowPrices.append(float(tds[6].text.replace("¥",'')))

        isEqual = all(a == b for a, b in zip(preOIds, nowPrices))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nowPrices[i] <= nowPrices[i + 1] for i in range(len(nowPrices) - 1)) is True) # 升序

        # 下一页
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        nextOIds = []
        nextPrices = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nextOIds.append(tds[0].text)
            nextPrices.append(float(tds[6].text.replace("¥", '')))

        isEqual = all(a == b for a, b in zip(nowOIds, nextOIds))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nextPrices[i] <= nextPrices[i + 1] for i in range(len(nextPrices) - 1)) is True)  # 升序

        # 组合判断
        comPrices = nowPrices + nextPrices
        self.assert_true(all(comPrices[i] <= comPrices[i + 1] for i in range(len(comPrices) - 1)) is True)  # 升序


    @pytest.mark.p1
    def test_kwaimoney_order_order_by_price_next_page_desc(self):
        """
        快赚客订单列表-根据价格排序后翻页,降序
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)

        # 未排序前
        preOIds = []
        prePrices = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            preOIds.append(tds[0].text)
            prePrices.append(float(tds[6].text.replace("¥", '')))

        self.click("(//span[contains(text(),'订单金额(元)')])[1]")
        time.sleep(0.5)
        self.click("(//span[contains(text(),'订单金额(元)')])[1]")
        time.sleep(0.5)

        # 降序
        nowOIds = []
        nowPrices = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nowOIds.append(tds[0].text)
            nowPrices.append(float(tds[6].text.replace("¥",'')))

        isEqual = all(a == b for a, b in zip(preOIds, nowPrices))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nowPrices[i] >= nowPrices[i + 1] for i in range(len(nowPrices) - 1)) is True)  # 降序

        # 下一页
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        nextOIds = []
        nextPrices = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nextOIds.append(tds[0].text)
            nextPrices.append(float(tds[6].text.replace("¥", '')))

        isEqual = all(a == b for a, b in zip(nowOIds, nextOIds))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nextPrices[i] >= nextPrices[i + 1] for i in range(len(nextPrices) - 1)) is True)  # 降序

        # 组合判断
        comPrices = nowPrices + nextPrices
        self.assert_true(all(comPrices[i] >= comPrices[i + 1] for i in range(len(comPrices) - 1)) is True)  # 降序

        # 上一页
        self.click("//li[@title='上一页']//button[@type='button']")


    @pytest.mark.p1
    def test_kwaimoney_order_order_by_commission_next_page_asc(self):
        """
        快赚客订单列表-根据佣金率排序后翻页,增序
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(2)

        # 未排序前
        preOIds = []
        preCommissions = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            preOIds.append(tds[0].text)
            preCommissions.append(float(tds[9].text.replace("%", '')))

        self.click("(//span[contains(text(),'佣金率')])[1]")
        time.sleep(1)

        # 升序
        nowOIds = []
        nowCommissions = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nowOIds.append(tds[0].text)
            nowCommissions.append(float(tds[9].text.replace("%", '')))
        isEqual = all(a == b for a, b in zip(preOIds, nowCommissions))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nowCommissions[i] <= nowCommissions[i + 1] for i in range(len(nowCommissions) - 1)) is True) # 升序

        # 下一页
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        nextOIds = []
        nextCommissions = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nextOIds.append(tds[0].text)
            nextCommissions.append(float(tds[9].text.replace("%", '')))

        isEqual = all(a == b for a, b in zip(nowOIds, nextOIds))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nextCommissions[i] <= nextCommissions[i + 1] for i in range(len(nextCommissions) - 1)) is True)  # 升序

        # 组合判断
        comPrices = nowCommissions + nextCommissions
        self.assert_true(all(comPrices[i] <= comPrices[i + 1] for i in range(len(comPrices) - 1)) is True)  # 升序


    # 目前后端存在bug
    @pytest.mark.skip
    @pytest.mark.p1
    def test_kwaimoney_order_order_by_commission_next_page_desc(self):
        """
        快赚客订单列表-根据佣金率排序后翻页,降序
        """
        self.baseLogin()
        self.select_time()
        self.click("(//span[contains(text(),'查 询')])[1]")
        time.sleep(1)

        # 未排序前
        preOIds = []
        preCommissions = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            preOIds.append(tds[0].text)
            preCommissions.append(float(tds[9].text.replace("%", '')))

        self.click("(//span[contains(text(),'佣金率')])[1]")
        time.sleep(1)
        self.click("(//span[contains(text(),'佣金率')])[1]")
        time.sleep(1)

        # 降序
        nowOIds = []
        nowCommissions = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nowOIds.append(tds[0].text)
            nowCommissions.append(float(tds[9].text.replace("%", '')))

        isEqual = all(a == b for a, b in zip(preOIds, nowCommissions))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nowCommissions[i] >= nowCommissions[i + 1] for i in range(len(nowCommissions) - 1)) is True)  # 降序

        # 下一页
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        nextOIds = []
        nextCommissions = []
        elements = self.find_elements("tr.ant-table-row.ant-table-row-level-0")
        for element in elements:
            tds = element.find_elements(By.CSS_SELECTOR, "td.ant-table-cell")
            nextOIds.append(tds[0].text)
            nextCommissions.append(float(tds[9].text.replace("%", '')))

        isEqual = all(a == b for a, b in zip(nowOIds, nextOIds))  # 判断订单是否发生变化
        self.assert_true(isEqual is False)
        self.assert_true(all(nextCommissions[i] >= nextCommissions[i + 1] for i in range(len(nextCommissions) - 1)) is True)  # 降序

        # 组合判断
        comPrices = nowCommissions + nextCommissions
        self.assert_true(all(comPrices[i] >= comPrices[i + 1] for i in range(len(comPrices) - 1)) is True)  # 降序

    @pytest.mark.p1
    def test_kwaimoney_order_search_reset(self):
        """
        筛选器重置
        """
        itemId = "22648854424147"
        self.baseLogin()
        self.select_time()
        self.input("(//input[@id='itemID'])[1]", itemId)
        self.assert_text(itemId, "(//input[@id='itemID'])[1]")
        self.click("(//button[@type='button'])[1]")
        self.assert_text("", "(//input[@id='itemID'])[1]")

