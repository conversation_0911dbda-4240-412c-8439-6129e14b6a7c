from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time

from test_case.assistant.base import BaseCase


# pc达人选品中心搜索场景及决策页场景
class TestDistributionPromterCenterSearch(DistributionToolTest):


    @pytest.mark.p0
    def test_distribution_search(self):
        self.driver.maximize_window()
        self.talent_login("DISTRIBUTION_PROMTER", 'maliya')  # 打开达人登录地址，登录账号
        time.sleep(3)
        self.assert_title('分销达人')  # 判断登录页面标头，确认登录成功
        time.sleep(2)
        self.click('//*[@id="rc_select_0"]')
        self.type('//*[@id="rc_select_0"]', '男装')
        time.sleep(3)
        self.click(
            "//button[@class='cps-ant-btn cps-ant-btn-primary cps-ant-btn-lg cps-ant-btn-two-chinese-chars cps-ant-input-search-button']")
        time.sleep(3)
        self.scroll_down(4)
        time.sleep(3)
        self.assert_element('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]') #判断这个位置的元素不为空  确保搜索返回数据
        self.click("//*[@id='root']/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div/div/div[2]")
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_element('//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]/div[2]/div[1]')
        self.click("//div[contains(text(),'推广数据')]")
        self.click("//div[contains(text(),'用户评价')]")
        self.click("//div[contains(text(),'TOP销售榜')]")
        time.sleep(2)
        self.scroll_down(1)
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[4]/div[1]/div[1]/div[3]/button/span[2]')
        time.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/div[1]/div/div[5]/div[2]/div/div/div/div[1]')
