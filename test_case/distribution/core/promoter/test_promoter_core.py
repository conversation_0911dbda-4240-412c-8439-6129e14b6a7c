from test_case.distribution.distribution_base_case import DistributionToolTest
import pytest
import time


class TestPromoterCore(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_PROMTER", "wb_huoyangyang")
        self.sleep(3)

    def test_distribution_menu(self):
        """ 登录成功-进入首页-判断首页元素菜单是否存在 """
        self.assert_title('分销达人')  # 判断登录页面标头，确认登录成功
        self.assert_text("搜索", "//span[contains(text(),'搜索')]")
        self.assert_text("活动广场", "//span[contains(text(),'活动广场')]")
        self.assert_text("效果看板", "//li[3]//div[1]//span[1]//span[1]//span[1]")
        self.assert_text("成交概览", "li[id='menu-wIl6Eg5saoc'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("发货情况", "li[id='menu-mH56d4yIHqY'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("订单数据", "li[id='menu-y-h1rH00zK8'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("氛围图工具", "//li[5]//span[1]//span[1]//span[1]//span[1]")
        self.assert_text("推广位管理", "li[id='menu-7629rfg0wgU'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("转链工具", "li[id='menu-g8TQ4TGKyN4'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("商家发票审核", "li[id='menu-UU-EHo1Z4ao'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("商家开票在线邀约",
                         "li[id='menu-te1rgOZEpMY'] span[class='dilu-main-badge'] span:nth-child(1)")
        # self.assert_text("平台开票", "li[id='menu-8bxBT7AIttU'] span[class='dilu-main-badge'] span:nth-child(1)")
        self.assert_text("达人保证金", "li[id='menu-9wwA5jhGmzg'] span[class='dilu-main-badge'] span:nth-child(1)")

    @pytest.mark.p1
    def test_distribution_search_by_id(self):
        """选品中心-根据商品id搜索"""
        self.driver.maximize_window()
        self.sleep(2)
        self.assert_title('分销达人') # 确认是否登录成功
        self.sleep(2)
        self.click('//*[@id="rc_select_0"]')
        self.sleep(2)
        self.type('//*[@id="rc_select_0"]', '23251819969484')
        self.sleep(2)
        self.click("//span[contains(text(),'搜索')]")
        self.sleep(2)
        self.scroll_down(1)
        self.sleep(2)
        self.click("(//div[@class='index-module__title--J3pxy'])[4]")
        self.sleep(2)
        self.click("//div[contains(text(),'推广数据')]")
        self.sleep(2)
        self.click("//div[contains(text(),'用户评价')]")
        self.sleep(2)
        self.click("//div[contains(text(),'TOP销售榜')]")
        self.sleep(2)
        self.scroll_down(1)
        self.sleep(2)
        self.click("(//span[contains(text(),'达人主页')])[1]")
