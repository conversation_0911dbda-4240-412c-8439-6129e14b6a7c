import os
import random
import time

import pytest
from seleniumbase.common.exceptions import NoSuchElementException

from test_case.distribution.distribution_base_case import DistributionToolTest

# 生成 30 到 90 之间的随机整数
random_number = random.randint(30, 90)
# 从指定文件中 随机取一个达人ID 输入并确认
script_directory = os.path.dirname(os.path.abspath(__file__))
relative_path = "./ShopOrientationPage.txt"
file_path = os.path.join(script_directory, relative_path)

"""
计划管理
计划管理-商品定向
pytest test_case/distribution/merchant/plan_management/test_commodity_orientation.py --headless -n=3
"""


@pytest.mark.skip
class TestDistributionGeneralPlan(DistributionToolTest):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.merchant_login("DISTRIBUTION_DOMAIN", 'wb_caijinwei')
        self.sleep(3)

    @pytest.mark.p0
    def test_commodity_orientation_page_core(self):
        """进入首页-点击计划管理-点击商品定向-判断是否进入商品定向页面"""
        self.click("//span[@class='kpro-workbench-layout-sider-v2__itemmenu-title'][contains(text(),'商品定向')]")
        self.sleep(3)
        self.assert_element(
            "//button[@class='kwaishop-cps-merchant-plan-pc-btn kwaishop-cps-merchant-plan-pc-btn-primary']")
        self.assert_text("添加达人", "(//div[@class='NYS_kfUDg6XEc081qe5w'])[1]")

