import time
import re
from selenium.webdriver.common.keys import Keys
from utils.convert_help import str2number
# from seleniumwire import webdriver


import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest

'''
快赚客推广专区-商品推广
'''

class TestKwaimoneyLivePromotion(DistributionToolTest):

    def baseLogin(self):
        try:
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        except:
            # 重试一次
            self.kwaimoney_login("DISTRIBUTION_KWAIMONEY", 'wb_caijinwei')  # 打开达人登录地址，登录账号
        time.sleep(2)
        self.driver.maximize_window()
        time.sleep(0.5)
        self.click("(//span[contains(text(),'推广专区')])[1]")
        time.sleep(0.5)
        self.click("(//a[contains(text(),'商品推广')])[1]")
        time.sleep(2)


    def getNumberFormStr(self, str):
        numbers = re.findall(r'\d+', str)
        return int(numbers[0])

    def test_promotion_good_field(self):
        """
        商品推广页面元素判断
        """
        self.baseLogin()
        # 检验筛选项
        self.assert_text("类目","(//span[@class='src-Components-FilterSelection-index-module__filterSelectionTitle--tNAYp'])[1]")
        self.assert_text("计划","(//span[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-FilterList-PlanFilters-index-module__planFiltersTitle--2K0XB'])[1]")
        self.assert_text("高级","(//span[contains(text(),'高级')])[1]")
        self.assert_text("筛选","(//span[contains(text(),'筛选')])[1]")

        # 检验表格字段
        self.assert_text("综合排序", "(//span[contains(text(),'综合排序')])[1]")
        self.assert_text("佣金比例", "(//span[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-FilterTool-index-module__sort_name--1fDbA'][contains(text(),'佣金比例')])[1]")
        self.assert_text("商品价格", "(//span[contains(text(),'商品价格')])[1]")
        self.assert_text("商品销量", "(//span[contains(text(),'商品销量')])[1]")


    def test_promotion_good_item_info(self):
        """
        商品推广页面-商品推广信息判断
        """
        self.baseLogin()
        # print(self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_action--5NOBu"))
        self.assert_text("查看详情", "(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb'][contains(text(),'查看详情')])[1]")
        self.assert_text("立即推广", "(//div[@class='src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button--2wgMS src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__link_button--2CDdb src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__button_color--30oiV'][contains(text(),'立即推广')])[1]")
        # 商品图片
        self.find_element("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_img--18Oov")
        # 商品名称
        itemName = self.get_text("p.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_title--3s98A")
        self.assert_true(itemName is not None and itemName != "")
        # 推广信息
        price = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_info--2yoHh div:nth-child(1) span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        commissionRate = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_info--2yoHh div:nth-child(2) span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        commissionPrice = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_info--2yoHh div:nth-child(3) span.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_value--FKFw3")
        self.assert_true(price != "" and commissionRate != "" and commissionPrice != "")
        # 店铺信息
        shopName = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__item_head_title--KTY17")
        self.assert_true(shopName is not None and shopName != "")
        # 销量
        stockNum = self.get_text("div.src-Modules-KwaiMoney-Pages-Promotion-Goods-DataList-CardView-CardItem-index-module__detail_right--zqm-2")
        self.assert_true("总销量" in stockNum)

