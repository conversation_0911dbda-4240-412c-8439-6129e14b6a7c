import datetime
import time
from time import sleep

import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

PRESALE_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/tool/presale/create?entry_src=toolsv2_all_marketing_tools'
PRESALE_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/tool/presale?entry_src=toolsv2_all_marketing_tools'



class TestMarketingPresale(BaseTestCase):

    # 点击添加商品-删除商品
    #@pytest.mark.skip
    @pytest.mark.p1
    def test_presale_create_page_delete_item(self):
        print('say presale create page delete item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(PRESALE_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建定金预售','//*[@id="root"]/div/div/div[1]/div/span[3]/span[1]')
        self.assert_text('创 建','//*[@id="create-presale"]/div[8]/div/div/div/div/div[1]/button/span')
        self.assert_text('取 消','//*[@id="create-presale"]/div[8]/div/div/div/div/div[2]/button/span')
        # 活动名称
        self.type('//*[@id="create-presale_campaignName"]','定金预售UI自动化测试')
        # 选择定金付款时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@id='create-presale_pre']", startTime)
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary marketing-fixed-price-presale-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束日期']", endTime)
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary marketing-fixed-price-presale-btn-sm']")
        sleep(2)

        # 选择尾款付款时间,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="create-presale"]/div[4]/div[2]/div/div/div/div[1]/div/div[1]/div/div/div/div')
        self.click("//div[@title='3小时']")

        # 选择尾款持续时间
        self.click('//*[@id="create-presale"]/div[4]/div[2]/div/div/div/div[1]/div/div[2]/div/div/div/div')
        self.click("(//div[contains(@title,'2天')])[2]")

        # 添加商品
        self.assert_text('添加商品','//*[@id="create-presale_hasItems"]/button/span')
        self.click('//*[@id="create-presale_hasItems"]/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品','//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消', "//button[@class='marketing-fixed-price-presale-btn']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/input',"定金预售UI-P自动化S测试商品1")
        self.click('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/span/button')

        # 添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary']")

        # 删除商品
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-link']")

        # 取消创建页面
        self.click('//*[@id="create-presale"]/div[8]/div/div/div/div/div[2]/button')
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/home')

    # 创建定金预售活动
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_presale_create_page_add_item(self):
        print('say presale add item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(PRESALE_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建定金预售', '//*[@id="root"]/div/div/div[1]/div/span[3]/span[1]')
        self.assert_text('创 建', '//*[@id="create-presale"]/div[8]/div/div/div/div/div[1]/button/span')
        self.assert_text('取 消', '//*[@id="create-presale"]/div[8]/div/div/div/div/div[2]/button/span')
        # 活动名称
        self.type('//*[@id="create-presale_campaignName"]', '定金预售UI自动化测试')
        # 选择定金付款时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@id='create-presale_pre']", startTime)
        self.click(
                "//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary marketing-fixed-price-presale-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束日期']", endTime)
        self.click(
                "//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary marketing-fixed-price-presale-btn-sm']")
        sleep(2)

        # 选择尾款付款时间,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="create-presale"]/div[4]/div[2]/div/div/div/div[1]/div/div[1]/div/div/div/div')
        self.click("//div[@title='3小时']")

        # 选择尾款持续时间
        self.click('//*[@id="create-presale"]/div[4]/div[2]/div/div/div/div[1]/div/div[2]/div/div/div/div')
        self.click("(//div[contains(@title,'2天')])[2]")

        # 添加商品1
        self.assert_text('添加商品', '//*[@id="create-presale_hasItems"]/button/span')
        self.click('//*[@id="create-presale_hasItems"]/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                             "//button[@class='marketing-fixed-price-presale-btn']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/input', "定金预售UI-P自动化S测试商品2")
        self.click('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/span/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary']")

        # 输入预售价
        self.type('//*[@id="create-presale_item_0_skuInfoList_0_skuPrePrice"]', "189")

        # 输入定金金额
        self.type('//*[@id="create-presale_item_0_skuInfoList_0_skuFixedPrice"]', "20")

        # 输入尾款立减金额
        self.type('//*[@id="create-presale_item_0_skuInfoList_0_skuReducePrice"]', "30")

        sleep(2)

        # 添加商品2
        self.assert_text('添加商品', '//*[@id="create-presale_hasItems"]/button/span')
        self.click('//*[@id="create-presale_hasItems"]/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 商品查询
        self.type('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/input', "定金预售UI-P自动化S测试商品3")
        self.click('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/span/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary']")

        # 输入预售价
        self.type('//*[@id="create-presale_item_1_skuInfoList_0_skuPrePrice"]', "189")

        # 输入定金金额
        self.type('//*[@id="create-presale_item_1_skuInfoList_0_skuFixedPrice"]', "20")

        # 输入尾款立减金额
        self.type('//*[@id="create-presale_item_1_skuInfoList_0_skuReducePrice"]', "30")

        # 点击创建
        self.click('//*[@id="create-presale"]/div[9]/div/div/div/div/div[1]/button')
        # url = self.get_current_url()


    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_presale_create_from_manage(self):
        print('say presale create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(PRESALE_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('定金预售', "//div[@class='Bjbr9am2qfuMaN7t4XSh'][contains(text(),'定金预售')]")
        self.assert_text('立即创建', "//a[contains(text(),'立即创建')]")
        # 点击立即创建
        # self.click("//span[@class='MxyeoMYSJFQGthZQXHSg marketing-fixed-price-presale-tooltip-disabled-compatible-wrapper marketing-fixed-price-presale-tooltip-open']")
        self.open(PRESALE_CREATE_PAGE_URL)
        self.assert_text('创建定金预售', '//*[@id="root"]/div/div/div[1]/div/span[3]/span[1]')
        self.assert_text('创 建', '//*[@id="create-presale"]/div[8]/div/div/div/div/div[1]/button/span')
        self.assert_text('取 消', '//*[@id="create-presale"]/div[8]/div/div/div/div/div[2]/button/span')
        # 活动名称
        self.type('//*[@id="create-presale_campaignName"]', '定金预售UI自动化测试')
        # 选择定金付款时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@id='create-presale_pre']", startTime)
        self.click(
            "//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary marketing-fixed-price-presale-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束日期']", endTime)
        self.click(
            "//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary marketing-fixed-price-presale-btn-sm']")
        sleep(2)

        # 选择尾款付款时间,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="create-presale"]/div[4]/div[2]/div/div/div/div[1]/div/div[1]/div/div/div/div')
        self.click("//div[@title='3小时']")

        # 选择尾款持续时间
        self.click('//*[@id="create-presale"]/div[4]/div[2]/div/div/div/div[1]/div/div[2]/div/div/div/div')
        self.click("(//div[contains(@title,'2天')])[2]")

        # 添加商品
        self.assert_text('添加商品', '//*[@id="create-presale_hasItems"]/button/span')
        self.click('//*[@id="create-presale_hasItems"]/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消', "//button[@class='marketing-fixed-price-presale-btn']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/input', "定金预售UI-P自动化S测试商品4")
        self.click('//*[@id="searchWrapperDOM"]/div[2]/span[1]/span/span/button')

        # 添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary']")

        # 取消创建页面
        self.click("//button[@class='marketing-fixed-price-presale-btn cS9blLBFqOo8EeJekdcQ']")
        url = self.get_current_url()
        print(url)
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/tool/presale?entry_src=toolsv2_all_marketing_tools')

    # 定金预售管理页面-查询+重置
    @pytest.mark.skip
    # @pytest.mark.p1
    def test_presale_manage_query(self):
        print('say presale_manage query')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(PRESALE_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('定金预售', "//div[@class='Bjbr9am2qfuMaN7t4XSh'][contains(text(),'定金预售')]")
        self.assert_text('活动状态', "//th[contains(text(),'活动状态')]")
        self.assert_text('活动名称', "//th[contains(text(),'活动名称')]")

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"未开始"
        self.click("//span[@title='全部']")
        self.click("//div[@title='未开始']")

        # 输入活动名称
        self.type("//input[@placeholder='请输入活动名称']", '定金预售UI自动化测试')

        # 点击查询
        self.click("//span[contains(text(),'查 询')]")

        # 检查查询结果
        self.assert_text('定金预售UI自动化测试',"//td[@class='marketing-fixed-price-presale-table-cell marketing-fixed-price-presale-table-cell-row-hover'][contains(text(),'定金预售UI自动化测试')]")

    # 定金预售查看页面-商品置为失效
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_presale_detail_invalid(self):
        print('say presale_detail invalid')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(PRESALE_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('定金预售', "//div[@class='Bjbr9am2qfuMaN7t4XSh'][contains(text(),'定金预售')]")
        self.assert_text('活动状态', "//th[contains(text(),'活动状态')]")
        self.assert_text('活动名称', "//th[contains(text(),'活动名称')]")

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"未开始"
        self.click("//span[@title='全部']")
        self.click("//div[@title='未开始']")

        # 输入活动名称
        self.type("//input[@placeholder='请输入活动名称']", '定金预售UI自动化测试')

        # 点击查询
        self.click("//span[contains(text(),'查 询')]")

        tag = True

        while tag:
            elements = self.find_elements("//td[contains(text(),'定金预售UI自动化测试')]")
            if len(elements) > 0:
                self.click("//div[@class='marketing-fixed-price-presale-space marketing-fixed-price-presale-space-horizontal marketing-fixed-price-presale-space-align-center']//div[1]")
                time.sleep(2)

                self.assert_text('定金预售UI自动化测试', '//*[@id="root"]/div/div[2]/div[1]/div[1]')
                self.click('//*[@id="root"]/div/div[2]/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div/button[2]')
                self.wait_for_element_visible("//span[@class='marketing-fixed-price-presale-modal-confirm-title']")
                self.assert_text('确定将商品置为失效吗？',"//span[@class='marketing-fixed-price-presale-modal-confirm-title']")

                self.click("//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary']")

                tag = False
            else:
                tag = False


    # 定金预售管理页面-结束活动
    @pytest.mark.skip
    # @pytest.mark.p1
    def test_presale_manage_finish(self):
        print('say presale_manage finish')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(PRESALE_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()

        # self.assert_text('定金预售', '//*[@id="root"]/div/div[2]/div[1]/div[1]/div/div[1]')
        self.assert_text('定金预售', "//div[@class='Bjbr9am2qfuMaN7t4XSh'][contains(text(),'定金预售')]")
        self.assert_text('活动状态', "//th[contains(text(),'活动状态')]")
        self.assert_text('活动名称', "//th[contains(text(),'活动名称')]")

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"未开始"
        self.click("//span[@title='全部']")
        self.click("//div[@title='未开始']")

        # 输入活动名称
        self.type("//input[@placeholder='请输入活动名称']", '定金预售UI自动化测试')

        # 点击查询
        self.click("//span[contains(text(),'查 询')]")

        # 点击结束活动
        tag = True
        while tag:
            elements = self.find_elements("//td[contains(text(),'定金预售UI自动化测试')]")
            if len(elements) > 0:
                self.click(
                    "button[class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-link u5TfasRQpl0PlZge_R6S']")
                time.sleep(2)

                self.wait_for_element_visible("//span[@class='marketing-fixed-price-presale-modal-confirm-title']")
                self.assert_text('确定结束活动吗？', "//span[@class='marketing-fixed-price-presale-modal-confirm-title']")

                self.click(
                    "//button[@class='marketing-fixed-price-presale-btn marketing-fixed-price-presale-btn-primary']")
            else:
                tag = False

        # 切换"已结束"tab
        self.click('//*[@id="root"]/div/div[2]/div[3]/div[1]/div[1]/div[1]/div[2]/span')
        self.click("//div[@title='已结束']")

        # 输入活动名称
        self.type("//input[@placeholder='请输入活动名称']", '定金预售UI自动化测试')

        # 点击查询
        self.click("//span[contains(text(),'查 询')]")

        self.assert_text('定金预售UI自动化测试',"//td[@class='marketing-fixed-price-presale-table-cell marketing-fixed-price-presale-table-cell-row-hover'][contains(text(),'定金预售UI自动化测试')]")

