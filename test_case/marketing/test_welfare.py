import time
from time import sleep
import datetime
import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

WELFARE_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/create'
WELFARE_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/manage'
WELFARE_DETAIL_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/detail?activityId=26590177828&tabKey=SELF_ITEM'

# @ddt
# class TestWelfare(BaseTestCase):

    # 添加商品弹窗关闭
    # @pytest.mark.p0
    # @pytest.mark.skip
    # def test_welfare_create_page_cancel(self):
    #     print('say welfare')