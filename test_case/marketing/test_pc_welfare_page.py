from time import sleep

import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

WELFARE_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/create?entry_src=toolsv2_common_tools'
WELFARE_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/manage?entry_src=toolsv2_common_tools'

class TestWelfare(BaseTestCase):

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_welfare_create_page(self):
        print('say welfare create page')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建福利购活动', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/h2')
        self.assert_text('自建福利购活动', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/div/div/div/div/div/div[1]/div[2]/div[1]')
        self.assert_text('为达人配置福利购活动','//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/div/div/div/div/div/div[2]/div[2]/div[1]')

        self.assert_text('设置任务', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[2]/h2')
        self.assert_text('任务类型',"//label[contains(text(),'任务类型')]")
        self.assert_text('活动任务', "//label[contains(text(),'活动任务')]")

        self.assert_text('选择商品', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[3]/h2')
        self.assert_text('选择商品', "//label[contains(text(),'选择商品')]")
        self.assert_text('商品信息设置', "//label[contains(text(),'商品信息设置')]")

        self.assert_text('风控等级', "//h2[contains(text(),'风控等级')]")
        self.assert_text('基础风控', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[4]/div/div/div/div/div/div[1]/div[2]/div[1]')
        self.assert_text('真身限购', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[4]/div/div/div/div/div/div[2]/div[2]/div[1]')
        self.assert_text('一般风控',
                         '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[4]/div/div/div/div/div/div[3]/div[2]/div[1]')
        self.assert_text('高级风控',
                         '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[4]/div/div/div/div/div/div[4]/div[2]/div[1]')

        self.assert_text('取 消', '//*[@id="root"]/div[2]/div[3]/button[1]/span')
        self.assert_text('创 建', '//*[@id="root"]/div[2]/div[3]/button[2]/span')


    # @pytest.mark.skip
    @pytest.mark.p0
    def test_welfare_manage_page(self):
        print('say welfare manage page')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('福利购', '//*[@id="root"]/div[2]/div/div[1]/div/div[1]/div[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div[2]/div/div[1]/div/div[1]/a/button/span')

        self.assert_text('自建商品活动管理', '//*[@id="rc-tabs-0-tab-SELF_ITEM"]')
        self.assert_text('分销商品活动管理', '//*[@id="rc-tabs-0-tab-DISTRIBUTION_ITEM"]')
        self.assert_text('达人福利品管理', '//*[@id="rc-tabs-0-tab-FOR_PROMOTER_ITEM"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-DATA_EFFECT"]')

        self.assert_text('待配置', '//*[@id="rc-tabs-0-panel-SELF_ITEM"]/div/div[1]/div/div[1]/div/label[1]/span[2]')
        self.assert_text('待开始', '//*[@id="rc-tabs-0-panel-SELF_ITEM"]/div/div[1]/div/div[1]/div/label[2]/span[2]')
        self.assert_text('活动中', '//*[@id="rc-tabs-0-panel-SELF_ITEM"]/div/div[1]/div/div[1]/div/label[3]/span[2]')
        self.assert_text('已结束', '//*[@id="rc-tabs-0-panel-SELF_ITEM"]/div/div[1]/div/div[1]/div/label[4]/span[2]')
        self.assert_text('全部', '//*[@id="rc-tabs-0-panel-SELF_ITEM"]/div/div[1]/div/div[1]/div/label[5]/span[2]')
        self.assert_text('查 询', '//*[@id="rc-tabs-0-panel-SELF_ITEM"]/div/div[1]/div/div[2]/form/div[3]/div/div/div/button/span')

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_welfare_select_item(self):
        print('say welfare select item')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建福利购活动', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[2]/h2')
        self.assert_text('选择商品', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[3]/h2')
        self.assert_text('风控等级', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[4]/h2')

        # 添加商品
        self.assert_text('添加商品', "//span[contains(text(),'添加商品')]")
        self.click(
            "//button[@class='ant-btn ant-btn-primary ant-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "//button[@class='ant-btn button___WiCe2']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']", "UI-G自动化B测试商品1")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        # 输入福利价
        self.type('//*[@id="items_0_displayPrice"]', "189")

        # 输入库存
        self.type('//*[@id="items_0_initStock"]', "1000")

        # 输入限购
        self.type('//*[@id="items_0_purchaseLimit"]', "10")

        # 点击取消创建
        self.click('//*[@id="root"]/div[2]/div[3]/button[1]')

    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_welfare_create_from_manage(self):
        print('say welfare create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('福利购', '//*[@id="root"]/div[2]/div/div[1]/div/div[1]/div[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div[2]/div/div[1]/div/div[1]/a/button/span')
        # 点击立即创建
        self.click('//*[@id="root"]/div[2]/div/div[1]/div/div[1]/a/button')

        self.assert_text('创建福利购活动', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[2]/h2')
        self.assert_text('选择商品', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[3]/h2')
        self.assert_text('风控等级', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[4]/h2')

        # 添加商品
        self.assert_text('添加商品', "//span[contains(text(),'添加商品')]")
        self.click(
            "//button[@class='ant-btn ant-btn-primary ant-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "//button[@class='ant-btn button___WiCe2']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']", "UI-G自动化B测试商品1")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        # 输入福利价
        self.type('//*[@id="items_0_displayPrice"]', "189")

        # 输入库存
        self.type('//*[@id="items_0_initStock"]', "1000")

        # 输入限购
        self.type('//*[@id="items_0_purchaseLimit"]', "10")

        # 点击取消创建
        self.click('//*[@id="root"]/div[2]/div[3]/button[1]')


