from time import sleep

from seleniumbase import BaseCase
from utils.kwaixiaodianUtils import KwaiXiaoDianToolTest
# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain

# 获取账号信息
account_data = get_account_info("kdstest102")
host = get_domain("MARKETING_DOMAIN")

class BaseTestCase(KwaiXiaoDianToolTest):

    def login(self, domain, account):

        self.kwaixiaodian_login(account)
        # account_data = get_account_info(account)
        # host = get_domain(domain)

        # self.open(host)
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        # sleep(0.1)
        # self.assert_text("扫码登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')  # div标签中的第一个元素
        # self.assert_text("手机号登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        # # sleep(0.1)
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        # # self.click("div.choseTab--1_6F7 > div:nth-child(1)")
        # self.type("input[placeholder='请输入手机号']", account_data['account'])
        # self.type("input[placeholder='请输入密码']", account_data['password'])
        # self.click("button[type='button']")
        #
        # self.open(host)
        # self.driver.maximize_window()
        # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        # self.click('//*[@id="username"]')
        # self.type('//*[@id="username"]', account_data['account'])
        # self.click('//*[@id="password"]')
        # self.type('//*[@id="password"]', account_data['password'])
        # self.click(
        #     '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
        # sleep(5)
        # url = self.get_current_url()
        # self.assert_equal(url, "https://s.kwaixiaodian.com/zone/marketing/tools/v2")
