from time import sleep

import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

SHOPPING_GROUP_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/fans-club-system/manageV2?activeTab=system'
SHOPPING_GROUP_RIGHT_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/fans-club-system/manageV2?activeTab=right'
SHOPPING_GROUP_QUERY_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/fans-club-system/manageV2?activeTab=query'

class TestShoppingGroup(BaseTestCase):

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_shopping_group_page(self):
        print('say shopping_group page')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('购物团首页', '//*[@id="rc-tabs-0-tab-system"]')
        self.assert_text('购物团', '//*[@id="root"]/div/div[3]/div[1]/div[1]/div/div[1]/div/span/div/div/span')
        self.assert_text('数据概览', '//*[@id="root"]/div/div[3]/div[1]/div[2]/div[1]/div/div[1]/span')
        self.assert_text('经营建议', '//*[@id="root"]/div/div[3]/div[1]/div[3]/div[1]/div/div/div/span[1]')

        self.assert_text('查看全部数据', '//*[@id="root"]/div/div[3]/div[1]/div[2]/div[1]/div/div[2]/div/span')
        self.assert_text('配置权益', '//div[@id="rc-tabs-0-tab-right"]')
        self.assert_text('数据中心','//div[@id="rc-tabs-0-tab-data"]')
        self.assert_text('成员查询','//div[@id="rc-tabs-0-tab-query"]')

        self.click('//div[@id="rc-tabs-0-tab-right"]')
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')
        self.click('//div[@id="rc-tabs-1-tab-manage"]')
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')

        self.click('//div[@id="rc-tabs-0-tab-data"]')
        self.assert_text('交易贡献', '//*[@id="root"]/div/div[3]/div[1]/div/div/div[1]/div[1]')
        self.assert_text('策略推荐', '//*[@id="root"]/div/div[3]/div[2]/div/div[1]')

        self.click('//div[@id="rc-tabs-0-tab-query"]')
        self.assert_text('购物团查询', '//*[@id="root"]/div/div[3]/div[1]/div[1]/div[1]/div/div/span')
        self.assert_text('巅峰团员榜', '//*[@id="root"]/div/div[3]/div[1]/div[2]/div[1]/div/div/span')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_shopping_group_right_page(self):
        print('say shopping_group right page')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_RIGHT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')

    # @pytest.mark.skip
    @pytest.mark.p1
    def test_shopping_group_name_edit(self):
        print('say shopping_group name edit')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('购物团首页', '//*[@id="rc-tabs-0-tab-system"]')
        self.assert_text('购物团', '//*[@id="root"]/div/div[3]/div[1]/div[1]/div/div[1]/div/span/div/div/span')
        self.assert_text('编辑团名','//*[@id="root"]/div/div[3]/div[1]/div[1]/div/div[2]/div/div[2]/button/span')

        # 点击编辑团名
        self.click('//*[@id="root"]/div/div[3]/div[1]/div[1]/div/div[2]/div/div[2]/button')
        self.wait_for_element_visible("//span[@class='kwaishop-marketing-fans-club-system-pc-modal-confirm-title']")
        self.assert_text('编辑购物团名称',"//span[@class='kwaishop-marketing-fans-club-system-pc-modal-confirm-title']")
        self.assert_text('购物团名称',"//label[contains(text(),'购物团名称')]")
        self.assert_text('取 消',"//span[contains(text(),'取 消')]")
        self.assert_text('确 定',"//span[contains(text(),'确 定')]")

        self.type("//input[@id='fansGroupName']",'购物团名称')

        self.click("//button[@class='kwaishop-marketing-fans-club-system-pc-btn']")

    # @pytest.mark.skip
    @pytest.mark.p1
    def test_shopping_group_enter_good_luck(self):
        print('say shopping_group enter good_luck')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_RIGHT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')

        # 跳转好运来
        self.click('//*[@id="root"]/div/div[3]/div[1]/div[2]/div/div[1]/div[1]/div[2]/div[1]/div')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/lottery/create?from=fans')
        self.assert_text('好运来抽奖创建','//*[@id="root"]/section/main/div[1]/div/span[3]/span[1]/span')

    # @pytest.mark.skip
    @pytest.mark.p1
    def test_shopping_group_enter_welfare(self):
        print('say shopping_group enter welfare')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_RIGHT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')

        # 跳转福利购
        self.click('//*[@id="root"]/div/div[3]/div[1]/div[2]/div/div[1]/div[1]/div[2]/div[2]/div')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/create?from=fans')
        self.assert_text('创建福利购活动', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/h2')

    @pytest.mark.p1
    def test_shopping_group_enter_pet(self):
        print('say shopping_group enter pet')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_RIGHT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')

        # 跳转购物团红包
        self.click('//*[@id="root"]/div/div[3]/div[1]/div[2]/div/div[1]/div[2]/div[2]/div[1]/div')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/pet/create?type=29')
        self.assert_text('创建宠爱红包','//*[@id="root"]/div[1]/span[3]/span[1]')

    @pytest.mark.p1
    def test_shopping_group_enter_coupon(self):
        print('say shopping_group enter coupon')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_RIGHT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')

        # 跳转购物团优惠券
        self.click('//*[@id="root"]/div/div[3]/div[1]/div[2]/div/div[1]/div[2]/div[2]/div[2]/div')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/coupon/create/seller-fans-level')
        self.assert_text('创建优惠券','//*[@id="root"]/section/main/div/div[1]/div/span[3]/span[1]/span')

    @pytest.mark.p2
    def test_shopping_group_right_query(self):
        print('say shopping_group right query')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_RIGHT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('新建权益', '//div[@id="rc-tabs-1-tab-create"]/span')
        self.assert_text('管理权益', '//div[@id="rc-tabs-1-tab-manage"]/span')

        # 切换管理权益tab
        self.click('//*[@id="rc-tabs-1-tab-manage"]')
        self.assert_text('权益类型','//*[@id="pro-form-wrapper"]/div/div[1]/div/div[1]/label')
        self.assert_text('权益状态', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div[1]/label')
        self.assert_text('活动名称', '//*[@id="pro-form-wrapper"]/div/div[3]/div/div[1]/label')
        self.assert_text('重 置', '//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[1]/button/span')
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')

        self.type('//*[@id="pro-form-wrapper"]/div/div[3]/div/div[2]/div/div/span/input','购物团优惠券20250516')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')

        sleep(2)

        self.assert_text('购物团优惠券20250516',"//td[contains(text(),'购物团优惠券20250516')]")

    @pytest.mark.p2
    def test_shopping_group_query_member(self):
        print('say shopping_group query member')

        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_GROUP_QUERY_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('购物团查询','//*[@id="root"]/div/div[3]/div[1]/div[1]/div[1]/div/div/span')
        self.assert_text('巅峰团员榜','//*[@id="root"]/div/div[3]/div[1]/div[2]/div[1]/div/div/span')
        self.assert_text('用户ID', '//*[@id="pro-form-wrapper"]/div/div[1]/div/div[1]/label')
        self.assert_text('重 置', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div/button[1]/span')
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div/button[2]/span')

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input','1275816938')
        self.click('//*[@id="pro-form-wrapper"]/div/div[2]/div/div/button[2]')

        sleep(2)

        self.assert_text('1275816938','//*[@id="root"]/div/div[3]/div[1]/div[1]/div[2]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr/td[1]')

