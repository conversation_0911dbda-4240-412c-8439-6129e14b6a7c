import time
from time import sleep
import datetime
import pytest as pytest
from ddt import ddt

from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
from selenium import webdriver

MARKETING_TOOLS_HOME_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/tools/v2?from=kwaixiaodian_market_pc'
GOOD_LUCK_LOTTERY_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/lottery/create?entry_src=toolsv2_common_tools'
GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage?entry_src=toolsv2_common_tools'
WELFARE_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/create?entry_src=toolsv2_common_tools'
WELFARE_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/manage?entry_src=toolsv2_common_tools'
WELFARE_DETAIL_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/toolset/welfare/detail?activityId=***********&tabKey=SELF_ITEM'
SECKILL_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/seckill/create?entry_src=toolsv2_common_tools'
SECKILL_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/seckill/manage?entry_src=toolsv2_common_tools'
PRESALE_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/tool/presale/create?entry_src=toolsv2_all_marketing_tools'
PRESALE_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/tool/presale?entry_src=toolsv2_all_marketing_tools'
PET_RED_PACKET_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/pet/create?type=0'
PET_RED_PACKET_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/pet/manage?entry_src=toolsv2_all_marketing_tools'
AUCTION_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/auction/create?entry_src=toolsv2_all_marketing_tools'
AUCTION_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/auction/manage?entry_src=toolsv2_all_marketing_tools'
MARKETING_ALL_TOOLS_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/tools/all-tools?from=kwaixiaodian_market_pc'
BUY_GIFT_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/create?entry_src=toolsv2_all_marketing_tools'
BUY_GIFT_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/manage?entry_src=toolsv2_all_marketing_tools'
MULTI_DISCOUNT_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/bulk-discount/save?mode=create&entry_src=toolsv2_all_marketing_tools'
MULTI_DISCOUNT_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/bulk-discount/manage?entry_src=toolsv2_all_marketing_tools'
FULL_DISCOUNT_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/full-discount/create?entry_src=toolsv2_all_marketing_tools'
FULL_DISCOUNT_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/full-discount/manage?entry_src=toolsv2_all_marketing_tools'
VALUABLE_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/valuable-coupon36/create?entry_src=toolsv2_all_marketing_tools'
VALUABLE_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/valuable-coupon36/manage?entry_src=toolsv2_all_marketing_tools'
GROUP_BUY_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/group-buy/create?entry_src=toolsv2_all_marketing_tools'
GROUP_BUY_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/group-buy/manage?entry_src=toolsv2_all_marketing_tools'
INCENTIVE_TASk_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/incentiveTask/create?entry_src=toolsv2_all_marketing_tools'
INCENTIVE_TASk_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/incentiveTask/manage?entry_src=toolsv2_all_marketing_tools'
VIP_ITEM_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights?tab=memberGoods&openCreate=1&entry_src=toolsv2_all_marketing_tools'
VIP_ITEM_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights'
VIP_FULLDISCOUNT_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights?tab=meetReturn'
VIP_SAMPLEGOODS_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights?tab=sampleGoods'
TALENT_MARKETING_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/talent-marketing2/create?entry_src=toolsv2_all_marketing_tools'
TALENT_MARKETING_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/talent-marketing2/home?entry_src=toolsv2_all_marketing_tools'
PINGJIA_YOULI_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/create?from=&scene=&toolCode=53&toolKey=pingjia-youli&entry_src=toolsv2_all_marketing_tools'
PINGJIA_YOULI_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/manage?from=&scene=&toolCode=53&toolKey=pingjia-youli&entry_src=toolsv2_all_marketing_tools'
SELLER_FULL_DISCOUNT_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/create?from=&scene=&toolKey=seller-full-discount&toolCode=52&entry_src=toolsv2_all_marketing_tools'
SELLER_FULL_DISCOUNT_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/manage?from=&scene=&toolKey=seller-full-discount&toolCode=52&entry_src=toolsv2_all_marketing_tools'
SKYFALL_COUPON_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/skyfall-coupon/management?entry_src=toolsv2_all_marketing_tools'
FANS_CLUB_PAGE_MANAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/fans-club-system/manage'



@ddt
class TestMarketingToolsPage(BaseTestCase):

    # @pytest.mark.skip
    # def setup_class(self):
    #     print('say hi setup')
    #
    #     # 浏览器最大化
    #     self.maximize_window()
    #     self.login("MARKETING_DOMAIN", "marketing")

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_marketing_tools_home_page(self):

        print('say marketing_tools_home')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MARKETING_TOOLS_HOME_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('营销数据', '//*[@id="homePosition"]/div[1]/div/div[1]/span[1]')
        self.assert_text('工具列表', '//*[@id="__common-tools-title__"]')
        self.assert_text('全部工具', "//button[@id='__common-tools-more-btn__']//span[contains(text(),'全部工具')]")
        self.assert_text('营销任务', '//*[@id="homePosition"]/div[2]/div/div[2]/div[1]/div[1]/div[1]')
        # 点击全部工具
        self.click("//button[@id='__common-tools-more-btn__']")
        sleep(2)
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/tools/all-tools')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_marketing_all_tools_page(self):

        print('say marketing_all_tools')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MARKETING_ALL_TOOLS_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        #self.assert_text('引流互动', '//div[@id="root"]/div/div[2]/div/div/div[2]')
        #self.hover('//div[@id="root"]/div/div[2]/div/div[2]/div')

    # 好运来
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_good_luck_lottery_create_page(self):

        print('say good_luck_lottery')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        # 抽奖人数
        self.type('//input[@id="lotteryNumber"]', '3')

        # 取消创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[1]')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_good_luck_lottery_manage_page(self):

        print('say good_luck_lottery_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('好运来活动数据总览', "//div[@class='marketing-lottery-card-head-title']")
        self.assert_text('全部', "//div[@class='marketing-lottery-tabs-tab marketing-lottery-tabs-tab-active']")
        self.assert_text('待开始', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[2]")
        self.assert_text('进行中', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[3]")
        self.assert_text('新建活动',"//span[contains(text(),'新建活动')]")

        self.is_element_visible('//*[@id="root"]/section/main/div/div[3]/div[1]')
        self.is_element_visible('//*[@id="root"]/section/main/div/div[4]/div/div/div[1]')

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        self.click("//button[@class='marketing-lottery-btn marketing-lottery-btn-primary']")


    #福利购
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_welfare_create_page(self):

        print('say welfare_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建福利购活动', '//*[@id="root"]/div[2]/div[2]/div[1]/div/div/form/div[1]/h2')
        self.assert_text('取 消', '//*[@id="root"]/div[2]/div[3]/button[1]/span')
        self.assert_text('创 建', '//*[@id="root"]/div[2]/div[3]/button[2]/span')

        self.click('//*[@id="root"]/div[2]/div[3]/button[1]/span')


    # @pytest.mark.skip
    @pytest.mark.p0
    def test_welfare_manage_page(self):

        print('say welfare_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('福利购', '//div[@id="root"]/div[2]/div/div/div/div/div[1]')
        self.assert_text('立即创建', '//div[@id="root"]/div[2]/div/div/div/div/a/button/span')
        self.is_element_visible('//div[@id="root"]/div[2]/div/div[2]/div[1]')
        self.is_element_visible('//div[@id="root"]/div[2]/div/div[2]/div[2]')

        self.click('//div[@id="root"]//div[2]/div/div/div/div/a/button/span')

    @pytest.mark.skip
    @pytest.mark.p0
    def test_welfare_detail_page(self):

        print('say welfare_detail')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WELFARE_DETAIL_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('更新福利购活动', '//*[@id="root"]/div[2]/div/div[2]')
        self.assert_text('更 新', "(//span[contains(text(),'更 新')])[1]")
        self.assert_text('取 消', "(//span[contains(text(),'取 消')])[1]")

        self.click("//button[@class='ant-btn']")

    #秒杀
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_seckill_create_page(self):
        print('say seckill_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SECKILL_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('秒杀设置', "//div[@class='seckill-header___gVPuA']")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")

        self.click("(//button[@type='button'])[4]")

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_seckill_manage_page(self):
        print('say seckill_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SECKILL_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建秒杀', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/button/span')
        #self.is_element_visible('//div[@id="root"]/section/main/div/div/div[3]/div[1]/div[2]/div[2]')

        self.click("//button[@class='kwaishop-seller-marketing-seckill-pc-btn kwaishop-seller-marketing-seckill-pc-btn-primary']")

    #定金预售
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_presale_create_page(self):

        print('say presale_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PRESALE_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创 建', '//*[@id="create-presale"]/div[8]/div/div/div/div/div[1]/button/span')
        self.assert_text('取 消', '//*[@id="create-presale"]/div[8]/div/div/div/div/div[2]/button/span')

        self.click('//*[@id="create-presale"]/div[8]/div/div/div/div/div[2]/button')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_presale_manage_page(self):
        print('say presale_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PRESALE_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('定金预售', "//div[@class='Bjbr9am2qfuMaN7t4XSh'][contains(text(),'定金预售')]")
        self.assert_text('活动状态', "//th[contains(text(),'活动状态')]")
        self.assert_text('活动名称', "//th[contains(text(),'活动名称')]")

        self.assert_text('立即创建', "//a[contains(text(),'立即创建')]")
        # 点击立即创建
        self.click("//span[@class='MxyeoMYSJFQGthZQXHSg marketing-fixed-price-presale-tooltip-disabled-compatible-wrapper']")

    #宠爱红包
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_pet_red_packet_create_page(self):

        print('say pet_red_packet_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PET_RED_PACKET_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创 建', '//*[@id="root"]/div[2]/div/div/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div[2]/div/div/button[2]/span')
        self.click('//*[@id="root"]/div[2]/div/div/button[1]/span')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_pet_red_packet_manage_page(self):

        print('say pet_red_packet_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PET_RED_PACKET_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('宠爱红包', '//div[@id="root"]/div[2]/div/div/div/div/div/div/div')
        self.is_element_visible('//div[@id="root"]/div[2]/div/div/div/div[3]/div')

    #老铁拍
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_auction_create_page(self):

        print('say auction_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(AUCTION_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.click("//button[@class='ant-btn']")

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_auction_manage_page(self):

        print('say auction_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(AUCTION_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//button[@type="button"]')

    #拼团
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_group_buy_create_page(self):

        print('say group_buy_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div/div[2]/div/div[2]/button[1]/span')
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/button[2]/span')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_group_buy_manage_page(self):

        print('say group_buy_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div/div[2]/div/div[2]/button/span')
        self.is_element_visible('//*[@id="rc-tabs-1-panel-1"]/div[2]/div[1]/div/div')

    #买赠
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_buy_gift_create_page(self):

        print('say buy_gift_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(BUY_GIFT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div/div[2]/div/div[3]/div/div[2]/button[1]/span')
        # 点击取消按钮
        self.click("//button[@class='kwaishop-marketing-buy-gift-pc-btn']")


    # @pytest.mark.skip
    @pytest.mark.p0
    def test_buy_gift_manage_page(self):

        print('say buy_gift_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(BUY_GIFT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[3]/button/span')
        self.is_element_visible('//*[@id="rc-tabs-0-tab-1"]')

    #多件优惠
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_multi_discount_create_page(self):

        print('say multi_discount_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MULTI_DISCOUNT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div[2]/div/div[3]/div[1]/div/div/div/div[2]/div[1]/button')
        self.click("(//button[@class='kwaishop-marketing-bulk-discount-pc-btn T4s3z0wPlml5nPDnwf0s'])[1]")

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_multi_discount_manage_page(self):

        print('say multi_discount_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MULTI_DISCOUNT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理','//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        self.assert_text('立即创建', '//*[@id="root"]/div[2]/div/div[1]/div/div[1]/div[2]/a[2]/span')

        self.click("//a[@class='kwaishop-marketing-bulk-discount-pc-btn kwaishop-marketing-bulk-discount-pc-btn-primary']")

   #满减满折
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_full_discount_create_page(self):

        print('say full_discount_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[2]')


    # @pytest.mark.skip
    @pytest.mark.p0
    def test_full_discount_manage_page(self):

        print('say full_discount_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="rc-tabs-0-tab-1"]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button')


    #新品预约优惠券
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_valuable_coupon_create_page(self):

        print('say valuable_coupon_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(VALUABLE_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[2]')

    # @pytest.mark.skip
    @pytest.mark.p0
    def test_valuable_coupon_manage_page(self):
        print('say valuable_coupon_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(VALUABLE_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="rc-tabs-0-tab-1"]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button')

    # # 天降红包
    # # @pytest.mark.skip
    # @pytest.mark.p0
    # def test_skyfall_coupon_page(self):
    #     print('say valuable_coupon_manage')
    #     # 浏览器最大化
    #     self.maximize_window()
    #     self.login("MARKETING_DOMAIN", "marketing")
    #     self.open(SKYFALL_COUPON_PAGE_URL)
    #     sleep(2)
    #     self.assert_no_404_errors()
    #     self.assert_text('快手官方天降红包活动管理', '//*[@id="root"]/section/main/div/div[1]/div/h1')
    #     # 分别点击进行中，已结束，全部。
    #     self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[1]/div/label[2]/span[2]')
    #     self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[1]/div/label[3]/span[2]')
    #     self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[1]/div/label[4]/span[2]')
    #     # 点击查询
    #     self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[2]/button')
    #     sleep(2)

    # 商家满返创建页
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_seller_full_discount_create_page(self):
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SELLER_FULL_DISCOUNT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建商家满返', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[5]/div[2]')
        self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        # 点击取消
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')

    # 商家满返管理页
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_seller_full_discount_manage_page(self):
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SELLER_FULL_DISCOUNT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('满返', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div/span/span')
        # 点击查询
        self.is_element_visible('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[2]')
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]')
        #判断查看详情是否可以正常跳转
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[3]')
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[7]/div/button[1]')
        self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[5]/div[2]')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button')
        # 判断活动数据是否可以正常跳转
        self.is_element_visible("//span[contains(text(),'已结束')]")
        self.click("(//label[@class='kwaishop-marketing-render-engine-pc-radio-button-wrapper kwaishop-marketing-render-engine-pc-radio-button kwaishop-marketing-render-engine-pc-radio-button-text'])[2]")
        self.assert_text('活动数据',"(//span[contains(text(),'活动数据')])[1]")
        self.click("(//button[@type='button'])[5]")
        self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]/div/div')
        self.assert_text('取 消',"//span[contains(text(),'取 消')]")
        self.click("div[class='kwaishop-marketing-render-engine-pc-modal-root'] button:nth-child(1)")
        #判断是否可以正常跳转创建页面
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/engine/create?entry_src=toolsv2_all_marketing_tools&from=&scene=&toolCode=52&toolKey=seller-full-discount')


    # 达人专属营销创建
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_talent_marketing_create_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "kdstest102")
        self.open(TALENT_MARKETING_CREATE_PAGE_URL)
        self.assert_no_404_errors()
        sleep(2)
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        self.click_if_visible('//*[@id="driver-popover-item"]/div[4]/button')
        self.type('//*[@id="root"]/div/div/form/div[2]/div[2]/div[1]/div/span/input', '1904866328')
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/button[1]')
        sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[4]/div/div/div/button')
        sleep(2)
        self.assert_text('重 置', '//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[1]/button')
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')
        self.is_element_visible('tr[data-row-key="22226681035928"] input[type="checkbox"].marketing-paas-checkbox-input')
        # 点击checkbox，（未定位到）
        # self.click('tr[data-row-key="22226681035928"] input[type="checkbox"].marketing-paas-checkbox-input')
        self.click('button[class="marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159"]')
        # 判断创建和取消按钮是否存在
        self.assert_text('创 建', '//*[@id="root"]/div/div[2]/div/div/div[2]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/div/div[2]/div/div/div[2]/button[2]')


    # 达人专属营销管理
    @pytest.mark.p0
    def test_talent_marketing_manage_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(TALENT_MARKETING_MANAGE_PAGE_URL)
        self.assert_no_404_errors()
        self.assert_text('达人专属营销', '//*[@id="root"]/div/div[2]/div/div[1]/div[1]/div[1]')
    #步骤一：商家为指定达人创建专属商品
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[1]/div[2]/div/div[2]/div/button[1]')
        sleep(5)
        self.assert_text('达人信息', "(//div[contains(text(),'达人信息')])[1]")
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/button[2]')
        sleep(2)
        # 点击商品管理
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[1]/div[2]/div/div[2]/div/button[2]')
        # 立即创建按钮+查询按钮
        self.is_element_visible('//*[@id="root"]/div/div[2]/div/div[2]/div[1]/div[2]/div/div[2]/div/button[2]')
        self.is_element_visible('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button/span')
        # 返回上一页
        self.click('//*[@id="root"]/div/div[1]/span[2]/span[1]/a')
    # 步骤二:配置专属商品营销活动(买赠)
        #立即创建
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/div/button[1]')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/buy-gift/create?entry_src=toolsv2_promoter_exclusives&type=3')
        self.go_back()
        # 活动管理
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/div[2]/div/div[2]/div/button[2]')
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/buy-gift/manage?entry_src=toolsv2_promoter_exclusives')

    # 评价有礼创建
    @pytest.mark.p0
    def test_comment_coupon_create_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建评价有礼', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        # 点击添加商品
        self.assert_text('添加商品', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')
        # 关闭弹窗
        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.click("//button[@aria-label='Close']")
        sleep(2)
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')
        

    # 评价有礼管理
    @pytest.mark.p0
    def test_comment_coupon_manage_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('评价有礼', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div/span/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-manage"]')
        # 点击立即创建
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button')
        sleep(2)
        self.assert_text('创建评价有礼', '//*[@id="root"]/div/div/div[2]/div/div[1]')

    # 定向激励任务创建页
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_Incentive_task_create_page(self):
        print('say Incentive_task_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        # 进入激励活动创建页面
        self.open(INCENTIVE_TASk_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div[2]/div/div[2]/div[1]')
        # 点击阅读协议并报名
        self.click('//*[@id="root"]/div[2]/div/div[2]/div[5]/button/span')
        # 判断取消和立即报名按钮是否存在
        self.is_element_visible('/html/body/div[4]/div/div[2]/div/div[2]/div/div/div[6]/button[1]/span')
        sleep(3)
        self.is_element_visible('/html/body/div[5]/div/div[2]/div/div[2]/div/div/div[6]/button[2]/span')

    # 定向激励任务活动管理页
    @pytest.mark.p0
    def test_Incentive_task_manage_page(self):
        print('say Incentive_task_create')
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(INCENTIVE_TASk_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        # 判断是否有定向激励任务标题
        self.assert_text('任务数据', '//*[@id="root"]/div[2]/div/div[2]/div[1]/div/div/span')
        self.assert_text('平台消耗金额', '//*[@id="root"]/div[2]/div/div[2]/div[2]/div[2]/div/div/div/div[1]/div/div/div[3]/div[2]')
        self.assert_text('商家消耗金额', '//*[@id="root"]/div[2]/div/div[2]/div[2]/div[2]/div/div/div/div[1]/div/div/div[4]/div[2]')

    # 会员专属商品
    @pytest.mark.p0
    def test_vip_item_create_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(VIP_ITEM_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('会员专属商品', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[1]/div/span[1]')
        self.assert_text('会员特价商品', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[2]/div/span[1]')
        self.is_element_visible('/html/body/div[3]/div/div[2]/div/div/div[3]/div/div/div[1]/button')
        self.click('svg[data-icon="system-close-medium-line"]')
        sleep(2)

    # 会员满额返礼品
    @pytest.mark.p0
    def test_vip_fulldiscount_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(VIP_FULLDISCOUNT_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible("//button[@class='kwaishop-seller-member-btn kwaishop-seller-member-btn-primary YNqBfCpCx6b0IGnjnmd_']")
        self.click("//button[@class='kwaishop-seller-member-btn kwaishop-seller-member-btn-primary YNqBfCpCx6b0IGnjnmd_']")
        sleep(2)
        self.assert_text('店铺满减券', "//span[contains(text(),'店铺满减券')]")
        self.assert_text('商品满减', "//span[contains(text(),'商品满减')]")
        # 关闭弹窗
        self.click("//button[@aria-label='Close']")
    # 会员派样
    @pytest.mark.p0
    def test_sampleGoods_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(VIP_SAMPLEGOODS_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.click('//*[@id="rc-tabs-0-panel-sampleGoods"]/div/div/div/div[1]/div/div[2]/button')
        sleep(2)
        self.assert_text('设置派样商品', 'div[class="G651OTYD5MeOP1e0WGwr"]')
        self. click('//*[@id="root"]/div/div/form/div/div[3]/div[2]/div/div/div/button')
        # self.assert_text('选择商品', '//*[@id="rcDialogTitle0"]')
        # 关闭弹窗
        self.click('svg[class="marketing-paas-modal-close-icon"]')
        sleep(2)

     # 粉丝团
    #@pytest.mark.skip
    def test_fans_club_page(self):
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FANS_CLUB_PAGE_MANAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('粉丝团', '//*[@id="root"]/div/div[2]/div/div[1]/div/span/div/span')
        self.assert_text('新建权益', '//*[@id="rc-tabs-0-tab-create"]/span')
        self.assert_text('管理权益', '//*[@id="root"]/div/div[3]/div[1]/div[2]/div[1]/div[1]/div/div[2]')

        # 管理权益
        self.click('//*[@id="root"]/div/div[3]/div[1]/div[2]/div[1]/div[1]/div/div[2]')
        # self.click('//*[@id="rc-tabs-0-tab-manage"]')
        sleep(1)
        # 点击重置和查询按钮
        #self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[1]/button')
        #self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button')
        # 权利列表展示
        self.assert_text('活动ID', '//*[@id="root"]/div/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('权益类型', '//*[@id="root"]/div/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
