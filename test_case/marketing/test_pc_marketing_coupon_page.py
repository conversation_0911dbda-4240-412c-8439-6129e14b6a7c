import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env


COUPON_CENTER_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/manage/v2'
SHOP_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=1'
ITEM_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=2'
FANS_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=4'
FANS_GROUP_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/seller-fans-level'
ANCHOR_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/anchor'
RIVER_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/stream-user-coupon'
WATCH_LIVE_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/live'
NEW_USER_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/new-user-coupon/create'
CROWD_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/coupon-in-crowd-package'
INTELLIGENT_FULL_DISCOUNT_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/intelligent-full-discount/create'
INTELLIGENT_FULL_DISCOUNT_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/intelligent-full-discount/manage'
TIMEING_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/chakra/create?activityType=1&amp;entrance=1'
TIMEING_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/chakra/index?activityType=1'
MEMBER_COUPON_PAGE_URL ='https://s.kwaixiaodian.com/zone/member/rights?tab=entry&autoOpen=1718876203713'
MEMBER_EXCLUSIVE_COUPON_PAGE_URL ='https://s.kwaixiaodian.com/zone/member/rights?tab=exclusive'
MEMBER_ITEM_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights?tab=memberGoods&autoOpen=1718883654444'
HEALTHCENTER_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/healthcenter/index'
HEALTHCENTER_RULE_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/healthcenter/rule'
ACTIVITY_LIST='https://s.kwaixiaodian.com/zone/business-invitation/record/list'
Coupon_ADDITEM_URL='https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=2&hideChangeCouponTypeBtn=1'
GROUP_CHAT_COUPON_URL='https://s.kwaixiaodian.com/zone/crm/chat/management'
XIAOFEIJIN_URL='https://s.kwaixiaodian.com/zone/marketing/recharge/manage'

@ddt
class TestMarketingCouponCreatePage(BaseTestCase):

    # #@pytest.mark.skip
    # def setup_class(self):
    #     print('say hi setup')
    #
    #     # 浏览器最大化
    #     #self.maximize_window()
    #     self.login("MARKETING_DOMAIN", "marketing")

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_river_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(RIVER_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_river_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_watch_live_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WATCH_LIVE_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_watch_live_coupon_info()


    @pytest.mark.p0
    def test_intelligent_full_discount_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(INTELLIGENT_FULL_DISCOUNT_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")


    @pytest.mark.p0
    def test_intelligent_full_discount_coupon_manage(self):

        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(INTELLIGENT_FULL_DISCOUNT_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_timing_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(TIMEING_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_timing_coupon_manage(self):

        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(TIMEING_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    def check_fill_in_river_coupon_info(self):
        time.sleep(2)
        self.assert_text('创 建', '//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')
        self.assert_text('取 消', '//div[@class="marketing-paas-btn-wrapper"]/button[2]/span')
        self.click('//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')


    def check_fill_in_watch_live_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[2]/span')
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[1]')


