import datetime
import time
from time import sleep

import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

GROUP_BUY_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/group-buy/create?entry_src=toolsv2_all_marketing_tools'
GROUP_BUY_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/group-buy/manage?entry_src=toolsv2_all_marketing_tools'



class TestMarketingGroupBuy(BaseTestCase):

    # 点击添加商品-删除商品
    #@pytest.mark.skip
    @pytest.mark.p1
    def test_group_buy_create_page_delete_item(self):
        print('say group_buy create page delete item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创 建',"//span[contains(text(),'创 建')]")
        self.assert_text('取 消',"//span[contains(text(),'取 消')]")

        # 选择活动时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始日期']", startTime)
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束日期']", endTime)
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 输入成团数量
        self.type("//input[@role='spinbutton']",'5')

        # 点击添加商品
        self.assert_text('添加商品','//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button/span')
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品','//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消', "//button[@class='marketing-paas-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']","UI-G自动化B测试商品1")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        # 输入拼团价和拼团库存
        self.type("//input[@placeholder='输入价格']", '150')
        self.type("//div[@class='marketing-paas-formily-grid-layout']//div//div[@class='marketing-paas-formily-item marketing-paas-formily-item-layout-horizontal marketing-paas-formily-item-feedback-layout-loose marketing-paas-formily-item-label-align-right marketing-paas-formily-item-control-align-left']//input[@role='spinbutton']", '100')

        # 删除商品
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-link']")

        # 取消创建页面
        self.click("//button[@class='kwaishop-marketing-group-buy-pc-btn']")
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/home')

    # 创建拼团活动
    @pytest.mark.skip
    @pytest.mark.p0
    def test_group_buy_create_page_add_item(self):
        print('say group_buy add item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")

        # 选择活动时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始日期']", startTime)
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束日期']", endTime)
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 输入成团数量
        self.type("//input[@role='spinbutton']", '5')

        # 点击添加商品
        self.assert_text('添加商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button/span')
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "//button[@class='marketing-paas-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']", "UI-G自动化B测试商品2")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        # 输入拼团价和拼团库存
        self.type("//input[@placeholder='输入价格']", '150')
        self.type(
            "//div[@class='marketing-paas-formily-grid-layout']//div//div[@class='marketing-paas-formily-item marketing-paas-formily-item-layout-horizontal marketing-paas-formily-item-feedback-layout-loose marketing-paas-formily-item-label-align-right marketing-paas-formily-item-control-align-left']//input[@role='spinbutton']",
            '100')

        # 点击创建
        self.click("//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary submit']")

        sleep(10)

        # self.wait_for_element_visible("//a[contains(text(),'管理拼团活动')]")
        # url = self.get_current_url()
        # self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/group-buy/manage')

        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        # 切换"全部"
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[4]')

        # 输入商品ID
        self.type("(//input[@placeholder='请输入'])[2]", '24532939351938')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

        tag = True

        while tag:
            elements = self.find_elements('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div[3]/button')
            if len(elements) > 0:
                self.click(
                    '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div[3]/button')
                time.sleep(2)

                self.wait_for_element_visible("//div[@role='tooltip']")
                self.assert_text('确认要结束活动吗？', "//div[@class='kwaishop-marketing-group-buy-pc-popover-message-title']")
                self.assert_text('确 定',
                                 "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary kwaishop-marketing-group-buy-pc-btn-sm']//span[contains(text(),'确 定')]")
                self.assert_text('取 消',
                                 "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-sm']//span[contains(text(),'取 消')]")
                self.click(
                    "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary kwaishop-marketing-group-buy-pc-btn-sm']")

                sleep(2)

                tag = False
            else:
                tag = False


    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_group_buy_create_from_manage(self):
        print('say group_buy create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('拼团活动', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('立即创建', "//span[contains(text(),'立即创建')]")
        # 点击立即创建
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[1]/div[1]/div/div[2]/div/div[2]/button')

        sleep(5)
        self.wait_for_element_visible('//*[@id="root"]/div/div[2]')

        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")

        # 选择活动时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始日期']", startTime)
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束日期']", endTime)
        self.click("//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 输入成团数量
        self.type("//input[@role='spinbutton']", '5')

        # 点击添加商品
        self.assert_text('添加商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button/span')
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "//button[@class='marketing-paas-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']", "UI-G自动化B测试商品3")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[3]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        # 输入拼团价和拼团库存
        self.type("//input[@placeholder='输入价格']", '150')
        self.type(
            "//div[@class='marketing-paas-formily-grid-layout']//div//div[@class='marketing-paas-formily-item marketing-paas-formily-item-layout-horizontal marketing-paas-formily-item-feedback-layout-loose marketing-paas-formily-item-label-align-right marketing-paas-formily-item-control-align-left']//input[@role='spinbutton']",
            '100')

        # 取消创建页面
        self.click("//button[@class='kwaishop-marketing-group-buy-pc-btn']")
        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/group-buy/manage?entry_src=toolsv2_all_marketing_tools')


    # 拼团管理页面-查询+重置
    #@pytest.mark.skip
    @pytest.mark.p1
    def test_group_buy_manage_query(self):
        print('say group_buy manage query')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('拼团活动', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('活动商品','//*[@id="rc-tabs-0-panel-1"]/div[2]/div[1]')
        self.assert_text('活动状态','//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/thead/tr/th[1]')

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"全部"
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[4]')

        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input','24532939351938')
        # 重置
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[2]')
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input','24532939351938')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

        # 检查查询结果
        self.assert_text('UI-G自动化B测试商品2',"//div[@class='OARaJvB7rfe3Xj8meUvG']")

    # 拼团管理页面编辑活动
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_group_buy_edit(self):
        print('say group_buy edit')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('拼团活动', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('活动商品', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[1]')
        self.assert_text('活动状态', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/thead/tr/th[1]')

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"待开始"
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[2]')

        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input', '24532939351938')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

        tag = True

        while tag:
            elements = self.find_elements("//div[@class='OARaJvB7rfe3Xj8meUvG']")
            if len(elements) > 0:
                self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div[2]/button')
                time.sleep(2)

                self.wait_for_element_visible("//div[@id='rcDialogTitle0']")
                self.assert_text('编辑活动', "//div[@id='rcDialogTitle0']")
                self.assert_text('确 定', "//span[contains(text(),'确 定')]")
                self.assert_text('取 消', "//span[contains(text(),'取 消')]")
                self.type("//input[@id='groupMinNum']",'8')
                self.click("//div[@class='kwaishop-marketing-group-buy-pc-modal-root']//button[2]")

                tag = False
            else:
                tag = False


    # 拼团管理页面-重启活动
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_group_buy_manage_restart(self):
        print('say group_buy manage restart')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('拼团活动', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('活动商品', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[1]')
        self.assert_text('活动状态', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/thead/tr/th[1]')

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"已结束"tab
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[3]')

        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input', '24532982807938')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

        tag = True

        while tag:
            elements = self.find_elements("//div[@class='OARaJvB7rfe3Xj8meUvG']")
            if len(elements) > 0:
                self.click(
                    '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div[2]/button')

                sleep(5)

                self.assert_text('创 建',"//span[contains(text(),'创 建')]")
                self.assert_text('取 消',"//span[contains(text(),'取 消')]")
                self.click(
                    "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary submit']")

                tag = False
            else:
                tag = False

        sleep(5)

        self.open(GROUP_BUY_MANAGE_PAGE_URL)

        tag2 = True

        while tag2:
            # 切换"待开始"tab
            self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[2]')

            # 输入商品ID
            self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input', '24532982807938')

            # 点击查询
            self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

            elements = self.find_elements("//div[@class='OARaJvB7rfe3Xj8meUvG']")
            if len(elements) > 0:
                # 结束活动
                self.click(
                    '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div[3]/button')
                time.sleep(2)
                self.wait_for_element_visible("//div[@role='tooltip']")
                self.assert_text('确认要结束活动吗？', "//div[@class='kwaishop-marketing-group-buy-pc-popover-message-title']")
                self.assert_text('确 定',
                                 "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary kwaishop-marketing-group-buy-pc-btn-sm']//span[contains(text(),'确 定')]")
                self.assert_text('取 消',
                                 "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-sm']//span[contains(text(),'取 消')]")
                self.click(
                    "//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary kwaishop-marketing-group-buy-pc-btn-sm']")

            else:
                tag2 = False



    # 拼团管理页面-结束活动
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_group_buy_manage_finish(self):
        print('say group_buy manage finish')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GROUP_BUY_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('拼团活动', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('活动商品', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[1]')
        self.assert_text('活动状态','//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/thead/tr/th[1]')

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换"待开始"
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[2]')

        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input', '24532939351938')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

        tag = True

        while tag:
            elements = self.find_elements("//div[@class='OARaJvB7rfe3Xj8meUvG']")
            if len(elements) > 0:
                self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div[3]/button')
                time.sleep(2)

                self.wait_for_element_visible("//div[@role='tooltip']")
                self.assert_text('确认要结束活动吗？',"//div[@class='kwaishop-marketing-group-buy-pc-popover-message-title']")
                self.assert_text('确 定',"//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary kwaishop-marketing-group-buy-pc-btn-sm']//span[contains(text(),'确 定')]")
                self.assert_text('取 消',"//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-sm']//span[contains(text(),'取 消')]")
                self.click("//button[@class='kwaishop-marketing-group-buy-pc-btn kwaishop-marketing-group-buy-pc-btn-primary kwaishop-marketing-group-buy-pc-btn-sm']")

                tag = False
            else:
                tag = False

        # 切换"已结束"tab
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[3]')

        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div[2]/input', '24532939351938')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[3]/button[1]')

        self.assert_text('UI-G自动化B测试商品2', "//div[@class='OARaJvB7rfe3Xj8meUvG']")

