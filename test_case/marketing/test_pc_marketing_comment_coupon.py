import datetime
from time import sleep

import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

PINGJIA_YOULI_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/create?from=&scene=&toolCode=53&toolKey=pingjia-youli&entry_src=toolsv2_common_tools'
PINGJIA_YOULI_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/manage?from=&scene=&toolCode=53&toolKey=pingjia-youli&entry_src=toolsv2_common_tools'



class TestMarketingCommentCoupon(BaseTestCase):

    # 点击添加商品-关闭弹窗
    #@pytest.mark.skip
    @pytest.mark.p2
    def test_common_coupon_create_page_select_item_close(self):
        print('say common_coupon_create close')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建评价有礼','//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建','//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消','//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[1]/div[2]/div/div/span/input','UI自动化测试')
        # 选择活动时间
        self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        # 添加商品
        self.assert_text('添加商品','//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品','//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消', "//button[@class='kwaishop-marketing-render-engine-pc-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")
        self.assert_text('重 置',"//span[contains(text(),'重 置')]")
        self.assert_text('查 询',"//span[contains(text(),'查 询')]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input',"评价有礼ui自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 重置
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[1]/button')
        self.assert_text('','//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input')

        # 关闭弹窗
        self.click("//button[@aria-label='Close']")

        # 选择券面额,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[6]/div[2]/div/div/div/div')
        self.click("(//div[@title='5元'])[1]")

        # 输入券张数
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[7]/div[2]/div/div/div/div/div[1]/div[2]/input',"100")
        # 取消创建页面
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')
        # url = self.get_current_url()
        # print(url) 'https://s.kwaixiaodian.com/zone/home'
        # self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/engine/manage?entry_src=toolsv2_common_tools&from=&scene=&toolCode=53&toolKey=pingjia-youli')

    # 点击添加商品-取消弹窗
    # @pytest.mark.skip
    @pytest.mark.p2
    def test_common_coupon_create_page_select_item_cancel(self):
        print('say common_coupon_create cancel')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建评价有礼', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[1]/div[2]/div/div/span/input', 'UI自动化测试')
        # 选择活动时间
        self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)
        # 添加商品
        self.assert_text('添加商品', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消', "//button[@class='kwaishop-marketing-render-engine-pc-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")
        self.assert_text('查 询', "//span[contains(text(),'查 询')]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "评价有礼ui自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 重置
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[1]/button')
        self.assert_text('', '//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input')

        # 取消添加
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn Nm5joasMziebb8vE2159']")

        # 选择券面额,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[6]/div[2]/div/div/div/div')
        self.click("(//div[@title='5元'])[1]")
        # 输入券张数
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[7]/div[2]/div/div/div/div/div[1]/div[2]/input',"100")
        # 取消创建页面
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')

    # 点击添加商品-确认添加(最后取消创建)
    # @pytest.mark.skip
    @pytest.mark.p2
    def test_common_coupon_create_page_add_item_cancel(self):
        print('say common_coupon add item cancel')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建评价有礼', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[1]/div[2]/div/div/span/input', 'UI自动化测试')
        # 选择活动时间
        self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)
        # 添加商品
        self.assert_text('添加商品', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "//button[@class='kwaishop-marketing-render-engine-pc-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")
        self.assert_text('查 询', "//span[contains(text(),'查 询')]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "评价有礼ui自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        self.click('//tbody/tr[2]/td[1]/label[1]')

        # 点击添加商品
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary Nm5joasMziebb8vE2159']")

        # 修改商品
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')
        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.click("//button[@aria-label='Close']")

        # 选择券面额,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[6]/div[2]/div/div/div/div')
        self.click("(//div[@title='5元'])[1]")

        # 输入券张数
        self.type(
            '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[7]/div[2]/div/div/div/div/div[1]/div[2]/input',
            "100")
        # 取消创建页面
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')

    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_common_coupon_create_from_manage(self):
        print('say common_coupon create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('评价有礼', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div/span/span')
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button/span')
        # 点击立即创建
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button')

        self.assert_text('创建评价有礼', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[1]/div[2]/div/div/span/input', 'UI自动化测试')
        # 选择活动时间
        self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)
        # 添加商品
        self.assert_text('添加商品','//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询'
        self.assert_text('取 消',"//button[@class='kwaishop-marketing-render-engine-pc-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")
        self.assert_text('查 询', "//span[contains(text(),'查 询')]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "评价有礼ui自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        self.click('//tbody/tr[2]/td[1]/label[1]')

        # 点击添加商品
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary Nm5joasMziebb8vE2159']")

        # 选择券面额,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[6]/div[2]/div/div/div/div')
        self.click("(//div[@title='5元'])[1]")

        # 输入券张数
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[7]/div[2]/div/div/div/div/div[1]/div[2]/input',"100")
        # 取消创建页面
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')

        sleep(5)
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/engine/manage?from=&scene=&toolCode=53&toolKey=pingjia-youli&entry_src=toolsv2_common_tools')

    # 点击添加商品-确认添加
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_common_coupon_create_page_add_item(self):
        print('say common_coupon add item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建评价有礼', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[1]/div[2]/div/div/span/input', 'UI自动化测试')
        # 选择活动时间
        self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)
        # 添加商品
        self.assert_text('添加商品', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',"//button[@class='kwaishop-marketing-render-engine-pc-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")
        self.assert_text('查 询', "//span[contains(text(),'查 询')]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "评价有礼ui自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary Nm5joasMziebb8vE2159']")

        # 选择券面额,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[6]/div[2]/div/div/div/div')
        self.click("(//div[@title='5元'])[1]")

        # 输入券张数
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[7]/div[2]/div/div/div/div/div[1]/div[2]/input',"100")
        # 点击创建
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]')

        sleep(5)
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/engine/manage?entry_src=toolsv2_common_tools&from=&scene=&toolCode=53&toolKey=pingjia-youli')


        # 切换"待开始"tab
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]')

        # self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input','UI自动化测试')

        # 点击查询
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]')

        # 检查查询结果
        # self.assert_text('UI自动化测试',"//div[contains(text(),'UI自动化测试')]")

    # 评价有礼管理页面-查询+重置
    #@pytest.mark.skip
    @pytest.mark.p1
    def test_common_coupon_manage_query(self):
        print('say common_coupon_manage query')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理','//*[@id="rc-tabs-0-tab-manage"]')
        self.assert_text('活动中','//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[1]/span[2]')
        self.assert_text('待开始','//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]/span[2]')
        self.assert_text('已结束','//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[3]/span[2]')
        self.assert_text('全部','//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[4]/span[2]')

        self.assert_text('查 询', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]/span')
        self.assert_text('重 置', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[2]/span')

        # 切换"待开始"tab
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]')

        # 切换"全部"tab
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[4]')

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input','UI自动化测试')
        # 重置
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[2]')
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化测试')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]')

        # 检查查询结果
        self.assert_text('UI自动化测试',"//div[contains(text(),'UI自动化测试')]")

    # 评价有礼管理页面-查看活动
    # @pytest.mark.skip
    @pytest.mark.p2
    def test_common_coupon_manage_check(self):
        print('say common_coupon_manage check')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-manage"]')
        self.assert_text('活动中', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[1]/span[2]')
        self.assert_text('待开始', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]/span[2]')
        self.assert_text('已结束', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[3]/span[2]')
        self.assert_text('全部', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[4]/span[2]')

        self.assert_text('查 询', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]/span')
        self.assert_text('重 置', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[2]/span')

        # 切换"待开始"tab
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]')
        # 切换"活动中"tab
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[1]')

        # 切换"全部"tab
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[4]')
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化测试')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]')

        # 点击查看活动
        self.click('//tbody//button[1]')
        self.wait_for_element_visible("//div[@class='form-title___hmfLf']")
        self.assert_text('UI自动化测试',"//input[@placeholder='请输入活动名称']")
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button')

        sleep(5)
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/engine/manage?from=&scene=&toolCode=53&toolKey=pingjia-youli&entry_src=toolsv2_common_tools')


    # 评价有礼管理页面-结束活动
    # @pytest.mark.skip
    @pytest.mark.p1
    def test_common_coupon_manage_finish(self):
        print('say common_coupon_manage finish')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(PINGJIA_YOULI_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-manage"]')
        self.assert_text('活动中', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[1]/span[2]')
        self.assert_text('待开始', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]/span[2]')
        self.assert_text('已结束', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[3]/span[2]')
        self.assert_text('全部', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[4]/span[2]')

        self.assert_text('查 询', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]/span')
        self.assert_text('重 置', '//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[2]/span')

        # 切换"待开始"tab
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]')
        # 切换"全部"tab
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[4]')

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化测试')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]')

        # 点击结束活动
        tag = True
        while tag:
            elements = self.find_elements("tbody button:nth-child(2)")
            print(len(elements))
            if len(elements) > 0:
                print(len(elements))
                self.click("tbody button:nth-child(2)")
            else:
                tag = False

        # 切换"已结束"tab
        self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[3]')
        self.type("//input[@placeholder='请输入']","UI自动化测试")
        self.click("//button[@type='submit']")
        self.assert_text('UI自动化测试',"//div[contains(text(),'UI自动化测试')]")

