import random
import time
from time import sleep
import datetime
import pytest as pytest
from ddt import ddt
import os

from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from test_case.marketing.base import BaseTestCase
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env, get_work_dir
from selenium import webdriver

GOOD_LUCK_LOTTERY_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/lottery/create?entry_src=toolsv2_all_marketing_tools'
GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage?entry_src=toolsv2_all_marketing_tools'


@ddt
class TestGoodLuckLotteryPage(BaseTestCase):

    #点击添加商品弹窗-关闭弹窗
    @pytest.mark.p0
    @pytest.mark.skip
    def test_good_luck_create_page_select_item_cancel(self):
        print('say good_luck_lottery')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        # 评论直播间
        self.type("//input[@placeholder='用户填写的评论']", '32132')

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click('//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')

        # 商品查询
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "测试")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")

        # 抽奖人数
        self.type('//input[@id="lotteryNumber"]', '3')

        # 取消创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[1]')
        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage')

    #点击新建奖品弹窗-取消弹窗
    @pytest.mark.p1
    @pytest.mark.skip
    def test_good_luck_create_page_create_item_cancel(self):
        print('say good_luck_lottery')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        # 评论直播间
        self.type("//input[@placeholder='用户填写的评论']",'32132')

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')
        self.assert_text('在售商品', '//*[@id="rc-tabs-0-tab-selfItem"]')
        self.assert_text('自建奖品', '//*[@id="rc-tabs-0-tab-lotteryItem"]')

        # 切换至自建奖品tab
        self.click("(//div[@id='rc-tabs-0-tab-lotteryItem'])[1]")

        # 点击新建奖品
        self.click("(//button[@type='button'])[7]")
        time.sleep(2)
        self.wait_for_element_visible("(//div[@class='marketing-lottery-drawer-content-wrapper'])[1]")
        self.assert_text('保 存', "//span[contains(text(),'保 存')]")
        self.assert_text('取 消', "//button[@class='marketing-lottery-btn lottery-goods-create-cancel']//span[contains(text(),'取 消')]")

        #点击取消按钮
        self.click("//button[@class='marketing-lottery-btn lottery-goods-create-cancel']")

        # 关闭添加商品弹窗
        self.click("//span[@aria-label='system-close-small-line']//*[name()='svg']")

        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/create?entry_src=toolsv2_all_marketing_tools')


    #新建奖品-保存奖品
    @pytest.mark.p1
    @pytest.mark.skip
    def test_good_luck_create_page_create_item(self):
        print('say good_luck_lottery')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        # 评论直播间
        self.type("//input[@placeholder='用户填写的评论']", '32132')

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')
        self.assert_text('在售商品', '//*[@id="rc-tabs-0-tab-selfItem"]')
        self.assert_text('自建奖品', '//*[@id="rc-tabs-0-tab-lotteryItem"]')

        # 切换至自建奖品tab
        self.click("(//div[@id='rc-tabs-0-tab-lotteryItem'])[1]")

        # 点击新建奖品
        self.click("(//button[@type='button'])[7]")
        time.sleep(2)
        self.wait_for_element_visible("(//div[@class='marketing-lottery-drawer-content-wrapper'])[1]")
        self.assert_text('保 存', "//span[contains(text(),'保 存')]")
        self.assert_text('取 消',
                         "//button[@class='marketing-lottery-btn lottery-goods-create-cancel']//span[contains(text(),'取 消')]")


        # self.assert_text('上传图片', '/html/body/div[4]/div/div[2]/div/div/div[2]/div[1]/div/form/div[1]/div[2]/div[1]/div/span/div/span/div/div')
        path = os.path.abspath("..")
        print(path + '/test_data/img/marketing_lottery_item.jpg')
        self.choose_file('input[type="file"]', path + '/test_data/img/marketing_lottery_item.jpg')
        time.sleep(4)

        num = random.randint(0,9)
        itemTitle = 'UI自动化好运来奖品' + str(num)

        self.type('//*[@id="itemTitle"]', itemTitle)

        # 点击奖品类目,input下拉框，先点击下拉框，再点击下拉框其中的输入项
        self.click(
            "//body/div/div[@class='marketing-lottery-drawer marketing-lottery-drawer-right marketing-lottery-drawer-open lottery-new-goods-drawer']/div[@class='marketing-lottery-drawer-content-wrapper']/div[@class='marketing-lottery-drawer-content']/div[@class='marketing-lottery-drawer-wrapper-body']/div[@class='marketing-lottery-drawer-body']/div[@class='marketing-lottery-spin-nested-loading']/div[@class='marketing-lottery-spin-container']/form[@class='marketing-lottery-form marketing-lottery-form-horizontal']/div[3]/div[2]/div[1]/div[1]/div[1]")
        self.click("//div[@title='箱包/饰品']")

        self.type('//*[@id="price"]', 2)
        self.type('//*[@id="stock"]', 9000)

        #点击保存
        self.click('/html/body/div[4]/div/div[2]/div/div/div[2]/div[2]/button[2]/span')

        self.wait_for_element_present('/html/body/div[5]/div')
        #关闭添加商品弹窗
        self.click("(//button[@aria-label='Close'])[1]")


        # 取消活动创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[1]')
        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage')

    # 评论任务创建
    @pytest.mark.skip
    @pytest.mark.p0
    def test_good_luck_comment_create(self):
        print('say good_luck_lottery create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(3)
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        # 评论直播间
        self.assert_text('评论直播间', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[2]/div[2]/div/div/div[2]/div/div/div[1]/div/span[2]/span')
        self.type("//input[@placeholder='用户填写的评论']", '老铁评论')

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')
        self.assert_text('在售商品', '//*[@id="rc-tabs-0-tab-selfItem"]')
        self.assert_text('自建奖品', '//*[@id="rc-tabs-0-tab-lotteryItem"]')

        self.click('//*[@id="rc-tabs-0-tab-lotteryItem"]')

        #自建奖品按商品名称选择
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化好运来奖品')
        #点击查询按钮
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        #商品选择框选择
        self.click('//tbody/tr[2]/td[1]/label[1]')

        # 自建奖品选择后保存确认
        self.assert_text('确 认', '//*[@class="marketing-lottery-modal-footer"]/button[2]/span')
        self.click('//*[@class="marketing-lottery-modal-footer"]/button[2]')
        time.sleep(2)

        # 抽奖人数
        self.type('//input[@id="lotteryNumber"]', '3')

        # 创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[2]')
        self.wait_for_element_visible("//span[contains(@class,'marketing-lottery-modal-confirm-title')]")
        self.assert_text('确认提交', "//span[contains(@class,'marketing-lottery-modal-confirm-title')]")
        self.assert_text('确 定', "//span[contains(text(),'确 定')]")
        self.assert_text('取 消', "//div[@class='marketing-lottery-modal-confirm-btns']//span[contains(text(),'取 消')]")

        self.click("//div[@class='marketing-lottery-modal-body']//button[2]")

        sleep(5)

        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage')

    # 关注任务创建
    @pytest.mark.skip
    @pytest.mark.p0
    def test_good_luck_follow_create(self):
        print('say good_luck_lottery create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(3)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        # 评论直播间
        self.assert_text('评论直播间','//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[2]/div[2]/div/div/div[2]/div/div/div[1]/div/span[2]/span')

        self.click("(//div[@class='marketing-lottery-select-selector'])[1]")
        self.click("(//div[@class='marketing-lottery-select-item-option-content'])[3]")
        self.assert_text('主播粉丝','//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[2]/div[2]/div/div/div[2]/div/div/div/div/span[2]/span')

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')
        self.assert_text('在售商品', '//*[@id="rc-tabs-0-tab-selfItem"]')
        self.assert_text('自建奖品', '//*[@id="rc-tabs-0-tab-lotteryItem"]')

        self.click('//*[@id="rc-tabs-0-tab-lotteryItem"]')

        # 自建奖品按商品名称选择
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化好运来奖品')
        # 点击查询按钮
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 商品选择框选择
        self.click('//tbody/tr[2]/td[1]/label[1]')

        # 自建奖品选择后保存确认
        self.assert_text('确 认', '//*[@class="marketing-lottery-modal-footer"]/button[2]/span')
        self.click('//*[@class="marketing-lottery-modal-footer"]/button[2]')
        time.sleep(2)

        # 抽奖人数
        self.type('//input[@id="lotteryNumber"]', '3')

        # 创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[2]')
        self.wait_for_element_visible("//span[contains(@class,'marketing-lottery-modal-confirm-title')]")
        self.assert_text('确认提交', "//span[contains(@class,'marketing-lottery-modal-confirm-title')]")
        self.assert_text('确 定', "//span[contains(text(),'确 定')]")
        self.assert_text('取 消', "//div[@class='marketing-lottery-modal-confirm-btns']//span[contains(text(),'取 消')]")

        self.click("//div[@class='marketing-lottery-modal-body']//button[2]")

        sleep(5)

        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage')


    #评论和观看任务创建
    @pytest.mark.skip
    @pytest.mark.p0
    def test_good_luck_comment_live_create(self):
        print('say good_luck_lottery create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(3)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        self.assert_text('单一任务', '//*[@id="taskIdentifyId"]/label[1]/span[2]')
        self.assert_text('组合任务', '//*[@id="taskIdentifyId"]/label[2]/span[2]')
        #点击组合任务
        self.click('//*[@id="taskIdentifyId"]/label[2]')
        sleep(2)
        self.assert_text('门槛任务1', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[1]')
        self.assert_text('门槛任务2', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[4]')

        self.assert_text('评论直播间', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[2]/div/span[2]')
        self.type('//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[3]/span/input', '老铁评论')

        self.assert_text('直播观看时长','//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[5]/div/span[2]/span')
        self.type('//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[6]/div/div/div[2]/input', '6')

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')
        self.assert_text('在售商品', '//*[@id="rc-tabs-0-tab-selfItem"]')
        self.assert_text('自建奖品', '//*[@id="rc-tabs-0-tab-lotteryItem"]')

        self.click('//*[@id="rc-tabs-0-tab-lotteryItem"]')

        # 自建奖品按商品名称选择
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化好运来奖品')
        # 点击查询按钮
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 商品选择框选择
        self.click('//tbody/tr[2]/td[1]/label[1]')

        # 自建奖品选择后保存确认
        self.assert_text('确 认', '//*[@class="marketing-lottery-modal-footer"]/button[2]/span')
        self.click('//*[@class="marketing-lottery-modal-footer"]/button[2]')
        time.sleep(2)

        # 抽奖人数
        self.type('//input[@id="lotteryNumber"]', '3')

        # 创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[2]')
        self.wait_for_element_visible('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[1]/span')
        self.assert_text('确认提交', '/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[1]/span')
        self.assert_text('确 定', "//span[contains(text(),'确 定')]")
        self.assert_text('取 消', "//div[@class='marketing-lottery-modal-confirm-btns']//span[contains(text(),'取 消')]")

        self.click("//div[@class='marketing-lottery-modal-body']//button[2]")

        sleep(5)

        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage')

    #评论和分享任务创建
    @pytest.mark.skip
    @pytest.mark.p0
    def test_good_luck_comment_share_create(self):
        print('say good_luck_lottery create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        sleep(3)
        self.assert_no_404_errors()
        self.assert_text('创建活动方式', '//*[@id="root"]/section/main/div[3]/div[1]/div[1]/h2')
        self.assert_text('基本信息', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[1]/h2')
        self.assert_text('设置任务', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/h2')
        self.assert_text('设置奖励', '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/h2')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div[4]/div[2]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div[4]/div[2]/button[1]/span')

        # 活动标题
        self.type('//*[@id="lotteryName"]', 'UI自动化测试')

        self.assert_text('单一任务', '//*[@id="taskIdentifyId"]/label[1]/span[2]')
        self.assert_text('组合任务', '//*[@id="taskIdentifyId"]/label[2]/span[2]')
        # 点击组合任务
        self.click('//*[@id="taskIdentifyId"]/label[2]')
        sleep(2)
        self.assert_text('门槛任务1','//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[1]')
        self.assert_text('门槛任务2','//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[4]')

        self.assert_text('评论直播间','//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[2]/div/span[2]')
        self.type('//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[3]/div[2]/div/div/div[2]/div/div/div[3]/span/input','老铁评论')

        #修改门槛任务2
        self.click("//div[@class='marketing-lottery-row marketing-lottery-form-item marketing-lottery-form-item-has-success']//div[5]//div[1]")
        self.click('/html/body/div[3]/div/div/div/div[2]/div[1]/div/div/div[6]/div')
        self.assert_text('分享直播间',"//span[contains(text(),'分享直播间')]")

        # 点击手动生效
        self.assert_text('手动生效', '//*[@id="effectType"]/label[2]/span[2]')
        self.click('//*[@id="effectType"]/label[2]')

        # 生效时间
        self.type('//input[@id="lotteryDuration"]', '8')

        # 添加奖励商品抽屉
        self.assert_text('添加商品',
                         '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button/span')
        self.click(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[3]/div[1]/div[2]/div[1]/div/div/div/button')

        self.wait_for_element_visible('//div[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//div[@id="rcDialogTitle0"]')
        self.assert_text('在售商品', '//*[@id="rc-tabs-0-tab-selfItem"]')
        self.assert_text('自建奖品', '//*[@id="rc-tabs-0-tab-lotteryItem"]')

        self.click('//*[@id="rc-tabs-0-tab-lotteryItem"]')

        # 自建奖品按商品名称选择
        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', 'UI自动化好运来奖品')
        # 点击查询按钮
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 商品选择框选择
        self.click('//tbody/tr[2]/td[1]/label[1]')

        # 自建奖品选择后保存确认
        self.assert_text('确 认', '//*[@class="marketing-lottery-modal-footer"]/button[2]/span')
        self.click('//*[@class="marketing-lottery-modal-footer"]/button[2]')
        time.sleep(2)

        # 抽奖人数
        self.type('//input[@id="lotteryNumber"]', '3')

        # 创建页面
        self.click('//*[@id="root"]/section/main/div[4]/div[2]/button[2]')
        sleep(2)
        self.assert_text('确 定', "//span[contains(text(),'确 定')]")
        self.assert_text('取 消', "//div[@class='marketing-lottery-modal-confirm-btns']//span[contains(text(),'取 消')]")

        self.click("//div[@class='marketing-lottery-modal-body']//button[2]")

        sleep(5)

        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage')


    # 好运来活动管理页面tab切换及查询
    @pytest.mark.skip
    @pytest.mark.p1
    def test_good_luck_lottery_manage_query(self):
        print('say good_luck_lottery manage query')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL)
        sleep(2)

        self.assert_no_404_errors()
        self.assert_text('好运来活动数据总览', "//div[@class='marketing-lottery-card-head-title']")
        self.assert_text('全部', "//div[@class='marketing-lottery-tabs-tab marketing-lottery-tabs-tab-active']")
        self.assert_text('待开始', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[2]")
        self.assert_text('进行中', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[3]")
        self.assert_text('已完成', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[4]")
        self.assert_text('已撤销', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[5]")
        self.assert_text('待发货', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[6]")

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        # 切换待开始tab
        self.click("//div[@class='marketing-lottery-tabs-nav-wrap']//div[2]")
        self.type("//input[@placeholder='请输入活动名称']", 'UI自动化测试')
        self.click("//button[@type='submit']")

        # 切换全部tab
        self.click('//*[@id="root"]/section/main/div/div[4]/div/div/div[1]/div/div[1]/div[1]/div/div[1]')
        self.type("//input[@placeholder='请输入活动名称']", 'UI自动化测试')
        self.click("//button[@type='submit']")
        self.assert_text('UI自动化测试','//*[@id="root"]/section/main/div/div[4]/div/div/div[3]/div[1]/div/div/div[1]/div[1]/div[1]/div[1]/div')


    # 好运来活动取消
    @pytest.mark.skip
    @pytest.mark.p1
    def test_good_luck_lottery_cancel(self):
        print('say good_luck_lottery cancel')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL)
        sleep(3)
        self.assert_no_404_errors()
        self.assert_text('好运来活动数据总览', "//div[@class='marketing-lottery-card-head-title']")
        self.assert_text('全部', "//div[@class='marketing-lottery-tabs-tab marketing-lottery-tabs-tab-active']")
        self.assert_text('待开始', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[2]")
        self.assert_text('进行中', "//div[@class='marketing-lottery-tabs-nav-wrap']//div[3]")

        self.assert_text('查 询', "//span[contains(text(),'查 询')]")
        self.assert_text('重 置', "//span[contains(text(),'重 置')]")

        tag = True

        while tag:
            self.click("//div[@class='marketing-lottery-tabs-nav-wrap']//div[2]")

            self.type("//input[@placeholder='请输入活动名称']", 'UI自动化测试')
            # 点击活动查询
            self.click("//button[@type='submit']")
            elements = self.find_elements('//*[@id="root"]/section/main/div/div[4]/div/div/div[3]/div[1]/div/div/div[1]/div[1]/div[1]/div[2]/div/button[2]/span')
            if len(elements)>0:
                self.assert_text('撤销活动','//*[@id="root"]/section/main/div/div[4]/div/div/div[3]/div[1]/div/div/div[1]/div[1]/div[1]/div[2]/div/button[2]/span')
                self.click('//*[@id="root"]/section/main/div/div[4]/div/div/div[3]/div[1]/div/div/div[1]/div[1]/div[1]/div[2]/div/button[2]/span')
                time.sleep(2)

                self.assert_text('确认撤销活动吗', "(//div[@class='marketing-lottery-popover-message-title'])[1]")
                self.click("(//button[@class='marketing-lottery-btn marketing-lottery-btn-primary marketing-lottery-btn-sm'])[1]")
            else:
                tag = False

