from time import sleep
import datetime
import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase
from ..distribution.test.test_speed import driver

SHOPPING_BUY_GIFT_CRETE_URL = 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/create?entry_src=toolsv2_common_tools'
SHOPPING_BUY_GIFT_MANAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/manage?entry_src=toolsv2_all_marketing_tools'
SHOPPING_BUY_GIFT_MANAGE_GIFT_URL = 'https://s.kwaixiaodian.com/zone/goods/v1/list?itemProfileQuery=noSaleGift&managerTab=ALL'

class TestMarketingBuyGiftP2(BaseTestCase):

    #@pytest.mark.skip
    @pytest.mark.p2
    # 买赠创建页面关闭添加商品弹窗
    def test_buy_gift_close_main_goods(self):
        print('say shopping_buy_gift page')
        # 最大化窗口
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        # 打开买赠创建页面
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        # 判断买赠页面元素展示正常
        self.assert_no_404_errors()
        self.assert_text('创建买赠活动', "(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.assert_text('活动名称', "//label[contains(text(),'活动名称')]")
        self.assert_text('选择主商品', "//label[contains(text(),'选择主商品')]")
        self.assert_text('选择赠品', "//label[contains(text(),'选择赠品')]")

        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试1')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅店铺页']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')

        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('全部商品', "(//div[@id='rc-tabs-0-tab-0'])[1]")
        self.assert_text('可选商品', "(//div[@id='rc-tabs-0-tab-1'])[1]")

        self.click("//span[@class='marketing-paas-modal-close-x']//*[name()='svg']")

    # 买赠创建页面取消添加商品
    @pytest.mark.p2
    def test_buy_gift_cancle_main_goods(self):
        print('say shopping_buy_gift page')
        # 最大化窗口
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        # 打开买赠创建页面
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        # 判断买赠页面元素展示正常
        self.assert_no_404_errors()
        self.assert_text('创建买赠活动', "(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.assert_text('活动名称', "//label[contains(text(),'活动名称')]")
        self.assert_text('选择主商品', "//label[contains(text(),'选择主商品')]")
        self.assert_text('选择赠品', "//label[contains(text(),'选择赠品')]")

        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试1')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅店铺页']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')

        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('全部商品', "(//div[@id='rc-tabs-0-tab-0'])[1]")
        self.assert_text('可选商品', "(//div[@id='rc-tabs-0-tab-1'])[1]")

        self.click("(//span[contains(text(),'取 消')])[2]")

    # 买赠创建页面取消新增非卖赠品
    @pytest.mark.p2
    def test_buy_gift_cancle_gift_goods(self):
        print('say shopping_buy_gift page')
        # 最大化窗口
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        # 打开买赠创建页面
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        # 判断买赠页面元素展示正常
        self.assert_no_404_errors()
        self.assert_text('创建买赠活动', "(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.assert_text('活动名称', "//label[contains(text(),'活动名称')]")
        self.assert_text('选择主商品', "//label[contains(text(),'选择主商品')]")
        self.assert_text('选择赠品', "//label[contains(text(),'选择赠品')]")

        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试1')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅店铺页']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        # self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        # self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('新建非卖赠品', "//span[contains(text(),'新建非卖赠品')]")

        self.click("//button[@class='marketing-paas-btn Nm5joasMziebb8vE2159']//span[contains(text(),'取 消')]")

    # 买赠创建页面关闭新增非卖赠品弹窗
    @pytest.mark.p2
    def test_buy_gift_close_gift_goods(self):
        print('say shopping_buy_gift page')
        # 最大化窗口
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        # 打开买赠创建页面
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        # 判断买赠页面元素展示正常
        self.assert_no_404_errors()
        self.assert_text('创建买赠活动', "(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.assert_text('活动名称', "//label[contains(text(),'活动名称')]")
        self.assert_text('选择主商品', "//label[contains(text(),'选择主商品')]")
        self.assert_text('选择赠品', "//label[contains(text(),'选择赠品')]")

        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试1')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅店铺页']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        # self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        # self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('新建非卖赠品', "//span[contains(text(),'新建非卖赠品')]")

        self.click("//*[name()='path' and contains(@d,'M0.29175 1')]")

    # 买赠管理页面进入管理非卖赠品页面
    @pytest.mark.p2
    def test_gift_manger_page(self):
        print('say buy_gift manage_gift from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOPPING_BUY_GIFT_MANAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[1]')
        self.assert_text('管理非卖赠品', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[2]/span')
        self.assert_text('立即创建',
                         '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[3]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        # 点击管理非卖赠品
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[2]/span')

        self.assert_text('全部','//*[@id="rc-tabs-0-tab-ALL"]/span/span[1]')
        self.assert_text('商品信息',"//th[contains(text(),'商品信息')]")
        self.assert_text('查看重复品',"//span[contains(text(),'查看重复品')]")
        self.assert_text('导出商品',"//span[contains(text(),'导出商品')]")
        self.assert_text('新增商品',"//button[@type='button']//span[contains(text(),'新增商品')]")

    # 买赠活动列表取消追加赠品套数
    @pytest.mark.p2
    def test_cancle_add_gift_num(self):
        print('say buy_gift_manage add gift')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")

        # 先创建活动
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        self.assert_no_404_errors()
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试2')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅直播间']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[@title='达人分销主品时，可继承赠品活动']")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']", "200")
        # 赠品数量
        self.type(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]",
            "2")

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[1]')

        sleep(5)

        # 进入管理页面追加赠品套数
        self.open(SHOPPING_BUY_GIFT_MANAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        try:
            self.click("//img[@class='closeIcon___TZ6G7']")
        except:
            pass
        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试2')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 点击追加赠品套数
        self.click('//tbody//button[1]')
        sleep(2)
        self.click("//span[contains(text(),'取 消')]")

        # 点击下线
        self.click('//tbody//button[2]')
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-modal-body']")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-root']//button[2]")

    # 买赠活动列表关闭追加赠品套数弹窗
    @pytest.mark.p2
    def test_close_add_gift_num(self):
        print('say buy_gift_manage add gift')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        # 先创建活动
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        self.assert_no_404_errors()
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试2')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅直播间']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[@title='达人分销主品时，可继承赠品活动']")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']", "200")
        # 赠品数量
        self.type(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]",
            "2")

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[1]')

        sleep(5)

        # 进入管理页面追加赠品套数
        self.open(SHOPPING_BUY_GIFT_MANAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        try:
            self.click("//img[@class='closeIcon___TZ6G7']")
        except:
            pass
        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试2')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 点击追加赠品套数
        self.click('//tbody//button[1]')
        sleep(2)
        self.click("//button[@aria-label='Close']//span[@aria-label='system-close-medium-line']//*[name()='svg']//*[name()='path' and contains(@d,'M3.32 18.7')]")

        # 点击下线
        self.click('//tbody//button[2]')
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-modal-body']")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-root']//button[2]")

    # 买赠数据效果列表查询活动
    @pytest.mark.p2
    def test_buy_git_data_effect(self):
        print('say buy_gift_manage add gift')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")

        # 进入管理页面追加赠品套数
        self.open(SHOPPING_BUY_GIFT_MANAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        try:
            self.click("//img[@class='closeIcon___TZ6G7']")
        except:
            pass
    #    点击数据效果tab
        self.click('//*[@id="rc-tabs-0-tab-2"]')

        self.assert_text('活动效果',"//div[@class='zOXZjcimdm_lO5233cEw']")
        self.assert_text('活动名称',"//span[contains(text(),'活动名称')]")
        self.assert_text('活动ID', "//span[contains(text(),'活动ID')]")

    #     查询活动名称
        self.type('//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div[2]/input','买赠UI自动化测试2')
        self.click("//span[contains(text(),'查 询')]")



        # self.execute_script("window.scrollBy(0,500);")

    # 买赠活动列表取消下线活动
    @pytest.mark.p2
    def test_buy_gift_cancle_Offline(self):
        print('say buy_gift_manage add gift')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")

        # 先创建活动
        self.open(SHOPPING_BUY_GIFT_CRETE_URL)
        sleep(2)
        self.assert_no_404_errors()
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试2')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅直播间']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[@title='达人分销主品时，可继承赠品活动']")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']", "200")
        # 赠品数量
        self.type(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]",
            "2")

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[1]')

        sleep(5)

        # 进入管理页面
        self.open(SHOPPING_BUY_GIFT_MANAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        try:
            self.click("//img[@class='closeIcon___TZ6G7']")
        except:
            pass


        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试2')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 点击下线
        self.click("//tbody//button[2]")
        sleep(2)

        # 点击关闭下线弹窗
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-confirm-btns']//span[contains(text(),'取 消')]")

        # 点击下线
        self.click('//tbody//button[2]')
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-modal-body']")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-root']//button[2]")
