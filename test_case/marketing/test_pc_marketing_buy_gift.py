from time import sleep
import datetime
import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

BUY_GIFT_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/create?entry_src=toolsv2_all_marketing_tools'
BUY_GIFT_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/manage?entry_src=toolsv2_all_marketing_tools'

@pytest.mark.skip
class TestMarketingBuyGift(BaseTestCase):
    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_buy_gift_create_from_manage(self):
        print('say buy_gift create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(BUY_GIFT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[1]')
        self.assert_text('管理非卖赠品', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[2]/span')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[3]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        # 点击立即创建
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[3]/button')
        sleep(2)

        self.wait_for_element_visible("(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创建买赠活动', "(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.assert_text('活动名称', "//label[contains(text(),'活动名称')]")
        self.assert_text('选择主商品', "//label[contains(text(),'选择主商品')]")
        self.assert_text('选择赠品', "//label[contains(text(),'选择赠品')]")
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅店铺页']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('全部商品', "(//div[@id='rc-tabs-0-tab-0'])[1]")
        self.assert_text('可选商品', "(//div[@id='rc-tabs-0-tab-1'])[1]")

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('新建非卖赠品', "//span[contains(text(),'新建非卖赠品')]")

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']","1")
        # 赠品数量
        self.type("/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]", "1")

        # 取消创建页面
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[2]')

        sleep(5)
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/buy-gift/manage')

    # 点击添加商品-确认添加
    @pytest.mark.p0
    def test_buy_gift_create_page_add_item(self):
        print('say buy_gift add item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(BUY_GIFT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建买赠活动', "(//div[@class='fD6y_RgI40hPIthjeeoQ'])[1]")
        self.assert_text('创 建', "//span[contains(text(),'创 建')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.assert_text('活动名称', "//label[contains(text(),'活动名称')]")
        self.assert_text('选择主商品', "//label[contains(text(),'选择主商品')]")
        self.assert_text('选择赠品', "//label[contains(text(),'选择赠品')]")
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试1')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅店铺页']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('全部商品', "(//div[@id='rc-tabs-0-tab-0'])[1]")
        self.assert_text('可选商品', "(//div[@id='rc-tabs-0-tab-1'])[1]")

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")
        self.assert_text('新建非卖赠品',"//span[contains(text(),'新建非卖赠品')]")

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']","100")
        # 赠品数量
        self.type("/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]", "1")

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[1]')

        sleep(5)
        url = self.get_current_url()
        self.assert_equal(url, 'https://s.kwaixiaodian.com/zone/marketing/buy-gift/manage?status=0')

        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试1')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 点击下线
        self.click('//tbody//button[2]')
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-modal-body']")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-root']//button[2]")


    # 买赠管理页面-追加赠品套数
    @pytest.mark.p1
    def test_buy_gift_manage_add_gift(self):
        print('say buy_gift_manage add gift')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")

        # 先创建活动
        self.open(BUY_GIFT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试2')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='仅直播间']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[@title='达人分销主品时，可继承赠品活动']")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']", "200")
        # 赠品数量
        self.type(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]",
            "2")

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[1]')

        sleep(5)

        #进入管理页面追加赠品套数
        self.open(BUY_GIFT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试2')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 点击追加赠品套数
        self.click('//tbody//button[1]')
        sleep(2)
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-drawer-body']")
        self.assert_text('BG-UI自动化测试赠品',"/html[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/div[2]/div[1]/span[1]")
        self.type("//input[@id='addNum']", "10")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-drawer kwaishop-marketing-buy-gift-pc-drawer-right kwaishop-marketing-buy-gift-pc-drawer-open']//button[2]")

        # 点击下线
        self.click('//tbody//button[2]')
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-modal-body']")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-root']//button[2]")

    # 买赠管理页面-查询+重置
    @pytest.mark.p1
    def test_buy_gift_manage_query(self):
        print('say buy_gift_manage query')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(BUY_GIFT_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[1]')
        self.assert_text('管理非卖赠品', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[2]/span')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[3]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        # 切换"全部"tab
        self.click("//label[@class='kwaishop-marketing-buy-gift-pc-radio-button-wrapper kwaishop-marketing-buy-gift-pc-radio-button-wrapper-checked kwaishop-marketing-buy-gift-pc-radio-button kwaishop-marketing-buy-gift-pc-radio-button-text']")

        # 重置
        self.type('//*[@id="activityName"]', '买赠UI自动化测试1')
        self.click("//span[@aria-label='system-error-circle-fill']")

        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试1')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 检查查询结果
        self.assert_text('买赠UI自动化测试1', "//div[normalize-space()='买赠UI自动化测试1']")

    # 买赠管理页面-下线活动
    @pytest.mark.p1
    def test_buy_gift_manage_finish(self):
        print('say buy_gift_manage finish')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")

        # 先创建活动
        self.open(BUY_GIFT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '买赠UI自动化测试3')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        # 选择生效渠道
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div')
        self.click("//div[@title='全部渠道（直播间、店铺页、短视频等）']")

        # 选择生效范围
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div')
        self.click("//div[contains(@title,'达人分销主品时，不可继承赠品活动')]")

        # 选择主商品
        self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 切换"可选商品"tab
        self.click("//div[@class='marketing-paas-tabs-tab']")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "买赠UI-B自动化G测试主商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择赠品
        self.click('//*[@id="root"]/div/div/form/div[6]/div[2]/div[1]/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle1"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle1"]')

        # 切换"可选商品"tab
        self.click("(//div[@class='marketing-paas-tabs-tab'])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "BG-UI自动化测试赠品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 赠品套数
        self.type("//input[@id='goodsStock']", "300")
        # 赠品数量
        self.type(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/form[1]/div[7]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[4]/div[1]/form[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/input[1]",
            "3")

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div/div[2]/button[1]')

        sleep(5)

        # 进入管理页面下线活动
        self.open(BUY_GIFT_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[1]')
        self.assert_text('管理非卖赠品', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[2]/span')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div/div/div[1]/div/div/div[2]/a[3]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        # 切换"全部"tab
        self.click("//label[@class='kwaishop-marketing-buy-gift-pc-radio-button-wrapper kwaishop-marketing-buy-gift-pc-radio-button-wrapper-checked kwaishop-marketing-buy-gift-pc-radio-button kwaishop-marketing-buy-gift-pc-radio-button-text']")

        # 点击查询
        self.type('//*[@id="activityName"]', '买赠UI自动化测试3')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")

        # 点击下线
        self.click('//tbody//button[2]')
        self.wait_for_element_visible("//div[@class='kwaishop-marketing-buy-gift-pc-modal-body']")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")
        self.assert_text('取 消', "//span[contains(text(),'取 消')]")
        self.click("//div[@class='kwaishop-marketing-buy-gift-pc-modal-root']//button[2]")

        # 切换"已结束"tab
        self.click("(//label[@class='kwaishop-marketing-buy-gift-pc-radio-button-wrapper kwaishop-marketing-buy-gift-pc-radio-button kwaishop-marketing-buy-gift-pc-radio-button-text'])[3]")
        self.type('//*[@id="activityName"]', '买赠UI自动化测试3')
        self.click("//span[@class='anticon anticon-system-search-line']//*[name()='svg']")
        self.assert_text('买赠UI自动化测试3', "//div[normalize-space()='买赠UI自动化测试3']")

