from time import sleep
import datetime
import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

FREE_ORDER_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/manage?entry_src=&entry_src=toolsv2_common_tools&from=&scene=&toolCode=62&toolKey=free-order'
FREE_ORDER_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/create?entry_src=&entry_src=toolsv2_common_tools&from=&scene=&toolCode=62&toolKey=free-order'
FREE_ORDER_DETAIL_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/review?entry_src=&from=&id=350465695328&scene=&toolCode=62&toolKey=free-order'
FREE_ORDER_COPY_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/engine/copy?entry_src=&from=&id=350465695328&scene=&toolCode=62&toolKey=free-order'


class TestMarketingFreeOrder(BaseTestCase):

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_free_order_manage(self):
        print('say free_order_manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('主播抽免单', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div/span/span')
        self.assert_text('立即创建', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-manage"]')

        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button/span')

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_free_order_create(self):
        print('say free_order_create')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动标题', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[2]/div[1]/div/span/label')
        self.assert_text('活动任务', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[5]/div[1]/div/span/label')

        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

    #@pytest.mark.skip
    def test_free_order_detail(self):
        print('say free_order_detail')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_DETAIL_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动标题', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[2]/div[1]/div/span/label')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button/span')

        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button/span')

    #@pytest.mark.skip
    def test_free_order_copy(self):
        print('say free_order_detail')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_COPY_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('活动标题', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[2]/div[1]/div/span/label')
        self.assert_text('活动任务', '//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[5]/div[1]/div/span/label')

        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

    #创建单一任务
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_free_order_create_sigle_task(self):
        print('say free_order create a sigle task')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_CREATE_PAGE_URL)
        sleep(3)
        self.assert_no_404_errors()
        self.assert_text('创建主播抽免单', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

        self.type("//input[@placeholder='请输入']", '抽免单单一任务UI自动化测试')

        self.assert_text('单一任务', "//span[contains(text(),'单一任务')]")
        # 选择"全场下单任务"
        self.click("(//div[@class='kwaishop-marketing-render-engine-pc-select nJrrLwRLzNwGV89zVzHK kwaishop-marketing-render-engine-pc-select-single kwaishop-marketing-render-engine-pc-select-show-arrow'])[1]")
        self.click("//span[contains(text(),'全场下单任务')]")
        self.type("(//input[@placeholder='1~200'])[1]","1")

        sleep(2)
        # 抽奖人数
        self.type('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[6]/div[2]/div/div/div/div/div[1]/div[2]/input', '1')
        # 活动时长
        self.type("(//input[@placeholder='3~180'])[1]", "10")

        # 点击固定时间
        self.assert_text('固定时间', "//span[contains(text(),'固定时间')]")
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[8]/div[2]/div/div/div/label[2]')

        # 选择开始时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M')
        self.type("//input[@placeholder='请选择日期']", startTime)
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-sm']")
        sleep(2)

        # 选择不膨胀
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[11]/div[2]/div/div/div/label[2]')

        # 点击活动创建页面创建按钮
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]')

    # 创建组合任务
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_free_order_create_combined_task(self):
        print('say free_order create combined task')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_CREATE_PAGE_URL)
        sleep(3)
        self.assert_no_404_errors()
        self.assert_text('创建主播抽免单', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

        self.type("//input[@placeholder='请输入']", '抽免单组合任务UI自动化测试')

        # 选择组合任务
        self.assert_text('组合任务', "//span[contains(text(),'组合任务')]")
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[4]/div[2]/div/div/div/div/div[2]/label')
        # 任务1 直播观看时长
        self.type("//input[@placeholder='3~59']", "3")

        # 任务2 添加商品
        self.assert_text('添加商品', "//span[contains(text(),'添加商品')]")
        self.click("//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',"//button[@class='kwaishop-marketing-render-engine-pc-btn dEyFJh6OwirmsDNJs4OS']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']", "抽单免UI-F自动化O测试商品1")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        sleep(2)

        # 抽奖人数
        self.type("//input[@placeholder='1~200']",'1')
        # 活动时长
        self.type("//input[@placeholder='3~180']", "10")

        # 点击固定时间
        self.assert_text('固定时间', "//span[contains(text(),'固定时间')]")
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[9]/div[2]/div/div/div/label[2]')

        # 选择开始时间
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M')
        self.type("//input[contains(@placeholder,'请选择日期')]", startTime)
        self.click("//div[contains(@class,'kwaishop-marketing-render-engine-pc-picker-dropdown kwaishop-marketing-render-engine-pc-picker-dropdown-placement-topLeft')]//div[contains(@class,'kwaishop-marketing-render-engine-pc-picker-footer')]//button[contains(@type,'button')]")
        sleep(2)

        # 选择不膨胀
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[12]/div[2]/div/div/div/label[2]')

        # 点击活动创建页面创建按钮
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]')

    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_free_order_create_from_manage(self):
        print('say free_order create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('主播抽免单', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div/span/span')
        self.assert_text('立即创建', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-manage"]')

        # 点击立即创建
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button/span')

        sleep(5)
        self.wait_for_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创建主播抽免单', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

        self.type("//input[@placeholder='请输入']", '抽免单UI自动化测试')

        self.assert_text('单一任务', "//span[contains(text(),'单一任务')]")

        # 添加商品
        self.assert_text('添加商品', "//span[contains(text(),'添加商品')]")
        self.click(
            "//button[@class='kwaishop-marketing-render-engine-pc-btn kwaishop-marketing-render-engine-pc-btn-primary kwaishop-marketing-render-engine-pc-btn-background-ghost']")

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "//button[@class='kwaishop-marketing-render-engine-pc-btn dEyFJh6OwirmsDNJs4OS']//span[contains(text(),'取 消')]")
        self.assert_text('确 认', "//span[contains(text(),'确 认')]")

        self.type("//input[@placeholder='请输入商品名称']", "抽单免UI-F自动化O测试商品1")
        self.click("//button[@type='submit']")

        # 选择商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click("//span[contains(text(),'确 认')]")

        sleep(2)
        # 抽奖人数
        self.type(
            "//input[@placeholder='1~200']", '1')
        # 活动时长
        self.type("//input[@placeholder='3~180']", "10")

        # 选择不膨胀
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/form/div[11]/div[2]/div/div/div/label[2]')

        # 点击活动创建页面取消按钮
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')
        sleep(5)
        self.assert_url(
            'https://s.kwaixiaodian.com/zone/marketing/engine/manage?entry_src=&entry_src=toolsv2_common_tools&from=&scene=&toolCode=62&toolKey=free-order')

    # 复制活动
    @pytest.mark.p1
    def test_free_order_copy_from_manage(self):
        print('say free_order copy from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FREE_ORDER_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('主播抽免单', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div/span/span')
        self.assert_text('立即创建', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/span/div/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-manage"]')
        self.assert_text('复制活动',"(//span[contains(text(),'复制活动')])[1]")

        # 点击复制活动
        self.click("(//button[@type='button'])[6]")

        sleep(5)
        self.wait_for_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('复制主播抽免单', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]/span')

        self.type("//input[@placeholder='请输入']", '复制抽免单UI自动化测试')

        # 点击活动创建页面取消按钮
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/button[2]')
        sleep(5)
        self.assert_url('https://s.kwaixiaodian.com/zone/marketing/engine/manage?entry_src=&entry_src=toolsv2_common_tools&from=&scene=&toolCode=62&toolKey=free-order')