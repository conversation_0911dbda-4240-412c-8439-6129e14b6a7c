import datetime
import time

import requests
from ddt import ddt
import pytest as pytest
from seleniumbase.common.exceptions import NoSuchElementException
from selenium.webdriver.common.by import By
from test_case.helper_assistant.base import BaseTestCase

GOOD_LUCK_LOTTERY_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/lottery/create?entry_src=toolsv2_all_marketing_tools'
GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/lottery/manage?entry_src=toolsv2_all_marketing_tools'

kimbot_key = "b59bb8e3-617d-4f89-8dac-186420cfa31d"

HELPER_ASSISTANT = 'https://zs-sim01.test.gifshow.com/page/helper'

# 磐石故障数据
# 福利购商品
fuligo_fans_item = '24795710403891'

@ddt
class TestAutoDrillGoodLuckLotteryPage(BaseTestCase):

    # 点击添加商品弹窗-关闭弹窗
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_creat_good_luck_lottery(self):
        if datetime.datetime.now().hour != 11:
            print('自动故障演练数据准备，每天11点执行')
            return
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "auto_drill_good_luck_lottery")
        # 开播校验，未开播直接skip
        self.check_isLive()
        # 确认是否有进行中的好运来活动
        self.open(GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL)
        time.sleep(2)
        self.refresh()
        time.sleep(2)
        task_status_list = self.find_elements('marketing-lottery-tag.marketing-lottery-tag-has-color', By.CLASS_NAME)
        if task_status_list[0].text == '进行中':
            self.skip("已有进行中的好运来活动")
        # 没有进行中活动，进行活动创建
        self.open(GOOD_LUCK_LOTTERY_CREATE_PAGE_URL)
        time.sleep(2)
        self.refresh()
        time.sleep(2)
        self.assert_no_404_errors()
        # self.click("//span[text()='新建活动']")
        time.sleep(2)
        self.input(
            '//*[@id="root"]/section/main/div[3]/div[1]/div[2]/div/form/div[2]/div[2]/div[2]/div/div/div[2]/div/div/div[2]/span/input',
            'auto_drill')
        self.input('lotteryDuration', '60', By.ID)
        self.click("//span[text()='添加商品']")
        self.input('marketing-lottery-input-number-input', '24842048834891', By.CLASS_NAME)
        self.click("//span[text()='查 询']")
        time.sleep(2)
        check_box_list = self.find_elements('marketing-lottery-checkbox-input', By.CLASS_NAME)
        check_box_list[len(check_box_list) - 1].click()
        self.click("//span[text()='确 认']")
        time.sleep(2)
        self.input('lotteryNumber', '1', By.ID)
        self.click("//span[text()='创 建']")
        self.click("//span[text()='确 定']")
        self.open(GOOD_LUCK_LOTTERY_MANAGE_PAGE_URL)
        time.sleep(5)
        self.refresh_page()
        time.sleep(2)
        task_status_list = self.find_elements('marketing-lottery-tag.marketing-lottery-tag-has-color', By.CLASS_NAME)
        # 活动创建失败提示
        if task_status_list[0].text != '进行中':
            message = '<@=username(wb_zhaojingjing03)=>账号1275816891创建好运来活动失败，请及时确认'
            self.kimRobat_send_message(message, kimbot_key)

    # 未在车时上车，如果上车失败发送kim消息通知
    def check_item_is_onSale_and_choose_item(self, IsOnSale, itemId):
        if IsOnSale == False:
            self.choose_item(itemId)
            IsOnSale = self.check_item_is_onSale(itemId)
            if IsOnSale == False:
                message = '<@=username(wb_zhaojingjing03)=>账号1275816891上车福利购粉丝团商品失败，请及时确认'
                self.kimRobat_send_message(message, kimbot_key)

    # 校验是否开播
    def check_isLive(self):
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        try:
            notLive = self.find_element('title2--Lesik', By.CLASS_NAME).text
            if notLive == "未开播" or notLive == '非电商开播':  # 前置条件不满足
                message = '<@=username(wb_zhaojingjing03)=>账号1275816891未开播，请及时开播，并更新压测直播ID'
                self.kimRobat_send_message(message, kimbot_key)
                self.skip("未开播，跳过本次测试")
        except NoSuchElementException:
            elementText = self.find_element('live-time--AHjj1', By.CLASS_NAME).text.split(' ')[0]
            if elementText != '已播':
                message = '<@=username(wb_zhaojingjing03)=>账号1275816891未开播，请及时开播，并更新压测直播ID'
                self.kimRobat_send_message(message, kimbot_key)
                self.skip("未开播，跳过本次测试")

    # 选品上车
    def choose_item(self, itemId):
        # 点击添加上车商品
        self.click('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[2]/div/div[5]/span/span')
        time.sleep(1)
        self.input('//input[@placeholder="输入商品ID/商品名称搜索"]',itemId, By.XPATH)
        time.sleep(1)
        self.click('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        self.click('//span[text()="更新小黄车"]')
        time.sleep(1)

    # 判断商品是否在车
    def check_item_is_onSale(self, itemID):
        itemList = self.get_itemList(0)
        if itemID not in itemList:
            return False
        else:
            return False

    def kimRobat_send_message(self, text, key):
        api = 'https://kim-robot.kwaitalk.com/api/robot/send?key='
        message = {
            'msgtype': 'markdown',
            'markdown': {
                'content': text,
            }
        }
        requests.post(api + key, json=message)

    # 获取跟播助手商品列表
    def get_itemList(self, index):
        # index枚举 小黄车列表=0，待上车列表=1，历史上车列表=2
        # 操作前注意列表有商品，不然会定位失败！！！！
        try:
            # 获取父元素节点-小黄车列表父元素节点
            parent_element = self.driver.find_elements(By.XPATH, '//div[@data-test-id="virtuoso-item-list"]')[index]
        except IndexError:
            return None
        else:
            # 使用 XPath 获取所有每个商品卡的元素
            item_card_child_elements = self.execute_script("return arguments[0].children", parent_element)
            # 打印子元素的数量
            itemList = []
            print(f"Number of child elements: {len(item_card_child_elements)}")
            if len(item_card_child_elements) == 0:
                return itemList
            # 遍历子元素,获取商品id
            for child in item_card_child_elements:
                # 继续获取子元素
                itemChild = self.execute_script("return arguments[0].children", child)
                itemId = itemChild[0].get_attribute('id')
                itemList.append(str(itemId))
            return itemList
