import time
import requests
from ddt import ddt
import pytest as pytest
from seleniumbase.common.exceptions import NoSuchElementException
from selenium.webdriver.common.by import By
from test_case.helper_assistant.base import BaseTestCase

fuligo_fans_item = '24795710403891'
fuligo_group_task_item = '24797620158891'
fuligo_watch_task_item = '24799628148891'
fuligo_fans_group_task_item = '24799711098891'

kimbot_key = "b59bb8e3-617d-4f89-8dac-186420cfa31d"

@ddt
class TestAutoDrillFuLiGo(BaseTestCase):

    # 自动故障平台-福利购粉丝任务商品上车
    def test_auto_drill_fuligo_item_choose(self):
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "auto_drill_good_luck_lottery")
        self.check_isLive()
        self.check_is_onSale_and_choose(fuligo_fans_item)

    # 自动故障平台-福利购组合任务商品上车
    def test_group_task_fuligo_item_choose(self):
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "auto_drill_good_luck_lottery")
        self.check_isLive()
        self.check_is_onSale_and_choose(fuligo_group_task_item)

    # 自动故障平台-福利购观播任务商品上车
    def test_watch_task_fuligo_item_choose(self):
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "auto_drill_good_luck_lottery")
        self.check_isLive()
        self.check_is_onSale_and_choose(fuligo_watch_task_item)

    # 自动故障平台-福利购粉丝团任务商品上车
    def test_fans_group_task_fuligo_item_choose(self):
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "auto_drill_good_luck_lottery")
        self.check_isLive()
        self.check_is_onSale_and_choose(fuligo_fans_group_task_item)


    # 未在车时上车，如果上车失败发送kim消息通知
    def check_is_onSale_and_choose(self, itemId):
        IsOnSale = self.check_item_is_onSale(itemId)
        if IsOnSale == False:
            self.choose_item(itemId)
            IsOnSale = self.check_item_is_onSale(itemId)
            if IsOnSale == False:
                message = '<@=username(wb_zhaojingjing03)=>账号1275816891上车福利购粉丝团商品失败，请及时确认'
                self.kimRobat_send_message(message, kimbot_key)

    # 校验是否开播
    def check_isLive(self):
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        try:
            notLive = self.find_element('title2--Lesik', By.CLASS_NAME).text
            if notLive == "未开播" or notLive == '非电商开播':  # 前置条件不满足
                message = '<@=username(wb_zhaojingjing03)=>账号1275816891未开播，请及时开播'
                self.kimRobat_send_message(message, kimbot_key)
                self.skip("未开播，跳过本次测试")
        except NoSuchElementException:
            elementText = self.find_element('live-time--AHjj1', By.CLASS_NAME).text.split(' ')[0]
            if elementText != '已播':
                message = '<@=username(wb_zhaojingjing03)=>账号1275816891未开播，请及时开播'
                self.kimRobat_send_message(message, kimbot_key)
                self.skip("未开播，跳过本次测试")

    # 选品上车
    def choose_item(self, itemId):
        # 点击添加上车商品
        self.click('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[2]/div/div[5]/span/span')
        time.sleep(1)
        self.input('//input[@placeholder="输入商品ID/商品名称搜索"]',itemId, By.XPATH)
        time.sleep(1)
        self.click('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        self.click('//span[text()="更新小黄车"]')
        time.sleep(1)

    # 判断商品是否在车
    def check_item_is_onSale(self, itemID):
        onSaleItemList = self.get_itemList(0)
        itemList = []
        for item in range(len(onSaleItemList)):
            itemList.append(onSaleItemList[item].split('-')[1])
        if itemID not in itemList:
            return False
        else:
            return True

    def kimRobat_send_message(self, text, key):
        api = 'https://kim-robot.kwaitalk.com/api/robot/send?key='
        message = {
            'msgtype': 'markdown',
            'markdown': {
                'content': text,
            }
        }
        requests.post(api + key, json=message)

    # 获取跟播助手商品列表
    def get_itemList(self, index):
        # index枚举 小黄车列表=0，待上车列表=1，历史上车列表=2
        # 操作前注意列表有商品，不然会定位失败！！！！
        try:
            # 获取父元素节点-小黄车列表父元素节点
            parent_element = self.driver.find_elements(By.XPATH, '//div[@data-test-id="virtuoso-item-list"]')[index]
        except IndexError:
            return None
        else:
            # 使用 XPath 获取所有每个商品卡的元素
            item_card_child_elements = self.execute_script("return arguments[0].children", parent_element)
            # 打印子元素的数量
            itemList = []
            print(f"Number of child elements: {len(item_card_child_elements)}")
            if len(item_card_child_elements) == 0:
                return itemList
            # 遍历子元素,获取商品id
            for child in item_card_child_elements:
                # 继续获取子元素
                itemChild = self.execute_script("return arguments[0].children", child)
                itemId = itemChild[0].get_attribute('id')
                itemList.append(str(itemId))
            return itemList
