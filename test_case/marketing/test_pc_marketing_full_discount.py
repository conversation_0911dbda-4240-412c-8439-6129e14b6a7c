from time import sleep
import datetime
import pytest as pytest
from ddt import ddt
import os
from .base import BaseTestCase

FULL_DISCOUNT_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/full-discount/create?entry_src=toolsv2_all_marketing_tools'
FULL_DISCOUNT_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/full-discount/manage?entry_src=toolsv2_all_marketing_tools'

class TestMarketingFullDiscount(BaseTestCase):
    # 从管理页面进入创建页面
    @pytest.mark.p0
    def test_full_discount_create_from_manage(self):
        print('say full_discount create from manage')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('满减满折活动', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[1]/div[1]')
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')
        # 点击立即创建
        self.click('//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button')

        self.assert_text('创建满减满折', '//*[@id="root"]/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[2]/span')
        self.assert_text('活动名称', '//*[@id="root"]/div/div/form/div[1]/div[1]/div/span/label')
        self.assert_text('添加商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '满减满折UI自动化测试')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)
        # 添加商品
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "FD-UI自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择优惠设置--满元优惠 满减
        #self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[1]/div[2]/label')
        # self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[2]/div[2]/label')
        self.type('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[3]/div[1]/div/div/div[2]/div[2]/input','20')
        self.type('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[3]/div[2]/div/div/div[2]/div[2]/input','2')
        # 取消创建页面
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[2]')

        sleep(5)
        url = self.get_current_url()
        self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/full-discount/manage?entry_src=toolsv2_all_marketing_tools')

    # 点击添加商品-确认添加
    @pytest.mark.p0
    def test_full_discount_create_page_add_item(self):
        print('say full_discount add item')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('创建满减满折', '//*[@id="root"]/div/div[2]/div/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[2]/span')
        self.assert_text('活动名称', '//*[@id="root"]/div/div/form/div[1]/div[1]/div/span/label')
        self.assert_text('添加商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button/span')
        # 活动名称
        self.type('//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]/div/span/input', '满减满折UI自动化测试')
        # 选择活动时间
        # self.click("//span[@class='kwaishop-marketing-render-engine-pc-picker-suffix']")
        self.click("//div[@class='marketing-paas-picker marketing-paas-picker-range']")
        startTime = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='开始时间']", startTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)

        endTime = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')
        self.type("//input[@placeholder='结束时间']", endTime)
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary marketing-paas-btn-sm']")
        sleep(2)
        # 添加商品
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/button')

        self.wait_for_element_visible('//*[@id="rcDialogTitle0"]')
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')

        # 商品查询
        self.assert_text('取 消',
                         "(//span[contains(text(),'取 消')])[2]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")
        self.assert_text('重 置', "(//span[contains(text(),'重 置')])[1]")
        self.assert_text('查 询', "(//span[contains(text(),'查 询')])[1]")

        self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input', "FD-UI自动化测试商品")
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')

        # 点击添加商品
        self.click("//tbody/tr[2]/td[1]/label[1]")
        self.click(
            "//button[@class='marketing-paas-btn marketing-paas-btn-primary Nm5joasMziebb8vE2159']")

        # 选择优惠设置--满件优惠
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[1]/div[2]/label')
        # self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[2]/div[2]/label')
        self.type('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[3]/div[1]/div/div/div[1]/div[2]/input','1')
        self.type('//*[@id="root"]/div/div/form/div[4]/div[2]/div[1]/div/div/div[3]/div[2]/div/div/div[1]/div[2]/input','9')

        # 点击创建
        self.click('//*[@id="root"]/div/div[2]/div/div[2]/div[2]/button[1]')

        sleep(5)
        # url = self.get_current_url()
        # self.assert_equal(url,'https://s.kwaixiaodian.com/zone/marketing/full-discount/manage')

        # 切换"待开始"tab
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[1]/div/div/label[2]')

        # self.type('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input','UI自动化测试')

        # 点击查询
        # self.click('//*[@id="rc-tabs-0-panel-manage"]/div/div[1]/div[2]/div[2]/button[1]')

        # 检查查询结果
        # self.assert_text('UI自动化测试',"//div[contains(text(),'UI自动化测试')]")

    # 满减满折管理页面-查询+重置
    @pytest.mark.p1
    def test_full_discount_manage_query(self):
        print('say full_discount_manage query')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('满减满折活动', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[1]/div[1]')
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        self.assert_text('查 询', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[1]/span')
        self.assert_text('重 置', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[2]/span')

        # 切换"全部"tab
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[1]/div/label[4]')

        self.type('//*[@id="campaignName"]', '满减满折UI自动化测试')
        # 重置
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[2]')
        self.type('//*[@id="campaignName"]', '满减满折UI自动化测试')

        # 点击查询
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[1]')

        # 检查查询结果
        self.assert_text('满减满折UI自动化测试', "//div[contains(text(),'满减满折UI自动化测试')]")

    # 满减满折管理页面-查看活动
    @pytest.mark.p2
    def test_full_discount_manage_check(self):
        print('say full_discount_manage check')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_MANAGE_PAGE_URL)
        sleep(2)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('满减满折活动', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[1]/div[1]')
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        self.assert_text('查 询',
                         '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[1]/span')
        self.assert_text('重 置',
                         '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[2]/span')

        # 切换"全部"tab
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[1]/div/label[4]')

        # 点击查询
        self.type('//*[@id="campaignName"]', '满减满折UI自动化测试')
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[1]')

        # 点击查看活动
        self.click('//tbody//button[1]')
        sleep(2)
        self.wait_for_element_visible("//div[@id='__qiankun_microapp_wrapper_for_marketing_paas__']")
        self.assert_text('满减满折UI自动化测试', "//input[@value='满减满折UI自动化测试']")


    # 满减满折管理页面-结束活动
    @pytest.mark.p1
    def test_full_discount_manage_finish(self):
        print('say full_discount_manage finish')
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FULL_DISCOUNT_MANAGE_PAGE_URL)
        sleep(3)
        self.refresh()
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('满减满折活动', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[1]/div[1]')
        self.assert_text('活动设置教程', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[1]')
        self.assert_text('立即创建', '//*[@id="root"]/div/div[2]/div/div/div[1]/div/div[2]/a[2]/button/span')
        self.assert_text('活动管理', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('数据效果', '//*[@id="rc-tabs-0-tab-2"]')

        self.assert_text('查 询',
                         '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[1]/span')
        self.assert_text('重 置',
                         '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[2]/span')

        self.refresh()

        tag = True

        while tag:
            # 切换"全部"tab
            self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[1]/div/label[4]')

            # 点击查询
            self.type('//*[@id="campaignName"]', '满减满折UI自动化测试')
            self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[2]/form/div[2]/div/div/div/button[1]')

            elements = self.find_elements('//tbody//button[2]')
            if len(elements) > 0:
                # 点击结束活动
                self.click('//tbody//button[2]')

                tag = False
            else:
                tag = False

        # 切换"已结束"tab
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[1]/div/label[3]')
        self.type('//*[@id="campaignName"]', '满减满折UI自动化测试')
        self.click("//button[@type='submit']")
        self.assert_text('满减满折UI自动化测试', "//div[contains(text(),'满减满折UI自动化测试')]")

