#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : chuyifeng
 Time :
 DESC :

 """
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p1
    def test_stadProduct_add_input(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 进入标品管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'标品管理')]")
        sleep(2)
        self.click("//span[contains(text(),'新增标品')]")
        sleep(3)
        self.switch_to_window(1)

        text = '测试测试叶子22'
        self.type("//input[@autocomplete='off']", text)
        sleep(2)
        self.click("//div[contains(text(),'" + text + "')]")
        sleep(3)

        self.click("//span[contains(text(),'下一步')]")
        sleep(2)

        text = '品牌logo合并测试1'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[1].click()
        sleep(1)
        self.click("//span[contains(text(),'" + text + "')]")

        text = '小板0.5*15mg*2'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[2].click()
        sleep(1)
        self.click("//div[contains(text(),'" + text + "')]")

        text = '测试'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[3].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'" + text + "')]")[-1].click()

        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[4].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'" + text + "')]")[-1].click()

        sleep(3)

        for i in range(6):
            text = '这是一个测试文案'
            self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入不超过200个字符']")[i].send_keys(text)

        text = '详见产品说明书'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[5].click()
        sleep(1)
        self.click("//div[contains(text(),'" + text + "')]")

        text = 'otc'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[6].click()
        sleep(1)
        self.click("//div[contains(text(),'" + text + "')]")

        self.assert_element_not_present('//span[contains(text(),"只允许输入中文、数字、英文、特殊字符集")]')
        self.assert_element_not_present('//span[contains(text(),"请完善信息")]')
        self.assert_element('//span[contains(text(),"提 交")]')
        self.assert_element('//span[contains(text(),"取 消")]')

    @pytest.mark.p1
    def test_stadProduct_add_upload(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 进入标品管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'标品管理')]")
        sleep(2)
        self.click("//span[contains(text(),'新增标品')]")
        sleep(3)
        self.switch_to_window(1)

        text = '测试测试叶子22'
        self.type("//input[@autocomplete='off']", text)
        sleep(2)
        self.click("//div[contains(text(),'" + text + "')]")
        sleep(3)

        self.click("//span[contains(text(),'下一步')]")
        sleep(2)

        text = '品牌logo合并测试1'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[1].click()
        sleep(1)
        self.click("//span[contains(text(),'" + text + "')]")

        text = '小板0.5*15mg*2'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[2].click()
        sleep(1)
        self.click("//div[contains(text(),'" + text + "')]")

        text = '测试'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[3].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'" + text + "')]")[-1].click()

        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[4].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'" + text + "')]")[-1].click()
        sleep(3)

        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"标品主图")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        # 增加了裁剪框
        self.click("//span[contains(text(),'确 定')]")
        sleep(3)
        self.click("//span[@class='goods-manage-upload']")
        self.click("//p[contains(text(),'预览')]")
        sleep(1)
        self.assert_element('//span[contains(text(),"预览")]')

    @pytest.mark.p1
    def test_stadProduct_edit_input(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 进入标品管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'标品管理')]")
        sleep(3)

        self.click("//div[contains(text(),'新建记录')]")
        sleep(2)
        pre_product_name = '药品测试品牌z 测试 测试 小板2*15mg'
        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入关键词']")[-1].send_keys(pre_product_name)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'查 询')]")[-1].click()
        sleep(2)

        self.driver.find_elements(By.XPATH, "//span[contains(text(),'纠错')]")[-1].click()
        sleep(5)
        self.switch_to_window(1)

        for i in range(6):
            text = '这是一个编辑测试文案'
            self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入不超过200个字符']")[i].send_keys(text)

        text = '详见产品说明书'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[-2].click()
        sleep(1)
        self.click("//div[contains(text(),'" + text + "')]")

        # 移动到部分
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'标品主图')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)

        text = 'otc'
        self.driver.find_elements(By.XPATH, "//div[@class='goods-manage-select-selector']")[-1].click()
        sleep(1)
        self.click("//div[contains(text(),'" + text + "')]")

        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"纠错原因")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        text = '这是一个编辑测试文案'
        self.driver.find_elements(By.XPATH, "//textarea[@placeholder='请输入']")[0].send_keys(text)

        self.assert_element_not_present('//span[contains(text(),"只允许输入中文、数字、英文、特殊字符集")]')
        self.assert_element_not_present('//span[contains(text(),"请完善信息")]')
        self.assert_element('//span[contains(text(),"提 交")]')
        self.assert_element('//span[contains(text(),"取 消")]')

    @pytest.mark.p1
    def test_stadProduct_edit_upload(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 进入标品管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'标品管理')]")
        sleep(3)

        self.click("//div[contains(text(),'新建记录')]")
        sleep(2)
        pre_product_name = '药品测试品牌z 测试 测试 小板2*15mg'
        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入关键词']")[-1].send_keys(pre_product_name)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'查 询')]")[-1].click()
        sleep(2)

        self.driver.find_elements(By.XPATH, "//span[contains(text(),'纠错')]")[-1].click()
        sleep(5)
        self.switch_to_window(1)

        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"标品主图")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.click("//span[@class='anticon anticon-close-circle close-icon___yakWH']")
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        # 增加了裁剪框
        self.click("//span[contains(text(),'确 定')]")
        sleep(3)
        self.click("//span[@class='goods-manage-upload']")
        self.click("//p[contains(text(),'预览')]")
        sleep(1)
        self.assert_element('//span[contains(text(),"预览")]')
        sleep(1)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-1].send_keys(
            path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
