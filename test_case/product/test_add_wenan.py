#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-20 20:37
 DESC :

 """
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip



class TestSytCustomer(BaseTestCase):

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_add_wenan(self):
        self.add_page_uploadImage()

        # 验证一级类目存在
        category_text = self.get_text("//*[text()='已开通类目']/../div[2]")  # 获取一级类目列表
        category_list = ["男鞋"]
        for cate in category_list:
            self.assert_in(cate, category_text)
        sleep(3)

        # 选择二级类目
        self.click("//span[contains(text(),'男鞋')]")
        sleep(10)

        # 选择三级类目
        self.click("//span[contains(text(),'雨鞋/雨靴')]")
        self.sleep(3)
        # 下一步，完善信息
        self.click('//span[contains(text(),"下一步")]')
        self.sleep(5)

        self.product_add_resure()

        # 填写标题校验

        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]", "商品标题测试商品标题测试商品标题测试商品标题测试商品标题测试商")
        sleep(1)
        self.assert_element('//div[contains(text(),"最多允许输入30个汉字（60字符）")]')

        # #填写短标题校验
        # #下限校验
        # elee = self.driver.find_element(By.XPATH, '//div[@id="itemRelease_title"]')
        # self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        # self.type("//input[@placeholder=\"支持2~10个字（4~20个字符）\"]", "哈")
        # sleep(1)
        # self.click('//span[contains(text(),"知道了")]')
        # sleep(1)
        # self.assert_element('//div[contains(text(),"商品短标题最少输入2个汉字（4个字符）")]')
        #
        # #上限校验
        # self.type("//input[@placeholder=\"支持2~10个字（4~20个字符）\"]", "123456789012345678901")
        # sleep(1)
        # self.assert_element('//div[contains(text(),"商品短标题最多输入10个汉字（20个字符）")]')
        #
        # #填写备注
        # #不支持特殊字符
        # elee = self.driver.find_element(By.XPATH, '//div[@id="itemRelease_itemRemark"]')
        # self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        # self.type("//input[@placeholder=\"支持中文、英文和数字，最多10个汉字(20个字符）\"]", "；；")
        # sleep(1)
        # self.assert_element('//div[contains(text(),"备注支持中文、英文、数字")]')
        #
        # #上限限制
        # self.type("//input[@placeholder=\"支持中文、英文和数字，最多10个汉字(20个字符）\"]", "备注备注备注备注备注备")
        # sleep(1)
        # self.assert_element('//div[contains(text(),"商品备注最多输入10个汉字（20个字符）")]')
        #
        # #填写商品卖点
        # #下限限制
        # elee = self.driver.find_element(By.XPATH, '//div[@id="itemRelease_sellingPoint"]')
        # self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        # self.type("//input[@placeholder=\"支持4-12个汉字（8-24个字符）\"]", "哈哈哈")
        # sleep(1)
        # self.assert_element('//div[contains(text(),"商品卖点最少输入4个汉字（8个字符）")]')
        #
        # #上限限制
        # self.type("//input[@placeholder=\"支持4-12个汉字（8-24个字符）\"]", "1234568123456812345681234")
        # sleep(1)
        # self.assert_element('//div[contains(text(),"商品卖点最多输入12个汉字（24个字符）")]')
        #
        #
        #
        #
        #
        #
        #
        #
