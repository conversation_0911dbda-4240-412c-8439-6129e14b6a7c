#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-08 11:45
 DESC :
 
 """
from ddt import ddt
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip


@ddt

class MyTestClass(BaseTestCase):

    # 类目选择--异常
    def test_createproduct(self):
        self.add_page_uploadImage()

        self.choose_category("定向邀约专用类目", "定邀类目", "search")
        self.assert_text("（线下定邀）您选择的类目需要定向邀请开通，请发送邮件（<EMAIL>）联系运营小二评估，详见","//span[contains(text(),'（线下定邀）您选择的类目需要定向邀请开通，请发送邮件（<EMAIL>）联系运营小二评估，详见')]")

        #资质校验类目
        sleep(2)
        self.choose_category("autotest-类目资质校验-有", "资质校验", "search")
        self.assert_text("您选择的商品类目所需资质《医疗器械生产许可证》，《特种行业许可证》，《质检报告》尚未上传，请前往电脑端「店铺管理-店铺信息-行业资质」维护资质，详见 ",
                         "//span[contains(text(),'您选择的商品类目所需资质《医疗器械生产许可证》，《特种行业许可证》，《质检报告》尚未上传，请前往电脑端「店铺管理-店铺信息-行业资质」维护资质，详见 ')]")

        #渠道校验类目
        sleep(2)
        self.choose_category("autotest-渠道校验-不可售", "渠道校验", "search")
        self.assert_text("您选择的类目在当前店铺类型暂不开放，类目开放明细可查询","//span[contains(text(),'您选择的类目在当前店铺类型暂不开放，类目开放明细可查询')]")


    #类目选择，最近使用，跳转下一页面
    @skip("111")
    def test_category_recently(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click("//span[contains(text(),'新增商品')]")
        self.driver.maximize_window()
        sleep(7)
        self.close_merchant_assistant()
        # 上传主图
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.click("//span[contains(text(),'确 定')]")
        sleep(3)
        ele = self.driver.find_element(By.XPATH,"//span[contains(text(),'商品类目')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        #选择最近选择类目
        self.click("//label[contains(text(),'最近使用：')]/../div/span")
        sleep(3)
        self.click('//span[contains(text(),"下一步")]')
        self.sleep(5)
        #进入发品页
        self.assert_no_404_errors()
        #切换类目
        self.click('//span[contains(text(),"切换类目")]')
        self.click('//span[contains(text(),"确 认")]')
        self.assert_no_404_errors()
        self.assert_text("错挂类目会导致商品下架，查看", "//span[contains(text(),'错挂类目会导致商品下架，查看')]")
        sleep(1)

    #类目选择，猜你喜欢
    @pytest.mark.p1
    def test_category_like(self):
        self.add_page_uploadImage()
        self.click("//label[contains(text(),'猜你想选：')]/../div/span")
        sleep(3)
        self.click('//span[contains(text(),"下一步")]')
        self.sleep(5)

        self.product_add_resure()
        #进入发品页
        self.assert_no_404_errors()
        #切换类目
        self.click('//span[contains(text(),"切换类目")]')
        self.click('//span[contains(text(),"确 认")]')
        self.assert_no_404_errors()
        self.assert_text("错挂类目会导致商品下架，查看", "//span[contains(text(),'错挂类目会导致商品下架，查看')]")
        sleep(1)

    #类目选择，标品类目，匹配成功
    # @skip("执行问题，调试中")
    @pytest.mark.p1
    def test_category_standard(self):
        self.add_page_uploadImage()
        #选择猜你想选类目
        ele = self.driver.find_element("class name", "goods-select-selection-search")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.choose_category("标品发布", "标品类目", "search")
        self.driver.find_elements(By.XPATH,'//input[@class="goods-select-selection-search-input"]')[1].send_keys("自动化匹配标品主图")
        sleep(2)
        self.click('//div[@class="goods-select-item-option-content"]/span')
        sleep(5)
        self.click('//span[contains(text(),"下一步")]')
        sleep(5)

        self.product_add_resure()

        #匹配标品成功
        self.assert_text('匹配到标品信息', "//span[contains(text(),'匹配到标品信息')]")
        self.click("//span[contains(text(),'知道了')]")
        #匹配标品主图
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"商品主图")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.assert_text('标品主图推荐', "//div[contains(text(),'标品主图推荐')]")
        sleep(2)






