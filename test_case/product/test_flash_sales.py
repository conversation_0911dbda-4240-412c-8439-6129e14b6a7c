#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : chuyifeng
 Time :
 DESC :

 """
from unittest import skip
from webbrowser import open_new

import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

from utils.apiUtils import ApiTools


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p1
    def test_flashSale_rules(self):
        self.login("PRODUCT_DOMAIN", "product_account_2")
        self.assert_title("快手小店")

        # 进入闪电购规则管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'闪电购管理')]")
        sleep(2)
        self.assert_element('//div[contains(text(),"闪电购运营指南")]')

        # 设置闪电购规则
        self.driver.find_element(By.XPATH, "//input[@placeholder='请输入']").send_keys('100')
        self.assert_element_not_present('//div[contains(text(),"请正确填写商品限购")]')

        text = '1.7'
        self.click(
            "//div[@class='kwaishop-goods-release-multimodal-pc-select kwaishop-goods-release-multimodal-pc-select-single kwaishop-goods-release-multimodal-pc-select-show-arrow kwaishop-goods-release-multimodal-pc-select-show-search']")
        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'" + text + "')]")[0].click()
        self.click(
            "//div[@class='kwaishop-goods-release-multimodal-pc-select kwaishop-goods-release-multimodal-pc-select-single kwaishop-goods-release-multimodal-pc-select-allow-clear kwaishop-goods-release-multimodal-pc-select-show-arrow']")
        # self.click(
        #     "//div[@class='kwaishop-goods-release-multimodal-pc-select kwaishop-goods-release-multimodal-pc-select-single kwaishop-goods-release-multimodal-pc-select-allow-clear kwaishop-goods-release-multimodal-pc-select-show-arrow']")
        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'支持7天无理由退货')]")[-2].click()
        self.assert_element_not_present('//div[contains(text(),"请正确填写退货规则")]')

        # 跳转新增模版
        self.click("//span[contains(text(),'新增模板')]")
        self.assert_no_404_errors()

    @pytest.mark.p1
    def test_flashSale_records(self):
        self.login("PRODUCT_DOMAIN", "product_account_2")
        self.assert_title("快手小店")

        # 进入闪电购规则管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'闪电购管理')]")
        sleep(2)
        self.assert_element('//div[contains(text(),"闪电购运营指南")]')

        # 查看售卖记录
        self.click("//div[contains(text(),'查看售卖记录')]")
        sleep(3)
        nums_str = self.get_text("//div[@class='kwaishop-goods-release-multimodal-pc-pro-toolbar-total-content ']",
                                 by="css selector", timeout=None)
        num1 = nums_str[1:-1]

        # 时间筛选
        ele = self.driver.find_element(By.XPATH, "//input[@placeholder='开始时间']")
        js = 'arguments[0].removeAttribute("readonly");'
        self.driver.execute_script(js, ele)
        sleep(1)
        ele.send_keys('2024-12-01 00:00:00')

        self.click("//span[contains(text(),'最近7天')]")
        self.click("//span[contains(text(),'查 询')]")
        sleep(1)
        nums_str = self.get_text("//div[@class='kwaishop-goods-release-multimodal-pc-pro-toolbar-total-content ']",
                                 by="css selector", timeout=None)
        num2 = nums_str[1:-1]
        self.assert_not_equal(num1, num2, msg=None)

    @pytest.mark.p0
    @pytest.mark.smoke
    # @skip("保证金问题待处理")
    def test_open_flash_sale_page(self):
        """
        PC闪电购场景覆盖
        """
        self.login("PRODUCT_DOMAIN", "product_account_3")
        self.assert_title("快手小店")

        # 接口调用，开启开播状态
        self.sdg_change_live_status()

        # PC打开开播助手
        self.click("//span[contains(text(),'内容')]")
        sleep(1)
        self.click("//span[contains(text(),'跟播助手')]")
        sleep(5)
        self.switch_to_window(1)
        self.SDGpage_set_localStorage()
        sleep(3)
        self.assert_element('//span[contains(text(),"暂无上车商品，去添加商品赚钱吧")]')

        # 开启闪电购，进入发品页
        self.click("//div[contains(text(),'直播设置')]")
        sleep(1)
        self.driver.find_element(By.XPATH, "//button[@class='ant-switch ant-switch-small']").click()
        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'闪电购')]")[1].click()
        sleep(3)
        self.assert_element('//div[contains(text(),"新建闪电购商品")]')

        ############# 主图上传 #######################
        self.assert_element('//div[contains(text(),"待截取或本地上传")]')
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/test_addMainImage.png')
        sleep(3)
        self.assert_element_not_present('//div[contains(text(),"待截取或本地上传")]')

        ############# 类目选择 #######################
        text = 'autotest-闪电购类目'
        self.type("//input[@class='kwaishop-goods-release-multimodal-pc-select-selection-search-input']", text)
        sleep(2)
        self.click("//div[contains(text(),'" + text + "')]")
        sleep(1)

        ############# 文本填写 #######################
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"发货时间")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.driver.find_element(By.XPATH, "//input[@placeholder='1~100000']").send_keys('100')
        self.driver.find_element(By.XPATH, "//input[@placeholder='1~150']").send_keys('100')

        ############# 发品+列表查询 #######################
        self.click("//span[contains(text(),'立即上车')]")
        sleep(3)
        self.assert_element_not_present('//span[contains(text(),"暂无上车商品，去添加商品赚钱吧")]')
        self.assert_element('//div[contains(text(),"闪电购商品")]')
        self.assert_element('//span[contains(text(),"下车")]')
        self.assert_element('//span[contains(text(),"价格库存")]')
        self.assert_element('//span[contains(text(),"开始讲解")]')

        ############# 下车删除+列表查询 #######################
        self.click('//span[contains(text(),"下车")]')
        sleep(1)
        self.assert_element('//div[contains(text(),"下车后闪电购商品将被删除，请谨慎操作")]')
        self.click('//span[contains(text(),"确 定")]')
        sleep(3)
        self.assert_element('//span[contains(text(),"暂无上车商品，去添加商品赚钱吧")]')

        # 接口调用，关闭开播状态（不关也没啥影响）
        # self.sdg_change_live_status(open_it=False)





