import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_draft_save(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        #移动到部分
        self.click('//span[contains(text(),"保存草稿")]')
        self.assert_element('//span[contains(text(),"保存草稿失败，商品草稿标题不能为空，请填写商品标题")]')
        # self.assert_text("保存草稿失败，商品草稿标题不能为空，请填写商品标题", '//span[contains(text(),"保存草稿失败，商品草稿标题不能为空，请填写商品标题")]')

        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]", "草稿箱测试草稿箱测试")
        self.click('//span[contains(text(),"保存草稿")]')
        sleep(2)
        self.assert_text("保存草稿成功", '//span[contains(text(),"保存草稿成功")]')

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_draft_edit(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        #收起商家助手
        self.close_merchant_assistant()

        self.click('//span[contains(text(),"草稿箱")]')
        # 放量状态，有重复跳转，增加时长
        sleep(10)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.refresh()
        sleep(10)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"编辑")]')[1].click()
        sleep(8)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_element('//span[contains(text(),"下一步，完善商品信息")]')

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_draft_delete(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        # 收起商家助手
        self.close_merchant_assistant()

        self.click('//span[contains(text(),"草稿箱")]')
        # 放量状态，有重复跳转，增加时长
        sleep(10)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.refresh()
        sleep(5)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"删除")]')[2].click()
        self.assert_text("商品草稿删除后无法恢复，是否确认删除？", '//div[contains(text(),"商品草稿删除后无法恢复，是否确认删除？")]')
        self.click('//span[contains(text(),"确 定")]')






