import os
from unittest import skip

import pytest
from time import sleep

from selenium.webdriver.common.by import By

from test_case.core_link.product_center.base import BaseTestCase
from test_case.core_link.product_center.base_config import UI_ITEMS_ID


class Test_bubtton(BaseTestCase):

    @pytest.mark.p2
    def test_product_preview(self):
        """
        列表页预览 预览弹窗正常展示 约20s
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)

        # 点击预览
        self.click("span[class='anticon anticon-system-previewopen-line usualIcon___xd5Qq viewIcon___MCvhO'] svg")
        sleep(1)
        # 检查弹窗是否开启
        self.assert_text('预览', "//div[@class='goods-list-v1-drawer-title']")
        self.assert_element('//span[contains(text(),"手机打开快手扫一扫预览商品展示效果")]')

    @pytest.mark.p1
    def test_product_combinedImage(self):
        """
        商品素材	主图合图		页面元素
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品素材')]")
        sleep(3)
        self.click("//div[contains(text(),'素材工具')]")
        self.click("//span[contains(text(),'主图合图')]")
        sleep(3)

        tab_list1 = ["合图模版", "我的商品素材", "达人合图素材"]
        for item in tab_list1:
            self.assert_element('//span[contains(text(),{})]'.format(item))

        # 合图模版页
        self.click("//div[contains(text(),'合图模板')]")
        self.click("//div[contains(text(),'日常模板')]")
        self.assert_element("//span[contains(text(),'使用模板')]")
        self.click("//div[contains(text(),'活动模板')]")
        self.assert_element("//span[contains(text(),'使用模板')]")

        # 合图素材
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'我的合图素材')]")[-1].click()
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查 询")]')[-1].click()
        sleep(1)

        # 达人合图素材页
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'达人合图素材')]")[-1].click()
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查 询")]')[-1].click()
        sleep(1)

    @pytest.mark.p1
    def test_product_testImage(self):
        """
        商品素材	主图测图	页面元素
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品素材')]")
        sleep(3)
        self.click("//div[contains(text(),'素材工具')]")
        self.click("//span[contains(text(),'主图测图')]")
        sleep(3)

        tab_list1 = ["全部", "待投放", "投放中", "投放结束"]
        for item in tab_list1:
            self.click("//div[contains(text(),'{}')]".format(item))
            self.driver.find_elements(By.XPATH, '//span[contains(text(),"查 询")]')[-1].click()
            sleep(1)


    @pytest.mark.p2
    def test_product_share(self):
        """
        列表页分享 分享页面正常展示 约20s
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)

        self.click("svg[clip-rule='evenodd'][fill-rule='evenodd'][viewBox='0 0 24 24'][focusable='false'][data-icon='share-export-line']")
        sleep(1)
        # 检查弹窗是否开启
        self.assert_element('//a[contains(text(),"下载二维码")]')
        self.assert_element('//span[contains(text(),"复制链接")]')

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_product_queryReset(self):
        """
        重置按钮 搜索商品后点击重置按钮，搜索结果为全部
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)

        # 确认仅1条数据
        nums_str = self.get_text("//div[@class='goods-list-v1-pro-toolbar-total-content verticalLine']",
                      by="css selector", timeout=None)
        num1 = nums_str[1:-1]
        self.assert_equal(num1, '1', msg=None)

        # 点击重置按钮
        self.click('//span[contains(text(),"重 置")]')
        sleep(6)

        # 数据重置，不为1条
        nums_str2 = self.get_text("//div[@class='goods-list-v1-pro-toolbar-total-content verticalLine']",
                                 by="css selector", timeout=None)
        self.assert_not_equal(nums_str2, nums_str, msg=None)

    @pytest.mark.p1
    def test_export_products_records(self):
        """
        导出商品与导出记录
        导出商品成功【搜索部分商品进行导出】，导出记录展示正常
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        good_id = UI_ITEMS_ID['listPage_query']

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        # 导出前查看记录条数
        self.click('//span[contains(text(),"导出记录")]', delay=5)
        sleep(3)
        self.switch_to_window(1)
        nums_str = self.get_text("//div[@class='sc-dkPtyc gnliEL']",by="css selector", timeout=None)
        num_pre = int("".join(list(filter(str.isdigit, nums_str))))

        # 回到小店，点击导出商品，弹出导出成功
        self.switch_to_window(0)
        self.type("//input[@placeholder=\"多条之间用中英文逗号或者空格分隔,最多50个\"]", good_id)
        self.click('//span[contains(text(),"查 询")]')
        self.click('//span[contains(text(),"导出商品")]', delay=3)
        self.assert_text("申请导出成功", "//div[@class='goods-list-v1-notification-notice-message']")

        # 再次点击导出记录，记录数量+1
        self.click('//span[contains(text(),"导出记录")]', delay=3)
        sleep(3)
        self.switch_to_window(2)
        nums_str = self.get_text("//div[@class='sc-dkPtyc gnliEL']",by="css selector", timeout=None)
        num_cur = int("".join(list(filter(str.isdigit, nums_str))))
        self.assert_true(num_cur == num_pre + 1, msg=None)

    @pytest.mark.p1
    def test_newVersion_jump(self):
        """
        商品处在新版本审核中（点击立即查看）	跳转到新版本审核的详情页面
        """
        self.login("PRODUCT_DOMAIN", "product_account_2")
        good_id = UI_ITEMS_ID['newVersion_audit']
        self.query_good_by_id(good_id, list_type='全部')
        self.logger.info("成功查询到商品")

        # 点击新版本审核
        self.click("//span[contains(text(),'新版本审核中')]")
        this_context = "您提交的商品信息已进入审核队列，请耐心等待，可前往查看新版本详情"
        this_selector = "//div[@class='goods-list-v1-popover-inner-content']"
        self.assert_text(this_context, this_selector)
        self.logger.info("测试文案over")

        # 点击跳转，判断跳转到详情页
        self.click("//span[contains(text(),'新版本审核中')]")
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"立即查看")]')[-1].click()
        sleep(3)
        self.switch_to_window(1)
        self.assert_element('//div[contains(text(),"商品详情")]')
        self.logger.info("测试跳转over")

    @pytest.mark.p2
    def test_onePoint_testProduct(self):
        """
        一键测品	半屏弹窗
        """
        self.login("PRODUCT_DOMAIN", "product_account_2")
        good_id = UI_ITEMS_ID['listPage_testProduct']
        self.query_good_by_id(good_id)
        self.logger.info("查询到商品 {}".format(good_id))

        self.click("//span[contains(text(),'一键测品')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"提交测品方案")]')

        self.click("//div[@class='goods-list-v1-select goods-list-v1-select-single goods-list-v1-select-allow-clear goods-list-v1-select-show-arrow']")
        self.click("//div[contains(text(),'广告账户ID：********-磁力金牛高级渠道户')]")

        self.click("//span[contains(text(),'取 消')]")

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_title_rename(self):
        """
        快捷操作 标题
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)
        self.logger.info("查询到商品 {}".format(good_id))

        # self.click('//*[@id="root"]/div[2]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div/div/div[2]/p/span')
        self.click('//span[contains(text(),"影响商城订单转化")]')
        # 服务器环境不同，时间放缓到6s
        sleep(6)
        self.driver.find_elements(By.XPATH, '//div[contains(text(),"手动优化")]')[0].click()
        sleep(3)
        self.assert_element('//div[contains(text(),"商品快捷编辑")]')

        name = "UI自动化快捷修改标题"
        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]", name)
        # self.click("//span[contains(text(),'取 消')]")
        self.type("//input[@placeholder=\"支持2~10个字（4~20个字符）\"]", name)
        sleep(1)

        self.assert_element_not_present('//div[contains(text(),"请输入标题")]')
        self.click("//span[contains(text(),'取 消')]")

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_mainImage_reset(self):
        """
        快捷操作 主图
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)
        self.logger.info("查询到商品 {}".format(good_id))

        self.click('//span[contains(text(),"影响商城订单转化")]')
        # 服务器环境不同，时间放缓到6s
        sleep(6)
        self.driver.find_elements(By.XPATH, '//div[contains(text(),"手动优化")]')[1].click()
        sleep(3)
        self.assert_element('//div[contains(text(),"商品快捷编辑")]')

        # 主图
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[1].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.assert_element('//div[contains(text(),"图片编辑工具")]')
        self.click("//span[contains(text(),'确认裁剪')]")
        sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

        # 3:4图
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[4].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.assert_element('//div[contains(text(),"图片编辑工具")]')
        self.click("//span[contains(text(),'确认裁剪')]")
        sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

        # 智能裁切
        sleep(6)
        self.click('//span[contains(text(),"1:1主图智能裁切")]')
        self.assert_element('//span[contains(text(),"3:4主图已帮您智能裁切")]')

        self.click("//span[contains(text(),'取 消')]")

