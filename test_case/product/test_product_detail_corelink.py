import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
import random
from time import sleep
from selenium.webdriver.common.by import By
from ddt import ddt
from unittest import skip, skipIf
from utils.env_help import get_work_dir
from test_case.core_link.product_center.base import BaseTestCase

class Test_bubtton(BaseTestCase):


    @skip("111")
    @pytest.mark.p0
    # @pytest.mark.smoke
    def test_onsale_product(self): # todo 适配case
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-list-v1-space goods-list-v1-space-horizontal goods-list-v1-space-align-start"]/div[2]/div/div')[0].click()
        sleep(5)

        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.driver.refresh()
        self.assert_text("商品详情", '//div[contains(text(),"商品详情")]')
        self.assert_text("下架商品", '//span[contains(text(),"下架商品")]')
        self.assert_text("发布相似品", '//span[contains(text(),"发布相似品")]')
        self.assert_text("复制链接", '//span[contains(text(),"复制链接")]')

        self.click('//span[contains(text(),"下架商品")]')
        self.assert_text("确定要下架该商品吗？", '//span[contains(text(),"确定要下架该商品吗？")]')
        self.click('//span[contains(text(),"确 认")]')
        self.assert_text("下架成功！", '//span[contains(text(),"下架成功！")]')


        self.click('//span[contains(text(),"复制链接")]')
        self.assert_text("复制成功！", '//span[contains(text(),"复制成功！")]')

        self.click('//span[contains(text(),"发布相似品")]')
        sleep(5)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("完善商品信息", '//div[contains(text(),"完善商品信息")]')

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_offline_product(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click('//span[contains(text(),"已下架")]')
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        self.driver.find_elements(By.XPATH, '//div[contains(text(),"UI自动化新增商品")]')[0].click()
        sleep(5)

        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.driver.refresh()
        self.assert_text("商品详情", '//div[contains(text(),"商品详情")]')
        self.assert_text("上架商品", '//span[contains(text(),"上架商品")]')
        self.assert_text("删除商品", '//span[contains(text(),"删除商品")]')
        self.assert_text("发布相似品", '//span[contains(text(),"发布相似品")]')
        self.assert_text("复制链接", '//span[contains(text(),"复制链接")]')

        self.click('//span[contains(text(),"上架商品")]')
        self.assert_text("确定要上架该商品吗？", '//span[contains(text(),"确定要上架该商品吗？")]')
        self.click('//span[contains(text(),"取 消")]')
        sleep(2)

        self.click('//span[contains(text(),"复制链接")]')
        self.assert_text("复制成功！", '//span[contains(text(),"复制成功！")]')
        sleep(2)

        # self.click('//span[contains(text(),"删除商品")]')
        # self.assert_text("确定要删除该商品吗？", '//span[contains(text(),"确定要删除该商品吗？")]')
        # self.click('//span[contains(text(),"确 认")]')
        # self.assert_text("删除成功！", '//span[contains(text(),"删除成功！")]')
        # sleep(8)
        # self.assert_text("商品列表", '//span[contains(text(),"商品列表")]')


    @skip("111")
    def test_admining_product(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click('//span[contains(text(),"审核中")]')
        sleep(2)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-list-v1-space goods-list-v1-space-horizontal goods-list-v1-space-align-start"]/div[2]/div/div')[0].click()
        sleep(5)

        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.driver.refresh()
        self.assert_text("待审核", '//span[contains(text(),"待审核")]')
        self.assert_text("复制链接", '//span[contains(text(),"复制链接")]')

        self.click('//span[contains(text(),"复制链接")]')
        self.assert_text("复制成功！", '//span[contains(text(),"复制成功！")]')
        sleep(2)

    def test_admin_modify_product(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click('//span[contains(text(),"审核待修改")]')
        sleep(10)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-list-v1-space goods-list-v1-space-horizontal goods-list-v1-space-align-start"]/div[2]/div/div')[0].click()
        sleep(5)

        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.driver.refresh()
        self.assert_text("复制链接", '//span[contains(text(),"复制链接")]')

        self.click('//span[contains(text(),"复制链接")]')
        self.assert_text("复制成功！", '//span[contains(text(),"复制成功！")]')
        sleep(2)

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_delete_product(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click('//span[contains(text(),"已下架")]')
        sleep(5)
        self.driver.find_elements(By.XPATH, '//div[contains(text(),"测试商品勿拍不发货penapi发品单测商品标题（新增可删除）")]')[-1].click()
        # self.driver.find_elements(By.XPATH, '//div[@class="goods-list-v1-space goods-list-v1-space-horizontal goods-list-v1-space-align-start"]/div[1]/div/div')[0].click()
        sleep(5)
        self.switch_to_window(-1)
        self.assert_no_404_errors()
        self.refresh()
        sleep(5)
        self.assert_text("复制链接", '//span[contains(text(),"复制链接")]')
        self.assert_text("删除商品", '//span[contains(text(),"删除商品")]')

        self.click('//span[contains(text(),"复制链接")]')
        self.assert_text("复制成功！", '//span[contains(text(),"复制成功！")]')
        sleep(2)

        self.click('//span[contains(text(),"删除商品")]')
        self.assert_text("确定要删除该商品吗？", '//span[contains(text(),"确定要删除该商品吗？")]')
        sleep(2)
        self.click('//span[contains(text(),"取 消")]')


