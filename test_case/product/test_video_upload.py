
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time
from unittest import skip

class TestSytCustomer(BaseTestCase):

    @pytest.mark.p1
    def test_add_video(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search", account="product_account_2")

        #进入视频列表页，侧边窗
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'支持上传本地视频和关联短视频素材')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        self.click("//div[contains(text(),'上传视频')]")
        self.click("//p[contains(text(),'选择视频')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"选择视频")]')

        # 上传弹窗
        self.click("//span[contains(text(),'本地上传')]")
        sleep(1)
        self.assert_element('//div[contains(text(),"点击上传或直接将视频文件拖入此区域")]')
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-1].send_keys(path + '/kwaishopuiautotest/test_data/img/upload_product_video.mp4')
        sleep(15)
        video_name = 'UI自动化测试视频'
        self.driver.find_element(By.XPATH, "//textarea[@placeholder='上传视频后为您自动生成标题，支持修改']").send_keys(video_name)
        self.assert_element_not_present('//div[contains(text(),"自动生成封面图")]')
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'确 定')]")[-1].click()
        sleep(2)

        self.assert_element('//span[contains(text(),"视频上传成功，已为您自动刷新列表")]')

    @pytest.mark.p1
    def test_edit_video(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search", account="product_account_2")

        #进入视频列表页，侧边窗
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'支持上传本地视频和关联短视频素材')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        self.click("//div[contains(text(),'上传视频')]")
        self.click("//p[contains(text(),'选择视频')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"选择视频")]')

        #在线编辑弹窗
        self.driver.find_elements(By.XPATH, "//span[@class='anticon anticon-system-edit-line']")[-2].click()
        sleep(3)
        self.assert_element('//div[contains(text(),"云剪编辑器")]')

    @pytest.mark.p1
    def test_delete_video(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search", account="product_account_2")

        #进入视频列表页，侧边窗
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'支持上传本地视频和关联短视频素材')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        self.click("//div[contains(text(),'上传视频')]")
        self.click("//p[contains(text(),'选择视频')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"选择视频")]')

        #删除弹窗
        self.driver.find_elements(By.XPATH, "//span[@class='anticon anticon-system-delete-line']")[-2].click()
        self.assert_element('//div[contains(text(),"确定删除视频，删除将无法恢复")]')
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'确 定')]")[1].click()
        sleep(2)
        self.assert_element('//span[contains(text(),"删除成功")]')

    @pytest.mark.p1
    def test_query_video(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search", account="product_account_2")

        #进入视频列表页，侧边窗
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'支持上传本地视频和关联短视频素材')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        self.click("//div[contains(text(),'上传视频')]")
        self.click("//p[contains(text(),'选择视频')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"选择视频")]')

        # 切换短视频素材tab 时间选择器
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'短视频素材')]")[-1].click()
        sleep(1)
        ele = self.driver.find_element(By.XPATH, "//input[@placeholder='开始时间']")
        js = 'arguments[0].removeAttribute("readonly");'
        self.driver.execute_script(js, ele)
        sleep(1)
        ele.send_keys('2024-12-01 00:00:00')
        self.click("//span[contains(text(),'最近7天')]")
        sleep(1)
        self.click("//span[contains(text(),'查 询')]")
        sleep(3)

        #切换已上传视频tab 单选，时间选择器
        self.click("//span[contains(text(),'已上传视频')]")
        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'全部')]")[-3].click()
        sleep(1)
        self.click("//div[contains(text(),'本地上传')]")
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'全部')]")[-2].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'审核通过')]")[-1].click()
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'全部')]")[-1].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, "//div[contains(text(),'全部')]")[-1].click()
        ele = self.driver.find_element(By.XPATH, "//input[@placeholder='开始时间']")
        js = 'arguments[0].removeAttribute("readonly");'
        self.driver.execute_script(js, ele)
        sleep(1)
        ele.send_keys('2024-12-01 00:00:00')
        self.click("//span[contains(text(),'最近45天')]")
        sleep(1)
        self.click("//span[contains(text(),'查 询')]")
        sleep(3)

        #关联成功
        self.driver.find_elements(By.XPATH, "//input[@class='goods-radio-input']")[-1].click()
        self.click("//span[contains(text(),'选 用')]")
        sleep(3)
        self.assert_element_not_present("//div[contains(text(),'上传视频')]")





