import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.select import Select
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException
from selenium import webdriver
from unittest import skip



class TestSytCustomer(BaseTestCase):

    # pc小店首页
    @skip("暂时屏蔽")
    @pytest.mark.p1
    def test_attribute_pre(self):
        #self.login("PRODUCT_DOMAIN", "product_account")
        self.add_page("洗发护发套装", "洗发/护发", "search")
        # 移动到部分
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"重要属性")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(2)
        # 填充文本组件-【保质期】
        self.type("//span[@class=\"goods-input-group goods-input-group-compact\"]/input","24")
        sleep(2)
        self.assert_text('请设置单位, 该字段是必填项', "//div[@class=\"goods-formily-item-error-help goods-formily-item-help goods-formily-item-help-enter goods-formily-item-help-enter-active\"]")
        # 填充单位
        self.driver.find_elements(By.XPATH, '//input[@class="goods-select-selection-search-input"]')[3].click()
        self.driver.find_elements(By.XPATH, '//div[@ class="rc-virtual-list-holder-inner"]')[0].click()
        sleep(2)
        self.assert_element("//div[@class=\"goods-select-selector\"]//span[@title=\"天\"]")

        # 填充双选项配置-【是否进口】
        # 选择是 页面有【报关单】属性
        self.driver.find_elements(By.XPATH, '//div[@title="是"]')[1].click()
        sleep(2)
        self.assert_text('报关单', "//label[contains(text(),\"报关单\")]")

        # 选择否 页面无【报关单】属性
        self.driver.find_elements(By.XPATH, '//div[@title="否"]')[1].click()
        sleep(2)
        try:
            element = self.driver.find_element(By.XPATH, "//label[contains(text(),\"报关单\")]")
            # 如果元素存在，抛出自定义异常
            raise Exception("元素存在")
        except NoSuchElementException:
            # 元素不存在，不抛出异常
            print("元素不存在")

        # 填充单选框-【产地】
        self.driver.find_elements(By.XPATH, '//div[@class="goods-select goods-select-single goods-select-allow-clear goods-select-show-arrow goods-select-show-search"]')[2].click()
        sleep(4)
        print("//div[@class=\"goods-select-item-option-content\"]")
        self.driver.find_element(By.XPATH, '//div[@title="山东"]').click()
        sleep(2)
        self.assert_element("//div//span[@title=\"山东\"]")
        sleep(2)

        # 填充多选框-功能
        self.driver.find_elements(By.XPATH, '//div[@class="goods-select-selector"]')[5].click()
        sleep(2)
        self.driver.find_element(By.XPATH, '//div[@title="其它"]/input').click()
        sleep(2)
        self.driver.find_element(By.XPATH, '//div[@title="防脱生发"]/input').click()
        sleep(2)
        # 时间选择器-【生产日期】
        self.type("//div[@class=\"goods-picker-input\"]//input", "2024-06-08")
        sleep(2)
        self.assert_element("//input[@title=\"2024-06-08\"]")

    @skip("111")
    @pytest.mark.p1
    def test_distribution(self):
        self.add_page("autotest-渠道校验-可售", "自动化测试类目", "search")
        # 移动到部分
        elee = self.driver.find_element(By.XPATH, '//span[text()="退货规则"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(2)
        # 验证商品限购开关
        self.driver.find_elements(By.XPATH, '//input[@role="spinbutton"]')[4].send_keys(10)
        sleep(2)
        self.assert_element("//input[@value=\"10\"]")
        self.driver.find_element(By.XPATH,
                                 '//div[@id="itemRelease_limitParam"]//button[@class="goods-switch goods-switch-checked"]').click()
        sleep(3)
        # 定点开售 //button[@class="goods-switch"]
        self.driver.find_element(By.XPATH, '//button[@class="goods-switch"]').click()
        sleep(2)
        # 关闭商品立即上架
        self.driver.find_element(By.XPATH, '//div[@id="itemRelease_serviceRule_immediatelyOnOfflineFlag"]//button[@class="goods-switch goods-switch-checked"]').click()
        # 分销推广
        # 填报佣金率
        self.driver.find_element(By.XPATH, '//div[@id="itemRelease_distributeRate"]//input[@class="goods-input"]').send_keys("30")
        #关闭分销
        self.driver.find_element(By.XPATH,'//div[@id="itemRelease_distributePromotion"]//button').click()

















