#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : chuyifeng
 Time :
 DESC :

 """
from webbrowser import open_new

import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

from utils.apiUtils import ApiTools


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p1
    def test_basic_goldPrice_page(self):
        self.login("PRODUCT_DOMAIN", "product_account_2")
        self.assert_title("快手小店")

        # 进入基础金价管理页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'基础金价管理')]")
        sleep(5)
        self.assert_element('//div[contains(text(),"基础金价管理攻略")]')

        # 文本框
        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入']")[0].send_keys('300')
        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入']")[-1].send_keys('300')

        # tab切换
        self.assert_element('//th[contains(text(),"商品信息")]')
        self.assert_element('//div[contains(text(),"以下商品为使用了基础金价的商品。如果商品未出现在这里，可能原因如下：")]')
        self.click("//span[contains(text(),'改价失败')]")
        sleep(3)
        self.assert_element('//th[contains(text(),"商品信息")]')
        self.assert_element('//th[contains(text(),"改价失败原因")]')
        self.click("//span[contains(text(),'全部')]")
        sleep(3)
        self.assert_element_not_present('//th[contains(text(),"改价失败原因")]')

        # 跳转商品编辑
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'编辑')]")[0].click()
        sleep(3)
        self.assert_no_404_errors()
        self.switch_to_window(-1)
        self.assert_element('//span[contains(text(),"完善商品信息")]')






