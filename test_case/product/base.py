#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-08 11:44
 DESC :
 
 """

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain

import time
class BaseTestCase(BaseCase):

    def login(self, domain, account):
        account_data = get_account_info("product_account")
        # 用户名 account_data['account']
        # 密码 account_data['password']

        host = get_domain(domain)

        self.open(host)

        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.assert_text("扫码登录", "//*[text()='扫码登录']")  # div标签中的第一个元素
        self.assert_text("手机号登录", "//*[text()='手机号登录']")
        self.click("//*[text()='手机号登录']")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        time.sleep(5)
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kefu-pingtaikefu-jiedaiyouhua-old-sider_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_gouwutiyanxingjiv1_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_fuwushichang-caidanqianyi2_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dianpuxinxishezhi-eduguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_xiaodiankefupingtai-lixiangongneng_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dingdanchaxun-yinsijiami_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-xiaoer-message_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_daifaguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-cs-center_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_111111112_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kefu-fenliufangshi-2023-12-18_1724903200','1');")
        self.refresh()
        self.refresh()
        time.sleep(2)


    def login_sub_account(self, domain, account):
        account_data = get_account_info("product_account_sub")
        # 用户名 account_data['account']
        # 密码 account_data['password']

        host = get_domain(domain)

        self.open(host)

        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.assert_text("扫码登录", "//*[text()='扫码登录']")  # div标签中的第一个元素
        self.assert_text("手机号登录", "//*[text()='手机号登录']")
        self.click("//*[text()='手机号登录']")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        time.sleep(2)
        self.click("//div[contains(text(),'z.69825个人店')]")
        time.sleep(5)
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kefu-pingtaikefu-jiedaiyouhua-old-sider_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_gouwutiyanxingjiv1_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_fuwushichang-caidanqianyi2_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dianpuxinxishezhi-eduguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_xiaodiankefupingtai-lixiangongneng_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dingdanchaxun-yinsijiami_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-xiaoer-message_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_daifaguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-cs-center_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_111111112_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kefu-fenliufangshi-2023-12-18_1724903200','1');")
        self.refresh()
        self.refresh()
        time.sleep(2)




