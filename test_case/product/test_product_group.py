import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip

class TestSytCustomer(BaseTestCase):

    @pytest.mark.p1
    def test_batch_group_link(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        #不选择商品
        self.click('//span[contains(text(),"加入分组")]')
        self.assert_text("请先选择商品",'//span[contains(text(),"请先选择商品")]')

        #选择十个商品
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"加入分组")]')
        sleep(5)

        self.click('//span[contains(text(),"分组管理")]')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_element('//span[contains(text(),"商品分组管理")]')
        self.click('//span[contains(text(),"新增商品分组")]')
        sleep(3)

        #创建新分组 弹窗
        self.driver.find_elements(By.XPATH, "//input[@class='kwaishop-tianhe-good-group-manage-pc-input']")[-1].send_keys("UI自动化测试")
        sleep(2)
        # self.type("//input[@class='kwaishop-tianhe-good-group-manage-pc-input']", "UI自动化测试")
        self.click('//span[contains(text(),"确 定")]')
        sleep(2)
        # self.driver.refresh()









