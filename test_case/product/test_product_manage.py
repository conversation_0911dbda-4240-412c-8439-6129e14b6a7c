#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-19 19:52
 DESC :
 
 """
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

class TestSytCustomer(BaseTestCase):

    # pc小店首页
    @pytest.mark.p0
    @pytest.mark.smoke
    def test_pc_homepage(self):
        self.login("PRODUCT_DOMAIN", "product_account")

        self.assert_title("快手小店")
        sleep(2)
        self.click("//span[contains(text(),'商品')]")
        sleep(2)
        # 页面切换延迟，重复点击
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        tabs_text1 = self.get_text('//span[contains(text(),"商品管理")]/../../../ul')  # 获取商品管理下面的功能tab
        tab_list1 = ["新增商品", "商品导入", "券码工具箱","带货商品报备","商品资质管理"]
        for item in tab_list1:
            self.assert_in(item, tabs_text1)

        # 商品素材独立tab
        self.assert_element('//span[contains(text(),"商品素材")]')

        tabs_text2 = self.get_text('//span[contains(text(),"商品成长")]/../../../ul')
        # 诊断页面下线
        # tab_list2 = ["商品诊断"]
        # for item in tab_list2:
        #     self.assert_in(item, tabs_text2)


