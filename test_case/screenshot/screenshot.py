import datetime
import os
from time import sleep

import pytest
from selenium import webdriver
from constant.account import get_account_info

# 获取账号信息
login_data = get_account_info("hanyanping")

# current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = r"C:/Users/<USER>/Desktop/kwaishopuiautotest/screenshot"

# driver = webdriver.Safari()
#
# driver.get(
#     "https://sso.corp.kuaishou.com/cas/login?service=https%3A%2F%2Ftianwen.corp.kuaishou.com%2Flogin%2Fcas%3Ftw_front_redirect_url%3Dhttps%253A%252F%252Ftianwen.corp.kuaishou.com%252Fservice-analyzer%252Fproduct%252F42717%252Fpatrol")
driver =webdriver.Chrome()
driver.get("https://tianwen.corp.kuaishou.com/")

driver.maximize_window()
# 输入用户名密码登陆
driver.find_element('id', 'ssoUsername').send_keys(login_data['account'])
driver.find_element('id', 'ssoPassword').send_keys(login_data['password'])

driver.find_element('xpath', '//*[@id="ssoSubmit"]').click()
# 等待登陆后页面加载的时间，后续需要优化为显式等待或者隐式等待为好
sleep(8)
driver.implicitly_wait(30)
driver.find_element('xpath', '//*[@id="app"]/section/section[2]/div[2]/main/div/main/div[1]/div/div/ul/li[2]').click()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_account_api():
    # kspay-account-api截图
    screenshot_path="kspay-account-api"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[1]/div[2]/div[2]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_account_biz_transfer_service():
    # kspay-account-biz-transfer-service截图
    screenshot_path="kspay-account-biz-transfer-service"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[1]/div[2]/div[8]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_account_biz_transfer_server_service():
    # kspay-account-biz-transfer-server-service截图
    screenshot_path="kspay-account-biz-transfer-server-service"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[1]/div[2]/div[7]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_account_user_info_service():
    # kspay-account-user-info-service截图
    screenshot_path="kspay-account-user-info-service"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[1]/div[2]/div[20]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_account_user_info_server_service():
    # kspay-account-user-info-server-service截图
    screenshot_path="kspay-account-user-info-server-service"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):

        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[1]/div[2]/div[19]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_account_withdraw_service():
    # kspay-account-withdraw-service截图
    screenshot_path="kspay-account-withdraw-service"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):

        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[1]/div[2]/div[24]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()


@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_pay_service_gift():
    # kspay-pay-service-gift截图
    screenshot_path="kspay-pay-service-gift"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):

        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[4]/div[2]/div[5]/div[2]/div[1]/div[1]/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_pay_service_deposit():
    # kspay-pay-service-deposit截图
    screenshot_path="kspay-pay-service-deposit"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[4]/div[2]/div[4]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_pay_service_wallet():
    # kspay-pay-service-wallet截图
    screenshot_path="kspay-pay-service-wallet"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[4]/div[2]/div[10]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_mywallet_api():
    # kspay-mywallet-api截图
    screenshot_path="kspay-mywallet-api"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[6]/div[2]/div[2]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()

@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_kspay_mywallet_service():
    # kspay-mywallet-service截图
    screenshot_path="kspay-mywallet-service"
    if not os.path.exists(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path))):
        os.mkdir(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path)))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').send_keys(screenshot_path)
    sleep(2)
    driver.find_element('xpath', '//*[@id="pane-tree"]/div/div[1]/div/div/div/div/div/div[1]/span[1]/strong').click()
    sleep(2)
    # 点击HB1:PROD
    driver.find_element('xpath',
                        '//*[@id="pane-tree"]/div/div[1]/div/div/div[2]/div/div[2]/div[2]/div[8]/div[2]/div[6]/div[2]/div[3]/div[2]/div[1]/div/div[2]/span').click()
    # 点击系统监控
    driver.find_element('xpath','//*[@id="tab-topology-basic-component"]').click()
    sleep(3)
    # cpu截图
    # driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[1]/div[2]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[2]/i/svg/path').click()
    # sleep(2)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"cpu.png")))
    sleep(2)
    # driver.back()
    # 内存截图
    element = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/section/div[2]/div[2]/div[1]/div')
    driver.execute_script("arguments[0].scrollIntoView();", element)
    # driver.find_element('xpath','//*[@id="tw-collapse-content-8176"]/div/div/div/div[1]/div[2]/section[1]/div[2]/div[1]/i/svg/path').click()
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"内存.png")))
    # GC截图
    driver.find_element('xpath','//*[@id="tab-topology-jvm"]').click()
    sleep(3)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC1.png")))
    element_GC = driver.find_element('xpath','/html/body/div[1]/section/section[2]/div[2]/main/div/main/div[2]/section/main/div/main/section[1]/main/tw-container/div/div[1]/div[2]/div/div/div[3]/section[1]/div[1]')
    driver.execute_script("arguments[0].scrollIntoView();", element_GC)
    sleep(1)
    driver.save_screenshot(os.path.abspath(os.path.join(current_dir,"screenshot_all",screenshot_path,datetime.datetime.now().strftime('%Y-%m-%d日%H_%M_%S')+"GC2.png")))
    driver.find_element('xpath', '//*[@id="tree-container"]/div/div[1]/div[2]/div/div/div[1]/div/input').clear()


