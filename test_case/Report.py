# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

import datetime
import os
import time

import pkg_resources

try:
    from ansi2html import Ansi2<PERSON><PERSON>Converter, style

    ANSI = True
except ImportError:
    # ansi2html is not installed
    ANSI = False

from py.xml import raw

import bisect

from py.xml import html
from pytest_html.plugin import H<PERSON>LReport

from config.case_tag import DEFAULT_CASE_TAG

DEFAULT_CASE_LEVEL = 1


class MyHTMLReport(HTMLReport):
    tag_stat = {}

    def __init__(self, logfile, config):
        super().__init__(logfile, config)

    def _appendrow(self, outcome, report):
        outcome_lower = outcome.lower()
        if not hasattr(report, 'case_tags'):
            report.case_tags = DEFAULT_CASE_TAG
        if not hasattr(report, 'case_level'):
            report.case_level = DEFAULT_CASE_LEVEL
        case_level_str = ('p%s' % str(report.case_level)) if report.case_level != -1 else 'px'
        show_case_tag_set = {}
        for case_tag in report.case_tags:
            # tag最高只支持3级，在报告中只取前2级展示
            show_case_tag = '-'.join(case_tag.split('-', 2)[:2])
            if show_case_tag not in self.tag_stat:
                self.tag_stat[show_case_tag] = {
                    "passed": 0, "skipped": 0, "failed": 0, "error": 0, "xfailed": 0, "xpassed": 0, "rerun": 0,
                    "level": {}
                }
            tag_stat_case_tag = self.tag_stat[show_case_tag]
            tag_stat_case_tag[outcome_lower] += 1
            # 非rerun用例按level统计数目
            if outcome_lower != 'rerun':
                tag_stat_case_tag_level = tag_stat_case_tag['level']
                tag_stat_case_tag_level.setdefault(case_level_str, 0)
                tag_stat_case_tag_level[case_level_str] += 1
            # 用于html中筛选的tag名称
            show_case_tag_set[show_case_tag] = None
        result = self.TestResult(outcome, report, self.logfile, self.config)
        if result.row_table is not None:
            index = bisect.bisect_right(self.results, result)
            self.results.insert(index, result)
            tbody = html.tbody(
                result.row_table,
                # 前边带$的表示真实tag名称；不带$的show_case_tag是用于在html报告中筛选的tag名称
                class_="${} {} {} {} results-table-row".format(' $'.join(report.case_tags),
                                                               ' '.join(show_case_tag_set.keys()), case_level_str,
                                                               result.outcome.lower()),
            )
            if result.row_extra is not None:
                tbody.append(result.row_extra)
            self.test_logs.insert(index, tbody)

    def _generate_report(self, session):
        suite_stop_time = time.time()
        suite_time_delta = suite_stop_time - self.suite_start_time
        numtests = self.passed + self.failed + self.xpassed + self.xfailed
        generated = datetime.datetime.now()

        self.style_css = pkg_resources.resource_string(
            __name__, os.path.join("resources", "style.css")
        ).decode("utf-8")

        if ANSI:
            ansi_css = [
                "\n/******************************",
                " * ANSI2HTML STYLES",
                " ******************************/\n",
            ]
            ansi_css.extend([str(r) for r in style.get_styles()])
            self.style_css += "\n".join(ansi_css)

        # <DF> Add user-provided CSS
        for path in self.config.getoption("css"):
            self.style_css += "\n/******************************"
            self.style_css += "\n * CUSTOM CSS"
            self.style_css += f"\n * {path}"
            self.style_css += "\n ******************************/\n\n"
            with open(path, "r") as f:
                self.style_css += f.read()

        css_href = "assets/style.css"
        html_css = html.link(href=css_href, rel="stylesheet", type="text/css")
        if self.self_contained:
            html_css = html.style(raw(self.style_css))

        head = html.head(
            html.meta(charset="utf-8"), html.title("Test Report"), html_css
        )

        class Outcome:
            def __init__(
                    self, outcome, total=0, label=None, test_result=None, class_html=None
            ):
                self.outcome = outcome
                self.label = label or outcome
                self.class_html = class_html or outcome
                self.total = total
                self.test_result = test_result or outcome

                self.generate_checkbox()
                self.generate_summary_item()

            def generate_checkbox(self):
                checkbox_kwargs = {"data-test-result": self.test_result.lower()}
                if self.total == 0:
                    checkbox_kwargs["disabled"] = "true"

                self.checkbox = html.input(
                    type="checkbox",
                    onChange="filter_table(this)",
                    name="filter_checkbox",
                    class_="filter",
                    hidden="true",
                    **checkbox_kwargs,
                )

            def generate_summary_item(self):
                self.summary_item = html.span(
                    f"{self.total} {self.label}", class_=self.class_html
                )

        outcomes = [
            Outcome("passed", self.passed),
            Outcome("skipped", self.skipped),
            Outcome("failed", self.failed),
            Outcome("error", self.errors, label="errors"),
            Outcome("xfailed", self.xfailed, label="expected failures"),
            Outcome("xpassed", self.xpassed, label="unexpected passes"),
        ]

        if self.rerun is not None:
            outcomes.append(Outcome("rerun", self.rerun))

        summary = [
            html.p(f"{numtests} tests ran in {suite_time_delta:.2f} seconds. "),
            html.p(
                "(Un)check the boxes to filter the results.",
                class_="filter",
                hidden="true",
            ),
        ]

        for i, outcome in enumerate(outcomes, start=1):
            summary.append(outcome.checkbox)
            summary.append(outcome.summary_item)
            if i < len(outcomes):
                summary.append(", ")

        cells = [
            html.th("Result", class_="sortable result initial-sort", col="result"),
            html.th("Test", class_="sortable", col="name"),
            html.th("Duration", class_="sortable numeric", col="duration"),
            html.th("Links"),
        ]
        session.config.hook.pytest_html_results_table_header(cells=cells)

        results = [
            html.h2("Results"),
            html.table(
                [
                    html.thead(
                        html.tr(cells),
                        html.tr(
                            [
                                html.th(
                                    "No results found. Try to check the filters",
                                    colspan=len(cells),
                                )
                            ],
                            id="not-found-message",
                            hidden="true",
                        ),
                        id="results-table-head",
                    ),
                    self.test_logs,
                ],
                id="results-table",
            ),
        ]

        main_js = pkg_resources.resource_string(
            __name__, os.path.join("resources", "main.js")
        ).decode("utf-8")

        # session.config.hook.pytest_html_report_title(report=self)

        body = html.body(
            html.script(raw(main_js)),
            # html.h1(self.title),
            html.p(
                "Report generated on {} at {} by ".format(
                    generated.strftime("%d-%b-%Y"), generated.strftime("%H:%M:%S")
                )
            ),
            onLoad="init()",
        )

        body.extend(self._generate_environment(session.config))

        summary_prefix, summary_postfix = [], []
        session.config.hook.pytest_html_results_summary(
            prefix=summary_prefix, summary=summary, postfix=summary_postfix
        )
        body.extend([html.h2("Summary")] + summary_prefix + summary + summary_postfix)

        body.extend(results)

        doc = html.html(head, body)

        unicode_doc = "<!DOCTYPE html>\n{}".format(doc.unicode(indent=2))

        # Fix encoding issues, e.g. with surrogates
        unicode_doc = unicode_doc.encode("utf-8", errors="xmlcharrefreplace")
        return unicode_doc.decode("utf-8")


class TagOutcome:
    def __init__(
            self, outcome, tag, total=0, label=None, test_result=None, class_html=None
    ):
        self.outcome = outcome
        self.tag = tag
        self.label = label or outcome
        self.class_html = class_html or outcome
        self.total = total
        self.test_result = test_result or outcome

        self.generate_checkbox()
        self.generate_summary_item()

    def generate_checkbox(self):
        checkbox_kwargs = {"data-test-result": '{} {}'.format(self.tag, self.test_result.lower())}
        if self.total == 0:
            checkbox_kwargs["disabled"] = "true"

        self.checkbox = html.input(
            type="checkbox",
            onChange="filter_table(this)",
            name="filter_checkbox",
            class_="filter",
            hidden="true",
            **checkbox_kwargs,
        )

    def generate_summary_item(self):
        self.summary_item = html.span(
            f"{self.label}: {self.total}", class_='tag ' + self.class_html
        )
