import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

CHECK_BOX_ELEMENT = "//body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[1]/label[1]/span[1]"
NO_DATA_ELEMENT = "(//div[@class='kwaishop-agent-pc-micro-empty-description'])[1]"
NORTH_WEST_ORDER_FLAG_ELEMENT = "//body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]/span[1]"
TREASURE_ORDER_ELEMENT = "(//span[@class='kwaishop-agent-pc-micro-tooltip-disabled-compatible-wrapper'])[1]"
EMPTY_ELEMENT = '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
PLACE_ORDER_BUTTON = "(//span[contains(text(),'下 单')])[1]"
QUERY_BUTTON = "(//span[contains(text(),'查 询')])[1]"
COLLECT_BUTTON = '//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[2]'
PROGRESS_BUTTON = '//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[3]'
EXCEPTION_BUTTON = "//span[contains(text(),'异常订单')]"


@ddt
class TestOnlineParcelPlaceOrder(BaseTestCase):
    """
    在线寄件-下单发货页面
    登陆PC小店-进行入在线寄件
    1. 待发货
    2. 待揽收
    3. 进行中
    4. 异常订单
    """

    @pytest.mark.p0
    def test_online_parcel_home(self):
        self.online_parcel_login()

        # 获取并校验url
        # c_url = self.get_current_url()
        # self.assert_equal(c_url.endswith('/zone/supply/package/online-parcel-sending'), True)

        self.assert_text('下单发货', '#order-tab')
        self.assert_text('待发货', '//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[1]')

        self.check_document('//*[@id="tutorials"]/button[1]/span', '中小商家专属功能——快手在线寄件操作手册 - 轻雀文档')
        self.switch_to_window(0)
        self.check_document('//*[@id="tutorials"]/button[2]/span',
                            '在线寄件报价单-单量阶梯价，下单越多 价格越优惠 - 轻雀文档', 2)

    @pytest.mark.p0
    def test_to_be_ship_table(self):
        self.online_parcel_login()
        self.assert_text('订单编号',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('商品信息',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('收货信息',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('订单状态',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('商品数量',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('支付时间',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[3]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[7]')

    @pytest.mark.p0
    def test_to_be_ship_pay_time_query(self):
        self.online_parcel_login()

        self.assert_text('支付日期',
                         '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/label')
        # 支付日期选择
        self.click(
            '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(1)
        # 近60天
        self.click("//span[contains(text(),'近60天')]")
        now_time = datetime.now()
        three_month_ago = now_time - timedelta(days=60)

        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = three_month_ago.strftime("%Y-%m-%d 23:59:59")
        self.click(QUERY_BUTTON)
        self.assert_text(begin_formatted_time,
                         '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.assert_text(end_formatted_time,
                         '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[3]/input')
        self.click(QUERY_BUTTON)

    @pytest.mark.p0
    def test_to_be_ship_order_query(self):
        self.online_parcel_login()

        self.type('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/span/input', "sxegc")
        self.assert_text('请输入数字',
                         '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div[2]/div')
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div/div[1]/button')  # 重置
        self.type('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/span/input', "1234")
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div/div[2]/button')
        # time.sleep(1)
        # self.assert_text('暂无数据', EMPTY_ELEMENT)

    @pytest.mark.p0
    def test_to_be_ship_add_new_sending_address(self):
        self.online_parcel_login()

        self.assert_text('新增发货地址', '//*[@id="address-select"]/button/span')
        self.click('//*[@id="address-select"]/button/span')

        self.switch_to_window(1)
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/address'), True)

    @pytest.mark.p0
    @pytest.mark.skip
    def test_to_be_ship_decrypt_address(self):
        self.online_parcel_login()
        if self.is_element_visible(EMPTY_ELEMENT):
            # self.assert_text('暂无数据', EMPTY_ELEMENT)
            print("无法进行地址解密操作")
        elif self.is_element_enabled(CHECK_BOX_ELEMENT):  # 如果可点击
            if self.is_exact_text_visible("新疆集运", NORTH_WEST_ORDER_FLAG_ELEMENT) or self.is_exact_text_visible(
                    "内蒙古集运", NORTH_WEST_ORDER_FLAG_ELEMENT):
                print("集运订单没有解密操作")
            else:
                self.click("//tbody/tr[2]/td[4]/div[1]/div[1]/div[2]/div[1]/span[1]/span[1]")  # 点击解密
                self.click('//*[@id="pro-form-wrapper"]/div[2]')  # 点击空白处
                time.sleep(1)
                phone_text = self.get_text_content("(//div[@class='kITt6RwaGO8Bry74MO0t'])[1]")
                assert "*" not in phone_text, "没有解密成功"
        else:
            self.hover_on_element("(//span[@class='kwaishop-agent-pc-micro-tooltip-disabled-compatible-wrapper'])[1]")
            time.sleep(1)
            disable_order = self.get_text_content("//div[contains(@class,'kwaishop-agent-pc-micro-tooltip-inner')]")
            assert disable_order in (
                "真宝仓订单暂不支持在线发货，请使用其他方式操作发货", "此订单含有赠品，暂不支持发货，请在小店后台自行寄出")

    @pytest.mark.p0
    def test_to_be_ship_place_order(self):
        self.online_parcel_login()

        self.click('//*[@id="place-order-button"]')
        self.assert_text('请先勾选表格中的订单，再点击按钮',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div[5]/div/div/div/div/div[2]')

        # 有些订单不支持在线寄件发货， 先不跑，没有数据，也不跑
        if self.is_element_enabled(CHECK_BOX_ELEMENT):
            self.click(CHECK_BOX_ELEMENT)

            self.click(PLACE_ORDER_BUTTON)  # 下单

            self.click(
                "//div[@class='F_p1P9flH5MfA9AqWNo6 WqpumMa0kijolusRUOLm']//div[@class='nRg0oDS738gtbsQ2fOOR']//div[1]")  # 选择圆通快递
            self.assert_element_present(
                "(//span[contains(text(),'预估运费')])[1]")  # 校验预估价格是否存在
            self.assert_element_present("(//div[@class='eICmz6HHtx8jMdy_DD3Q'])[1]")  # 地址存在
            self.assert_text('共1单',
                             "//body/div/div[@class='kwaishop-agent-pc-micro-modal-root']/div[@role='dialog']/div[@role='document']/div[@class='kwaishop-agent-pc-micro-modal-content']/div[@class='kwaishop-agent-pc-micro-modal-body']/div[@class='ynRrBGUHpc1MmCdAlzur']/div[@class='zXuRuklMmOT6KxTDrDpq']/span[1]")  # 校验共有几单
            self.assert_text('取件时请勿向快递员付款，前往支付列表支付',
                             "//div[@class='ynRrBGUHpc1MmCdAlzur']//div[@class='kwaishop-agent-pc-micro-alert-content']//div[1]")  # 校验是提示

            # 文档check
            self.check_document("(//a[contains(text(),'豁免权益')])[1]", '在线寄件商家超时揽收豁免权益说明')
            self.switch_to_window(0)
            self.check_document("(//div[@class='xqxKrBt9KvKHH0uohljN'])[1]", '禁寄物品目录-圆通-pc', 2)
            self.switch_to_window(0)
            self.check_document("(//a[contains(text(),'《在线寄件商家服务协议》及相关协议')])[1]",
                                '《在线寄件商家服务协议》及相关协议', 3)

    @pytest.mark.p0
    def test_to_be_collect_table(self):
        self.online_parcel_login()
        self.click('//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[2]')  # 待揽收

        self.assert_text('运单号',
                         "//th[contains(text(),'运单号')]")
        self.assert_text('商品信息',
                         "//th[contains(text(),'商品信息')]")
        self.assert_text('收货信息',
                         "//th[contains(text(),'收货信息')]")
        self.assert_text('快递公司',
                         "//th[contains(text(),'快递公司')]")
        self.assert_text('运单状态', "//th[contains(text(),'运单状态')]")
        self.assert_text('操作', "//th[@class='kwaishop-supply-shipping-center-component-pc-table-cell'][contains(text(),'操作')]")

    @pytest.mark.p0
    def test_to_be_collect_update_time_query(self):
        self.online_parcel_login()
        self.click(COLLECT_BUTTON)  # 待揽收页面

        self.assert_text('更新时间', "//label[contains(text(),'更新时间')]")
        # 更新日期选择
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(1)
        # 近60天
        self.click("//span[contains(text(),'近60天')]")
        self.click("//button[@type='submit']")  # 查询
        now_time = datetime.now()
        three_month_ago = now_time - timedelta(days=60)

        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = three_month_ago.strftime("%Y-%m-%d 23:59:59")
        self.assert_text(begin_formatted_time,
                         "//input[@placeholder='开始时间']")
        self.assert_text(end_formatted_time, "//input[@placeholder='结束时间']")

    @pytest.mark.p0
    def test_to_be_collect_waybill_query(self):
        self.online_parcel_login()
        self.click(COLLECT_BUTTON)  # 待揽收页面

        self.type("//input[@placeholder='请输入运单号']", "sxegc")
        self.click("//span[contains(text(),'重 置')]")  # 重置
        self.type("//input[@placeholder='请输入运单号']", "1234")
        self.click(QUERY_BUTTON)  # 查询
        # time.sleep(1)
        # self.assert_text('暂无数据', EMPTY_ELEMENT)

    @pytest.mark.p0
    def test_progressing_sub_menu(self):
        self.online_parcel_login()
        self.click('//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[3]')  # 进行中页面

        self.is_element_clickable("(//div[@class='v4u9xZzwcSoLw2tnFrlO'])[1]")
        self.click('//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div/div/div/div/div/div[2]')  # 点击已签收
        self.is_element_clickable("(//div[@class='v4u9xZzwcSoLw2tnFrlO hTwyg4qkFFGUOfT6Q0lc'])[1]")
        self.click('//*[@id="rc-tabs-0-panel-下单发货"]/div[2]/div/div/div/div/div/div[1]')  # 点击进行中

    @pytest.mark.p0
    def test_progressing_table(self):
        self.online_parcel_login()
        self.click('//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[3]')  # 进行中页面

        self.assert_text('运单号',
                         "//th[contains(text(),'运单号')]")
        self.assert_text('商品信息',
                         "//th[contains(text(),'商品信息')]")
        self.assert_text('收货信息',
                         "//th[contains(text(),'收货信息')]")
        self.assert_text('快递公司',
                         "//th[contains(text(),'快递公司')]")
        self.assert_text('运单状态', "//th[contains(text(),'运单状态')]")
        self.assert_text('操作', "//th[@class='kwaishop-supply-shipping-center-component-pc-table-cell'][contains(text(),'操作')]")

    @pytest.mark.p0
    def test_progressing_update_time_query(self):
        self.online_parcel_login()
        self.click(PROGRESS_BUTTON)  # 进行中页面

        self.assert_text('更新时间', "//label[contains(text(),'更新时间')]")
        # 更新日期选择
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(1)
        # 近30天
        self.click("//span[contains(text(),'近30天')]")
        self.click("//button[@type='submit']")  # 查询
        now_time = datetime.now()
        three_month_ago = now_time - timedelta(days=30)

        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = three_month_ago.strftime("%Y-%m-%d 23:59:59")
        self.assert_text(begin_formatted_time,
                         "//input[@placeholder='开始时间']")
        self.assert_text(end_formatted_time, "//input[@placeholder='结束时间']")

    @pytest.mark.p0
    def test_progressing_waybill_query(self):
        self.online_parcel_login()
        self.click(PROGRESS_BUTTON)  # 进行中页面

        self.type("//input[@placeholder='请输入运单号']", "sxegc")
        self.click("//span[contains(text(),'重 置')]")  # 重置
        self.type("//input[@placeholder='请输入运单号']", "1234")
        self.click(QUERY_BUTTON)  # 查询
        # time.sleep(1)
        # self.assert_text('暂无数据', EMPTY_ELEMENT)

    @pytest.mark.p0
    def test_exception_order_table(self):
        self.online_parcel_login()
        self.click('//*[@id="rc-tabs-0-panel-下单发货"]/div[1]/div/div/div/div/div/div[4]')  # 异常订单

        self.assert_text('运单号',
                         '//*[@id="rc-tabs-0-panel-下单发货"]/div[4]/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('商品信息',
                         "//th[contains(text(),'商品信息')]")
        self.assert_text('收货信息',
                         "//th[contains(text(),'收货信息')]")
        self.assert_text('快递公司',
                         "//th[contains(text(),'快递公司')]")
        self.assert_text('异常情况', "//th[contains(text(),'异常情况')]")
        self.assert_text('操作', "//th[@class='kwaishop-supply-shipping-center-component-pc-table-cell'][contains(text(),'操作')]")

    @pytest.mark.p0
    def test_exception_update_time_query(self):
        self.online_parcel_login()
        self.click(EXCEPTION_BUTTON)  # 异常订单

        self.assert_text('更新时间', "//label[contains(text(),'更新时间')]")
        # 更新日期选择
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(1)
        # 近7天
        self.click("//span[contains(text(),'近7天')]")
        self.click("//button[@type='submit']")  # 查询
        now_time = datetime.now()
        three_month_ago = now_time - timedelta(days=7)

        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = three_month_ago.strftime("%Y-%m-%d 23:59:59")
        self.assert_text(begin_formatted_time,
                         "//input[@placeholder='开始时间']")
        self.assert_text(end_formatted_time, "//input[@placeholder='结束时间']")

    @pytest.mark.p0
    def test_exception_waybill_query(self):
        self.online_parcel_login()
        self.click(EXCEPTION_BUTTON)  # 异常订单

        self.driver.maximize_window()
        time.sleep(2)
        self.type("//input[@placeholder='请输入运单号']", "sxegc")
        self.click("//span[contains(text(),'重 置')]")  # 重置
        self.type("//input[@placeholder='请输入运单号']", "1234")
        self.click(QUERY_BUTTON)  # 查询

        # time.sleep(1)
        # self.assert_text('暂无数据', EMPTY_ELEMENT)

    @pytest.mark.p1
    def test_to_be_deliver_receiver_phone(self):
        self.online_parcel_login()

        self.driver.maximize_window()
        time.sleep(2)
        self.type(
            '//*[@id="pro-form-wrapper"]/div[3]/div[1]/div/div[2]/div/div/span/input',
            "192")
        self.click(QUERY_BUTTON)  # 查询
        self.assert_text('暂无数据', EMPTY_ELEMENT)

    @pytest.mark.p1
    def test_to_be_deliver_destination(self):
        self.online_parcel_login()
        self.driver.maximize_window()

        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div/div/div')  # 点击目的省市
        self.click(
            '//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div/div/ul[1]/li[1]/div[1]')  # 选中北京市
        # self.click(
        #     '//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div/div/ul[1]/li[1]/div[2]/span/svg')  # 选中小箭头展开二级地址
        #
        # self.click('//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div/div[1]/div')  # 点击目的省市
        # self.click(
        #     '//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div/div/ul[1]/li[3]/span/span')  # 选中河北省
        #
        # # 检查是否有这上面选中的两个省份
        # self.assert_text("北京市", "//span[@class='kwaishop-agent-pc-micro-select-selection-item-content']")
        # self.assert_text("河北省",
        #                  "//span[contains(@class,'kwaishop-agent-pc-micro-select-selection-item-content')][contains(text(),'河北省')]")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_to_be_remained_time(self):
        self.online_parcel_login()
        self.driver.maximize_window()

        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[3]/div')  # 点击查看剩余时效
        self.click("//span[@title='全部']")

        self.assert_text("全部", "//span[@title='全部']")
        self.assert_text("12h内需发货", "//div[@title='12h内需发货']")
        self.assert_text("24h内需发货", "//div[@title='24h内需发货']")
        self.assert_text("48h内需发货", "//div[@title='48h内需发货']")
        self.assert_text("72h内需发货", "//div[@title='72h内需发货']")
        self.assert_text("超时发货", "//div[@title='超时发货']")

        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div[2]/div/div/div')

        self.assert_text("12h内需发货", "//div[@title='12h内需发货']")
    @pytest.mark.p1
    def test_merge_order(self):
        self.online_parcel_login()
        self.driver.maximize_window()

        self.click('//*[@id="merge-order-button"]/button')
        self.assert_text("仅展示可合并的订单列表（搜索条件暂不可用）", '//*[@id="merge-order-button"]/span[2]')

        if self.is_element_enabled(
                "//div[contains(@class,'kwaishop-agent-pc-micro-pro-list-container-column-content')]//input[contains(@type,'checkbox')]"):
            self.click("(//div[contains(@class,'zdDot7uUfxClPlQX82Sz')])[1]")
            self.assert_element_present(
                "(//div[contains(@class,'kwaishop-agent-pc-micro-pro-list-row-footer mooJY45LPtCwUTnb63gf')])[1]")

        self.click('//*[@id="place-order-button"]')

    @pytest.mark.skip
    def test_deliver_address(self):
        self.online_parcel_login()
        self.driver.maximize_window()

        element = self.find_element(
            "//div[contains(@class,'MO7mY8zHpiO6mLYx4RdC')]//div[contains(@class,'kwaishop-agent-pc-micro-select-selector')]//div[1]")  # 发货地址
        text_before = element.text

        self.click(
            "//div[contains(@class,'MO7mY8zHpiO6mLYx4RdC')]//div[contains(@class,'kwaishop-agent-pc-micro-select-selector')]//div[1]")
        self.click(
            "(//div[contains(@class,'kwaishop-agent-pc-micro-select-item kwaishop-agent-pc-micro-select-item-option')])[2]")
        element = self.find_element(
            "//div[contains(@class,'MO7mY8zHpiO6mLYx4RdC')]//div[contains(@class,'kwaishop-agent-pc-micro-select-selector')]//div[1]")
        text_now = element.text

        self.assert_not_equal(text_before, text_now, "之前和现在发货地址应该不一样")
