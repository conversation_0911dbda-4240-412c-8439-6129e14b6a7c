import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase


@ddt
class TestOnlineParcelPayOrder(BaseTestCase):
    """
       在线寄件-支付运费
       登陆PC小店-进行入在线寄件
       1. 待支付
       2. 已支付
       """

    def online_parcel_pay_tab(self):
        self.online_parcel_login()

        # 获取并校验url
        self.click("//span[@id='pay-tab']")
        time.sleep(0.5)

    @pytest.mark.p0
    def test_to_be_pay_update_time_query(self):
        self.online_parcel_pay_tab()

        self.click('//*[@id="rc-tabs-0-tab-支付运费"]')

        self.assert_text('更新时间', "//label[contains(text(),'更新时间')]")
        # 更新日期选择
        self.click("//input[@placeholder='开始时间']")
        self.sleep(1)
        # 近60天
        self.click("//span[contains(text(),'近60天')]")
        self.click("//button[@type='submit']")  # 查询
        now_time = datetime.now()
        three_month_ago = now_time - timedelta(days=60)

        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = three_month_ago.strftime("%Y-%m-%d 23:59:59")
        # self.assert_text(begin_formatted_time,
        #                  "//input[@placeholder='开始日期']")
        # self.assert_text(end_formatted_time, "//input[@placeholder='结束日期']")
        self.click("//button[@class='kwaishop-supply-shipping-center-component-pc-btn']")  # 重置按钮

    @pytest.mark.p0
    def test_to_be_pay_table(self):
        self.online_parcel_pay_tab()
        self.assert_text('批次号', "//th[contains(text(),'批次号')]")
        self.assert_text('发货地址', "//th[contains(text(),'发货地址')]")
        self.assert_text('快递公司', "//th[contains(text(),'快递公司')]")
        self.assert_text('运单数量', "//th[contains(text(),'运单数量')]")
        self.assert_text('应付运费(元)', "//th[contains(text(),'应付运费(元)')]")
        self.assert_text('操作', "//th[@class='kwaishop-supply-shipping-center-component-pc-table-cell'][contains(text(),'操作')]")

    @pytest.mark.p0
    def test_paid_update_time_query(self):
        self.online_parcel_pay_tab()

        self.click('//*[@id="rc-tabs-0-panel-支付运费"]/div[1]/div/div/div/div/div/div[2]')  # 选择已支付

        self.assert_text('更新时间', "//label[contains(text(),'更新时间')]")
        # 更新日期选择
        self.click("//input[@placeholder='开始时间']")
        self.sleep(1)
        # 近60天
        self.click("//span[contains(text(),'近60天')]")
        self.click("//span[contains(text(),'查 询')]")  # 查询
        now_time = datetime.now()
        three_month_ago = now_time - timedelta(days=60)

        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = three_month_ago.strftime("%Y-%m-%d 23:59:59")
        # self.assert_text(begin_formatted_time,
        #                  "//input[@placeholder='开始日期']")
        # self.assert_text(end_formatted_time, "//input[@placeholder='结束日期']")

        self.click("//span[contains(text(),'重 置')]")  # 重置按钮

    @pytest.mark.p0
    def test_paid_table(self):
        self.online_parcel_pay_tab()

        self.click('//*[@id="rc-tabs-0-panel-支付运费"]/div[1]/div/div/div/div/div/div[2]')  # 选择已支付
        self.assert_text('批次号', "//th[contains(text(),'批次号')]")
        self.assert_text('发货地址', "//th[contains(text(),'发货地址')]")
        self.assert_text('快递公司', "//th[contains(text(),'快递公司')]")
        self.assert_text('运单数量', "//th[contains(text(),'运单数量')]")
        self.assert_text('应付运费(元)', "//th[contains(text(),'应付运费(元)')]")
        self.assert_text('支付时间', "//th[contains(text(),'支付时间')]")
        self.assert_text('操作', "//th[@class='kwaishop-supply-shipping-center-component-pc-table-cell'][contains(text(),'操作')]")
