import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase
@ddt
class TestOnlineParcelHistoryShippingRecord(BaseTestCase):
    """
       在线寄件-历史发货记录
       登陆PC小店-进行入在线寄件
       """
    def online_parcel_history_shipping_record_tab(self):
        self.online_parcel_login()
        # 获取并校验url
        self.click("//span[@id='history-tab']")
        time.sleep(0.5)

    @pytest.mark.p0
    def test_history_shipping_record_create_time_query(self):
        self.online_parcel_history_shipping_record_tab()

        self.assert_text("创建时间","//label[contains(text(),'创建时间')]")
        #创建日期选择
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div')

        self.sleep(1)
        #近30天
        self.click("//span[contains(text(),'近30天')]")
        self.click("//span[contains(text(),'查 询')]")

        now_time =datetime.now()
        one_month_ago = now_time-timedelta(days=30)
        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = one_month_ago.strftime("%Y-%m-%d 23:59:59")
        self.assert_text(begin_formatted_time,"//input[@placeholder='开始日期']")
        self.assert_text(end_formatted_time,"//input[@placeholder='结束日期']")
        #重置按钮
        self.click("//div[@class='kwaishop-supply-shipping-center-component-pc-space kwaishop-supply-shipping-center-component-pc-space-horizontal kwaishop-supply-shipping-center-component-pc-space-align-center']//button[@type='button']")

    @pytest.mark.p0
    def test_history_shipping_record_table(self):
        self.online_parcel_history_shipping_record_tab()
        self.assert_text("批次号","//th[contains(text(),'批次号')]")
        self.assert_text("发货地址","//th[contains(text(),'发货地址')]")
        self.assert_text("快递公司","//th[contains(text(),'快递公司')]")
        self.assert_text("运单数量","//th[contains(text(),'运单数量')]")
        self.assert_text("应付运费","//th[contains(text(),'应付运费')]")
        self.assert_text("生成日期","//th[contains(text(),'生成日期')]")
        self.assert_text("操作","//th[@class='kwaishop-supply-shipping-center-component-pc-table-cell'][contains(text(),'操作')]")


    @pytest.mark.p1
    def test_reverse_order_of_creat_time(self):
        self.online_parcel_history_shipping_record_tab()
        self.click('//*[@id="rc-tabs-0-panel-历史发货记录"]/div/div[2]/div[1]/div/div[2]/div/div[3]')
        self.assert_text("按创建时间降序","//span[@title='按创建时间降序']")