import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestOwnerPlatform(BaseTestCase):

    @pytest.mark.p0
    def test_trade_page(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        self.sleep(3)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/fangzhou/home'), True)
        # 点击开始使用
        self.click('//*[@id="u_E6A21B75"]/div/div/button')
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/express/inventoryManage'), True)

    @pytest.mark.p0
    def test_trade_page_panel(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击货主管理
        self.click('//*[@id="menu-supply-info"]')
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/fangzhou/information'), True)
        self.assert_text('货主名称','//*[@id="u_09BCF61C"]/div/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[1]')
        self.assert_text('货主ID','//*[@id="u_09BCF61C"]/div/div[2]/div/div/div/div/div/table/tbody/tr[3]/td[1]')
        self.assert_text('货主类型','//*[@id="u_09BCF61C"]/div/div[2]/div/div/div/div/div/table/tbody/tr[4]/td[1]')
        self.assert_text('贸易方式','//*[@id="u_09BCF61C"]/div/div[2]/div/div/div/div/div/table/tbody/tr[5]/td[1]')
        self.assert_text('联系电话','//*[@id="u_09BCF61C"]/div/div[2]/div/div/div/div/div/table/tbody/tr[6]/td[1]')
        self.assert_text('电子邮箱','//*[@id="u_09BCF61C"]/div/div[2]/div/div/div/div/div/table/tbody/tr[7]/td[1]')

    @pytest.mark.p0
    def test_shop_relation(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击供销关系管理
        self.click('//*[@id="menu-supply-seller-relation"]')
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/fangzhou/shop-relation-list'), True)
        self.assert_text('分销店铺ID','//*[@id="u_F1C4C88F"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('分销店铺名称','//*[@id="u_F1C4C88F"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('供货类型','//*[@id="u_F1C4C88F"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('状态','//*[@id="u_F1C4C88F"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('添加时间','//*[@id="u_F1C4C88F"]/div/div[2]/div/div/div/div/div/table/thead/tr/th[5]')

    @pytest.mark.p0
    def test_goods_manage(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击货品管理
        self.click('//*[@id="node_menu_for_intersectionObserver_3"]/ul/li')
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/express/goodsManage'), True)
        self.assert_text('货品ID','//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[1]/div[1]/div/div[1]')
        # 查询某一货品ID
        self.type('//*[@id="wareCode"]','***********')
        self.click('//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[2]/button[2]')
        self.assert_text('***********','//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr/td[1]')
        # 清空筛选条件
        self.click('//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[2]/button[1]')
        # 查询某一货品名称
        self.assert_text('货品名称','//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[1]/div[2]/div/div[1]')
        self.type('//*[@id="wareName"]','test009')
        self.click('//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[2]/button[2]')
        self.assert_text('test009','//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr/td[2]')
        # 清空筛选条件
        self.click('//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[2]/button[1]')
        # 查询某一货品外部编码
        self.assert_text('货品外部编码','//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[1]/div[3]/div/div[1]')
        self.type('//*[@id="wareOutCode"]','***************')
        self.click('//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[2]/button[2]')
        self.assert_text('***************','//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr/td[3]')
        # 清空筛选条件
        self.click('//*[@id="root"]/section/main/div/div/div/div[1]/div/div/div/div[1]/div/form/div[2]/button[1]')

    @pytest.mark.p0
    def test_goods_manage_panel(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击货品管理
        self.click('//*[@id="node_menu_for_intersectionObserver_3"]/ul/li')
        self.assert_text('货品ID','//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('货品名称', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('货品外部编码', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('条形码', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('操作人', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('操作时间', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('操作', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[7]')
        # 货品列表-操作
        self.assert_text('查看','//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/button[1]')
        # 编辑按钮
        self.assert_text('编辑', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/button[2]')
        self.assert_text('删除', '//*[@id="root"]/section/main/div/div/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/button[3]')

    @pytest.mark.p0
    def test_warehouse_manage(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击仓库管理
        self.click('//*[@id="node_menu_for_intersectionObserver_4"]/ul/li')
        self.sleep(3)
        self.assert_text('仓库编码','//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/form/div[1]/div[1]/label')
        self.assert_text('仓库名称','//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/form/div[2]/div[1]/label')
        #  根据仓库编码查询
        self.type('//*[@id="warehouseCode"]','WH11461272')
        self.click('//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/form/div[3]/button[2]')
        self.assert_text('WH11461272','//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/tbody/tr/td[1]')
        # 重置按钮
        self.click('//*[@id="root"]/section/main/div/div/div[1]/div[2]/div/form/div[3]/button[1]')

    @pytest.mark.p0
    def test_warehouse_manage_pannel(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击仓库管理
        self.click('//*[@id="node_menu_for_intersectionObserver_4"]/ul/li')
        self.assert_text('仓库编码','//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[1]/span/div/span[1]')
        self.assert_text('仓库名称', '//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[2]/span/div/span[1]')
        self.assert_text('别名', '//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[3]/span/div/span[1]')
        self.assert_text('地址', '//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[4]/span/div/span[1]')
        self.assert_text('操作', '//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[5]/span/div/span[1]')
        # 点击覆盖范围
        self.click('//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/tbody/tr[1]/td[5]/button[1]')
        self.click("//span[@class='ant-modal-close-x']")
        self.sleep(3)
        # 操作按钮
        self.assert_text('编辑','//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/tbody/tr[1]/td[5]/button[2]')
        self.assert_text('删除', '//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/div/div/div/table/tbody/tr[1]/td[5]/button[3]')

    @pytest.mark.p0
    def test_inventory_manage(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击库存管理
        self.click('//*[@id="node_menu_for_intersectionObserver_5"]/ul/li[1]')
        self.assert_text('货品外部编码','//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/form/div[1]/div[1]/label')
        self.assert_text('货品ID', '//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/form/div[2]/div[1]')
        self.assert_text('货品名称', '//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/form/div[3]/div[1]')
        self.type('//*[@id="wareOutCode"]','**************')
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/form/div[4]/button[2]')
        self.assert_text('**************','//*[@id="root"]/section/main/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr/td[3]')
        self.click('//*[@id="root"]/section/main/div/div[1]/div/div[2]/div/form/div[4]/button[1]')
        self.assert_text('调整库存','//*[@id="root"]/section/main/div/div[2]/div/div[2]/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/button')

    @pytest.mark.p0
    def test_inventory_setting(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击库存分配设置
        self.click('//*[@id="node_menu_for_intersectionObserver_5"]/ul/li[2]/span/span')
        # 全局策略
        self.click('//*[@id="root"]/div/div/div[2]/div/div/div[2]/button')
        self.assert_text('选择',"//tbody/tr[1]/td[3]/button[1]/span[1]")
        self.assert_text('查看详情',"//tbody/tr[1]/td[3]/a[1]/button[1]")
        self.click("//tbody/tr[1]/td[3]/a[1]/button[1]")
        # 编辑库存管理
        self.click('//*[@id="root"]/div/div/div[2]/div[2]/div/div[7]/a/button')
        self.click('//*[@id="root"]/div/div/div[2]/div[2]/div/form/div[4]/div/div[2]/div/div/div/div/div')
        self.click('//*[@id="root"]/div/div/div[2]/div[2]/div/form/div[7]/div/div[2]/div/div/div/div/div')

    @pytest.mark.p0
    def test_inventory_setting_panel(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 点击库存分配设置
        self.click('//*[@id="node_menu_for_intersectionObserver_5"]/ul/li[2]/span/span')
        self.type('//*[@id="wareCode"]','***********')
        self.click('//*[@id="root"]/div/div/div[3]/div/div[2]/form/div[2]/div/div/div/div/div/button[1]')
        self.click('//*[@id="root"]/div/div/div[3]/div/div[2]/form/div[2]/div/div/div/div/div/button[2]')
        # 选择 通用策略
        self.assert_text('批量绑定','//*[@id="root"]/div/div/div[3]/div/div[3]/div[1]/button[1]')
        self.assert_text('批量解绑','//*[@id="root"]/div/div/div[3]/div/div[3]/div[1]/button[2]')
        self.assert_text('绑定','//*[@id="root"]/div/div/div[3]/div/div[4]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/button[1]')
        self.assert_text('定制','//*[@id="root"]/div/div/div[3]/div/div[4]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/button[2]')