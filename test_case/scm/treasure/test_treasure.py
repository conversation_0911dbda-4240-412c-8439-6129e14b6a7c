import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase


@ddt
class TestTreasure(BaseTestCase):

    @pytest.mark.p0
    def test_trade_page(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supplychain/treasure'), True)

    @pytest.mark.p0
    def test_trade_checklist(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.assert_text('订单编号', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[1]/div/div[1]/label')
        self.assert_text('订单码', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[2]/div/div[1]/label')
        self.assert_text('履约状态', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[3]/div/div[1]/label')
        self.assert_text('快递单号', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[4]/div/div[1]/label')
        self.assert_text('入库时间', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[5]/div/div[1]/label')
        self.assert_text('付款时间', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[6]/div/div[1]/label')
        self.assert_text('商品ID', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[7]/div/div[1]/label')
        self.assert_text('商品名称', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[8]/div/div[1]/label')
        self.assert_text('买家ID', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[9]/div/div[1]/label')
        self.assert_text('异常原因', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[10]/div/div[1]/label')
        self.assert_text('是否可选质检', '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[11]/div/div[1]/label')

    @pytest.mark.p0
    def test_trade_list_btn(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        # 订单编号填入
        self.type("//input[@id='tradeOrderCode']", '测试模板9811')
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[12]/div/div[1]/button')
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[12]/div/div[2]/button')
        # 履约状态
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[3]/div/div[2]/div/div/div')
        # 点击已取消
        self.click(
            '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[3]/div/div[2]/div/div/div/div[2]/div/div/div/div[2]/div[1]/div/div/div[7]/div')
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[12]/div/div[1]/button')
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[12]/div/div[2]/button')
        # 异常原因
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[10]/div/div[2]/div/div/div')
        self.click(
            '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[10]/div/div[2]/div/div/div/div[2]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        # 是否可选质检
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[11]/div/div[2]/div/div/div')
        self.click(
            '//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[11]/div/div[2]/div/div/div/div[2]/div/div/div/div[2]/div[1]/div/div/div[1]/div')
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/form/div/div[12]/div/div[1]/button')

    @pytest.mark.p0
    def test_btn_checklist(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        # 真宝仓信息-按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[1]/div/div[1]/button')
        self.click("//div[@class='buttonWrapper___QMrVC']//button[@type='button']")
        # 打印订单码-按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[1]/div/div[2]/button')
        self.click(
            "//div[@class='kwaishop-supply-express-micro-modal']//span[@class='kwaishop-supply-express-micro-modal-close-x']")
        self.assert_text('快捷绑码', '//*[@id="root"]/div[1]/div[2]/div/div[1]/div/div[3]/button')
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[1]/div/div[4]/button')
        self.click("//button[@class='kwaishop-supply-express-micro-drawer-close']")

    @pytest.mark.p0
    def test_panel_checklist(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        self.assert_text('商品信息',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('订单编号',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('订单码',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('买家',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('履约状态',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('退回原因',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('原因描述',
                         '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[7]')

    @pytest.mark.p0
    def test_lock_trade(self):
        self.login('TREASURE_DAMAIN', 'supply_account_template')
        self.maximize_window()
        # 点击 绑定订单码
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[11]/div')
        self.assert_text('绑定订单码', "//label[contains(text(),'绑定订单码')]")
        self.assert_text('是否合单', "//label[contains(text(),'是否合单')]")
        self.assert_text('指定快递', "//label[contains(text(),'指定快递')]")
        # 取消按钮
        # self.assert_text('确定',"(//button[@type='button'])[30]")
        self.click(
            "//div[@class='kwaishop-supply-express-micro-drawer kwaishop-supply-express-micro-drawer-right kwaishop-supply-express-micro-drawer-open']//div[@class='kwaishop-supply-express-micro-space kwaishop-supply-express-micro-space-horizontal kwaishop-supply-express-micro-space-align-center']//div[1]//button[1]")
