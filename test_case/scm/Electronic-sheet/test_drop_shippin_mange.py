from test_case.core_link.supply_chain.facesheet_case.Handwork_case import *

@ddt
class TestDropSheet(Handwork):


    def test_drop_shipping(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/electronic-sheet/drop-shipping-manage'), True)

        self.assert_text('绑定新厂家', '//*[@id="root"]/div/div[3]/div/div[2]/div[1]/div[3]/button/span[2]')
        self.assert_text('使用教程', '//*[@id="root"]/div/div[3]/div/div[1]/button')

        self.assert_text('厂家ID',"//th[contains(text(),'厂家ID')]")
        self.assert_text('厂家名称',"//th[contains(text(),'厂家名称')]")
        self.assert_text('商家联系信息',"//th[contains(text(),'商家联系信息')]")
        self.assert_text('申请描述',"//th[contains(text(),'申请描述')]")
        self.assert_text('申请绑定时间',"//th[contains(text(),'申请绑定时间')]")
        self.assert_text('操作',"//th[contains(text(),'操作')]")
        self.assert_text('取消申请绑定',"//span[@class='link___MxLf2']")

    def test_drop_shipping_locked(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 已绑定
        self.click('//*[@id="root"]/div/div[3]/div/div[2]/div[1]/div[1]/div/div[2]')
        self.assert_text('厂家ID', "//th[contains(text(),'厂家ID')]")
        self.assert_text('厂家名称', "//th[contains(text(),'厂家名称')]")
        self.assert_text('商家联系信息', "//th[contains(text(),'商家联系信息')]")
        self.assert_text('申请绑定时间', "//th[contains(text(),'申请绑定时间')]")
        self.assert_text('绑定时间', "//th[contains(text(),'绑定时间')]")
        self.assert_text('操作', "//th[contains(text(),'操作')]")
        self.assert_text('解除绑定',"//body[1]/div[1]/section[1]/section[1]/main[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[6]/div[1]/div[1]/span[1]")

    def test_drop_shipping_deny(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 已拒绝
        self.click('//*[@id="root"]/div/div[3]/div/div[2]/div[1]/div[1]/div/div[3]')
        self.assert_text('厂家ID', "//th[contains(text(),'厂家ID')]")
        self.assert_text('厂家名称', "//th[contains(text(),'厂家名称')]")
        self.assert_text('商家联系信息', "//th[contains(text(),'商家联系信息')]")
        self.assert_text('申请绑定时间', "//th[contains(text(),'申请绑定时间')]")
        self.assert_text('拒绝时间', "//th[contains(text(),'拒绝时间')]")
        self.assert_text('拒绝原因', "//th[contains(text(),'拒绝原因')]")
        self.assert_text('操作', "//th[contains(text(),'操作')]")

    def test_drop_shipping_unlock(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 已解绑
        self.click('//*[@id="root"]/div/div[3]/div/div[2]/div[1]/div[1]/div/div[4]')
        self.assert_text('厂家ID', "//th[contains(text(),'厂家ID')]")
        self.assert_text('厂家名称', "//th[contains(text(),'厂家名称')]")
        self.assert_text('商家联系信息', "//th[contains(text(),'商家联系信息')]")
        self.assert_text('申请绑定时间', "//th[contains(text(),'申请绑定时间')]")
        self.assert_text('解绑时间', "//th[contains(text(),'解绑时间')]")
        self.assert_text('解绑', "//th[contains(text(),'解绑方')]")
        self.assert_text('解绑原因', "//th[contains(text(),'解绑原因')]")
        self.assert_text('操作', "//th[contains(text(),'操作')]")

    def test_drop_shipping_order(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 代发订单
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[2]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/electronic-sheet/drop-shipping-order'), True)
        # 点击查询时间
        self.click('//*[@id="filter"]/div[4]/div[2]/div/div[2]/div/div/div/div[1]/input')
        # 点击近30天
        self.click("//span[contains(text(),'近30天')]")
        # 点击 查询
        self.click('//*[@id="filter"]/div[6]/div/button[1]')
        self.assert_text('代发订单号','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('订单编号','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('收货信息','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('商品信息','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('订单状态','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('代发状态','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[7]')
        self.assert_text('商品数量','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[8]')
        self.assert_text('买家备注','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[9]')
        self.assert_text('操作','//*[@id="root"]/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[16]')

        self.assert_text('批量分配','//*[@id="root"]/div[2]/div/div[1]/div/button[1]')
        self.assert_text('批量取消分配', '//*[@id="root"]/div[2]/div/div[1]/div/button[2]')
        self.assert_text('导出明细', '//*[@id="root"]/div[2]/div/div[1]/button')

    def test_drop_shipping_order_list(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 代发订单
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[2]')
        self.click('//*[@id="filter_quickFilter"]/span[1]')
        self.click('//*[@id="filter_quickFilter"]/span[2]')
        self.click('//*[@id="filter_quickFilter"]/span[3]')
        self.click('//*[@id="filter_quickFilter"]/span[4]')

    def test_drop_shipping_order_handwork(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 代发订单
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[2]')
        self.sleep(2)
        # 点击手工订单
        self.click('//*[@id="root"]/div[1]/div/div/div[1]/div[1]/div/div[2]')
        self.assert_text('批量导入','//*[@id="root"]/div[2]/div/div[1]/div[1]/div[1]/button')
        self.assert_text('手工录入','//*[@id="root"]/div[2]/div/div[1]/div[1]/div[2]/button')
        self.assert_text('批量分配','//*[@id="root"]/div[2]/div/div[1]/div[1]/div[3]/button')
        self.assert_text('批量取消分配','//*[@id="root"]/div[2]/div/div[1]/div[1]/div[4]/button')
        self.assert_text('批量删除','//*[@id="root"]/div[2]/div/div[1]/div[1]/div[5]/button')
        self.assert_text('批量修改商品信息','//*[@id="root"]/div[2]/div/div[1]/div[1]/div[6]/button')

    def test_drop_shipping_stratery(self):
        self.sheet()
        # 点击 代发管理
        self.click('//*[@id="main_root"]/section/header/div[1]/a[2]')
        # 点击 代发策略管理
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[3]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/electronic-sheet/drop-shipping-strategy'), True)
        self.assert_text('新增代发策略', '//*[@id="root"]/div/div/div[2]/div[1]/button')
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/button')
        # 点击新增代发策略
        self.switch_to_window(1)
        self.sleep(2)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/electronic-sheet/drop-shipping-strategy/multiple-detail'), True)
        self.switch_to_window(0)
