import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestDoorInstall(BaseTestCase):
    """
       物流服务-上门安装服务
       登陆PC小店-进行入物流服务
       """
    # 生鲜到家服务页面
    @pytest.mark.p0
    def test_door_install_shippingest_door_install_shipping(self):
        self.express_door_install_shipping_login()
        self.maximize_window()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/door-install'), True)
        # 查看使用指南
        self.assert_text('使用指南', '//*[@id="root"]/div[2]/div/div[2]/div/div[1]/button')
        self.check_document('//*[@id="root"]/div[2]/div/div[2]/div/div[1]/button', '【上门安装】商家操作手册 - 轻雀文档')
        self.switch_to_window(0)
        self.assert_text('关闭服务', '//*[@id="root"]/div[2]/div/div[2]/div/div[2]/button')
        self.assert_text('商品id','//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[1]')
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/input','23024929926880')
        self.assert_text('商品标题','//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[1]')
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/span/input','测试商品')
        self.assert_text('类目名称','//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[1]')
        # 选择类目
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div/div/div')
        self.assert_text('商品价格','//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/label')
        # 选择商品价格
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div')
        # 选择 0-10 元
        # self.click('/html/body/div[10]/div/div/div/div[2]/div[1]/div/div/div[1]/div')
        # 点击查询
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button')
        # 点击重置
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[1]/button')

    def test_door_install_uninstall_panel(self):
        self.express_door_install_shipping_login()
        self.maximize_window()
        self.assert_text('商品信息','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('上门安装状态','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('操作','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('支持上门安装','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div/button')
        # 点击支持上门安装
        # self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div/button')
        # self.assert_text('确认支持',"//button[@class='kwaishop-supply-logistics-pc-btn kwaishop-supply-logistics-pc-btn-primary']")
        # self.assert_text('取消','/html/body/div[4]/div/div[2]/div/div[2]/div/div/div[2]/button[1]')
        # 点击取消
        # self.click('/html/body/div[6]/div/div[2]/div/div[2]/div/div/div[2]/button[1]')
        # 全选
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span')
        self.assert_text('批量支持上门安装','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div/div[2]/div/button')

    def test_door_install_installed_panel(self):
        self.express_door_install_shipping_login()
        self.maximize_window()
        # 切换已支持上门安装商品
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.assert_text('商品id', '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[1]')
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/input', '23024929926880')
        self.assert_text('商品标题', '//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[1]')
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/span/input', '测试商品')
        self.assert_text('类目名称', '//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[1]')
        # 选择类目
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div/div/div')
        self.assert_text('商品价格', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/label')
        # 选择商品价格
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div')
        # 选择 0-10 元
        # self.click('/html/body/div[4]/div/div/div/div[2]/div[1]/div/div/div[1]/div')
        # 点击查询
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button')
        # 点击重置滴
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[1]/button')
        self.assert_text('商品信息','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('上门安装状态','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('操作','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        # self.assert_text('解除上门安装','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div/button')
        # 全选
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span')
        # self.assert_text('批量解除上门安装','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div/div[2]/div/button')

    def test_door_all_selled_panel(self):
        self.express_door_install_shipping_login()
        self.maximize_window()
        # 切换全部在售商品
        self.click('//*[@id="rc-tabs-0-tab-1"]')
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span')
        self.assert_text('批量支持上门安装','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div/div[2]/div[1]/button')
        self.assert_text('批量解除上门安装','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div/div[2]/div[2]/button')
        self.assert_text('商品信息','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('上门安装状态','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('操作','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')

