

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestFreshHome(BaseTestCase):
    """
       物流服务-生鲜到家服务
       登陆PC小店-进行入物流服务
       """
    # 生鲜到家服务页面
    @pytest.mark.p0
    def test_fresh_home_shipping(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/xdj-service'), True)
        # 查看操作手册
        self.assert_text('操作手册', '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[1]/button')
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[1]/button')
        self.switch_to_window(0)
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/xdj-service'), True)
        # 查看服务介绍
        self.assert_text('服务介绍', '//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[2]/button')
        # 唤起半屏
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[2]/button')
        # 关闭半屏
        self.click("//div[@class='kwaishop-supply-logistics-pc-drawer-footer']")
        # 关闭服务
        self.assert_text('关闭服务','//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[3]/button')

    def test_service_manage_table(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        self.assert_text('单号查询','//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[1]')
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/div')
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/textarea','12767124687692141124')
        self.assert_text('创建时间','//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[1]')
        self.assert_text('快递公司','//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]')
        self.assert_text('违规发货','//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[1]')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[1]/button')

    def test_service_manage_panel(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        self.assert_text('服务单号','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('订单编号','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('物流信息','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('物流状态','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        # self.click('//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div/div')
        self.assert_text('创建时间','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('结算方式','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('违规发货','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[7]')
        self.assert_text('送货上门结果','//*[@id="rc-tabs-0-panel-ServiceOrderManagement"]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[8]')

    def test_delivery_template(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        # 点击配送模板
        self.click('//*[@id="root"]/div[1]/div[3]/div/div[1]/div[1]/div/div[2]')
        # 点击新增配送区域
        self.click('//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[1]/div/div/div[2]/div/div[2]/div/div/button')
        self.assert_text('模板名称',"//label[contains(text(),'模板名称')]")
        self.assert_text('配送区域',"//label[contains(text(),'配送区域')]")
        self.assert_text('关联商品',"//label[contains(text(),'关联商品')]")
        # 点击 添加商品
        self.click("//span[contains(text(),'添加商品')]")
        #点击 退出
        self.click("//span[@class='kwaishop-supply-logistics-pc-modal-close-x']")
        # self.assert_text('确认',"//span[contains(text(),'确 认')]")
        # self.assert_text('取消', "//div[@class='Tz584ailQBKCPSCWG5Ml']//div[1]//button[1]")
        # self.click("//div[@class='Tz584ailQBKCPSCWG5Ml']//div[1]//button[1]")
        self.click("//button[@aria-label='Close']//span[@aria-label='system-close-medium-line']//*[name()='svg']")

    def test_delivery_template_planel(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        # 点击配送模板
        self.click('//*[@id="root"]/div[1]/div[3]/div/div[1]/div[1]/div/div[2]')
        self.assert_text('模板名称','//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('收货区域','//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        # 点击收货区域详情
        # self.click('//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/a')
        # self.click("//span[@class='kwaishop-supply-logistics-pc-modal-close-x']")
        self.assert_text('已关联商品数','//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('最后修改时间','//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('操作','//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        # 查看商品按钮
        self.assert_text('查看商品',
            '//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/div[2]/button')
        # 删除按钮
        self.assert_text('删除',
                         '//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/div[3]/button')
        # 编辑按钮
        self.click('//*[@id="rc-tabs-0-panel-DeliveryTemplate"]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/div[1]/button')


    def test_delivery_manage(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        # 点击物流商管理
        self.click('//*[@id="rc-tabs-0-tab-LogisticsProviderManagement"]')
        # 已订阅记录查看
        self.click('//*[@id="rc-tabs-0-panel-LogisticsProviderManagement"]/div/div[2]/div[1]/div[3]/div[2]/span/span[2]')
        # 查看订阅
        self.assert_text('查看订阅','//*[@id="rc-tabs-0-panel-LogisticsProviderManagement"]/div/div[3]/div/div/div/div/div/table/tbody/tr[1]/td[4]/div')
        # 新增合作网点按钮
        self.assert_text('新增合作网点','//*[@id="rc-tabs-0-panel-LogisticsProviderManagement"]/div/div[2]/div[2]/div/div[1]/button')
        self.assert_text('停用服务','//*[@id="rc-tabs-0-panel-LogisticsProviderManagement"]/div/div[2]/div[2]/div/div[2]/button')

    def test_logistics_service_work(self):
        self.express_fresh_home_shipping_login()
        self.maximize_window()
        # 点击物流服务工单
        self.click('//*[@id="root"]/div[1]/div[3]/div/div[1]/div[1]/div/div[4]')
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/work-order?logisticsServiceCode=8'),True)
