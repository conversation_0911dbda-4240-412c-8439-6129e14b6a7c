import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestAutoDeliverysServices(BaseTestCase):
    """
       登陆PC小店-进入自动发货服务
       """
    def test_button(self):
        self.express_auto_delivery_login()
        self.assert_text('使用指南','//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[1]/button')
        self.assert_text('停止使用','//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[2]/button')
        self.click('//*[@id="root"]/div[1]/div[2]/div/div[2]/div/div[1]/button')
        self.switch_to_window(1)
        self.sleep(1)



