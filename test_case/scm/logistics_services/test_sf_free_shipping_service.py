import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestSffreeShippingServices(BaseTestCase):
    """
       物流服务-顺丰包邮服务
       登陆PC小店-进行入物流服务
       """

    # 顺丰包邮服务页面
    @pytest.mark.p0
    def test_sf_free_shipping(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/sf-free-shipping'), True)
        # 查看服务介绍
        self.assert_text('服务介绍', '//*[@id="root"]/div[2]/div/div[2]/div/div[1]/button')
        # 唤起半屏
        self.click('//*[@id="root"]/div[2]/div/div[2]/div/div[1]/button')
        # 关闭半屏
        self.click("//button[@class='kwaishop-supply-logistics-pc-drawer-close']")
        # 查看使用指南
        self.assert_text('使用指南', '//*[@id="root"]/div[2]/div/div[2]/div/div[2]/button')
        self.click('//*[@id="root"]/div[2]/div/div[2]/div/div[2]/button')
        # self.check_document('//*[@id="root"]/div[2]/div/div[2]/div/div[2]/button', '顺丰包邮服务手册')
        self.switch_to_window(0)
        # 查看协议
        self.assert_text('查看协议', '//*[@id="root"]/div[2]/div/div[2]/div/div[3]/button')
        self.click('//*[@id="root"]/div[2]/div/div[2]/div/div[3]/button')
        self.switch_to_window(0)

    @pytest.mark.p0
    def test_sf_free_shipping_page(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        # 关闭服务
        self.assert_text('关闭服务', '//*[@id="root"]/div[2]/div/div[2]/div/div[4]/button')
        self.click('//*[@id="root"]/div[2]/div/div[2]/div/div[4]/button')
        # 关闭服务原因
        self.click("//label[1]//span[1]//span[1]")
        self.click("//label[2]//span[1]//span[1]")
        self.click("//label[3]//span[1]//span[1]")
        self.click("//label[4]//span[1]//span[1]")
        self.click(
            "//span[@class='kwaishop-supply-logistics-pc-checkbox kwaishop-supply-logistics-pc-checkbox-checked']")
        self.type("//textarea[@placeholder='请输入']", 12345678901230)
        # 再想想
        # self.assert_text('再想想',"//body[1]/div[8]/div[1]/div[2]/div[1]/div[2]/div[3]/div[1]/div[1]/button[1]")
        # #确定关闭
        # self.assert_text('确认关闭',"//body[1]/div[8]/div[1]/div[2]/div[1]/div[2]/div[3]/div[1]/div[1]/button[2]")
        # self.click("//body[1]/div[3]/div[1]/div[2]/div[1]/div[2]/div[3]/div[1]/div[1]/button[1]")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/button/span')

    @pytest.mark.p0
    def test_sf_free_pagelist(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()

        self.assert_text('服务模板', '//*[@id="root"]/div[3]/div/div[1]/div[1]/div/div[2]')
        self.click('//*[@id="root"]/div[3]/div/div[1]/div[1]/div/div[2]')
        self.assert_text('违规订单', '//*[@id="root"]/div[3]/div/div[1]/div[1]/div/div[3]')
        self.click('//*[@id="root"]/div[3]/div/div[1]/div[1]/div/div[3]')
        self.assert_text('绑定的商品', '//*[@id="root"]/div[3]/div/div[1]/div[1]/div/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[1]/div[1]/div/div[1]')

        self.assert_text('未绑定顺丰包邮商品', '//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[3]')
        self.assert_text('已绑定顺丰模板商品', '//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[2]')
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[2]')
        self.assert_text('全部在售商品', '//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[1]')
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[1]')

    @pytest.mark.p0
    def test_sf_free_unlocked(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/input', "1234fsd")
        self.assert_text('请输入数字', '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div[2]/div')
        # 商品id输入框
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div[1]/div/span/span')
        # 商品标题输入框
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/input', "1234")
        # 类目名称下拉框
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div')
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div/div/div/div')
        # 商品价格下拉框
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div/span[2]')
        # 查询按钮
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button')
        # 重置按钮
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[1]/button')

    @pytest.mark.p0
    def test_sf_free_unlocked_item(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        self.assert_text('商品信息',
                         '//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('绑定状态',
                         '//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('生效状态',
                         '//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('运费模板',
                         '//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('操作',
                         '//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[6]')

    @pytest.mark.p0
    def test_sf_free_unlocked_button(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        # 新品自动绑定模板
        self.assert_text('',
                         '//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div[3]/div/div/div/div[1]/span/button')
        # 一键绑定所有商品
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div[3]/div/div/div/div[2]/button')
        # 弹窗选择模板
        self.click(
            "//div[@class='kwaishop-supply-logistics-pc-col kwaishop-supply-logistics-pc-col-18 kwaishop-supply-logistics-pc-form-item-control']")
        # self.click('/html/body/div[6]/div/div[2]/div/div[2]/div[2]/form/div/div[2]/div[1]/div/div/span')
        # 取消按钮
        # self.assert_text('取消','/html/body/div[6]/div/div[2]/div/div[2]/div[3]/div/div[1]/button/span')
        # self.assert_text('保存',"//body[1]/div[6]/div[1]/div[2]/div[1]/div[2]/div[3]/div[1]/div[1]/button[2]")
        self.click("//button[@aria-label='Close']")

    @pytest.mark.p0
    def test_sf_free_locked_button(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        # 已绑定
        self.assert_text('已绑定顺丰模板商品', '//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[2]')
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[2]')
        # 模版名称
        self.assert_text('模板名称','//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[1]')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/div')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/div/div/span[2]')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button')
        self.sleep(2)

    @pytest.mark.p0
    def test_sf_free_locked_page(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        # 已绑定
        self.assert_text('已绑定顺丰模板商品', '//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[2]')
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[2]')

        self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]/div')
        # self.assert_text('批量解除绑定','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div[1]/div[2]/div/button')
        #查看打标效果
        # # self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div')
        # self.sleep(1)
        # # 换绑模板
        # self.assert_text('换绑模板','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[6]/div/div[1]/button')
        # # 解除绑定
        # self.assert_text('解除绑定','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[6]/div/div[2]/button')

    @pytest.mark.p0
    def test_sf_free_all_selled(self):
        self.express_sf_free_shipping_login()
        self.maximize_window()
        # 切换全部在售商品
        self.assert_text('全部在售商品', '//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[1]')
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[1]/div[1]/div[1]/div/div[1]')
        # 点击全部商品
        self.click('//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('批量绑定商品','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div[1]/div[2]/div[1]/button')
        self.assert_text('批量解除绑定','//*[@id="rc-tabs-1-panel-1"]/div[3]/div/div[1]/div/div/div/div/div[1]/div[2]/div[2]/button')