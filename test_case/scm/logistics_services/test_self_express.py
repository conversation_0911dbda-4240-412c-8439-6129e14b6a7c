from time import sleep

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestSelfExpressService(BaseTestCase):

    @pytest.mark.p0
    def test_title_element(self):
        self.express_service_login_self_select_express()
        self.maximize_window()
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[2]/div/div/button')
        self.switch_to_window(1)
        sleep(1)
        ## 校验url正确性
        c_url=self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/self-select-express'),True)

        #验证title
        self.assert_text('自选快递服务','//*[@id="root"]/div[2]/div/div[1]/div/div/span')
        self.assert_text('免费开通','//*[@id="root"]/div[2]/div/div[1]/div/div/div/span[1]/span')
        self.assert_text('促进转化','//*[@id="root"]/div[2]/div/div[1]/div/div/div/span[2]/span')
        self.assert_text('流量加权','//*[@id="root"]/div[2]/div/div[1]/div/div/div/span[3]/span')
        self.assert_text('开通用户自选快递能力，为用户提供更多选择，可提升商品转化，获得更多流量曝光以及销量；目前邮政快递是消费者咨询中要求指定最多的快递，建议配置邮政快递。','//*[@id="root"]/div[2]/div/div[1]/div/section')

        #使用指南
        self.assert_text('使用指南','//*[@id="root"]/div[2]/div/div[2]/div/div[1]/button/span[2]')
        self.click('//*[@id="root"]/div[2]/div/div[2]/div/div[1]/button')
        self.switch_to_window(2)
        sleep(1)
        self.switch_to_window(1)

        self.assert_text('服务介绍','//*[@id="root"]/div[2]/div/div[2]/div/div[2]/button/span[2]')
        self.click('//*[@id="root"]/div[2]/div/div[2]/div/div[2]/button')
        sleep(1)


        #服务介绍
        self.assert_text('自选快递服务',"//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//div[@class='kwaishop-supply-logistics-pc-drawer-title'][contains(text(),'自选快递服务')]")
        self.assert_text('服务介绍','//*[@id="serviceWorth"]/div[1]/div/section')
        self.assert_element('//*[@id="serviceWorth"]/div[2]/img[1]')
        self.assert_element('//*[@id="serviceWorth"]/div[2]/img[2]')

        self.assert_text('服务价值','//*[@id="afterActivation"]/div[1]/div/section')
        self.assert_element('//*[@id="afterActivation"]/div[2]/img[1]')
        self.assert_element('//*[@id="afterActivation"]/div[2]/img[2]')
        self.assert_element('//*[@id="afterActivation"]/div[2]/img[3]')

        self.assert_text('常见问题','//*[@id="faq"]/div[1]/div/section')
        self.assert_element('//*[@id="faq"]/div[2]/section[1]')
        self.assert_element('//*[@id="faq"]/div[2]/section[2]')

        #关闭按钮
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//button[@aria-label='Close']")
        #【我知道了】按钮存在
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//div[@class='kwaishop-supply-logistics-pc-drawer-footer']//button[@type='button']")
        self.click("//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//div[@class='kwaishop-supply-logistics-pc-drawer-footer']//button[@type='button']")
        sleep(1)

    @pytest.mark.p0
    def test_result_list(self):
        self.express_service_login_self_select_express()
        self.maximize_window()
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[2]/div/div/button')
        self.switch_to_window(1)
        sleep(1)
        ## 校验url正确性
        c_url=self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/self-select-express'),True)

        #结果列表表头
        self.assert_text('自选快递公司','//*[@id="root"]/div[3]/div/div/div[1]/div/div/div/div/div[1]/div')
        self.assert_element('//*[@id="root"]/div[3]/div/div/div[1]/div/div/div/div/div[2]/div/div/button')

        #结果字段check
        self.assert_text('快递公司','//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('快递可履约的地区',"//th[contains(text(),'快递可履约的地区')]")
        self.assert_text('状态','//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('操作','//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')

        #校验一个快递公司
        self.assert_text('邮政','//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[5]/td[1]/div/div/div[1]')
        self.assert_text('消费者指定最多','//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[5]/td[1]/div/div/div[2]/span/span[2]')
        # 可覆盖区域查看详情
        self.assert_element('//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[5]/td[2]/div/a')
        self.click('//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[5]/td[2]/div/a')
        sleep(1)
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-modal-wrap']")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-modal-body']")
        self.assert_text('快递可履约的地区','//*[@id="rcDialogTitle0"]')
        self.click("//span[@class='kwaishop-supply-logistics-pc-modal-close-x']")
        sleep(1)
        #校验操作栏按钮存在
        self.assert_element('//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div[1]/button')
        self.assert_element('//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div[2]/button')
        self.assert_element('//*[@id="root"]/div[3]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div[3]/button')

    @pytest.mark.p0
    def test_button(self):
        self.express_service_login_self_select_express()
        self.maximize_window()
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[2]/div/div/button')
        self.switch_to_window(1)
        sleep(1)
        # 校验url正确性
        c_url=self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/self-select-express'),True)
        # 添加快递公司按钮
        self.click('//*[@id="root"]/div[3]/div/div/div[1]/div/div/div/div/div[2]/div/div/button')
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//div[@class='kwaishop-supply-logistics-pc-drawer-content']")

        self.assert_text('添加自选快递公司',"//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//div[@class='kwaishop-supply-logistics-pc-drawer-header-title']//div[1]")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-drawer kwaishop-supply-logistics-pc-drawer-right kwaishop-supply-logistics-pc-drawer-open']//button[@aria-label='Close']")
        self.assert_text('可自选的快递公司',"//label[contains(text(),'可自选的快递公司')]")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-select kwaishop-supply-logistics-pc-select-single kwaishop-supply-logistics-pc-select-show-arrow']//div[@class='kwaishop-supply-logistics-pc-select-selector']")
        self.assert_text('快递可履约的地区',"//label[contains(text(),'快递可履约的地区')]")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-select-selection-overflow']")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-select-selection-overflow']")
        self.assert_element("//button[@class='kwaishop-supply-logistics-pc-btn']")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-space kwaishop-supply-logistics-pc-space-horizontal kwaishop-supply-logistics-pc-space-align-center']//div[1]//button[1]")
        self.click("//button[@class='kwaishop-supply-logistics-pc-btn']")

        self.click("//body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[4]/div[1]/div[3]/button[1]")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-modal kwaishop-supply-logistics-pc-modal-confirm kwaishop-supply-logistics-pc-modal-confirm-confirm']//div[@class='kwaishop-supply-logistics-pc-modal-body']")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-modal-body']//button[1]")
        self.assert_element("//div[@class='kwaishop-supply-logistics-pc-modal-body']//button[1]")































