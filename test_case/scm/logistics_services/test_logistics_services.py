import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase

@ddt
class TestLogisticsServices(BaseTestCase):
    """
       登陆PC小店-进行入物流服务
       """

    @pytest.mark.p0
    def test_express_service_home_login(self):
        self.express_service_login()
        self.maximize_window()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/express-service'), True)
        # 点击已开通「 西北集运服务」说明
        # self.click('//*[@id="root"]/div[1]/div/div/div/div/div/div/a')
        # self.switch_to_window(1)
        # self.sleep(0.5)
        # c_url = self.get_current_url()
        # self.assert_equal(c_url.endswith('/kwaishop/newKnowledge/631573974543532115/581287827683016715'), True)


    # 西北集运服务面板
    @pytest.mark.p0
    def test_logistics_service_panel(self):
        self.express_service_login()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[1]/div[2]/div/div[1]/button')
        self.switch_to_window(1)
        self.sleep(2)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supplychain/address?tabs=mras'),True)
        self.switch_to_window(0)
        # 结算明细按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[1]/div[2]/div/div[2]/button')
        self.switch_to_window(2)
        self.sleep(2)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/logistics_settlement/logisticsSettlement'), True)
        self.switch_to_window(0)

        # 拦截明细按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[1]/div[2]/div/div[3]/button')
        self.switch_to_window(3)
        self.sleep(2)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplyLogisticsIntercept/kuaishop_wuliulanjie'), True)
        self.switch_to_window(0)

        # 物流工单按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[1]/div[2]/div/div[4]/button')
        self.switch_to_window(4)
        self.sleep(2)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/logistics/services/work-order?logisticsServiceCode=65'), True)
        self.switch_to_window(0)

        # 查看服务按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[1]/div[2]/div/div[5]/button')
        self.switch_to_window(5)
        self.sleep(2)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/express-service/consolidate-service'), True)
        self.switch_to_window(0)

    # 物流工单服务面板
    @pytest.mark.p0
    def test_work_order_service_panel(self):
        self.express_service_login()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[2]/div[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[2]/div[2]/div/div/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/logistics/services/work-order'), True)
        self.switch_to_window(0)

    # 平台特邀服务面板
    @pytest.mark.p0
    def test_special_invite_service_panel(self):
        self.express_service_login()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[3]/div[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[3]/div[2]/div/div/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/logistics/services/special-invite-logistics'), True)
        self.switch_to_window(0)

    # 顺丰包邮服务面板
    @pytest.mark.p0
    def test_sf_free_panel(self):
        self.express_service_login()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[4]/div[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[4]/div[2]/div/div/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/logistics/services/sf-free-shipping'), True)
        self.switch_to_window(0)

    # 物流拦截面板
    @pytest.mark.p0
    def test_express_service_panel(self):
        self.express_service_login()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[5]/div[1]')
        # 查看售后策略按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[5]/div[2]/div/div[1]/button')
        self.switch_to_window(1)
        self.sleep(0.5)
        self.switch_to_window(0)
        # 服务介绍按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[5]/div[2]/div/div[2]/button')
        self.switch_to_window(2)
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/package-intercept'), True)
        self.switch_to_window(0)
        # 拦截管理按钮
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[5]/div[2]/div/div[3]/button')
        self.switch_to_window(3)
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/logistics/services/package-intercept-manage'), True)
        self.switch_to_window(0)

    # 生鲜到家服务面板
    @pytest.mark.p0
    def test_express_freshhome_panel(self):
        self.express_service_login()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[6]/div[1]/section[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[6]/div[2]/div/div/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/xdj-service'), True)
        self.switch_to_window(0)

    # 上门安装服务面板
    @pytest.mark.p0
    def test_express_door_install_panel(self):
        self.express_service_login_new()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[7]/div[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[7]/div[2]/div/div/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/door-install'), True)
        self.switch_to_window(0)

    # 自动发货服务面板
    @pytest.mark.p0
    def test_auto_delivery_panel(self):
        self.express_service_login_new()
        self.maximize_window()
        self.assert_text('','//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[2]/div/div[2]/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/common-introduce?serviceItemCode=21'), True)
        self.switch_to_window(0)
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[2]/div/div[1]/button')
        self.switch_to_window(2)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/trade-shipping/shipping-center/home?tab=AutoShipping'), True)


    #自选快递服务面板
    @pytest.mark.p0
    def test_self_express_panel(self):
        self.express_service_login_self_select_express()
        self.maximize_window()
        self.assert_text('自选快递服务','//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[1]/section[1]')
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div[8]/div[2]/div/div/button')
        self.switch_to_window(1)
        self.sleep(1)
        c_url=self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/self-select-express'), True)
        self.switch_to_window(0)


