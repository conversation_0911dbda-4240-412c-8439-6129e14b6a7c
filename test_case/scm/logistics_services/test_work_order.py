import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase


@ddt
class TestWorkOrderServices(BaseTestCase):
    """
       物流服务-物流共担服务
       登陆PC小店-进行入物流服务
       """

    # 物流工单服务页面
    @pytest.mark.p0
    def test_work_order_shipping(self):
        self.express_work_order_login()
        self.maximize_window()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/logistics/services/work-order'), True)

    # 搜索栏
    @pytest.mark.p0
    def test_work_order_search(self):
        self.express_work_order_login()
        self.maximize_window()
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/div')  # 点击单号查询
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/span/div/div/span[2]')  # 选择订单编号
        self.type("//textarea[@placeholder='批量查询时请间隔单号']", '1234')  # 填入订单编号
        # 创建时间筛选
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(0.5)
        # 完成时间筛选
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]/input')  # 今天
        self.sleep(0.5)
        # 工单类型
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/div')
        self.sleep(1)
        self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div[3]/div/div[2]/form/div[2]/div[2]/div/div[2]/div/div/div/div')
        self.sleep(1)
        # 物流服务
        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[1]/div/div[2]/div/div/div/div/span')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[1]/div/div[2]/div/div/div/div/div/div[1]')
        # 物流公司
        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div/div/div/div')
        # 查询
        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button')
        # 重置
        self.click('//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[1]/button')

    # 看板
    @pytest.mark.p0
    def test_work_order_pannel(self):
        self.express_work_order_login()
        self.maximize_window()
        self.assert_text('工单编号','//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('工单类型', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('物流服务', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('订单编号', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('物流信息', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('工单状态', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('工单关联时间', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[7]')
        self.assert_text('操作', '//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[8]')
        # 点击详情
        self.assert_text('','//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div[1]/button')
        # 点击补充材料
        self.click('//*[@id="root"]/div[3]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div[2]/button')

    # 按键button
    @pytest.mark.p0
    def test_work_order_butt(self):
        self.express_work_order_login()
        self.maximize_window()
        self.assert_text('全部','//*[@id="root"]/div[3]/div/div[3]/div[1]/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[3]/div[1]/div[1]')
        self.assert_text('服务商处理中','//*[@id="root"]/div[3]/div/div[3]/div[2]/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[3]/div[2]/div[1]')
        self.assert_text('商家处理中','//*[@id="root"]/div[3]/div/div[3]/div[3]/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[3]/div[3]/div[1]')
        self.assert_text('平台处理中','//*[@id="root"]/div[3]/div/div[3]/div[4]/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[3]/div[4]/div[1]')
        self.assert_text('已完结','//*[@id="root"]/div[3]/div/div[3]/div[5]/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[3]/div[5]/div[1]')
        self.assert_text('已撤销','//*[@id="root"]/div[3]/div/div[3]/div[6]/div[1]')
        self.click('//*[@id="root"]/div[3]/div/div[3]/div[6]/div[1]')
        self.assert_text('导出工单','//*[@id="root"]/div[3]/div/div[4]/div/div[1]/div/div/div/div/div[2]/div/div[1]/button')
        self.assert_text('新增工单','//*[@id="root"]/div[3]/div/div[4]/div/div[1]/div/div/div/div/div[2]/div/div[2]/button')





