import time

from selenium.common import WebDriverException
from seleniumbase.config.settings import SMALL_TIMEOUT
from seleniumbase.fixtures import page_actions

from seleniumbase import BaseCase
# 获取账号信息
from constant.domain import get_domain_by_env
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        """
        前置登录
        """
        account_data = get_account_info(account)
        # 若运行环境为prt时，指定泳道:PRT.test
        if self.var1 and self.var1 == 'prt':
            self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
            host = get_domain_by_env(domain, 'prt')
        else:
            # 运行线上环境
            host = get_domain_by_env(domain, 'online')
        self.open(host)
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    # def login(self, domain, account):
    #     account_data = get_account_info(account)
    #     host = get_domain(domain)
    #     self.open(host)
    #     self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
    #     self.type("input[placeholder='请输入手机号']", account_data['account'])
    #     self.type("input[placeholder='请输入密码']", account_data['password'])
    #     self.click(
    #         "//*[@id='root']/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span")

    def sub_account_login(self, domain, account):
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        # 选择我是员工
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[1]/div[2]/span[2]')
        # 选择密码登陆
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        # 登陆
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button')

    def click_if_exist(self, selector, by="css selector", timeout=SMALL_TIMEOUT, delay=0, scroll=True):
        try:
            page_actions.wait_for_element_visible(
                self.driver,
                selector,
                by,
                timeout=timeout)
            self.click(selector, by, timeout, delay, scroll)
        except WebDriverException:
            print("not exist element, ingore")

    def click_xiaodian_popup_window(self):
        time.sleep(2)
        count = 0
        while count < 3:
            if self.is_element_visible('#driver-popover-item'):
                self.click('#driver-page-overlay')  # 点击空白处
            count += 1
            time.sleep(1)

    def check_document(self, selector, doc_name, tab_no=1):
        # 点击连接
        self.click(selector)
        # 切换窗口
        self.switch_to_window(tab_no)
        self.sleep(3)
        # 校验title
        self.assert_title(doc_name)

    def online_parcel_login(self):
        self.login('ONLINE_PARCEL_SENDING_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        self.sleep(0.5)
        self.click_xiaodian_popup_window()
        self.click('//*[@id="OnlineShipping"]/span')

    def privacy_login(self):
        self.login('PRIVACY_DOMAIN', 'supply_account_template')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_template_login(self):
        self.login('EXPRESS_TEMPLATE_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def freight_template_form_login(self):
        self.login('FREIGHT_TEMPLATE_FORM_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def add_freight_template_form_login(self):
        self.login('FREIGHT_TEMPLATE_FORM_DOMAIN', 'supply_account_template')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_service_login(self):
        self.login('EXPRESS_SERVICE_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_service_login_new(self):
        self.login('EXPRESS_SERVICE_DOMAIN', 'supply_account_template')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_service_login_self_select_express(self):
        self.login('EXPRESS_SERVICE_DOMAIN', 'supply_account_self_select_express')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_sf_free_shipping_login(self):
        self.login('EXPRESS_SF_FREE_SHIPPING_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_work_order_login(self):
        self.login('EXPRESS_WORK_ORDER_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_fresh_home_shipping_login(self):
        self.login('EXPRESS_FRESH_HOME_DOMAIN', 'supply_account')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_door_install_shipping_login(self):
        self.login('DOOR_INSTALL_DOMAIN', 'supply_account_template')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()

    def express_auto_delivery_login(self):
        self.login('AUTO_DELIVERY_DOMAIN', 'supply_account_template')
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.click_xiaodian_popup_window()


