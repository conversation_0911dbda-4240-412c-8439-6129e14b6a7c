import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.scm.base import BaseTestCase



@ddt
class TestExpressTemplateManagement(BaseTestCase):
    """
    运费模版-模版基础操作
    登陆PC小店-进入运费模版
    """

    @pytest.mark.p0
    def test_express_template_home(self):
        self.express_template_login()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/address/freight-template/list'), True)
        self.assert_text('偏远地区设置', '//*[@id="root"]/div[2]/div[3]/div/div[1]/div/div/div/div/div[2]/div/div[1]/span/button')
        self.check_document('//*[@id="root"]/div[2]/div[1]/div/div/div/button', '新版 运费模板 操作手册 - 轻雀文档')
        self.switch_to_window(0)

    @pytest.mark.p0
    def test_consolidate_service(self):
        self.express_template_login()
        self.maximize_window()
        self.assert_text('已开通「西北集运服务」', '//*[@id="root"]/div[2]/div[1]/span/div')
        # 点击已开通「 西北集运服务」按钮
        self.click('//*[@id="root"]/div[2]/div[1]/span/div')

        self.switch_to_window(1)
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/express-service/consolidate-service'), True)

    @pytest.mark.p0
    def test_restricted_sale_diagnosis(self):
        self.express_template_login()
        self.maximize_window()
        self.assert_text('限售诊断 (非偏远地区)','//*[@id="root"]/div[2]/div[2]/div[1]')
        self.assert_text('近七天拦截下单次数', '//*[@id="root"]/div[2]/div[2]/div[2]/div[1]/div[1]')
        self.assert_text('近七天限售损失GMV', '//*[@id="root"]/div[2]/div[2]/div[2]/div[2]/div[1]')
        self.assert_text('限售模板数量', '//*[@id="root"]/div[2]/div[2]/div[2]/div[3]/div[1]')
        self.assert_text('推荐解除省份', '//*[@id="root"]/div[2]/div[2]/div[2]/div[4]/div[1]')

    @pytest.mark.p0
    def test_freight_template_form_add(self):
        self.express_template_login()
        self.maximize_window()
        self.assert_text('新增运费模板', '//*[@id="root"]/div[2]/div[3]/div/div[1]/div/div/div/div/div[2]/div/div[2]/button')
        # 点击新增运费模版
        self.click('//*[@id="root"]/div[2]/div[3]/div/div[1]/div/div/div/div/div[2]/div/div[2]/button')
        self.switch_to_window(1)
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/address/freight-template/form'), True)

    @pytest.mark.p0
    def test_express_template_table(self):
        self.express_template_login()
        self.maximize_window()
        self.assert_text('模板名称', '//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('计费方式', '//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('最后编辑时间', '//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('操作', '//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[4]')

    @pytest.mark.p0
    def test_freight_template_detail(self):
        self.express_template_login()
        self.maximize_window()
        # self.assert_text('', '//*[@id="root"]/div[3]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/a')
        # 点击模板名称可以跳转
        self.click('//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]')
        # self.switch_to_window(1)
        # self.sleep(0.5)
        # c_url = self.get_current_url()
        # self.assert_equal(c_url.endswith('/zone/supply/address/freight-template/detail?id=17242507142'), True)

    @pytest.mark.p0
    def test_freight_template_operation(self):
        self.express_template_login()
        self.maximize_window()
        # 复制操作
        self.assert_text('复制',"//tbody/tr[10]/td[4]/button[1]")
        # self.click('//*[@id="root"]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/button[1]')
        # 删除按钮
        # self.assert_text('删除','//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[5]/td[4]/button[2]')

        # 使用详情
        # self.assert_text('使用详情','//*[@id="root"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/a[2]/button')
        # 编辑操作
        # self.assert_text('编辑','//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/a/button')
        # 点击编辑运费模板
        # self.click('//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[4]/a/button')
        # self.switch_to_window(1)
        # self.sleep(1)

    # @pytest.mark.p0
    # def test_freight_template_unusual(self):
    #     self.express_template_login()
    #     self.maximize_window()
    #     # 运费模板异常
    #     self.assert_text('运费异常，请立即修改','//*[@id="root"]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[5]/td[1]/span')
    #     # 点击修改异常运费模板
    #     self.click('//*[@id="root"]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[5]/td[1]/span')
    #     self.switch_to_window(1)
    #     self.sleep(1)

    @pytest.mark.p0
    def test_freight_template_form(self):
        self.freight_template_form_login()
        self.maximize_window()
        # 新增运费模版页面-面板设置
        self.assert_text('基本信息', '//*[@id="root"]/div/div/form/div[1]')
        self.assert_text('西北集运', '//*[@id="root"]/div/div/form/div[2]')
        self.assert_text('包邮区域设置', '//*[@id="root"]/div/div/form/div[3]')
        self.assert_text('不包邮地区设置', '//*[@id="root"]/div/div/form/div[4]')
        self.assert_text('不配送地区设置', '//*[@id="root"]/div/div/form/div[5]')
        # self.click(SUBMIT_BUTTON)
        # self.assert_text('提交', '//*[@id="root"]/div/div/form/div[6]/div[2]/div/button')

    @pytest.mark.p0
    def test_freight_template_basic_information(self):
        self.freight_template_form_login()
        self.maximize_window()
        # 新增运费模版页面-基本信息设置
        self.assert_text('模板名称','//*[@id="root"]/div/div/form/div[1]/div[2]/div[1]')
        self.assert_text('发货地址', '//*[@id="root"]/div/div/form/div[1]/div[3]/div[1]')
        # 点击新增运费模版
        self.click('//*[@id="root"]/div/div/form/div[1]/div[3]/div[2]/div[1]')
        self.sleep(1)

    @pytest.mark.p0
    def test_freight_template_consolidate_service(self):
        self.freight_template_form_login()
        self.maximize_window()
        # 新增运费模版页面-西北集运
        self.check_document('//*[@id="root"]/div/div/form/div[2]/div[2]/a[1]', '【西北集运】暂不承运商品类目 - 轻雀文档')
        self.switch_to_window(0)
        self.sleep(2)
        self.check_document('//*[@id="root"]/div/div/form/div[2]/div[2]/a[2]', '【西北集运】暂不承运商品类目 - 轻雀文档')
        self.switch_to_window(0)
        self.sleep(2)
        self.assert_text('已开通「西北集运服务」','//*[@id="root"]/div/div/form/div[2]/div[1]/span')
        # 点击西北集运服务按钮
        self.click('//*[@id="root"]/div/div/form/div[2]/div[1]/span')
        self.sleep(2)

    @pytest.mark.p0
    def test_freight_free_shipping_area(self):
        self.freight_template_form_login()
        self.maximize_window()
        # 新增运费模版页面-包邮区域
        self.assert_text('效果预览','//*[@id="root"]/div/div/form/div[3]/div[2]/div[2]/div[1]/div/a')
        #点击效果预览
        # self.click("//div[@class='kwaishop-supply-eshop-address-pc-spin-container']//div[1]//div[1]//a[1]")
        # self.sleep(2)
        # self.assert_text('效果预览',"//form[@class='kwaishop-supply-eshop-address-pc-form kwaishop-supply-eshop-address-pc-form-horizontal CetSi1Lc9CTumuMMrlpO']//div[1]//div[1]//a[1]")
        #点击效果预览
        # self.click("//div[@class='kwaishop-supply-eshop-address-pc-spin-container']//div[1]//div[1]//a[1]")
        # self.sleep(2)
        # self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div[2]/div[2]/div/a')
        self.assert_text('全选','//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[1]/div')
        self.assert_text('华北地区','//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[2]')
        self.assert_text('东北地区', '//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[3]')
        self.assert_text('华东地区', '//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[4]')
        self.assert_text('华中地区', '//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[5]')
        self.assert_text('华南地区', '//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[6]')
        self.assert_text('西南地区', '//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[7]')
        self.assert_text('西北地区', '//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[8]')
        self.sleep(2)
        # 点击取消西北地区包邮区域
        self.click('//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[8]/div[2]/label[5]')
        self.click('//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[1]')
        self.sleep(2)

    @pytest.mark.p0
    def test_freight_template_no_free_shipping_area(self):
        self.freight_template_form_login()
        self.maximize_window()
        # 新增运费模版页面-不包邮区域
        self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/button')
        # 点击选择区域/地址
        self.click('//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[8]/div[2]/label[5]')
        self.sleep(2)
        # 点击运费设置
        self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/div[2]/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[1]')
        self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/div[2]/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[2]')
        self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/div[2]/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[3]')
        self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/div[2]/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[4]')
        # 点击指定条件包邮
        self.click("//button[@role='switch']")
        # 点击添加不包邮地区
        self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/button')
        self.sleep(2)


    @pytest.mark.p0
    def test_freight_template_undistributed_area(self):
        self.freight_template_form_login()
        self.maximize_window()
        # 新增运费模版页面-不配送区域
        self.assert_text('添加不配送地区','//*[@id="root"]/div/div/form/div[5]/div[3]/button')
        # 点击添加不配送地区
        self.click('//*[@id="root"]/div/div/form/div[5]/div[3]/button/span[1]')
        self.assert_text('区域/地址','//*[@id="root"]/div/div/form/div[5]/div[3]/div[1]/div[1]')
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[5]/div[3]/div[1]/div[1]/div[2]/div/div/div/div[1]/span')
        self.assert_text('不配送原因','//*[@id="root"]/div/div/form/div[5]/div[3]/div[1]/div[3]/div[1]/label')
        # self.click('//*[@id="excludeProvinces_0_reasonCode"]/label[1]/span[1]')
        self.sleep(2)
        self.assert_text('删除','//*[@id="root"]/div/div/form/div[5]/div[3]/div[1]/div[4]/button')
        self.click('//*[@id="root"]/div/div/form/div[5]/div[3]/div[1]/div[4]/button')
        self.sleep(2)

    # @pytest.mark.p0
    # def test_add_freight_template(self):
    #     self.add_freight_template_form_login()
    #     self.maximize_window()
    #     self.type("//input[@id='name']",'测试模板9811')
    #     self.click('//*[@id="root"]/div/div/form/div[1]/div[3]/div[2]/div[1]/div/div/div[1]/span[1]')
    #     self.click('//*[@id="root"]/div/div/form/div[1]/div[3]/div[2]/div[1]/div/div/div[2]/div/div/div/div/ul[1]/li[1]')
    #     self.click('//*[@id="root"]/div/div/form/div[1]/div[3]/div[2]/div[1]/div/div/div[2]/div/div/div/div/ul[2]/li/div[1]')
    #     self.click('//*[@id="root"]/div/div/form/div[1]/div[3]/div[2]/div[1]/div/div/div[2]/div/div/div/div/ul[3]/li[1]/div')
    #     self.assert_text('','//*[@id="root"]/div/div/form/div[4]/div[3]/div')
    #     self.assert_text('','//*[@id="root"]/div/div/form/div[4]/div[3]/button')
    #     self.click('//*[@id="root"]/div/div/form/div[3]/div[4]/div/div/div/div[8]/div[2]/label[5]')
    #     self.assert_text('','//*[@id="root"]/div/div/form/div[4]/div[3]/div[1]/div')
    #     self.assert_text('','//*[@id="root"]/div/div/form/div[4]/div[3]/div[2]/div[2]/div')
    #     self.assert_text('','//*[@id="root"]/div/div/form/div[4]/div[3]/button')
    #     # 点击不包邮下拉框
    #     self.click('//*[@id="root"]/div/div/form/div[4]/div[3]/div[2]/div[2]/div/div[1]/div[2]/div[1]/div/div/div[1]/div/div[1]/span/span')
    #     self.assert_text('添加不配送地区','//*[@id="root"]/div/div/form/div[5]/div[2]')
    #     # 点击添加不配送地区
    #     self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/button')
    #     self.assert_text('区域/地址','//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div[1]')
    #     self.sleep(2)
    #     self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div[1]/div[2]/div/div/div/div[1]/span')
    #     self.assert_text('不配送原因','//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div[3]/div[1]/label')
    #     self.assert_text('删除','//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div[4]/button')
    #     self.click('//*[@id="root"]/div/div/form/div[5]/div[2]/div[1]/div[4]/button')
    #     # 提交按钮
    #     self.click("(//span[contains(text(),'提 交')])[1]")
    #     self.click("//button[@class='kwaishop-supply-eshop-address-pc-btn kwaishop-supply-eshop-address-pc-btn-primary']")
    #     # 返回上一页
    #     # c_url = self.get_current_url()
    #     # self.assert_equal(c_url.endswith('/zone/supply/address/freight-template/list'), True)
    #     self.assert_text('测试模板9811','//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[4]/td[1]')
    #     # 删除当前模板
    #     self.click('//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[4]/td[4]/button[2]')
    #     self.click('//*[@id="root"]/div[2]/div[3]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[4]/td[4]/div/div/div/div/div[2]/div/div[2]/button[2]')
