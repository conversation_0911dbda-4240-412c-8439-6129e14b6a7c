import pytest

from test_case.core_link.supply_chain.facesheet_case.Handwork_case import *
from test_case.scm.base import BaseTestCase

@ddt
class TestPrivacy(BaseTestCase):

    def test_privacy_tel(self):
        self.privacy_login()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supplychain/privacy'), True)

        self.click('//*[@id="root"]/div[1]/div/div[3]/div[1]/div[2]/div[2]/div[1]/div/div[2]/a')
        self.switch_to_window(0)
        self.assert_text('子账号管理','//*[@id="root"]/div[1]/div/div[3]/div[1]/div[2]/div[2]/div[2]/div/div[2]/a')
        self.assert_text('新增主叫号码','//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[1]/div[2]/div[3]/button')

    def test_privacy_tel_panel(self):
        self.privacy_login()
        self.assert_text('账号昵称','//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('账号ID', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('岗位', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('手机号码', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('座机号码', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('操作', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('修改','//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[6]/div/div[1]/button')
        self.assert_text('设为默认', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[6]/div/div[2]/button')
        self.assert_text('删除', '//*[@id="root"]/div[1]/div/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[6]/div/div[3]/button')

    def test_tel_list(self):
        self.privacy_login()
        self.click('//*[@id="root"]/div[1]/div/div[2]/div[1]/div[1]/div/div[2]')
        self.maximize_window()
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supplychain/privacy'), True)

        # self.assert_text('重置',"//span[contains(text(),'重 置')]")
        # self.assert_text('查询',"//span[contains(text(),'查 询')]")

        self.assert_text('订单编号',
                         '//*[@id="root"]/div[1]/div/div[3]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('开始通话-结束通话时间',
                         '//*[@id="root"]/div[1]/div/div[3]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('通话时长',
                         '//*[@id="root"]/div[1]/div/div[3]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('主叫号码',
                         '//*[@id="root"]/div[1]/div/div[3]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('被叫买家昵称和ID',
                         '//*[@id="root"]/div[1]/div/div[3]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('虚拟号',
                         '//*[@id="root"]/div[1]/div/div[3]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[6]')

