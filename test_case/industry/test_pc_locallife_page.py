from time import sleep, time
from unittest import skip, skipIf
from ddt import ddt, data, unpack
from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain
from test_case.industry.base import BaseTestCase
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env

# host = get_domain("BAIDU_DOMAIN")
# # 获取账号信息
# account = get_account_detail(get_account_info("TEST_ACCOUNT"))

class TestLocallifePage(BaseTestCase):

    @skip
    def test_demo(self):
        self.login("MERCHANT_DOMAIN", "locallife_account")
        self.assert_title("快手小店")
        sleep(5)
        #点击券码工具箱
        self.click("#menu_item_84 > span > span")
        sleep(2)
        #document.querySelector("#rc-tabs-0-tab-VERIFY_LIST")
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith("zone/trade/voucher/verify-list"), True)
        self.assert_text("券码核销", "#rc-tabs-0-tab-VERIFY_LIST")
        # rc-tabs-1-tab-CODE_MANAGEMENT
        #document.querySelector("#rc-tabs-1-tab-CODE_MANAGEMENT")
        sleep(2)
        # rc-tabs-0-tab-CODE_MANAGEMENT | #rc-tabs-1-tab-CODE_MANAGEMENT
        self.assert_text("券码库管理", "#rc-tabs-0-tab-CODE_MANAGEMENT")
        sleep(2)
        # rc-tabs-1-tab-SHOP_MANAGEMENT
        self.assert_text("门店管理", "#rc-tabs-0-tab-SHOP_MANAGEMENT")

        #查看卡券核销列表不为空
        #self.assert_element("div.zone-trade-spin-container>div>div>div>table>tbody>tr:nth-child(2)")
        self.assert_element("div.ant-table-wrapper > div > div > div > div > div > table > tbody > tr:nth-child(2)")

        #点开核销: body > div:nth-child(9) > div > div > ul > li:nth-child(1) > span
        # #root > div > div > div.content__2Sx19 > div.operations__2MxmR > div.buttons__TATqq > button.zone-trade-btn.zone-trade-btn-primary.zone-trade-dropdown-trigger
        # #root > div > div > div.content__2Sx19 > div.operations__2MxmR > div.buttons__TATqq > button.zone-trade-btn.zone-trade-btn-primary.zone-trade-dropdown-trigger > span
        # body > div:nth-child(12) > div > div > ul > li:nth-child(1) > span
        # body > div:nth-child(12) > div > div > ul > li:nth-child(1) > span
        #self.hover_and_click("div.content__2Sx19 > div.operations__2MxmR > div.buttons__TATqq > button.zone-trade-btn.zone-trade-btn-primary.zone-trade-dropdown-trigger > span", "body > div:nth-child(12) > div > div > ul > li:nth-child(1) > span", hover_by="css selector", click_by="css selector")
        #检查单个核销，批量核销
        self.hover_and_click("button.ant-btn.ant-btn-primary.ant-dropdown-trigger > span", "li.ant-dropdown-menu-item.ant-dropdown-menu-item-only-child", hover_by="css selector", click_by="css selector")
        #检查请输入卡券的输入框： #eTicketNo，#eTicketNo
        self.assert_element("#eTicketNo")
        #self.assert_equal(self.get_p("#eTicketNo"), "请输入卡券券码")
        #打开门店管理，不出现404（#rc-tabs-0-tab-SHOP_MANAGEMENT）
        self.click("#rc-tabs-0-tab-SHOP_MANAGEMENT")
        self.assert_no_404_errors()  # 校验没有404
        #后退, 点击券码工具箱
        self.click("#menu_item_84 > span > span")
        #去点击券码库管理: #rc-tabs-1-tab-CODE_MANAGEMENT
        self.click("#rc-tabs-1-tab-CODE_MANAGEMENT")
        self.assert_no_404_errors()  # 校验没有404








