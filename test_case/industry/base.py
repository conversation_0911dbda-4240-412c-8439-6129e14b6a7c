import time

from selenium.webdriver.chrome import webdriver
from common.Goods.goodsBase import BaseTestCase as BaseCase
from seleniumbase import get_driver
from constant.account import get_account_info
from constant.domain import get_domain

from constant.account import get_account_info
from constant.domain import get_domain

class BaseTestCase(BaseCase):
    def login(self, domain, account):
        # account_data = get_account_info(account)
        # # 用户名 account_data['account']
        # # 密码 account_data['password']
        # host = get_domain(domain)
        # self.open(host)
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        # self.assert_text("扫码登录", "div.choseTab--gqNJB > div:nth-child(1)")  # div标签中的第一个元素
        # self.assert_text("手机号登录", "div.choseTab--gqNJB > div:nth-child(2)")
        # self.click("div.choseTab--gqNJB > div:nth-child(2)")
        # self.type("input[placeholder='请输入手机号']", account_data['account'])
        # self.type("input[placeholder='请输入密码']", account_data['password'])
        # self.click("button[type='button']")

        #super(BaseTestCase, self).login(domain, account)

        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)
        self.open(host)
        time.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.assert_text("扫码登录", "//*[text()='扫码登录']")  # div标签中的第一个元素
        self.assert_text("手机号登录", "//*[text()='手机号登录']")
        self.click("//*[text()='手机号登录']")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        time.sleep(5)
        