# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/20 11:32 AM
@Auth ： zhanle
@File ：test_new_dashboard.py
@IDE ：PyCharm
"""
import random
import time
import unittest
from time import sleep
from unittest import skip

from selenium import webdriver
from selenium.webdriver.chrome.options import Options  # 导入无头浏览器的包
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
account_proxy_app = BaseHttpRequest(user_id="APP_**********")

class TestSytLiveList(BaseTestCase):

    def checkout_flowplan_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy(**********, *********)
        self.refresh()
        self.sleep(3)
        # 新手引导
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(3)
        self.click("//span[contains(text(),'川流计划')]")

    @skip
    def test_chuanliu(self):
        self.checkout_flowplan_module()
        self.assert_no_404_errors()
        self.assert_element("//div[@class='module-left module-title']")
        account_proxy_app.account_proxy_remove(**********, *********)
    def close_popup_if_exists(self):
        try:
            # 等待弹出框出现
            popup_close_buttons = WebDriverWait(self.driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, '//*[@class="ant-modal-close-x"]'))
            )
            # 随机选择一个关闭按钮进行点击
            if popup_close_buttons:
                random.choice(popup_close_buttons).click()
                print("Closed the popup.")
        except Exception as e:
            print("No popup found or unable to close the popup:", e)

    def live_list(self):
        self.login2("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy(**********, *********)
        self.refresh()
        #self.close_popup_if_exists()
        try:
            self.click("(//span[contains(text(),'直播')])[1]")
            self.sleep(3)
            self.click("(//span[contains(text(),'直播列表')])[1]")
            self.sleep(3)
        except Exception as e:
            print("An error occurred while interacting with the live list:", e)

    def live_list(self):
        self.login("SYT_DOMAIN", "supply_account")
        # 点击服务 tab
        self.driver.maximize_window()
        self.refresh()
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)

        self.find_element("//span[contains(text(),'直播')]", by=By.XPATH).click()
        self.sleep(3)
        self.find_element("//span[contains(text(),'直播列表')]", by=By.XPATH).click()
        if self.is_element_clickable("(//span[contains(text(),'复盘诊断')])[5]"):
            self.find_element("(//span[contains(text(),'复盘诊断')])[5]", by=By.XPATH).click()
            self.sleep(3)
        else:
            self.click("(//span[contains(text(),'复盘诊断')])[6]")

    def test_zhenduan_jump(self):
        self.live_list()
        if self.is_element_visible("//span[contains(text(),'查看诊断说明')]"):
            self.click("//span[contains(text(),'查看诊断说明')]")
            self.switch_to_window(1)
            self.sleep(2)
            self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'直播诊断全新升级，助您提升直播流量')]")
        else:
            return

    def test_zhenduan_overview(self):
        self.live_list()

        self.assert_element("//span[contains(text(),'直播间成交金额')]",by=By.XPATH)
        self.assert_element("//span[contains(text(),'成交订单数')]",by=By.XPATH)
        self.assert_element("//span[contains(text(),'退款金额')]",by=By.XPATH)
        self.assert_element("//span[contains(text(),'直播诊断')]",by=By.XPATH)
        self.assert_element("//span[contains(text(),'时长诊断')]",by=By.XPATH)
        self.assert_element("//span[contains(text(),'流量诊断')]",by=By.XPATH)
       # self.assert_element("//span[contains(text(),'曝光转化判断')]",by=By.XPATH)

    def test_zhenduan_jump_chuanliu_1(self):
        self.live_list()
        if self.is_element_visible("//span[contains(text(),'去查看')]"):

            self.click("//span[contains(text(),'去查看')]")
            self.switch_to_window(1)
            self.sleep(3)
            self.assert_text("川流计划","//div[@class='module-left module-title']")
        else:
            return

    def test_zhenduan_jump_chuanliu_2(self):
        self.live_list()
        if self.is_element_visible("(//span[@class='kpro-data-diagnosis-suggestion-video-card-suggestion-content-link'][contains(text(),'提升攻略')])[8]"):

            self.click("(//span[@class='kpro-data-diagnosis-suggestion-video-card-suggestion-content-link'][contains(text(),'提升攻略')])[8]")
            self.switch_to_window(1)
            self.sleep(3)
            self.assert_element("(//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'“川流计划”商家运营指南')])[1]")
        else:
            return

    def test_zhenduan_jump_2(self):
        self.live_list()
        if self.is_element_visible("(//span[contains(text(),'去提升')])[2]"):
            self.click("(//span[contains(text(),'去提升')])[2]")
            self.switch_to_window(1)
            self.sleep(3)
            self.assert_element("//h2[contains(text(),'创建好运来抽奖活动')]")
        else:
            return















