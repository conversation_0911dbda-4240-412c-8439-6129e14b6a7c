# -*- coding: utf-8 -*-
"""
@Time ： 2024/6/18 4:18 PM
@Auth ： zhanle
@File ：test_new_homepage.py
@IDE ：PyCharm
"""
import random
from unittest import skip, skipIf
import pytest
from datetime import time
import time

from utils.http_help import BaseHttpRequest
from test_case.merchant_syt.base import BaseTestCase
account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class MyTestClass(BaseTestCase):

    def check_self_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)


    @pytest.mark.p0
    # 生意通新首页经营总览
    def test_homepage_overview(self):
        self.check_self_module()
        self.assert_text("经营概览", "//div[@class='normal-title']")
        real_info1 = self.get_text("//div[@class='overview-datacard']")
        self.assert_in("成交金额", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        real_info2 = self.get_text("//div[@id='overview-channel-cards']")
        self.assert_in("自营直播", real_info2)
        self.assert_in("自营短视频", real_info2)
        self.assert_in("达人合作", real_info2)
        self.assert_in("自营商品卡", real_info2)
        self.assert_element("//div[@class='trend-chart']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    # 生意通新首页经营总览-查看更多
    def test_homepage_todetail(self):
        self.check_self_module()
        self.click("(//span[contains(text(),'查看更多')])[1]")  #查看更多
        self.switch_to_window(1)
        self.assert_text('全店成交分析',"(//span[contains(text(),'全店成交分析')])[2]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    # 生意通新首页经营总览-切换时间选择器
    def test_homepage_selected(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 11:
            return
        self.check_self_module()

        self.click("//div[@class='btn-item selected']")
        self.click("//div[@class='btn-item'][contains(text(),'昨日')]")
        self.sleep(2)
        self.assert_element("//div[@class='overview-content-container']")
        self.click("//div[@class='btn-item'][contains(text(),'近30天')]")
        self.sleep(2)
        self.assert_element("//div[@class='overview-content-container']")
        self.click("//div[@class='kwaishop-data-realtime-pc-dropdown-trigger btn-item null']")
        self.sleep(1)
        self.click("(//span[contains(text(),'快手818上新季')])[1]")
        self.sleep(1)
        self.assert_element("//div[@class='overview-content-container']")
        self.click("(//span[contains(text(),'查看更多')])[1]")
        self.switch_to_window(1)
        self.assert_text("交易总览","//div[@class='module-left module-title']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    @pytest.mark.skip
    # 生意通新首页跳转到大促大屏
    def test_homepage_banner(self):
        self.check_self_module()
        self.click('//*[@id="driver-popover-item"]/div[4]/button')
        self.sleep(10)
        self.click("//div[@class='promotion-barnner-container-right']")  #banner数据详情
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//div[@class='bVTuGGXFcclDGQo9OZuw']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    # 生意通新首页经营诊断
    def test_homepage_diagnosis(self):
        self.check_self_module()

        self.assert_text("经营诊断","//span[contains(text(),'经营诊断')]")
        self.assert_element("//div[@class='tip-container']")  #诊断内容
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    # 生意通新首页经营诊断
    def test_homepage_diagnosis_suggestion(self):
        self.check_self_module()

        i = random.randint(1,5)
        self.click('//*[@id="home-management-diagnosis"]/div/div/div[2]/div[1]/div[1]/div[1]/div/div['+str(i)+']')
        j = random.randint(1,2)
        self.click('//*[@id="home-management-diagnosis"]/div/div/div[2]/div[2]/div['+str(j)+']')
        self.sleep(1)
        self.assert_element('//*[@id="Diagnosis_CardList_right"]')
        self.click('//*[@id="Diagnosis_CardList_left"]/div[1]/div[1]/div[2]/span')
        self.switch_to_window(1)
        self.assert_element('//*[@id="root"]')
        #account_proxy_app.account_proxy_remove(**********, **********)






