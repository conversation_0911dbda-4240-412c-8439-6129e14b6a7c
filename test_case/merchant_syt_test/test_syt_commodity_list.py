# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/18 3:57 PM
@Auth ： zhanle
@File ：test_syt_commodity_list.py
@IDE ：PyCharm
"""
import random
from unittest import skip

from ddt import ddt

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import time
from datetime import datetime, timedelta
import re

account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytShelf(BaseTestCase):

    # 商品
    def checkout_commoditylist_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        self.sleep(3)


        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.refresh()
        self.sleep(3)

        self.click("(//span[contains(text(),'商品')])[2]")
        self.sleep(5)
        #时间选择器弹窗
        if self.is_element_visible("(//button[contains(text(),'关闭')])[1]"):
            self.click("(//button[contains(text(),'关闭')])[1]")
        self.sleep(3)
        self.click("//div[@id='zones_goodsManagement_goods_list']")
        self.sleep(5)
        #预警弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(5)

    def checkout_commoditylist_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/goodsManagement/goods_list")
        self.sleep(5)
        # 预警弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")

        self.sleep(3)
        self.refresh()
        self.sleep(3)


    #实时
    #列表指标
    def test_commoditylist_overview(self):
        self.checkout_commoditylist_module_old()
        self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[2]/div[1]")
        self.sleep(5)

        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("商品总库存", real_info1)
        self.assert_in("操作", real_info1)

    #指标配置出现
    @pytest.mark.p0
    def test_commodtylist_configuration(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("可添加指标","//div[contains(text(),'可添加指标')]")

    #预警配置出现
    @pytest.mark.p0
    def test_commodtylist_alarm(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("//span[contains(text(),'预警配置')]")
        self.assert_text("预警配置","(//span[contains(text(),'预警配置')])[1]")


    #列表-默认首指标降序
    @pytest.mark.p0
    def test_commoditylist_amt_desc(self):
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.checkout_commoditylist_module()
        meynumber1 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-成交件数指标降序
    @pytest.mark.p0
    def test_commodity_list_amtnum_desc(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交件数')]")
        self.sleep(2)
        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交件数')]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[3]/div/div').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##列表-翻页
    @pytest.mark.p0
    def test_commoditylist_reverse(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text

        self.click(
                "//li[@title='下一页']//button[@type='button']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    ##列表-翻页
    @pytest.mark.p0
    def test_commoditylist_reverse_2(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text

        self.click(
                "//li[@title='3']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text
        self.sleep(2)
        substr1 = title1[:20]
        substr2 = title2[:20]
        assert substr1 != substr2

    ##列表-sku分析
    @pytest.mark.p0
    def test_commoditylist_jump_sku(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        self.click("(//span[contains(text(),'SKU分析')])[1]")
        self.sleep(5)
        self.assert_element("//span[contains(text(),'商品SKU明细')]")

    ##列表-流量分析
    @pytest.mark.p0
    def test_commoditylist_jump_traffic(self):
        self.checkout_commoditylist_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("//div[@id='goods_list_button_table']//span[contains(text(),'流量分析')]")
        self.sleep(5)
        self.assert_element("(//span[@customstyle='[object Object]'][contains(text(),'流量来源明细')])[1]")

    #离线-近7天
    #列表指标
    def test_commoditylist_overview_7day(self):
        self.checkout_commoditylist_module()
        self.click('/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[2]/div[3]')  # 近7天
        self.sleep(3)
        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品曝光人数", real_info1)
        self.assert_in("成交订单数（剔除退款）", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("操作", real_info1)

    #指标配置出现
    @pytest.mark.p0
    def test_commodtylist_configuration_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("可添加指标","//div[contains(text(),'可添加指标')]")

    #下载出现
    @pytest.mark.p0
    def test_commodtylist_download_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.assert_text("下载数据","//span[contains(text(),'下载数据')]")

    #列表-默认首指标降序
    @pytest.mark.p0
    def test_commoditylist_amt_desc_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-商品点击人数指标降序
    @pytest.mark.p0
    def test_commodity_list_amtnum_desc_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//span[contains(text(),'商品点击人数')]")
        self.sleep(2)
        self.click("//span[contains(text(),'商品点击人数')]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##列表-翻页
    @pytest.mark.p0
    def test_commoditylist_reverse_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text

        self.click(
                "//li[@title='下一页']//button[@type='button']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text
        self.sleep(2)
        substr1 = title1[:20]
        substr2 = title2[:20]
        assert substr1 != substr2

    ##列表-翻页
    @pytest.mark.p0
    def test_commoditylist_reverse_2_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text

        self.click(
                "//li[@title='3']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    ##列表-查看详情
    @pytest.mark.p0
    def test_commoditylist_jump_detail(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(5)
        self.assert_element("//span[contains(text(),'核心数据')]")

    ##列表-查看类目
    @pytest.mark.p0
    def test_commoditylist_category(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("(//div[@class='ant-select-selector'])[1]")
        self.click("//li[@title='手机/数码/电脑办公']")
        self.click("//li[contains(@title,'手机/配件')]")
        self.click("//li[contains(@data-path-key,'手机/数码/电脑办公__RC_CASCADER_SPLIT__手机/配件__RC_CASCADER_SPLIT__手机')]")
        self.sleep(5)
        title = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span').text

        assert title == '手机/数码/电脑办公/手机/配件/手机'

    ##列表-查看标签
    @pytest.mark.p0
    def test_commoditylist_target(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//span[@class='ant-select-selection-item']")
        self.assert_element("//div[@class='ant-select-item-option-content']") #近7天上架商品

    ##列表-二级售卖方式-自营
    @pytest.mark.p0
    def test_commoditylist_self(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click('//*[@id="pro-tag-form-wrapper"]/div[1]/div/div/div/div[2]/div[2]/div/div/div/div[2]')
        self.assert_element("(//div[contains(text(),'全部')])[3]")
        self.assert_element("//div[contains(text(),'自建自卖')]")
        self.assert_element("//div[contains(text(),'分销他人')]")

    ##列表-二级售卖方式-合作
    @pytest.mark.p0
    def test_commoditylist_cooperation(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("(//div[contains(@class,'')][contains(text(),'合作')])[2]")
        self.assert_element("(//div[contains(text(),'全部')])[3]")
        self.assert_element("(//div[contains(@class,'')][contains(text(),'达人合作')])[2]")
        self.assert_element("//div[contains(text(),'平台售卖')]")
        self.assert_element("//div[contains(text(),'合作二创')]")

    ##列表-自建自卖-流量分析查看趋势
    @pytest.mark.p0
    def test_commoditylist_jumptraffic_trend_self(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看趋势')])[1]")
        real_info1 = self.get_text("(//div[contains(@customstyle,'[object Object]')])[13]")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("成交件数", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("收藏人数", real_info1)
        self.assert_in("好评数", real_info1)
        self.assert_in("品退单量", real_info1)
        self.assert_element("(//div[contains(@class,'ant-spin-nested-loading')])[1]")  #趋势图

    ##列表-自建自卖-流量分析讲解回放
    @pytest.mark.p0
    def test_commoditylist_traffic_livevedio(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'讲解回放')])[1]"):
            self.click("(//span[contains(text(),'讲解回放')])[1]")
            self.sleep(2)
            self.assert_element("(//span[contains(text(),'商品直播高光切片')])[1]")

    ##列表-自建自卖-流量分析讲解回放-指标降序
    @pytest.mark.p0
    def test_commoditylist_traffic_livevedio_desc(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'讲解回放')])[1]"):
            self.click("(//span[contains(text(),'讲解回放')])[1]")
            self.sleep(2)
            self.assert_element("(//span[contains(text(),'商品直播高光切片')])[1]")
            meynumber1 = self.find_element(
                '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[1]/div[3]/div[5]/span[2]').text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(
                '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[2]/div[3]/div[5]/span[2]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2


    ##列表-自建自卖-流量分析讲解回放-翻页
    @pytest.mark.p0
    def test_commoditylist_traffic_livevedio_reverse(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'讲解回放')])[1]"):
            self.click("(//span[contains(text(),'讲解回放')])[1]")
            self.sleep(2)
            self.assert_element("(//span[contains(text(),'商品直播高光切片')])[1]")
            if self.is_element_visible("//li[contains(@aria-disabled,'false')]"):  # 有多页

                title1 = self.find_element(
                    '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[1]/div[3]/div[2]/span[2]').text

                self.click(
                    "//li[contains(@aria-disabled,'false')]")

                self.sleep(2)
                title2 = self.find_element(
                    '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[1]/div[3]/div[2]/span[2]').text
                self.sleep(2)
                substr1 = title1[:5]
                substr2 = title2[:5]
                assert substr1 != substr2

    ##列表-自建自卖-流量分析单个直播间讲解回放-指标降序
    @pytest.mark.p0
    def test_commoditylist_traffic_singlelivevedio_desc(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'讲解回放')])[2]"):
            self.click("(//span[contains(text(),'讲解回放')])[2]")
            self.sleep(2)
            meynumber1 = self.find_element(
                '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[1]/div[3]/div[5]/span[2]').text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(
                '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[2]/div[3]/div[5]/span[2]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2


    ##列表-自建自卖-流量分析单个直播间讲解回放-翻页
    @pytest.mark.p0
    def test_commoditylist_traffic_singlelivevedio(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'讲解回放')])[2]"):
            self.click("(//span[contains(text(),'讲解回放')])[2]")
            self.sleep(2)
            if self.is_element_visible("//li[contains(@aria-disabled,'false')]"):  # 有多页

                title1 = self.find_element(
                    '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[1]/div[3]/div[2]/span[2]').text

                self.click(
                    "//li[contains(@aria-disabled,'false')]")

                self.sleep(2)
                title2 = self.find_element(
                    '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div[3]/div[1]/div[3]/div[2]/span[2]').text
                self.sleep(2)
                substr1 = title1[:2]
                substr2 = title2[:2]
                assert substr1 != substr2


    ##列表-自建自卖-流量分析单个直播间查看详情
    @pytest.mark.p0
    def test_commoditylist_traffic_live(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("(//div[contains(text(),'自建自卖')])[1]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        self.click("//div[contains(@title,'直播间')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//span[contains(text(),'实时操盘系统')]")

    ##列表-自建自卖-流量分析单个直播间查看详情-抽屉
    @pytest.mark.p0
    def test_commoditylist_traffic_live_detail(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        self.click("//div[contains(@title,'直播间')]")
        self.sleep(2)
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.assert_element("(//span[contains(text(),'查看详情')])[5]")


    ##列表-自建自卖-流量分析单个短视频查看详情
    @pytest.mark.p0
    def test_commoditylist_traffic_photo(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//div[contains(@class,'')][contains(text(),'短视频')])[2]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        self.click("//div[contains(@title,'短视频')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//span[contains(text(),'短视频详情')]")

    ##列表-自建自卖-流量分析单个短视频查看详情-抽屉
    @pytest.mark.p0
    def test_commoditylist_traffic_photo_detail(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//div[contains(@class,'')][contains(text(),'短视频')])[2]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        self.click("//div[contains(@title,'短视频')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.assert_element("(//span[contains(text(),'查看详情')])[5]")

    ##列表-自建自卖-流量分析单个短视频查看详情-抽屉-播放
    @pytest.mark.p0
    def test_commoditylist_traffic_photo_detail_play(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'流量分析')])[1]")
        self.sleep(3)
        self.click("//div[contains(@title,'短视频')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.assert_element("(//span[contains(text(),'查看详情')])[5]")
            self.click('//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[1]')
            self.assert_element("//div[contains(@class,'iZoaXjeOlL1x09kIYLb8 react-draggable')]")


    #离线-近7天
    #sku
    def test_commoditydetail_sku_overview_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]')  # 近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'SKU分析')])[1]")
        self.sleep(3)
        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("排名", real_info1)
        self.assert_in("商品信息", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交金额（剔除退款）", real_info1)
        self.assert_in("操作", real_info1)

    #sku-查看趋势
    @pytest.mark.p0
    def test_commoditydetail_sku_trend_7day(self):
        self.checkout_commoditylist_module()
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div[2]/div[3]') #近7天
        self.sleep(3)
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'SKU分析')])[1]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看趋势')])[1]")
        self.assert_element("//span[contains(text(),'流量趋势')]")
        self.assert_element("(//span[contains(text(),'成交金额')])[1]")
        self.assert_element("(//span[contains(text(),'成交人数')])[1]")
        self.assert_element("(//span[contains(text(),'退款金额')])[1]")
        self.assert_element("(//span[contains(text(),'成交金额（剔除退款）')])[1]")
        self.assert_element("//div[@class='ant-spin-container']//div//div//div//canvas")

    #下载出现
    @pytest.mark.p0
    def test_commodtydetail_sku_download_1day(self):
        self.checkout_commoditylist_module()
        self.click("//div[contains(text(),'自营')]")
        self.sleep(2)
        self.click("//div[contains(text(),'自建自卖')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click("(//div[contains(text(),'SKU分析')])[1]")
        self.sleep(3)
        self.assert_text("下载数据","//span[contains(text(),'下载数据')]")






