# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/14 3:20 PM
@Auth ： zhanle
@File ：test_syt_commodity.py
@IDE ：PyCharm
"""
import random
from unittest import skip

from ddt import ddt

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import time
from datetime import datetime, timedelta
import re

account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytShelf(BaseTestCase):

    # 商品
    def checkout_commodity_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        self.sleep(3)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.refresh()
        self.sleep(3)

        self.click("(//span[contains(text(),'商品')])[2]")
        self.sleep(5)
        # 时间选择器弹窗
        if self.is_element_visible("(//button[contains(text(),'关闭')])[1]"):
            self.click("(//button[contains(text(),'关闭')])[1]")
        self.sleep(3)

    def checkout_commodity_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/goodsManagement/goods_overview")
        self.sleep(6)
        # 时间选择器弹窗
        if self.is_element_visible("(//button[contains(text(),'关闭')])[1]"):
            self.click("(//button[contains(text(),'关闭')])[1]")
        self.sleep(3)
        self.refresh()


    # 今日tab切换
    @pytest.mark.p1
    def test_timedata_todayday_tab_change_commodity(self):
        self.checkout_commodity_module()
        time.sleep(1)
        # 点击今日的tab
        self.click("//div[@class='btn-item selected']")
        time_yes = self.find_element('//*[@id="goods_overview_time"]/div/div/div[1]/span[2]/span').text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")

        # 获取今天的日期
        today = datetime.now().date()

        # 获取今天的日期
        assert today == element_datetime.date()

    #核心数据
    def test_commodity_overview(self):
        self.checkout_commodity_module_old()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品曝光人数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)


    #核心数据-直播间-全部、自营、合作
    def test_commodity_overview_live(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        self.click("(//span[contains(text(),'直播间')])[2]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)
        self.click("(//span[contains(text(),'自营')])[1]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)
        self.click("(//span[contains(text(),'合作')])[2]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)


    #核心数据-短视频-全部、自营、合作
    def test_commodity_overview_photo(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        self.click("(//span[contains(text(),'短视频')])[2]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)
        self.click("(//span[contains(text(),'自营')])[1]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)
        self.click("(//span[contains(text(),'合作')])[2]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)

    #核心数据-商品卡-全部、自营、合作
    def test_commodity_overview_goods(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        self.click("(//span[contains(text(),'商品卡')])[2]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)
        self.click("(//span[contains(text(),'自营')])[1]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)
        self.click("(//span[contains(text(),'合作')])[2]")
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交件数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("商品退款金额", real_info1)

    #指标配置出现
    @pytest.mark.p0
    def test_commodty_configuration(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("可添加指标","//div[contains(text(),'可添加指标')]")

    #流量来源-漏斗
    @pytest.mark.p0
    def test_commodity_cvr(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        real_info1 = self.get_text("//div[@class='kpro-data-funnel ']//*[name()='svg']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("点击成交率", real_info1)

    #流量来源-条数
    def test_commodity_source_count(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        elements = self.find_elements(
            "tr.ant-table-row.ant-table-row-level-0")
        if  len(elements) == 5:
            self.assert_true(len(elements) == 5)
        else:
            self.assert_true(len(elements) == 4)
        self.assert_true(all(item.text != "" for item in elements) == True)


    #流量来源-指标
    @pytest.mark.p0
    def test_commodity_list_detail(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("流量来源", real_info1)
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("点击成交率(人数)", real_info1)


    #流量来源-列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)
        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)
        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    #流量来源-自营直播间列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_live(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'自营直播间')])[1]")
        self.sleep(3)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##流量来源-自营直播间列表-翻页
    @pytest.mark.p0
    def test_commodity_list_amt_desc_live_reverse(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'自营直播间')])[1]")
        self.sleep(3)
        if self.is_element_visible("//li[@title='2']"):  # 有多页

            title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]').text

            self.click(
                "//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]').text
            self.sleep(2)
            substr1 = title1[:5]
            substr2 = title2[:5]
            assert substr1 != substr2

    #流量来源-自营短视频列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_photo(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'自营短视频')])[1]")
        self.sleep(3)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##流量来源-自营短视频列表-翻页
    @pytest.mark.p0
    def test_commodity_list_amt_desc_photo_reverse(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'自营短视频')])[1]")
        self.sleep(3)
        if self.is_element_visible("//li[@title='2']"):  # 有多页

            title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]').text

            self.click(
                "//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]').text
            self.sleep(2)
            substr1 = title1[:15]
            substr2 = title2[:15]
            assert substr1 != substr2

    #流量来源-商品卡列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_goods(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'商品卡')])[3]")
        self.sleep(3)

        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)
        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #流量来源-合作达人列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_daren(self):
        self.checkout_commodity_module()
        self.click(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'合作达人')])[1]")
        self.sleep(3)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##流量来源-合作达人列表-翻页
    @pytest.mark.p0
    def test_commodity_list_amt_desc_daren_reverse(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'合作达人')])[1]")
        self.sleep(3)
        if self.is_element_clickable("//li[@title='下一页']"):  # 有多页

            title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[1]/div').text

            self.click(
                "//li[@title='下一页']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[1]/div').text
            self.sleep(2)
            substr1 = title1[:5]
            substr2 = title2[:5]
            assert substr1 != substr2

    #流量来源-平台售卖列表
    @pytest.mark.p0
    def test_commodity_list_amt_desc_platform(self):
        self.checkout_commodity_module()
        self.click(
            "/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'合作达人')])[1]")
        self.sleep(3)
        if self.is_element_visible("//span[contains(text(),'【爆款竞价】')]"):
            self.click("//span[contains(text(),'【爆款竞价】')]")
            self.switch_to_window(1)
            self.sleep(3)
            self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'超级链接竞价招商手册')]")
        else:
            real_info1 = self.get_text("(//thead[@class='ant-table-thead'])[1]")
            self.assert_in("流量来源", real_info1)
            self.assert_in("商品成交人数", real_info1)
            self.assert_in("商品点击人数", real_info1)
            self.assert_in("点击成交率(人数)(%)", real_info1)
            self.assert_in("商品成交金额", real_info1)



    #核心数据
    def test_commodity_overview_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        real_info1 = self.get_text("//div[@class='ant-carousel']")
        self.assert_in("在售商品数", real_info1)
        self.assert_in("商品曝光人数", real_info1)
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("商品成交金额", real_info1)

    #核心数据
    def test_commodity_trend_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.assert_element("(//div[@customstyle='[object Object]'])[11]")

    #流量来源-漏斗
    @pytest.mark.p0
    def test_commodity_cvr_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        real_info1 = self.get_text("//div[@class='kpro-data-funnel ']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("商品曝光人数", real_info1)
        self.assert_in("点击成交率", real_info1)
        self.assert_in("曝光成交率", real_info1)
        self.assert_in("曝光点击率", real_info1)


    #流量来源-条数
    def test_commodity_source_count_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        elements = self.find_elements(
            "tr.ant-table-row.ant-table-row-level-0")
        if  len(elements) == 5:
            self.assert_true(len(elements) == 5)
        else:
            self.assert_true(len(elements) == 4)
        self.assert_true(all(item.text != "" for item in elements) == True)


    #流量来源-指标
    @pytest.mark.p0
    def test_commodity_list_detail_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("流量来源", real_info1)
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("商品成交金额", real_info1)
        self.assert_in("点击成交率(人数)", real_info1)


    #流量来源-列表-指标升序
    @pytest.mark.p0
    def test_commodity_list_amt_asc_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[5]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 < mey_number2


    #流量来源-自营直播间列表-曝光指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_live_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'自营直播间')])[1]")
        self.sleep(3)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##流量来源-自营直播间列表-翻页
    @pytest.mark.p0
    def test_commodity_list_amt_desc_live_reverse_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'自营直播间')])[1]")
        self.sleep(3)
        if self.is_element_visible("//li[@title='2']"):  # 有多页

            title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span').text

            self.click(
                "//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span').text
            self.sleep(2)
            substr1 = title1[:15]
            substr2 = title2[:15]
            assert substr1 != substr2

    #流量来源-自营短视频列表-曝光指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_photo_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'自营短视频')])[1]")
        self.sleep(3)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##流量来源-自营短视频列表-翻页
    @pytest.mark.p0
    def test_commodity_list_amt_desc_photo_reverse_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'自营短视频')])[1]")
        self.sleep(3)
        if self.is_element_visible("//li[@title='2']"):  # 有多页

            title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]').text

            self.click(
                "//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]').text
            self.sleep(2)
            substr1 = title1[:15]
            substr2 = title2[:15]
            assert substr1 != substr2

    #流量来源-商品卡列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_goods_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'商品卡')])[3]")
        self.sleep(3)

        self.click("(//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交人数')])[1]")
        self.sleep(2)
        self.click("(//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交人数')])[1]")
        self.sleep(2)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #流量来源-合作达人列表-指标降序
    @pytest.mark.p0
    def test_commodity_list_amt_desc_daren_7day(self):
        self.checkout_commodity_module()
        self.click('/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'合作达人')])[1]")
        self.sleep(3)

        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)
        self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'商品成交金额')]")
        self.sleep(2)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[5]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    ##流量来源-合作达人列表-翻页
    @pytest.mark.p0
    def test_commodity_list_amt_desc_daren_reverse_7day(self):
        self.checkout_commodity_module()
        self.click('/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'合作达人')])[1]")
        self.sleep(3)
        if self.is_element_visible("//li[@title='2']"):  # 有多页

            title1 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[1]/div').text

            self.click(
                "//li[@title='下一页']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div[2]/div[4]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[1]/div').text


            self.sleep(2)
            substr1 = title1[:5]
            substr2 = title2[:5]
            assert substr1 != substr2

    #流量来源-平台售卖列表
    @pytest.mark.p0
    def test_commodity_list_amt_desc_platform_7day(self):
        self.checkout_commodity_module()
        self.click('/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div/div[1]/div[2]/div/div[2]/div[3]')
        self.sleep(3)
        self.click("(//span[contains(text(),'平台售卖')])[1]")
        self.sleep(3)

        if self.is_element_visible("//span[contains(text(),'【爆款竞价】')]"):
            self.click("//span[contains(text(),'【爆款竞价】')]")
            self.switch_to_window(1)
            self.sleep(3)
            self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'超级链接竞价招商手册')]")
        else:
            real_info1 = self.get_text("(//thead[@class='ant-table-thead'])[1]")
            self.assert_in("流量来源", real_info1)
            self.assert_in("商品成交人数", real_info1)
            self.assert_in("商品点击人数", real_info1)
            self.assert_in("点击成交率(人数)", real_info1)
            self.assert_in("商品成交金额", real_info1)

