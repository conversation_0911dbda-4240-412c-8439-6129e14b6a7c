from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain




class BaseTestCase(BaseCase):

    def login(self, domain, account):


        # self.driver.quit()
        # self.driver = webdriver.Chrome()
        # self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        # self.maximize_window()

        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.sleep(1)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.sleep(1)
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])

        self.sleep(1)
        self.click("//*[@id='root']/div/div[2]/div/div/div/div[4]/form/div[3]/button")

        self.sleep(1)
        try:
             self.assert_text('选择您登录的身份')
             self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
             self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
             self.sleep(1)
             self.type("input[placeholder='请输入手机号']", account_data['account'])
             self.type("input[placeholder='请输入密码']", account_data['password'])

             self.sleep(1)
             self.click("//*[@id='root']/div/div[2]/div/div/div/div[4]/form/div[3]/button")
        except BaseException:
            print("Element does not exist")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')


    def login2(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
        self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")


    def map_user_id(self, user_id):
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.click('//*[@id="DropDownContainer"]/img')
        self.click('//*[@id="DropDownContainer"]/div/div/div/ul/div[2]/div[1]/span[2]')
        #self.type('#rc_select_0.syt-main-select-selection-search-input', user_id , retry=1)
        #self.sleep(2)
        #self.send_keys('#rc_select_0.syt-main-select-selection-search-input', '\ue007')
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/button[2]')


    def map_user_id_admin(self, user_id):
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.click('//*[@id="DropDownContainer"]/img')
        self.click('//*[@id="DropDownContainer"]/span')
        #self.type('#rc_select_0.syt-main-select-selection-search-input', user_id, retry=1)
        #self.sleep(2)
        #self.send_keys('#rc_select_0.syt-main-select-selection-search-input', '\ue007')
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/button[2]')


    def tanchuan(self):
        if self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(3)
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        elif self.is_element_visible("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']"):
            self.sleep(1)
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        else:
            self.assert_element("//div[@class='AOSjK37cO3N6aGXAjqF4']")

    def tanchuan_diagnose(self):
        if self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        else:
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")