# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/18 5:28 PM
@Auth ： zhanle
@File ：test_commodity_detail.py
@IDE ：PyCharm
"""
import random
from unittest import skip

from ddt import ddt

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import time
from datetime import datetime, timedelta
import re

account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytShelf(BaseTestCase):

    # 商品
    def checkout_commoditydetail_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        self.sleep(3)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.refresh()
        self.sleep(3)

        self.click("(//span[contains(text(),'商品')])[2]")
        self.sleep(5)

        #时间选择器弹窗
        if self.is_element_visible("(//button[contains(text(),'关闭')])[1]"):
            self.click("(//button[contains(text(),'关闭')])[1]")
        self.sleep(3)

        self.click("//div[@id='zones_goodsManagement_goods_list']")
        self.sleep(5)
        # 预警弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(5)

        self.click("/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div/div[2]/div[1]") #实时
        self.sleep(3)
        self.click("(//span[contains(text(),'SKU分析')])[1]")
        self.sleep(5)
        # 流量分析弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)

    def checkout_commoditydetail_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/goodsManagement/goods_list")
        self.sleep(10)
        # 预警弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(5)
        itemid = self.get_text('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[1]/div/span[1]')
        # print(live_id)
        self.open("https://syt.kwaixiaodian.com/zones/goodsManagement/goods_detail_overview?activeTab=goods_detail_SKU&itemId={}".format(itemid))
        self.sleep(5)
        # 流量分析弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)

    #实时
    #sku
    def test_commoditydetail_sku_overview(self):
        self.checkout_commoditydetail_module_old()

        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("排名", real_info1)
        self.assert_in("商品信息", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("操作", real_info1)

    #sku-指标配置出现
    @pytest.mark.p0
    def test_commodtydetail_sku_configuration(self):
        self.checkout_commoditydetail_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/div[1]")
        self.sleep(5)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("可添加指标","//div[contains(text(),'可添加指标')]")
        self.assert_text("成交金额","(//span[contains(text(),'成交金额')])[2]")

    #sku-成交金额标降序
    @pytest.mark.p0
    def test_commoditylist_sku_amt_desc(self):
        self.checkout_commoditydetail_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/div[1]")
        self.sleep(5)

        self.click("//span[contains(text(),'成交金额')]")
        self.sleep(2)
        self.click("//span[contains(text(),'成交金额')]")
        self.sleep(2)

        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[3]/div/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[3]/div/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #sku-查看趋势
    @pytest.mark.p0
    def test_commoditydetail_sku_trend(self):
        self.checkout_commoditydetail_module()
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/div[1]")
        self.sleep(5)

        self.click("(//span[contains(text(),'查看趋势')])[1]")
        self.assert_element("//span[contains(text(),'流量趋势')]")
        self.assert_element("(//span[contains(text(),'成交金额')])[1]")
        self.assert_element("(//span[contains(text(),'成交人数')])[1]")
        self.assert_element("(//span[contains(text(),'退款金额')])[1]")
        self.assert_element("//div[@class='ant-spin-container']//div//div//div//canvas")


    #商品概览
    #实时
    def test_commoditydetail_overview(self):
        self.checkout_commoditydetail_module()

        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/div[1]")
        self.sleep(5)
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("商品点击人数", real_info1)


    #指标配置出现
    @pytest.mark.p0
    def test_commodtydetail_configuration(self):
        self.checkout_commoditydetail_module()

        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/div[1]")
        self.sleep(5)
        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("实时指标","//div[@class='group-filter-modal-content-left-opt-item-head']")

    #漏斗
    @pytest.mark.p0
    def test_commoditydetail_cvr(self):
        self.checkout_commoditydetail_module()

        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)
        self.click(
            "/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/div[1]")
        self.sleep(5)
        real_info1 = self.get_text("//div[@class='kpro-data-funnel ']//*[name()='svg']")
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("点击成交率", real_info1)

    #离线-近7天
    #商品概览
    def test_commoditydetail_overview_1day(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品曝光人数", real_info1)
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("退款金额", real_info1)

    def test_commoditydetail_trend_1day(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)
        self.assert_element("//div[@class='ant-spin-container']")


    #漏斗
    @pytest.mark.p0
    def test_commoditydetail_cvr_1day(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)
        real_info1 = self.get_text("//div[@class='kpro-data-funnel ']//*[name()='svg']")
        self.assert_in("商品曝光人数", real_info1)
        self.assert_in("商品点击人数", real_info1)
        self.assert_in("商品成交人数", real_info1)
        self.assert_in("点击成交率", real_info1)
        self.assert_in("曝光点击率", real_info1)
        self.assert_in("曝光成交率", real_info1)

    #指标配置出现
    @pytest.mark.p0
    def test_commodtydetail_configuration_1day(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'商品概览')]")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("流量指标",'//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div/div[1]')
        self.assert_text("交易指标", '//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div/div[2]')
        self.assert_text("收藏加购", '//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div/div[3]')
        self.assert_text("退款指标", '//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div/div[4]')
        self.assert_text("售后指标", '//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div/div[5]')

    #流量分析
    #实时
    def test_commoditydetail_traffic_overview(self):
        self.checkout_commoditydetail_module()
        self.click("//div[@id='rc-tabs-0-tab-giids_detail_flow']//div[1]")  # 流量分析
        self.sleep(3)
        self.click('//*[@id="root"]/div/div/div[3]/div/div/div[1]/div[1]/div/div/div[2]/div[1]')  # 实时
        self.sleep(3)

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("操作", real_info1)

    #指标配置出现
    @pytest.mark.p0
    def test_commodtydetail_traffic_configuration(self):
        self.checkout_commoditydetail_module()
        self.click("//div[@id='rc-tabs-0-tab-giids_detail_flow']//div[1]")  # 流量分析
        self.sleep(3)
        self.click('//*[@id="root"]/div/div/div[3]/div/div/div[1]/div[1]/div/div/div[2]/div[1]')  # 实时
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("实时指标","//div[@class='group-filter-modal-content-left-opt-item-head']")

    #列表-默认首指标降序
    @pytest.mark.p0
    def test_commoditydetail_traffic_amt_desc(self):
        self.checkout_commoditydetail_module()
        self.click("//div[@id='rc-tabs-0-tab-giids_detail_flow']//div[1]")  #流量分析
        self.sleep(3)
        self.click('//*[@id="root"]/div/div/div[3]/div/div/div[1]/div[1]/div/div/div[2]/div[1]') #实时
        self.sleep(3)

        # self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[3]")
        # self.sleep(5)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[3]/div/div/div[1]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[3]/div/div/div[1]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #售后评价
    def test_commoditydetail_aftersale_overview(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'售后评价')]")
        self.sleep(3)

        real_info1 = self.get_text("(//div[@customstyle='[object Object]'])[14]")
        self.assert_in("商品差评率", real_info1)
        self.assert_in("商品质量差评率", real_info1)
        self.assert_in("商品品质退单率", real_info1)
        self.assert_in("商品自销品质退单率", real_info1)
        self.assert_in("商品他销品质退单率", real_info1)

    #售后评价-退款原因
    def test_commoditydetail_aftersale_refund(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'售后评价')]")
        self.sleep(3)
        real_info1 = self.get_text("(//thead[@class='ant-table-thead'])[1]")
        self.assert_in("品质退款原因", real_info1)
        self.assert_in("订单编号", real_info1)
        self.assert_in("退款单编号", real_info1)
        self.assert_in("退款描述", real_info1)

    #售后评价-差评原因
    def test_commoditydetail_aftersale_badcomment(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'售后评价')]")
        self.sleep(3)
        real_info1 = self.get_text("(//thead[@class='ant-table-thead'])[2]")
        self.assert_in("订单ID", real_info1)
        self.assert_in("质量评分", real_info1)
        self.assert_in("物流评分", real_info1)
        self.assert_in("服务评分", real_info1)
        self.assert_in("评价描述", real_info1)

    #人群画像
    def test_commoditydetail_people_overview(self):
        self.checkout_commoditydetail_module()
        self.click("//div[contains(text(),'人群画像')]")
        self.sleep(3)
        real_info1 = self.get_text("(//div[@customstyle='[object Object]'])[14]")
        self.assert_in("曝光用户", real_info1)
        self.assert_in("点击用户", real_info1)
        self.assert_in("加购用户", real_info1)
        self.assert_in("成交用户", real_info1)
        self.assert_in("点击未成交用户", real_info1)
        self.assert_in("加购未成交用户", real_info1)