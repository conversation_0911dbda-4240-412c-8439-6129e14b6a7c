# -*- coding: utf-8 -*-
"""
@Time ： 2024/9/19 10:55 AM
@Auth ： zhanle
@File ：test_syt_newplan.py
@IDE ：PyCharm
"""
from ddt import ddt
from unittest import skip, skipIf
from seleniumbase import BaseCase
import pytest
from test_case.merchant_syt.base import BaseTestCase
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import re
#
#
account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")

# @ddt
class MyTestClass(BaseTestCase):

    def prepare(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()

        self.sleep(3)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)

        self.click("(//span[contains(text(),'活动')])[1]")
        self.sleep(3)
        self.click("//span[contains(text(),'新品计划')]")  #新品计划
        self.sleep(3)

    #切换载体
    @pytest.mark.p0
    def test_tab_change_carriertype(self):
        self.prepare()
        self.click("(//div[@title='全部'][contains(text(),'全部')])[1]")
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'核心指标')])[1]")
        self.click("(//div[@title='短视频'])[1]")
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'核心指标')])[1]")
        self.click("(//div[@title='商品卡'])[1]")
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'核心指标')])[1]")

        #(**********, **********)

    #切换售卖方式
    @pytest.mark.p0
    def test_tab_change_tradetype(self):
        self.prepare()

        self.click("(//div[@title='全部'][contains(text(),'全部')])[2]")
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'核心指标')])[1]")
        self.click("(//div[@title='合作'])[1]")
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'核心指标')])[1]")
        #(**********, **********)

    #核心指标
    @pytest.mark.p0
    def test_cole_overview(self):
        self.prepare()
        real_info1 = self.get_text("(//div[@class='space-slider-item'])[1]")
        self.assert_in("活动商品曝光人数", real_info1)
        self.assert_in("活动商品曝光次数", real_info1)
        self.assert_in("活动商品点击人数", real_info1)
        self.assert_in("活动商品点击次数", real_info1)
        self.assert_in("曝光点击率（人数）", real_info1)
        self.click("//span[@aria-label='system-arrow-medium-right-line']")
        self.sleep(3)
        real_info2 = self.get_text("(//div[@class='space-slider-item'])[2]")
        self.assert_in("活动商品加购人数", real_info2)
        self.assert_in("活动商品成交人数", real_info2)
        self.assert_in("活动商品成交订单数", real_info2)
        self.assert_in("活动商品成交金额", real_info2)
        self.assert_in("点击成交率（人数）", real_info2)

    #趋势对比
    @pytest.mark.p0
    def test_trend_prepare(self):
        self.prepare()
        self.click("(//button[@role='switch'])[1]")  #趋势对比关闭
        self.sleep(1)
        self.click("(//button[@role='switch'])[1]")  # 趋势对比打开
        self.assert_element("(//canvas)[1]")
         

    #活动商品列表信息
    @pytest.mark.p0
    def test_list_info(self):
        self.prepare()
        list_info = self.get_text("(//thead[@class='ant-table-thead'])[1]")
        self.assert_in("排名", list_info)
        self.assert_in("商品信息", list_info)
        self.assert_in("活动商品曝光人数", list_info)
        self.assert_in("活动商品曝光次数", list_info)
        self.assert_in("活动商品点击人数", list_info)
        self.assert_in("活动商品点击次数", list_info)
        self.assert_in("曝光点击率（人数）", list_info)
        self.assert_in("活动商品加购人数", list_info)
        self.assert_in("活动商品成交人数", list_info)
        self.assert_in("活动商品成交订单数", list_info)
        self.assert_in("点击成交率（人数）", list_info)
        self.assert_in("活动商品成交金额", list_info)
        self.assert_in("活动商品退款订单数", list_info)
        self.assert_in("活动商品退款金额", list_info)
         

    # 指标排序
    @pytest.mark.p0
    def test_rank_amt(self):
        self.prepare()
        # 成交金额降序
        self.click("(//span[@class='ant-table-column-title-no-align'][contains(text(),'活动商品成交金额')])[1]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2
         

    # 商品弹窗出现
    @pytest.mark.p0
    def test_good_appear(self):
        self.prepare()
        self.click("(//div[@class='WMAh5R1RtlEUmigZdPMp'])[1]")
        self.sleep(2)
        self.assert_element("(//div[@class='EUnoMK92PHNn9yfhCBuE react-draggable'])[1]")
        self.click("(//span[@aria-label='system-close-small-line'])[1]")  #关闭弹窗
         

    # 商品信息 - 切页功能正常
    @pytest.mark.p1
    def test_click_title(self):
        self.prepare()
        title1 = self.find_element('//*[@id="root"]/div/div/div/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div/span').text

        self.click("//a[normalize-space()='2']")
        self.sleep(1)
        title2 = self.find_element('//*[@id="root"]/div/div/div/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div/span').text
        self.sleep(1)
        self.click("//span[@aria-label='system-arrow-large-right-line']")
        self.sleep(1)
        title3 = self.find_element('//*[@id="root"]/div/div/div/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div/span').text
        self.sleep(2)
        substr1 = title1[:22]
        substr2 = title2[:22]
        substr3 = title3[:22]
        assert substr1 != substr2 and substr3 != substr2
         

    # 商品列表-点击详情
    @pytest.mark.p1
    def test_click_detail(self):
        self.prepare()
        self.click("(//span[contains(text(),'详情')])[2]")
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_text("商品概览","//div[contains(text(),'商品概览')]")
         


    # 顶部-查看活动详情
    @pytest.mark.p1
    def test_top_click_detail(self):
        self.prepare()
        self.click("//span[contains(text(),'查看活动详情')]")
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_text("新品计划官方公告","//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'新品计划官方公告')]")
         

