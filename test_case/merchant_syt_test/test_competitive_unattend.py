# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/19 10:52 AM
@Auth ： zhanle
@File ：test_competitive_unattend.py
@IDE ：PyCharm
"""
import re
import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from selenium.common import NoSuchElementException

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")


@ddt

class TestCompetitiveUnattend(BaseTestCase):


    def competitive_assay_unattend(self):
        self.login("SYT_DOMAIN", "syt_account_wj")

        account_proxy_app.account_proxy(**********, *********)
        self.maximize_window()
        self.refresh()
        self.sleep(5)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(5)
        self.click("//span[contains(text(),'市场')]")


        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")   #商达榜单弹窗


        self.click("//span[contains(text(),'竞争分析')]")
        self.sleep(5)

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")



    # 出现-立即启动的按钮
    @pytest.mark.p1
    def test_unattend_appear_start_now(self):
        self.competitive_assay_unattend()
        time.sleep(1)
        self.assert_element('//*[@id="root"]/div/div/div[1]/button')
        account_proxy_app.account_proxy_remove(**********, *********)




    # 下方出现 使用说明文字
    @pytest.mark.p1
    def test_unattend_appear_explain_text(self):
        self.competitive_assay_unattend()
        time.sleep(1)
        self.assert_text("点击“立即启动”",'//*[@id="root"]/div/div/div[1]/span[2]')
        account_proxy_app.account_proxy_remove(**********, *********)