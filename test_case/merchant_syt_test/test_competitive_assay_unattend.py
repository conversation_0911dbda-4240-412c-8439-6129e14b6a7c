import re
import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from selenium.common import NoSuchElementException

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")


@ddt

class TestCompetitiveAssayUnattend(BaseTestCase):


    def competitive_assay_unattend(self):
        self.login("SYT_DOMAIN", "supply_account")

        account_proxy_app.account_proxy_confirm(**********, *********)
        self.maximize_window()
        self.refresh()
        self.sleep(5)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(5)
        self.click("//span[contains(text(),'市场')]")


        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")   #商达榜单弹窗


        self.click("//span[contains(text(),'竞争分析')]")
        self.sleep(5)

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")





    def competitive_assay_attend(self):
        self.login("SYT_DOMAIN", "supply_account")

        account_proxy_app.account_proxy_confirm(**********, *********)
        self.maximize_window()
        self.refresh()
        self.sleep(5)
        self.refresh()
        self.sleep(5)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.click("//span[contains(text(),'市场')]")
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
            self.click("//span[contains(text(),'竞争分析')]")
        else:self.click("//span[contains(text(),'竞争分析')]")
        self.sleep(7)





    # 相关提示文字出现
    @pytest.mark.p1
    def test_attend_appear_relative_text(self):
        self.competitive_assay_attend()
        time.sleep(3)
        self.assert_element('//*[@id="PageContain"]/div[1]/div[1]/div/div[1]/div/span')
        #account_proxy_app.account_proxy_remove(**********, **********)


    # 出现我的店铺 - 对比店铺
    @pytest.mark.p1
    def test_attend_appear_my_shop(self):
        self.competitive_assay_attend()
        self.assert_element("//div[@id='competition-shop-card-2']//div[@class='shop-card-content']")
        #account_proxy_app.account_proxy_remove(**********, **********)


    # 动销商品 - -合作达人数都有数据
    @pytest.mark.p1
    def test_attend_appear_number(self):
        self.competitive_assay_attend()
        time.sleep(1)
        number1 = self.find_element('//*[@id="competition-shop-card-2"]/div[2]/div/div[2]/div[1]/div[2]').text
        num1 = (int)(number1)
        assert num1 > 0
        number2 = self.find_element('//*[@id="competition-shop-card-2"]/div[2]/div/div[2]/div[2]/div[2]').text
        num2 = (int)(number2)
        assert num2 > 0
        #account_proxy_app.account_proxy_remove(**********, **********)


        # 成交金额指数tab切换
    @pytest.mark.p1
    def test_attend_trend_deal_money(self):
        self.competitive_assay_attend()
        time.sleep(3)

        self.click("//div[@title='成交金额指数']")
        time.sleep(1)
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)


    # 商品曝光次数指数tab切换
    @pytest.mark.p1
    def test_attend_trend_show_times(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click("//div[@title='商品曝光次数指数']")
        time.sleep(1)
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 商品访问次数指数tab切换
    @pytest.mark.p1
    def test_attend_trend_click_times(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click("//div[@title='商品访问次数指数']")
        time.sleep(1)
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 成交订单指数tab切换
    @pytest.mark.p1
    #@pytest.mark.skip
    def test_attend_trend_deal_count(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="PageContain"]/div[2]/div[2]/div/div[1]/div/div/div[4]')
        time.sleep(1)
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 曝光 - 点击率指数tab切换
    @pytest.mark.p1
    def test_attend_trend_show_and_click_times(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click("//div[@title='曝光-点击率']")
        time.sleep(1)
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 点击 - 支付率指数tab切换
    @pytest.mark.p1
    def test_attend_trend_click_and_pay(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click("//div[@title='点击-支付率指数']")
        time.sleep(1)
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)







    # 成交金额指数tab切换
    @pytest.mark.p1
    def test_attend_deal1_money(self):
        self.competitive_assay_attend()
        time.sleep(5)
        self.click("//span[contains(text(),'核心趋势')]")

        self.click("//div[@title='成交金额指数']")
        time.sleep(1)
        number = self.find_element('//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = int(round(float(number)))

        #num = (int)(number)
        assert  num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 商品曝光次数指数tab切换
    @pytest.mark.p1
    def test_attend_deal1_show_times(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[3]/div[2]/div/div[1]/div/div/div/div[2]')
        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 商品点击tab切换
    @pytest.mark.p1
    def test_attend_deal1_click_times(self):
        self.competitive_assay_attend()
        time.sleep(4)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')
        time.sleep(5)
        self.click('//*[@id="PageContain"]/div[3]/div[2]/div/div[1]/div/div/div/div[3]')
        time.sleep(5)

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = (int)(number)
        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 成交订单指数tab切换
    @pytest.mark.p1
    def test_attend_deal1_deal_count(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[3]/div[2]/div/div[1]/div/div/div/div[4]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = int(round(float(number)))

        #num = (int)(number)
        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 曝光 - 点击率指数tab切换
    @pytest.mark.p1
    def test_attend_deal1_show_and_click(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[3]/div[2]/div/div[1]/div/div/div/div[5]')
        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 点击 - 支付率指数tab切换
    @pytest.mark.p1
    def test_attend_deal1_click_and_pay(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[3]/div[2]/div/div[1]/div/div/div/div[6]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))
        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)





    # 点击趋势图出现图形
    @pytest.mark.p1
    def test_attend_deal_click_trend_pictur(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div/div/div/div/div/canvas')
        time.sleep(1)
        self.assert_element('//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div/div/div/div/div[1]')
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 点击 - 完整数据 - 出现弹窗
    @pytest.mark.p1
    def test_attend_deal_(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click("(//span[contains(text(),'完整数据')])[1]")
        number = self.find_element('//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = (int)(number)
        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)














# 流量数据
# 自营直播间tab切换
    @pytest.mark.p1
    def test_attend_deal_(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_FLOW_DATA"]')
        time.sleep(1)
        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[1]/div/div/div[2]')
        time.sleep(3)
        number = self.find_element(
            '//*[@id="PageContain"]/div[4]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = (int)(number)
        assert num >= 0

        # 自营短视频tab切换

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[1]/div/div/div[3]')
        time.sleep(3)

        number = self.find_element(
            '//*[@id="PageContain"]/div[4]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = (int)(number)
        assert num >= 0

        # 自营商品卡tab切换
        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[1]/div/div/div[1]')
        time.sleep(3)

        number = self.find_element(
            '//*[@id="PageContain"]/div[4]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        num = (int)(number)
        assert num >= 0
        #account_proxy_app.account_proxy_remove(**********, **********)





#  成交金额tab切换
    @pytest.mark.p1
    def test_attend_liuliang_deal_money(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[2]/div/div/div[1]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)

#  商品曝光次数tab切换
    @pytest.mark.p1
    def test_attend_liuliang_show_times(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[2]/div/div/div[2]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)

#  商品访问次数tab切换
    @pytest.mark.p1
    def test_attend_liuliang_visit_times(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[2]/div/div/div[3]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)

#  成交订单tab切换
    @pytest.mark.p1
    def test_attend_liuliang_already_do_deal(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[2]/div/div/div[4]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)

#  曝光-点击率tab切换
    @pytest.mark.p1
    @pytest.mark.skip
    def test_attend_liuliang_click_and_show(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[2]/div/div/div[5]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
        assert number != " "
        #account_proxy_app.account_proxy_remove(**********, **********)

#  点击-成交率tab切换
    @pytest.mark.p1
    def test_attend_liuliang_click_and_deal_finish(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click('//*[@id="PageContain"]/div[4]/div[2]/div/div[1]/div[2]/div/div/div[6]')

        number = self.find_element(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text

        assert number != " "
        #account_proxy_app.account_proxy_remove(**********, **********)


    @pytest.mark.p1
    def test_attend_liuliang_click_trend_pictur(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click(
            '//*[@id="PageContain"]/div[3]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div/div/div/div/div/canvas')
        time.sleep(1)
        self.assert_element('//*[@id="root"]/div[2]/div/div[2]/div/div[2]/div/div/div/div/div[1]')
        #account_proxy_app.account_proxy_remove(**********, **********)


    # 点击 - 完整数据 - 出现弹窗
    @pytest.mark.p1
    @pytest.mark.skip
    def test_attend_liuliang_click_full_data(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_TRADE_DATA"]')

        self.click("(//span[contains(text(),'完整数据')])[1]")
        number = self.find_element(
            '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div[1]').text
        number = number.replace(",", "")  # 去除逗号
        #num = (int)(number)
        num = int(round(float(number)))

        assert num > 0
        #account_proxy_app.account_proxy_remove(**********, **********)




    # 自营直播间tab切换
    @pytest.mark.p1
    def test_attend_top_ziying(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_ITEM_RANK"]')
        # 进了店铺页
        self.click('//*[@id="PageContain"]/div[5]/div[2]/div/div[1]/div[1]/div/div/div[2]')

        time.sleep(5)
        self.assert_element(
            '//*[@id="PageContain"]/div[5]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]')
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 自营短视频tab切换
    @pytest.mark.p1
    def test_attend_top_short_video(self):
        self.competitive_assay_attend()
        time.sleep(3)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_ITEM_RANK"]')
        # 进了店铺页
        self.click('//*[@id="PageContain"]/div[5]/div[2]/div/div[1]/div[1]/div/div/div[3]')
        time.sleep(5)
        self.assert_element('//*[@id="PageContain"]/div[5]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]')
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 自营商品卡tab切换
    @pytest.mark.p1
    def test_attend_top_card(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_ITEM_RANK"]')
        # 进了店铺页
        self.click('//*[@id="PageContain"]/div[5]/div[2]/div/div[1]/div[1]/div/div/div[4]')
        time.sleep(5)
        self.assert_element(
            '//*[@id="PageContain"]/div[5]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]')
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 达人合作tab切换
    @pytest.mark.p1
    def test_attend_top_promoter(self):
        self.competitive_assay_attend()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-tab-COMPETITION_ITEM_RANK"]')
        # 进了店铺页
        self.click('//*[@id="PageContain"]/div[5]/div[2]/div/div[1]/div[1]/div/div/div[5]')
        time.sleep(5)
        self.assert_element(
            '//*[@id="PageContain"]/div[5]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]')

        #account_proxy_app.account_proxy_remove(**********, **********)







