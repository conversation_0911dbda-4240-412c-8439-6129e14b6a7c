
import time
from datetime import datetime

import pytest
from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")


@ddt
class TestSpecific618Screen(BaseTestCase):

    #展示大促大屏
    def activity_618_screen(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #self.click('//*[@id="driver-popover-item"]/div[4]/button')
        self.click('//*[@id="kwaishop-syt-layout-container-new"]/div/header/div/div[1]/div[2]/div/span[1]/a/div/span')
        self.sleep(3)
        self.click("//div[@id='ttps:__zs.kwaixiaodian.com_realtime_operating_promotionShop?from=syt_top']")
        self.sleep(2)
        # self.switch_to_window(1)
        self.maximize_window()


    # 检验是否白屏
    @pytest.mark.p1
    def test_whole_entery_normal(self):
        self.activity_618_screen()
        self.assert_element(
            "//div[@id='activityBoardShopGmvRank']//div[@class='ant-spin-container']//div//div[@class='JV2SYmppjywdX0MsVJ87']")
        self.assert_element("//span[contains(text(),'大促趋势')]")
        self.assert_element("//div[@class='A5rIHeHwWjygtsIBTZ3e']")

    # 活动大屏 - 指标解释
    @pytest.mark.p1
    def test_explain_of_activity_right(self):
        self.activity_618_screen()
        self.click("//div[@class='IskptpEpeTJlsLoMygzW']")
        time.sleep(1)
        self.assert_text("指标解释", "//div[@id='rcDialogTitle0']")
        self.assert_element("(//td[contains(text(),'自营直播间成交金额')])[1]")
        self.assert_element(
            "//td[contains(text(),'统计周期内，本店被其他合作达人售卖自身店铺商品、及参与超链竞价活动被平台售卖的商品，所有支付成功的订')]")
        self.assert_element("//span[@class='ant-modal-close-x']")

    # 活动大屏 - 本店累计成交排行模块-本电成交排行
    @pytest.mark.p1
    def test_shop_deal_successful_rank_myselfshop(self):
        self.activity_618_screen()
        # 点击商品tab 检验元素
        self.click("//div[@class='_c_g9SoqKZEaeK4UXolA'][contains(text(),'商品')]")
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[1]/div[1]/div[1]/picture[1]/img[1]")
        time.sleep(1)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[3]")

    # 活动大屏 - 本店累计成交排行模块-商品tab
    @pytest.mark.p1
    def test_shop_deal_successful_rank_goods(self):
        self.activity_618_screen()

        # 点击商品tab 检验元素
        self.click("//div[@class='_c_g9SoqKZEaeK4UXolA'][contains(text(),'商品')]")
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[1]/div[1]/div[1]/picture[1]/img[1]")
        time.sleep(1)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[3]")

    # 活动大屏 - 本店累计成交排行模块-直播间tab
    @pytest.mark.p1
    def test_shop_deal_successful_rank_liveroom(self):
        self.activity_618_screen()
        self.click("//div[@class='ypz4at98nITpQedjEftv'][contains(text(),'直播间')]")
        time.sleep(1)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]")
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[3]")
        self.assert_text("开播时间",
                         "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]/div[2]/div[1]")

    # 活动大屏 - 本店累计成交排行模块-短视频tab
    @pytest.mark.p1
    def test_shop_deal_successful_rank_shortvideo(self):
        self.activity_618_screen()
        self.click("//div[@class='ypz4at98nITpQedjEftv'][contains(text(),'短视频')]")
        time.sleep(2)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]")
        time.sleep(1)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[3]")
        self.assert_text("发布时间",
                         "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]/div[2]/div[1]")

    # 活动大屏 - 本店累计成交排行模块-商品卡tab
    @pytest.mark.p1
    @pytest.mark.skip
    def test_shop_deal_successful_rank_goodcard(self):
        self.activity_618_screen()
        self.click("//div[@class='ypz4at98nITpQedjEftv'][contains(text(),'商品卡')]")
        time.sleep(1)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]")
        self.assert_text("商品ID",
                         "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]/div[3]/div[1]")
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[3]")

    # 活动大屏 - 本店累计成交排行模块-达人合作tab
    @pytest.mark.p1
    def test_shop_deal_successful_rank_cooperate(self):
        self.activity_618_screen()
        self.click("//div[@class='ypz4at98nITpQedjEftv'][contains(text(),'达人合作')]")
        time.sleep(1)
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]")
        self.assert_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[3]")
        self.assert_text("快手ID",
                         "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[1]/span[1]")

    # 活动大屏 - 更新时间
    @pytest.mark.p1
    def test_activity_update_time_right(self):
        self.activity_618_screen()
        eltext = self.find_element(
            "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/span[1]").text
        time_str = eltext.split(': ')[1]

        # 将时间字符串转换为 datetime 对象
        element_time = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

        # 获取当前时间
        current_time = datetime.now()
        time_difference = current_time - element_time
        time_difference_seconds = time_difference.total_seconds()
        # 计算时间差
        assert time_difference_seconds <10

    # 活动大屏 - 大促累积总成交金额
    @pytest.mark.p1
    def test_activity_sum_of_money_show(self):
        self.activity_618_screen()
        time.sleep(1)
        self.assert_element("//div[@class='A5rIHeHwWjygtsIBTZ3e']")
        text = self.find_element("//div[@class='ItdmOX25UrZ61B6kPztQ']").text
        text_without_commas = text.replace(",", "")
        number = float(text_without_commas)
        assert number >= 0


    # 活动大屏 - 大促趋势模块-总成交金额
    @pytest.mark.p1
    def test_activity_trend_money(self):
        self.activity_618_screen()
        time.sleep(1)
        self.click("(//div[@class='HyG34D1YD1dFCEwF92yz'])[1]")
        time.sleep(1)
        self.assert_element("//div[@class='V65ssZH5hcMTZbvqcTNT']//div//div//div//canvas")






    # 活动大屏 - 大促趋势模块-自营直播间成交金额
    @pytest.mark.p1
    def test_activity_trend_self_liveroom(self):
        self.activity_618_screen()
        time.sleep(1)
        self.click("(//div[@class='HyG34D1YD1dFCEwF92yz'])[2]")
        time.sleep(1)
        self.assert_element("//div[@class='V65ssZH5hcMTZbvqcTNT']//div//div//div//canvas")




    # 活动大屏 - 大促趋势模块-自营短视频成交金额
    @pytest.mark.p1
    def test_activity_trend_self_shortvideo(self):
        self.activity_618_screen()
        time.sleep(1)
        self.click("(//div[@class='mxzhMSuSZBbopZpDNthE'])[1]")
        time.sleep(1)
        self.assert_element("//div[@class='V65ssZH5hcMTZbvqcTNT']//div//div//div//canvas")

    # 活动大屏 - 大促趋势模块-自营商品卡成交金额
    @pytest.mark.p1
    def test_activity_trend_self_card(self):
        self.activity_618_screen()
        time.sleep(1)
        self.click("(//div[@class='mxzhMSuSZBbopZpDNthE'])[2]")

        time.sleep(1)
        self.assert_element("//div[@class='V65ssZH5hcMTZbvqcTNT']//div//div//div//canvas")

    # 活动大屏 - 大促趋势模块-达人分销成交金额
    @pytest.mark.p1
    def test_activity_trend_promoter(self):
        self.activity_618_screen()
        time.sleep(1)
        self.click("(//div[@class='mxzhMSuSZBbopZpDNthE'])[3]")
        time.sleep(1)
        self.assert_element("//div[@class='V65ssZH5hcMTZbvqcTNT']//div//div//div//canvas")

    # 活动大屏 - 今日数据
    @pytest.mark.p1
    @pytest.mark.skip
    def test_activity_click_today_data(self):
        self.activity_618_screen()
        time.sleep(1)
        # 点击 今日数据
        #self.click("//div[@class='sj9s3tanCYaNId8U0JNM']//div[@class='tBbtNutfCgaxeAQBMJFL']//div[1]")
        time.sleep(1)
        text1 = self.find_element("//div[@class='ItdmOX25UrZ61B6kPztQ']").text
        todaymk = text1.replace(",", "")
        # 拿到今日的营业额 判断他是不是大于零
        number1 = float(todaymk)
        assert number1>=0

        time.sleep(2)
        self.click("//div[@class='sj9s3tanCYaNId8U0JNM']//div[@class='tBbtNutfCgaxeAQBMJFL']//div[1]")
        time.sleep(1)
        text2 = self.find_element("//div[@class='ItdmOX25UrZ61B6kPztQ']").text
        allmk = text2.replace(",", "")
        # 拿到今日的营业额 判断他是不是大于零
        number2 = float(allmk)
        assert number2 >= 0 and number2 >= number1



    # 活动大屏 - 大促累积数据
    @pytest.mark.p1
    def test_activity_click_accumalate_data(self):
        self.activity_618_screen()
        time.sleep(1)
        # 点击 今日数据
        #self.click("//div[@class='sj9s3tanCYaNId8U0JNM']//div[@class='tBbtNutfCgaxeAQBMJFL']//div[1]")
        time.sleep(1)
        text1 = self.find_element("//div[@class='ItdmOX25UrZ61B6kPztQ']").text
        todaymk = text1.replace(",", "")
        # 拿到今日的营业额 判断他是不是大于零
        number1 = float(todaymk)
        assert number1 >= 0

        time.sleep(2)
        self.click("//div[@class='Cld1v8if_O70KQBoytgY']")
        time.sleep(1)
        text2 = self.find_element("//div[@class='ItdmOX25UrZ61B6kPztQ']").text
        allmk = text2.replace(",", "")
        # 拿到今日的营业额 判断他是不是大于零
        number2 = float(allmk)
        assert number2 >= 0

    # 活动大屏 - 全行业累计成交榜单 - 商家榜
    @pytest.mark.p1
    def test_activity_cut_seller_change_normal(self):
        global flag1,flag2, goodsname,sellername
        flag1 = False
        flag2 = False
        self.activity_618_screen()
        time.sleep(1)
        # 点击商品榜
        self.click('//*[@id="activityBoardIndustryGmvRank"]/div/div/div/div[2]/div[1]/div[1]/div[1]/div/div[2]')
        if self.is_element_visible("//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]"):
            goodsname = self.find_element("//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]/div[1]").text
            assert goodsname != " "
            flag1 = True
        else:
            time.sleep(1)

        # 点击商家榜
        self.click('//*[@id="activityBoardIndustryGmvRank"]/div/div/div/div[2]/div[1]/div[1]/div[1]/div/div[1]')
        if self.is_element_visible("//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]"):
            sellername = self.find_element("//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[1]/div[1]").text
            assert sellername != " "
            flag2 = True

        if flag1 and flag2:
            assert goodsname != sellername

    # 活动大屏 - 全行业累计成交榜单 - 商品榜
    @pytest.mark.p1
    def test_activity_cut_goods_change_normal(self):
        global flag1, flag2, goodsname, sellername
        flag1 = False
        flag2 = False
        self.activity_618_screen()
        time.sleep(1)
        # 点击商品榜
        self.click('//*[@id="activityBoardIndustryGmvRank"]/div/div/div/div[2]/div[1]/div[1]/div[1]/div/div[2]')
        if self.is_element_visible(
                "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]"):
            goodsname = self.find_element(
                "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[2]/div[1]").text
            assert goodsname != " "
            flag1 = True
        else:
            time.sleep(1)

        # 点击商家榜
        self.click('//*[@id="activityBoardIndustryGmvRank"]/div/div/div/div[2]/div[1]/div[1]/div[1]/div/div[1]')
        if self.is_element_visible(
                "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]"):
            sellername = self.find_element(
                "//body[1]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]/div[1]/div[1]/div[1]").text
            assert sellername != " "
            flag2 = True

        if flag1 and flag2:
            assert goodsname != sellername



    # 活动大屏 - 全行业累计成交榜单 - 类目切换-  切换到达人版大促大屏
    @pytest.mark.p1
    @pytest.mark.skip
    def test_change_edit_of_promoter(self):
        self.activity_618_screen()
        time.sleep(1)
        self.click("//div[@class='Cld1v8if_O70KQBoytgY']")
        time.sleep(1)


