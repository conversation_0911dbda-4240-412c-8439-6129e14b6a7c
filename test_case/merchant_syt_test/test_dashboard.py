# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/13 2:27 PM
@Auth ： zhanle
@File ：test_dashboard.py
@IDE ：PyCharm
"""
import random
import time

import pytest
from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import re


account_proxy_app = BaseHttpRequest(user_id="B_**********")

@ddt
class TestSytScreen(BaseTestCase):


    #登录直播大屏
    def live_screen_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'直播')])[1]")
        self.sleep(3)
        self.sleep(3)
        # meynumber1 = self.find_element(
        #     '//*[@id="root"]/div/div/div[2]/div[2]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[3]/div').text
        # match2 = re.findall(r'(\d+)', meynumber1)
        # mey_number1 = ""
        # for num in match2:
        #     mey_number1 += num
        # mey_number1 = int(mey_number1)
        #
        # if mey_number1 == 0:
        #     self.click("(//span[contains(text(),'大屏复盘')])[2]")  # 大屏诊断
        # else:
        #     self.click("(//span[contains(text(),'大屏复盘')])[1]")
        # self.sleep(3)
        self.click("(//span[contains(text(),'大屏复盘')])[3]")
        self.sleep(3)
        self.switch_to_window(1)
        self.maximize_window()

        if self.is_element_visible("//div[@class='ant-modal-content']"):
            self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
            self.sleep(3)

    def live_screen(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.open("https://syt.kwaixiaodian.com/zones/live/live_list")
        self.sleep(5)
        self.click("(//span[contains(text(),'大屏复盘')])[3]")

        self.switch_to_window(1)
        self.maximize_window()
        self.sleep(3)

        if self.is_element_visible("//div[@class='ant-modal-content']"):
            self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
            self.sleep(3)






    def test_homepage(self):
        self.live_screen_old()

        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("//span[contains(text(),'普通版')]")
        self.sleep(5)
        #切到普通版
        self.click("/html/body/div[2]/div/div/div/div/div/span[2]/img")
        self.sleep(2)

        self.assert_text("快手小店直播间成交金额","//span[@class='aAufyrth1v+WcDM12ljNiA==']")
        #self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[1]/div/div/div/div[2]')

        platform_text = self.get_text("//div[@class='BKxE4ip858pGOzHuIqhUvg==']")
        self.assert_in("新增粉丝数", platform_text)
        self.assert_in("成交订单数", platform_text)
        self.assert_in("成交人数", platform_text)
        self.assert_in("商品观看-成交率（人数）", platform_text)
        self.assert_in("千次观看成交", platform_text)
        self.assert_in("成交粉丝占比", platform_text)
        self.assert_in("粉丝复购率", platform_text)
        self.assert_in("人均观看时长", platform_text)
        self.assert_in("实时观看人数", platform_text)
        self.assert_in("累计观看人数", platform_text)
        #account_proxy_app.account_proxy_remove(**********, *********)


    def test_livereport(self):
        self.live_screen()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("//span[contains(text(),'普通版')]")
        self.sleep(5)
        self.click("/html/body/div[2]/div/div/div/div/div/span[2]/img")
        self.sleep(2)

        self.assert_element("//div[@class='pyqBZK2ethVC9b4ssB_s']")

        #account_proxy_app.account_proxy_remove(**********, *********)


    #大屏趋势图
    def test_trend(self):
        self.live_screen()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("//span[contains(text(),'普通版')]")
        self.sleep(5)
        self.click("/html/body/div[2]/div/div/div/div/div/span[2]/img")
        self.sleep(2)

        #self.click('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[2]/div/div/div/div/div[1]/div[1]/div[1]/div/div[1]')
        self.click("//span[@class='ant-radio']")
        self.assert_element("//div[@id='rc-tabs-1-panel-1']//div//div//canvas")
        self.click("//div[@class='ant-tabs-tab']")
        self.assert_element("//div[@id='rc-tabs-1-panel-2']//div//div//canvas")
        #account_proxy_app.account_proxy_remove(**********, *********)


    #大屏画像分析
    def test_analysis(self):
        self.live_screen()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("//span[contains(text(),'普通版')]")
        self.sleep(5)
        self.click("/html/body/div[2]/div/div/div/div/div/span[2]/img")
        self.sleep(2)

        self.click('/html/body/div[2]/div/div/div/div[2]/div/div/div[3]/button')  #弹窗
        self.click("//li[contains(text(),'累计成交金额')]")


        self.click("//li[contains(text(),'看播用户画像')]")

        self.click("//div[@class='swiper-slide swiper-slide-active']//li[@class='item'][contains(text(),'年龄')]")

        self.sleep(2)
        #self.find_element("(//div[@class='screen-preview-item'])[15]")
        self.click("//div[@class='swiper-slide swiper-slide-active']//li[@class='item'][contains(text(),'地域')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #观众来源分布
    def test_customerdistribution(self):
        self.live_screen()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("//span[contains(text(),'普通版')]")
        self.sleep(5)
        # 切到普通版
        self.click("/html/body/div[2]/div/div/div/div/div/span[2]/img")
        self.sleep(2)

        self.click("//div[@class='screen-pc-flow-button']")
        self.assert_element("//div[@class='kwaishop-data-realtime-components-pc-drawer-content']")
        self.assert_text("渠道名称","//div[@class='kwaishop-data-realtime-components-pc-drawer-header']")
        self.click("//span[@aria-label='system-close-medium-line']")
        self.assert_element("//div[@class='screen-preview__inner']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    def test_goodslist(self):
        self.live_screen()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("//span[contains(text(),'普通版')]")
        self.sleep(5)
        # 切到普通版

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[1]/div[5]/div/div')
        self.assert_element('/html/body/div[3]/div/div/div')
        self.assert_text('商品数据配置选项', "//div[@class='BT3lLmRwWN7XxIJtJ24cyQ==']")
        self.click("//button[@class='ant-btn ant-btn-primary vpVQCDB24hbgejYDPE80DQ==']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #操盘系统

    # def test_caopan_homepage(self):
    #     self.live_screen()
    #     self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
    #     self.click("//span[contains(text(),'专业版旧版')]")
    #
    #     self.sleep(3)
    #     if self.is_element_visible("/html/body/div[2]/div/div/div/div[2]"):
    #         self.click('/html/body/div[2]/div/div/div/div[2]/div/div/div[3]/button')
    #
    #     self.assert_element("//div[@class='TLWjQYvcH28cQnwEzWkA']")  #趋势图
    #     self.click("//div[@class='XuJA3azN_zTGG0XDUdUK']") #点击川流计划tab
    #     self.sleep(3)
    #     self.assert_element("//div[@class='XRbAVt7crMy734IzoqvQ']")
    #     self.assert_text("重要流量事件","//div[@class='Z8bAnpExVFeUYkO_UaTs']")
    #     self.assert_text("实时用户画像","//div[@id='protrait-card']")
    #     #流量渠道
    #     self.assert_element("//div[@id='screen-pc-flow-realtime']")
    #     self.click("//div[@class='screen-pc-flow-button']")
    #     self.sleep(3)
    #     self.assert_text("渠道名称","//div[contains(text(),'渠道名称')]")
    #     self.click("//div[@class='kwaishop-data-realtime-components-pc-drawer-mask']")
    #     self.sleep(2)
    #     self.click("//a[contains(text(),'流量作战室')]")
    #     self.switch_to_window(2)
    #     self.sleep(3)
    #     self.assert_text("直播间成交金额","//span[contains(text(),'直播间成交金额')]")
    #     self.sleep(3)
    #     self.switch_to_window(1)
    #     self.click("//a[contains(text(),'商品作战室')]")
    #     self.switch_to_window(3)
    #     self.sleep(3)
    #     self.assert_text("直播间成交金额","//span[contains(text(),'直播间成交金额')]")
    #     self.sleep(3)
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    # def test_caopan_goods(self):
    #     self.live_screen()
    #     self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
    #     self.click("//span[contains(text(),'专业版旧版')]")
    #
    #
    #     self.sleep(3)
    #     if self.is_element_visible("/html/body/div[2]/div/div/div/div[2]"):
    #         self.click('/html/body/div[2]/div/div/div/div[2]/div/div/div[3]/button')
    #
    #     self.click("//div[@class='AjeOU54ntIUayR_6MJ6U']//div[contains(text(),'商品')]") #商品作战室
    #     self.sleep(3)
    #     self.assert_text("商品", "//div[@class='I1MfuZFEZr33y1gMmLxU']")
    #     self.click("(//div[@class='data-screen__component__metric-config'])[1]") #指标配置
    #     self.sleep(3)
    #     self.click("(//span[@class='ant-checkbox ant-checkbox-checked'])[9]")
    #     self.sleep(3)
    #     self.click("(//button[@class='ant-btn ant-btn-primary'])[1]") #确定
    #     self.sleep(3)
    #     self.click("//div[@class='goods-download']") #下载
    #     self.assert_downloaded_file("safe.xls")
    #     self.delete_downloaded_file("safe.xls")
    #     self.assert_element("//div[@class='sigle-title']")
    #     self.click("//div[@class='HvkoDA_wambKYc5alpgF']//div[3]")  # 流量作战室
    #     self.sleep(3)
    #     if self.is_element_visible("//div[@class='kpro-data-doughnut-content']//div//div//canvas"):
    #         self.click("//span[@class='conversion-explanation']")  # 来源页面说明
    #         self.assert_element("//div[@class='ant-drawer-body']//img")
    #         self.click("//button[@aria-label='Close']")
    #         self.sleep(2)
    #         self.click("//div[@class='natural-source']//span[@class='ant-table-column-title'][contains(text(),'成交占比')]")  #升序
    #         self.sleep(2)
    #         self.click("//div[@class='all-button']")  #全部自然流量渠道
    #         self.click("//span[@class='ant-table-column-title-no-align'][contains(text(),'成交占比')]")  # 升序
    #         self.assert_element("(//div[@class='eYs3GjVdnGGQPYxEd2UB'])[1]")
    #     else:
    #         self.click("//span[@class='conversion-explanation']") #来源页面说明
    #         self.assert_element("//div[@class='ant-drawer-body']//img")
    #         self.click("//button[@aria-label='Close']")
    #         self.sleep(2)
    #         self.click("//div[@class='kwaishop-data-screen-pc-dropdown-selected']") #默认排序
    #         self.click("//div[@class='kwaishop-data-screen-pc-popup-container']//div[2]") #升序
    #
    #     self.sleep(2)
    #     self.click("//div[@class='radio-card-item']")  # 曝光人次
    #     self.assert_element("//div[@class='wkAJ6FxBTDL0eB3ua_mm']//div//div//div//canvas") #趋势图
    #
    #     self.click("//div[@id='screen-pc-flow-reservation-board-v2']//div[@class='tabs-container']//div[1]")  # 直播预约引流
    #     self.sleep(2)
    #     self.click("//div[@class='portrait-hover-pointer']")  # 查看用户画像详情数据
    #     self.sleep(1)
    #     self.assert_element("(//div[@class='flow-data-card'])[1]")
    #     self.click("//div[@class='ant-drawer-mask']")
    #     self.sleep(1)
    #     self.assert_element("(//div[@class='eYs3GjVdnGGQPYxEd2UB'])[5]")
    #     self.click("//div[@id='screen-pc-flow-marketing-board-v2']//div[@class='left-container']//div[2]")
    #     self.sleep(2)
    #     self.click("(//div[@class='ant-space-item'])[307]")  # 查看营销数据
    #     self.sleep(2)
    #     self.assert_element("//div[@class='lP5SifGg_AfuYPIbx2WU']")
    #     self.click('//*[@id="screen-pc-flow-marketing-board-v2"]/div[1]/div/div/div[2]/div/div/div[1]/div/div/span')
    #     self.click("(//div[@class='ant-space-item'])[306]")  # 去投放
    #     self.sleep(3)
    #     self.switch_to_window(2)
    #     self.assert_text("快分销", "//div[@class='R2pelbq1XUGwZ1BfAx5_']")
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    #全站商家流量作战室
    @pytest.mark.skip
    def test_caopan_traffic(self):
        self.live_screen_quanzhan()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("(//img)[35]")
        self.sleep(3)
        #account_proxy_app.account_proxy(**********, **********)
        self.click("//div[@class='HvkoDA_wambKYc5alpgF']//div[3]") #流量作战室
        self.sleep(3)
        self.click("//span[@class='conversion-explanation']") #来源页面说明
        self.assert_element("//div[@class='ant-drawer-body']//img")
        self.click("//button[@aria-label='Close']")
        self.sleep(2)
        self.click("//div[@class='all-button']") #全部自然流量渠道
        self.sleep(2)
        self.assert_element("//body/div/div/div/div/div[@role='tooltip']/div/div[1]")
        account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    def test_dacu(self):
        self.live_screen()
        self.click("//span[@class='w7ycqBQwxF8JpTLvi_oz']")
        self.click("/html/body/div[2]/div/div/div/div[2]/div/div/div/div[3]/div[1]/img")
        self.sleep(3)
        if self.is_element_visible("/html/body/div[2]/div/div/div/div[2]"):
            self.click('/html/body/div[2]/div/div/div/div[2]/div/div/div[3]/button')

        if self.is_element_visible("/div[contains(text(),'大促')]"):
            self.click("//div[contains(text(),'大促')]")
            self.switch_to_window(2)
            self.sleep(3)
            self.assert_element("//div[@class='bVTuGGXFcclDGQo9OZuw']//img")
        else:return







