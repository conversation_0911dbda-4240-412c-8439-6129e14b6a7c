# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/20 11:32 AM
@Auth ： zhanle
@File ：test_new_dashboard.py
@IDE ：PyCharm
"""
import random
import time

import pytest
from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest


account_proxy_app = BaseHttpRequest(user_id="B_**********")

@ddt
class TestSytScreen(BaseTestCase):


    #登录直播大屏
    def live_screen_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        # 新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        # 大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'直播')])[1]")
        self.sleep(10)
        self.click("(//span[contains(text(),'大屏复盘')])[1]")  #大屏诊断
        self.sleep(3)
        self.switch_to_window(1)
        self.maximize_window()

        if self.is_element_visible("//div[@class='ant-modal-content']"):
            self.click("//button[@class='ant-btn ant-btn-text']")  #跳过
        self.sleep(3)

    def checkout_live_fupan_new(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open('https://syt.kwaixiaodian.com/zones/live/live_list')
        self.sleep(6)
        # live_id = self.get_attribute('tr.kwaishop-tianhe-live_list-pc-table-row.kwaishop-tianhe-live_list-pc-table-row-level-0:nth-child(4)',"data-row-key")
        # print(live_id)
        #
        # self.open("https://syt.kwaixiaodian.com/zones/live/review?tab=coreData&liveId={}".format(live_id))
        # self.sleep(5)
        self.click("(//span[contains(text(),'大屏复盘')])[1]")  # 大屏诊断
        self.sleep(5)

        self.switch_to_window(1)
        self.maximize_window()

        if self.is_element_visible("//div[@class='ant-modal-content']"):
            self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
        self.sleep(3)


    def test_new_homepage(self):
        self.live_screen_old()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  #专业版新版
        self.sleep(3)

        self.assert_text("直播间成交金额", "//span[contains(text(),'直播间成交金额')]")
        # self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[1]/div/div/div/div[2]')

        platform_text = self.get_text("//div[@class='vQTRh42YF2v_qgLh3Cyd']")
        self.assert_in("直播曝光次数", platform_text)
        self.assert_in("曝光-点击率(次数)", platform_text)
        self.assert_in("累计观看人数", platform_text)
        self.assert_in("平均在线观看人数", platform_text)
        self.assert_in("次均观看时长", platform_text)
        self.assert_in("点赞率", platform_text)
        self.assert_in("千次观看成交", platform_text)
        self.assert_in("观看-成交率（人数）", platform_text)
        self.assert_in("退款金额", platform_text)

        self.click("//div[@class='CvnDisNjIQ5ReUkgn4g4']")  #设置
        self.assert_element("//div[@class='kwaishop-tianhe-screenMicro-pc-modal-content']")
        self.click("(//*[name()='svg'])[27]")  #取消指标
        #self.click("(//span)[185]")  #选择实时在线人数
        self.click("//span[contains(text(),'确 定')]")

        self.assert_element("//div[@id='screen-pc-gmv-panel']")
        #account_proxy_app.account_proxy_remove(**********, *********)


    def test_new_trend_1(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  #专业版新版
        self.sleep(3)

        self.assert_element("(//canvas)[1]")  #趋势图
        self.click("//span[contains(text(),'川流计划加持中')]")  #川流
        self.assert_text("川流计划加持中","//div[@class='Idqe601bTKUP54PdDZ3Z']")
        self.click("//span[contains(text(),'川流计划介绍')]")#川流计划介绍
        self.switch_to_window(2)
        self.sleep(3)
        self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'“川流计划”商家运营指南')]")
        self.switch_to_window(1)
        self.sleep(3)
        self.click("//span[contains(text(),'去数据分析')]")  #历史川流数据分析
        self.switch_to_window(2)
        self.sleep(3)
        self.assert_text("川流计划","//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'“川流计划”商家运营指南')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    def test_new_trend_liuliang(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click('//*[@id="rc-tabs-0-tab-flowAnalyse"]/div') #流量分析
        self.click("(//span[@class='UbAX7ZwEc6FCArEjCsWn'])[1]")  #流量来源说明
        self.assert_element("//div[@class='kwaishop-tianhe-screenMicro-pc-drawer-title']")
        self.click("//button[@aria-label='Close']//span[@aria-label='system-close-medium-line']")

        self.click("(//div[contains(@class,'tab-item')])[1]")  #曝光人次
        #self.assert_element("(//canvas)[2]")  #趋势图
        #account_proxy_app.account_proxy_remove(**********, *********)

    def test_new_trend_yinliu_ctr(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click('//*[@id="rc-tabs-0-tab-shortModule"]/div')  #引流短视频
        self.sleep(3)
        self.assert_text("短视频引流数据","//span[contains(text(),'短视频引流数据')]")
        self.click("//div[@class='OINcxboPtrC31Z8Wh51p']//div[1]//div[1]//picture[1]//img[1]")  #播放
        self.assert_element("//div[@class='kwaishop-tianhe-screenMicro-pc-modal-content']")
        self.click("//span[@class='kwaishop-tianhe-screenMicro-pc-modal-close-x']")  #叉号
        #account_proxy_app.account_proxy_remove(**********, *********)

    def test_new_trend_yinliu_amt(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click('//*[@id="rc-tabs-0-tab-shortModule"]/div')  # 引流短视频
        self.sleep(3)

        self.click("//body//div[@id='root']//div[@class='container___KVcrU']//div[@customstyle='[object Object]']//div[@customstyle='[object Object]']//div[@class='custom-tab-container-small']//div[1]//div[1]")   #引导支付金额由高到低
        self.click("(//img[@class='ma20UTeIM2KI9Ff4eWMh'])[1]")  #播放
        self.assert_element("//div[@class='kwaishop-tianhe-screenMicro-pc-modal-content']")
        self.click("//button[@aria-label='Close']")  #叉号
        #account_proxy_app.account_proxy_remove(**********, *********)

    def test_new_trend_yinliu_nums(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(5)

        self.click('//*[@id="rc-tabs-0-tab-shortModule"]/div')  # 引流短视频
        self.sleep(5)
        self.click("(//div[contains(@class,'tab-item')])[5]")  #引流直播间人次由高到低排序
        self.sleep(5)
        self.click("(//img[@class='ma20UTeIM2KI9Ff4eWMh'])[1]")  # 播放
        self.assert_element("//div[@class='kwaishop-tianhe-screenMicro-pc-modal-content']")
        self.click("//button[@aria-label='Close']")  # 叉号
        #account_proxy_app.account_proxy_remove(**********, *********)

    @skip
    def test_new_trend_yinliu_reservation(self):
        self.live_screen()
        self.click("//span[@id='screen_change_version']")
        self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click('//*[@id="rc-tabs-0-tab-shortModule"]/div')  # 引流短视频
        self.sleep(3)
        #account_proxy_app.account_proxy_remove(**********, *********)

    #营销投放
    def test_new_yingxiao(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)
        self.click("//div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'广告')]")  #左侧广告
        self.sleep(5)

        self.assert_text("营销投放数据","//div[@class='kpro-data-user-tag-screenContainer-title']")
        yingxiao_text = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("直播期间投放金额", yingxiao_text)
        self.assert_in("投放引导观看次数", yingxiao_text)
        self.assert_in("投放引导涨粉数量", yingxiao_text)
        self.assert_in("投放引导下单量", yingxiao_text)
        self.assert_in("投放引导成交金额", yingxiao_text)
        self.assert_in("直播期间投放ROI", yingxiao_text)

        self.click("//span[contains(text(),'查看含全站推广的营销数据')]")
        self.sleep(5)
        self.assert_text("直播间营销投放数据","//div[@class='kwaishop-tianhe-screenMicro-pc-drawer-title']")
        quanzhan_text = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("直播期间投放金额", quanzhan_text)
        #self.assert_in("投放引导曝光次数", quanzhan_text)
        self.assert_in("投放引导观看次数", quanzhan_text)
        self.assert_in("投放引导成交金额", quanzhan_text)
        self.assert_in("投放引导订单数", quanzhan_text)
        self.assert_in("全站推广订单成本", quanzhan_text)
        self.assert_in("全站推广ROI", quanzhan_text)
        feiquanzhan_text = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[3]")
        self.assert_in("直播期间投放金额", feiquanzhan_text)
        self.assert_in("投放引导观看次数", feiquanzhan_text)
        self.assert_in("投放引导涨粉量", feiquanzhan_text)
        self.assert_in("投放引导订单数", feiquanzhan_text)
        self.assert_in("投放引导成交金额", feiquanzhan_text)
        self.assert_in("直播期间投放ROI", feiquanzhan_text)
        #account_proxy_app.account_proxy_remove(**********, *********)


    #直播画面
    def test_new_video(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.assert_element("//video[@class='xplayer-video']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品
    def test_new_goods(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click("//div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'商品')]")  #点击商品
        self.sleep(5)
        self.assert_element("//span[contains(text(),'商品总览')]")
        self.assert_text("成交金额","(//span[contains(text(),'成交金额')])[1]")
        self.click("//button[normalize-space()='2']")  #滑动下边栏
        self.sleep(2)
        self.assert_text("讲解卡片成交商品件数","(//span[contains(text(),'讲解卡片成交商品件数')])[1]")
        self.click("//button[normalize-space()='3']")
        self.sleep(2)
        self.assert_text("成交人数","(//span[contains(text(),'成交人数')])[2]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品
    def test_new_goods_configuration(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click("//div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'商品')]")  #点击商品
        self.sleep(2)
        self.assert_element("//span[contains(text(),'商品总览')]")
        self.click("//span[@customstyle='[object Object]'][contains(text(),'指标配置')]")
        self.assert_element("//div[@role='document']")
        self.click("//button[@class='kwaishop-tianhe-screenMicro-pc-btn kpro-es-button ']")  #取消
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 商品
    def test_new_goods_explain(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click("//div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'商品')]")  # 点击商品
        self.sleep(2)
        explain_text = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("近5分钟成交金额", explain_text)
        self.assert_in("近5分钟商品点击次数", explain_text)
        self.assert_in("累计成交金额", explain_text)
        self.assert_in("未支付订单数", explain_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 人群
    def test_new_user(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click("//div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'人群')]")  #点击人群
        self.sleep(2)
        self.assert_element("//span[contains(text(),'当前看播用户画像')]")
        user_text = self.get_text("(//div[@customstyle='[object Object]'])[7]")
        self.assert_in("性别分布",user_text)
        self.assert_in("来源分布",user_text)
        self.assert_in("年龄分布", user_text)
        self.assert_in("粉丝分布", user_text)
        self.assert_in("常买品偏好", user_text)
        self.assert_in("地域分布", user_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 人群
    def test_new_buyer(self):
        self.live_screen()
        #self.click("//span[@id='screen_change_version']")
        #self.click("//div[@class='Dzb9bMLjAszTd4wfPBg9']//div[1]//div[1]//img[1]")  # 专业版新版
        self.sleep(3)

        self.click("//div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'人群')]")  #点击人群
        self.sleep(2)

        self.click("//span[contains(text(),'成交用户画像')]")
        buyer_text = self.get_text("(//div[@customstyle='[object Object]'])[7]")
        self.assert_in("性别分布", buyer_text)
        self.assert_in("来源分布", buyer_text)
        self.assert_in("年龄分布", buyer_text)
        self.assert_in("粉丝分布", buyer_text)
        self.assert_in("常买品偏好", buyer_text)
        self.assert_in("地域分布", buyer_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_new_dacu(self):
        self.live_screen()
        self.sleep(3)

        if self.is_element_visible("/div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'大促')]"):
            self.click("/div[@class='CTj6XtpHsh2Nf_1di1hB'][contains(text(),'大促')]")
            self.switch_to_window(2)
            self.sleep(3)
            self.assert_element("//div[@class='bVTuGGXFcclDGQo9OZuw']//img")
        else:
            return





