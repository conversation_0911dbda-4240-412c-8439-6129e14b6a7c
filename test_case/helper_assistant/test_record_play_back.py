"""
# Author     ：author wb_<PERSON><PERSON>jingjing03
# Description：
"""
import time

import pytest
from ddt import ddt

from test_case.helper_assistant.load.load_record_play_back import LoadRecordPlayBackPage


@ddt
class TestLoadRecordPlayBackPage(LoadRecordPlayBackPage):
    #@pytest.mark.skip
    # 校验讲解回放管理页展示
    def test_check_record_playback_page(self):
        self.check_record_playback_page()

    #@pytest.mark.skip
    # 查询讲解回放短视频-视频id校验查询
    def test_check_search_video(self):
        self.check_search_video()

    #@pytest.mark.skip
    # 查询讲解回放短视频-商品id校验查询
    def test_check_search_item(self):
        self.check_search_item()

    #@pytest.mark.skip
    # 录制时间查询
    def test_check_search_record_time(self):
        self.check_search_record_time()

    #@pytest.mark.skip
    # 自动发布短视频去授权按钮
    def test_check_auto_publish_video_button(self):
        self.check_auto_publish_video_button()

    @pytest.mark.skip
    # 全部tab下讲解回放视频前台展示
    def test_check_front_desk_display(self):
        self.check_front_desk_display()

    #@pytest.mark.skip
    # 全部tab 数据check
    def test_check_total_tab(self):
        self.check_total_tab()

    #@pytest.mark.skip
    # 已剪辑tab 数据check
    def test_check_video_clips_tab(self):
        self.check_video_clips_tab()

    #@pytest.mark.skip
    # 已发布tab 数据check
    def test_check_publish_tab(self):
        self.check_publish_tab()
