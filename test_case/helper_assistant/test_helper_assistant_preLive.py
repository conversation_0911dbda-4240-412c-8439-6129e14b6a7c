"""
# Author     ：author <PERSON><PERSON><PERSON><PERSON><PERSON>
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from test_case.helper_assistant.load.load_helper_assistant_preLive import LoadHelperAssistantPreLive
from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env

# from .load_helper_assistant_preLive import LoadHelperAssistantPreLive

# m_load_pre_live_v2: LoadHelperAssistantPreLive = LoadHelperAssistantPreLive()


zs_live_assistant_history_list = 'https://zs.kwaixiaodian.com/page/list'
zs_live_assistant_page_set = 'https://zs.kwaixiaodian.com/page/set'


@ddt
class TestHelperAssistantPreLiveV2(LoadHelperAssistantPreLive):

    # 进行初始化和清理工作
    def setUp(self):
        super().setUp()
        print("我是TestHelperAssistantPreLiveV2 setUp方法，进行测试前的初始化工作")
        # 浏览器最大化
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "helper_assistant_preLive")
        sleep(4)
        # 跳过新手引导
        self.skip_guider()

    def tearDown(self):
        super().tearDown()
        print("我是TestHelperAssistantPreLiveV2 tearDown方法，执行测试后的清除工作")

    # 跳过新手引导
    # @pytest.mark.skip
    def test_checkout_new_guide(self, selector=None):
        # 跳过新手引导
        self.skip_guider()

    # 校验预开播跟播助手顶部
    # @pytest.mark.skip
    def test_checkout_pre_guide(self, selector=None):
        # 校验预开播页任务区
        self.check_pre_guide()

    # 校验小黄车列表按钮
    # @pytest.mark.skip
    def test_checkout_shop_car_title(self, selector=None):
        # 判断小黄车管理标题区域
        self.check_shop_car_title()

    # 测试智能讲解
    # @pytest.mark.skip
    def test_check_intelligent_explanation(self, selector=None):
        # 测试智能讲解
        self.check_intelligent_explanation()

    # 点击添加小黄车商品弹窗
    # @pytest.mark.skip
    def test_check_add_item_popup(self, selector=None):
        # 测试添加商品弹窗
        self.check_add_shop_car_item_popup()

    # 校验推荐商品
    def test_recommend_item(self):
        self.recommend_item()

    # 判断小黄车列表
    def test_check_onsale_item_list(self, selector=None):
        self.check_onsale_item_list()

    # 校验卖点弹窗
    def test_check_selling_point_popup(self):
        self.check_selling_point_popup()

    # 新增/修改卖点
    def test_set_selling_point(self):
        self.set_selling_point()

    # 直播-跳过新手引导-校验提词半屏
    def testk_prompt_button(self, selector=None):
        self.check_prompt_page()

    # 直播-跳过新手引导-判断题词设置半屏
    # @pytest.mark.skip
    def test_prompt_set(self, selector=None):
        self.check_prompt_set()

    # 更新提词信息
    def test_change_prompt(self):
        self.change_prompt()

    # 更新提词信息点击取消-不保存，校验是否保存
    def test_check_cancel_change_prompt(self):
        self.check_cancel_change_prompt()

    # 检验尺码表半屏
    def test_chcek_size_chart_page(self):
        self.chcek_size_chart_page()

    # 设置尺码表
    def test_set_size_chart(self):
        self.set_size_chart()

    # 直播-跳过新手引导-校验待上车列表按钮
    # @pytest.mark.skip
    def test_checkout_wait_shop_car_title(self, selector=None):
        # 判断小黄车管理标题区域
        self.check_wait_shop_car_title()

    # 直播-跳过新手引导-判断待上车列表
    # @pytest.mark.skip
    def test_check_wait_onsale_item_list(self, selector=None):
        self.wait_onsale_item_list()

    # 待上车商品上车
    def test_wait_on_sale_add(self):
        self.wait_on_sale_add(5)

    # 待上车商品全部下车
    def test_wait_on_sale_total_get_off(self):
        self.wait_on_sale_total_get_off()
        self.wait_on_sale_add(5)

    # 待上车校验编辑半屏
    def test_check_wait_onsale_edit(self):
        self.check_wait_onsale_edit()

    # 待上车编辑半屏中，商品优惠-限时秒杀
    def test_wait_onsale_edit_seckill(self):
        self.wait_onsale_edit_seckill()

    # 待上车编辑半屏中，商品优惠-福利购
    def test_wait_onsale_edit_fuligou(self):
        self.wait_onsale_edit_fuligou()

    # 直播-跳过新手引导-判断历史直播列表
    # @pytest.mark.skip
    def test_check_live_assistant_history_list(self, selector=None):
        self.open(zs_live_assistant_history_list)
        sleep(1)
        self.refresh_page()
        sleep(1)
        self.check_live_assistant_history_list()

    # 直播-跳过新手引导-判断历史直播创建计划
    # @pytest.mark.skip
    def test_check_live_assistant_history_create_plan(self, selector=None):
        self.open(zs_live_assistant_history_list)
        sleep(1)
        self.refresh_page()
        sleep(1)
        self.check_live_assistant_history_create_plan()

    # 历史直播-查看直播详情
    def test_check_history_live_detail(self):
        self.open(zs_live_assistant_history_list)
        sleep(1)
        self.refresh_page()
        sleep(1)
        self.check_history_live_detail()

    # 直播-跳过新手引导-进入直播设置页
    # @pytest.mark.skip
    def test_check_live_assistant_page_set(self, selector=None):
        self.open(zs_live_assistant_page_set)
        sleep(1)
        self.refresh_page()
        sleep(1)
        self.check_live_assistant_page_set()

    # 直播-跳过新手引导-进入直播设置-商品设置-尺码表展示
    def test_check_live_assistant_page_set_size_display(self, selector=None):
        self.open(zs_live_assistant_page_set)
        sleep(2)
        self.refresh_page()
        self.check_live_assistant_page_set_size_display()

    # 直播-跳过新手引导-进入直播设置-商品设置-商品参数展示
    def test_check_live_assistant_page_set_product_display(self, selector=None):
        self.open(zs_live_assistant_page_set)
        sleep(2)
        self.refresh_page()
        self.check_live_assistant_page_set_product_display()

    # 直播-跳过新手引导-进入直播设置-商品设置-上车并讲解设置
    def test_check_live_assistant_page_set_onCart_explain(self, selector=None):
        self.open(zs_live_assistant_page_set)
        sleep(2)
        self.refresh_page()
        self.check_live_assistant_page_set_onCart_explain()

    # 直播-跳过新手引导-进入直播设置-商品设置-直播商品保存
    def test_check_live_assistant_page_set_live_product_sive(self, selector=None):
        self.open(zs_live_assistant_page_set)
        sleep(2)
        self.refresh_page()
        self.check_live_assistant_page_set_live_product_sive()

    # 直播设置-上车位置设置
    def test_check_onSale_position(self, selector=None):
        self.open(zs_live_assistant_page_set)
        sleep(2)
        self.refresh_page()
        self.check_onSale_position()

    # @pytest.mark.skip
    # 直播-跳过新手引导-进入直播设置页-判断贴纸状态
    def test_stickers_live_notice(self):
        self.open(zs_live_assistant_page_set)
        self.stickers_live_notice()

    # 贴纸设置-动图设置
    def test_stickers_yellowCart_gif(self):
        self.open(zs_live_assistant_page_set)
        self.stickers_yellowCart_gif()

    # 校验商品置顶功能
    def test_check_item_top(self):
        self.check_item_top()

    # 校验回放管理半屏页
    def test_check_record_manage_page(self):
        self.check_record_manage_page()

    # 校验价格库存弹窗
    def test_check_change_priceAndINV(self):
        self.check_change_priceAndINV()

    # 更改价格
    def test_change_price(self):
        self.change_price()

    # 校验修改库存
    def test_change_price_inv(self):
        self.change_INV()

    # 校验上车顺序
    def test_check_onSale_order(self):
        self.check_onSale_order()

    # 商品id搜索
    def test_check_search_itemID(self):
        self.check_search_itemID()

    # 商品序号搜索
    def test_check_search_order(self):
        self.check_search_order()

    # 全选按钮校验
    def test_check_all_button(self):
        self.check_all_button()

    # 规格明细
    def test_check_sku_button(self):
        self.check_sku_button()

    # 贴纸-服务保障
    def test_stickers_guarante_notice(self):
        self.open(zs_live_assistant_page_set)
        self.stickers_guarante_notice()

    # 贴纸-直播福利
    def test_stickers_live_benefit(self):
        self.open(zs_live_assistant_page_set)
        self.stickers_live_benefit()

    # 贴纸-主播信息
    def test_stickers_seller_info(self):
        self.open(zs_live_assistant_page_set)
        self.stickers_seller_info()

    # 贴纸-降温公告
    def test_stickers_temperature_drop(self):
        self.open(zs_live_assistant_page_set)
        self.stickers_temperature_drop()

    # 选品上车
    def test_on_live_add_item(self):
        self.on_live_add_item(3)

    # 全选商品下车
    def test_onSale_total_getOff(self):
        self.onSale_total_getOff()
        self.on_live_add_item(3)


@ddt
class TestHelperAssistantPreLiveV2Child(TestHelperAssistantPreLiveV2):
    def test_child_login(self):
        # 登陆子账号
        self.child_login("WB_DOMAIN", "detest017_75")
        sleep(2)
