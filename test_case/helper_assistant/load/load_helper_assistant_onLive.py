import time
from time import sleep

from selenium.webdriver.common.by import By
from seleniumbase.common.exceptions import NoSuchElementException, ElementNotVisibleException

from test_case.helper_assistant.base import BaseTestCase


class LoadHelperAssistantOnLive(BaseTestCase):

    # 浏览器最大化，登录跟播助手
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "helper_assistant_onLive")

    def skip_guider(self):
        # 刷新当前页面
        self.refresh_page()
        sleep(1)

    def check_isLive(self):
        try:
            # notLive = self.find_element('title2--Lesik', By.CLASS_NAME).text
            # 本地运行没问题，服务器报错，尝试换xpath定位元素
            notLive = self.find_element('//*[@id="main-container"]/section/main/div/div/div[1]/div/div[1]/div/span',By.XPATH).text
            if notLive == "未开播" or notLive == '非电商开播':  # 前置条件不满足
                self.skip("未开播，跳过本次测试")
        except NoSuchElementException:
            elementText = self.find_element('live-time--AHjj1', By.CLASS_NAME).text.split(' ')[0]
            assert elementText == '已播'


    # 判断顶部开播信息
    def check_top_LiveInfo(self):
        # 校验开播
        liveTitle = self.find_element('title2--Lesik', By.CLASS_NAME)
        assert liveTitle != '未开播'
        elementText = self.find_element('live-time--AHjj1', By.CLASS_NAME).text.split(' ')[0]
        assert elementText == '已播'
        self.find_element("//span[text()='商品讲解率']")
        # 校验直播设置
        self.assert_text('直播设置', 'set--j9neo', By.CLASS_NAME)

    # 判断顶部直播设置
    def check_live_set(self):
        self.assert_text('直播设置', 'set--j9neo', By.CLASS_NAME)
        self.hover('set--j9neo', By.CLASS_NAME)
        self.click("//div[text()='直播间装修工具']")
        time.sleep(1)
        checkText = self.find_elements('ant-tabs-tab-btn', By.CLASS_NAME)
        assert checkText[0].text == '公告板'
        assert checkText[1].text == '贴纸'

    # 校验小黄车列表顶部按钮
    def check_shop_car_title(self):
        time.sleep(1)
        self.assert_text('小黄车商品', 'ant-badge.badge--bodTX', By.CLASS_NAME)
        self.assert_text('待上车商品',
                         '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[2]/div[1]/div[1]/div[2]')
        self.assert_text('历史上车商品',
                         '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[2]/div[1]/div[1]/div[3]')
        self.assert_text('全选', '//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[1]/label/span[2]')
        # self.assert_text('搜索', 'text--aEIsb', By.CLASS_NAME)
        elements = self.find_elements('name--urwBR', By.CLASS_NAME)
        # 部分商家有闪电购全选
        if len(elements) == 3:
            for i in range(len(elements)):
                textList = ['智能讲解', '刷新', '添加上车商品']
                assert elements[i].text == textList[i]
        elif len(elements) == 4:
            for i in range(len(elements)):
                textList = ['智能讲解', '闪电购', '刷新', '添加上车商品']
                assert elements[i].text == textList[i]

    # 直播中-添加商品上车 count=上车数量
    def on_live_add_item(self, count):
        itemList = self.get_itemList(0)
        if itemList:
            self.onSale_total_getOff()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[2]/div/div[4]/span/span')
        time.sleep(1)
        # 获取商品主图 用于点击勾选
        imgElements = self.find_elements('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        if imgElements:
            # 获取商品id
            itemIdElements = self.find_elements('ant-typography.id-text--R02TA', By.CLASS_NAME)
            itemIdText = []
            addItemId = []
            # 点击主图进行勾选
            for i in range(count):
                imgElements[i].click()
                itemIdText.append(itemIdElements[i].text)
                addItemId.append(itemIdText[i].split(' ')[1])
            self.click("//span[text()='更新小黄车']")
            time.sleep(2)
            itemList = self.get_itemList(0)
            cartItemId = []
            # 这里还需判断下上车顺序
            for i in range(len(itemList)):
                cartItemId.append(itemList[i].split('-')[1])
            # 校验添加的商品和在车商品
            for i in range(len(cartItemId)):
                assert cartItemId[len(cartItemId)- i -1] == addItemId[i]

    # 全选商品下车
    def onSale_total_getOff(self):
        time.sleep(1)
        itemList = self.get_itemList(0)
        if itemList:
            time.sleep(1)
            self.click('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[1]/label/span[2]')
            # 点击下车
            self.click('//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/button/span')
            self.assert_text('确定下车该商品', 'ant-modal-confirm-title', By.CLASS_NAME)
            self.click("//span[text()='确 定']")
            time.sleep(1)
            notItemNoticeList = self.find_elements('default-empty-content--IiHqA', By.CLASS_NAME)
            assert notItemNoticeList[0].text == '暂无上车商品，去添加商品赚钱吧'
        else:
            notItemNoticeList = self.find_elements('default-empty-content--IiHqA', By.CLASS_NAME)
            assert notItemNoticeList[0].text == '暂无上车商品，去添加商品赚钱吧'

    # 全选按钮信息校验
    def check_all_button(self):
        itemList = self.get_itemList(0)
        # 如果没有商品就上车商品
        if itemList == None:
            self.on_live_add_item(3)
        # 获取商品大列表
        itemList = self.find_elements(
            'sort-goods-container--nDxaJ.goods-item--sidPr.goods-card-border--e4omN.dragOverall--mySrj', By.CLASS_NAME)
        # 滑动获取元素获取商品个数
        self.execute_script("arguments[0].scrollIntoView();", itemList[len(itemList) - 1])
        # 获取商品序号
        itemOrder = self.find_elements('ant-input.ant-input-borderless.sort-input--F23Bw.do-not-drag-me',
                                          By.CLASS_NAME)
        count = itemOrder[len(itemOrder) - 1].get_attribute('value')
        # 点击全选
        self.click('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[1]/label/span[2]')
        # 校验底部条文案
        text = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[6]/div/div[3]/div[1]/span').text
        res = '已选中' + str(count) + '个商品/列表共有' + str(count) + '个'
        assert res == text
        # 校验底部条按钮
        btn1 = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[1]/button/span').text
        btn2 = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/span/button/span').text
        btn3 = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/button/span').text
        assert btn1 == '全部取消'
        assert btn2 == '置顶'
        assert btn3 == '下车'

    # 商品id搜索
    def check_search_itemID(self):
        time.sleep(2)
        self.check_on_sale_item()
        # 获取小黄车商品列表
        itemList = self.get_itemList(0)
        # 获取商品id
        itemId = itemList[0].split("-")[1]
        self.hover('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[2]/div/div[1]/div/div[2]/span[2]')
        time.sleep(1)
        inputElement = self.find_elements('ant-select-selection-search-input', By.CLASS_NAME)
        inputElement[1].send_keys(itemId)
        text = self.find_element('id--rxoMH', By.CLASS_NAME).text
        resItemId = text.split("：")[1]
        assert resItemId == itemId

    # 商品序号搜索
    def check_search_order(self):
        time.sleep(2)
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        # 序号为1搜索
        orderId = 1
        # 序号为1的商品id
        itemId = itemList[0].split("-")[1]
        # 通过序号搜索，拿到对应商品id resItemId
        self.hover('//*[@id="rc-tabs-1-panel-c1"]/div[1]/div[2]/div/div[1]/div/div[2]/span[2]')
        time.sleep(1)
        inputElement = self.find_elements('ant-select-selection-search-input', By.CLASS_NAME)
        inputElement[1].send_keys(orderId)
        text = self.find_element('id--rxoMH', By.CLASS_NAME).text
        resItemId = text.split("：")[1]
        assert resItemId == itemId

    # 判断车上是否有商品
    def check_on_sale_item(self):
        itemList = self.get_itemList(0)
        if itemList == None:
            self.on_live_add_item(3)
        time.sleep(1)

    # 获取列表商品list
    def get_itemList(self, index):
        # index枚举 小黄车列表=0，待上车列表=1，历史上车列表=2
        # 操作前注意列表有商品，不然会定位失败！！！！
        try:
            # 获取父元素节点-小黄车列表父元素节点
            parent_element = self.driver.find_elements(By.XPATH, '//div[@data-test-id="virtuoso-item-list"]')[index]
        except IndexError:
            return None
        else:
            # 使用 XPath 获取所有每个商品卡的元素
            item_card_child_elements = self.execute_script("return arguments[0].children", parent_element)
            # 打印子元素的数量
            itemList = []
            print(f"Number of child elements: {len(item_card_child_elements)}")
            if len(item_card_child_elements) == 0:
                return itemList
            # 遍历子元素,获取商品id
            for child in item_card_child_elements:
                # 继续获取子元素
                itemChild = self.execute_script("return arguments[0].children", child)
                itemId = itemChild[0].get_attribute('id')
                itemList.append(str(itemId))
            return itemList
