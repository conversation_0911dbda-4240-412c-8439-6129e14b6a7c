"""
# Author     ：author wb_zhaojingjing03
# Description：
"""
import time
from datetime import datetime

import pytest

from test_case.helper_assistant.base import BaseTestCase
from selenium.webdriver.common.by import By

s_record_paly_back_page = 'https://s.kwaixiaodian.com/zone/live/record-playback/home'


class LoadRecordPlayBackPage(BaseTestCase):

    # 浏览器最大化操作 等
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_ESC_DOMAIN", "helper_assistant_preLive")
        self.open(s_record_paly_back_page)
        time.sleep(2)
        self.refresh_page()
        time.sleep(1)

    # 进行初始化和清理工作
    def setUp(self):
        super().setUp()
        print("我是LoadRecordPlayBackPage setUp方法，进行测试前的初始化工作")
        # 浏览器最大化操作 等
        self.maximize_window_and_login()

    def tearDown(self):
        super().tearDown()
        print("我是LoadRecordPlayBackPage tearDown方法，执行测试后的清除工作")

    # 确认页面
    def check_record_playback_page(self):
        time.sleep(2)
        self.assert_no_404_errors()
        self.assert_no_broken_links()
        self.assert_text('昨 日', '//*[@id="root"]/div[1]/div[1]/div/div/div[1]/div[2]/div/button[1]/span')
        self.assert_text('讲解回放', '//*[@id="root"]/div[1]/div[2]/div[1]/div[1]')
        self.assert_text('回放设置', '//*[@id="root"]/div[1]/div[2]/div[1]/div[2]')
        self.assert_text('视频信息',
                         '//*[@id="rc-tabs-0-panel-10"]/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')
        # 已剪辑需商家等级大于等于L2
        TabList = ['全部', '已剪辑', '已发布']
        tabElemnt = self.find_elements('kwaishop-seller-record-playback-pc-tabs-tab-btn', By.CLASS_NAME)
        for i in range(len(tabElemnt)):
            assert tabElemnt[i].text == TabList[i]

    # 查询短视频id校验查询
    def check_search_video(self):
        self.check_record_playback_page()
        # 获取当前页面列表下标第0位，讲解视频id
        video_id = self.get_videoId(0)
        time.sleep(1)
        self.input('//*[@id="rc-tabs-0-panel-10"]/div[1]/span/span/span/span[1]/input', video_id)
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-10"]/div[1]/span/span/span/span[2]/button')
        self.assert_no_404_errors()
        time.sleep(2)
        self.assert_text('发布短视频', "//span[text()='发布短视频']")

    # 查询短商品id校验查询
    def check_search_item(self):
        self.check_record_playback_page()
        # 获取当前页面列表下标第0位，商品id
        item_id = self.get_itemId(0)
        time.sleep(1)
        self.click('kwaishop-seller-record-playback-pc-select-selection-item', By.CLASS_NAME)
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-panel-10"]/div[1]/span/div/div[2]/div/div/div/div[2]/div/div/div/div[2]')
        time.sleep(1)
        self.input('//*[@id="rc-tabs-0-panel-10"]/div[1]/span/span/span/span[1]/input', item_id)
        time.sleep(1)
        self.click('//*[@id="rc-tabs-0-panel-10"]/div[1]/span/span/span/span[2]/button')
        self.assert_no_404_errors()
        time.sleep(2)
        self.assert_text('发布短视频', "//span[text()='发布短视频']")

    # 录制时间查询
    def check_search_record_time(self):
        time.sleep(2)
        # 获取首个视频id的时间日期
        timeElements = self.find_elements('video-text___zuBvO', By.CLASS_NAME)
        date = timeElements[0].text
        # 日期转换时间戳
        timestamp = self.date_change_timestamp(date)
        # 计算开始-结束时间范围 单位s
        start_time = self.timestamp_change_date(timestamp - 42)
        end_time = self.timestamp_change_date(timestamp + 42)
        # input 时间范围筛选 目前input此输入框 2024-10-29 17:42:24 会格式化为2024-10-29 17:42:024。前导零未能兼容
        self.input('//*[@id="rc-tabs-0-panel-10"]/div[1]/div[2]/div[2]/div[1]/input', start_time)
        time.sleep(1)
        self.input('//*[@id="rc-tabs-0-panel-10"]/div[1]/div[2]/div[2]/div[3]/input', end_time)
        self.click('//*[@id="rc-tabs-0-panel-10"]/div[1]/div[2]/div[3]/div/div/div/div[2]/div[2]/ul/li/button/span')
        time.sleep(1)
        self.assert_text('发布短视频', "//span[text()='发布短视频']")
        self.assert_text('共1条', 'kwaishop-seller-record-playback-pc-pagination-total-text', By.CLASS_NAME)

    # 自动发布短视频去授权
    def check_auto_publish_video_button(self):
        hover_element = '//*[@id="root"]/div[1]/div[2]/div[1]/div[2]/div[1]'
        self.hover(hover_element)
        element = self.find_element('btn___zLMXB', By.CLASS_NAME)
        element.click()
        self.assert_text('直播切片托管设置', 'kwaishop-seller-shortVideo-promote-pc-drawer-title', By.CLASS_NAME)
        # 子账号不展示去授权，需补充

    # 判断讲解回放视频前台展示状态
    def check_front_desk_display(self):
        self.check_record_playback_page()
        time.sleep(2)
        element = self.find_element(
            '//*[@id="rc-tabs-0-panel-10"]/div[2]/div/div/div/div/div/div/table/tbody/tr[2]/td[*]/div/div/button[1]/span'
        ).text
        if element == '取消展示':
            self.click(
                '//*[@id="rc-tabs-0-panel-10"]/div[2]/div/div/div/div/div/div/table/tbody/tr[*]/td[*]/div/div/button[1]/span')
            time.sleep(2)
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
            self.assert_text('否',
                             '//*[@id="rc-tabs-0-panel-10"]/div[2]/div/div/div/div/div/div/table/tbody/tr[2]/td[6]/span')
        else:
            self.click(
                '//*[@id="rc-tabs-0-panel-10"]/div[2]/div/div/div/div/div/div/table/tbody/tr[*]/td[*]/div/div/button[1]/span')
            time.sleep(1)
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div/div/div[2]/button[2]')
            self.assert_text('是',
                             '//*[@id="rc-tabs-0-panel-10"]/div[2]/div/div/div/div/div/div/table/tbody/tr[2]/td[6]/span')

    # 获取视频id
    def get_videoId(self, index):
        time.sleep(2)
        # 遍历所有列表元素，
        videoIdElements = self.find_elements(
            'kwaishop-seller-record-playback-pc-table-row.kwaishop-seller-record-playback-pc-table-row-level-0',
            By.CLASS_NAME)
        # 通过data-row-key获取视频id
        video_id = videoIdElements[index].get_attribute('data-row-key')
        return video_id

    # 获取商品id
    def get_itemId(self, index):
        time.sleep(2)
        # 遍历所有商品元素
        itemIdElements = self.find_elements('goods-id___dJrTH', By.CLASS_NAME)
        # 获取商品id
        item_id = itemIdElements[index].text.split("：")[1]
        return item_id

    # 全部tab
    def check_total_tab(self):
        self.assert_text('视频ID', 'kwaishop-seller-record-playback-pc-select-selection-item', By.CLASS_NAME)
        searchList = ['数据周期', '录制时间', '视频来源']
        searchElement = self.find_elements('z0soqetU4sSVoAZ8mxvL', By.CLASS_NAME)
        for i in range(len(searchElement)):
            assert searchList[i] == searchElement[i].text

    # 已剪辑tab
    def check_video_clips_tab(self):
        self.click("//div[text()='已剪辑']")
        time.sleep(1)
        self.assert_text('视频ID', 'kwaishop-seller-record-playback-pc-select-selection-item', By.CLASS_NAME)
        searchList = ['数据周期', '剪辑时间']
        searchElement = self.find_elements('z0soqetU4sSVoAZ8mxvL', By.CLASS_NAME)
        for i in range(len(searchElement)):
            assert searchList[i] == searchElement[i].text
        self.assert_text('发布短视频', "//span[text()='发布短视频']")

    # 发布tab
    def check_publish_tab(self):
        # 数据验证
        self.click("//div[text()='已发布']")
        time.sleep(1)
        self.assert_text('视频ID', 'kwaishop-seller-record-playback-pc-select-selection-item', By.CLASS_NAME)
        searchList = ['数据周期', '发布时间']
        searchElement = self.find_elements('label___dBqFd', By.CLASS_NAME)
        for i in range(len(searchElement)):
            assert searchList[i] == searchElement[i].text
        # 查看数据
        self.click("//span[text()='查看数据']")
        time.sleep(0.5)
        self.assert_text('视频详情', 'kwaishop-seller-record-playback-pc-drawer-title', By.CLASS_NAME)
        self.assert_text('短视频收益数据', 'title___q3l6r', By.CLASS_NAME)
        self.assert_text('转化效果', 'title___VjJGI', By.CLASS_NAME)

    # 日期转时间戳
    def date_change_timestamp(self, date):
        # 定义时间格式
        time_format = "%Y-%m-%d %H:%M:%S"
        # 将时间字符串转换为datetime对象
        dateTime = datetime.strptime(date, time_format)
        # 将datetime对象转换为时间戳
        timestamp = dateTime.timestamp()
        return int(timestamp)

    # 时间戳转日期
    def timestamp_change_date(self, timestamp):
        # 将时间戳转换为datetime对象
        time = datetime.fromtimestamp(timestamp)
        # 将datetime对象格式化为字符串
        date = time.strftime("%Y-%m-%d %H:%M:%S")
        return date

    # 字符转布尔
    def str_to_bool(self, value):
        return value.lower() == 'true'
