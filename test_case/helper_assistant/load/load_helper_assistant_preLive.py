"""
# Author     ：author r<PERSON><PERSON><PERSON><PERSON>
# Description：
"""
import random
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from selenium.common import NoSuchElementException
from selenium.webdriver.common.by import By
from seleniumbase.common.exceptions import TextNotVisibleException
from seleniumbase.fixtures.page_actions import timeout_exception

from test_case.helper_assistant.base import BaseTestCase
from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env


# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

@ddt
class LoadHelperAssistantPreLive(BaseTestCase):

    # 浏览器最大化，登录跟播助手
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "helper_assistant_preLive")

    # 跳过新手引导
    def skip_guider(self):
        # 刷新当前页面
        self.refresh_page()
        sleep(1)

    # 判断预开播顶部任务区
    def check_pre_guide(self):
        # 校验预开播
        self.assert_text('未开播', 'title2--Lesik', By.CLASS_NAME)
        # 校验直播设置
        self.assert_text('直播设置', 'set--j9neo', By.CLASS_NAME)
        # 校验顶部直播前准备营销工具
        titles = self.find_elements('title--XFbSQ', By.CLASS_NAME)
        titleList = ['直播前准备', '直播货品准备', '直播间装修', '设置营销']
        for i in range(len(titles)):
            assert titles[i].text == titleList[i]
        # 文案
        titleTexts = self.find_elements('subtitle-1--LbW7w', By.CLASS_NAME)
        titleTextList = ['提前添加小黄车商品', '提升直播卖货氛围，提升商品曝光',
                         '创建新人券，提升观播新客转化']
        for i in range(0, len(titleTexts) - 1):
            assert titleTexts[i].text == titleTextList[i]
        preOnSale = self.find_elements('anticon.anticon-system-arrow-medium-right-line', By.CLASS_NAME)
        preOnSale[0].click()
        time.sleep(1)
        self.assert_text('添加商品', 'rcDialogTitle0', By.ID)

    # 校验小黄车列表顶部按钮
    def check_shop_car_title(self):
        self.assert_text('小黄车商品', 'ant-badge.badge--bodTX', By.CLASS_NAME)
        self.assert_text('待上车商品',
                         '//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.assert_text('全选', '//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[1]/label/span[2]')
        self.assert_text('搜索', 'text--aEIsb', By.CLASS_NAME)
        elements = self.find_elements('name--urwBR', By.CLASS_NAME)
        # 部分商家有闪电购全选
        if len(elements) == 3:
            for i in range(len(elements)):
                textList = ['智能讲解', '刷新', '添加上车商品']
                assert elements[i].text == textList[i]
        elif len(elements) == 4:
            for i in range(len(elements)):
                textList = ['智能讲解', '闪电购', '刷新', '添加上车商品']
                assert elements[i].text == textList[i]

    # 测试小黄车管理按钮
    def check_onsale_item_list(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        self.assert_text('移除',
                         '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
        try:
            self.assert_text('开启提词',
                         '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
        except TextNotVisibleException:
            self.assert_text('提词中',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
        # 分销商品价格库存无法点击
        try:
            self.assert_text('价格库存',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        except TextNotVisibleException:
            self.assert_text('回放管理',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        else:
            self.assert_text('价格库存',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.assert_text('回放管理',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[4]/span')
        # 校验商品卡售价/成交金额等信息存在
        self.find_element("//span[text()='售价']")
        self.find_element("//span[text()='成交金额']")
        self.find_element("//span[text()='成交/库存']")
        self.find_element("//span[text()='讲解卡成交金额']")
        # 点击讲解卡成交金额倒三角
        self.click('anticon.anticon-system-arrow-large-down-line', By.CLASS_NAME)
        time.sleep(1)
        # 校验讲解卡弹窗信息
        popupTextList = ['讲解卡成交占比', '讲解卡成交件数', '讲解卡曝光点击率']
        popupElementList = self.find_elements('pure--WF7L8', By.CLASS_NAME)
        for i in range(len(popupElementList)):
            assert popupTextList[i] == popupElementList[i].text.split('：')[0]

    # 推荐商品 会默认上架
    def recommend_item(self):
        # 提示条文案
        text = self.find_element('tips--WRUKM', By.CLASS_NAME).text
        assert text == "根据你日常带货表现，为你推荐这些直播爆款商品"
        # 点击推荐引导条 --> 去看看 按钮拉起选品弹窗
        self.click("//span[text()='去看看']")
        time.sleep(2)
        # 判断推荐tab是否被锚定
        isChecked = self.find_elements('ant-tabs-tab-btn', By.CLASS_NAME)[1].get_attribute('aria-selected')
        assert isChecked == 'true'
        # 获取顶部文案，断言文案
        elements = self.find_elements('ant-table-cell', By.CLASS_NAME)
        assert elements[1].text == '商品信息'
        assert elements[2].text == '价格'
        assert elements[3].text == '佣金'
        # 校验推荐商品数大于0
        itemList = self.find_elements('row--vrv2H.select-goods-list__item', By.CLASS_NAME)
        assert len(itemList) > 0
        '''
        # 推荐商品上车
        imgElements = self.find_elements('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        if imgElements:
            # 获取商品id
            itemIdElements = self.find_elements('ant-typography.id-text--m7RQq', By.CLASS_NAME)
            # 点击图片进行勾选
            imgElements[0].click()
            itemIdText = []
            for itemIds in itemIdElements:
                text = itemIds.text
                itemIdText.append(text)
            addItemId = itemIdText[0].split(' ')[1]
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/button[2]/span')
            time.sleep(1)
            itemList = self.get_itemList(0)
            # 这里还需判断下上车顺序
            cartItemId = itemList[0].split('-')[1]
            # 校验添加的商品和
            assert cartItemId == addItemId
            # 移除添加商品
            self.click(
                '//*[@id="inner_c1goods-' + cartItemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
        '''

    # 全选按钮
    def check_all_button(self):
        self.check_on_sale_item()
        itemList = self.find_elements(
            'sort-goods-container--nDxaJ.goods-item--sidPr.goods-card-border--e4omN.dragOverall--mySrj', By.CLASS_NAME)
        # 滑动获取元素获取商品个数
        self.execute_script("arguments[0].scrollIntoView();", itemList[len(itemList) - 1])
        iputElements = self.find_elements('ant-input.ant-input-borderless.sort-input--F23Bw.do-not-drag-me',
                                          By.CLASS_NAME)
        count = iputElements[len(iputElements) - 1].get_attribute('value')
        self.click('//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[1]/label/span[2]')
        # 校验底部条文案
        text = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[1]/span').text
        res = '已选中' + str(count) + '个商品/列表共有' + str(count) + '个'
        assert res == text
        # 校验底部条按钮
        btn1 = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[1]/button/span').text
        btn2 = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/span/button/span').text
        btn3 = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/button/span').text
        assert btn1 == '全部取消'
        assert btn2 == '置顶'
        assert btn3 == '下车'

    # 商品id搜索
    def check_search_itemID(self):
        time.sleep(2)
        self.check_on_sale_item()
        # 获取小黄车商品列表
        itemList = self.get_itemList(0)
        # 获取商品id
        itemId = itemList[0].split("-")[1]
        self.hover('text--aEIsb', By.CLASS_NAME)
        time.sleep(1)
        self.input('ant-select-selection-search-input', itemId, By.CLASS_NAME)
        text = self.find_element('id--rxoMH', By.CLASS_NAME).text
        resItemId = text.split("：")[1]
        assert resItemId == itemId

    # 商品序号搜索
    def check_search_order(self):
        time.sleep(2)
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        # 序号为1搜索
        orderId = 1
        # 序号为1的商品id
        itemId = itemList[0].split("-")[1]
        # 通过序号搜索，拿到对应商品id resItemId
        self.hover('text--aEIsb', By.CLASS_NAME)
        time.sleep(1)
        self.input('ant-select-selection-search-input', orderId, By.CLASS_NAME)
        text = self.find_element('id--rxoMH', By.CLASS_NAME).text
        resItemId = text.split("：")[1]
        assert resItemId == itemId

    # 校验待上车列表智能讲解弹窗
    def check_intelligent_explanation(self):
        time.sleep(2)
        self.assert_text('智能讲解', '//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[2]/div/div[2]/span/span')
        time.sleep(1)
        self.click('//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[2]/div/div[2]/span/span')
        self.assert_text('智能讲解设置', '//*[@id="rcDialogTitle0"]')
        self.assert_text('商品智能识别', "/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div[2]/div/div[1]/div[1]")
        self.assert_text('开启后，将智能识别主播口播内容，关于小黄车的商品匹配，识别到同款后会提示是否打开讲解功能。',
                         '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div[2]/div/div[3]')
        self.assert_text('智能讲解', '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div[3]/div/div[1]/div[1]')
        self.assert_text('查看详情', '/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div[3]/div/div[3]/a')
        self.assert_text('取消', '/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/button[1]/span')
        self.assert_text('确认', '/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/button[2]/span')
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/button[2]/span')

    # 预开播-上车列表全选下车
    def onSale_total_getOff(self):
        time.sleep(1)
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        time.sleep(1)
        self.click('//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[1]/label/span[2]')
        # 点击下车
        self.click('//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/button/span')
        self.assert_text('确定下车该商品', 'ant-modal-confirm-title', By.CLASS_NAME)
        self.click("//span[text()='确 定']")
        time.sleep(1)
        notItemNoticeList = self.find_elements('default-empty-content--IiHqA', By.CLASS_NAME)
        assert notItemNoticeList[0].text == '暂无上车商品，去添加商品赚钱吧'

    # <=====选品弹窗start====>
    # 校验添加商品弹窗-列表试图
    def check_add_shop_car_item_popup(self):
        time.sleep(1)
        btns = self.find_elements('name--urwBR', By.CLASS_NAME)
        # 点击添加小黄车商品按钮
        btns[len(btns) - 1].click()
        # 断言页面元素
        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]/span')
        titles = self.find_elements('ant-tabs-tab-btn', By.CLASS_NAME)
        titleList = ['全部商品', '推荐商品']
        for i in range(len(titles)):
            assert titles[i].text == titleList[i]
        input = self.find_element(
            '/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[1]/span/span/input')
        inputText = input.get_attribute('placeholder')
        assert inputText == '输入商品ID/商品名称搜索'
        self.assert_text('拖拽商品调整顺序', 'sortable-tips--FZWaS', By.CLASS_NAME)
        # 判断商品信息
        elements = self.find_elements('thead tr th', By.CSS_SELECTOR)
        textList = ['商品信息', '价格', '库存']
        for i in range(len(textList)):
            assert elements[i + 1].text == textList[i]
        # 商品列表有数据
        itemList = self.find_elements('row--vrv2H.select-goods-list__item', By.CLASS_NAME)
        assert len(itemList) > 1
        self.assert_text('全选', '/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/div[1]/label/span[2]')
        self.assert_text('全部取消', '/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/div[1]/a')
        self.assert_text('取 消', '/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/button[1]/span')
        self.assert_text('更新小黄车', '/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/button[2]/span')

    # 选品上车
    def on_live_add_item(self, count):
        itemList = self.get_itemList(0)
        if itemList:
            self.onSale_total_getOff()
        time.sleep(1)
        self.click('//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[2]/div/div[4]/span/span')
        time.sleep(1)
        # 这里还需判断下上车顺序
        getCartOrder = self.find_element("//div[contains(text(), '商品将添加至小黄车')]").text.split(' ')[0].split('小黄车')[1]
        # 获取商品主图 用于点击勾选
        imgElements = self.find_elements('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        if imgElements:
            # 获取商品id
            itemIdElements = self.find_elements('ant-typography.id-text--R02TA', By.CLASS_NAME)
            itemIdText = []
            addItemId = []
            # 点击主图进行勾选
            for i in range(count):
                imgElements[i].click()
                itemIdText.append(itemIdElements[i].text)
                addItemId.append(itemIdText[i].split(' ')[1])
            self.click("//span[text()='更新小黄车']")
            time.sleep(2)
            itemList = self.get_itemList(0)
            cartItemId = []
            # 这里还需判断下上车顺序
            for i in range(len(itemList)):
                cartItemId.append(itemList[i].split('-')[1])
            # 上车顺序为顶部时，上车序号跟选品序号相反
            if getCartOrder == '顶部':
                for i in range(len(cartItemId)):
                    assert cartItemId[len(cartItemId) - i - 1] == addItemId[i]
            # 上车顺序为顶部时，上车序号跟选品序号一致
            elif getCartOrder == '底部':
                for i in range(len(cartItemId)):
                    assert cartItemId[i] == addItemId[i]
        else:
            self.skip("选品页无商品，请确认后上架商品重试")

    # <=====选品弹窗end====>
    # <=====待上车start====>
    # 校验待上车列表顶部按钮
    def check_wait_shop_car_title(self):
        self.assert_text('待上车商品',
                         '//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        time.sleep(1)
        self.assert_text('全选', '//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[1]/label/span[2]')
        self.assert_text('搜索', '//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[2]/div/div[1]/div/div[2]/span[2]')
        self.assert_text('智能讲解', '//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[2]/div/div[2]/span/span')
        self.assert_text('管理计划', '//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[2]/div/div[3]/span/span')
        self.assert_text('刷新', '//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[2]/div/div[4]/span/span')
        time.sleep(2)
        self.assert_text('添加待上车商品', '//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[2]/div/div[5]/span/span')

    # 校验待上车商品按钮
    def wait_onsale_item_list(self):
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.check_wait_on_sale_item()
        itemList = self.get_itemList(1)
        itemId = itemList[0]
        checkElement = self.find_element(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span').text
        if checkElement == '编辑':
            self.assert_text('移除',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        else:
            self.assert_text('移除',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')

    # 待上车商品上车
    def wait_on_sale_add(self, count):
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        itemList = self.get_itemList(1)
        if itemList:
            self.wait_on_sale_total_get_off()
        # 待上车列表顶部功能
        btnList = self.find_elements('name--urwBR', By.CLASS_NAME)
        btnList[len(btnList) - 1].click()
        time.sleep(1)
        imgElements = self.find_elements('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        if imgElements:
            # 获取商品id
            itemIdElements = self.find_elements('ant-typography.id-text--EuTrg', By.CLASS_NAME)
            itemIdText = []
            addItemId = []
            # 点击主图进行勾选
            for i in range(count):
                imgElements[i].click()
                itemIdText.append(itemIdElements[i].text)
                addItemId.append(itemIdText[i].split(' ')[1])
            self.click("//span[text()='更新待上车商品']")
        else:
            self.skip("选品页无商品，请确认后上架商品重试")

    # 待上车商品全部下车
    def wait_on_sale_total_get_off(self):
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.check_wait_on_sale_item()
        itemList = self.get_itemList(1)
        self.click('//*[@id="rc-tabs-2-panel-c2"]/div[1]/div[1]/label/span[1]')
        # 点击移除
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[3]/div[2]/button/span')
        self.assert_text('确定移除该商品', 'ant-modal-confirm-title', By.CLASS_NAME)
        self.click("//span[text()='确 定']")
        time.sleep(1)
        notItemNoticeList = self.find_element('des--Te8nO', By.CLASS_NAME).text
        assert '可在直播前提前添加商品，或导入直播计划' in notItemNoticeList

    # 校验待上车编辑半屏
    def check_wait_onsale_edit(self):
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.check_wait_on_sale_item()
        itemList = self.get_itemList(1)
        itemId = itemList[0]
        itemNameElement = self.find_element(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[1]/div')
        itemName = itemNameElement.text
        # 判断非自建商品编辑按钮置灰不可点击；点击编辑按钮，进入编辑半屏
        for i in range(0, len(itemList) - 1):
            try:
                self.assert_text('编辑', '//*[@id="inner_' + itemList[
                    i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
            except TextNotVisibleException:
                continue
            else:
                self.click(
                    '//*[@id="inner_' + itemList[i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
                self.assert_text(itemName, 'info-item--RKSxl.item-title--mZjsR', By.CLASS_NAME)
                self.assert_text('待上车商品设置', '/html/body/div[*]/div/div[2]/div/div/div[1]/div/div')
                self.assert_text('设置商品优惠', 'sub-title--xIXA2', By.CLASS_NAME)
                self.assert_text('不设置', 'ant-select-selection-item', By.CLASS_NAME)
                # 断言 商品优惠枚举
                self.click('ant-select-selection-item', By.CLASS_NAME)
                list = ['不设置', '限时秒杀', '福利购']
                time.sleep(1)
                lists = self.find_elements('ant-select-item-option-content', By.CLASS_NAME)
                for i in range(len(lists)):
                    assert lists[i].text == list[i]
                self.assert_text('价格和库存', 'title-text--lW6Qs', By.CLASS_NAME)
                self.assert_text('批量设置',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[1]/div[2]/button[1]/span')
                self.assert_text('清空全部',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[1]/div[2]/button[2]/span')
                elements = self.find_elements("thead tr th", By.CSS_SELECTOR)
                textList = ['规格', '原库存', '上车库存', '原价', '上车价格']
                for i in range(len(elements)):
                    assert elements[i].text == textList[i]
                self.assert_text('取消', '/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[1]/span')
                self.assert_text('保存', '/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[2]/span')
                break

    # 待上车编辑半屏中，商品优惠-限时秒杀
    def wait_onsale_edit_seckill(self):
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.check_wait_on_sale_item()
        time.sleep(1)
        itemList = self.get_itemList(1)
        # 判断非自建商品编辑按钮置灰不可点击；点击编辑按钮，进入编辑半屏
        for i in range(0, len(itemList) - 1):
            try:
                self.assert_text('编辑', '//*[@id="inner_' + itemList[
                    i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
            except TextNotVisibleException:
                continue
            else:
                self.click(
                    '//*[@id="inner_' + itemList[i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
                # 点击选择商品优惠
                self.click('ant-select-selection-item', By.CLASS_NAME)
                time.sleep(0.5)
                # 选择限时秒杀 ['不设置', '限时秒杀', '福利购']
                lists = self.find_elements('ant-select-item-option-content', By.CLASS_NAME)
                lists[1].click()
                self.assert_text('购买条件', '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[1]/div')
                self.assert_text('生效方式',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[2]/div[1]/span[1]')
                inputelement = self.find_element(
                    '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[2]/div[2]/input').get_attribute(
                    'value')
                assert inputelement == '上车时生效'
                self.assert_text('秒杀时长',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[2]/div[3]')
                self.assert_text('秒杀库存', '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[3]/div')
                self.assert_text('总库存',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[4]/div[1]/span[1]')
                self.assert_text('每人限购',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[4]/div[3]')
                break

    # 待上车编辑半屏中，商品优惠-福利购
    def wait_onsale_edit_fuligou(self):
        self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[2]/div[1]/div[1]/div[2]')
        self.check_wait_on_sale_item()
        itemList = self.get_itemList(1)
        # 判断非自建商品编辑按钮置灰不可点击；点击编辑按钮，进入编辑半屏
        for i in range(0, len(itemList) - 1):
            try:
                self.assert_text('编辑',
                                 '//*[@id="inner_' + itemList[
                                     i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
            except TextNotVisibleException:
                continue
            else:
                self.click(
                    '//*[@id="inner_' + itemList[i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
                # 点击选择商品优惠
                self.click('ant-select-selection-item', By.CLASS_NAME)
                time.sleep(0.5)
                # 选择限时秒杀 ['不设置', '限时秒杀', '福利购']
                lists = self.find_elements('ant-select-item-option-content', By.CLASS_NAME)
                lists[2].click()
                self.assert_text('条件设置', '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[1]/div')
                self.assert_text('购买条件',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[2]/div[1]/span')
                self.assert_text('条件详情',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[2]/div[3]')
                self.assert_text('总库存',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[3]/div[1]/span[1]')
                self.assert_text('总库存',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[3]/div[1]/span[1]')
                self.assert_text('每人限购',
                                 '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]/div[4]/div[3]/div[3]')
                break

    # <=====待上车end====>
    # <=====卖点start====>
    # 校验卖点弹窗
    def check_selling_point_popup(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        # 点击卖点设置透出弹窗
        self.click(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[2]/div[2]/span')
        self.assert_text('设置商品卖点', '//*[@id="rcDialogTitle0"]')
        self.assert_text('卖点内容将作为用户维权凭证，请谨慎设置', 'ant-alert-message', By.CLASS_NAME)
        elements = self.find_elements('label--J86Eh', By.CLASS_NAME)
        textList = ['商品卖点', '价格描述']
        for i in range(len(elements)):
            assert elements[i].text == textList[i]
        buttontextList = ['取消', '更新']
        buttonTexts = self.find_elements('btn--LBSpu .ant-btn', By.CLASS_NAME)
        for i in range(len(buttontextList)):
            assert buttonTexts[i].text == buttontextList[i]

    # 新增/修改卖点
    def set_selling_point(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        # 点击卖点设置透出弹窗
        self.click(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[2]/div[2]/span')
        # 输入卖点信息
        randomText = random.randint(1, 100)
        input = '自动化卖点信息'
        inputText = input + str(randomText)
        self.input('presetDisplay', inputText, By.ID)
        # 点击更新
        self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div/div/div/div[3]/button[2]/span')
        time.sleep(1)
        # 获取更新后的卖点信息
        afterText = self.find_element(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[2]/div[1]').text
        assert afterText == inputText

    # <=====卖点end====>
    # <=====提词start====>
    # 校验提词半屏
    def check_prompt_page(self):
        self.check_on_sale_item()
        time.sleep(2)
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        # 获取商品标题用于半屏断言
        itemName = self.find_element(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[1]/div').text
        # 点击入口进入半屏
        self.click(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
        self.assert_text('设置提词器内容', '/html/body/div[*]/div/div[2]/div/div/div[1]/div/div')
        self.assert_text(itemName,
                         '/html/body/div[*]/div/div[2]/div/div/div[2]/div[1]/div/div/div/div[1]/div[2]/div[1]/div')
        self.assert_text('提词内容编辑', '/html/body/div[*]/div/div[2]/div/div/div[2]/div[2]')

    # 删除提词信息，回滚为无提词
    def del_prompt(self, itemId):
        # itemId = 'c1goods-20903808476853'
        elementText = self.find_element(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
        if elementText == '已设置提词':
            self.click(
                '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
            time.sleep(1)
            textArea = self.find_element(
                '/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]')
            textArea.clear()
            # 点击更新
            self.click('/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[2]/span')
            return True
        elif elementText == '设置提词':
            return True

    # 校验题词设置按钮联动状态
    def check_prompt_set(self):
        self.check_on_sale_item()
        time.sleep(2)
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        elementText = self.find_element(
            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]').text
        # 有提词
        if elementText == '已设置提词':
            '//*[@id="inner_c1goods-20903808476853"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span'
            self.assert_text('开启提词',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
            time.sleep(2)
            # 点击开启题词
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
            # 开启题词的按钮应该变为题词中
            self.assert_text('提词中',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
            # 再点击提词中，按钮变为开启提词
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
            self.assert_text('开启提词',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
            self.assert_text('已设置提词',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
        # 无提词
        elif elementText == '设置提词':
            # 点击开启题词
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[2]/span')
            # 校验二次确认弹窗
            self.assert_text('当前暂无预设提词内容', 'rcDialogTitle0', By.ID)
            self.assert_text('请先设置提词内容后，否则将影响讲解',
                             '/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div')
            # 点击取消
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/button[1]')
            self.assert_text('设置提词',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
            # 点击去设置，拉起半屏
            self.click(
                '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
            time.sleep(1)
            self.assert_text('设置提词器内容', 'ant-drawer-title', By.CLASS_NAME)

    # 更新提词信息
    def change_prompt(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        if len(itemList) >= 2:
            itemId = itemList[1]
            # 两种情况，商品有无提词，商品有提词。
            elementText = self.find_element(
                '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]').text
            # 录入内容需要每次不一致。
            input = '自动化设置提词'
            # 无提词
            if elementText == '设置提词':
                self.click(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                time.sleep(2)
                # 录入提词内容
                textArea = self.find_element('/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]')
                textArea.send_keys(input)
                # 点击保存
                self.click('/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[2]/span')
                # 断言
                self.assert_text('已设置提词',
                                 '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
                # 断言半屏内容
                self.click(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                time.sleep(1)
                afterText = self.find_element(
                    '/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]').text
                assert afterText == input
            # 有提词
            elif elementText == '已设置提词':
                self.click(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                time.sleep(1)
                # 获取已设置提词
                beforeText = self.find_element('/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]').text
                # 循环判断提词文案是否一致，替换为不一致
                while True:
                    randomInt = random.randint(1, 100)
                    inputText = input + str(randomInt)
                    if beforeText != inputText:
                        textArea = self.find_element('/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]')
                        # 清空后，再次录入
                        textArea.clear()
                        textArea.send_keys(inputText)
                        # 点击更新
                        self.click('/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[2]/span')
                        # 断言
                        self.assert_text('已设置提词',
                                         '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
                        # 断言半屏内容
                        self.click(
                            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                        time.sleep(1)
                        afterText = self.find_element(
                            '/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]').text
                        assert afterText == inputText
                        break
                    else:
                        break

    # 更新提词信息点击取消-不保存，校验是否保存
    def check_cancel_change_prompt(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        if len(itemList) >= 2:
            itemId = itemList[1]
            # 两种情况，商品有无提词，商品有提词。
            elementText = self.find_element(
                '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]').text
            # 录入内容需要每次不一致。
            input = '自动化设置提词'
            # 无提词
            if elementText == '设置提词':
                self.click(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                time.sleep(2)
                # 录入提词内容
                textArea = self.find_element('/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]')
                textArea.send_keys(input)
                # 点击取消
                self.click('/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[1]/span')
                # 二次弹窗点击不保存
                self.click('/html/body/div[*]/div/div[2]/div/div[2]/div/div/div[2]/button[1]/span')
                # 断言
                self.assert_text('设置提词',
                                 '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
            # 有提词
            elif elementText == '已设置提词':
                self.click(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                time.sleep(1)
                # 获取已设置提词
                beforeText = self.find_element('/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]').text
                # 循环判断提词文案是否一致，替换为不一致
                while True:
                    randomInt = random.randint(1, 100)
                    inputText = input + str(randomInt)
                    if beforeText != inputText:
                        textArea = self.find_element('/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]')
                        # 清空后，再次录入
                        textArea.clear()
                        textArea.send_keys(inputText)
                        # 点击更新
                        self.click('/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[1]/span')
                        # 二次弹窗点击不保存
                        self.click('/html/body/div[*]/div/div[2]/div/div[2]/div/div/div[2]/button[1]/span')
                        # 断言
                        self.assert_text('已设置提词',
                                         '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[1]')
                        # 断言半屏内容
                        self.click(
                            '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[2]/span')
                        afterText = self.find_element(
                            '/html/body/div[*]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]').text
                        assert afterText != inputText
                        break
                    else:
                        break

    # <=====提词end====>
    # <=====尺码表start====>
    def chcek_size_chart_page(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        # 遍历列表中商品
        for i in range(len(itemList)):
            itemId = itemList[i]
            # 商品是否有设置尺码表开关
            try:
                element = self.find_element(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[4]/div[1]').text
            except:
                print('没有对应商品有尺码表设置')
                return None
            else:
                if element == '设置尺码表':
                    self.click(
                        '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[4]/div[2]/span')
                    time.sleep(1)
                    self.assert_text(
                        '直播间尺码表展示设置', '/html/body/div[*]/div/div[2]/div/div/div[1]/div/div/div')
                    self.assert_text('请选择尺码表维度', 'title--exLMT', By.CLASS_NAME)
                    self.assert_text('勾选要展示的尺码（最多8项）', 'l1--NqU1c', By.CLASS_NAME)
                    # self.assert_text('肩宽（cm）',
                    #                  '/html/body/div[*]/div/div[2]/div/div/div[2]/div/div/div/div[1]/label[1]/span[2]')
                    # self.assert_text('胸围（cm）',
                    #                  '/html/body/div[*]/div/div[2]/div/div/div[2]/div/div/div/div[1]/label[2]/span[2]')
                    break

    # 设置尺码表
    def set_size_chart(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        # 遍历列表中商品
        for i in range(len(itemList)):
            itemId = itemList[i]
            # 商品是否有设置尺码表开关
            try:
                element = self.find_element(
                    '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[4]/div[1]').text
            except:
                print('没有对应商品有尺码表设置')
                return None
            else:
                if element == '设置尺码表':
                    self.click(
                        '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[4]/div[2]/span')
                    time.sleep(1)
                    # self.click("//span[contains(text(),'胸围')]")
                    sizes = self.find_elements("tbody tr", By.CSS_SELECTOR)
                    sizes[0].click()
                    # 用class判断勾选状态已勾选：ant-table-row ant-table-row-level-0 ant-table-row-selected row--SPF0I
                    beforeClassName = sizes[0].get_attribute('class')
                    # 点击确定按钮
                    self.click('/html/body/div[*]/div/div[2]/div/div/div[3]/div/button[2]/span')
                    # 断言
                    self.click(
                        '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[2]/div[4]/div[2]/span')
                    time.sleep(1)
                    # self.click("//span[contains(text(),'胸围')]")
                    afterClassName = sizes[0].get_attribute('class')
                    assert beforeClassName == afterClassName

    # <=====尺码表end====>
    # 校验商品置顶功能
    def check_item_top(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        if len(itemList) == 1:
            itemId = itemList[len(itemList) - 1]
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[1]/div[1]/label/span')
            self.assert_text('置顶',
                             '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[3]/div[2]/span/button/span')
        else:
            # 获取商品列表最后一个商品
            itemId = itemList[len(itemList) - 1]
            reTopItemName = self.find_element(
                '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[2]/div[1]/div').text
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[1]/div[1]/label/span')
            time.sleep(1)
            self.assert_text('置顶',
                             '//*[@id="main-container"]/section/main/div/div/div[6]/div/div[3]/div[2]/button[1]/span')
            self.click('//*[@id="main-container"]/section/main/div/div/div[6]/div/div[3]/div[2]/button[1]/span')
            time.sleep(1)
            afterTopItemList = self.get_itemList(0)
            if afterTopItemList:
                afterTopItemId = afterTopItemList[0]
                self.assert_text(reTopItemName,
                                 '//*[@id="inner_' + afterTopItemId + '"]/div/div/div/div/div/div[1]/div[2]/div[1]/div')

    # 校验回放管理半屏页
    def check_record_manage_page(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        # 分销商品价格库存无法点击
        try:
            self.assert_text('价格库存',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        except TextNotVisibleException:
            self.assert_text('回放管理',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.assert_text('回放管理', '/html/body/div[*]/div/div[2]/div/div/div[1]/div/div/div/div[1]')
            self.assert_text('成交数据',
                             '/html/body/div[*]/div/div[2]/div/div/div[2]/div/div/div[2]/div/div[1]/div[1]/div')
            self.assert_text('讲解回放',
                             '/html/body/div[*]/div/div[2]/div/div/div[2]/div/div/div[2]/div/div[2]/div[1]/div[1]/div[1]')
        else:
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[4]/span')
            self.assert_text('回放管理', '/html/body/div[*]/div/div[2]/div/div/div[1]/div/div/div/div[1]')
            self.assert_text('成交数据',
                             '/html/body/div[*]/div/div[2]/div/div/div[2]/div/div/div[2]/div/div[1]/div[1]/div')
            self.assert_text('讲解回放',
                             '/html/body/div[*]/div/div[2]/div/div/div[2]/div/div/div[2]/div/div[2]/div[1]/div[1]/div[1]')

    # 校验价格/库存弹窗
    def check_change_priceAndINV(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        try:
            self.assert_text('价格库存',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        except TextNotVisibleException:
            msg = '非自建商品不可修改价格/库存'
            print(msg)
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
        else:
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.assert_text('批量调整:', '/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/div[1]')
            elements = self.find_elements('.ant-table-row.ant-table-row-level-0', By.CSS_SELECTOR)
            self.assert_text('价格库存（共' + str(len(elements)) + '条)', 'ant-modal-title', By.CLASS_NAME)

    # 更改价格
    def change_price(self, _after_price=-1):
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        try:
            self.assert_text('价格库存',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        except TextNotVisibleException:
            msg = '非自建商品不可修改价格/库存'
            print(msg)
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
        else:
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.assert_text('批量调整:', '/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/div[1]')
            if _after_price > 0:
                afterPrice = _after_price
            else:
                afterPrice = random.randint(1, 100)
            # self.input('setStock', '1', By.ID)
            self.input('setPrice', afterPrice, By.ID)
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/button/span')
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            self.assert_text(afterPrice,
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[2]/div[1]/div/div[1]/div[1]/span[2]/span[2]')

    # 修改库存
    def change_INV(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        itemId = itemList[0]
        try:
            self.assert_text('价格库存',
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
        except TextNotVisibleException:
            msg = '非自建商品不可修改价格/库存'
            print(msg)
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
        else:
            beforeINV = self.find_element(
                '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[2]/div[1]/div/div[3]/div[1]/span[2]/span[2]').text.split(
                '/')[1]
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.assert_text('批量调整:', '/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/div[1]')
            # 通过模糊id查询有多少个sku
            partial_id = "presetStock"
            elements = self.find_elements(f"[id*='{partial_id}']", By.CSS_SELECTOR)
            addINV = random.randint(1, 10)
            # 批量添加随机1-10库存
            self.input('setStock', addINV, By.ID)
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/button/span')
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            afterINV = int(beforeINV) + (addINV * len(elements))
            self.assert_text('0/' + str(afterINV),
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[2]/div[1]/div/div[3]/div[1]/span[2]/span[2]')

            # 回滚原始库存，减去新增库存
            self.click('//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            self.assert_text('批量调整:', '/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/div[1]')
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/div[2]/span/div[1]/div/span[2]')
            self.click('/html/body/div[*]/div/div/div/div[2]/div/div/div/div[2]')
            # 减去新增库存
            self.input('setStock', addINV, By.ID)
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[2]/div[1]/button/span')
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            # 校验原始库存
            self.assert_text('0/' + str(beforeINV),
                             '//*[@id="inner_' + itemId + '"]/div/div/div/div/div/div[2]/div[1]/div/div[3]/div[1]/span[2]/span[2]')

    # <=====历史直播页start====>
    # 校验历史直播列表
    def check_live_assistant_history_list(self):
        self.assert_text('我的直播列表', '//*[@id="main-container"]/section/main/div/div/div/div[1]/div[1]/div')
        elements = self.find_elements('thead tr th', By.CSS_SELECTOR)
        titleList = ['直播信息', '直播数据', '预约信息', '直播信息', '操作']
        for i in range(len(elements)):
            assert elements[i].text == titleList[i]
        liveTitleTexts = self.find_elements('text--rKnD7', By.CLASS_NAME)
        assert liveTitleTexts[0].text == '测试直播间，勿进'
        self.assert_text('讲解回放管理',
                         '//*[@id="main-container"]/section/main/div/div/div/div[1]/div[2]/div/button[2]/span')
        self.assert_text('建计划得流量',
                         '//*[@id="main-container"]/section/main/div/div/div/div[1]/div[2]/div/button[3]/span')
        self.assert_text('直播片段发短视频',
                         '//*[@id="main-container"]/section/main/div/div/div/div[1]/div[2]/div/button[1]/span')

    # 校验历史直播创建计划
    def check_live_assistant_history_create_plan(self):
        self.assert_text('我的直播列表', '//*[@id="main-container"]/section/main/div/div/div/div[1]/div[1]/div')
        self.refresh_page()
        self.assert_text('建计划得流量',
                         '//*[@id="main-container"]/section/main/div/div/div/div[1]/div[2]/div/button[3]/span')
        self.click('//*[@id="main-container"]/section/main/div/div/div/div[1]/div[2]/div/button[3]/span')
        sleep(2)
        self.assert_text('创建直播计划', '/html/body/div[2]/div/div/div[2]/div/div/div[1]/div/div')
        self.assert_text('取 消', '/html/body/div[2]/div/div/div[2]/div/div/div[3]/div/button[1]/span')
        self.assert_text('保 存', '/html/body/div[2]/div/div/div[2]/div/div/div[3]/div/button[2]/span')

    # 校验历史直播直播详情
    def check_history_live_detail(self):
        # 获取元素 用于详情断言 标题、时间、时长、上车商品数量
        liveTitle = self.find_elements('text--rKnD7', By.CLASS_NAME)[0].text
        liveTime = self.find_elements('sub--pS7e1', By.CLASS_NAME)[0].text
        LenliveTimes = self.find_elements('sub--pS7e1', By.CLASS_NAME)[1].text
        elements = self.find_elements('tbody tr td p', By.CSS_SELECTOR)
        onSaleItemLen = elements[5].text.split(' ')[1]
        # 点击第一个直播历史的查看详情按钮
        btns = self.find_elements('ant-btn.ant-btn-link.detail-btn--PsFXO', By.CLASS_NAME)
        btns[0].click()
        time.sleep(1)
        # 获取详情页信息
        detailTitleText = self.find_element('title2--Lesik', By.CLASS_NAME).text
        assert detailTitleText == liveTitle
        detailTime = self.find_element(
            '//*[@id="main-container"]/section/main/div/div/div[1]/div/div[1]/div/span[2]').text.split('·')[1]
        assert detailTime == liveTime
        detailTimes = self.find_element('live-time--AHjj1', By.CLASS_NAME).text
        assert detailTimes == LenliveTimes
        self.assert_text('历史上车商品',
                         '//*[@id="main-container"]/section/main/div/div/div[*]/div/div[2]/div[1]/div[1]/div')
        # 商品列表
        itemList = self.find_elements('list-item--yi7a8.goods-item--sidPr', By.CLASS_NAME)
        assert len(itemList) == int(onSaleItemLen)

    # <=====历史直播页end====>
    # <=====直播设置页start====>
    # 校验直播设置页
    def check_live_assistant_page_set(self):
        time.sleep(1)
        # 商品设置
        itemSetList = ['尺码表展示', '商品参数展示', '上车并讲解设置', '直播商品保存', '上车位置设置']
        itemSetElement = self.find_elements('title--ce1ah', By.CLASS_NAME)
        for i in range(len(itemSetElement)):
            assert itemSetElement[i].text == itemSetList[i]
        # 直播设置
        self.click('rc-tabs-0-tab-直播设置', By.ID)
        time.sleep(0.5)
        liveSetList = ['屏蔽美化设置', '粉丝团模式', '直播间装修工具']
        liveSetElement = self.find_elements('title--ce1ah', By.CLASS_NAME)
        for i in range(len(liveSetList)):
            assert liveSetElement[len(liveSetElement) - 1 - i].text == liveSetList[len(liveSetList) - 1 - i]

    # 校验直播设置页-尺码表切换展示
    def check_live_assistant_page_set_size_display(self):
        time.sleep(2)
        xpath = '//*[@id="rc-tabs-0-panel-商品设置"]/div/div[2]/div/div/div[1]/div[1]/label[1]/span[1]/input'
        element = self.is_clicked(xpath)
        # 判断第一个勾选框是否是勾选状态
        # 不是勾选状态，点击勾选
        if element == False:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[2]/div/div/div[1]/div[1]/label[1]/span[2]')
            time.sleep(1)
            self.assert_text('确认修改为：展示？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            self.assert_false(element)

        elif element == True:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[2]/div/div/div[1]/div[1]/label[2]/span[2]')
            time.sleep(1)
            self.assert_text('确认修改为：不展示？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            self.assert_true(element)

    # 校验直播设置页-商品参数切换展示
    def check_live_assistant_page_set_product_display(self):
        xpath = '//*[@id="rc-tabs-0-panel-商品设置"]/div/div[4]/div/div/div[1]/div[1]/label[1]/span[1]/input'
        element = self.is_clicked(xpath)
        if element == False:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[4]/div/div/div[1]/div[1]/label[1]/span[2]')
            time.sleep(1)
            self.assert_text('确认修改为：展示？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            self.assert_false(element)

        elif element == True:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[4]/div/div/div[1]/div[1]/label[2]/span[2]')
            time.sleep(1)
            self.assert_text('确认修改为：不展示？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            self.assert_true(element)
        time.sleep(1)

    # 校验直播设置页-上车并讲解设置
    def check_live_assistant_page_set_onCart_explain(self):
        xpath = '//*[@id="rc-tabs-0-panel-商品设置"]/div/div[6]/div/div/div[1]/div[1]/label[1]/span[1]/input'
        element = self.is_clicked(xpath)
        if element == False:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[6]/div/div/div[1]/div[1]/label[1]/span[2]')
            time.sleep(1)
            self.assert_text('是否确认修改为“商品价可售”', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            time.sleep(1)
            self.assert_false(element)

        elif element == True:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[6]/div/div/div[1]/div[1]/label[2]/span[2]')
            time.sleep(1)
            self.assert_text('是否确认修改为“模糊价不可售”', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            self.assert_true(element)
        time.sleep(1)

    # 校验直播设置页-直播商品保存
    def check_live_assistant_page_set_live_product_sive(self):
        xpath = '//*[@id="rc-tabs-0-panel-商品设置"]/div/div[8]/div/div/div[1]/div[1]/label[1]/span[1]/input'
        element = self.is_clicked(xpath)
        if element == False:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[8]/div/div/div[1]/div[1]/label[1]/span[2]')
            time.sleep(1)
            self.assert_false(element)

        elif element == True:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[8]/div/div/div[1]/div[1]/label[2]/span[2]')
            time.sleep(1)
            self.assert_text('确认关闭直播商品保存？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            self.assert_true(element)
        time.sleep(1)

    # 直播设置-上车位置设置
    def check_onSale_position(self):
        xpath = '//*[@id="rc-tabs-0-panel-商品设置"]/div/div[10]/div/div/div[1]/div[1]/label[1]/span[1]/input'
        element = self.is_clicked(xpath)
        if element == False:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[10]/div/div/div[1]/div[1]/label[1]/span[2]')
            time.sleep(1)
            self.assert_text('确认切换到上车后置顶吗？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            self.assert_false(element)

        elif element == True:
            self.click('//*[@id="rc-tabs-0-panel-商品设置"]/div/div[10]/div/div/div[1]/div[1]/label[2]/span[2]')
            time.sleep(1)
            self.assert_text('确认切换到上车后置底吗？', '//*[@id="rcDialogTitle0"]')
            time.sleep(2)
            self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
            self.assert_true(element)
        time.sleep(1)

    # 直播间贴纸动图设置
    def stickers_yellowCart_gif(self):
        self.click('rc-tabs-0-tab-直播设置', By.ID)
        time.sleep(0.5)
        self.click("//span[text()='动图设置']")
        time.sleep(0.5)
        self.assert_text('小黄车点击引导', "//span[text()='小黄车点击引导']")

    # <=====直播设置页end====>

    # 判断上车顺序-置顶or置底
    def check_onSale_order(self):
        beforeItemList = self.get_itemList(0)
        self.click('//*[@id="rc-tabs-2-panel-c1"]/div[1]/div[2]/div/div[4]/span/span')
        time.sleep(2)
        # 获取选品弹窗中商品图片list == 商品list 用图片定位了商品，因为点击图片可以勾选（狗头）
        imgElements = self.find_elements('ant-image-img.ant-image-img-placeholder', By.CLASS_NAME)
        if imgElements:
            # 获取上车顺序字段 置顶 or 置底
            element = self.find_element('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/div[2]').text
            order = element.split('车')[1].split(' ')[0]
            # 获取选品弹窗中商品list == 商品list 精准获取商品id
            itemIdElements = self.find_elements('ant-typography.id-text--R02TA', By.CLASS_NAME)
            # 点击未勾选未上车商品
            index = 0
            if beforeItemList:
                index = len(beforeItemList)
            imgElements[index].click()
            # split后获取具体商品id
            itemIdText = []
            for itemIds in itemIdElements:
                text = itemIds.text
                itemIdText.append(text)
            addItemId = itemIdText[index].split(' ')[1]
            self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/div/button[2]/span')
            time.sleep(2)
            afterItemList = self.get_itemList(0)
            # 为空时新增 and 商品增加才判断上车顺序。商品减少意味着未勾选商品上车不做判断
            if beforeItemList and len(beforeItemList) < len(afterItemList):
                # 置顶判断上车后第一个商品
                if order == '顶部':
                    itemList = self.get_itemList(0)
                    cartItemId = itemList[0].split('-')[1]
                    assert cartItemId == addItemId
                    self.click(
                        '//*[@id="inner_c1goods-' + cartItemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')
                # 置底判断上车后最后一个商品
                elif order == '底部':
                    itemList = self.get_itemList(0)
                    print(itemList)
                    cartItemId = itemList[len(itemList) - 1].split('-')[1]
                    assert cartItemId == addItemId
                    self.click(
                        '//*[@id="inner_c1goods-' + cartItemId + '"]/div/div/div/div/div/div[1]/div[3]/div/button[1]/span')

    # 规格明细
    def check_sku_button(self):
        self.check_on_sale_item()
        itemList = self.get_itemList(0)
        # 遍历获取商品id
        for i in range(0, len(itemList)):
            # 点击价格库存打开价格库存弹窗
            try:
                self.assert_text('价格库存',
                                 '//*[@id="inner_' + itemList[
                                     i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            except TextNotVisibleException:
                continue
            else:
                self.click(
                    '//*[@id="inner_' + itemList[i] + '"]/div/div/div/div/div/div[1]/div[3]/div/button[3]/span')
            # 获取sku详情，sku大于1时，才会列表透出规格明细
            skuList = self.find_elements('ant-table-row.ant-table-row-level-0', By.CLASS_NAME)
            if len(skuList) > 1:
                # 价格 # 规格信息 # 库存 # 活动价格
                PriceList = []
                NameList = []
                StockList = []
                activityPriceList = []
                for i in range(0, len(skuList)):
                    # 规格信息 ['165/80A', '100', '增加', '-']
                    skuElements = skuList[i].text.split('\n')
                    # 规格名称 ['165/80A', '170/84A']
                    NameList.append(skuElements[0])
                    # 价格 ['28', '28']
                    skuId = skuList[i].get_attribute('data-row-key')
                    skuPrice = self.find_element('presetPrice-' + skuId, By.ID).get_attribute('value').split(' ')[1]
                    PriceList.append(skuPrice)
                    # 库存 ['100', '200']
                    StockList.append(skuElements[1])
                    # 活动价格 空：['-', '-']
                    activityPrice = skuElements[len(skuElements) - 1]
                    activityPriceList.append(activityPrice)
                # 关闭价格库存弹窗
                self.click("//span[text()='取消']")
                # 点击规格明细，打开规格明细弹窗
                self.click(
                    '//*[@id="inner_c1goods-' + itemList[i] + '"]/div/div/div/div/div/div[2]/div[2]/div')
                time.sleep(1)
                # 获取规格明细弹窗中数据
                skuElements = self.find_elements('ant-table-row.ant-table-row-level-0', By.CLASS_NAME)
                skuNameList = []
                skuPriceList = []
                skuStockList = []
                for i in range(0, len(skuElements)):
                    # 规格信息 ['规格', '售价', '库存', '成交金额']
                    skuElementsList = skuElements[i].text.split(' ')
                    skuNameList.append(skuElementsList[0])
                    skuPriceList.append(skuElementsList[1].split('￥')[1])
                    skuStockList.append(skuElementsList[2].split('/')[1])
                # 断言 规格信息
                for i in range(0, len(skuElements)):
                    assert NameList[i] == skuNameList[i]
                    assert PriceList[i] == skuPriceList[i]
                    assert StockList[i] == skuStockList[i]
                # 断言成功 break
                break
            else:
                # 关闭价格库存弹窗,重新下一个商品
                self.click('/html/body/div[*]/div/div[2]/div/div[2]/div[3]/button[1]/span')

    # 跳过直播设置贴纸引导
    def skip_stickers_guider(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-直播设置"]/span')
        time.sleep(2)
        self.refresh_page()
        time.sleep(2)
        self.click("//span[text()='公告板管理']")
        self.sleep(2)
        self.refresh_page()

    # 替换弹窗
    def check_replace_popup(self):
        while True:
            # 替换弹窗是否存在
            elements = self.find_elements('//*[@class="modal-title--HzOnk" and text()="替换"]')
            if elements:
                self.click('//span[text()="确定"]')
                break
            else:
                break

    # 贴纸-直播公告
    def stickers_live_notice(self):
        self.skip_stickers_guider()
        # 点击公告板管理
        self.click("//span[text()='公告板管理']")
        # 点击 直播公告
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[3]/div[2]/div[1]/div[2]/div/div[1]/div[1]/div[1]/div')
        num = random.randint(1, 100)
        # 设置标题
        inputTitleText = '自动化' + str(num)
        self.input('title', inputTitleText, By.ID)
        # 设置内容
        inputText = '自动化测试' + str(num)
        self.input('content1', inputText, By.ID)
        # 点击保存
        self.click("//span[text()='保存']")
        time.sleep(1)
        # 点击使用
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[2]/div[2]/div/div[2]')
        # 替换弹窗判断
        self.check_replace_popup()
        # 点击投放
        self.click("//span[text()='确认']")
        time.sleep(1)
        # 断言 公告为投放中
        self.click("//span[text()='公告板管理']")
        time.sleep(1)
        # 断言标题和内容
        checkTitleText = self.find_elements('stickerNoticeTitle--jbICs', By.CLASS_NAME)
        assert checkTitleText[0].text == inputTitleText
        checkInputText = self.find_elements('stickerNoticeContent--SacBt', By.CLASS_NAME)
        assert checkInputText[0].text == inputText
        # 断言右侧预览中标题和内容
        TitleElements = self.find_elements('stickerNoticeTitle--jbICs', By.CLASS_NAME)
        TextElements = self.find_elements('stickerNoticeContent--SacBt', By.CLASS_NAME)
        PreviewTitleText = TitleElements[len(TitleElements) - 1].text
        PreviewText = TextElements[len(TextElements) - 1].text
        assert PreviewTitleText == inputTitleText
        assert PreviewText == inputText
        # 断言使用中状态
        self.assert_text('使用中', '//*[@class="mask--I1gb_"]')

    # 贴纸-服务保障
    def stickers_guarante_notice(self):
        self.skip_stickers_guider()
        # 点击公告板管理
        self.click("//span[text()='公告板管理']")
        # 点击 服务保障
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[3]/div[2]/div[1]/div[2]/div/div[1]/div[2]/div[1]/div')
        time.sleep(1)
        # 获取权益单选框
        equityList = self.find_elements('ant-select-selection-item', By.CLASS_NAME)

        # 选择权益1 填写权益1内容
        equityList[0].click()
        time.sleep(1)
        equityListOne = ['破损包退', '过敏包退', '7天无理由', '极速退款', '运费险', '退货补运费']
        caseList = self.find_elements('ant-select-item-option-content', By.CLASS_NAME)
        # 断言权益信息
        for i in range(0, len(equityListOne) - 1):
            assert equityListOne[i] == caseList[i].text
        caseList[1].click()
        equityOneText = caseList[1].text
        num = random.randint(1, 100)
        inputEquityOneInfoText = '自动化' + str(num)
        self.input('content1', inputEquityOneInfoText, By.ID)

        # 选择权益2 填写权益2内容
        equityList[1].click()
        time.sleep(1)
        equityListTwo = ['无', '破损包退', '过敏包退', '7天无理由', '极速退款', '运费险', '退货补运费']
        caseList = self.find_elements('ant-select-item-option-content', By.CLASS_NAME)
        # 断言权益信息
        for i in range(0, len(equityListTwo) - 1):
            assert equityListTwo[i] == caseList[13 + i - 7].text
        caseList[9].click()
        equityTwoText = caseList[9].text
        num = random.randint(1, 100)
        inputEquityTwoInfoText = '自动化' + str(num)
        self.input('content2', inputEquityTwoInfoText, By.ID)

        # 点击保存
        self.click("//span[text()='保存']")
        time.sleep(1)
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[2]/div[2]/div/div[2]')
        # 替换弹窗
        self.check_replace_popup()
        self.click("//span[text()='确认']")
        time.sleep(1)
        self.click("//span[text()='公告板管理']")
        time.sleep(1)
        # 断言 公告为投放中
        self.assert_text('使用中', '//*[@class="mask--I1gb_"]')
        # 断言 内容
        checkTitleText = self.find_elements('stickerGuaranteeTitle--aUISM', By.CLASS_NAME)
        assert checkTitleText[0].text == '服务保障'
        checkText = self.find_elements('stickerGuaranteeContent--gMwZE', By.CLASS_NAME)
        assert equityOneText == checkText[0].text
        assert equityTwoText == checkText[1].text
        checkEquityInfo = self.find_elements('stickerGuaranteeText--WjSjs', By.CLASS_NAME)
        assert checkEquityInfo[0].text == inputEquityOneInfoText
        assert checkEquityInfo[1].text == inputEquityTwoInfoText

    # 贴纸-直播福利
    def stickers_live_benefit(self):
        self.skip_stickers_guider()
        # 点击公告板管理
        self.click("//span[text()='公告板管理']")
        # 点击 直播福利
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[3]/div[2]/div[1]/div[2]/div/div[1]/div[3]/div[1]/div')
        # 编辑
        num = random.randint(1, 100)
        inputLine1Text = '自动化' + str(num)
        inputLine2Text = '自动化' + str(num)
        inputLine3Text = '自动化' + str(num)
        inputLine4Text = '自动化' + str(num)
        self.input('line1', inputLine1Text, By.ID)
        self.input('line2', inputLine2Text, By.ID)
        self.input('line3', inputLine3Text, By.ID)
        self.input('line4', inputLine4Text, By.ID)
        # 点击保存
        self.click("//span[text()='保存']")
        time.sleep(1)
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[2]/div[2]/div/div[2]')  # 使用
        self.check_replace_popup()
        self.click("//span[text()='确认']")  # 确认投放
        time.sleep(1)
        # 断言 公告为投放中
        self.click("//span[text()='公告板管理']")
        time.sleep(1)
        checkTitleText = self.find_elements('stickerWelfareTitle--zwMHp', By.CLASS_NAME)
        assert checkTitleText[0].text == '直播福利'
        checkTextLine1 = self.find_elements('stickerWelfareContent--IAOJA', By.CLASS_NAME)
        assert checkTextLine1[0].text == inputLine1Text
        assert checkTextLine1[1].text == inputLine3Text
        checkTextLine2 = self.find_elements('stickerWelfareText--Id3se', By.CLASS_NAME)
        assert checkTextLine2[0].text == inputLine2Text
        assert checkTextLine2[1].text == inputLine4Text
        self.assert_text('使用中', '//*[@class="mask--I1gb_"]')

    # 贴纸-主播信息
    def stickers_seller_info(self):
        self.skip_stickers_guider()
        # 点击公告板管理
        self.click("//span[text()='公告板管理']")
        # 点击主播信息
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[3]/div[2]/div[1]/div[2]/div/div/div[4]/div[1]/div/div')
        # 编辑
        num = random.randint(1, 100)
        sellerNameText = '自动' + str(num)
        sellerBaseInfoText = '自动化' + str(num)
        sellerSizeText = '自动化' + str(num)
        self.input('sellerName', sellerNameText, By.ID)
        self.input('sellerBaseInfo', sellerBaseInfoText, By.ID)
        self.input('sellerSize', sellerSizeText, By.ID)
        # 点击保存
        self.click("//span[text()='保存']")
        time.sleep(1)
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[2]/div[2]/div/div[2]')  # 使用
        self.check_replace_popup()
        self.click("//span[text()='确认']")  # 确认投放
        time.sleep(1)
        # 断言 公告为投放中
        self.click("//span[text()='公告板管理']")
        time.sleep(1)
        checkTitleText = self.find_elements('stickerInfoTitle--oiWUK', By.CLASS_NAME)
        assert checkTitleText[0].text == '主播信息'
        checkSellNameText = self.find_elements('stickerInfoContent--tdwla', By.CLASS_NAME)
        assert checkSellNameText[0].text == sellerNameText
        checkSellerBaseInfoText = self.find_elements('stickerInfoText--yXJtm', By.CLASS_NAME)
        assert checkSellerBaseInfoText[0].text == sellerBaseInfoText
        assert checkSellerBaseInfoText[1].text == sellerSizeText
        self.assert_text('使用中', '//*[@class="mask--I1gb_"]')

    # 贴纸-降温公告
    def stickers_temperature_drop(self):
        self.skip_stickers_guider()
        # 点击公告板管理
        self.click("//span[text()='公告板管理']")
        # 点击降温公告
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[3]/div[2]/div[1]/div[2]/div/div[1]/div[5]/div[1]/div')
        # 编辑
        num = random.randint(1, 100)
        inputLine1 = '自动' + str(num)
        inputLine2 = '自动化' + str(num) + '°C'
        self.input('line1', inputLine1, By.ID)
        self.input('line2', inputLine2, By.ID)
        # 点击保存
        self.click("//span[text()='保存']")
        time.sleep(1)
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[2]/div[2]/div/div[2]')  # 使用
        self.check_replace_popup()
        self.click("//span[text()='确认']")  # 确认投放
        time.sleep(1)
        # 断言 公告为投放中
        self.click("//span[text()='公告板管理']")
        time.sleep(1)
        checkInputOneText = self.find_elements('stickerWelfareContent--IAOJA', By.CLASS_NAME)
        assert checkInputOneText[0].text == inputLine1
        checkInputTwoText = self.find_elements('stickerWelfareText--Id3se', By.CLASS_NAME)
        assert checkInputTwoText[0].text == inputLine2
        self.assert_text('使用中', '//*[@class="mask--I1gb_"]')

    # 判断车上是否有商品
    def check_on_sale_item(self):
        itemList = self.get_itemList(0)
        if itemList == None:
            self.on_live_add_item(3)
        time.sleep(1)

    # 判断待车上是否有商品
    def check_wait_on_sale_item(self):
        itemList = self.get_itemList(1)
        if itemList == None:
            self.wait_on_sale_add(3)
        time.sleep(1)

    # 判断勾选框是否被选中
    def is_clicked(self, Xpath):
        element = self.get_element(Xpath).is_selected()
        return element

    def judge_text(self, text):
        time.sleep(2)
        if text in self.get_page_source():
            return True
        return False

    # 判断选品弹窗的商品有没有被选中
    def isCheckBox(self, xPath):
        isCheckBox = self.get_element(xPath)
        # 如果复选框没有被选中，就点击复选框
        if isCheckBox != 'ant-checkbox ant-checkbox-checked':
            self.click(xPath)

    # 获取列表商品list
    def get_itemList(self, index):
        # index枚举 小黄车列表=0，待上车列表=1，历史上车列表=2
        # 操作前注意列表有商品，不然会定位失败！！！！
        try:
            # 获取父元素节点-小黄车列表父元素节点
            parent_element = self.driver.find_elements(By.XPATH, '//div[@data-test-id="virtuoso-item-list"]')[index]
        except IndexError:
            return None
        else:
            # 使用 XPath 获取所有每个商品卡的元素
            item_card_child_elements = self.execute_script("return arguments[0].children", parent_element)
            # 打印子元素的数量
            itemList = []
            print(f"Number of child elements: {len(item_card_child_elements)}")
            if len(item_card_child_elements) == 0:
                return itemList
            # 遍历子元素,获取商品id
            for child in item_card_child_elements:
                # 继续获取子元素
                itemChild = self.execute_script("return arguments[0].children", child)
                itemId = itemChild[0].get_attribute('id')
                itemList.append(str(itemId))
            return itemList
