# -*- coding: utf-8 -*-
# auth: cy
# create 11-27-18
# update

from ddt import ddt
from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain

# 获取账号信息
account_data = get_account_info("helper_assistant_preLive")
# 用户名 account_data['account']
# 密码 account_data['password']
host = get_domain("MERCHANT_DOMAIN")



# from run_path import test_report_path

@ddt
class testQRCodeLogin(BaseCase):


    def login_page(self):
        self.open(host)

        tabs_text = self.get_text('#root > div > div.contentWrap--1G9Bm > div > div > div')  # 获取对应的text
        tab_list = ["我是店主", "我是员工", "0元开店"]

        for item in tab_list:
            self.assert_in(item, tabs_text)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.assert_text("扫码登录", "div.choseTab--12n2b > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.choseTab--12n2b > div:nth-child(2)")
        self.click("div.choseTab--12n2b > div:nth-child(2)")

    def login_success(self):
        self.login_page()
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.assert_element("div.header-root-logo")




if __name__ == '__main__':
    pass

