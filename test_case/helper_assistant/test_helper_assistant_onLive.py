from time import sleep

import pytest
from ddt import ddt

from test_case.helper_assistant.load.load_helper_assistant_onLive import LoadHelperAssistantOnLive


@ddt
class TestHelperAssistantOnLiveV2(LoadHelperAssistantOnLive):

    # 进行初始化和清理工作
    def setUp(self):
        super().setUp()
        print("我是TestHelperAssistantOnLiveV2 setUp方法，进行测试前的初始化工作")
        # 浏览器最大化
        self.maximize_window()
        self.login("HELPER_ASSISTANT_DOMAIN", "helper_assistant_onLive")
        sleep(6)
        # 跳过新手引导
        self.skip_guider()
        self.check_isLive()

    def tearDown(self):
        super().tearDown()
        print("我是TestHelperAssistantOnLiveV2 tearDown方法，执行测试后的清除工作")

    # 跳过新手引导
    #@pytest.mark.skip
    def test_checkout_new_guide(self, selector=None):
        # 跳过新手引导
        self.skip_guider()

    # 校验顶部开播信息
    # @pytest.mark.skip
    def test_check_top_LiveInfo(self, selector=None):
        # 校验预开播页任务区
        self.check_top_LiveInfo()

    # 校验开播跟播助手顶部直播设置
    #@pytest.mark.skip
    def test_check_live_set(self):
        self.check_live_set()

    # 校验小黄车列表按钮
    #@pytest.mark.skip
    def test_checkout_shop_car_title(self, selector=None):
        # 判断小黄车管理标题区域
        self.check_shop_car_title()

    # 直播中商品上车
    #@pytest.mark.skip
    def test_on_live_add_item(self):
        self.on_live_add_item(3)

    # 全选商品下车
    #@pytest.mark.skip
    def test_onSale_total_getOff(self):
        self.onSale_total_getOff()
        self.on_live_add_item(3)

    # 全选按钮信息校验
    #@pytest.mark.skip
    def test_check_all_button(self):
        self.check_all_button()

    # 商品id搜索
    def test_check_search_itemID(self):
        self.check_search_itemID()

    # 商品序号搜索
    def test_check_search_order(self):
        self.check_search_order()