import time
from time import sleep

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env

# 获取账号信息
#account_data = get_account_info("helper_assistant")
#host = get_domain("HELPER_ASSISTANT_DOMAIN")

class BaseTestCase(BaseCase):

    def login(self, domain, account):

        self.maximize_window()

        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        elif self.var2 and self.var2 == 'prt':
            env = 'prt'
        elif self.var3 and self.var3 == 'prt':
            env = 'prt'
        else:
            env = 'online'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    def child_login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)
        self.open(host)
        self.click('//span[contains(text(),"我是员工")]')
        self.click('//span[contains(text(),"密码登录")]')
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("//button[text()='快手商家账号登录']")
        sleep(2)
        # self.assert_text("选择登录店铺", '//*[@id="rcDialogTitle0"]')
        self.click(account_data['default_log_select_page'])  # 点击小店试试的小店
        sleep(1)