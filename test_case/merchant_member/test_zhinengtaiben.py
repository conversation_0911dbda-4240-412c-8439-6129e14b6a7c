import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time


@ddt
class TestMemberCommon(BaseTestCase):
    def test_member_common_all_tab1(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/live/ai-prompt/home")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)
        # 点击使用教程
        self.click("//span[contains(text(),'使用教程')]")
        self.assert_text("【对外】AI智能台本使用教程", "//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'【对外】AI智能台本使用教程')]")
        sleep(2)
        self.switch_to_window(0)
        # 选择排序
        self.click("//span[@title='生成时间 近到远']")
        self.assert_text("我的台本","//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 选择远到近
        self.click("//div[contains(text(),'生成时间 远到近')]")
        self.assert_text("我的台本", "//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 选择排序
        self.click("//div[@class='kwaishop-seller-live-ai-prompt-pc-select-selector']")
        self.assert_text("我的台本", "//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 选择最后编辑时间近到远
        self.click("//div[contains(text(),'最后编辑时间 近到远')]")
        self.assert_text("我的台本", "//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 选择排序
        self.click("//div[@class='kwaishop-seller-live-ai-prompt-pc-select-selector']")
        self.assert_text("我的台本", "//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 选择最后编辑时间远到近
        self.click("//div[contains(text(),'最后编辑时间 远到近')]")
        self.assert_text("我的台本", "//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 点击下一页
        self.click("//li[@title='下一页']//button[@type='button']")
        self.assert_text("我的台本", "//div[@class='PTrTh_CnIBrBPs5x3kf5']")
        sleep(2)
        # 点击去生成
        self.click("//span[contains(text(),'去生成')]")
        self.assert_text("智能生成设定", "//div[contains(text(),'智能生成设定')]")
        sleep(2)
        self.switch_to_window(0)


    def test_member_common_all_tab(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/live/ai-prompt/script")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)

        #点击选择商品
        self.click("//span[contains(text(),'选择商品')]")
        self.assert_text("选择商品", '//*[@id="root"]/div/div[2]/div/div/div/form/div[2]/div[2]/div/div/div/button')
        sleep(2)

        #点击分销推广
        self.click('/html/div/div/div[2]/div/div/div[2]/div/div[1]/div/div[1]/div[1]/div/div[2]')
        self.assert_text("分销推广", '/html/div/div/div[2]/div/div/div[2]/div/div[1]/div/div[1]/div[1]/div/div[2]')
        sleep(2)

        #点击取消
        self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/div[1]/button')
        sleep(2)

        #点击真诚亲切
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.assert_text("真诚亲切", '//*[@id="rc-tabs-0-tab-2"]')
        sleep(2)

        #点击家人们
        self.click('//*[@id="rc-tabs-1-tab-家人们"]')
        self.assert_text("家人们", '//*[@id="rc-tabs-1-tab-家人们"]')
        sleep(2)

    def test_member_common_all_tab_online(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/live/ai-prompt/script")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)

        #点击选择商品
        self.click("//span[contains(text(),'选择商品')]")
        self.assert_text("选择商品", '//*[@id="root"]/div/div[2]/div/div/div/form/div[2]/div[2]/div/div/div/button')
        sleep(2)
        #点击分销推广
        self.click('/html/div/div/div[2]/div/div/div[2]/div/div[1]/div/div[1]/div[1]/div/div[2]')
        self.assert_text("分销推广", '/html/div/div/div[2]/div/div/div[2]/div/div[1]/div/div[1]/div[1]/div/div[2]')
        sleep(2)
        #点击取消
        self.click('/html/div/div/div[2]/div/div/div[3]/div/div[2]/div[1]/button')
        sleep(2)
        # 点击直播计划导入商品
        self.click("//span[contains(text(),'直播计划导入商品')]")
        self.assert_text("从直播计划导入", "//div[contains(text(),'从直播计划导入')]")
        sleep(2)
        # 点击取消
        self.click("//div[@class='HUvXmHqppfED0iPZWKHt']//span[contains(text(),'取 消')]")
        sleep(2)
        #点击真诚亲切
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.assert_text("真诚亲切", '//*[@id="rc-tabs-0-tab-2"]')
        sleep(2)
        #点击家人们
        self.click('//*[@id="rc-tabs-1-tab-家人们"]')
        self.assert_text("家人们", '//*[@id="rc-tabs-1-tab-家人们"]')
        sleep(2)
        # 点击宝宝
        self.click("//div[@class='pLBVuzChE8zU2bPBwqvB']//div[@role='tablist']//div[3]")
        self.assert_text("宝宝", "//div[@class='pLBVuzChE8zU2bPBwqvB']//div[@role='tablist']//div[3]")
        sleep(2)


