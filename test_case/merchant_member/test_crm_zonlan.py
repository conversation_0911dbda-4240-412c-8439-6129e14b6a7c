import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time


@ddt
class TestMemberCommon(BaseTestCase):

    @pytest.mark.skip
    def test_member_common_all_tab(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/customer/overview")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)

        self.click("//div[@id='statTabDriver1']//div[@class='h7M9xOX2eyTwJLGicd3g']")
        sleep(2)
        self.click("//div[@id='statTabDriver2']//div[@class='h7M9xOX2eyTwJLGicd3g']")
        sleep(2)
        self.click("//body/div[@id='main-root']/div[@class='seller-main-spin-nested-loading']/div[@class='seller-main-spin-container']/div[@id='main-pc-atmosphere-layout']/div[@id='main-pc-page-layout']/div[@class='HkCTsYZjM3B7nYatmuHR']/div[@class='js-page-content p9K58evEYBgCD6FuDIKq OIk4RvF5SyjZUH1tXi9j undefined']/div[@id='micro-viewport']/div[@id='__qiankun_microapp_wrapper_for_kwaishop_seller_member_workbench_pc__']/div[@id='root']/div[@class='kGNqlZJNCcLb41qBeyDL']/div[@class='ejlnQuZ9QVMmu2IIZPRu']/div[@class='HCLOyVwRoFjbdEll3igo']/div[1]/div[2]/span[2]//*[name()='svg']")
        sleep(2)
        self.click("(//button[contains(text(),'关闭')])[1]")
        sleep(2)
        self.click("//div[@id='tabsNav-tab-reachData']")
        sleep(2)
        self.click("//span[contains(text(),'手动短信数据')]")
        sleep(2)
        self.click("//span[contains(text(),'自动触达数据')]")
        sleep(2)
        self.click("(//span[contains(text(),'近7天')])[1]")
        sleep(2)
        self.click("(//span[contains(text(),'近30天')])[1]")
        sleep(2)
        self.click("//div[@id='tabsNav-tab-msgManage']")
        sleep(2)
        self.click("//span[contains(text(),'立即充值')]")
        sleep(2)
        self.click("//span[contains(text(),'取 消')]")
        sleep(2)

