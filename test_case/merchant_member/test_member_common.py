import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time



@ddt
class TestMemberCommon(BaseTestCase):
    @pytest.mark.skip
    def test_member_common_all_tab(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/member/score")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("知道了", "//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                sleep(2)
            else:
                break
        self.click("//span[text()='会员配置']")
        sleep(2)
        self.assert_text("等级设置", "//div[text()='等级设置']")
        self.assert_text("编辑修改", "//span[text()='编辑修改']")
        print("检查是否可点击" + str(self.is_element_clickable("//span[text()='编辑修改']")))
        if self.is_element_clickable("//span[text()='编辑修改']"):
        # if not self.is_element_present("//span[text()='编辑修改']"):
            self.hover("//span[text()='编辑修改']")
            sleep(2)
            if self.is_exact_text_visible("//span[text()='查看']"):
                sleep(1)
                self.click("//span[text()='关闭']")
                sleep(2)
            else:
                self.click("//span[text()='编辑修改']")
                sleep(1)
                self.click("//span[text()='保存']")
                sleep(2)
        self.assert_text("每年最多允许修改 2 次", "//span[text()='每年最多允许修改 2 次']")
        sleep(2)
        self.click("//div[text()='会员规则']")
        self.assert_text("一、会员权益规则", "//div[text()='一、会员权益规则']")
        sleep(2)
        # self.click("//div[text()='会员服务']")
        # self.assert_text("保 存", "//span[text()='保 存']")
        # sleep(2)
        self.click("//span[text()='会员权益']")

        # self.assert_text("复购转化", "//span[text()='复购转化']")
        # self.assert_text("促进回购", "//span[text()='促进回购']")
        # self.assert_text("会员忠诚", "//span[text()='会员忠诚']")
        # self.assert_text("运营攻略", "//span[text()='运营攻略']")
        self.assert_text("领取/使用时间", "//th[text()='领取/使用时间']")
        sleep(2)
        self.click("//div[text()='会员专享券']")
        self.assert_text("使用会员专享券，提升店铺会员私域的成交金额和复购频次",
                         "//span[text()='使用会员专享券，提升店铺会员私域的成交金额和复购频次']")
        self.click("(//button[@class='kwaishop-seller-member-btn kwaishop-seller-member-btn-primary zhmvw2d10GFLy7jhcy9M'])[1]")
        sleep(2)
        self.click("//span[contains(text(),'取消')]")
        sleep(2)
        self.click("//div[text()='入会优惠券']")
        self.assert_text("首次入会即可领取新会员优惠券", "//span[text()='首次入会即可领取新会员优惠券']")
        sleep(2)
        self.click("//div[text()='会员满额返礼']")
        self.assert_text("会员在指定时间内累计购物金额达到活动门槛，即可领取限量奖励，有助于提升会员客单价", "//div[@class='uqDiLEAuDm7Geu3kJpCS']//div[1]")
        sleep(2)
        self.click("//div[text()='会员派样']")
        self.assert_text("为会员低价派发样品提升会员招募效果，追加回购券促进正装回购", "//div[@class='fSv1ISVC3XlQlbsObzQ4']//div[1]")
        self.click("//span[contains(text(),'新建会员派样')]")
        self.click("//button[@class='marketing-paas-btn']//span[contains(text(),'选择商品')]")
        self.click("(//span[contains(text(),'取 消')])[1]")
        sleep(2)
        self.click("(//span[contains(text(),'取消')])[1]")  
        sleep(2)
        self.click("//div[text()='会员商品']")
        sleep(2)
        self.click("//div[text()='会员群聊']")
        self.assert_text("您已有会员群聊，点击进行群聊管理", "//span[text()='您已有会员群聊，点击进行群聊管理']")
        sleep(2)
        self.click("//span[text()='积分管理']")
        sleep(2)
        self.assert_text("编辑", "//span[text()='编辑']")
        self.click("//div[text()='积分互动']")
        sleep(2)

        # if self.is_element_present("//span[text()='停用']"):
        if self.is_element_present(
                "(//button[contains(@class,'kwaishop-seller-member-btn kwaishop-seller-member-btn-link')]//span)[2]"):
            self.click(
                "(//button[contains(@class,'kwaishop-seller-member-btn kwaishop-seller-member-btn-link')]//span)[2]")
            sleep(2)
            self.click("//span[text()='确定']")
            sleep(2)
            # self.click("//span[text()='编辑']")
            self.click("//button[contains(@class,'kwaishop-seller-member-btn kwaishop-seller-member-btn-link')]//span")
            sleep(2)
            point = random.randint(1, 100)
            self.type("input[placeholder='请输入发放积分(1-100)']", str(point))
            sleep(2)
            self.click("//span[text()='确定']")
            sleep(2)
            # self.click("//span[text()='启用']")
            self.click(
                "(//button[contains(@class,'kwaishop-seller-member-btn kwaishop-seller-member-btn-link')]//span)[2]")
            sleep(2)
            self.click(
                "(//button[contains(@class,'kwaishop-seller-member-btn kwaishop-seller-member-btn-primary')]//span)[2]")
            sleep(2)

        self.click("//div[text()='积分兑换']")
        sleep(2)
        self.click("//span[text()='查询']")
        sleep(2)
        self.click("//span[text()='会员查询']")
        sleep(2)
        if self.is_exact_text_visible("会员入口", "//span[text()='会员入口']"):
            self.click("//span[text()='会员入口']")
        if self.is_exact_text_visible("入口管理", "//span[text()='入口管理']"):
            self.click("//span[text()='入口管理']")
        sleep(2)
        self.click("//span[text()='会员招募']")
        sleep(2)
        self.assert_text("客服页入口", "//span[text()='客服页入口']")
        sleep(2)
        self.click("//span[text()='会员运营']")
        sleep(5)
        self.click("//span[text()='自营分销']")
        sleep(3)
        self.click("//span[text()='人群管理']")
        sleep(2)
        self.click("//span[text()='+ 新增自定义人群']")
        sleep(7)
        self.assert_text("2、直播间消费与互动行为", "//span[text()='2、直播间消费与互动行为']")
        sleep(1)
        self.assert_text("5、行业偏好标签", "//span[text()='5、行业偏好标签']")
        sleep(5)
        print("检查【入会时间】标签 是否可点击" + str(self.is_element_clickable("//div[text()='入会时间']")))
        sleep(2)
        self.click("//div[text()='入会时间']")
        sleep(2)
        self.click("//div[@id='root']/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[4]/div[2]/div[1]")
        # sleep(2)
        # self.find_element(By.XPATH("//div[text()='入会时间']"))
        sleep(5)

        if self.is_element_present("(//div[text()='入会时间'])[2]"):
            # self.click("(//input[@class='zone-crowd-edit-radio-input'])[3]")
            self.click("//span[text()='近三个月']")
            sleep(2)
            self.click("//span[text()='确 认']")
            sleep(2)
            self.click("//span[text()='更新数据']")
            sleep(2)
            self.click("//span[text()='保存人群']")
            sleep(2)
            self.type("//input[@class='zone-crowd-edit-input']","Ui测试群人包1" + str(point+1000))
            sleep(2)
            self.type("crowdInfoForm_crowdDesc","Ui测试群人包1" + str(point+1000)+"描述信息111")
            sleep(2)
            self.click("//span[text()='确 认']")
        sleep(5)
        self.click("//span[text()='用户']")
        sleep(3)
        self.click("//span[text()='人群管理']")
        sleep(3)
        self.click("//span[text()='自定义']")
        sleep(2)
        self.click("//span[text()='数据效果']")
        sleep(2)
        self.click("//span[text()='运营策略']")
        sleep(2)
        self.click("//span[text()='短信营销']")
        sleep(2)
        self.click("//span[text()='客服营销']")
        sleep(2)
        self.click("//span[text()='群聊管理']")
        sleep(2)
        self.click("//span[text()='会员权益']")
        sleep(2)
        self.click("//div[text()='会员群聊']")
        sleep(2)
        self.click("//span[text()='管理群聊']")
        sleep(2)
        self.click("//div[text()='群聊入口']")

        url2 = "https://syt.kwaixiaodian.com/zones/user/member-syt"
        self.open(url2)
        sleep(2)
        self.assert_text("会员运营", "//div[text()='会员运营']")
        self.assert_text("核心数据", "//span[text()='核心数据']")
        self.assert_text("会员成交人数占比", "//span[text()='会员成交人数占比']")

        self.click("//span[text()='自营分销']")
        sleep(2)
        self.assert_text("会员运营", "//div[text()='会员运营']")
        self.assert_text("核心数据", "//span[text()='核心数据']")
        self.assert_text("会员成交人数占比", "//span[text()='会员成交人数占比']")
