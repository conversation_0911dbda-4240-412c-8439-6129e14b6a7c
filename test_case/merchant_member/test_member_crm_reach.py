import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep


@ddt
class TestMemberCrmReach(BaseTestCase):
    @pytest.mark.skip
    def test_member_crm_chuda(self):
        """UI自动化-用户触达"""
        self.login("MERCHANT_DOMAIN", "member_account")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/customer/overview")
        print("页面title:", self.get_page_title())
        sleep(3)
        self.click('//*[@id="driver-popover-item"]/div[4]/button')  # 跳过弹窗引导
        self.assert_text("场景运营", "//div[text()='场景运营']")
        self.driver.maximize_window()  # 页面放大
        sleep(3)
        self.click("(//span[contains(text(),'老客')])[1]")
        sleep(3)
        self.click("(//span[contains(text(),'新建计划')])[1]")  # 复购触达
        self.assert_text("触达计划", "//span[text()='触达计划']")
        sleep(3)
        self.click('//*[@id="driver-popover-item"]/div[4]/button')
        sleep(3)
        self.click("(//span[contains(text(),'选择人群包')])[1]")  # 打开人群包弹窗
        self.assert_text("全部人群包", "//div[text()='全部人群包']")
        self.click("(//span[contains(text(),'取 消')])[2]")
        sleep(3)
        self.click("(//span[contains(text(),'选择商品')])[1]")  # 打开添加商品弹窗
        self.assert_text("添加商品", "//div[text()='添加商品']")
        self.click("(//span[contains(text(),'取 消')])[2]")
        sleep(3)
        self.click('//*[@id="channelType"]/label[2]/span[2]')  # 点击短信触达渠道
        self.assert_text("短信触达", "//div[text()='短信触达']")
        sleep(3)
        self.click("(//span[contains(text(),'取 消')])[1]")
        sleep(3)
        self.click("(//span[@id='menu_item_yZXNooPhbI8'])[1]")   # 点击用户触达
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')  # 跳过弹窗引导
        self.assert_text("手动触达计划", "//div[text()='手动触达计划']")
        sleep(3)
        self.click("(//div[@id='tabsNav-tab-reachData'])[1]")  # 触达数据
        self.assert_text("数据概览", "//div[text()='数据概览']")
        sleep(3)
        self.click("(//span[contains(text(),'自动触达数据')])[1]")  # 点击自动触达数据
        sleep(5)
        self.assert_text("消费营销", "//span[text()='消费营销']")
        sleep(3)
        self.click("(//div[@id='tabsNav-tab-msgManage'])[1]")  # 点击短信营销
        self.assert_text("我的模板", "//div[text()='我的模板']")
        sleep(3)
        self.click("(//span[contains(text(),'+新增短信模板')])[1]")
        self.assert_text("新建自定义模板", "//span[text()='新建自定义模板']")
        self.click("(//span[contains(text(),'取 消')])[1]")
        sleep(3)
        self.click("(//span[@id='menu_item_yZXNooPhbI8'])[1]")  # 点击用户触达
        self.click("(//span[contains(text(),'客服触达')])[1]")  # 客服触达
        sleep(5)
        self.assert_text("基础信息", "//div[text()='基础信息']")
        self.click("(//a[contains(text(),'用户触达')])[1]")  # 返回到用户触达页面
        sleep(3)
        self.click("(//span[contains(text(),'短信触达')])[1]")
        self.assert_text("短信触达", "//span[text()='短信触达']")
        sleep(3)
        self.click("(//a[contains(text(),'用户触达')])[1]")  # 返回到用户触达页面
        sleep(3)
        self.click("(//span[@id='menu_item_61U2zIR74PI'])[1]")  # 人群管理
        sleep(3)
        self.assert_text("潜客转化", "//div[text()='潜客转化']")
        self.assert_text("我的人群", "//span[text()='我的人群']")
        sleep(3)
        self.click("(//span[@id='menu_item_x3CXxb0iYqI'])[1]")
        self.assert_text("粉丝团升级专享券配置", "//span[text()='粉丝团升级专享券配置']")
        sleep(3)
        self.click("(//div[@id='tabsNav-tab-data'])[1]")
        self.assert_text("粉丝团数据", "//div[text()='粉丝团数据']")
        self.assert_text("使用效果", "//div[text()='使用效果']")


