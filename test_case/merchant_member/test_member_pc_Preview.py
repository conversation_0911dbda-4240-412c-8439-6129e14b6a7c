import random

import pytest
from ddt import ddt
import os
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep

@ddt
class TestMemberYUGAO(BaseTestCase):

    @pytest.mark.skip
    def test_member_yugao_list(self):
        self.login("MERCHANT_DOMAIN", "lyh_UI")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/live/preview/")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("知道了", "//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                sleep(2)
            else:
                break
        sleep(3)
        self.assert_text("直播预告收益", "//span[text()='直播预告收益']")
        self.click('//*[@id="root"]/div[1]/div[1]/div[2]/div[1]/div[1]/div/div[2]')
        self.assert_text("预告创建数量", "//span[text()='预告创建数量']")
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div/div/div[1]/div[1]/div/div[2]')    # 使用过
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div/div/div[1]/div[1]/div/div[1]')    # 生效中
        sleep(2)
        self.click('//*[@id="root"]/div[1]/div[2]/div[2]/div/div/div[1]/div[3]')    # 新建预告
        self.driver.maximize_window()
        sleep(5)
        self.assert_text("1.基础信息", "//div[text()='1.基础信息']")
        self.assert_text("2.关联商品与短视频", "//div[text()='2.关联商品与短视频']")
        self.assert_text("3.其他", "//div[text()='3.其他']")
        self.click("(//span[contains(text(),'添加短视频')])[1]")
        self.assert_text("请选择关联视频", "//span[text()='请选择关联视频']")
        sleep(2)
        self.click('//*[@id="root"]/div[1]/div[3]/div/button[1]')   # 取消
        self.assert_text("预告列表", "//div[text()='预告列表']")
        sleep(2)
        self.click("(//span[contains(text(),'追加商品')])[1]")  # 追加商品
        self.assert_text("添加商品", "//div[text()='添加商品']")
        sleep(2)
        self.click("(//span[contains(text(),'取 消')])[1]")  # 关闭商品抽屉
        sleep(2)
        self.click("(//span[contains(text(),'追加视频')])[1]")  # 追加视频
        self.assert_text("请选择关联视频", "//span[text()='请选择关联视频']")
        sleep(2)
        self.click("(//span[contains(text(),'取 消')])[1]")  # 关闭商品抽屉
        sleep(2)
        self.click("(//span[contains(text(),'预告详情')])[1]")
        self.assert_text("1.基础信息", "//div[text()='1.基础信息']")






