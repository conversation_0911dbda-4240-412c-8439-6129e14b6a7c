#
#
# from ddt import ddt
# from .base import BaseTestCase
# from time import sleep
#
#
#
# @ddt
# class TestMemberGroup(BaseTestCase):
#     def test_member_group(self):
#         self.login("MERCHANT_DOMAIN", "member_account2")
#         # 会员群聊
#         self.open("https://s.kwaixiaodian.com/zone/crm/chat/management")
#         sleep(2)
#         self.click("//div[text()='群聊入口']")
#         sleep(2)
#         self.click("//div[text()='群聊入口']")
#         sleep(2)
#         self.click("//div[text()='群聊运营']")
#         sleep(2)
#         self.click("//div[text()='群聊数据']")
#         sleep(5)
#         self.click("//span[text()='近30天']")
#
#
