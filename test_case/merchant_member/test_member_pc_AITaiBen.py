import random
import pytest
from ddt import ddt
from selenium.webdriver.common.by import By
from .base import BaseTestCase
from time import sleep


@ddt
class TestMemberAITaiBen(BaseTestCase):
    def test_member_AI_TaiBen(self):
        """AI台本"""
        self.login("MERCHANT_DOMAIN", "lyh_UI")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/live/plan")
        print("页面title:", self.get_page_title())
        self.driver.maximize_window()
        self.click("(//span[@id='menu_item_OB9J3YUbTOs'])[1]")  # 智能台本
        sleep(3)
        self.assert_text("智能直播台本", "//div[text()='智能直播台本']")
        self.click("(//span[contains(text(),'去生成')])[1]")
        sleep(3)
        self.assert_text("智能生成设定", "//div[text()='智能生成设定']")
        self.click("(//span[contains(text(),'选择商品')])[1]")
        sleep(3)
        self.assert_text("从货架选择", "//div[text()='从货架选择']")
        self.click(
            "(//label[@class='kwaishop-seller-live-ai-prompt-pc-checkbox-wrapper KUbxXLiatpB6BW3bEitr'])[1]")  # 勾选商品
        self.click("(//span[contains(text(),'添 加')])[1]")
        sleep(3)
        self.assert_text("货架商品", "//div[text()='货架商品']")
        self.click("(//span[contains(text(),'重新选择')])[1]")  # 重新选择二次弹窗
        self.click("(//span[contains(text(),'重 选')])[1]")
        sleep(2)
        self.click("(//a[contains(text(),'智能台本')])[1]")  # 返回到台本列表页
        sleep(2)
        self.assert_text("我的台本", "//div[text()='我的台本']")
        self.click("(//div[@class='BPghGJ9w9yh2f29N1PXI x4cYXj1Oljq3rRjYia2m'])[1]")  # 已生成的点击台本
        sleep(2)
        self.assert_text("智能生成设定", "//div[text()='智能生成设定']")
        self.assert_text("商品讲解台本", "//div[text()='商品讲解台本']")
        sleep(5)
        # self.refresh_page()
        self.click(
            "(//div[@class='vFhK6HZwlqgzEL5amndu'])[1]")  # 点击商品台本
        self.click("(//button[contains(text(),'我知道了')])[1]")
        sleep(2)
        self.assert_text("不满意？再写一版", "//span[text()='不满意？再写一版']")
        self.assert_text("反馈", "//span[text()='反馈']")
        self.click("(//span[contains(text(),'反馈')])[1]")
        sleep(2)
        self.assert_text("根据您的真实诉求，我们持续优化", "//div[text()='根据您的真实诉求，我们持续优化']")