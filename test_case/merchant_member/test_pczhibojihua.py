import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time


@ddt
class TestMemberCommon(BaseTestCase):
    #@pytest.mark.skip
    def test_member_common_all_tab(self):
        """UI自动化-直播计划"""
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.driver.maximize_window()
        sleep(1)
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/live/plan")

        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)
        # 点击流量活动
        self.click("//div[@class='tabItemContent___BLPRC'][contains(text(),'流量活动')]")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击直播日历
        self.click("//div[contains(text(),'直播日历')]")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击上一周
        self.click("//span[contains(text(),'上一周')]")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击下一周
        self.click("//span[contains(text(),'下一周')]")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击本周
        self.click("//span[contains(text(),'本 周')]")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击使用教程
        self.click("//a[contains(text(),'使用教程')]")
        self.assert_text("如何通过直播计划做好直播前的盘播动作", "//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'如何通过直播计划做好直播前的盘播动作')]")
        sleep(2)
        self.switch_to_window(0)
        #点击新建直播计划
        self.click("//span[contains(text(),'新建计划')]")
        self.assert_text("新建直播带货计划", "(//div[@id='rcDialogTitle0'])[1]")
        sleep(2)
        # 点击取消直播计划弹窗
        self.click("//button[@class='kwaishop-marketing-live-plan-pc-btn']")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击投放预告
        self.click("//span[contains(text(),'投放预告')]")
        self.assert_text("直播预告", "//div[@id='rc-tabs-1-tab-1']")
        sleep(2)
        self.switch_to_window(0)
        # 点击复制计划
        self.click("//span[contains(text(),'复制计划')]")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击取消直播计划弹窗
        self.click("//button[@class='kwaishop-marketing-live-plan-pc-btn']")
        self.assert_text("全部计划", "(//div[@class='title___z6fba'])[1]")
        sleep(2)
        # 点击编辑
        self.click("//button[@class='kwaishop-marketing-live-plan-pc-btn kwaishop-marketing-live-plan-pc-btn-primary actionItem___bU0Sq']")
        self.assert_text("选品备货", "//span[@class='text___nzBZ8 active___md0iL']")
        sleep(2)
        #点击添加商品
        self.click("//span[contains(text(),'添加商品')]")
        sleep(2)
        #点击自主选择
        self.click("(//span[contains(text(),'自主选择')])[1]")
        self.assert_text("添加商品", "//div[@class='kwaishop-marketing-live-plan-pc-drawer-title']")
        sleep(2)
        #点击分销推广
        self.click("//div[@class='kwaishop-marketing-live-plan-pc-drawer kwaishop-marketing-live-plan-pc-drawer-right kwaishop-marketing-live-plan-pc-drawer-open SelectGoodsModal___qEpZa']//div[@class='kwaishop-marketing-live-plan-pc-tabs-nav-wrap']//div[2]")
        self.assert_text("添加商品", "//div[@class='kwaishop-marketing-live-plan-pc-drawer-title']")
        sleep(2)
        #点击粉丝心愿商品
        self.click("(//div[@class='kwaishop-marketing-live-plan-pc-tabs-tab'])[2]")
        self.assert_text("添加商品", "//div[@class='kwaishop-marketing-live-plan-pc-drawer-title']")
        sleep(2)
        #点击取消
        self.click("//span[contains(text(),'取 消')]")
        self.assert_text("选品备货", '//*[@id="live-plan-pc_root"]/div[1]/div[3]/div/div[1]/div/div/div/div[1]/div/span[1]')
        sleep(2)
        # 点击编辑基础信息
        self.click("//span[contains(text(),'编辑基础信息')]")
        self.assert_text("直播名称", "//label[contains(text(),'直播名称')]")
        sleep(2)
        # 点击取消
        self.click("//div[@class='kwaishop-marketing-live-plan-pc-modal-footer']//button[1]")
        self.assert_text("选品备货",'//*[@id="live-plan-pc_root"]/div[1]/div[3]/div/div[1]/div/div/div/div[1]/div/span[1]')
        sleep(5)
        # 点击换一换
        self.click("(//span[@class='change___gUcGd'])[1]")
        self.assert_text("选品备货",'//*[@id="live-plan-pc_root"]/div[1]/div[3]/div/div[1]/div/div/div/div[1]/div/span[1]')
        sleep(5)
        # 点击流量准备
        self.click("(//span[contains(text(),'流量准备')])[1]")
        self.assert_text("流量准备",'//*[@id="live-plan-pc_root"]/div[1]/div[3]/div/div[1]/div/div/div/div[2]/div/span[1]')
        sleep(2)
        # 点击前往创建
        self.click("//span[contains(text(),'前往创建')]")
        self.assert_text("直播预告", "//div[@id='rc-tabs-1-tab-1']")
        sleep(2)
        self.switch_to_window(0)
        # 点击营销工具
        self.click('//*[@id="live-plan-pc_root"]/div[1]/div[3]/div/div[1]/div/div/div/div[3]')
        self.assert_text("开播时间生效工具", "//span[contains(text(),'开播时间生效工具')]")
        sleep(2)
        # 点击服务保障
        self.click('//*[@id="live-plan-pc_root"]/div[1]/div[3]/div/div[1]/div/div/div/div[4]')
        self.assert_text("自动询单服务", "//div[@class='item___UKZSC active___K7VBO']")
        sleep(2)

class TestMemberCommon(BaseTestCase):
    #@pytest.mark.skip
    def test_member_common_all_tab1(self):
        """UI自动化-直播预告详情"""
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.driver.maximize_window()
        sleep(1)
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/live/preview/previewDetail?id=***********")

        print("页面title:", self.get_page_title())
        sleep(3)
        # 校验基础信息
        self.click("//div[contains(text(),'1.基础信息')]")
        self.assert_text("基础信息", "(//div[contains(text(),'1.基础信息')])[1]")
        sleep(2)
        # 校验打榜商品和短视频设置
        self.click("(//div[@class='title___BS8Tq secondTitle___ZGkFY'])[1]")
        self.assert_text("打榜商品和短视频设置", "(//div[@class='title___BS8Tq secondTitle___ZGkFY'])[1]")
        sleep(2)
        # 校验其他
        self.click("//div[contains(text(),'3.其他')]")
        self.assert_text("其他", "(//div[contains(text(),'3.其他')])[1]")
        sleep(2)
        # 点击封禁规则
        self.click("//span[contains(text(),'封禁规则')]")
        self.assert_text("封禁规则", "(//span[contains(text(),'封禁规则')])[1]")
        sleep(2)
        # 点击关闭封禁规则
        self.click("//*[name()='path' and contains(@d,'M3.32 18.7')]")
        self.assert_text("基础信息", "(//div[contains(text(),'1.基础信息')])[1]")
        sleep(2)

class TestMemberCommon(BaseTestCase):
    @pytest.mark.skip
    def test_member_common_all_tab1(self):
        """UI自动化-直播预告"""
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.driver.maximize_window()
        sleep(1)
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/live/preview")

        print("页面title:", self.get_page_title())
        sleep(3)
        # 点击历史
        self.click("//div[@id='rc-tabs-0-tab-2']")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击月
        self.click("//div[@class='BoxButton___slYlH null']")
        self.assert_text("直播预告", "//div[@id='rc-tabs-1-tab-1']")
        sleep(2)
        # 点击创建预告
        self.click("//span[contains(text(),'新建预告')]")
        self.assert_text("1.基础信息", "//div[contains(text(),'1.基础信息')]")
        sleep(2)
        # 点击取消
        self.click("//div[@class='FooterModule___k6aGS']//button[1]")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击查看榜单
        self.click("//span[contains(text(),'查看榜单')]")
        self.assert_text("直播预告榜单", "//div[@class='kwaishop-seller-live-preview-pc-drawer-title']")
        sleep(2)
        # 点击消费者自主反馈商品榜单
        self.click("//div[@class='kwaishop-seller-live-preview-pc-drawer-body']//div[@class='kwaishop-seller-live-preview-pc-tabs-tab']")
        self.assert_text("直播预告榜单", "//div[@class='kwaishop-seller-live-preview-pc-drawer-title']")
        sleep(2)
        # 点击“x”
        self.click("//*[name()='path' and contains(@d,'M3.32 18.7')]")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击预告数据
        self.click("//span[contains(text(),'预告数据')]")
        self.assert_text("预告数据", "//div[@id='rcDialogTitle0']")
        sleep(2)
        # 点击知道了
        self.click("//span[contains(text(),'知道了')]")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击预告详情
        self.click("//span[contains(text(),'预告详情')]")
        self.assert_text("1.基础信息", "//div[contains(text(),'1.基础信息')]")
        sleep(2)
        # 点击返回直播预告
        self.click("//a[contains(text(),'直播预告')]")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击预告投流
        self.click("//span[contains(text(),'预告投流')]")
        self.assert_text("流量推广", "//div[@class='kwaishop-seller-live-preview-pc-drawer-title']")
        sleep(10)
        # 点击“x”
        self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击撤回
        self.click("//span[contains(text(),'撤回')]")
        self.assert_text("确定撤回预告？", "//span[@class='kwaishop-seller-live-preview-pc-modal-confirm-title']")
        sleep(2)
        # 点击不撤回
        self.click("//button[@class='kwaishop-seller-live-preview-pc-btn kwaishop-seller-live-preview-pc-btn-primary']")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击追加视频
        self.click("//span[contains(text(),'追加视频')]")
        self.assert_text("请选择关联视频", "//span[contains(text(),'请选择关联视频')]")
        sleep(2)
        # 点击取消
        self.click("//button[@class='kwaishop-seller-live-preview-pc-btn']")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)
        # 点击追加商品
        self.click("//span[contains(text(),'追加商品')]")
        self.assert_text("添加商品", "//div[@class='kwaishop-seller-live-preview-pc-drawer-title']")
        sleep(2)
        # 点击取消
        self.click("//button[@class='kwaishop-seller-live-preview-pc-btn']")
        self.assert_text("预告列表", "//div[@class='title___PwXsv']")
        sleep(2)

