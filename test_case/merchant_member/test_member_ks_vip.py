import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep

@pytest.mark.skip
@ddt
class TestMemberVIP(BaseTestCase):
    """UI自动化-会员运营"""
    def test_member_vip_tab_01(self):
        self.login("MERCHANT_DOMAIN", "lyh_UI")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/member/score")
        print("页面title:", self.get_page_title())
        sleep(5)
        for i in range(7):
            if self.is_exact_text_visible("知道了", "//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                sleep(2)
            else:
                break
        self.refresh_page()
        sleep(2)
        self.click("//span[text()='会员运营']")
        sleep(5)
        self.assert_text("自营分销", "//span[text()='自营分销']")
        self.assert_text("店铺自营", "//span[text()='店铺自营']")
        self.assert_text("核心数据", "//span[text()='核心数据']")
        self.assert_text("经营建议", "//span[text()='经营建议']")
        self.refresh_page()
        sleep(5)
        self.driver.execute_script("window.scrollBy(0, 1000)")
        element = self.driver.find_element(By.XPATH, "//div[@id='tabsNav-tab-0']")
        element.click()
        sleep(5)
        self.assert_text("会员成交", "//span[text()='会员成交']")
        self.click("//span[text()='自营分销']")
        sleep(5)
        self.assert_text("店铺自营", "//span[text()='店铺自营']")
        self.assert_text("自营分销", "//span[text()='自营分销']")

    @pytest.mark.p1
    # @pytest.mark.skip
    def test_member_vip_tab_02(self):
        """会员权益"""
        self.login("MERCHANT_DOMAIN", "lyh_UI")
        sleep(1)
        self.driver.maximize_window()
        for i in range(7):
            if self.is_exact_text_visible("知道了", "//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                sleep(2)
            else:
                break
        self.hover('//*[@id="menu_item_MH587YWTo5w"]')
        sleep(3)
        self.click('//*[@id="menu_item_uvCXc1nULik"]')   #会员权益
        sleep(5)
        self.click("(//*[name()='svg'][@class='kwaishop-seller-member-rights-modal-pc-modal-close-icon'])[1]")
        self.assert_text("入会券", "//span[text()='入会券']")
        self.click("//span[text()='去创建']")
        sleep(2)
        self.refresh_page()
        sleep(2)
        self.click("(//*[name()='svg'][@class='kwaishop-seller-member-rights-modal-pc-modal-close-icon'])[1]")
        sleep(2)
        self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")    # 关闭新增入会券抽屉
        self.click('//*[@id="rc-tabs-0-tab-exclusive"]')    #专享券
        sleep(2)
        self.refresh_page()
        sleep(2)
        self.click("(//*[name()='svg'][@class='kwaishop-seller-member-rights-modal-pc-modal-close-icon'])[1]")
        sleep(2)
        self.click("//span[text()='新增会员专享券']")
        sleep(5)
        self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")

        self.click('//*[@id="rc-tabs-0-tab-sampleGoods"]')    #会员派样
        self.driver.execute_script("window.scrollBy(0, 1000)")
        self.click("//span[text()='新建会员派样']")
        sleep(5)
        self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")

        self.click("//div[text()='会员群聊']")
        self.assert_text("会员群聊", "//div[text()='会员群聊']")
        sleep(10)

        self.click("//div[text()='会员商品']")   #会员商品
        self.click("//span[text()='新建商品活动']")
        self.click("#root > div > div > form > div:nth-child(3) > div.marketing-paas-formily-item-control > div > div > div > button")







