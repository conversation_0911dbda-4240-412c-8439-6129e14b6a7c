import random
import time
import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep


@ddt
class TestMemberLivezhushou(BaseTestCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.skip
    def test_member_live_zhushou_01(self):
        """UI自动化-跟播助手"""
        self.login("MERCHANT_DOMAIN", "lyh_UI")
        sleep(1)
        self.open("https://zs.kwaixiaodian.com/page/helper")
        print("页面title:", self.get_page_title())
        sleep(3)
        self.refresh_page()

        self.assert_text("直播前准备", "//span[text()='直播前准备']")
        self.click("//span[text()='回到旧版']")
        self.driver.maximize_window()    # 页面放大
        sleep(3)
        self.click("(//span[contains(text(),'跳过')])[1]")
        self.assert_text("直播前准备", "//span[text()='直播前准备']")
        self.click("(//span[contains(text(),'添加待上车商品')])[2]")
        sleep(3)
        self.assert_text("全部商品", "//div[text()='全部商品']")
        self.click("(//span[contains(text(),'取消')])[1]")  # 关闭弹窗
        sleep(3)
        self.driver.execute_script("window.scrollBy(0, 1000)")
        # self.click('//*[@id="root"]/div/div[2]/div[1]/div/button/span')
        self.click("//span[text()='立即推广']")
        sleep(3)
        self.assert_text("推广设置", "//div[text()='推广设置']")

    @pytest.mark.skip
    def test_member_live_zhushou_02(self):
        """新版直播计划-日历板块"""
        self.login("MERCHANT_DOMAIN", "member_account")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/live/plan")
        print("页面title:", self.get_page_title())
        sleep(3)
        self.click("//span[text()='上一周']")
        sleep(3)
        self.assert_text("当周计划数", "//div[text()='当周计划数']")  # 校验日历模块有展示
        self.click('//*[@id="live-plan-pc_root"]/div[1]/div[1]/div[2]/div[1]/div/div/div/div/div/table/tbody/tr/td/div/div/div/table/tbody/tr/td[1]/div/div[1]/div[2]/a/div/div/span[1]/span[2]/span')
        self.assert_text("活动介绍", "//span[text()='活动介绍']")
        sleep(3)
        self.click('//*[@id="root"]/div/div/div/div[1]/div[2]/div[1]/div/div[2]/div/div[3]/div[1]')  # 点击活动弹窗内，提报直播计划
        self.assert_text("批量提报", "//span[text()='批量提报']")
        sleep(3)
        self.click('//*[@id="root"]/div/div/div/div[1]/div[2]/div[1]/div/div[3]/div/div[3]/div[1]')  # 点击查看报名记录
        self.assert_text("提报记录", "//span[text()='提报记录']")
        sleep(3)
        self.click('//*[@id="root"]/div/div/div/div[2]/button[1]')  # 关闭活动报名弹窗

    @pytest.mark.skip
    def test_member_home_TiCi_tab(self):
        """小店首页-直播卖货智能提词tab"""
        self.login("MERCHANT_DOMAIN", "lyh_UI")
        self.open("https://s.kwaixiaodian.com/zone/home")
        self.click("(//button[contains(text(),'知道了')])[1]")
        self.click("(//button[contains(text(),'知道了')])[1]")
        sleep(6)
        # self.click('我知道了', '/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button[1]/span')
        if self.is_element_visible('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button[1]/span'):
            self.click_xpath('我知道了', '/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button[1]/span')
            time.sleep(5)
        elif self.is_element_visible("//button[text()='我知道了']"):
            self.click("//button[text()='我知道了']")

        sleep(3)
        self.click("(//span[@class='cyN6TjjLqTRpTIDUywEA'])[1]")    # 选中第一个计划
        sleep(3)
        self.click("(//div[@id='driverLivePrompter'])[1]")  # 点击直播提词设置tab
        self.assert_text("查看提词", "//div[text()='查看提词']")
        sleep(3)
        self.click('//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/div[2]/div/div/div[2]/div[1]/div[3]/div/div/div/div[1]/div[2]')  # 点击查看提词打开提词抽屉
        self.assert_text("智能话术", "//div[text()='智能话术']")
        sleep(3)
        self.click('//*[@id="dilu-micro-root"]/div/div/div[2]/button[1]')  # 取消关闭抽屉
        self.click("(//div[@class='vcdweKNXQbSQbCYzc8Pw false'])[1]")