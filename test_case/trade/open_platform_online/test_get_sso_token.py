import os
import time

import yaml
from selenium import webdriver
from selenium.webdriver.common.by import By

from .base import BaseTestCase


class TestGetSsoToken():
    def test_get_wjx_ksop_sso_token_online(self):
        # time.sleep(30)
        times = 3
        for i in range(times):
            try:
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument('--no-sandbox')  # 取消沙盒模式,浏览器的安全性会降低，如果不取消,linux下运行会报错
                chrome_options.add_argument('--disable-dev-shm-usage')  # 解决资源有限的问题
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--incognito')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(
                    "https://sso.corp.kuaishou.com/cas/login?service=https://apsso.staging.kuaishou.com/callback/sso?request_id=1c126aab-f612-4037-974b-fc19c6526df6")

                driver.maximize_window()
                driver.find_element('id', 'username_sso').send_keys("weijingxiao")
                driver.find_element('id', 'password_sso').send_keys("WJXwjx123456")
                driver.find_element('xpath', '//*[@id="fm1"]/section[8]/input[3]').submit()

                time.sleep(1)

                driver.get("https://ksop.corp.kuaishou.com/rest/open/management/user/info")
                cookie = driver.get_cookie('accessproxy_session')
                wjx_cookie = f"{cookie['name']}={cookie['value']}"

                curPath = os.path.abspath(os.path.dirname(__file__))
                txt_path = curPath + '/cookie_token.ymal'

                with open(txt_path, 'r', encoding="utf-8") as f:
                    token = yaml.load(f.read(), Loader=yaml.FullLoader)
                    token['AUTO_TEST_WYH_COOKIE'] = wjx_cookie

                with open(txt_path, 'w', encoding="utf-8") as f:
                    yaml.dump(token, f, allow_unicode=True)
            except:
                if i < times - 1:
                    driver.quit()
                    time.sleep(3)
                    continue
                else:
                    raise
            break

    def test_get_wjx_apicenter_sso_token_online(self):
        # time.sleep(30)
        times = 3
        for i in range(times):
            try:
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument('--no-sandbox')  # 取消沙盒模式,浏览器的安全性会降低，如果不取消,linux下运行会报错
                chrome_options.add_argument('--disable-dev-shm-usage')  # 解决资源有限的问题
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--incognito')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(
                    "https://sso.corp.kuaishou.com/cas/login?service=https://apsso.staging.kuaishou.com/callback/sso?request_id=1c126aab-f612-4037-974b-fc19c6526df6")

                driver.maximize_window()
                driver.find_element('id', 'username_sso').send_keys("weijingxiao")
                driver.find_element('id', 'password_sso').send_keys("WJXwjx123456")
                driver.find_element('xpath', '//*[@id="fm1"]/section[8]/input[3]').submit()

                time.sleep(1)

                driver.get("https://apicenter.corp.kuaishou.com/rest/user/info")
                cookie = driver.get_cookie('accessproxy_session')
                wjx_cookie = f"{cookie['name']}={cookie['value']}"

                curPath = os.path.abspath(os.path.dirname(__file__))
                txt_path = curPath + '/cookie_token.ymal'

                with open(txt_path, 'r', encoding="utf-8") as f:
                    token = yaml.load(f.read(), Loader=yaml.FullLoader)
                    token['AUTO_TEST_WYH_COOKIE_API'] = wjx_cookie

                with open(txt_path, 'w', encoding="utf-8") as f:
                    yaml.dump(token, f, allow_unicode=True)
            except:
                if i < times - 1:
                    driver.quit()
                    time.sleep(3)
                    continue
                else:
                    raise
            break

    def test_get_yhy_ksop_sso_token_online(self):
        # time.sleep(30)
        times = 3
        for i in range(times):
            try:
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument('--no-sandbox')  # 取消沙盒模式,浏览器的安全性会降低，如果不取消,linux下运行会报错
                chrome_options.add_argument('--disable-dev-shm-usage')  # 解决资源有限的问题
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--incognito')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(
                    "https://sso.corp.kuaishou.com/cas/login?service=https://apsso.staging.kuaishou.com/callback/sso?request_id=1c126aab-f612-4037-974b-fc19c6526df6")

                driver.maximize_window()
                driver.find_element('id', 'username_sso').send_keys("yanghaiyang03")
                driver.find_element('id', 'password_sso').send_keys("yhy.950821")
                driver.find_element('xpath', '//*[@id="fm1"]/section[8]/input[3]').submit()

                time.sleep(1)

                driver.get("https://ksop.corp.kuaishou.com/rest/open/management/user/info")
                cookie = driver.get_cookie('accessproxy_session')

                yhy_cookie = f"{cookie['name']}={cookie['value']}"

                curPath = os.path.abspath(os.path.dirname(__file__))
                txt_path = curPath + '/cookie_token.ymal'

                with open(txt_path, 'r', encoding="utf-8") as f:
                    token = yaml.load(f.read(), Loader=yaml.FullLoader)
                    token['AUTO_TEST_YHY_COOKIE'] = yhy_cookie

                with open(txt_path, 'w', encoding="utf-8") as f:
                    yaml.dump(token, f, allow_unicode=True)

                driver.get(
                    "https://git.corp.kuaishou.com/x7-qa/autotestpytest/-/blob/yanghaiyang03/test-case/trade/third/open_platform/file_folder/cookie_token.ymal")
                time.sleep(3)

                driver.find_element(By.CSS_SELECTOR,
                                    "div.gl-sm-display-flex.file-actions > div:nth-child(2) > div > button:nth-child(1)").click()
                time.sleep(3)

                file = open(txt_path, 'r')
                print("txt test：", file.read())
                file.close()
                driver.find_element(By.CSS_SELECTOR,
                                    r"div.gl-w-full.gl-relative.gl-h-200\!.gl-mb-4 > input").send_keys(
                    txt_path)
                time.sleep(5)

                driver.find_element(By.CSS_SELECTOR,
                                    "#modal-replace-blob___BV_modal_footer_>button:nth-child(2)").click()
                time.sleep(5)

                # driver.find_element(By.CSS_SELECTOR,"div.gl-alert-actions > a").click()
                # time.sleep(3)

                driver.get("https://git.corp.kuaishou.com/x7-qa/autotestpytest/-/merge_requests/new")
                time.sleep(3)
                # driver.find_element(By.CSS_SELECTOR,
                #                     "#content-body > div.top-area > div > a:nth-child(4)").click()
                # time.sleep(3)
                #
                driver.find_element(By.CSS_SELECTOR,
                                    "div:nth-child(1) > div > div.card-body.clearfix > div:nth-child(2) > button").click()
                time.sleep(2)

                driver.find_element(By.CSS_SELECTOR,
                                    "div.merge-request-select.dropdown.show > div > div.dropdown-input > input").send_keys(
                    "yanghaiyang03")
                time.sleep(5)
                #
                # driver.find_element(By.CSS_SELECTOR,
                #                     "div.merge-request-select.dropdown.show > div > div.dropdown-content").click()
                # time.sleep(5)
                #
                # driver.find_element(By.CSS_SELECTOR, "#new_merge_request > input").click()
                # time.sleep(2)

                driver.find_element(By.CSS_SELECTOR,
                                    "div.merge-request-select.dropdown.show > div > div.dropdown-content > ul > li").click()
                time.sleep(3)

                driver.find_element(By.CSS_SELECTOR, "#new_merge_request > input").click()
                time.sleep(5)

                driver.find_element(By.CSS_SELECTOR, "div.row-content-block.middle-block > input").click()
                time.sleep(2)

                driver.find_element(By.CSS_SELECTOR, "div.gl-align-self-start.btn-group > button").click()
                time.sleep(2)
            except:
                if i < times - 1:
                    driver.quit()
                    time.sleep(3)
                    continue
                else:
                    raise
            break

    def test_get_yhy_apicenter_sso_token_online(self):
        # time.sleep(30)
        times = 3
        for i in range(times):
            try:
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument('--no-sandbox')  # 取消沙盒模式,浏览器的安全性会降低，如果不取消,linux下运行会报错
                chrome_options.add_argument('--disable-dev-shm-usage')  # 解决资源有限的问题
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--incognito')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(
                    'https://sso.corp.kuaishou.com/cas/login?service=https://apsso.staging.kuaishou.com/callback/sso?request_id=1c126aab-f612-4037-974b-fc19c6526df6')

                driver.maximize_window()
                driver.find_element('id', 'username_sso').send_keys("yanghaiyang03")
                driver.find_element('id', 'password_sso').send_keys("yhy.950821")
                driver.find_element('xpath', '//*[@id="fm1"]/section[8]/input[3]').submit()

                time.sleep(1)

                driver.get("https://apicenter.corp.kuaishou.com/rest/user/info")
                cookie = driver.get_cookie('accessproxy_session')
                yhy_cookie = f"{cookie['name']}={cookie['value']}"

                curPath = os.path.abspath(os.path.dirname(__file__))
                txt_path = curPath + '/cookie_token.ymal'

                with open(txt_path, 'r', encoding="utf-8") as f:
                    token = yaml.load(f.read(), Loader=yaml.FullLoader)
                    token['AUTO_TEST_YHY_COOKIE_API'] = yhy_cookie

                with open(txt_path, 'w', encoding="utf-8") as f:
                    yaml.dump(token, f, allow_unicode=True)
            except:
                if i < times - 1:
                    driver.quit()
                    time.sleep(3)
                    continue
                else:
                    raise
            break

    def test_get_wjx_apicenter_sso_token_prt(self):
        # time.sleep(30)
        times = 3
        for i in range(times):
            try:
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument('--no-sandbox')  # 取消沙盒模式,浏览器的安全性会降低，如果不取消,linux下运行会报错
                chrome_options.add_argument('--disable-dev-shm-usage')  # 解决资源有限的问题
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--incognito')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(
                    'https://apsso.test.gifshow.com/cas/login??service=https://apicenter-prt.test.gifshow.com/rest/user/info')

                driver.maximize_window()
                time.sleep(3)
                driver.find_element('id', 'username_sso').send_keys("weijingxiao")
                driver.find_element('id', 'password_sso').send_keys("WJXwjx123456")
                driver.find_element('xpath', '//*[@id="fm1"]/section[8]/input[3]').submit()

                time.sleep(3)

                driver.get("https://apicenter-prt.test.gifshow.com/rest/user/info")
                cookie = driver.get_cookie('accessproxy_session')
                wjx_cookie = f"{cookie['name']}={cookie['value']}"

                curPath = os.path.abspath(os.path.dirname(__file__))
                txt_path = curPath + '/cookie_token.ymal'

                with open(txt_path, 'r', encoding="utf-8") as f:
                    token = yaml.load(f.read(), Loader=yaml.FullLoader)
                    token['AUTO_TEST_WYH_COOKIE_API_PRT'] = wjx_cookie

                with open(txt_path, 'w', encoding="utf-8") as f:
                    yaml.dump(token, f, allow_unicode=True)
            except:
                if i < times - 1:
                    driver.quit()
                    time.sleep(3)
                    continue
                else:
                    raise
            break

    def test_get_yhy_apicenter_sso_token_prt(self):
        # time.sleep(30)
        times = 3
        for i in range(times):
            try:
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument('--no-sandbox')  # 取消沙盒模式,浏览器的安全性会降低，如果不取消,linux下运行会报错
                chrome_options.add_argument('--disable-dev-shm-usage')  # 解决资源有限的问题
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--incognito')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(
                    'https://apsso.test.gifshow.com/cas/login??service=https://apicenter-prt.test.gifshow.com/rest/user/info')

                driver.maximize_window()
                time.sleep(3)
                driver.find_element('id', 'username_sso').send_keys("yanghaiyang03")
                driver.find_element('id', 'password_sso').send_keys("yhy.950821")
                driver.find_element('xpath', '//*[@id="fm1"]/section[8]/input[3]').submit()

                time.sleep(3)

                driver.get("https://apicenter-prt.test.gifshow.com/rest/user/info")
                cookie = driver.get_cookie('accessproxy_session')
                yhy_cookie = f"{cookie['name']}={cookie['value']}"

                curPath = os.path.abspath(os.path.dirname(__file__))
                txt_path = curPath + '/cookie_token.ymal'

                with open(txt_path, 'r', encoding="utf-8") as f:
                    token = yaml.load(f.read(), Loader=yaml.FullLoader)
                    token['AUTO_TEST_YHY_COOKIE_API_PRT'] = yhy_cookie

                with open(txt_path, 'w', encoding="utf-8") as f:
                    yaml.dump(token, f, allow_unicode=True)
            except:
                if i < times - 1:
                    driver.quit()
                    time.sleep(3)
                    continue
                else:
                    raise
            break
