import logging
import time
from time import sleep

from selenium.common import *
from selenium.webdriver.chrome import webdriver
from selenium.webdriver.common.by import By
# from common.Goods.goodsBase import BaseTestCase as BaseCase
from seleniumbase import BaseCase
# from seleniumbase import get_driver
from seleniumbase.fixtures import page_actions
from seleniumwire import webdriver

from constant.account import get_account_info
from constant.domain import get_domain_by_env
from page_objects.refund.refund import RefundPage
from page_objects.trade_order.trade_order_page import TradeOrderPage
from selenium.webdriver import ActionChains, Keys

# from utils.kwaixiaodianUtils import KwaiXiaoDianToolTest

# from seleniumwire import webdriver

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 创建控制台处理器并设置级别
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
# 将处理器添加到日志记录器
logger.addHandler(console_handler)


class BaseTestCase(BaseCase):

    def setUp(self):
        super(BaseTestCase, self).setUp()

    def mainLogin(self, domain, account):
        """
        前置登录
        """
        account_data = get_account_info(account)
        # 若运行环境为prt时，指定泳道:PRT.test
        if self.var1 and self.var1 == 'prt':
            # self.driver.quit()
            # self.driver = webdriver.Chrome()
            self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
            host = get_domain_by_env(domain, self.var1)
            logger.info("current env prt with PRT.test")
        else:
            # 运行线上环境
            host = get_domain_by_env(domain, 'online')
            logger.info("current env:online")
        self.open(host)
        time.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        self.click('//*[@id="username"]')
        self.type('//*[@id="username"]', account_data['account'])
        self.click('//*[@id="password"]')
        self.type('//*[@id="password"]', account_data['password'])
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')

    def kwaixiaodian_login(self, account):
        """ 快手小店登录 """
        self.maximize_window()
        # self.var1 = 'prt'

        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        elif self.var2 and self.var2 == 'prt':
            env = 'prt'
        elif self.var3 and self.var3 == 'prt':
            env = 'prt'
        else:
            env = 'online'

        domain = 'KWAIXIAODIAN'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        # self.sleep(2)
        time = 0
        while (self.is_element_visible("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]") is False and self.is_element_visible("//span[contains(text(),'密码登录')]") is False) and time < 10:
            logger.info("登录页面未加载完成")
            time += 1
            sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            # self.sleep(2)
            time = 0
            while not self.is_element_visible("//img[@alt='logo']") and time < 10:
                logger.info("登录后首页未加载完成")
                time += 1
                sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            # self.sleep(2)
            while not self.is_element_visible("//img[@alt='logo']") and time < 10:
                logger.info("登录后首页未加载完成")
                time += 1
                sleep(2)

    def login(self, domain, account):
        """
        快手小店登陆，单用户
        """
        # self.mainLogin(domain, account)
        self.kwaixiaodian_login(account)

        # 通用弹窗关闭，关闭5次（蓝色气泡弹窗）
        for _ in range(5):
            if self.is_element_visible("//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                time.sleep(2)

        time.sleep(3)
        # 即将发货超时订单提醒
        if self.is_element_visible('/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/button[1]') and self.is_element_visible(
                "//span[text()='我已知晓']"):
            self.click('/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/button[1]')
            time.sleep(2)

        # 即将发货超时订单提醒
        self.close_over_time_ship_order_remind()

        time.sleep(2)
        # 大牌大补弹窗关闭,前几秒不能关，多等一会
        if self.is_element_visible("//span[text()='忍痛放弃']"):
            self.click("//span[text()='忍痛放弃']")
            time.sleep(2)

        # 关闭优先发货设置功能弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            time.sleep(2)

        time.sleep(1)
        # 关闭消息中心弹窗
        if self.is_element_visible("button[text()='立即处理']"):
            self.click("button[text()='立即处理']")
            time.sleep(2)

        # self.close_merchant_assistant()

        # 关闭生意通升级助大促爆发弹窗
        if self.is_element_visible("//div[@class='kpro-notice-modal-pictext-title']"):
            time.sleep(5)
            self.click("//span[contains(text(),'我知道了')]")
            time.sleep(2)

        # 切换至旧版导航栏
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
            self.sleep(2)

    def loginForTimeTest(self, domain, account):
        """
        快手小店登陆，单用户
        """
        self.mainLogin(domain, account)
        self.maximize_window()

        # 通用弹窗关闭，关闭5次（蓝色气泡弹窗）
        for _ in range(5):
            if self.is_element_visible("//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                # time.sleep(2)

        time.sleep(1)
        # 即将发货超时订单提醒
        if self.is_element_visible('/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/button[1]') and self.is_element_visible(
                "//span[text()='我已知晓']"):
            self.click('/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/button[1]')
            # time.sleep(2)

        # 即将发货超时订单提醒
        self.close_over_time_ship_order_remind()

        time.sleep(2)
        # 大牌大补弹窗关闭,前几秒不能关，多等一会
        if self.is_element_visible("//span[text()='忍痛放弃']"):
            self.click("//span[text()='忍痛放弃']")
            time.sleep(2)

        # 关闭优先发货设置功能弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            # time.sleep(2)

        time.sleep(1)
        # 关闭消息中心弹窗
        if self.is_element_visible("button[text()='立即处理']"):
            self.click("button[text()='立即处理']")
            # time.sleep(2)

        # self.close_merchant_assistant()

    def login_for_PRT_test_land(self, domain, account):
        """

        快手小店登陆，单用户
        """
        self.mainLogin_for_PRT_test_land(domain, account)
        self.maximize_window()
        # 通用弹窗关闭，关闭5次（蓝色气泡弹窗）
        for _ in range(5):
            if self.is_element_visible("//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                time.sleep(2)
        time.sleep(5)
        # 即将发货超时订单提醒
        if self.is_element_visible(
                '/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/button[1]') and self.is_element_visible(
            "//span[text()='我已知晓']"):
            self.click('/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/button[1]')
            time.sleep(2)

        # 即将发货超时订单提醒
        self.close_over_time_ship_order_remind()
        time.sleep(2)
        # 大牌大补弹窗关闭,前几秒不能关，多等一会
        if self.is_element_visible("//span[text()='忍痛放弃']"):
            self.click("//span[text()='忍痛放弃']")
            time.sleep(2)
        # 关闭优先发货设置功能弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            time.sleep(2)
        time.sleep(2)
        # 关闭消息中心弹窗
        if self.is_element_visible("button[text()='立即处理']"):
            self.click("button[text()='立即处理']")
            time.sleep(2)

        # self.close_merchant_assistant()

    def mainLogin_for_PRT_test_land(self, domain, account):
        """
        临时方案
        前置登录
        """

        self.driver.quit()
        self.driver = webdriver.Chrome()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}  # 可修改为任意泳道
        self.maximize_window()

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, 'prt')
        self.open(host)
        time.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        self.click('//*[@id="username"]')
        self.type('//*[@id="username"]', account_data['account'])
        self.click('//*[@id="password"]')
        self.type('//*[@id="password"]', account_data['password'])
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')

    def login_prt(self, account):
        account_data = get_account_info(account)
        from seleniumwire import webdriver as webdriver2
        # self.driver.quit()    # TODO：把quit去了试试，感觉这个是导致现在执行prt的时候是小屏幕的原因
        self.driver = webdriver2.Chrome()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.maximize_window()
        self.open("https://eshop-s.prt.kwaixiaodian.com")
        time.sleep(8)
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        self.click('//*[@id="username"]')
        self.type('//*[@id="username"]', account_data['account'])
        self.click('//*[@id="password"]')
        self.type('//*[@id="password"]', account_data['password'])
        self.click(
            '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
        self.maximize_window()

    def to_order_list(self, account="order_account"):
        # prt_case_set = {"TestOrderGoodsInfo", "TestOrderTopInfo"}
        prt_case_set = {}
        if self.__class__.__name__ in prt_case_set:
            self.login_prt(account)
            logger.debug("Login PRT Environment ... ")
        else:
            self.login("MERCHANT_DOMAIN", account)

            logger.debug("Login Online Environment ... ")
        # self.login_prt(account")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    def to_order_list_checking_order(self):
        """订单查询页"""
        self.login("MERCHANT_DOMAIN", "order_account")

        self.assert_title("快手小店")
        # self.sleep(2)
        self.close_blue_window()
        self.open_order_list_checking_order()
        self.close_confirm()

    def to_order_list_checking_order2(self, account="order_account"):
        """订单查询页"""
        self.login("MERCHANT_DOMAIN", account)

        self.assert_title("快手小店")
        # self.sleep(2)
        self.close_blue_window()
        self.open_order_list_checking_order()
        self.close_confirm()

    def to_combined_shipping(self):
        """合并发货页"""
        self.login("MERCHANT_DOMAIN", "order_account")
        # self.wait_for_element("//h1[@class='fklZitkCGZVm8IaYNX2h']")
        self.assert_title("快手小店")
        # self.sleep(2)
        self.close_confirm()
        self.open_order_list_checking_order()
        self.close_confirm()
        # self.sleep(2)
        # self.wait_for_element("#mergeship")
        time = 0
        while self.is_element_visible("#mergeship") == False and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
        # assert self.is_element_clickable("#mergeship"), '合并发货按钮不存在'
        self.click("#mergeship")
        time = 0
        while self.is_element_visible("//span[@class='kwaishop-trade-shipping-center-pc-badge badge___CYaYT'][contains(text(),'合并发货')]") == False and time < 10:
            logger.info("合并发货页未加载完成")
            time += 1
            sleep(2)
        self.close_confirm()

    def to_negotiate_delivery(self):
        """协商发货页"""
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        # self.sleep(3)
        self.close_confirm()
        self.close_northwest_ship_server_tips()
        self.close_merchant_assistant()

        time = 0
        while self.is_element_visible("//div[@id='menu_item_H7aQVESsruU']//img[@class='v7hCM6qCLFJ4dV1Cxols']") == False and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(1)

        elements = self.driver.find_elements(By.CLASS_NAME, 'bASqM1kzJlIX6ZNynVIc')
        texts = [element.text for element in elements]
        assert '保障' in texts, "保障按钮不存在"
        elements[texts.index('保障')].click()
        self.sleep(2)
        assert self.is_element_clickable('#menu_item_FQ-V_JH-NsQ'), '报备中心按钮不可点击'
        self.click('#menu_item_FQ-V_JH-NsQ')
        self.sleep(2)
        time = 0
        while self.is_element_visible("#rc-tabs-0-tab-negotiation") == False and time<10:
            logger.info("页面未加载完成")
            time += 1
            sleep(1)
        assert self.is_element_clickable('#rc-tabs-0-tab-negotiation'), '协商发货时间按钮不可点击'
        self.click('#rc-tabs-0-tab-negotiation')
        self.sleep(2)

    def to_delay_delivery(self):
        """延迟发货报备页"""
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        # self.sleep(3)
        self.close_confirm()

        # 保障
        time = 0
        while self.is_element_visible("//div[@id='menu_item_H7aQVESsruU']//span[@class='seller-main-badge']//span[1]") == False and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
        self.click("//div[@id='menu_item_H7aQVESsruU']//span[@class='seller-main-badge']//span[1]")
        time = 0
        while (self.is_element_visible("//div[@id='rc-tabs-0-tab-delayReport']") == False and self.is_element_visible("//span[@id='menu_item_oyPhPEhgN6s']") == False) and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
            if time == 5:
                self.refresh()

        # '报备中心'
        self.click("//span[@id='menu_item_oyPhPEhgN6s']")
        self.sleep(2)

        self.refresh()

        time = 0
        while not self.is_element_visible("//div[@id='rc-tabs-0-tab-delayReport']") and time<10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
        assert self.is_element_clickable("//div[@id='rc-tabs-0-tab-delayReport']"), '延迟发货报备按钮不可点击'
        self.click('#rc-tabs-0-panel-delayReport')

    def to_unable_delivery(self):
        """无法发货报备页"""
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        # self.sleep(3)
        self.close_confirm()

        time = 0
        while self.is_element_visible(
                "//div[@id='menu_item_H7aQVESsruU']//img[@class='v7hCM6qCLFJ4dV1Cxols']") == False and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(1)

        elements = self.driver.find_elements(By.CLASS_NAME, 'bASqM1kzJlIX6ZNynVIc')
        texts = [element.text for element in elements]
        assert '保障' in texts, "保障按钮不存在"
        elements[texts.index('保障')].click()
        self.sleep(2)
        assert self.is_element_clickable('#menu_item_FQ-V_JH-NsQ'), '报备中心按钮不可点击'
        self.click('#menu_item_FQ-V_JH-NsQ')
        self.sleep(2)
        time=0
        while not self.is_element_visible("//div[@id='rc-tabs-0-tab-unableDeliveryNegotiation']") and time<10:
            logger.info("页面未加载完成")
            time += 1
            sleep(1)
        assert self.is_element_clickable("//div[@id='rc-tabs-0-tab-unableDeliveryNegotiation']"), '无法发货赔付按钮不可点击'
        self.click("//div[@id='rc-tabs-0-tab-unableDeliveryNegotiation']")

    def to_report_center(self):
        """新版报备中心页"""
        self.to_order_list_checking_order()

        # 报备中心按钮
        if self.is_element_visible("//span[@id='menu_item_62V1uky-Ahc']"):
            self.click("//span[@id='menu_item_62V1uky-Ahc']")
        else:
            self.click("//span[@id='pinned_menu_for_intersectionObserver_62V1uky-Ahc_under_R553YgpLnhQ']")
        assert "zone/fulfill/delayship-report/list" in self.get_current_url(), "跳转错误"


    def multiLogin(self, domain, account, choice):
        """
        多用户登录
        chi
        """
        self.mainLogin(domain, account)
        time.sleep(2)
        if choice == 1:
            self.click('//*[@id="root"]/div/div[2]/div/div/div/div[4]/div[1]/div[1]')
        else:
            self.click('//*[@id="root"]/div/div[2]/div/div/div/div[4]/div[1]/div[2]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[4]/div[2]/button')
        time.sleep(2)

        self.close_merchant_assistant()

    def close_confirm(self):
        # 双十一购物节弹窗
        if self.is_element_visible('//*[@id="main-pc-atmosphere-layout"]/div[4]/div[2]/div/div[2]/div[2]/span'):
            self.click('//*[@id="main-pc-atmosphere-layout"]/div[4]/div[2]/div/div[2]/div[2]/span', by='xpath')
            self.sleep(2)
            logger.debug("关闭 双十一购物节 弹窗")

        # 先关闭订单筛选功能弹框
        for i in range(2):
            if self.is_element_visible(TradeOrderPage.order_select_confirm):
                self.click(TradeOrderPage.order_select_confirm)
                self.sleep(2)

        # check 登陆小店进来的2个弹窗，如果存在，则点掉
        if self.is_element_visible(TradeOrderPage.open_kwaixiaodian_confirm):
            self.click(TradeOrderPage.open_kwaixiaodian_confirm)
            self.sleep(2)

        # 如果再次出现弹窗，则继续点掉
        if self.is_element_visible(TradeOrderPage.open_kwaixiaodian_confirm):
            self.click(TradeOrderPage.open_kwaixiaodian_confirm)
            self.sleep(2)

        # 优先发货设置弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button') or self.is_element_visible(
                "//button[text()='我知道了']"):
            if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
                self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
                self.sleep(2)
                logger.debug("关闭优先设置发货弹窗 xpath")
            else:
                self.click("//button[text()='我知道了']")
                self.sleep(2)
                logger.debug("关闭优先设置发货弹窗 text")

        # 官方在线寄件弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button') or self.is_element_visible("//button[text()='关闭']"):
            if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
                self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
                self.sleep(2)
                logger.debug("关闭官方在线寄件弹窗 xpath")
            else:
                self.click("//button[text()='关闭']")
                self.sleep(2)
                logger.debug("关闭官方在线寄件弹窗 text")

        # 如果有客服消息弹窗，也关闭
        if self.is_element_visible(
                'body > div:nth-child(5) > div > div.ant-modal-wrap > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button:nth-child(1)'):
            self.click(
                'body > div:nth-child(5) > div > div.ant-modal-wrap > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button:nth-child(1)')
            self.sleep(2)
            logger.debug("关闭客服消息弹窗")

        # 如果有发货提醒弹窗，也关闭
        if self.is_element_visible("//span[text()='暂不处理']"):
            self.click("//span[text()='暂不处理']")
            self.sleep(2)
            logger.debug("关闭发货提醒弹窗")

        # 商家售后工具弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭商家售后工具弹窗")

        # 开通「快递拦截」弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭开通「快递拦截」弹窗")

        # 优先发货设置弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(2)
            logger.debug("关闭 优先设置发货 弹窗")

        # 竞价工具提示弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(2)
            logger.debug("关闭 竞价工具提示 弹窗")

        # 直播间讲解回放管理菜单新功能上线 弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(2)
            logger.debug("关闭 直播间讲解回放管理菜单新功能上线 弹窗")

        # 直播计划上新送流量 弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click_xpath('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(2)
            logger.debug("关闭 直播计划上新送流量 弹窗")

        # 降价活动 弹窗
        if self.is_element_visible('/html/body/div[9]/div/div[2]/div/div[2]/div/div/button'):
            self.click_xpath('/html/body/div[9]/div/div[2]/div/div[2]/div/div/button')
            self.sleep(2)
            logger.debug("关闭 降价活动 弹窗")

        # 新品免费起量权益弹窗
        if self.is_element_visible('/html/body/div[6]/div/div[2]/div/div[2]/button/span/span'):
            self.click_xpath('/html/body/div[6]/div/div[2]/div/div[2]/button/span/span')
            self.sleep(2)
            logger.debug("关闭 新品免费起量权益 弹窗")

        # 一键降价 弹窗
        if self.is_element_visible('/html/body/div[10]/div/div[2]/div/div[2]/div/div/button'):
            self.click_xpath('/html/body/div[10]/div/div[2]/div/div[2]/div/div/button')
            self.sleep(2)
            logger.debug("关闭 一键降价 弹窗")
        # self.click_if_exist('//*[@id="driver-popover-item"]/div[4]/button')
        # self.click_if_exist('//*[@id="driver-popover-item"]/div[4]/button')
        # self.click_if_exist('//*[@id="driver-popover-item"]/div[4]/button')

        # PRT 一键降价弹窗
        if self.is_element_visible('/html/body/div[6]/div/div[2]/div/div[2]/div/div/button'):
            self.click_xpath('/html/body/div[6]/div/div[2]/div/div[2]/div/div/button')
            self.sleep(2)
            logger.debug("关闭 PRT 一键降价 弹窗")

        # 降价弹窗
        if self.is_element_visible('.anticon.anticon-system-close-large-line'):
            self.click('.anticon.anticon-system-close-large-line')
            self.sleep(2)
            logger.debug("关闭 降价 弹窗")

        # 一站式无法发货弹窗
        if self.is_element_visible("//button[text()='关闭']"):
                self.click("//button[text()='关闭']")
                self.sleep(2)
                logger.debug("关闭一站式无法发货弹窗")

        # 新版订详满意度弹窗
        if self.is_element_visible("//span[@class='rate-dialog-close']//span[@aria-label='system-close-medium-line']//*[name()='svg']"):
            self.click_xpath("//span[@class='rate-dialog-close']//span[@aria-label='system-close-medium-line']//*[name()='svg']")
            self.sleep(2)
            logger.debug("关闭 新版订详满意度 弹窗")

        # 618生意通升级助大促爆发弹窗
        if self.is_element_visible("//span[contains(text(),'立即使用')]"):
            self.sleep(5)
            self.click_xpath("//span[contains(text(),'我知道了')]")
            self.sleep(2)
            logger.debug("关闭 生意通 弹窗")

        # 待发货紧急消息通知
        if self.is_element_visible("//span[contains(text(),'立即处理')]"):
            self.sleep(5)
            self.click("//span[contains(text(),'立即处理')]")
            self.switch_to_window(-1)

            self.driver.close()
            self.switch_to_window(-1)
            logger.debug("待发货紧急消息通知")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")
            logger.debug("关闭 您有多大程度满意我们的产品 弹窗")




    def click_if_exist(self, selector, by="css selector", timeout=3, delay=0, scroll=True):
        try:
            page_actions.wait_for_element_visible(
                self.driver,
                selector,
                by,
                timeout=timeout)
            self.click(selector, by, timeout, delay, scroll)
        except WebDriverException:
            print("not exist element, ingore")

    def open_order_list(self):
        if self.is_element_visible(TradeOrderPage.urgent_message_notice):
            self.sleep(5)
            self.click(TradeOrderPage.urgent_message_close_button)
            self.switch_to_tab(0)
        # 点击左侧边栏订单tab
        if self.is_element_visible(TradeOrderPage.left_order_enter):
            self.click(TradeOrderPage.left_order_enter)
        else:
            self.click(TradeOrderPage.left_order_enter_new)

        if self.is_element_visible(TradeOrderPage.test_window):
            self.click(TradeOrderPage.test_window_confirm)
        self.sleep(5)
        self.close_confirm()
        self.close_northwest_ship_server_tips()
        self.close_merchant_assistant()

    def open_order_list_checking_order(self):
        """打开订单页面-订单查询"""
        # 点击左侧边栏订单tab
        if self.is_element_visible(TradeOrderPage.left_order_enter):
            self.click(TradeOrderPage.left_order_enter)
        else:
            self.click("//span[@id='pinned_menu_for_intersectionObserver_zAoD7EEcix0_under_R553YgpLnhQ']")
        # attempts = 0
        # while attempts < 5:
        #     try:
        #         self.click(TradeOrderPage.left_order_enter)
        #         break
        #     except Exception as e:
        #         attempts += 1
        #         logger.debug(e)
        #         # 获取页面加载状态状态
        #         logger.debug("订单加载状态 = {}".format(self.driver.execute_script('return document.readyState')))
        #         logger.debug("左侧【首页】按钮是否展示 = {}".format(self.is_element_present('#menu_item_W5bfiXQszVg')))
        #         logger.debug("左侧【订单】按钮是否展示 = {}".format(self.is_element_present(TradeOrderPage.left_order_enter)))
        #         logger.debug(("快手小店左上角logo是否展示 = {}".format(
        #             self.is_element_present('#seller-main-pc-header > div > div > div.R2pelbq1XUGwZ1BfAx5_ > img'))))
        #         if attempts == 3:
        #             raise Exception("重试3次后仍然失败") from e
        #     if attempts < 3:
        #         time.sleep(1)
        #     else:
        #         logger.debug("click(TradeOrderPage.left_order_enter) 重试次数达到上限，程序终止")
        # self.sleep(2)
        # self.click(TradeOrderPage.order_list_enter)
        self.close_confirm()
        # 查询按钮存在？
        self.wait_for_element("//button[@type='submit']")
        # self.sleep(1)
        self.close_northwest_ship_server_tips()
        self.close_merchant_assistant()

        # self.click(TradeOrderPage.order_list_enter)
        #
        # self.sleep(3)
        # self.close_confirm()
        # self.close_northwest_ship_server_tips()
        # self.close_merchant_assistant()

    def close_blue_window(self):
        """
        关闭蓝色弹窗
        """
        for _ in range(5):
            if self.is_element_visible("//button[text()='知道了']"):
                self.click("//button[text()='知道了']")
                time.sleep(2)

    def search_order_by_oid(self, search_oid):
        """
        通过ID搜索订单
        """
        time.sleep(2)
        if self.is_element_visible('//span[contains(text(),"立即发货")]'):
            self.click('//span[contains(text(),"立即发货")]')
        time.sleep(1)
        if self.is_element_visible('//span[contains(text(),"立即发货")]'):
            self.click('//span[contains(text(),"立即发货")]')
        self.input(TradeOrderPage.ORDER_ID_INPUT_BOX_SELECTOR, text=search_oid)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)

    def search_order_by_oid_six_month_before(self, search_oid):
        """
        通过oid搜索6个月前的订单
        """
        order_id_input_box = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_id_input_box, text=search_oid)
        self.click_xpath('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button')
        self.click_xpath('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button')
        self.sleep(2)

    def get_list_order_count(self):
        order_list_element = self.find_element(
            '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]')
        order_cards = order_list_element.find_elements(by='xpath', value='./*')
        if order_cards[0].text == "暂无符合条件的订单，请尝试其他条件":
            logger.debug("暂无符合条件的订单，请尝试其他条件")
            return 0
        logger.debug("当前列表订单数 = {}".format(len(order_cards)))
        return len(order_cards)

    def close_merchant_assistant(self):
        """关闭商家助手"""
        if self.is_element_visible(TradeOrderPage.merchant_assistant):
            merchant_assistant_close_button = self.find_elements(TradeOrderPage.merchant_assistant_close_button)
            if len(merchant_assistant_close_button) > 0:
                merchant_assistant_close_button[-1].click()
                self.sleep(2)
            # if len(merchant_assistant_close_button) == 1:
            #     merchant_assistant_close_button[0].click()
            # elif len(merchant_assistant_close_button) == 2:
            #     merchant_assistant_close_button[1].click()

    def close_over_time_ship_order_remind(self):
        """即将超时发货订单提醒弹窗"""
        if self.is_element_visible('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button/span'):
            self.click_xpath('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button/span')
            time.sleep(2)
        elif self.is_element_visible("//button[text()='立即处理']"):
            self.click("//button[text()='立即处理']")
            time.sleep(2)

    def close_northwest_ship_server_tips(self):
        """关闭西北集运提示窗口"""
        if self.is_element_visible(
                '#main-pc-atmosphere-layout > div.seller-main-modal-root > div.seller-main-modal-wrap.kpro-modal-pictext-wrap.seller-main-modal-centered > div > div.kpro-modal-pictext-box > div.kpro-modal-pictext-cont > div.kpro-modal-pictext-btn > button:nth-child(1)'):
            self.click(
                '#main-pc-atmosphere-layout > div.seller-main-modal-root > div.seller-main-modal-wrap.kpro-modal-pictext-wrap.seller-main-modal-centered > div > div.kpro-modal-pictext-box > div.kpro-modal-pictext-cont > div.kpro-modal-pictext-btn > button:nth-child(1)')
            self.sleep(2)

    def to_workbench(self, account='refund_account'):
        """
        进入售后工作台
        """
        # 登陆页面
        self.login("MERCHANT_DOMAIN", account)
        self.assert_title("快手小店")
        time.sleep(3)
        # 判断是否有弹窗，如果有的话点击确定
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            time.sleep(5)
        self.click(RefundPage.left_refund_btn)
        time.sleep(8)
        if self.is_element_visible('#kpro-tool-box--sellerHelperBox > div.VpIP1yFmUG94lwOfFD5b > img'):
            self.click('#kpro-tool-box--sellerHelperBox > div.VpIP1yFmUG94lwOfFD5b > img')
        # 关闭弹窗
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            time.sleep(8)

    def to_Privilege(self, account='huwen', choice=0):
        """
        进入权益中心页面
        """
        if choice == 0:
            self.login("MERCHANT_DOMAIN", account)
        else:
            self.multiLogin(domain="MERCHANT_DOMAIN", account=account, choice=choice)
        self.assert_title("快手小店")
        sleep(5)
        # # 判断是否有弹窗，如果有的话点击确定
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(5)
        self.click(RefundPage.left_shop_btn)
        self.click(RefundPage.left_shop_btn)
        # 点两次， 不然鼠标hover有问题
        self.click(RefundPage.left_privilege_btn)
        self.assert_text("品质放心·售后无忧", '//*[@id="root"]/div/div[1]/div/div/div[1]/div[1]/div[2]')
        self.close_merchant_assistant()

    def to_workbench_prt(self, account='refund_account'):
        """
        临时方案
        进入售后工作台
        """
        self.login_for_PRT_test_land("MERCHANT_DOMAIN", account)
        self.assert_title("快手小店")
        time.sleep(5)
        # 判断是否有弹窗，如果有的话点击确定
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            time.sleep(2)
        self.click(RefundPage.left_refund_btn)
        time.sleep(8)
        if self.is_element_visible('#kpro-tool-box--sellerHelperBox > div.VpIP1yFmUG94lwOfFD5b > img'):
            self.click('#kpro-tool-box--sellerHelperBox > div.VpIP1yFmUG94lwOfFD5b > img')
        # 关闭弹窗
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            time.sleep(8)

    def find_order(self, order_id):
        """
        售后工作台筛选订单
        """
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", order_id)
        self.click('//*[@id="kwaishop-seller-refund-pc-combinationFilter"]/div/div[9]/button[1]')
        time.sleep(1)

    def to_refund_detail(self, order_id, account='refund_account'):
        """
        跳转至售后详情页
        """
        self.to_workbench(account)
        self.find_order(order_id)
        self.click("//span[contains(text(),'售后详情')]")
        time.sleep(5)
        # 关闭详情页弹窗
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            time.sleep(5)

    def to_refund_detail_new(self, refund_id, account="huwen"):
        """
        直接进入售后详情页
        """
        domain = {"online": "https://s.kwaixiaodian.com",
                  "prt": "https://eshop-s.prt.kwaixiaodian.com"}
        self.login("MERCHANT_DOMAIN", account)
        if self.var1 and self.var1 == 'prt':
            host = domain['prt']
        else:
            host = domain['online']
        self.open(f"{host}/zone/refund/detail?refundId={refund_id}&refer=REFUND_LIST")
        sleep(4)
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            time.sleep(5)

    def get_new_page_url(self):
        """
        获取当前页面的url
        """
        sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        cur_url = self.get_current_url()
        return cur_url

    def hover_down1(self, times):
        """
        模拟向下
        :param times: 向下的次数
        """
        for i in range(times):
            ActionChains(self.driver).send_keys(Keys.ARROW_DOWN).perform()
            time.sleep(1)