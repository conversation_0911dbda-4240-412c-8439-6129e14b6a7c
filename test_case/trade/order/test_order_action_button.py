import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderActionButton(BaseTestCase):
    """
    订单按钮
    """
    # 【订单详情】 按钮
    @pytest.mark.p0
    def test_orderlist_detail(self):
        self.to_order_list()
        # 点击进入交易成功Tab
        self.click(TradeOrderPage.SUCCESSFUL_TRANSACTION_ORDER_BUTTON)
        sleep(1)
        # 点击订单详情
        self.click_xpath(TradeOrderPage.order_detail_href)
        self.assert_no_404_errors()
        sleep(1)
        # 断言
        self.assert_equal(self.get_current_url().__contains__('zone/order/detail'), True, msg="当前url：{}".format(self.get_current_url()))

    # 待付款订单【修改价格】按钮
    @pytest.mark.p0
    def test_unpaid_order_price_change(self):
        self.to_order_list()
        # 待付款订单 Tab
        self.click(TradeOrderPage.AWAITING_PAYMENT_ORDER_BUTTON)
        sleep(2)
        prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        tail = ']/div[2]/div[6]/div/div/button[1]'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_order_xpath = prev + str(i) + tail
            if self.is_element_visible(curr_order_xpath):
                button_text = self.get_text(curr_order_xpath)
                # 如果当前订单有【修改价格】按钮
                if button_text == "修改价格":
                    print(curr_order_xpath)
                    self.click_xpath(curr_order_xpath)
                    # self.find_element(TradeOrderPage.PRICE_CHANGE_POPUP)    # TradeOrderPage.PRICE_CHANGE_POPUP 不稳定
                    break
        sleep(2)
        self.assert_no_404_errors()

    # 待付款订单【关闭交易】按钮
    @pytest.mark.p0
    def test_unpaid_order_close_transaction(self):
        self.to_order_list()
        # 待付款订单 Tab
        self.click(TradeOrderPage.AWAITING_PAYMENT_ORDER_BUTTON)
        self.sleep(2)
        prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        tail = ']/div[2]/div[6]/div/div/button[2]'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_order_xpath = prev + str(i) + tail
            # 如果元素存在
            if self.is_element_visible(curr_order_xpath):
                button_text = self.get_text(curr_order_xpath)
                # 如果当前订单有【关闭交易】按钮
                if button_text == "关闭交易":
                    self.click_xpath(curr_order_xpath)
                    # self.find_element(TradeOrderPage.CLOSE_TRANSACTION_POPUP)  # TradeOrderPage.CLOSE_TRANSACTION_POPUP 不稳定
                    break
        self.assert_no_404_errors()

    # 待发货订单【发货】按钮
    @pytest.mark.p0
    def test_goods_dispatch(self):
        self.to_order_list()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        self.sleep(2)
        prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        tail = ']/div[2]/div[6]/div/div/button[1]'
        for i in range(1, 11):
            curr_order_xpath = prev + str(i) + tail
            # 如果元素存在
            if self.is_element_visible(curr_order_xpath):
                button_text = self.get_text(curr_order_xpath)
                after_sale_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div' \
                                   '/div/div/div/div[2]/div[' + str(i) + ']/div[2]/div[2]/div/div/div[3]'
                # 如果当前订单有【发货】按钮
                if button_text == "发 货" and self.is_element_visible(after_sale_xpath) is False:
                    self.click_xpath(curr_order_xpath)
                    sleep(2)
                    self.is_element_visible(TradeOrderPage.GOODS_DISPATCH_POPUP)
                    break
        self.assert_no_404_errors()

    # 待发货订单【合并发货】按钮
    @pytest.mark.p0
    def test_goods_combined_dispatch(self):
        self.to_order_list()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        self.sleep(2)
        prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        tail = ']/div[2]/div[6]/div/div/button[2]'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_order_xpath = prev + str(i) + tail
            # 如果元素存在
            if self.is_element_visible(curr_order_xpath):
                button_text = self.get_text(curr_order_xpath)
                # 如果当前订单有【合并发货】按钮
                if button_text == "合并发货":
                    self.click_xpath(curr_order_xpath)
                    sleep(2)
                    self.switch_to_window(1)
                    self.assert_equal(self.get_current_url().__contains__('zone/order/ship?oid'), True,
                                      msg="当前url：{}".format(self.get_current_url()))
                    break

    # 待发货订单的【修改地址】按钮
    @pytest.mark.p0
    def test_change_consignee_address(self):
        self.to_order_list()
        self.sleep(2)
        self.close_confirm()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        self.sleep(2)
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div/div'
        action_button_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        action_button_suffix = ']/div[2]/div[6]/div/div'
        change_address_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        change_address_suffix = ']/div[2]/div[6]/div/div/button[3]'
        for i in range(1, 11):
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            order_status = self.get_text(order_status_xpath, by='xpath')
            if order_status != "待发货":
                MyLogger.logger.debug("{} - {}".format(i, order_status))
                continue
            action_button_xpath = action_button_prefix + str(i) + action_button_suffix
            action_button_element = self.find_element(action_button_xpath)
            action_buttons = action_button_element.find_elements(by='xpath', value='./*')
            # 遍历查找“修改地址”按钮
            modify_address_button = None
            for btn in action_buttons:
                text = btn.text.strip()
                # if text == "合并发货":
                #     MyLogger.logger.debug(f"{i} - 跳过合并发货按钮")
                #     modify_address_button = None
                #     break  # 合并发货订单不能修改地址，跳出内层循环
                if text == "修改地址":
                    modify_address_button = btn

            if not modify_address_button:
                MyLogger.logger.debug(f"{i} - 未找到“修改地址”按钮")
                continue

            # 点击“修改地址”按钮并断言弹窗
            modify_address_button.click()
            self.sleep(2)
            popup_xpath = '//*[@id="rcDialogTitle1"]'
            self.assertEqual(self.is_element_visible(popup_xpath), True, msg="修改收货地址弹窗不存在")
            popup_title = self.get_text(popup_xpath)
            self.assertEqual(popup_title, "修改收货地址", msg=f"当前标题文案 = {popup_title}")
            break
            # 2025.05.08前代码
            # if len(action_buttons) <= 2:
            #     MyLogger.logger.debug("{} - {}".format(i, action_buttons[0].text))
            #     continue
            # # if len(action_buttons) > 3:
            # #     MyLogger.logger.debug("{} - {}".format(i, action_buttons[0].text))
            # #     continue
            # if action_buttons[-2].text == "合并发货":
            #     MyLogger.logger.debug("{} - {}".format(i, action_buttons[-2].text))
            #     continue
            # self.assertEqual(action_buttons[-2].text, "修改地址", msg="当前按钮 = {}".format(action_buttons[-2].text))
            # change_address_xpath = change_address_prefix + str(i) + change_address_suffix
            # self.assertEqual(self.is_element_visible(change_address_xpath), True)
            # if not self.get_text(change_address_xpath, by='xpath') == "修改地址":
            #     MyLogger.logger.debug("{} - {}".format(i, self.get_text(change_address_xpath, by='xpath')))
            #     continue
            # self.click_xpath(change_address_xpath)
            # self.sleep(2)
            # consignee_address_change_popup = '//*[@id="rcDialogTitle1"]'
            # self.assertEqual(self.is_element_visible(consignee_address_change_popup), True, msg="修改收货地址弹窗不存在")
            # popup_title = self.get_text(consignee_address_change_popup)
            # self.assertEqual(popup_title, "修改收货地址", msg="当前标题文案 = {}".format(popup_title))
            # break

    # 已发货订单【追加包裹】按钮
    @pytest.mark.p0
    def test_additional_package(self):
        self.to_order_list()
        # 已发货订单 Tab
        self.click(TradeOrderPage.SHIPMENT_DISPATCHED_ORDER_BUTTON)
        self.sleep(2)
        prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        tail = ']/div[2]/div[6]/div/div/button[3]'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_order_xpath = prev + str(i) + tail
            # 如果元素存在
            if self.is_element_visible(curr_order_xpath):
                button_text = self.get_text(curr_order_xpath)
                # 如果当前订单有【追加包裹】按钮
                if button_text == '追加包裹':
                    self.click(curr_order_xpath)
                    sleep(2)
                    # self.find_element(TradeOrderPage.ADDITIONAL_PACKAGE_POPUP)  # TradeOrderPage.ADDITIONAL_PACKAGE_POPUP 不稳定
                    break
        self.assert_no_404_errors()

    # 已发货订单【延长收货时间】按钮
    @pytest.mark.p0
    def test_extend_receipt_time(self):
        self.to_order_list()
        # 已发货订单 Tab
        self.click(TradeOrderPage.SHIPMENT_DISPATCHED_ORDER_BUTTON)
        self.sleep(2)
        prev = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        tail = ']/div[2]/div[6]/div/div/button[2]'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_order_xpath = prev + str(i) + tail
            # 如果元素存在
            if self.is_element_visible(curr_order_xpath):
                button_text = self.get_text(curr_order_xpath)
                # 如果当前订单有【延长收货时间】按钮
                if button_text == "延长收货时间":
                    self.click_xpath(curr_order_xpath)
                    sleep(2)
                    self.find_element(TradeOrderPage.EXTEND_RECEIPT_TIME_POPUP)
                    break
        self.assert_no_404_errors()

    # 已发货订单【修改物流】按钮
    @pytest.mark.p0
    def test_change_logistics_button(self):
        self.to_order_list()
        # 已发货订单 Tab
        self.click(TradeOrderPage.SHIPMENT_DISPATCHED_ORDER_BUTTON)
        self.sleep(2)
        button_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        button_xpath_suffix = ']/div[2]/div[6]/div/div/span/button'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_button_path = button_xpath_prefix + str(i) + button_xpath_suffix
            # 如果元素存在
            if self.is_element_visible(curr_button_path):
                button_text = self.get_text(curr_button_path, by='xpath')
                # 如果是【修改物流】按钮
                if button_text == "修改物流":
                    self.click_xpath(curr_button_path)
                    sleep(2)
                    # self.find_element(TradeOrderPage.CHANGE_LOGISTICS_POPUP)  # TradeOrderPage.CHANGE_LOGISTICS_POPUP 链接不稳定
                    break
        self.assert_no_404_errors()

    # 交易成功订单【打开售后入口】按钮
    @pytest.mark.p0
    def test_open_after_sale_entrance(self):
        self.to_order_list()
        # 交易成功订单 Tab
        self.click(TradeOrderPage.SUCCESSFUL_TRANSACTION_ORDER_BUTTON)
        self.sleep(2)
        button_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        button_xpath_suffix = ']/div[2]/div[6]/div/div/button[2]'
        # after_sale_open_popup = '/html/body/div[5]/div/div[2]/div/div[2]/div/div/div[2]/button[2]'
        # 修改为相对xpath
        after_sale_open_popup = "//button[@class='ant-btn ant-btn-primary']//span[contains(text(),'打开售后入口')]"
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_button_xpath = button_xpath_prefix + str(i) + button_xpath_suffix
            if self.is_element_visible(curr_button_xpath):
                button_text = self.get_text(curr_button_xpath, by='xpath')
                if button_text == "打开售后入口":
                    self.click_xpath(curr_button_xpath)
                    self.sleep(5)
                    self.assertEqual(self.is_element_visible(after_sale_open_popup), True,
                                     msg="打开售后入口弹窗没显示")
                    break

    # 定金预售订单【同意退定金】按钮
    @pytest.mark.p0
    def test_agree_refund_deposit_button(self):
        self.to_order_list()
        self.click(TradeOrderPage.ORDER_STATUS_SELECTOR)
        self.sleep(2)
        # unpaid_balance_button = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[8]/div'
        # 修改为相对xpath
        unpaid_balance_button = "//div[@title='已付定金，待付尾款']//div[1]"
        self.assertEquals(self.is_element_visible(unpaid_balance_button), True, "待付尾款下拉框元素错误")
        self.click_xpath(unpaid_balance_button)
        self.sleep(1)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        button_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        button_xpath_suffix = ']/div[2]/div[6]/div/div/button[1]'
        agree_refund_pre_sale_popup = '/html/body/div[6]/div/div[2]/div/div[2]/div/div/div[2]/button[2]'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_button_xpath = button_xpath_prefix + str(i) + button_xpath_suffix
            if self.is_element_visible(curr_button_xpath):
                button_text = self.get_text(curr_button_xpath)
                if button_text == "已同意退定金":
                    self.assertEqual(self.is_element_clickable(curr_button_xpath), False,
                                     msg="{}按钮可点击".format(button_text))
                elif button_text == "同意退定金":
                    self.click_xpath(curr_button_xpath)
                    self.sleep(2)
                    self.assertEqual(self.is_element_visible(agree_refund_pre_sale_popup), True,
                                     msg="同意退定金弹窗没显示")
                    break
        self.assert_no_404_errors()