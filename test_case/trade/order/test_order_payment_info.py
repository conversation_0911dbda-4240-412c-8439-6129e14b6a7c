import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderPaymentInfo(BaseTestCase):
    """
    订单实收款信息
    """

    @pytest.mark.p1
    # 实收款 在线支付
    def test_paid_details_no_subsidy(self):
        self.to_order_list()
        order_id = '2504502560008160'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥0.01"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 3):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    @pytest.mark.p1
    # 实收款 在线支付 + 平台补贴
    def test_paid_details_platform_subsidy(self):
        self.to_order_list()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        order_id = '2431102024000916'
        self.search_order_by_oid_six_month_before(order_id)
        order_number_button = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        curr_order_id = self.get_text(order_number_button, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥100.00", "含平台补贴", "￥5.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 5):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    @pytest.mark.p1
    # 实收款 在线支付 + 店铺优惠
    def test_paid_details_shop_discount(self):
        self.to_order_list()
        order_id = '2502001682648458'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥2.00", "含店铺优惠", "￥1.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 5):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    @pytest.mark.p1
    # 实收款 在线支付 + 店铺优惠 + 平台补贴
    def test_paid_details_platform_subsidy_shop_discount(self):
        self.to_order_list()
        # order_id = '2422500105703553'
        order_id = '2431302181296838'
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        self.search_order_by_oid_six_month_before(order_id)
        # order_number_button = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div[1]/div/a'
        order_number_button = "//a[@class='S83nu6wVlp8Dz2t5shb6']"
        curr_order_id = self.get_text(order_number_button, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        # detail_text = ['在线支付', "￥1.00", "含店铺优惠", "￥2.00", "含平台补贴", "￥0.10"]
        detail_text = ['在线支付', "￥209.00", "含店铺优惠", "￥1.00", "含平台补贴", "￥5.00"]
        detail_text_prefix = "//div[@class='CojkqX5gvYRfOdWhKjHF']//div["
        for i in range(1, 7):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    @pytest.mark.p1("修改包邮链接无了")
    # 实收款 在线支付 + 商品运费 + 修改包邮链接 + 店铺优惠
    # def test_paid_details_delivery_fee(self):
    #     self.to_order_list()
    #     order_id = '2435802164362458'
    #     self.search_order_by_oid(order_id)
    #     curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
    #     self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
    #     detail_text = ['在线支付', "￥4.00", "含商品运费", "￥1.00", "含主播补贴", "￥1.00"]
    #     detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
    #     for i in range(1, 7):
    #         detail_text_xpath = detail_text_prefix + str(i) + ']'
    #         self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
    #         detail_value = self.get_text(detail_text_xpath, by='xpath')
    #         self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    @pytest.mark.p1
    # 实收款 在线支付 + 店铺优惠 + 主播补贴
    def test_paid_details_shop_discount_liver_subsidy(self):
        self.to_order_list()
        order_id = '2509201958538305'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥1.00", "含店铺优惠", "￥1.00", "含主播补贴", "￥0.01"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 7):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))
    @pytest.mark.skip
    @pytest.mark.p1
    # 实收款 在线支付 + 先用后付抵扣
    def test_paid_details_use_before_pay(self):
        self.to_order_list()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        self.sleep(2)
        # order_id = '2419100053909092'
        order_id = '2426901854760079'
        self.search_order_by_oid_six_month_before(order_id)
        order_number_button = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div'
        curr_order_id = self.get_text(order_number_button, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        # detail_text = ['在线支付', "￥12.04", "含先用后付抵扣", "￥10.00"]
        detail_text = ['在线支付', "￥10.04", "含先用后付抵扣", "￥10.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 5):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    @pytest.mark.p1
    # 定金预售 待收定金 + 待收尾款 / 已收定金 + 待收尾款
    def test_pre_sale_order_paid_details(self):
        self.to_order_list()
        paid_detail_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[4]/div/div/div['
        order_id = '2501601848800532'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        details = ["待收定金", "￥2.00", "待收尾款", "￥5.00"]
        for i in range(1, 5):
            paid_detail_xpath = paid_detail_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(paid_detail_xpath), True, msg="实收款明细元素报错")
            paid_detail_text = self.get_text(paid_detail_xpath, by='xpath')
            self.assertEquals(paid_detail_text, details[i-1], msg="实收款明细 = {} - {}".format(paid_detail_text, order_id))
        order_id = '2501601848824532'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        details = ["已收定金", "￥2.00", "待收尾款", "￥5.00"]
        for i in range(1, 5):
            paid_detail_xpath = paid_detail_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(paid_detail_xpath), True, msg="实收款明细元素报错")
            paid_detail_text = self.get_text(paid_detail_xpath, by='xpath')
            self.assertEquals(paid_detail_text, details[i-1], msg="实收款明细 = {} - {}".format(paid_detail_text, order_id))

    @pytest.mark.p1
    # 含税费
    def test_payment_include_tax(self):
        self.login("MERCHANT_DOMAIN", "clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        payment_info_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]'
        payment_info = self.get_text(payment_info_xpath).split("\n")
        self.assertEquals(payment_info[2] + payment_info[3], "含税费￥0.46", msg="当前实收款 = {}".format(payment_info))
