import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderStatusInfo(BaseTestCase):
    """
    订单状态栏信息
    """

    # 待付款 + 剩余时间xx自动关闭
    @pytest.mark.p0
    def test_sub_status_unpaid_count_time(self):
        self.to_order_list()
        # self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        # order_id = '2422600112905013'
        # self.search_order_by_oid_six_month_before(order_id)
        order_id = '2506302560441957'
        self.search_order_by_oid(order_id)
        order_number_button = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        curr_order_id = self.get_text(order_number_button, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        order_status = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[1]/div/div'
        self.assertEqual(self.is_element_visible(order_status), True, msg="订单状态元素错误")
        order_status_text = self.get_text(order_status, by='xpath')
        self.assertEqual(order_status_text, "待付款", msg="当前订单状态 = {}".format(order_status_text))
        order_sub_title = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[2]/div/div/span'
        self.assertEqual(self.is_element_visible(order_sub_title), True, msg="待付款倒计时元素错误")
        unpaid_time_text = self.get_text(order_sub_title, by='xpath')
        self.assertEqual("剩余时间" in unpaid_time_text and "自动关闭" in unpaid_time_text, True, msg="当前待付款倒计时 = {}".format(unpaid_time_text))

    # 待发货 + 请在xx内发货
    @pytest.mark.p0
    def test_sub_status_promised_shipping_time(self):
        self.to_order_list()
        # 待发货TAB
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        self.sleep(2)
        order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        ask_send_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        ask_send_suffix = ']/div[2]/div[2]/div/div/div[1]/div[2]/div/span'
        for i in range(1, 11):
            ask_send = ask_send_prefix + str(i) + ask_send_suffix
            # 绕过 催发货 标签
            if self.is_element_visible(ask_send):
                MyLogger.logger.debug(self.get_text(ask_send))
                continue
            order_sub_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
            order_sub_status_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
            order_sub_status_xpath = order_sub_status_prefix + str(i) + order_sub_status_suffix
            self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
            order_sub_status = self.get_text(order_sub_status_xpath, by='xpath')
            # 绕过 支付成功，平台审核中，预计48小时内完成审核
            if "平台审核中" in order_sub_status:
                MyLogger.logger.debug(order_sub_status)
                continue
            # 绕过 部分发货
            if "部分发货" in order_sub_status:
                MyLogger.logger.debug(order_sub_status)
                continue
            # 绕过 支付成功，拼团中
            if "拼团中" in order_sub_status:
                MyLogger.logger.debug(order_sub_status)
                continue
            # 绕过 支付成功，待用户签署协议
            if "待用户签署协议" in order_sub_status:
                MyLogger.logger.debug(order_sub_status)
                continue
            # 拼团订单的伏待发货标题 = "等待卖家发货"
            self.assertEqual(order_sub_status == "待发货" or order_sub_status == "等待卖家发货"
                             or order_sub_status == '已支付，待填地址'
                             , True,
                             msg="当前订单状态 = {} - {}".format(order_sub_status, order_id))
            order_sub_title_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
            order_sub_title_suffix = ']/div[2]/div[2]/div/div/div[2]/div/div/span'
            # # 修改div
            # order_sub_title_suffix = ']/div[2]/div[2]/div/div/div[3]/div/div/span'
            order_sub_title_xpath = order_sub_title_prefix + str(i) + order_sub_title_suffix
            self.assertEqual(self.is_element_visible(order_sub_title_xpath), True, msg="订单副标题元素错误")
            delivery_time_text = self.get_text(order_sub_title_xpath, by='xpath')
            # 绕过 逾期发货
            if "已超过承诺发货时间" in delivery_time_text:
                MyLogger.logger.debug(delivery_time_text)
                continue
            # 绕过 会员充值虚拟订单
            if "剩余时间" in delivery_time_text and "自动关闭" in delivery_time_text:
                MyLogger.logger.debug(delivery_time_text)
                continue
            self.assertEqual("内发货" in delivery_time_text or "未填写自动" in delivery_time_text, True,
                             msg="当前待发货副标题 = {} - {}".format(delivery_time_text, order_id))
            break

    # 待发货订单 + 已超过承诺发货时间xx
    @pytest.mark.p0
    def test_sub_status_shipment_time_overdue(self):
        self.to_order_list()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        sleep(2)
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        delivery_time_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_time_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/div/span'
        delivery_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
        order_sub_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        # order_sub_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[2]/div/div/span'
        # 修改div
        order_sub_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[2]/div/div/span'
        for i in range(1, 11):
            curr_tag_path = delivery_time_tag_xpath_prefix + str(i) + delivery_time_tag_xpath_suffix
            if self.is_element_visible(curr_tag_path):
                curr_tag_text = self.get_text(curr_tag_path, by='xpath')
                if "已逾期" in curr_tag_text:
                    # 获取发货状态文案
                    curr_delivery_status_path = delivery_status_xpath_prefix + str(i) + delivery_status_xpath_suffix
                    curr_delivery_status = self.get_text(curr_delivery_status_path, by='xpath')
                    # 如果是待发货状态
                    if curr_delivery_status == "待发货":
                        # 获取订单ID
                        curr_oid_xpath = oid_xpath_prefix + str(i) + oid_xpath_suffix
                        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
                        # 获取副标题文案
                        curr_order_sub_status_path = order_sub_status_xpath_prefix + str(i) + order_sub_status_xpath_suffix
                        curr_order_sub = self.get_text(curr_order_sub_status_path, by='xpath')
                        self.assert_equal("已超过承诺发货时间" in curr_order_sub, True,
                                          msg="当前oid：{}，副标题文案：{}".format(curr_oid_text, curr_order_sub))
                        break
        self.assert_no_404_errors()

    # 配货中
    @pytest.mark.p1
    def test_sub_status_picking(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()
        # order_enter = "//span[@id='pinned_menu_for_intersectionObserver_zAoD7EEcix0_under_R553YgpLnhQ']"
        # self.click(order_enter)
        # self.sleep(5)
        # self.close_confirm()
        # self.close_northwest_ship_server_tips()
        # self.close_merchant_assistant()
        # self.to_order_list()
        order_id = '****************'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        order_sub_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[1]/div/div'
        self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
        order_sub_status = self.get_text(order_sub_status_xpath, by='xpath')
        self.assertEquals(order_sub_status, "配货中", msg="当前订单状态 = {} - {}".format(order_sub_status, order_id))

    @pytest.mark.skip("支付成功，平台审核中，预计48小时内完成审核 订单缺失 先跳过")
    # 待发货订单状态文案 支付成功，平台审核中，预计48小时内完成审核
    @pytest.mark.p1
    def test_sub_status_wait_send_risk_review(self):
        """
        rpc：https://kess.corp.kuaishou.com/#/service/info/kwaishop-trade-center?tabCode=discoveries-info&path=%2Fkuaishou%2Fkwaishop%2Fkwaishop-platform%2Fkwaishop-platform-trade%2Fkwaishop-trade-center&tab=debugger&selectDivision=CENTRAL&selectRegistrationName=grpc_kwaishop-trade-center
        方法：updateOrderAttributes
        {
          "oid": 2431701928808183,
          "buyerId": 3600621183,
          "orderVersion": 0,
          "orderAttribute": [],
          "orderExtendAttribute": {
            "blockDeliveryParam": "{\"blockDelivery\":1,\"unBlockTimeStamp\":1733970558000}"
          },
          "stepType": 0,
          "stepNo": 0,
          "orderStatus": []
        }
        """
        self.to_order_list()
        order_id = '2431701928808183'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        order_sub_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[1]/div/div'
        self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
        order_sub_status = self.get_text(order_sub_status_xpath, by='xpath')
        self.assertEquals(order_sub_status, "支付成功，平台审核中，预计48小时内完成审核", msg="当前订单状态 = {} - {}".format(order_sub_status, order_id))

    # 待发货订单状态文案 支付成功，待用户签署协议
    @pytest.mark.skip
    @pytest.mark.p1
    def test_sub_status_wait_buyer_sign_protocol(self):
        """
        rpc: https://kess.corp.kuaishou.com/#/service/info/kwaishop-trade-center?tabCode=discoveries-info&path=%2Fkuaishou%2Fkwaishop%2Fkwaishop-platform%2Fkwaishop-platform-trade%2Fkwaishop-trade-center&tab=debugger&selectDivision=CENTRAL&selectRegistrationName=grpc_kwaishop-trade-center
        方法：updateOrderAttributes
        {
          "oid": 2430402260808183,
          "buyerId": 3600621183,
          "orderVersion": 0,
          "orderAttribute": [],
          "orderExtendAttribute": {
            "fulfillmentAgreementInfo": "{\"signed\": false, \"agreements\": [{\"id\": 1, \"status\": 1, \"expireTime\": 1761795016000, \"operateTime\": 0}]}"
          },
          "stepType": 0,
          "stepNo": 0,
          "orderStatus": []
        }
        """
        self.to_order_list()
        order_id = '2507903040008305'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        order_sub_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div'
        self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
        order_status_element = self.find_element(order_sub_status_xpath)
        order_status_content = order_status_element.find_elements(by='xpath', value='./*')
        order_sub_status = order_status_content[0].text
        order_close_order_text = order_status_content[1].text
        self.assertEquals(order_sub_status, "支付成功，待用户签署协议", msg="当前订单状态 = {} - {}".format(order_sub_status, order_id))
        self.assertEqual("用户未签署协议订单将自动关闭" in order_close_order_text, True, msg="当前订单状态 = {} - {}".format(order_close_order_text, order_id))

    # 部分发货
    @pytest.mark.p1
    def test_sub_status_part_delivery(self):
        self.to_order_list()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        # 点击【部分发货】勾选框
        self.click_xpath(TradeOrderPage.PARTIAL_DELIVERY_CHECKBOX)
        sleep(2)
        part_delivery_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        part_delivery_text_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
        for i in range(1, 11):
            curr_part_text_xpath = part_delivery_text_prefix + str(i) + part_delivery_text_suffix
            if self.is_element_visible(curr_part_text_xpath):
                curr_part_text = self.get_text(curr_part_text_xpath, by='xpath')
                self.assertEqual(curr_part_text, "部分发货", msg="当前订单状态 = {}".format(curr_part_text))
                break

    # 已发货 + 确认收货还剩xxx
    @pytest.mark.p0
    def test_sub_status_wait_sign_order_status(self):
        self.to_order_list()
        # 已发货TAB
        self.click(TradeOrderPage.SHIPMENT_DISPATCHED_ORDER_BUTTON)
        self.sleep(2)
        order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        logistics_abnormal_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        logistics_abnormal_suffix = ']/div[2]/div[2]/div/div/div[1]/div[2]/div/span[1]'
        for i in range(1, 11):
            logistics_abnormal = logistics_abnormal_prefix + str(i) + logistics_abnormal_suffix
            # 绕过 物流异常 标签
            if self.is_element_visible(logistics_abnormal):
                MyLogger.logger.debug(self.get_text(logistics_abnormal, by='xpath'))
                continue
            order_sub_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
            order_sub_status_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
            order_sub_status_xpath = order_sub_status_prefix + str(i) + order_sub_status_suffix
            self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
            order_sub_status = self.get_text(order_sub_status_xpath, by='xpath')
            self.assertIn(order_sub_status, ["已发货","卖家已发货"], msg="当前已发货副标题 = {} - {}".format(order_sub_status, order_id))
            look_logistics_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
            look_logistics_suffix = ']/div[2]/div[2]/div/div/div[2]'
            look_logistics_xpath = look_logistics_prefix + str(i) + look_logistics_suffix
            if self.get_text(look_logistics_xpath) == "卡券核销后系统会自动确认收货":
                continue
            self.assertEqual(self.is_element_visible(look_logistics_xpath), True, msg="订单副标题查看物流元素错误")
            look_logistics_text = self.get_text(look_logistics_xpath, by='xpath')
            self.assertEqual(look_logistics_text, "查看物流", msg="当前查看物流标题 = {} - {}".format(look_logistics_text, order_id))
            sign_time_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
            sign_time_suffix = ']/div[2]/div[2]/div/div/div[3]/div/div/span'
            sign_time_xpath = sign_time_prefix + str(i) + sign_time_suffix
            self.assertEqual(self.is_element_visible(sign_time_xpath), True, msg="订单副标题确认收货文案元素错误")
            sign_time = self.get_text(sign_time_xpath, by='xpath')
            self.assertEqual("确认收货时间还剩" in sign_time or "确认收货还剩" in sign_time, True, msg="当前待收货副标题 = {} - {}".format(sign_time, order_id))
            break

    # 已发货 + 物流异常 + 确认收货还剩xxx
    @pytest.mark.p1
    def test_sub_status_wait_sign_extend_order_status(self):
        self.to_order_list()
        # 已发货TAB
        self.click(TradeOrderPage.SHIPMENT_DISPATCHED_ORDER_BUTTON)
        self.sleep(2)
        order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        logistics_abnormal_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        logistics_abnormal_suffix = ']/div[2]/div[2]/div/div/div[1]/div[2]/div/span[1]'
        for i in range(1, 11):
            logistics_abnormal = logistics_abnormal_prefix + str(i) + logistics_abnormal_suffix
            # 找 物流异常 标签
            if self.is_element_visible(logistics_abnormal):
                logistics_abnormal_text = self.get_text(logistics_abnormal, by='xpath')
                MyLogger.logger.debug(self.get_text(logistics_abnormal, by='xpath'))
                # 绕过催发货
                if "催发货" in logistics_abnormal_text:
                    continue
                self.assertEqual(logistics_abnormal_text, "物流异常", msg="当前物流异常标签文案 = {}".format(logistics_abnormal_text))
                order_sub_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
                order_sub_status_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
                order_sub_status_xpath = order_sub_status_prefix + str(i) + order_sub_status_suffix
                self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
                order_sub_status = self.get_text(order_sub_status_xpath, by='xpath')
                self.assertIn(order_sub_status, ["已发货", "卖家已发货"], msg="当前已发货副标题 = {} - {}".format(order_sub_status, order_id))
                look_logistics_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
                look_logistics_suffix = ']/div[2]/div[2]/div/div/div[2]/span'
                look_logistics_xpath = look_logistics_prefix + str(i) + look_logistics_suffix
                self.assertEqual(self.is_element_visible(look_logistics_xpath), True, msg="订单副标题查看物流元素错误")
                look_logistics_text = self.get_text(look_logistics_xpath, by='xpath')
                self.assertEqual(look_logistics_text, "查看物流", msg="当前查看物流标题 = {} - {}".format(look_logistics_text, order_id))
                sign_time_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
                sign_time_suffix = ']/div[2]/div[2]/div/div/div[3]/div/div/span'
                sign_time_xpath = sign_time_prefix + str(i) + sign_time_suffix
                self.assertEqual(self.is_element_visible(sign_time_xpath), True, msg="订单副标题确认收货文案元素错误")
                sign_time = self.get_text(sign_time_xpath, by='xpath')
                # self.assertEqual("确认收货还剩" in sign_time and "已延长" in sign_time, True,
                #                  msg="当前待收货副标题 = {} - {}".format(sign_time, order_id))
                is_time_format1 = "确认收货还剩" in sign_time and "即将发起系统自动确认收货" in sign_time  # 第一种格式
                is_time_format2 = "确认收货还剩" in sign_time and "已延长" in sign_time  # 第二种格式
                self.assertTrue(is_time_format1 or is_time_format2,
                                msg="当前待收货副标题 = {} - {}".format(sign_time, order_id))
                break
            MyLogger.logger.debug(i)

    @pytest.mark.skip("卡券核销订单过期")
    # 已发货 + 卡券核销后系统会自动确认收货
    @pytest.mark.p1
    def test_sub_status_card_shop_receive_remind(self):
        self.to_order_list()
        order_id = '2430902003722183'
        self.search_order_by_oid(order_id)
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        order_status_xpath = order_status_prefix + str(1) + order_status_suffix
        order_status_element = self.find_element(order_status_xpath)
        sub_status_element = order_status_element.find_elements(by='xpath', value='./*')[1]
        sub_status_content = sub_status_element.text
        self.assertEqual(sub_status_content, "卡券核销后系统会自动确认收货",
                         msg="当前订单副状态 = {}".format(sub_status_content))

    # 已收货订单状态副文案
    @pytest.mark.p1
    def test_sub_status_signed_order_status(self):
        self.to_order_list()
        # 已收货TAB
        self.click(TradeOrderPage.RECEIVED_ORDER_BUTTON)
        self.sleep(2)
        for i in range(1, 11):
            product_name_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div['
            product_name_suffix = ']/div[2]/div[1]/div/div/div/div[2]/div[1]/a'
            product_name_xpath = product_name_prefix + str(i) + product_name_suffix
            self.assertEqual(self.is_element_visible(product_name_xpath), True, "商品名称元素错误")
            product_name = self.get_text(product_name_xpath, by='xpath')
            order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div['
            order_status_suffix = ']/div[2]/div[2]/div/div/div/div/div'
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            self.assertEqual(self.is_element_visible(order_status_xpath), True, "订单标题元素报错")
            order_status = self.get_text(order_status_xpath, by='xpath')
            self.assertIn(order_status, ["已收货", "买家已收货"], msg="已收货订单标题 = {}".format(order_status))
            look_logistics_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div['
            look_logistics_suffix = ']/div[2]/div[2]/div/div/div[2]/span'
            look_logistics_xpath = look_logistics_prefix + str(i) + look_logistics_suffix
            if self.is_element_visible(look_logistics_xpath):
                look_logistics = self.get_text(look_logistics_xpath, by='xpath')
                self.assertEqual(look_logistics, "查看物流", msg="查看物流按钮文案 = {}".format(look_logistics))
                break
            else:
                MyLogger.logger.debug(product_name)

    # 交易成功订单状态文案
    @pytest.mark.p1
    def test_sub_status_completed_order_status(self):
        self.to_order_list()
        # 已收货TAB
        self.click(TradeOrderPage.SUCCESSFUL_TRANSACTION_ORDER_BUTTON)
        self.sleep(2)
        for i in range(1, 11):
            product_name_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div['
            product_name_suffix = ']/div[2]/div[1]/div/div/div/div[2]/div[1]/a'
            product_name_xpath = product_name_prefix + str(i) + product_name_suffix
            self.assertEqual(self.is_element_visible(product_name_xpath), True, "商品名称元素错误")
            product_name = self.get_text(product_name_xpath, by='xpath')
            order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div['
            order_status_suffix = ']/div[2]/div[2]/div/div/div/div/div'
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            self.assertEqual(self.is_element_visible(order_status_xpath), True, "订单标题元素报错")
            order_status = self.get_text(order_status_xpath, by='xpath')
            self.assertEqual(order_status, "交易成功", msg="交易成功订单标题 = {}".format(order_status))
            look_logistics_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div['
            look_logistics_suffix = ']/div[2]/div[2]/div/div/div[2]/span'
            look_logistics_xpath = look_logistics_prefix + str(i) + look_logistics_suffix
            if self.is_element_visible(look_logistics_xpath):
                look_logistics = self.get_text(look_logistics_xpath, by='xpath')
                self.assertEqual(look_logistics, "查看物流", msg="查看物流按钮文案 = {}".format(look_logistics))
                break
            else:
                MyLogger.logger.debug(product_name)

    # 交易关闭订单状态文案
    @pytest.mark.p1
    def test_sub_status_closed_order_status(self):
        self.to_order_list()
        order_id = '2505201691864052'
        self.search_order_by_oid(order_id)
        self.sleep(5)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        if self.is_element_visible("(//div[@id='rc-tabs-3-tab-0'])[1]"):
            self.click("(//div[@id='rc-tabs-3-tab-0'])[1]")
        self.sleep(2)
        for i in range(3):
            ActionChains(self.driver).send_keys(Keys.PAGE_DOWN).perform()
        self.sleep(2)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        self.assertEqual(self.is_element_visible(TradeOrderPage.ORDER_STATUS), True, msg="订单状态元素错误")
        order_status = self.get_text(TradeOrderPage.ORDER_STATUS)
        self.assertEqual(order_status, "订单关闭", msg="当前订单状态 = {} - {}".format(order_status, curr_order_id))

    # 交易关闭 + 用户超时未签署定制协议，系统自动退款成功，订单关闭
    @pytest.mark.skip
    @pytest.mark.p1
    def test_sub_status_buyer_no_sign_protocol_close_order(self):
        self.to_order_list()
        order_id = '2430402260808183'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        order_sub_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div'
        self.assertEqual(self.is_element_visible(order_sub_status_xpath), True, msg="订单副标题元素错误")
        order_status_element = self.find_element(order_sub_status_xpath)
        order_status_content = order_status_element.find_elements(by='xpath', value='./*')
        order_sub_status = order_status_content[0].text
        order_close_order_text = order_status_content[1].text
        self.assertEquals(order_sub_status, "交易关闭", msg="当前订单状态 = {} - {}".format(order_sub_status, order_id))
        self.assertEqual("用户超时未签署定制协议，系统自动退款成功，订单关闭" == order_close_order_text, True, msg="当前订单状态 = {} - {}".format(order_close_order_text, order_id))

    @pytest.mark.skip("拼团订单 - 6小时自动成团")
    # 拼团订单 支付成功，拼团中 + 用户支付6小时后将自动成团
    @pytest.mark.p1
    def test_sub_status_team_purchasing(self):
        self.to_order_list()
        order_id = '2430401940104183'
        self.search_order_by_oid(order_id)
        self.sleep(5)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        order_status_xpath = order_status_prefix + str(1) + order_status_suffix
        order_status_element = self.find_element(order_status_xpath)
        team_purchasing_element = order_status_element.find_elements(by='xpath', value='./*')
        self.assertEqual(team_purchasing_element[0].text, "支付成功，拼团中", msg="当前文案 = {}".format(team_purchasing_element[0].text))
        self.assertEqual(team_purchasing_element[1].text, "用户支付6小时后将自动成团", msg="当前文案 = {}".format(team_purchasing_element[1].text))

    # 拼团订单 交易关闭 + 超时未拼团成功或拼团中订单已退款，订单关闭
    @pytest.mark.p1
    def test_sub_status_team_purchase_failed(self):
        self.to_order_list()
        # order_id = '2434702330409183'
        order_id = '2515502560042342'
        self.search_order_by_oid(order_id)
        self.click(TradeOrderPage.ALL_ORDER_BUTTON)
        self.sleep(2)
        order_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div'
        order_status_element = self.find_element(order_status_xpath)
        team_purchasing_element = order_status_element.find_elements(by='xpath', value='./*')
        self.assertEqual(team_purchasing_element[0].text, "交易关闭", msg="当前文案 = {}".format(team_purchasing_element[0].text))
        # TODO 找不到合适的订单，2025/6/10注释
        # self.assertEqual(team_purchasing_element[1].text, "超时未拼团成功或拼团中订单已退款，订单关闭", msg="当前文案 = {}".format(team_purchasing_element[1].text))

    # 定金预售待付尾款订单的 订单售后状态=待付尾款 + 尾款支付倒计时
    @pytest.mark.p1
    def test_sub_status_wait_pay_balance(self):
        self.to_order_list()
        self.click(TradeOrderPage.ORDER_STATUS_SELECTOR)
        self.sleep(2)
        # unpaid_balance_button = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[8]/div'
        # 相对xpath
        unpaid_balance_button = "//div[contains(@title,'已付定金，待付尾款')]//div[1]"
        self.assertEquals(self.is_element_visible(unpaid_balance_button), True, "待付尾款下拉框元素错误")
        self.click_xpath(unpaid_balance_button)
        self.sleep(1)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        # order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON)
        ORDER_NUMBER = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div[1]/div[1]/div/div/div[1]/div/div[1]/div/a'
        order_id = self.get_text(ORDER_NUMBER)
        # self.assertEquals(self.is_element_visible(TradeOrderPage.ORDER_STATUS), True, msg="订单状态元素报错")
        # order_status = self.get_text(TradeOrderPage.ORDER_STATUS, by='xpath')
        ORDER_STATUS = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[1]/div/div'
        self.assertEquals(self.is_element_visible(ORDER_STATUS), True, msg="订单状态元素报错")
        order_status = self.get_text(ORDER_STATUS, by='xpath')
        self.assertEquals(order_status, "待付尾款", msg="待付尾款订单状态 = {} - {}".format(order_status, order_id))
        # self.assertEquals(self.is_element_visible(TradeOrderPage.ORDER_SUB_TITLE), True, msg="订单状态副标题元素报错")
        # order_sub_title = self.get_text(TradeOrderPage.ORDER_SUB_TITLE)
        ORDER_SUB_TITLE = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[2]/div/div/span'
        self.assertEquals(self.is_element_visible(ORDER_SUB_TITLE), True, msg="订单状态副标题元素报错")
        order_sub_title = self.get_text(ORDER_SUB_TITLE)
        self.assertEquals("尾款支付倒计时" in order_sub_title, True, msg="待付尾款副标题文案 = {} - {}".format(order_sub_title, order_id))

    # 定金预售订单的 订单售后状态=未支付尾款，已协商退定金 + 售后按钮
    @pytest.mark.p1
    def test_sub_status_refunded_deposit(self):
        self.to_order_list()
        order_id = '2505002560003183'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        self.assertEquals(self.is_element_visible(TradeOrderPage.ORDER_STATUS), True, msg="订单状态元素报错")
        order_status = self.get_text(TradeOrderPage.ORDER_STATUS, by='xpath')
        self.assertEquals(order_status, "未支付尾款，已协商退定金", msg="已协商/自动退定金订单状态 = {} - {}".format(order_status, order_id))
        after_sale_title = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[2]/div[1]'
        self.assertEquals(self.is_element_visible(after_sale_title), True, "售后按钮元素报错")
        self.assertEquals(self.get_text(after_sale_title, by='xpath'), "售后：",
                          msg="售后按钮标题：{} - {}".format(self.get_text(after_sale_title), order_id))
        after_sale_link = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div[2]/div[2]/a'
        self.assertEquals(self.is_element_visible(after_sale_link), True, "退款成功元素报错")
        self.assertEquals(self.get_text(after_sale_link, by='xpath'), "退款成功",
                          msg="售后按钮标题：{} - {}".format(self.get_text(after_sale_link), order_id))

    # 定金预售超时未付尾款关单订单的 订单售后状态=超时未付定金订单关闭
    @pytest.mark.p1
    def test_sub_status_order_closed_for_unpaid_deposit(self):
        self.to_order_list()
        order_id = '2505502561179959'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        self.assertEquals(self.is_element_visible(TradeOrderPage.ORDER_STATUS), True, msg="订单状态元素报错")
        order_status = self.get_text(TradeOrderPage.ORDER_STATUS, by='xpath')
        self.assertEquals(order_status, "超时未付定金订单关闭", msg="超时未付定金订单状态 = {} - {}".format(order_status, order_id))

    # 定金预售超时未付尾款关单订单的 订单售后状态=超时未付尾款订单关闭
    @pytest.mark.p1
    def test_sub_status_order_closed_for_unpaid_balance(self):
        self.to_order_list()
        order_id = '2501301935200260'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        self.assertEquals(self.is_element_visible(TradeOrderPage.ORDER_STATUS), True, msg="订单状态元素报错")
        order_status = self.get_text(TradeOrderPage.ORDER_STATUS, by='xpath')
        self.assertEquals(order_status, "超时未付尾款订单关闭", msg="超时未付尾款订单状态 = {} - {}".format(order_status, order_id))

    # 订单状态的【催发货】标签
    @pytest.mark.p1
    def test_sub_status_order_ask_delivery(self):
        self.to_order_list()
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        self.sleep(2)
        self.click(TradeOrderPage.ORDER_LABEL_SELECTOR)
        self.sleep(2)
        # ask_send_button = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[7]'
        # 相对xpath
        ask_send_button = "//label[contains(text(),'催发货')]"
        self.assertEqual(self.is_element_visible(ask_send_button), True, msg="催发货选择标签元素错误")
        self.click_xpath(ask_send_button)
        self.sleep(2)
        self.click_xpath(TradeOrderPage.SEARCH_BUTTON_MORE_FILTERING)
        self.sleep(2)
        order_count = self.get_list_order_count()
        if order_count != 0:
            order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON)
            # 催发货标签
            ask_delivery_tag = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[1]/div[2]/div/span'
            # ask_delivery_tag = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div//div[5]/div[3]/div/div[2]/div/div/div/div/div/div[1]/span'
            self.assertEqual(self.is_element_visible(ask_delivery_tag), True, msg="催发货标签元素错误 - {}".format(order_id))
            ask_delivery_text = self.get_text(ask_delivery_tag, by='xpath')
            self.assertEqual("催发货" in ask_delivery_text, True, msg="催发货标签当前文案 = {} - {}".format(ask_delivery_text, order_id))

    # 订单状态的【加急催发货】标签
    @pytest.mark.p1
    def test_sub_status_order_buzz_ask_delivery(self):
        self.to_order_list()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        # 加急催发货订单
        target_order_id = '2429101693171706'
        self.search_order_by_oid_six_month_before(target_order_id)
        sleep(5)
        order_number_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        curr_order_id = self.get_text(order_number_xpath, by='xpath')
        self.assertEqual(target_order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        # 加急催发货标签
        buzz_ask_delivery_tag = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[1]/div[2]/div/span'
        self.assertEqual(self.is_element_visible(buzz_ask_delivery_tag), True, msg="加急催发货标签元素错误 - {}".format(target_order_id))
        ask_delivery_text = self.get_text(buzz_ask_delivery_tag, by='xpath')
        self.assertEqual(ask_delivery_text, "加急催发货", msg="催发货标签当前文案 = {} - {}".format(ask_delivery_text, target_order_id))

    # 订单状态的【优先发货】标签
    @pytest.mark.p1
    def test_sub_status_priority_send(self):
        self.to_order_list()
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        self.sleep(2)
        self.click(TradeOrderPage.ORDER_LABEL_SELECTOR)
        self.sleep(2)
        # priority_send_button = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[8]'
        # 相对xpath
        priority_send_button = "//label[contains(text(),'优先发货')]"
        self.assertEqual(self.is_element_visible(priority_send_button), True, msg="优先发货选择标签元素错误")
        self.assertEqual(self.get_text(priority_send_button, by='xpath'), "优先发货",
                         msg="优先发货选择标签文案错误，当前文案={}".format(self.get_text(priority_send_button, by='xpath')))
        self.click_xpath(priority_send_button)
        self.sleep(2)
        self.click_xpath(TradeOrderPage.SEARCH_BUTTON_MORE_FILTERING)
        self.sleep(2)
        order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON)
        # 优先发货标签
        priority_delivery_tag = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[1]/div[2]/div/span'
        # priority_delivery_tag = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[6]/div//div[5]/div[3]/div/div[2]/div/div/div/div/div/div[1]/span'
        self.assertEqual(self.is_element_visible(priority_delivery_tag), True, msg="优先发货标签元素错误 - {}".format(order_id))
        priority_delivery_text = self.get_text(priority_delivery_tag, by='xpath')
        self.assertEqual(priority_delivery_text, "优先发货", msg="优先发货标签当前文案 = {} - {}".format(priority_delivery_text, order_id))

    # 部分发货订单的【查看物流】按钮
    @pytest.mark.p0
    def test_sub_status_partial_delivery_check_logistics(self):
        self.to_order_list()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        sleep(2)
        # 点击【部分发货】勾选框
        self.click_xpath(TradeOrderPage.PARTIAL_DELIVERY_CHECKBOX)
        sleep(2)
        self.assert_no_404_errors()
        check_logistics_button_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        check_logistics_button_suffix = ']/div[2]/div[2]/div/div/div[2]/span'
        for i in range(1, 11):
            curr_check_logistics_button_path = check_logistics_button_prefix + str(
                i) + check_logistics_button_suffix
            if self.is_element_clickable(curr_check_logistics_button_path):
                curr_check_logistics_button_text = self.get_text(curr_check_logistics_button_path, by='xpath')
                if curr_check_logistics_button_text == "查看物流":
                    self.click_xpath(curr_check_logistics_button_path)
                    sleep(2)
                    self.assert_equal(self.is_element_visible("//div[text()='物流详情']"), True, msg="物流详情页显示异常")
                    break
        self.assert_no_404_errors()

    # 已收货订单【查看物流】按钮
    @pytest.mark.p0
    def test_sub_status_look_logistics_button(self):
        self.to_order_list()
        # 已发货订单 Tab
        self.click(TradeOrderPage.RECEIVED_ORDER_BUTTON)
        self.sleep(2)
        button_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        button_xpath_suffix = ']/div[2]/div[2]/div/div/div[2]/span'
        # 遍历当前订单列表所有订单
        for i in range(1, 11):
            curr_button_path = button_xpath_prefix + str(i) + button_xpath_suffix
            # 如果元素存在
            if self.is_element_visible(curr_button_path):
                button_text = self.get_text(curr_button_path, by='xpath')
                # 如果是【查看物流】按钮
                if button_text == "查看物流":
                    self.click_xpath(curr_button_path)
                    sleep(2)
                    break
        self.assert_no_404_errors()

    # 订单售后状态 待卖家处理
    @pytest.mark.p0
    def test_sub_status_after_sale_wait_seller_process(self):
        self.to_order_list()
        # 售后状态下拉框
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        # under_after_sale = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[2]/div'
        # 相对xpath
        under_after_sale = "//label[contains(text(),'售后中')]"
        self.assertEquals(self.is_element_visible(under_after_sale), True, msg="售后中下拉框元素报错")
        self.click_xpath(under_after_sale)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_count = self.get_list_order_count()
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        for i in range(1, order_count+1):
            MyLogger.logger.debug("{}th's order".format(i))
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            # 这里可能没有10笔订单，通过判断 第i个位置的订单状态 是否存在，来判断是否有第i笔订单
            if not self.is_element_visible(order_status_xpath):
                break
            # 获取 订单/售后状态 元素
            order_status_element = self.find_element(order_status_xpath)
            # 售后状态在 订单/售后状态 的最后一个
            after_sale_element = order_status_element.find_elements(by='xpath', value='./*')[-1]
            after_sale_status = after_sale_element.text.split("\n")[-1]
            if after_sale_status != "待卖家处理":
                MyLogger.logger.debug(after_sale_status)
                continue
            self.assertEquals(after_sale_status, "待卖家处理", msg="待卖家处理售后状态 = {}".format(after_sale_status))
            break

    # 订单售后状态 待买家处理
    @pytest.mark.p0
    def test_sub_status_after_sale_wait_buyer_process(self):
        self.to_order_list()
        # 售后状态下拉框
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        # under_after_sale = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[2]/div'
        # 相对xpath
        under_after_sale = "//label[contains(text(),'售后中')]"
        self.assertEquals(self.is_element_visible(under_after_sale), True, msg="售后中下拉框元素报错")
        self.click_xpath(under_after_sale)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_count = self.get_list_order_count()
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        for i in range(1, order_count+1):
            MyLogger.logger.debug("{}th's order".format(i))
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            # 这里可能没有10笔订单，通过判断 第i个位置的订单状态 是否存在，来判断是否有第i笔订单
            if not self.is_element_visible(order_status_xpath):
                break
            # 获取 订单/售后状态 元素
            order_status_element = self.find_element(order_status_xpath)
            # 售后状态在 订单/售后状态 的最后一个
            after_sale_element = order_status_element.find_elements(by='xpath', value='./*')[-1]
            after_sale_status = after_sale_element.text.split("\n")[-1]
            if after_sale_status != "待买家处理":
                MyLogger.logger.debug(after_sale_status)
                continue
            self.assertEquals(after_sale_status, "待买家处理", msg="待买家处理售后状态 = {}".format(after_sale_status))
            break

    # 订单售后状态 待买家退货
    @pytest.mark.p0
    def test_sub_status_after_sale_wait_buyer_return_product(self):
        self.to_order_list()
        # 售后状态下拉框
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        self.sleep(2)
        # under_after_sale = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[2]/div'
        # 相对xpath
        under_after_sale = "//label[contains(text(),'售后中')]"
        self.assertEquals(self.is_element_visible(under_after_sale), True, msg="售后中下拉框元素报错")
        self.click_xpath(under_after_sale)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_count = self.get_list_order_count()
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        for i in range(1, order_count+1):
            MyLogger.logger.debug("{}th's order".format(i))
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            # 这里可能没有10笔订单，通过判断 第i个位置的订单状态 是否存在，来判断是否有第i笔订单
            if not self.is_element_visible(order_status_xpath):
                break
            # 获取 订单/售后状态 元素
            order_status_element = self.find_element(order_status_xpath)
            # 售后状态在 订单/售后状态 的最后一个
            after_sale_element = order_status_element.find_elements(by='xpath', value='./*')[-1]
            after_sale_status = after_sale_element.text.split("\n")[-1]
            if after_sale_status != "待买家退货":
                MyLogger.logger.debug(after_sale_status)
                continue
            self.assertEquals(after_sale_status, "待买家退货", msg="待买家退货售后状态 = {}".format(after_sale_status))
            break

    # 订单售后状态 待卖家收货
    @pytest.mark.p0
    def test_sub_status_wait_seller_sign(self):
        self.to_order_list()
        # 售后状态下拉框
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        self.sleep(2)
        # under_after_sale = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[2]/div'
        # 相对xpath
        under_after_sale = "//label[contains(text(),'售后中')]"
        self.assertEquals(self.is_element_visible(under_after_sale), True, msg="售后中下拉框元素报错")
        self.click_xpath(under_after_sale)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_count = self.get_list_order_count()
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        for i in range(1, order_count+1):
            MyLogger.logger.debug("{}th's order".format(i))
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            # 这里可能没有10笔订单，通过判断 第i个位置的订单状态 是否存在，来判断是否有第i笔订单
            if not self.is_element_visible(order_status_xpath):
                break
            # 获取 订单/售后状态 元素
            order_status_element = self.find_element(order_status_xpath)
            # 售后状态在 订单/售后状态 的最后一个
            after_sale_element = order_status_element.find_elements(by='xpath', value='./*')[-1]
            after_sale_status = after_sale_element.text.split("\n")[-1]
            if after_sale_status != "待卖家收货":
                MyLogger.logger.debug(after_sale_status)
                continue
            self.assertEquals(after_sale_status, "待卖家收货", msg="待卖家收货售后状态 = {}".format(after_sale_status))
            break

    # 订单售后状态 待平台判定
    @pytest.mark.p0
    def test_sub_status_after_sale_wait_platform_judge(self):
        self.to_order_list()
        # 售后状态下拉框
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        self.sleep(2)
        # under_after_sale = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[2]/div'
        # 相对xpath
        under_after_sale = "//label[contains(text(),'售后中')]"
        self.assertEquals(self.is_element_visible(under_after_sale), True, msg="售后中下拉框元素报错")
        self.click_xpath(under_after_sale)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_count = self.get_list_order_count()
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        for i in range(1, order_count+1):
            MyLogger.logger.debug("{}th's order".format(i))
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            # 获取 订单/售后状态 元素
            order_status_element = self.find_element(order_status_xpath)
            # 售后状态在 订单/售后状态 的最后一个
            after_sale_element = order_status_element.find_elements(by='xpath', value='./*')[-1]
            after_sale_status = after_sale_element.text.split("\n")[-1]
            if after_sale_status != "待平台判定":
                MyLogger.logger.debug(after_sale_status)
                continue
            self.assertEquals(after_sale_status, "待平台判定", msg="待平台判定售后状态 = {}".format(after_sale_status))
            break

    # 订单售后状态 退款执行中
    @pytest.mark.p0
    def test_sub_status_after_sale_refunding(self):
        self.to_order_list()
        # 售后状态下拉框
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        self.sleep(2)
        # under_after_sale = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[3]/div'
        # 相对xpath
        under_after_sale = "//label[contains(text(),'同意退款，退款中')]"
        self.assertEquals(self.is_element_visible(under_after_sale), True, msg="售后中下拉框元素报错")
        self.click_xpath(under_after_sale)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.sleep(2)
        order_count = self.get_list_order_count()
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div'
        for i in range(1, order_count+1):
            MyLogger.logger.debug("{}th's order".format(i))
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            # 获取 订单/售后状态 元素
            order_status_element = self.find_element(order_status_xpath)
            # 售后状态在 订单/售后状态 的最后一个
            after_sale_element = order_status_element.find_elements(by='xpath', value='./*')[-1]
            after_sale_status = after_sale_element.text.split("\n")[-1]
            if after_sale_status != "退款执行中":
                MyLogger.logger.debug(after_sale_status)
                continue
            self.assertEquals(after_sale_status, "退款执行中", msg="退款执行中售后状态 = {}".format(after_sale_status))
            break

    # 订单售后状态 退款成功
    @pytest.mark.p0
    def test_sub_status_after_refund_success(self):
        self.to_order_list()
        order_id = '2514701696602260'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        refund_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[2]/div[2]/a'
        self.assertEquals(self.is_element_visible(refund_status_xpath), True, msg="售后状态元素报错")
        refund_status = self.get_text(refund_status_xpath, by='xpath')
        self.assertEquals(refund_status, "退款成功", msg="退款成功售后状态 = {}".format(refund_status))

    # 订单售后状态 退款关闭
    @pytest.mark.p0
    def test_sub_status_after_refund_closed(self):
        self.to_order_list()
        # order_id = '2431702250058732'
        order_id = '2511102006424827'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        refund_status_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[7]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[3]/div[2]/a'
        self.assertEquals(self.is_element_visible(refund_status_xpath), True, msg="售后状态元素报错")
        refund_status = self.get_text(refund_status_xpath, by='xpath')
        self.assertEquals(refund_status, "退款关闭", msg="退款关闭售后状态 = {}".format(refund_status))