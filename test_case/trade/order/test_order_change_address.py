import re
import time
import logging


from urllib.parse import urlparse
import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderChangeAddress(BaseTestCase):
    """
    订单改地址服务
    """

    # 【待处理】同意修改地址订单正常显示
    @pytest.mark.p1
    def test_handle_order_change_address_wait(self):
        # 登陆账号
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        self.sleep(2)
        # 打开改地址服务
        self.click(TradeOrderPage.left_order_enter)
        self.sleep(2)
        change_order_address = "//span[@id='menu_item_wT7GT1ygRUI']"
        self.click(change_order_address)
        self.sleep(5)
        self.close_confirm()
        self.close_northwest_ship_server_tips()
        self.close_merchant_assistant()
        self.sleep(2)
        self.assert_no_404_errors()
        # 订单输入框
        ORDER_ID_INPUT_BOX = "//input[@id='oid']"
        # 查询按钮
        SEARCH_BUTTON = "//button[@type='submit']//span[contains(text(),'查询')]"
        agree_change_order_id = ''
        self.input(ORDER_ID_INPUT_BOX, text=agree_change_order_id)
        self.click(SEARCH_BUTTON)
        self.sleep(2)


    # 【已处理】同意修改地址订单正常显示
    @pytest.mark.p1
    def test_handle_order_change_address_agree(self):
        # 登陆账号
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        self.sleep(2)
        # 打开改地址服务
        self.click(TradeOrderPage.left_order_enter)
        self.sleep(2)
        change_order_address = "//span[@id='menu_item_wT7GT1ygRUI']"
        self.click(change_order_address)
        self.sleep(5)
        self.close_confirm()
        self.close_northwest_ship_server_tips()
        self.close_merchant_assistant()
        self.sleep(2)
        # 打开已处理订单
        handle_order_address = "//div[@id='rc-tabs-1-tab-2']"
        # 订单输入框
        ORDER_ID_INPUT_BOX = "//input[@id='oid']"
        # 查询按钮
        SEARCH_BUTTON = "//button[@type='submit']//span[contains(text(),'查询')]"
        self.click(handle_order_address)
        self.sleep(2)
        self.close_confirm()
        agree_change_order_id = '****************'
        self.input(ORDER_ID_INPUT_BOX, text=agree_change_order_id)
        self.click(SEARCH_BUTTON)
        self.sleep(2)
        curr_tag_status = "//span[@class='ant-badge-status-text']"
        # curr_oid_xpath = '/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/a[1]'
        curr_oid_xpath = "//a[@title='点击跳转订单详情页']"
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        curr_tag_value = self.get_text(curr_tag_status)
        self.assert_equal(self.is_element_visible(curr_tag_status), True,
                          msg="当前oid：{}, 处理结果：{}".format(curr_oid_text, curr_tag_status))
        self.assert_equal(curr_tag_value, "已同意",
                          msg="当前oid：{}, 处理结果：{}".format(curr_oid_text, curr_tag_value))

    # 【已处理】拒绝修改地址订单正常显示
    @pytest.mark.p1
    def test_handle_order_change_address_refuse(self):
        # 登陆账号
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        self.sleep(2)
        # 打开改地址服务
        self.click(TradeOrderPage.left_order_enter)
        self.sleep(2)
        change_order_address = "//span[@id='menu_item_wT7GT1ygRUI']"
        self.click(change_order_address)
        self.sleep(5)
        self.close_confirm()
        self.close_northwest_ship_server_tips()
        self.close_merchant_assistant()
        self.sleep(2)
        # 打开已处理订单
        handle_order_address = "//div[@id='rc-tabs-1-tab-2']"
        # 订单输入框
        ORDER_ID_INPUT_BOX = "//input[@id='oid']"
        # 查询按钮
        SEARCH_BUTTON = "//button[@type='submit']//span[contains(text(),'查询')]"
        self.click(handle_order_address)
        self.sleep(2)
        self.close_confirm()
        agree_change_order_id = '****************'
        self.input(ORDER_ID_INPUT_BOX, text=agree_change_order_id)
        self.click(SEARCH_BUTTON)
        self.sleep(2)
        curr_tag_status = "//span[@class='ant-badge-status-text']"
        # curr_oid_xpath = '/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/a[1]'
        curr_oid_xpath = "//a[@title='点击跳转订单详情页']"
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        curr_tag_value = self.get_text(curr_tag_status)
        self.assert_equal(self.is_element_visible(curr_tag_status), True,
                          msg="当前oid：{}, 处理结果：{}".format(curr_oid_text, curr_tag_status))
        self.assert_equal(curr_tag_value, "已拒绝",
                          msg="当前oid：{}, 处理结果：{}".format(curr_oid_text, curr_tag_value))
