import logging
import pytest
from time import sleep

# from ddt import ddt
from ..base import BaseTestCase
# from page_objects.trade_order.trade_order_page import TradeOrderPage
from selenium.webdriver.common.by import By
from utils.kconf_help import get_kconf

oid_list = get_kconf(env_name="staging", conf_key='tradeQaTool.appUiStatistics.appUIoidmanage')


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)

# 物流详情
class TestPcLogisticsDetails(BaseTestCase):
    # 苹果mono物流信息新增状态
    '''
    已下线
    '''
    # @pytest.mark.p0
    # # @pytest.mark.skip
    # def test_appleo2o_new_logistics_status(self):
    #     # self.to_order_list_checking_order()
    #     self.login("MERCHANT_DOMAIN", "member_account2")
    #     self.assert_title("快手小店")
    #     # self.sleep(2)
    #     self.open_order_list_checking_order()
    #     self.close_confirm()
    #     # copy selector
    #     # self.click('#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(1) > div > div.ant-space.ant-space-vertical > div > div > div.ant-pro-toolbar-table-actions > div > button:nth-child(3) > span')
    #     # copy Xpath
    #     # self.click('//*[@id="mergeship"]/span')
    #     sleep(1)
    #     # # 六个月前订单tab
    #     # self.click("#rc-tabs-0-tab-2")
    #     sleep(1)
    #     assert self.is_element_visible("//input[@placeholder='请输入完整的订单编号']"), "订单编号输入框不存在"
    #     self.input("//input[@placeholder='请输入完整的订单编号']", ****************)
    #     assert self.get_text("//input[@placeholder='请输入完整的订单编号']")=='****************', '输入订单号异常'
    #     sleep(1)
    #     assert self.is_element_clickable("button[class='ant-btn ant-btn-primary ant-btn-background-ghost']"), "查询按钮不存在"
    #     self.click("button[class='ant-btn ant-btn-primary ant-btn-background-ghost']")
    #     sleep(1)
    #     self.click("span[class='P4hzH6TNQmnQW_OLwqLM']")
    #     sleep(1)
    #     elements = self.driver.find_elements(By.CLASS_NAME, 'VFsahM2_JlxBAWVcSxv4')
    #     texts = [element.text for element in elements]
    #     assert '商家呼叫骑手' in texts, "商家呼叫骑手不存在"
    #     assert '骑手已接单' in texts, "骑手已接单不存在"
    #     assert '配送已取消' in texts, "配送已取消不存在"
    #     assert '骑手已取货' in texts, "骑手已取货不存在"

    # 修改物流
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_modified_logistics(self):
        # 登陆
        # 点击订单查询
        self.to_order_list_checking_order()

        # 点击已发货
        self.click('#rc-tabs-1-tab-3')
        sleep(2)
        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")

        sleep(2)
        # 点击修改物流
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-btn.ant-btn-link')
        texts = [element.text for element in elements]
        elements[texts.index('修改物流')].click()
        sleep(1)
        # 快递单号输入框输入内容
        self.add_text('.ant-input.ant-dropdown-trigger', '自动化测试')
        assert self.get_text('.ant-input.ant-dropdown-trigger') == '自动化测试', "快递单号号输入框不可用"
        sleep(1)
        # 点击物流公司输入框
        self.click('#expressCode')
        # 修改物流公司
        self.click('.ant-select-item.ant-select-item-option.ant-select-item-option-grouped')
        if(self.is_element_visible("//body/div/div[@class='ant-drawer ant-drawer-right ant-drawer-open']/div[@class='ant-drawer-content-wrapper']/div[@class='ant-drawer-content']/div[@class='ant-drawer-wrapper-body']/div[@class='ant-drawer-body']/div[4]/div[1]") == False):
            # 取消按钮
            assert self.is_element_clickable('.ant-drawer-footer > div > button.ant-btn'), '取消按钮不存在\n' + self.get_text(
                '.ant-drawer-footer > div > button.ant-btn')
            # 发货按钮
            assert self.is_element_clickable(
                '.ant-drawer-footer > div > button.ant-btn.ant-btn-primary'), '确认按钮不存在\n' + self.get_text(
                '.ant-drawer-footer > div > button.ant-btn.ant-btn-primary')

    # 追加包裹弹窗
    @pytest.mark.p1
    # @pytest.mark.skip
    def test_added_package_popup(self):
        # 登陆
        # 点击订单查询
        self.to_order_list_checking_order()

        # 点击已发货
        self.click('#rc-tabs-1-tab-3')
        sleep(1)
        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")

        sleep(1)
        # 点击追加包裹
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-btn.ant-btn-link')
        texts = [element.text for element in elements]
        elements[texts.index('追加包裹')].click()

        # 点击快递单号输入框
        self.click('#expressNo')
        # 快递单号输入框输入内容
        self.add_text('#expressNo', '自动化测试')
        assert self.get_text('#expressNo')=='自动化测试', "快递单号号输入框不可用"
        sleep(1)
        # 点击物流公司输入框
        self.click('.ant-select.ant-select-single.ant-select-show-arrow.ant-select-show-search')
        # 物流公司选择
        self.click('.ant-select-item.ant-select-item-option.ant-select-item-option-grouped.ant-select-item-option-active')

        # 取消按钮
        assert self.is_element_clickable('.ant-modal-footer > button.ant-btn'), '取消按钮不存在\n' + self.get_text(
            '.ant-modal-footer > button.ant-btn')
        # 发货按钮
        assert self.is_element_clickable(
            '.ant-modal-footer > button.ant-btn.ant-btn-primary'), '确认按钮不存在\n' + self.get_text(
            '.ant-modal-footer > button.ant-btn.ant-btn-primary')

    # 物流详情页
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_logistics_details(self):
        # 登陆
        # 点击订单查询
        self.to_order_list_checking_order2("logistics_account")

        # 点击已发货
        self.click('#rc-tabs-1-tab-3')
        sleep(1)
        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        # assert self.is_element_clickable(".ant-btn.ant-btn-primary.ant-btn-background-ghost"), "「查询」按钮不可点击"
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")

        sleep(1)
        self.wait_for_element(".V4KK1qdmk9hYesxBjBtk > .ant-btn.ant-btn-link")
        # 点击订单详情
        self.click(".V4KK1qdmk9hYesxBjBtk > .ant-btn.ant-btn-link")
        sleep(3)
        self.wait_for_element(".ant-steps-item-container")
        # 检查有没有物流信息
        assert self.is_element_visible("//div[@class='zpD691Rhqm7w8ZwdeBG_']"), '物流信息不存在\n'+self.get_text("//div[@class='zpD691Rhqm7w8ZwdeBG_']")
        assert len(self.get_text('.ant-steps-item-container')) > 0, "物流信息列表内容为空"

    # 客服物流详情
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_customer_service_logistics_details(self):
        # 登陆
        # 点击订单查询
        self.to_order_list_checking_order2("logistics_account")

        oid = oid_list[1]
        self.wait_for_element("//input[@placeholder='请输入正确的收货人姓名/手机号']")
        assert self.is_element_visible("//input[@placeholder='请输入正确的收货人姓名/手机号']"), "收货人姓名输入框不存在"

        self.input("//input[@placeholder='请输入正确的收货人姓名/手机号']", "金名")
        assert self.get_text("//input[@placeholder='请输入正确的收货人姓名/手机号']") == "金名", '输入订单号异常'
        sleep(1)
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)

        oid = oid_list[1]

        # # 点击已发货
        # self.click('#rc-tabs-1-tab-3')
        # sleep(1)
        # # 筛选无售后状态单
        # elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        # texts = [element.text for element in elements]
        # elements[2].click()
        # sleep(1)
        # # 选择无售后
        # elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        # texts = [element.text for element in elements]
        # elements[texts.index('无售后')].click()
        # # 点击查询
        # # assert self.is_element_clickable(".ant-btn.ant-btn-primary.ant-btn-background-ghost"), "「查询」按钮不可点击"
        # self.click("//button[@type='submit']//span[contains(text(),'查询')]")

        # 客服页
        self.wait_for_element("//div[@class='kpro-cs-magic-mods-link-button-nickname']")
        self.click("//div[@class='kpro-cs-magic-mods-link-button-nickname']")
        # self.switch_to_window(0)
        self.close_confirm()
        self.wait_for_element("//div[contains(@class,'tabItem-order')]//div[1]")
        self.click("//div[contains(@class,'tabItem-order')]//div[1]")
        self.wait_for_element("//span[@class='anticon anticon-system-search-line searchIcon']//*[name()='svg']")
        self.click("//span[@class='anticon anticon-system-search-line searchIcon']//*[name()='svg']")

        self.wait_for_element("//span[@title='商品名称']")
        self.click("//span[@title='商品名称']")
        self.wait_for_element("//div[@title='订单编号']//div[@class='ant-select-item-option-content'][contains(text(),'订单编号')]")
        self.click("//div[@title='订单编号']//div[@class='ant-select-item-option-content'][contains(text(),'订单编号')]")

        self.input("//input[@placeholder='请输入要搜索的关键词']", oid)
        sleep(1)
        self.wait_for_element("//div[normalize-space()='2433302560059528']")

        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-collapse-item.ant-collapse-item-active')
        texts = [element.text for element in elements]
        assert '收货信息' in texts[0], "收货信息不存在"
        assert '发货时间' in texts[0], "发货时间不存在"
        assert '物流信息' in texts[0], "物流信息不存在"

        # 查看
        self.wait_for_element("//div[@class='consigneePackageListContain']//span//span[1]")
        if self.is_element_visible("//span[contains(text(),'全部物流信息')]"):
            self.click("//span[contains(text(),'全部物流信息')]")
        else:
            self.click("//div[@class='list']//div[1]//div[2]//div[4]//div[1]//div[2]//div[1]//div[1]//div[6]//div[1]//div[1]//div[1]//span[1]//span[1]")
        sleep(3)
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-spin-container')
        texts = [element.text for element in elements]
        assert '物流公司' in texts[2], "物流公司不存在"
        assert '物流单号' in texts[2], "物流单号不存在"
        assert '官方电话' in texts[2], "官方电话不存在"
        assert '发货时间' in texts[2], "发货时间不存在"
        assert '商品信息' in texts[2], "商品信息不存在"
        assert '物流信息' in texts[2], "物流信息不存在"
        assert '已发货' in texts[2], "已发货不存在"
        assert '包裹正在等待揽收' in texts[2], "物流信息不存在"

    # 订列物流详情抽屉页-三段物流
    @pytest.mark.p0
    @pytest.mark.skip
    def test_logistics_detail_three_stage_logistics(self):
        # 登陆
        """订单查询页"""
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.assert_title("快手小店")
        # self.sleep(2)
        self.open_order_list_checking_order()
        self.close_confirm()

        time = 0
        # 关闭 您有多大程度满意我们的产品弹窗
        while self.is_element_clickable(".anticon.anticon-system-close-medium-line") and time < 10:
            self.click(".anticon.anticon-system-close-medium-line")
            logger.debug("关闭 您有多大程度满意我们的产品 弹窗")
            sleep(1)
            time += 1

        assert self.is_element_visible("#valueMixed"), "订单编号输入框不存在"
        self.input("#valueMixed", oid_list[0])
        assert self.get_text("#valueMixed") == oid_list[0], '输入订单号异常'
        sleep(1)

        time = 0
        # 关闭 您有多大程度满意我们的产品弹窗
        while self.is_element_clickable(".anticon.anticon-system-close-medium-line") and time < 10:
            self.click(".anticon.anticon-system-close-medium-line")
            logger.debug("关闭 您有多大程度满意我们的产品 弹窗")
            sleep(1)
            time += 1

        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        self.click("span[class='P4hzH6TNQmnQW_OLwqLM']")
        sleep(1)

        assert self.get_text("//div[@class='tPIFpX3Vvfcw5ooa9uiW F1uDviY0GZVQmai0emH5']") == "退回\n｜\n中转仓\n商家", '三段tab展示异常'
        assert self.get_text("//section[@class='ZxkwC6jfWusbHiL65zNC']//div[contains(text(),'顺丰速运')]") == "顺丰速运", '物流公司异常'
        # assert self.get_text("//div[normalize-space()='SF3149175157393']") == "SF3149175157393", '物流单号异常'
        assert self.get_text("//section[contains(text(),'退回地址')]") == "退回地址", '退回地址标题异常'
        assert self.get_text("//div[contains(text(),'浙江省杭州市余杭区良渚街道良渚街道良渚文化公园2')]") == "浙江省杭州市余杭区良渚街道良渚街道良渚文化公园2", '退回地址异常'
        elements = self.driver.find_elements(By.CLASS_NAME, 'VFsahM2_JlxBAWVcSxv4')
        texts = [element.text for element in elements]
        # assert '已退签' in texts, "已退签不存在"
        assert '退回中' in texts, "退回中不存在"

        # 进入商家-》中转仓tab
        self.click("//div[@class='tPIFpX3Vvfcw5ooa9uiW']")
        assert self.get_text("//section[@class='ZxkwC6jfWusbHiL65zNC']//div[contains(text(),'圆通速递')]") == "圆通速递", '物流公司异常'
        # assert self.get_text("//div[normalize-space()='YT3802630751019']") == "YT3802630751019", '物流单号异常'
        elements_one = self.driver.find_elements(By.CLASS_NAME, 'VFsahM2_JlxBAWVcSxv4')
        texts_one = [element.text for element in elements_one]
        assert '已入中转仓' in texts_one, "已入中转仓不存在"

    # 订列物流详情抽屉页-三段物流
    @pytest.mark.p0
    @pytest.mark.skip
    def test_order_detail_into_logistics_detail_three_stage_logistics(self):
        # 登陆
        """订单查询页"""
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.assert_title("快手小店")
        # self.sleep(2)
        self.open_order_list_checking_order()
        self.close_confirm()

        self.wait_for_element("#valueMixed")
        assert self.is_element_visible("#valueMixed"), "订单编号输入框不存在"
        self.input("#valueMixed", oid_list[0])
        assert self.get_text("#valueMixed") == oid_list[0], '输入订单号异常'
        sleep(1)
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        self.wait_for_element(".V4KK1qdmk9hYesxBjBtk > button.ant-btn.ant-btn-link")
        # 点击订单详情
        assert self.is_element_clickable(
            '.V4KK1qdmk9hYesxBjBtk > button.ant-btn.ant-btn-link'), '订单详情按钮不存在\n' + self.get_text(
            '.V4KK1qdmk9hYesxBjBtk > button.ant-btn.ant-btn-lin')
        self.click('.V4KK1qdmk9hYesxBjBtk > button.ant-btn.ant-btn-link')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])

        # self.wait_for_element(".zpD691Rhqm7w8ZwdeBG_")
        time=0
        while self.is_element_visible("//div[@class='tPIFpX3Vvfcw5ooa9uiW F1uDviY0GZVQmai0emH5']") == False and time < 10:
            logger.info("页面未加载完成")
            sleep(2)
            time += 1
        assert self.get_text(
            "//div[@class='tPIFpX3Vvfcw5ooa9uiW F1uDviY0GZVQmai0emH5']") == "退回\n｜\n中转仓\n商家", '三段tab展示异常'
        assert self.get_text(
            "//section[@class='ZxkwC6jfWusbHiL65zNC']//div[contains(text(),'顺丰速运')]") == "顺丰速运", '物流公司异常'
        # assert self.get_text("//div[normalize-space()='SF3101027442595']") == "SF3101027442595", '物流单号异常'
        assert self.get_text("//section[contains(text(),'退回地址')]") == "退回地址", '退回地址标题异常'
        assert self.get_text(
            "//div[contains(text(),'浙江省杭州市余杭区良渚街道良渚街道良渚文化公园2')]") == "浙江省杭州市余杭区良渚街道良渚街道良渚文化公园2", '退回地址异常'
        elements = self.driver.find_elements(By.CLASS_NAME, 'VFsahM2_JlxBAWVcSxv4')
        texts = [element.text for element in elements]
        # assert '已退签' in texts, "已退签不存在"
        assert '退回中' in texts, "退回中不存在"

        # 进入商家-》中转仓tab
        self.click("//div[@class='tPIFpX3Vvfcw5ooa9uiW']")
        assert self.get_text(
            "//section[@class='ZxkwC6jfWusbHiL65zNC']//div[contains(text(),'圆通速递')]") == "圆通速递", '物流公司异常'
        # assert self.get_text("//div[normalize-space()='75933536850236']") == "75933536850236", '物流单号异常'
        elements_one = self.driver.find_elements(By.CLASS_NAME, 'VFsahM2_JlxBAWVcSxv4')
        texts_one = [element.text for element in elements_one]
        assert '已入中转仓' in texts_one, "已入中转仓不存在"