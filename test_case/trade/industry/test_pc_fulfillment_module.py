import logging
import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from page_objects.trade_order.trade_order_page import TradeOrderPage
from selenium.webdriver.common.by import By
from datetime import datetime


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)

# 订单列表页
class TestPcOrderListPage(BaseTestCase):
    # 订单列表-代发货状态-「发货」按钮
    @pytest.mark.p0
    def test_order_list_delivery_button(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-btn.ant-btn-primary')
        texts = [element.text for element in elements]
        assert '发货' in texts, "发货不存在"

    # 订单列表 - 已发货 / 已收货状态 -「修改物流」按钮
    @pytest.mark.p1
    def test_order_list_update_logistics_button(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_clickable("div[id='rc-tabs-1-tab-3']"), "已发货按钮不可点击"
        self.click("div[id='rc-tabs-1-tab-3']")
        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")

        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-btn.ant-btn-link')
        texts = [element.text for element in elements]
        assert '修改物流' in texts, "修改物流不存在"

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-4']"), "已收货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-4']")
        sleep(1)
        # 存在订单再断言
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-pro-list-row-header-content')
        if len(elements) > 0:
            elements = self.driver.find_elements(By.CLASS_NAME, 'ant-btn.ant-btn-link')
            texts = [element.text for element in elements]
            assert '修改物流' in texts, "修改物流不存在"

    # 订单列表 - 已发货状态 -「追加包裹」按钮
    @pytest.mark.p1
    def test_order_list_add_package_button(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-3']"), "已发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-3']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-btn.ant-btn-link')
        texts = [element.text for element in elements]
        assert '追加包裹' in texts, "追加包裹按钮不存在"

    # # 批量发货弹窗 - 页面展示内容、「发货结果列表」内容
    # @pytest.mark.p1
    # def test_batch_delivery_pop_up(self):
    #     '''
    #     没有这个弹窗了 统一收口到发货中心 下方更新一个批量发货跳转发货中心的case
    #     '''
    #     self.to_order_list_checking_order()
    #
    #     text = '导入发货文件完成后，可在发货结果列表查看进度和下载发货结果。(发货模板已更新，请重新下载！)'
    #     assert self.is_element_clickable("#import-excel-driver-share"), "「批量发货」按钮不可点击"
    #     self.click("#import-excel-driver-share")
    #     sleep(1)
    #     get_text=self.get_text('.ant-alert-content > div.ant-alert-message')
    #     assert get_text==text, '顶部文案不一致\n'+get_text
    #     get_text_list=self.get_text('.PmVcaP7GGFIOfzXX_Fnx')
    #     assert len(get_text_list)>=80, '「发货结果列表」列表内容展示异常\n'+get_text_list

    # 批量发货跳转发货中心
    @pytest.mark.p1
    def test_batch_delivery_jumpurl(self):
        self.to_order_list_checking_order()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        # text = '导入发货文件完成后，可在发货结果列表查看进度和下载发货结果。(发货模板已更新，请重新下载！)'
        assert self.is_element_clickable("//span[contains(text(),'批量发货')]"), "「批量发货」按钮不可点击"
        self.click("//span[contains(text(),'批量发货')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/trade-shipping/shipping-center/home?tab=BatchShipping&source=orderList')

# 发货抽屉页
class TestPcShippingDrawerPage(BaseTestCase):
    # 发货抽屉页「整单发货」tab
    @pytest.mark.p0
    def test_shipping_drawer_page_ship_whole_order_tab(self):
        self.to_order_list_checking_order()

        sleep(1)
        self.wait_for_element("#rc-tabs-1-tab-2")
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)

        # 筛选无售后状态单
        self.wait_for_element(".ant-select-selector")
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        self.wait_for_element(".ant-select-item-option-content")
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        self.wait_for_element("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        # 打开抽屉页
        self.click("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        # self.is_element_visible('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input')
        # 判断整单发货按钮是否存在
        # self.assertEquals(self.is_element_clickable('div.kpro-delivery-goods--title'), True)
        self.wait_for_element("//span[contains(text(),'确认')]")
        elements = self.driver.find_elements(By.CLASS_NAME, 'kpro-delivery-goods--title')
        texts = [element.text for element in elements]
        assert '整单发货' in texts, "整单发货不存在"

        # button_text = self.get_text('/html/body/div[3]/div/div[2]/div/div/div[2]/div/div/div[5]/div[2]/div/div[1]/label/span[2]/div[1]')
        # assert button_text == "整单发货", '整单发货按钮文字对不上'

        # 解密小眼睛
        elements = self.driver.find_elements(By.CSS_SELECTOR, ".kpro-delivery-goods-decrypt--infoLine")
        texts = [element.text for element in elements]
        assert '*****' in texts[0], "地址未加密"
        # 点击解密小眼睛
        self.click("//span[@aria-label='system-previewcloseone-line']//*[name()='svg']")
        sleep(1)
        elements = self.driver.find_elements(By.CSS_SELECTOR, ".kpro-delivery-goods-decrypt--infoLine")
        texts = [element.text for element in elements]
        assert '*****' not in texts[0], "地址未解密"
        # 点击加密
        self.click(".anticon.anticon-system-previewopen-line.kpro-delivery-goods-decrypt--icon")
        sleep(1)
        elements = self.driver.find_elements(By.CSS_SELECTOR, ".kpro-delivery-goods-decrypt--infoLine")
        texts = [element.text for element in elements]
        assert '*****' in texts[0], "地址未加密"

        # 判断关闭按钮是否存在
        # assert self.is_element_visible("span[class='anticon anticon-SystemCloseSmallLine']"), "关闭按钮不存在"

    # 发货抽屉页「拆单发货」、「关联商品」选择框&输入框、「添加包裹」按钮、「添加关联商品」按钮、「删除关联商品」按钮、「删除包裹」按钮
    @pytest.mark.p0
    def test_shipping_drawer_page_ship_Split_order_tab(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)

        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        # 打开抽屉页
        self.click("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)

        elements = self.driver.find_elements(By.CLASS_NAME, 'kpro-delivery-goods--title')
        texts = [element.text for element in elements]
        assert '拆单发货' in texts, "拆单发货不存在"

        # button_text = self.get_text(
        # '/html/body/div[3]/div/div[2]/div/div/div[2]/div/div/div[5]/div[2]/div/div[2]/label/span[2]/div[1]')
        # assert button_text == "拆单发货", '拆单发货按钮文字对不上'

        self.click("span[class='ant-radio']")
        text = self.get_text(
        '//*[@id="deliveryForm"]/div[1]/div[3]/div[1]/div/div[1]/div/div[1]/label')
        assert text == "关联商品", '关联商品文字对不上'
        # 判断订单选择框是否存在
        assert self.is_element_visible("div[class='ant-select ant-select-single ant-select-show-arrow']"), "订单选择框不存在"
        # 判断商品选择框是否存在
        assert self.is_element_visible('//*[@id="deliveryForm"]/div[1]/div[3]/div[1]/div/div[2]/div/div/div/div'), "商品选择框不存在"

        # 判断添加关联商品按钮是否存在
        assert self.is_element_visible(
        "div[class='kpro-delivery-goods--addGoodsBtn']"), "添加关联商品按钮不存在"
        self.click("div[class='kpro-delivery-goods--addGoodsBtn']")

        # 判断删除关联商品按钮是否存在
        assert self.is_element_clickable("//div[@class='ant-spin-container']//div[1]//div[3]//div[1]//div[1]//div[3]//div[1]//div[1]//div[1]//div[1]//span[1]//*[name()='svg']"), "删除关联商品按钮不可点击"

        # 判断添加包裹按钮是否存在
        assert self.is_element_visible(
        "button[style='margin-top: 4px;']"), "添加包裹按钮不存在"

        # 判断删除包裹按钮是否存在
        assert self.is_element_clickable(
            "//body/div/div[@class='ant-drawer ant-drawer-right ant-drawer-open kpro-delivery-goods--deliveryDrawer']/div[@class='ant-drawer-content-wrapper']/div[@class='ant-drawer-content']/div[@class='ant-drawer-wrapper-body']/div[@class='ant-drawer-body']/div[@class='ant-spin-nested-loading']/div[@class='ant-spin-container']/form[@id='deliveryForm']/div[1]/div[1]/span[1]//*[name()='svg']//*[name()='path' and contains(@d,'M7.05 16.9')]"), "删除包裹按钮不可点击"

    # 发货抽屉页「物流单号」输入框
    @pytest.mark.p0
    def test_shipping_drawer_page_logistics_number_input_box(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)
        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        # 打开抽屉页
        self.click("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        # 判断输入框是否存在
        assert self.is_element_visible('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input'), "输入框不存在"
        self.add_text('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input', "测试填写")
        res = self.get_text('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input')
        assert res == "测试填写", '输入框输入错误'

    # 发货抽屉页「物流公司」选择框
    @pytest.mark.p0
    def test_shipping_drawer_page_logistics_company_selection_box(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)

        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        # 打开抽屉页
        self.click("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)

        # 判断选择框是否存在
        assert self.is_element_visible('//*[@id="express-from"]/div/div[2]/div/div[2]'), "选择框不存在"
        res = self.get_text('//*[@id="express-from"]/div/div[2]/div/div[1]/label')
        assert res == "物流公司", '物流公司文字错误'


    # 发货抽屉页「取消」按钮
    @pytest.mark.p0
    def test_shipping_drawer_page_cancel_button(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)

        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        # 打开抽屉页
        self.click("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        # 判断取消按钮是否存在
        assert self.is_element_clickable("button[class='ant-btn']"), "取消按钮不可点击"

        # button_text = self.get_text('/html/body/div[2]/div/div[2]/div/div/div[3]/div/button[1]/span')
        # assert button_text == "取消", '取消按钮文字对不上'

    # 发货抽屉页「确认」按钮
    @pytest.mark.p0
    def test_shipping_drawer_page_confirm_button(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)
        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        # 打开抽屉页
        self.click("//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        # 判断确认按钮是否存在
        assert self.is_element_clickable(
            "button[class='ant-btn ant-btn-primary kpro-delivery-goods--confirmBtn']"), "确认按钮不可点击"
        #
        # button_text = self.get_text('/html/body/div[2]/div/div[2]/div/div/div[3]/div/button[2]/span')
        # assert button_text == "确认", '确认按钮文字对不上'

    # 发货抽屉页 订单号「复制」按钮
    @pytest.mark.p0
    def test_shipping_drawer_page_copy_button(self):
        self.to_order_list_checking_order()

        sleep(1)
        assert self.is_element_visible("div[id='rc-tabs-1-tab-2']"), "待发货按钮不存在"
        self.click("div[id='rc-tabs-1-tab-2']")
        sleep(1)

        # 筛选无售后状态单
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-selector')
        texts = [element.text for element in elements]
        elements[2].click()
        sleep(1)
        # 选择无售后
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item-option-content')
        texts = [element.text for element in elements]
        elements[texts.index('无售后')].click()
        # 点击查询
        self.click("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(1)
        # 打开抽屉页
        self.click(
            "//div[@class='ant-spin-container']//div//div[1]//div[2]//div[6]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        # 判断复制按钮是否存在
        # assert self.is_element_clickable("svg[data-icon='NormalCopyLine']"), "复制按钮不可点击"

# 协商发货页
class TestPcNegotiateDeliveryPage(BaseTestCase):
    # 顶部文案
    @pytest.mark.p1
    @pytest.mark.skip
    def test_negotiat_delivery_page_top_text(self):
        self.to_negotiate_delivery()
        sleep(1)
        # 判断顶部公告文案是否存在
        assert self.is_element_visible('.ant-alert-message'), "顶部公告文案不可见"
        res = self.get_text('.ant-alert-message')
        assert res=='亲爱的商家，「商户与买家自主协商发货时间」功能上线啦，与买家协商一致后可按照与买家约定时间发货及考核。如遇缺货无货情况，请不要进行发货，及时与买家进行协商。', '顶部公告文案异常或被修改'


    # 「协商发货操作指导」区域 - 所有可点击区域 - 超链接
    @pytest.mark.p1
    @pytest.mark.skip
    def test_negotiat_delivery_page_operation_guide(self):
        self.to_negotiate_delivery()

        # 商家售后工具弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭商家售后工具弹窗")

        # 「收起」
        assert self.is_element_clickable(".J7gIVyx6WDzCy3UtJeB7 > button.ant-btn.ant-btn-link"), "「收起」按钮不可点击"

        elements = self.driver.find_elements(By.CLASS_NAME, 'EGv2UcN59rANgRHL38lX')
        texts = [element.text for element in elements]
        for i in range(len(elements)):
            is_displayed=elements[i].is_displayed()
            is_enabled = elements[i].is_enabled()
            if (is_displayed and is_enabled)==False:
                assert False, texts[i] + '按钮不可点击'
            # print(texts[i]+'正常')



    # 协商流水记录 -「查询」、「重置」按钮、「批量发送协商发货卡片」按钮、「协商流水记录」列表内容
    @pytest.mark.p1
    @pytest.mark.skip
    def test_negotiat_delivery_page_query_reset_button(self):
        self.to_negotiate_delivery()
        # 「查询」、「重置」按钮
        assert self.is_element_clickable(".ant-space-item > button.ant-btn"), "「重置」按钮不可点击"
        assert self.is_element_clickable(".ant-btn.ant-btn-primary.ant-btn-background-ghost"), "「查询」按钮不可点击"

        # 商家售后工具弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭直播间讲解回放弹窗")
        # 无法发货赔付工具弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭无法发货赔付工具弹窗")
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if (int(formatted_time[11:13]) >= 23 or int(formatted_time[11:13]) < 8):
            assert self.is_element_clickable("//span[contains(text(),'批量发送协商发货卡片')]"), "「批量发送协商发货卡片」按钮不可点击"
            self.hover("//span[contains(text(),'批量发送协商发货卡片')]")
            assert self.get_text("//div[contains(text(),'每日08:00~23:00可操作')]")=='每日08:00~23:00可操作', "「批量发送协商发货卡片」按钮夜间异常"
        else:
            assert self.is_element_clickable("//span[contains(text(),'批量发送协商发货卡片')]"), "「批量发送协商发货卡片」按钮不可点击"
        # 「协商流水记录」列表内容
        assert len(self.get_text('.ant-table-tbody')) > 0, "「协商流水记录」列表内容为空"

    # 批量发送协商发货卡片页 - 顶部文案、「文件处理结果」列表内容
    @pytest.mark.p1
    @pytest.mark.skip
    def test_batch_send_negotiat_delivery_page(self):
        self.to_negotiate_delivery()
        # 商家售后工具弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭商家售后工具弹窗")
        # 无法发货赔付工具弹窗
        if self.is_element_visible("//button[text()='知道了']"):
            self.click("//button[text()='知道了']")
            self.sleep(2)
            logger.debug("关闭无法发货赔付工具弹窗")
        text = '为减少对用户的打扰以及提升您的协商成功率，针对高价值用户或单量较少场景建议您先联系消费者协商，协商一致后，再通过订单列表与买家发起会话并发送协商发货卡片'
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if (int(formatted_time[11:13]) >= 23 or int(formatted_time[11:13]) < 8):
            assert self.is_element_clickable("//span[contains(text(),'批量发送协商发货卡片')]"), "「批量发送协商发货卡片」按钮不可点击"
            self.hover("//span[contains(text(),'批量发送协商发货卡片')]")
            assert self.get_text("//div[contains(text(),'每日08:00~23:00可操作')]") == '每日08:00~23:00可操作', "「批量发送协商发货卡片」按钮夜间异常"
        else:
            assert self.is_element_clickable(
                "//span[contains(text(),'批量发送协商发货卡片')]"), "「批量发送协商发货卡片」按钮不可点击"
            self.click("//span[contains(text(),'批量发送协商发货卡片')]")
            sleep(1)
            get_text=self.get_text('.ant-alert.ant-alert-info.ant-alert-closable > div.ant-alert-content')
            assert get_text==text, '顶部文案不一致\n'+get_text
            assert len(self.get_text('.n1nKszXrs24Nqdm1qNuw'))>=95, '「文件处理结果」列表内容展示异常\n'+self.get_text('.n1nKszXrs24Nqdm1qNuw')

    # 「导出协商记录」按钮、「查看导出报表」按钮
    @pytest.mark.p1
    @pytest.mark.skip
    def test_export_negotiation_records_view_export_report(self):
        self.to_negotiate_delivery()
        assert self.is_element_clickable("//span[contains(text(),'导出协商记录')]"), "「导出协商记录」按钮不可点击"
        self.click("//span[contains(text(),'导出协商记录')]")
        assert self.get_text("//span[@class='ant-modal-confirm-title']")=='确认导出当前查询的协商记录？', "导出协商记录弹窗标题不正确"
        assert self.get_text("//div[@class='ant-modal-confirm-content']//div[1]") == '1. 为了保证查询性能，两次导出的时间间隔请保持在5分钟以上\n2. 生成报表后，7天内可以下载\n3. 每次最多导出100万条记录，若超出数量限制，则只导出最近的100万条\n4. 仅支持导出协商结果为「用户已拒绝」「超时未处理协商自动失败」的记录', "导出协商记录弹窗明细展示不正确"

        assert self.is_element_clickable("//div[@class='ant-modal-root']//button[1]"), "「取消」按钮不可点击"
        assert self.is_element_clickable("//span[contains(text(),'导 出')]"), "「导出」按钮不可点击"
        # 点击取消
        self.click("//div[@class='ant-modal-root']//button[1]")
        assert self.is_element_clickable("//span[contains(text(),'查看导出报表')]"), "「查看导出报表」按钮不可点击"
        self.click("//span[contains(text(),'查看导出报表')]")
        assert self.get_text("//div[@class='VxxQeRCv5GIQAqPtxUTu']")=='协商记录导出报表\n只支持下载最近7天的报表', "导出报表标题展示不正确"
        assert self.is_element_clickable("//span[@aria-label='system-refresh-line']//*[name()='svg']"), "「刷新」按钮不可点击"
        assert self.is_element_clickable("//*[name()='path' and contains(@d,'M216.32 80')]"), "「x」关闭按钮不可点击"


# 报备中心页
# @pytest.mark.skip
class TestPcDelayDeliveryReportPage(BaseTestCase):
    # 订列无法发货解决方案入口
    @pytest.mark.p0
    def test_order_list_entrance_button(self):
        self.to_order_list_checking_order()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'

        # sleep(1)
        # 「无法发货解决方案」按钮
        self.click("//div[@id='negotiationCenter']//div[1]")
        self.wait_for_element("//span[@class='modalTitle___Z41e7']")

        self.assert_text("请选择无法发货原因", "//span[@class='modalTitle___Z41e7']")
        self.assert_text("店铺遇到消费者要求延迟发货、自然灾害、会议赛事等非商家责任问题，导致无法正常发货", "//body//div//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-body']//div//div[1]//div[1]")
        self.assert_text("店铺遇到商品缺货、价格设错等商家责任问题，导致无法正常发货", "//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-body']//div[2]//div[1]")
        self.assert_text("若发生大面积无法发货问题，如台风或大型赛事，可以向平台反馈，平台采纳后会统一配置平台免考核", "//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-root']//div[3]//div[1]")
        self.assert_text("了解更多：无法发货场景解决指南", "//span[contains(text(),'了解更多：')]")

        # 去报备按钮
        self.click("//span[contains(text(),'去报备')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/fulfill/delayship-report/list')
        self.wait_for_element("//div[@id='rc-tabs-0-tab-delayReport']")
        self.assert_text("报备中心", "//div[@id='rc-tabs-0-tab-delayReport']")
        self.driver.close()
        self.switch_to_window(-1)
        # 去协商按钮
        self.click("//span[contains(text(),'去协商')]")
        self.wait_for_element("//div[@class='kwaishop-trade-fulfill-negotiate-pc-drawer-title']")
        self.assert_text("发货协商", "//div[@class='kwaishop-trade-fulfill-negotiate-pc-drawer-title']")
        self.click("//span[contains(text(),'取 消')]")
        self.wait_for_element("//div[@id='negotiationCenter']//div[1]")
        # 「无法发货解决方案」按钮
        self.click("//div[@id='negotiationCenter']//div[1]")
        self.wait_for_element("//span[@class='modalTitle___Z41e7']")
        # 去反馈按钮
        self.click("//span[contains(text(),'去反馈')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list?drawerType=feedbackPage" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/fulfill/delayship-report/list?drawerType=feedbackPage')
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("不可抗力反馈", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.driver.close()
        self.switch_to_window(-1)

        # 解决指南按钮
        self.click("//a[contains(text(),'无法发货场景解决指南')]")
        self.switch_to_window(-1)
        assert "d/home/<USER>" in self.get_current_url(), "跳转错误"
        self.driver.close()
        self.switch_to_window(-1)

    # 打单发货无法发货解决方案入口
    @pytest.mark.p0
    def test_dadan_delivery_entrance_button(self):
        self.to_order_list_checking_order()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'

        # 「打单发货」按钮
        if self.is_element_visible("//span[@id='menu_item_a-oQ1Q74vlU']"):
            self.click("//span[@id='menu_item_a-oQ1Q74vlU']")
        else:
            self.click("//button[@id='printWaybill']//span[contains(text(),'打单发货')]")
        self.wait_for_element("//span[@aria-label='share-commoditymanagement-line']//*[name()='svg']")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")
            logger.debug("关闭 您有多大程度满意我们的产品 弹窗")

        # sleep(1)
        # 「无法发货解决方案」按钮
        self.click("//span[contains(text(),'无法发货解决方案')]")
        self.wait_for_element("//span[@class='modalTitle___Z41e7']")

        self.assert_text("请选择无法发货原因", "//span[@class='modalTitle___Z41e7']")
        self.assert_text("店铺遇到消费者要求延迟发货、自然灾害、会议赛事等非商家责任问题，导致无法正常发货",
                         "//body//div//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-body']//div//div[1]//div[1]")
        self.assert_text("店铺遇到商品缺货、价格设错等商家责任问题，导致无法正常发货",
                         "//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-body']//div[2]//div[1]")
        self.assert_text("若发生大面积无法发货问题，如台风或大型赛事，可以向平台反馈，平台采纳后会统一配置平台免考核",
                         "//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-root']//div[3]//div[1]")
        self.assert_text("了解更多：无法发货场景解决指南", "//span[contains(text(),'了解更多：')]")

        # 去报备按钮
        self.click("//span[contains(text(),'去报备')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/fulfill/delayship-report/list')
        self.wait_for_element("//div[@id='rc-tabs-0-tab-delayReport']")
        self.assert_text("报备中心", "//div[@id='rc-tabs-0-tab-delayReport']")
        self.driver.close()
        self.switch_to_window(-1)
        # 去协商按钮
        self.click("//span[contains(text(),'去协商')]")
        self.wait_for_element("//div[@class='kwaishop-trade-fulfill-negotiate-pc-drawer-title']")
        self.assert_text("发货协商", "//div[@class='kwaishop-trade-fulfill-negotiate-pc-drawer-title']")
        self.click("//span[contains(text(),'取 消')]")
        self.wait_for_element("//span[@aria-label='share-commoditymanagement-line']//*[name()='svg']")

        # 「无法发货解决方案」按钮
        self.click("//span[contains(text(),'无法发货解决方案')]")
        self.wait_for_element("//span[@class='modalTitle___Z41e7']")
        # 去反馈按钮
        self.click("//span[contains(text(),'去反馈')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list?drawerType=feedbackPage" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/fulfill/delayship-report/list?drawerType=feedbackPage')
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("不可抗力反馈", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.driver.close()
        self.switch_to_window(-1)

        # 解决指南按钮
        self.click("//a[contains(text(),'无法发货场景解决指南')]")
        self.switch_to_window(-1)
        assert "d/home/<USER>" in self.get_current_url(), "跳转错误"
        self.driver.close()
        self.switch_to_window(-1)

    # 在线寄件无法发货解决方案入口
    @pytest.mark.p0
    def test_online_delivery_entrance_button(self):
        self.to_order_list_checking_order()
        # sleep(1)
        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'

        # 「在线寄件」按钮
        if self.is_element_visible("//span[@id='menu_item_LQkIHkAi5T8']"): # 发货中心按钮
            self.click("//span[@id='menu_item_LQkIHkAi5T8']")
            self.close_confirm()
            self.click("//span[@class='kwaishop-trade-shipping-center-pc-badge badge___CYaYT'][contains(text(),'在线寄件')]")
        else:
            self.click("//button[@id='order-list-online-delivery']//span[contains(text(),'在线寄件')]")
        self.wait_for_element("//span[contains(text(),'无法发货解决方案')]")

        # 「无法发货解决方案」按钮
        self.click("//span[contains(text(),'无法发货解决方案')]")
        self.wait_for_element("//span[@class='modalTitle___Z41e7']")

        self.assert_text("请选择无法发货原因", "//span[@class='modalTitle___Z41e7']")
        self.assert_text("店铺遇到消费者要求延迟发货、自然灾害、会议赛事等非商家责任问题，导致无法正常发货",
                         "//body//div//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-body']//div//div[1]//div[1]")
        self.assert_text("店铺遇到商品缺货、价格设错等商家责任问题，导致无法正常发货",
                         "//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-body']//div[2]//div[1]")
        self.assert_text("若发生大面积无法发货问题，如台风或大型赛事，可以向平台反馈，平台采纳后会统一配置平台免考核",
                         "//div[@class='kwaishop-trade-fulfill-negotiate-pc-modal-root']//div[3]//div[1]")
        self.assert_text("了解更多：无法发货场景解决指南", "//span[contains(text(),'了解更多：')]")

        # 去报备按钮
        self.click("//span[contains(text(),'去报备')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/fulfill/delayship-report/list')
        self.wait_for_element("//div[@id='rc-tabs-0-tab-delayReport']")
        self.assert_text("报备中心", "//div[@id='rc-tabs-0-tab-delayReport']")
        self.driver.close()
        self.switch_to_window(-1)
        # 去协商按钮
        self.click("//span[contains(text(),'去协商')]")
        self.wait_for_element("//div[@class='kwaishop-trade-fulfill-negotiate-pc-drawer-title']")
        self.assert_text("发货协商", "//div[@class='kwaishop-trade-fulfill-negotiate-pc-drawer-title']")
        self.click("//span[contains(text(),'取 消')]")
        self.wait_for_element("//span[contains(text(),'无法发货解决方案')]")
        # 「无法发货解决方案」按钮
        self.click("//span[contains(text(),'无法发货解决方案')]")
        self.wait_for_element("//span[@class='modalTitle___Z41e7']")
        # 去反馈按钮
        self.click("//span[contains(text(),'去反馈')]")
        self.switch_to_window(-1)
        # assert "zone/fulfill/delayship-report/list?drawerType=feedbackPage" in self.get_current_url(), "跳转错误"
        self.assert_url(env + 'zone/fulfill/delayship-report/list?drawerType=feedbackPage')
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("不可抗力反馈", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.driver.close()
        self.switch_to_window(-1)

        # 解决指南按钮
        self.click("//a[contains(text(),'无法发货场景解决指南')]")
        self.switch_to_window(-1)
        assert "d/home/<USER>" in self.get_current_url(), "跳转错误"
        self.driver.close()
        self.switch_to_window(-1)

    # 报备中心-公告栏
    @pytest.mark.p0
    def test_report_center_bulletin_board(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'


        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-alert-message']//div//div[1]")
        self.assert_text("报备中心升级啦！平台整合升级了平台免考核、发货协商、延迟报备等工具，帮商家应对不可抗力及商家原因导致的发货物流异常问题。", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-alert-message']//div//div[1]")
        self.assert_text("1、若因商家原因发货困难（如爆单缺货），可使用「发货协商」工具和用户协商新发货时间或取消订单；", "//div[contains(text(),'1、若因商家原因发货困难（如爆单缺货），可使用「发货协商」工具和用户协商新发货时间或取消订单；')]")
        self.assert_text("2、若因不可抗力原因发货困难（如台风等自然灾害），平台主动为商家做了物流考核保护，商家可在「数据概览」模块查看平台免考核范围。若受影响订单不在公告范围内，可「发起报备」。若遇到异常下单（如收货地址异常）等情况，您可前往「举报中心」进行举报。点击查看操作指南", "//div[contains(text(),'2、若因不可抗力原因发货困难（如台风等自然灾害），平台主动为商家做了物流考核保护，商家可在「数据概览')]")

        # 点击查看操作指南按钮
        self.click("//a[contains(text(),'点击查看操作指南')]")
        self.switch_to_window(-1)
        # assert "https://university.kwaixiaodian.com/kwaishop/newKnowledge/588446489815830626/581287785622441994" == self.get_current_url(), "跳转错误"
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.driver.close()
        self.switch_to_window(-1)

        # 点击举报中心按钮
        self.click("//a[contains(text(),'「举报中心」')]")
        self.switch_to_window(-1)
        self.assert_url(env + 'zone/control/report')
        self.driver.close()
        self.switch_to_window(-1)

    # 报备中心-数据概览
    @pytest.mark.p0
    def test_report_center_data_overview(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")
        self.assert_text("查询已豁免订单", "//span[contains(text(),'查询已豁免订单')]")
        self.click("//span[contains(text(),'查询已豁免订单')]")

        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("查询已豁免订单", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("输入订单编号后可查询对应订单的发货报备记录以及免考核记录。仅支持查询近半年内的报备记录，每次最多支持查询20个订单，查询结果最多展示200条~", "//div[contains(text(),'输入订单编号后可查询对应订单的发货报备记录以及免考核记录。仅支持查询近半年内的报备记录，每次最多支持')]")

        self.is_element_clickable("//button[@class='kwaishop-trade-unable-delivery-solution-pc-btn kwaishop-trade-unable-delivery-solution-pc-btn-primary']//span[contains(text(),'查 询')]")
        self.click("//span[contains(text(),'知道了')]")

        self.assert_text("无法发货场景解决指南", "//span[contains(text(),'无法发货场景解决指南')]")
        self.click("//span[contains(text(),'无法发货场景解决指南')]")
        self.switch_to_window(-1)
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.driver.close()
        self.switch_to_window(-1)

    # 报备中心-数据概览-商家报备
    @pytest.mark.p0
    def test_report_center_data_overview_seller_report(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")
        # self.hover("//span[@class='anticon anticon-system-questionmark-circle-line exemptContent___nTvgv kwaishop-trade-unable-delivery-solution-pc-popover-open']//*[name()='svg']//*[name()='path' and contains(@d,'M12 20a8 8')]")

        elements = self.driver.find_elements(By.CSS_SELECTOR, '.tips___Edrpl')
        text_mod = ['消费者要求延迟发货、自然灾害等情况商家发起报备，平台审核通过后豁免', '指自然灾害、会议赛事等非商家责任的不可抗力问题导致无法发货的情况']
        texts = [element.text for element in elements]
        for i in range(len(texts)):
            assert texts[i] == text_mod[i], '文案错误'

        elements = self.driver.find_elements(By.CSS_SELECTOR, '.itemCard___HaqD4')
        text_mod = ['发起数', '通过数', '不通过数', '个免考核地区', '今日新增', '今日取消', '免考核数量']
        texts = [element.text for element in elements]
        for i in range(len(texts)):
            assert text_mod[i] in texts[i], '文案错误'

    # 报备中心-数据概览-平台免考核范围
    @pytest.mark.p0
    def test_report_center_data_overview_platform_assessment_exemption(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        # 点当前几个免考核地区
        self.click("//body//div[@id='main-root']//div[@class='card___YzqTQ']//div[@class='card___YzqTQ']//div[1]//div[1]//div[1]//p[2]")
        self.wait_for_element("//th[@class='kwaishop-trade-unable-delivery-solution-pc-table-cell kwaishop-trade-unable-delivery-solution-pc-table-cell-fix-left kwaishop-trade-unable-delivery-solution-pc-table-cell-fix-left-last']")
        self.assert_text("若您当前受影响的地区平台没有覆盖到，可以向平台进行反馈点击立即反馈。平台会基于实际影响情况新增免考核地区，若有调整平台会新增相关豁免公告", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer kwaishop-trade-unable-delivery-solution-pc-drawer-right kwaishop-trade-unable-delivery-solution-pc-drawer-open']//div[@class='kwaishop-trade-unable-delivery-solution-pc-alert-message']//div[1]")

        # 点击公告内立即反馈
        self.click("//a[contains(text(),'点击立即反馈')]")
        self.switch_to_window(-1)
        self.assert_url(env+"zone/fulfill/delayship-report/list?drawerType=feedbackPage")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("不可抗力反馈","//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.driver.close()
        self.switch_to_window(-1)

        elements = self.driver.find_elements(By.CSS_SELECTOR, '#pro-form-wrapper')
        text_mod = ['正在生效中免考核地区', '今日新增免考核地区', '3天内取消免考核地区', '今日取消免考核地区', '已失效免考核地区', '查询地区是否已发布免考核公告']
        texts = [element.text for element in elements]
        for i in range(len(text_mod)):
            assert text_mod[i] in texts[0], '文案错误'

        elements = self.driver.find_elements(By.CSS_SELECTOR, '.kwaishop-trade-unable-delivery-solution-pc-table-thead')
        text_mod = '豁免地区 免考核时间 时效延长公告 物流分豁免规则 发布时间\n待发货-订单维度豁免\n范围：承诺发货时间命中免考核时间\n已发货待揽收-包裹维度豁免\n范围：应揽收时间命中免考核时间\n已揽收-包裹维度豁免\n范围：流入/流出分拨中心时间命中免考核时间'
        texts = [element.text for element in elements]
        assert text_mod == texts[1], '文案错误'
        self.click("//span[contains(text(),'知道了')]")

        # 点今日新增几个免考核地区
        self.click("//div[@class='activeFiling___gAi6x']//div[2]//div[1]//div[1]//p[2]")
        self.wait_for_element("//th[@class='kwaishop-trade-unable-delivery-solution-pc-table-cell kwaishop-trade-unable-delivery-solution-pc-table-cell-fix-left kwaishop-trade-unable-delivery-solution-pc-table-cell-fix-left-last']")
        self.click("//span[contains(text(),'知道了')]")

        # 点今日取消几个免考核地区
        self.click("//div[@class='card___YzqTQ']//div[3]//div[1]//div[1]//p[2]")
        self.wait_for_element(
            "//th[@class='kwaishop-trade-unable-delivery-solution-pc-table-cell kwaishop-trade-unable-delivery-solution-pc-table-cell-fix-left kwaishop-trade-unable-delivery-solution-pc-table-cell-fix-left-last']")
        self.click("//span[contains(text(),'知道了')]")

        # 点免考核数量
        self.click("//p[contains(text(),'免考核数量')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.click("//button[@class='kwaishop-trade-unable-delivery-solution-pc-btn']//span[contains(text(),'重 置')]")
        self.click("//button[@class='kwaishop-trade-unable-delivery-solution-pc-btn kwaishop-trade-unable-delivery-solution-pc-btn-secondary']//span[contains(text(),'查 询')]")
        self.click("//span[contains(text(),'知道了')]")

        # 去反馈
        self.click("//span[contains(text(),'去反馈')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("不可抗力反馈", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")

    # 报备中心-数据概览-平台免考核范围-去反馈
    @pytest.mark.p0
    def test_report_center_data_overview_platform_assessment_exemption_feedback(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        # 去反馈
        self.click("//span[contains(text(),'去反馈')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("不可抗力反馈", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.assert_text("若您当前受影响的地区平台没有覆盖到，可以向平台进行反馈。平台会基于实际影响情况新增免考核地区，若有调整平台会新增相关豁免公告", "//div[contains(text(),'若您当前受影响的地区平台没有覆盖到，可以向平台进行反馈。平台会基于实际影响情况新增免考核地区，若有调')]")

        elements = self.driver.find_elements(By.CSS_SELECTOR, '.feedCount___V0Tdj')
        text_mod = '反馈信息\n不可抗力事件类型\n自然灾害\n会议赛事\n其他\n受影响地区\n请选择地区\n受影响时段\n补充说明\n相关证明上传\n上传图片 (0/3)'
        texts = [element.text for element in elements]
        assert text_mod == texts[0], '文案错误'
        self.is_element_clickable("//span[contains(text(),'取 消')]")
        self.click("//span[contains(text(),'提 交')]")

    # 报备中心-发起报备-查看报备场景说明
    @pytest.mark.p0
    def test_report_center_submit_report_watch_description(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        # 查看报备场景说明
        self.click("//span[contains(text(),'查看报备场景说明')]")
        self.wait_for_element("//div[@id='rcDialogTitle0']")
        self.assert_text("查看报备场景说明", "//div[@id='rcDialogTitle0']")
        self.assert_text("以下6个场景由于不满足平台规则或因商家自身原因无法发货，不支持自主报备","//div[contains(text(),'以下6个场景由于不满足平台规则或因商家自身原因无法发货，不支持自主报备')]")

        elements = self.driver.find_elements(By.CSS_SELECTOR, '.kwaishop-trade-unable-delivery-solution-pc-table-content')
        text_mod = '场景 不支持报备原因 操作\n订单状态为已延迟状态\n已经延迟发货状态：不支持发货报备，因为报备需在应发货时间前报备且延迟后已经对消费者产生体验损伤，故不支持报备\n已经延迟揽收状态：不支持揽收报备，因为报备需在应揽收时间前报备且延迟后已经对消费者产生体验损伤，故不支持报备\n-\n商家自身原因\n因人力不足、价格设置错误等商家自身问题导致无法履约的情况不支持报备。可使用【发货协商】工具和消费者协商，经双方沟通消费者同意后，商家可通过客服工作台或快手小店后台发送协商发货卡片至消费者，消费者确认后即可按照新的承诺发货时间来考核或按照协商一致方案取消订单，若消费者超时未确认或不同意，则发货协商失败，按照原承诺发货时间考核。\n去协商发货\n快递网点服务能力问题\n快递网点服务能力问题属于商家责任，如快递丢件、派件超区、非不可抗力造成的服务不可达、无法派送、网点经营问题等场景，故不支持报备，建议商家选择优质的物流公司合作\n-\n订单为异常订单（如双地址等）\n异常订单可以在快手小店-保障-举报中心发起举报，举报成功后会豁免订单物流类判罚及剔除对应店铺分考核\n去举报\n消费者备注信息无法满足\n如遇消费者备注信息无法满足的情况，商家可按照订单信息履约，或联系消费者取消订单，故不支持报备\n-\n商家报备原因不明确\n商家报备原因不明确，平台不能准确了解商家诉求，无法解决商家问题，故不支持报备\n-'
        texts = [element.text for element in elements]
        assert text_mod == texts[1], '文案错误'

        # 点击去协商发货
        self.click("//a[contains(text(),'去协商发货')]")
        self.switch_to_window(-1)
        self.assert_url(env + "zone/fulfill/delayship-report/list?tabId=negotiationCenter")
        self.wait_for_element("//span[@class='moduleTitle___o2MUB']")
        self.assert_text("发货协商指导说明", "//span[@class='moduleTitle___o2MUB']")
        self.driver.close()
        self.switch_to_window(-1)

        # 点击去举报
        self.click("//a[contains(text(),'去举报')]")
        self.switch_to_window(-1)
        self.assert_url(env + "zone/control/report")
        self.wait_for_element("//span[@class='titleLabel___NxNeN']")
        self.assert_text("举报中心", "//span[@class='titleLabel___NxNeN']")
        self.driver.close()
        self.switch_to_window(-1)

        self.is_element_clickable("//div[contains(text(),'看得懂')]")
        self.is_element_clickable("//div[contains(text(),'看不懂')]")

        # 点击可报备场景
        self.click("//div[@id='rc-tabs-3-tab-1']")
        sleep(1)
        elements = self.driver.find_elements(By.CSS_SELECTOR,'.kwaishop-trade-unable-delivery-solution-pc-table-content')
        text_mod = '场景 场景说明\n自然灾害\n因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景\n大型会议\n因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收件/派件的场景\n重大赛事\n因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景\n停电\n由于商家发货地停电无法发货或上游工厂、仓库停电无法生产造成商家无法按时发货的场景\n着火\n由于商家发货地发生火灾或上游工厂、仓库发生火灾造成商家无法按时发货的场景\n消费者要求延迟发货\n因消费者主动要求延迟发货的场景，建议优先和消费者使用【发货协商】工具协商一致后用户点击同意来推迟发货，无需平台审核。去协商发货\n消费者详细收货地址缺失\n消费者详细收货地址缺失导致商家无法按平台时效要求进行发货履约的场景\n消费者未提供定制品信息\n消费者未提供定制品信息导致商家无法按平台时效要求进行发货履约的场景'
        texts = [element.text for element in elements]
        assert text_mod == texts[2], '文案错误'

        # 点击去协商发货
        self.click("//div[contains(text(),'因消费者主动要求延迟发货的场景，建议优先和消费者使用【发货协商】工具协商一致后用户点击同意来推迟发货')]//a[contains(text(),'去协商发货')]")
        self.switch_to_window(-1)
        self.assert_url(env + "zone/fulfill/delayship-report/list?tabId=negotiationCenter")
        self.wait_for_element("//span[@class='moduleTitle___o2MUB']")
        self.assert_text("发货协商指导说明", "//span[@class='moduleTitle___o2MUB']")
        self.driver.close()
        self.switch_to_window(-1)

        self.click("//span[contains(text(),'知道了')]")

    # # 顶部文案-超链接
    # @pytest.mark.p1
    # def test_delay_delivery_page_top_text(self):
    #     self.to_delay_delivery()
    #     sleep(1)
    #     # 判断顶部公告文案是否存在
    #     assert self.is_element_visible('.ant-alert-message'), "顶部公告文案不可见"
    #     elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-alert-content > div > div > div > a')
    #     texts = [element.text for element in elements]
    #     for i in range(len(elements)):
    #         is_displayed=elements[i].is_displayed()
    #         is_enabled = elements[i].is_enabled()
    #         if (is_displayed and is_enabled) == False:
    #             assert False, texts[i] + '按钮不可点击'
    #         print(texts[i] + '--正常')
    #
    #
    # # 「商家报备指导区域」-所有可点击区域-超链接
    # @pytest.mark.p1
    # def test_delay_delivery_page_operation_guide(self):
    #     self.to_delay_delivery()
    #     sleep(1)
    #
    #     # 「收起」
    #     assert self.is_element_clickable(".J7gIVyx6WDzCy3UtJeB7 > button.ant-btn.ant-btn-link"), "「展开」按钮不可点击"
    #     self.click(".J7gIVyx6WDzCy3UtJeB7 > button.ant-btn.ant-btn-link")
    #
    #     # 判断顶部公告文案是否存在
    #     assert self.get_text("//span[@class='oskt0GT4Uv3bKIeCbXtP']") == "商家报备指导", "指导标题错误"
    #     assert self.get_text("//span[@class='xZyJXKCDyhtAYao3A8z_']") == "因不可抗力因素无法按时发货，命中免考核地区公告/报备审核通过订单即获多重权益", "操作指导错误"
    #
    #     text_template=["延长订单发货时效", "延长订单揽收时效", "店铺体验分考核更新", "查看不可抗力平台免考核公告", "查看支持报备场景及举证材料", "查看报备审核进度和结果", "查看报备成功订单明细"]
    #     url_template=["https://docs.qingque.cn/d/home/<USER>",
    #                   "https://docs.qingque.cn/d/home/<USER>",
    #                   "https://docs.qingque.cn/d/home/<USER>",
    #                   "https://s.kwaixiaodian.com/zone/trade/delayship-report/exemption-area",
    #                   "https://docs.qingque.cn/d/home/<USER>",
    #                   "https://edu.kwaixiaodian.com/bbs/web/article?id=9738&layoutType=4",
    #                   "https://s.kwaixiaodian.com/zone/trade/delayship-report/report-order-list"
    #                   ]
    #
    #     while self.is_element_visible("//a[contains(text(),'延长订单发货时效')]") == False:
    #         logger.info("页面未加载完成")
    #         sleep(1)
    #     # 顺丰包邮服务弹窗
    #     if self.is_element_visible("//button[text()='知道了']"):
    #         self.click("//button[text()='知道了']")
    #         self.sleep(2)
    #         logger.info("顺丰包邮服务弹窗")
    #     elements = self.driver.find_elements(By.CLASS_NAME, 'EGv2UcN59rANgRHL38lX')
    #     texts = [element.text for element in elements]
    #     j=0
    #     for i in range(len(elements)):
    #         assert texts[i] == text_template[i], "操作指导错误"
    #         elements[i].click()
    #         sleep(1)
    #         if i in [0,1,2,4,5]:
    #             self.switch_to_window(j+1)
    #         assert self.get_current_url() == url_template[i], "操作执导跳转错误"
    #         sleep(1)
    #         self.switch_to_window(0)
    #         if i == 3:
    #             self.go_back()
    #             while self.is_element_visible("//a[contains(text(),'延长订单发货时效')]") == False:
    #                 logger.info("页面未加载完成")
    #             elements = self.driver.find_elements(By.CLASS_NAME, 'EGv2UcN59rANgRHL38lX')
    #             texts = [element.text for element in elements]
    #             j-=1
    #         j+=1
    #
    #
    #
    #
    # # 延迟发货报备-平台免考核范围
    # @pytest.mark.p1
    # # @pytest.mark.skip
    # def test_delay_delivery_page_assessment_exemption_scope(self):
    #     self.to_delay_delivery()
    #
    #     self.assert_text("平台免考核范围", "//div[@class='wquHsJpns6T8KMTCcG0Q']")
    #     self.assert_text("收货/发货地址在下方且承诺发货时间在“免考核时间”内订单，平台将免除及时发货率、揽收异常率考核和延迟发货扣款，无需报备。", "//span[@class='F3xAYpn1Jm8RAS4e4Aje']")
    #     self.assert_text("查看规则", "//span[contains(text(),'查看规则')]")
    #     self.click("//span[contains(text(),'查看规则')]")
    #     self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
    #     self.switch_to_window(0)
    #     self.assert_text("今日新增", "//div[@class='ieEbCVoGWCygo92c6tik fq7mUFh_eBHbaptfWV8u']")
    #     self.assert_text("个免考核地区", "//body//div[@id='main-root']//div[@class='ant-spin-container']//div[@class='ant-spin-container']//div[1]//div[1]//div[2]//span[2]")
    #     self.assert_text("3天后取消", "//div[@class='ieEbCVoGWCygo92c6tik yNsbau9WarBilLS3fjNM']")
    #     self.assert_text("个免考核地区", "//div[@class='GE60tEIvxqPzpMVTM2BS']//div[2]//div[1]//div[2]//span[2]")
    #     self.assert_text("今日取消", "//div[@class='ieEbCVoGWCygo92c6tik l_Z0Pp_beU7aGRmF0Gob']")
    #     self.assert_text("个免考核地区", "//div[@class='V98SymYAjaScccFruOtw']//div[3]//div[1]//div[2]//span[2]")
    #
    # # 延迟发货报备-已豁免订单查询
    # @pytest.mark.p1
    # # @pytest.mark.skip
    # def test_delay_delivery_exempted_order_query(self):
    #     self.to_delay_delivery()
    #     self.assert_text("已豁免订单查询", "//div[@class='D5s8EsWQqPcJqpiOJ9of']")
    #     self.assert_text("支持查询订单的报备记录和平台免考核记录", "//div[@class='TgKYaVSHg6oUIFKPKF7A']")
    #     assert self.is_element_clickable("//button[@class='ant-btn ant-btn-primary']"), "查询按钮不可点击"
    #     assert self.is_element_clickable("//span[contains(text(),'清空')]"), "按钮不可点击"
    #
    #
    # # 延迟发货报备-我要报备
    # @pytest.mark.p1
    # # @pytest.mark.skip
    # def test_delay_delivery_i_want_report(self):
    #     self.to_delay_delivery()
    #
    #     if 's.kwaixiaodian.com' in self.get_current_url():
    #         env='https://s.kwaixiaodian.com/'
    #     else:
    #         env='https://eshop-s.prt.kwaixiaodian.com/'
    #
    #     self.assert_text("我要报备", "//span[contains(text(),'我要报备')]")
    #     self.assert_text("收货/发货地区报备", "//div[@class='LbYEZF5I0oYTyJIGjW1j'][contains(text(),'收货/发货地区报备')]")
    #     self.assert_text("针对受影响地区报备，审核通过后豁免全店相关地区订单", "//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[2]//div[2]")
    #     self.click("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
    #     self.assert_url(env + 'zone/trade/delayship-report/add?reportDimension=1')
    #     self.switch_to_window(0)
    #     self.assert_text("订单报备", "//div[@class='LbYEZF5I0oYTyJIGjW1j'][contains(text(),'订单报备')]")
    #     self.assert_text("针对指定订单报备，审核通过后豁免指定订单", "//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[2]//div[2]")
    #     self.click("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
    #     self.assert_url(env + 'zone/trade/delayship-report/add?reportDimension=2')
    #     self.switch_to_window(0)

    # 延迟发货报备流水
    @pytest.mark.p1
    # @pytest.mark.skip
    def test_delay_delivery_report_list(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        # self.scroll_down(1)

        self.assert_text("延迟发货报备记录", "//div[@class='title___Ap_s9']")
        self.assert_text("延迟揽收报备记录可至揽收报备页面查看", "//div[@class='tips___ORAXw']")
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(-1)
        self.assert_url(env + "zone/fulfill/delayship-report/collect-package-list/index")
        self.driver.close()
        self.switch_to_window(-1)

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")
            logger.debug("关闭 您有多大程度满意我们的产品 弹窗")

        self.assert_text("报备单号", "//label[@title='报备单号']")
        self.input("//input[@id='id']", 2426901624802528)
        assert self.get_text("//input[@id='id']") == '2426901624802528', '输入订单号异常'
        self.assert_text("报备原因", "//label[@title='报备原因']")

        elements = self.driver.find_elements(By.CSS_SELECTOR, ".kwaishop-trade-unable-delivery-solution-pc-select-selector")
        texts = [element.text for element in elements]
        text_total = [["全部", "大型会议", "重大赛事", "自然灾害", "商家因素", "特殊订单", "疫情原因", "双地址", "停电/着火", "消费者要求延迟发货", "消费者详细收货地址缺失",
                       "消费者未提供定制品信息"],
                      ["全部", "审核中", "已撤回", "审核通过", "审核不通过"],
                      ["全部", "发货报备", "揽收报备"],
                      ["全部", "收货/发货地报备", "订单报备"]]
        path_total = ["//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-item-option-content'][contains(text(),'{}')]","//div[@title='{}']"]

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")
            logger.debug("关闭 您有多大程度满意我们的产品 弹窗")

        for i in range(4):
            elements[i].click()
            self.hover_down1(1)
            for j in range(1, len(text_total[i])):
                path = path_total[0].format(text_total[i][j])
                if self.is_element_clickable(path) == False:
                    path = path_total[1].format(text_total[i][j])
                if self.get_text(path) != text_total[i][-1]:
                    self.hover_down1(1)
                else:
                    self.click(path)

        self.assert_text("申请时间", "//label[@title='申请时间']")
        self.click("//input[@id='time']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")

        assert self.is_element_clickable("//span[contains(text(),'查 询')]"), "按钮不可点击"
        assert self.is_element_clickable("//span[contains(text(),'重 置')]"), "按钮不可点击"

        assert self.is_element_clickable("//span[contains(text(),'报备通过')]"), "按钮不可点击"
        assert self.is_element_clickable("//span[contains(text(),'报备不通过')]"), "按钮不可点击"

        text_template_list = '报备单号 申请时间 报备单状态 报备类型 报备维度 报备原因 操作'
        elements = self.driver.find_elements(By.CLASS_NAME, "kwaishop-trade-unable-delivery-solution-pc-table-thead")
        texts = [element.text for element in elements]
        assert texts[0] == text_template_list, "报备流水列名有误"

    # 履约-新增订单报备-大型会议
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_new_delay_delivery_large_conference(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        self.assert_text("店铺遇到自然灾害、会议赛事等不可抗力等问题，导致无法正常发货", "//div[@class='application___HCsHW']//div[3]//div[1]//div[1]//div[2]//div[1]")
        self.assert_text("可使用如下订单报备工具发起报备，经平台审核通过后，将为您推迟订单的发货考核时间", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[1]//div[1]//div[2]//div[2]")
        self.assert_text("大型会议影响", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[2]//div[1]//div[1]")
        self.assert_text("因国家大型会议导致部分地区的商家无法按时发货的场景", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[2]//div[1]//div[2]")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")

        # 发起报备
        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[2]//div[2]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        text1='1、如因自然灾害等不可抗力问题导致无法发货，可先查看平台发布的免考核公告，平台已针对受影响地区主动豁免物流相关判罚，并推迟承诺发货时间，商家无需重复报备。平台免考核信息可点击查看。查看详情'
        text2='2、当前页面为延迟发货报备申请提交页，仅支持未发货，且未超承诺发货时间的订单报备，已发货的订单无法报备。若已发货但受不可抗力影响无法按照揽收时效及时揽收，可至揽收报备页面进行报备'
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-alert-message')
        texts = [element.text for element in elements]
        assert text1 in texts[0], "公告栏信息错误"
        assert text2 in texts[0], "公告栏信息错误"

        self.click("//a[contains(text(),'查看详情')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
        self.driver.close()
        self.switch_to_window(-1)
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(-1)
        self.assert_url(env+'zone/fulfill/delayship-report/list?tabId=delayCollectReport')
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//span[@title='大型会议']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-select-item.kwaishop-trade-unable-delivery-solution-pc-select-item-option')
        texts = [element.text for element in elements]
        text3 = ['大型会议\n因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景','自然灾害\n因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景','停电/着火\n因停电、着火导致商家无法按平台时效要求进行发货履约的场景','重大赛事\n因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] == texts[i], '报备原因下拉选项错误'

        self.assert_text('因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景', "//div[@class='kwaishop-trade-unable-delivery-solution-pc-form-item-extra']//div[contains(text(),'因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景')]")

        self.assert_text("报备维度", "//label[@title='报备维度']")
        self.is_element_clickable("//div[@class='label___yrk3c'][contains(text(),'订单报备')]")
        self.assert_text("针对指定订单报备，审核通过后豁免指定订单", "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper kwaishop-trade-unable-delivery-solution-pc-radio-wrapper-checked']//div[2]")
        self.is_element_clickable("//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[1]")
        self.assert_text("针对受影响地区报备，审核通过后豁免全店相关地区订单", "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[2]")


        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")

        self.assert_text("选择订单", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.click("//*[name()='path' and contains(@d,'M3.32 18.7')]")
        sleep(1)

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable("//div[@id='pics']//div[1]//div[1]//div[1]//div[1]//div[2]//span[1]//span[1]//div[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable("//div[@class='kwaishop-trade-unable-delivery-solution-pc-col kwaishop-trade-unable-delivery-solution-pc-form-item-control']//div[2]//div[1]//div[1]//div[1]//div[2]//span[1]//span[1]//div[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取 消')]")

    # 履约-新增订单报备-大型会议-选择订单
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_new_delay_delivery_large_conference_order_select(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")
        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")

        # 发起报备
        self.click(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[2]//div[2]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")

        self.assert_text("选择订单", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        elements = self.driver.find_elements(By.CLASS_NAME,
                                             'kwaishop-trade-unable-delivery-solution-pc-alert-description')
        texts = [element.text for element in elements]
        text4 = ['1.系统已自动筛选出未超过承诺发货时间的订单，商家可选择状态为「可报备」的订单进行报备，最多可选择50条订单',
                 '2.当前系统仅展示近六个月内下单的订单。商家若想针对六个月前生成的订单报备，需在「订单编号」输入框中手动输入订单编号（支持同时输入多个订单编号，以空格区分）',
                 '3.订单报备仅支持同时报备最多50个订单，主要适用于订单收货信息不全等受影响订单较少的情况。若因不可抗力需要报备较多订单，请在「收货/发货地报备」模块进行报备']
        for i in range(len(text4)):
            assert text4[i] in texts[0], '选择订单抽屉公告错误'
        self.assert_text("订单编号", "//label[@title='订单编号']")
        self.assert_text("订单创建时间", "//label[@title='订单创建时间']")
        self.click("//input[@placeholder='开始时间']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("承诺发货时间", "//label[@title='承诺发货时间']")
        self.click("//span[@title='三天内需发货']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        texts = [element.text for element in elements]
        text5 = '24小时内需发货\n三天内需发货\n七天内需发货\n全部'
        assert text5 in texts[0], '选择订单抽屉承诺发货时间下拉选项错误'
        self.is_element_clickable("//span[contains(text(),'重 置')]")
        self.is_element_clickable("//span[contains(text(),'查 询')]")

        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-table-tbody')
        texts = [element.text for element in elements]
        assert len(texts[0]) > 1, '选择订单列表为空'

        self.is_element_clickable("//button[@class='kwaishop-trade-unable-delivery-solution-pc-btn btn___XH3ou']//span[contains(text(),'取 消')]")
        self.is_element_clickable("//span[contains(text(),'确 认')]")
        self.is_element_clickable("//*[name()='path' and contains(@d,'M3.32 18.7')]")

    # 履约-新增报备-重大赛事
    @pytest.mark.p1
    # @pytest.mark.skip
    def test_new_delay_delivery_major_events(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        self.assert_text("重大赛事影响",
                         "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[4]//div[1]//div[1]")
        self.assert_text("因重大赛事导致部分地区的商家无法按时发货的场景",
                         "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[4]//div[1]//div[2]")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")

        # 发起报备
        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[4]//div[2]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        text1 = '1、如因自然灾害等不可抗力问题导致无法发货，可先查看平台发布的免考核公告，平台已针对受影响地区主动豁免物流相关判罚，并推迟承诺发货时间，商家无需重复报备。平台免考核信息可点击查看。查看详情'
        text2 = '2、当前页面为延迟发货报备申请提交页，仅支持未发货，且未超承诺发货时间的订单报备，已发货的订单无法报备。若已发货但受不可抗力影响无法按照揽收时效及时揽收，可至揽收报备页面进行报备'
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-alert-message')
        texts = [element.text for element in elements]
        assert text1 in texts[0], "公告栏信息错误"
        assert text2 in texts[0], "公告栏信息错误"

        self.click("//a[contains(text(),'查看详情')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
        self.driver.close()
        self.switch_to_window(-1)
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(-1)
        self.assert_url(env + 'zone/fulfill/delayship-report/list?tabId=delayCollectReport')
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//span[@title='重大赛事']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME,
                                             'kwaishop-trade-unable-delivery-solution-pc-select-item.kwaishop-trade-unable-delivery-solution-pc-select-item-option')
        texts = [element.text for element in elements]
        text3 = ['大型会议\n因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景',
                 '自然灾害\n因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景',
                 '停电/着火\n因停电、着火导致商家无法按平台时效要求进行发货履约的场景',
                 '重大赛事\n因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] == texts[i], '报备原因下拉选项错误'

        self.assert_text('因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景', "//div[@class='kwaishop-trade-unable-delivery-solution-pc-form-item-extra']//div[contains(text(),'因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("报备维度", "//label[@title='报备维度']")
        self.is_element_clickable("//div[@class='label___yrk3c'][contains(text(),'订单报备')]")
        self.assert_text("针对指定订单报备，审核通过后豁免指定订单", "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper kwaishop-trade-unable-delivery-solution-pc-radio-wrapper-checked']//div[2]")
        self.assert_text("针对受影响地区报备，审核通过后豁免全店相关地区订单", "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[2]")
        i=0
        while self.is_element_visible("//label[@title='受影响区域类型']")==False and i<5:
            self.click("//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[1]")
            sleep(0.5)
            i+=1
        self.assert_text("受影响区域类型", "//label[@title='受影响区域类型']")
        self.is_element_clickable("//span[contains(text(),'发货地')]")
        self.is_element_clickable("//span[contains(text(),'收货地')]")
        self.assert_text("最多可选择50个区/县报备", "//div[contains(text(),'最多可选择50个区/县报备')]")
        sleep(0.5)
        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-selection-overflow']")
        sleep(0.5)
        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-tree-list-holder-inner']//div[1]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-tree-treenode kwaishop-trade-unable-delivery-solution-pc-select-tree-treenode-switcher-close kwaishop-trade-unable-delivery-solution-pc-select-tree-treenode-leaf-last']//span[@aria-label='system-arrow-medium-down-line']//*[name()='svg']")
        sleep(0.5)
        self.click("//span[@class='kwaishop-trade-unable-delivery-solution-pc-select-tree-title'][contains(text(),'东城区')]")
        sleep(0.5)

        self.assert_text("发货受影响时间段", "//label[@title='发货受影响时间段']")
        self.click("//input[@id='reportingTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable(
            "//div[@id='pics']//div[1]//div[1]//div[1]//div[1]//div[2]//span[1]//span[1]//div[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable(
            "/html[1]/body[1]/div[5]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[8]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/span[1]/span[1]/div[1]/div[1]/span[1]/div[1]/div[1]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取 消')]")

    # 履约-新增报备-自然灾害
    @pytest.mark.p1
    # @pytest.mark.skip
    def test_new_delay_delivery_natural_disaster(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        self.assert_text("自然灾害影响",
                         "//div[@class='application___HCsHW']//div[3]//div[2]//div[1]//div[1]//div[1]")
        self.assert_text("因自然灾害（洪水、暴雪、台风、地震等）导致部分地区的商家无法按时发货的场景",
                         "//div[@class='application___HCsHW']//div[3]//div[2]//div[1]//div[1]//div[2]")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")

        # 发起报备
        self.click(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[1]//div[2]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        text1 = '1、如因自然灾害等不可抗力问题导致无法发货，可先查看平台发布的免考核公告，平台已针对受影响地区主动豁免物流相关判罚，并推迟承诺发货时间，商家无需重复报备。平台免考核信息可点击查看。查看详情'
        text2 = '2、当前页面为延迟发货报备申请提交页，仅支持未发货，且未超承诺发货时间的订单报备，已发货的订单无法报备。若已发货但受不可抗力影响无法按照揽收时效及时揽收，可至揽收报备页面进行报备'
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-alert-message')
        texts = [element.text for element in elements]
        assert text1 in texts[0], "公告栏信息错误"
        assert text2 in texts[0], "公告栏信息错误"

        self.click("//a[contains(text(),'查看详情')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
        self.driver.close()
        self.switch_to_window(-1)
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(-1)
        self.assert_url(env + 'zone/fulfill/delayship-report/list?tabId=delayCollectReport')
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备原因", "//label[@title='报备原因']")

        self.click("//span[@title='自然灾害']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME,
                                             'kwaishop-trade-unable-delivery-solution-pc-select-item.kwaishop-trade-unable-delivery-solution-pc-select-item-option')
        texts = [element.text for element in elements]
        text3 = ['大型会议\n因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景',
                 '自然灾害\n因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景',
                 '停电/着火\n因停电、着火导致商家无法按平台时效要求进行发货履约的场景',
                 '重大赛事\n因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] == texts[i], '报备原因下拉选项错误'

        self.assert_text('因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景',
                         "//div[contains(text(),'因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效')]")

        self.assert_text("报备维度", "//label[@title='报备维度']")
        self.is_element_clickable("//div[@class='label___yrk3c'][contains(text(),'订单报备')]")
        self.assert_text("针对指定订单报备，审核通过后豁免指定订单",
                         "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper kwaishop-trade-unable-delivery-solution-pc-radio-wrapper-checked']//div[2]")
        self.assert_text("针对受影响地区报备，审核通过后豁免全店相关地区订单",
                         "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[2]")
        i = 0
        while self.is_element_visible("//label[@title='受影响区域类型']") == False and i < 5:
            self.click("//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[1]")
            sleep(0.5)
            i += 1
        self.assert_text("受影响区域类型", "//label[@title='受影响区域类型']")
        self.is_element_clickable("//span[contains(text(),'发货地')]")
        self.is_element_clickable("//span[contains(text(),'收货地')]")
        self.assert_text("最多可选择50个区/县报备", "//div[contains(text(),'最多可选择50个区/县报备')]")
        sleep(0.5)
        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-selection-overflow']")
        sleep(0.5)
        self.click(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-tree-list-holder-inner']//div[1]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-select-tree-treenode kwaishop-trade-unable-delivery-solution-pc-select-tree-treenode-switcher-close kwaishop-trade-unable-delivery-solution-pc-select-tree-treenode-leaf-last']//span[@aria-label='system-arrow-medium-down-line']//*[name()='svg']")
        sleep(0.5)
        self.click(
            "//span[@class='kwaishop-trade-unable-delivery-solution-pc-select-tree-title'][contains(text(),'东城区')]")
        sleep(0.5)

        self.assert_text("发货受影响时间段", "//label[@title='发货受影响时间段']")
        self.click("//input[@id='reportingTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable(
            "//div[@id='pics']//div[1]//div[1]//div[1]//div[1]//div[2]//span[1]//span[1]//div[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable(
            "/html[1]/body[1]/div[5]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]/form[1]/div[8]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/span[1]/span[1]/div[1]/div[1]/span[1]/div[1]/div[1]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取 消')]")

    # 履约-新增报备-停电/着火'
    @pytest.mark.p1
    # @pytest.mark.skip
    def test_new_delay_delivery_fire(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        self.assert_text("停电、着火、网络异常影响",
                         "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[3]//div[1]//div[1]")
        self.assert_text("因停电、火灾、网络异常导致部分地区的商家无法按时发货的场景",
                         "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[3]//div[1]//div[2]")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")

        # 发起报备
        self.click(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-spin-container']//div//div[3]//div[2]//div[3]//div[2]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        text1 = '1、如因自然灾害等不可抗力问题导致无法发货，可先查看平台发布的免考核公告，平台已针对受影响地区主动豁免物流相关判罚，并推迟承诺发货时间，商家无需重复报备。平台免考核信息可点击查看。查看详情'
        text2 = '2、当前页面为延迟发货报备申请提交页，仅支持未发货，且未超承诺发货时间的订单报备，已发货的订单无法报备。若已发货但受不可抗力影响无法按照揽收时效及时揽收，可至揽收报备页面进行报备'
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-alert-message')
        texts = [element.text for element in elements]
        assert text1 in texts[0], "公告栏信息错误"
        assert text2 in texts[0], "公告栏信息错误"

        self.click("//a[contains(text(),'查看详情')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
        self.driver.close()
        self.switch_to_window(-1)
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(-1)
        self.assert_url(env + 'zone/fulfill/delayship-report/list?tabId=delayCollectReport')
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//span[@title='停电/着火']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME,
                                             'kwaishop-trade-unable-delivery-solution-pc-select-item.kwaishop-trade-unable-delivery-solution-pc-select-item-option')
        texts = [element.text for element in elements]
        text3 = ['大型会议\n因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景',
                 '自然灾害\n因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景',
                 '停电/着火\n因停电、着火导致商家无法按平台时效要求进行发货履约的场景',
                 '重大赛事\n因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] == texts[i], '报备原因下拉选项错误'

        self.assert_text('因停电、着火导致商家无法按平台时效要求进行发货履约的场景',
                         "//div[contains(text(),'因停电、着火导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("报备维度", "//label[@title='报备维度']")
        self.is_element_clickable("//div[@class='label___yrk3c'][contains(text(),'订单报备')]")
        self.assert_text("针对指定订单报备，审核通过后豁免指定订单",
                         "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper kwaishop-trade-unable-delivery-solution-pc-radio-wrapper-checked']//div[2]")
        self.is_element_clickable("//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[1]")
        self.assert_text("针对受影响地区报备，审核通过后豁免全店相关地区订单",
                         "//label[@class='kwaishop-trade-unable-delivery-solution-pc-radio-wrapper']//div[2]")

        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")

        self.assert_text("选择订单", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.click("//*[name()='path' and contains(@d,'M3.32 18.7')]")
        sleep(1)

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable(
            "//div[@id='pics']//div[1]//div[1]//div[1]//div[1]//div[2]//span[1]//span[1]//div[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-col kwaishop-trade-unable-delivery-solution-pc-form-item-control']//div[2]//div[1]//div[1]//div[1]//div[2]//span[1]//span[1]//div[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取 消')]")

    # 履约-新增订单报备-消费者要求延迟发货'
    @pytest.mark.p1
    # @pytest.mark.skip
    def test_new_delay_delivery_consumers_demand(self):
        self.to_report_center()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//span[contains(text(),'查询已豁免订单')]")

        self.assert_text("店铺遇到消费者要求延迟发货、订单信息异常等问题，导致无法正常发货",
                         "//div[@class='application___HCsHW']//div[2]//div[1]//div[1]//div[2]//div[1]")
        self.assert_text("可使用如下订单报备工具发起报备，经平台审核通过后，将为您推迟订单的发货考核时间",
                         "//div[@class='application___HCsHW']//div[2]//div[1]//div[1]//div[2]//div[2]")
        self.assert_text("消费者要求延迟发货",
                         "//div[@class='title___mlCkj'][contains(text(),'消费者要求延迟发货')]")
        self.assert_text("消费者主动要求延迟发货导致商家无法按时发货的场景",
                         "//div[@class='application___HCsHW']//div[2]//div[2]//div[2]//div[1]//div[2]")

        # 关闭 您有多大程度满意我们的产品弹窗
        if self.is_element_clickable(".anticon.anticon-system-close-medium-line"):
            self.click(".anticon.anticon-system-close-medium-line")

        # 发起报备
        self.click(
            "//div[@class='kwaishop-trade-unable-delivery-solution-pc-tabs-content-holder']//div[2]//div[2]//div[2]//div[2]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        text1 = '1、如因自然灾害等不可抗力问题导致无法发货，可先查看平台发布的免考核公告，平台已针对受影响地区主动豁免物流相关判罚，并推迟承诺发货时间，商家无需重复报备。平台免考核信息可点击查看。查看详情'
        text2 = '2、当前页面为延迟发货报备申请提交页，仅支持未发货，且未超承诺发货时间的订单报备，已发货的订单无法报备。若已发货但受不可抗力影响无法按照揽收时效及时揽收，可至揽收报备页面进行报备'
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-unable-delivery-solution-pc-alert-message')
        texts = [element.text for element in elements]
        assert text1 in texts[0], "公告栏信息错误"
        assert text2 in texts[0], "公告栏信息错误"

        self.click("//a[contains(text(),'查看详情')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
        self.driver.close()
        self.switch_to_window(-1)
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(-1)
        self.assert_url(env + 'zone/fulfill/delayship-report/list?tabId=delayCollectReport')
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//span[@title='消费者要求延迟发货']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME,
                                             'kwaishop-trade-unable-delivery-solution-pc-select-item.kwaishop-trade-unable-delivery-solution-pc-select-item-option')
        texts = [element.text for element in elements]
        text3 = ['消费者详细地址缺失\n消费者详细收货地址缺失导致商家无法按平台时效要求进行发货履约的场景',
                 '消费者要求延迟发货\n因消费者主动要求延迟发货的场景，建议优先和消费者协商，协商一致后使用协商发货功能用户点击同意来推迟发货，无需平台审核。点击去协商发货 或 查看协商发货指南',
                 '消费者未提供定制品信息\n消费者未提供定制品信息导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] in texts[i], '报备原因下拉选项错误'

        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-form-item-extra']//div//a[contains(text(),'点击去协商发货')]")
        self.switch_to_window(-1)
        self.assert_url(env+"zone/fulfill/delayship-report/list?tabId=negotiation")
        self.driver.close()
        self.switch_to_window(-1)

        self.click("//div[@class='kwaishop-trade-unable-delivery-solution-pc-form-item-extra']//div//a[contains(text(),'查看协商发货指南')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/bbs/web/article?id=20641&layoutType=4")
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        self.wait_for_element("//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")

        self.assert_text("选择订单", "//div[@class='kwaishop-trade-unable-delivery-solution-pc-drawer-title']")
        self.click("//*[name()='path' and contains(@d,'M3.32 18.7')]")
        sleep(1)

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(-1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable("//*[name()='path' and contains(@d,'M19 13h-6v')]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取 消')]")

    # 履约-新增订单报备-消费者详细收货地址缺失'
    @pytest.mark.p1
    @pytest.mark.skip
    def test_new_delay_delivery_address_missing(self):
        self.to_delay_delivery()

        self.wait_for_element("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
        self.click("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        sleep(1)
        self.hover_down1(6)
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-item.ant-select-item-option')
        texts = [element.text for element in elements]
        text3 = '消费者详细收货地址缺失\n消费者详细收货地址缺失导致商家无法按平台时效要求进行发货履约的场景'
        assert text3 in texts[5], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'消费者详细收货地址缺失')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'消费者详细收货地址缺失')]")
        sleep(1)
        self.assert_text(text3[1], "//div[@class='ant-legacy-form-extra']//div[contains(text(),'消费者详细收货地址缺失导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        sleep(1)

        self.assert_text("选择订单", "//div[@class='ant-drawer-title']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-alert-description')
        texts = [element.text for element in elements]
        text4 = ['1.系统已自动筛选出未超过承诺发货时间的订单，商家可选择状态为「可报备」的订单进行报备，最多可选择50条订单',
                 '2.当前系统仅展示近六个月内下单的订单。商家若想针对六个月前生成的订单报备，需在「订单编号」输入框中手动输入订单编号（支持同时输入多个订单编号，以空格区分）',
                 '3.订单报备仅支持同时报备最多50个订单，主要适用于订单收货信息不全等受影响订单较少的情况。若因不可抗力需要报备较多订单，请在「收货/发货地报备」模块进行报备']
        for i in range(len(text4)):
            assert text4[i] in texts[0], '选择订单抽屉公告错误'
        self.assert_text("订单编号", "//label[@title='订单编号']")
        self.assert_text("订单创建时间", "//label[@title='订单创建时间']")
        self.click("//input[@placeholder='开始时间']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("承诺发货时间", "//label[@title='承诺发货时间']")
        self.click("//span[@title='三天内需发货']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        texts = [element.text for element in elements]
        text5 = '24小时内需发货\n三天内需发货\n七天内需发货\n全部'
        assert text5 in texts[1], '选择订单抽屉承诺发货时间下拉选项错误'
        self.is_element_clickable("//span[contains(text(),'重置')]")
        self.is_element_clickable("//button[@type='submit']")

        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-table-tbody')
        texts = [element.text for element in elements]
        assert len(texts[0]) > 1, '选择订单列表为空'

        self.is_element_clickable("//button[@class='ant-btn nYhbEsBWvMGf7P2q9jAh']//span[contains(text(),'取消')]")
        self.is_element_clickable("//span[contains(text(),'确认')]")
        self.click("//*[name()='path' and contains(@d,'M216.32 80')]")
        sleep(1)

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 履约-新增订单报备-消费者未提供定制品信息
    @pytest.mark.p1
    @pytest.mark.skip
    def test_new_delay_delivery_no_information_provided(self):
        self.to_delay_delivery()

        self.wait_for_element("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
        self.click("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        sleep(0.5)
        self.hover_down1(6)
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-select-item.ant-select-item-option')
        texts = [element.text for element in elements]
        text3 = '消费者未提供定制品信息\n消费者未提供定制品信息导致商家无法按平台时效要求进行发货履约的场景'
        assert text3 in texts[6], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'消费者未提供定制品信息')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'消费者未提供定制品信息')]")
        sleep(1)
        self.assert_text(text3[1],
                         "//div[@class='ant-legacy-form-extra']//div[contains(text(),'消费者未提供定制品信息导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        sleep(1)

        self.assert_text("选择订单", "//div[@class='ant-drawer-title']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-alert-description')
        texts = [element.text for element in elements]
        text4 = ['1.系统已自动筛选出未超过承诺发货时间的订单，商家可选择状态为「可报备」的订单进行报备，最多可选择50条订单',
                 '2.当前系统仅展示近六个月内下单的订单。商家若想针对六个月前生成的订单报备，需在「订单编号」输入框中手动输入订单编号（支持同时输入多个订单编号，以空格区分）',
                 '3.订单报备仅支持同时报备最多50个订单，主要适用于订单收货信息不全等受影响订单较少的情况。若因不可抗力需要报备较多订单，请在「收货/发货地报备」模块进行报备']
        for i in range(len(text4)):
            assert text4[i] in texts[0], '选择订单抽屉公告错误'
        self.assert_text("订单编号", "//label[@title='订单编号']")
        self.assert_text("订单创建时间", "//label[@title='订单创建时间']")
        self.click("//input[@placeholder='开始时间']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("承诺发货时间", "//label[@title='承诺发货时间']")
        self.click("//span[@title='三天内需发货']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        texts = [element.text for element in elements]
        text5 = '24小时内需发货\n三天内需发货\n七天内需发货\n全部'
        assert text5 in texts[1], '选择订单抽屉承诺发货时间下拉选项错误'
        self.is_element_clickable("//span[contains(text(),'重置')]")
        self.is_element_clickable("//button[@type='submit']")

        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-table-tbody')
        texts = [element.text for element in elements]
        assert len(texts[0]) > 1, '选择订单列表为空'

        self.is_element_clickable("//button[@class='ant-btn nYhbEsBWvMGf7P2q9jAh']//span[contains(text(),'取消')]")
        self.is_element_clickable("//span[contains(text(),'确认')]")
        self.click("//*[name()='path' and contains(@d,'M216.32 80')]")
        sleep(1)

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.is_element_clickable("//div[@class='ant-legacy-form-item-control']//div[1]//div[1]//div[1]//span[1]//div[2]//div[2]//span[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable("//div[@class='ant-col ant-legacy-form-item-control-wrapper']//div[2]//div[1]//div[1]//span[1]//div[2]//div[2]//span[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable("//div[@class='JfvBKENCflx9HFxicjuH']//div[3]//div[1]//div[1]//span[1]//div[2]//div[2]//span[1]//div[1]//span[1]//div[1]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 履约-收货/发货地报备-大型会议
    @pytest.mark.p0
    @pytest.mark.skip
    def test_new_area_report_large_conference(self):
        self.to_delay_delivery()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env='https://s.kwaixiaodian.com/'
        else:
            env='https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        self.click("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        # 进入新增报备
        self.wait_for_element("//label[@title='报备原因']")

        text1 = '1、如因自然灾害等不可抗力问题导致无法发货，可先查看平台发布的免考核公告，平台已针对受影响地区主动豁免物流相关判罚，并推迟承诺发货时间，商家无需重复报备。平台免考核信息可点击查看。查看详情'
        text2 = '2、当前页面为延迟发货报备申请提交页，仅支持未发货，且未超承诺发货时间的订单报备，已发货的订单无法报备。若已发货但受不可抗力影响无法按照揽收时效及时揽收，可至揽收报备页面进行报备'
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-alert.ant-alert-info.QfYBnBpiKrEYrqKsks2g')
        texts = [element.text for element in elements]
        assert text1 in texts[0], "公告栏信息错误"
        assert text2 in texts[0], "公告栏信息错误"

        self.click("//a[contains(text(),'查看详情')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=qw7tNmjOam")
        self.switch_to_window(1)
        self.click("//a[contains(text(),'揽收报备')]")
        self.switch_to_window(3)
        sleep(1)
        self.assert_url(env+'zone/trade/delayship-report/list?tabId=delayCollectReport')
        self.switch_to_window(1)

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        sleep(1)
        texts = [element.text for element in elements]
        text3 = ['大型会议',
                 '因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景']
        for i in range(len(text3)):
            assert text3[i] in texts[0], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'大型会议')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'大型会议')]")
        sleep(1)
        self.assert_text(text3[1],
                         "//div[@class='ant-legacy-form-extra']//div[contains(text(),'因国家/社会大型会议，导致快递公司/网点对受影响地区快递明确停止收/派件的场景')]")

        self.assert_text("受影响区域类型", "//label[@title='受影响区域类型']")
        self.assert_text("发货地", "//label[@class='ant-radio-wrapper ant-radio-wrapper-checked']//span[contains(text(),'发货地')]")
        self.assert_text("选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单", "//div[contains(text(),'选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单')]")
        self.assert_text("商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地", "//div[contains(text(),'商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地')]")
        self.assert_text("收货地", "//span[contains(text(),'收货地')]")
        self.click("//span[contains(text(),'收货地')]")
        sleep(0.5)

        self.assert_text("受影响区域", "//label[@title='受影响区域']")
        self.assert_text("最多可选择50个区/县报备", "//div[contains(text(),'最多可选择50个区/县报备')]")
        sleep(0.5)
        self.click("//div[@class='ant-select-selection-overflow']")
        sleep(0.5)
        self.click("//div[@class='ant-select-tree-list-holder-inner']//div[1]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click("//div[contains(@class,'ant-select-dropdown xrEl8HAvi4PYDbXL7UQ5 ant-tree-select-dropdown ant-select-dropdown-placement-bottomLeft')]//div[2]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click("//span[@title='永宁县']//span[1]")
        sleep(0.5)

        self.assert_text("发货受影响时间段", "//label[@title='发货受影响时间段']")
        self.click("//input[@id='reportingTime']")
        sleep(0.5)
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。", "//div[contains(text(),'（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。')]")
        self.assert_text("（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。",
                         "//div[contains(text(),'（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。')]")
        self.assert_text("（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。",
                         "//div[contains(text(),'（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。')]")
        self.assert_text("（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货扣款。",
                         "//div[contains(text(),'（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货')]")

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        sleep(0.5)
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(4)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.assert_text("报备证明图片", "//label[@title='报备证明图片']")
        self.is_element_clickable(
            "//div[@class='ant-legacy-form-item-control']//div[1]//div[1]//div[1]//span[1]//div[2]//div[2]//span[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']")
        self.is_element_clickable(
            "//div[@class='ant-col ant-legacy-form-item-control-wrapper']//div[2]//div[1]//div[1]//span[1]//div[2]//div[2]//span[1]//div[1]//span[1]//div[1]//span[1]//*[name()='svg']//*[name()='path' and contains(@d,'M482 152h6')]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 履约-收货/发货地报备-重大赛事
    @pytest.mark.p1
    @pytest.mark.skip
    def test_new_area_report_major_events(self):
        self.to_delay_delivery()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        self.click("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        # 进入新增报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        sleep(1)
        texts = [element.text for element in elements]
        text3 = ['重大赛事',
                 '因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] in texts[0], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'重大赛事')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'重大赛事')]")
        sleep(1)
        self.assert_text(text3[1],
                         "//div[@class='ant-legacy-form-extra']//div[contains(text(),'因举办重大赛事，快递公司/网点对受影响地区明确停止收件，导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("受影响区域类型", "//label[@title='受影响区域类型']")
        self.assert_text("发货地",
                         "//label[@class='ant-radio-wrapper ant-radio-wrapper-checked']//span[contains(text(),'发货地')]")
        self.assert_text("选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单",
                         "//div[contains(text(),'选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单')]")
        self.assert_text("商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地",
                         "//div[contains(text(),'商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地')]")
        self.assert_text("收货地", "//span[contains(text(),'收货地')]")
        self.click("//span[contains(text(),'收货地')]")
        sleep(0.5)

        self.assert_text("受影响区域", "//label[@title='受影响区域']")
        self.assert_text("最多可选择50个区/县报备", "//div[contains(text(),'最多可选择50个区/县报备')]")
        sleep(0.5)
        self.click("//div[@class='ant-select-selection-overflow']")
        sleep(0.5)
        self.click("//div[@class='ant-select-tree-list-holder-inner']//div[1]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click(
            "//div[contains(@class,'ant-select-dropdown xrEl8HAvi4PYDbXL7UQ5 ant-tree-select-dropdown ant-select-dropdown-placement-bottomLeft')]//div[2]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click("//span[@title='永宁县']//span[1]")
        sleep(0.5)

        self.assert_text("发货受影响时间段", "//label[@title='发货受影响时间段']")
        self.click("//input[@id='reportingTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。",
                         "//div[contains(text(),'（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。')]")
        self.assert_text("（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。",
                         "//div[contains(text(),'（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。')]")
        self.assert_text("（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。",
                         "//div[contains(text(),'（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。')]")
        self.assert_text("（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货扣款。",
                         "//div[contains(text(),'（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货')]")

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.assert_text("报备证明图片", "//label[@title='报备证明图片']")
        self.is_element_clickable(
            "//div[@class='ant-legacy-form-item-control']//div[1]//div[1]//div[1]//span[1]//div[2]//div[2]//span[1]//div[1]//span[1]//div[1]")
        self.is_element_clickable(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/form[1]/div[7]/div[2]/div[1]/span[1]/div[2]/div[1]/div[1]/span[1]/div[2]/div[2]/span[1]/div[1]/span[1]/div[1]/p[1]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 履约-收货/发货地报备-自然灾害
    @pytest.mark.p1
    @pytest.mark.skip
    def test_new_area_report_natural_disaster(self):
        self.to_delay_delivery()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        self.click("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        sleep(1)
        texts = [element.text for element in elements]
        text3 = ['自然灾害',
                 '因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] in texts[0], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'自然灾害')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'自然灾害')]")
        sleep(1)
        self.assert_text(text3[1],
                         "//div[@class='ant-legacy-form-extra']//div[contains(text(),'因洪水、台风、地震等灾害型事件，快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("受影响区域类型", "//label[@title='受影响区域类型']")
        self.assert_text("发货地",
                         "//label[@class='ant-radio-wrapper ant-radio-wrapper-checked']//span[contains(text(),'发货地')]")
        self.assert_text("选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单",
                         "//div[contains(text(),'选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单')]")
        self.assert_text("商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地",
                         "//div[contains(text(),'商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地')]")
        self.assert_text("收货地", "//span[contains(text(),'收货地')]")
        self.click("//span[contains(text(),'收货地')]")
        sleep(0.5)

        self.assert_text("受影响区域", "//label[@title='受影响区域']")
        self.assert_text("最多可选择50个区/县报备", "//div[contains(text(),'最多可选择50个区/县报备')]")
        sleep(0.5)
        self.click("//div[@class='ant-select-selection-overflow']")
        sleep(0.5)
        self.click("//div[@class='ant-select-tree-list-holder-inner']//div[1]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click(
            "//div[contains(@class,'ant-select-dropdown xrEl8HAvi4PYDbXL7UQ5 ant-tree-select-dropdown ant-select-dropdown-placement-bottomLeft')]//div[2]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click("//span[@title='永宁县']//span[1]")
        sleep(0.5)

        self.assert_text("发货受影响时间段", "//label[@title='发货受影响时间段']")
        self.click("//input[@id='reportingTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。",
                         "//div[contains(text(),'（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。')]")
        self.assert_text("（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。",
                         "//div[contains(text(),'（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。')]")
        self.assert_text("（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。",
                         "//div[contains(text(),'（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。')]")
        self.assert_text("（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货扣款。",
                         "//div[contains(text(),'（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货')]")

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.assert_text("报备证明图片", "//label[@title='报备证明图片']")
        self.is_element_clickable(
            "/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/form[1]/div[7]/div[2]/div[1]/span[1]/div[1]/div[1]/div[1]/span[1]/div[2]/div[2]/span[1]/div[1]/span[1]/div[1]/p[1]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 履约-收货/发货地报备-停电/着火
    @pytest.mark.p1
    @pytest.mark.skip
    def test_new_area_report_fire(self):
        self.to_delay_delivery()

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        self.click("//div[@class='CtI7VTvzuV1kcSvH4hkD']//div[1]//div[3]//button[1]//span[1]")
        # 进入新增报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        sleep(1)
        texts = [element.text for element in elements]
        text3 = ['停电/着火',
                 '因停电、着火导致商家无法按平台时效要求进行发货履约的场景']
        for i in range(len(text3)):
            assert text3[i] in texts[0], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'停电/着火')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'停电/着火')]")
        sleep(1)
        self.assert_text(text3[1],
                         "//div[@class='ant-legacy-form-extra']//div[contains(text(),'因停电、着火导致商家无法按平台时效要求进行发货履约的场景')]")

        self.assert_text("受影响区域类型", "//label[@title='受影响区域类型']")
        self.assert_text("发货地",
                         "//label[@class='ant-radio-wrapper ant-radio-wrapper-checked']//span[contains(text(),'发货地')]")
        self.assert_text("选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单",
                         "//div[contains(text(),'选择「受影响区域」后，系统会根据【运费模板】校验店铺内待发货且未发货超时的订单')]")
        self.assert_text("商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地",
                         "//div[contains(text(),'商家填写的「受影响区域」需与「运费模板发货地」及订单实际发货地址一致。请注意核实运费模板填写的发货地')]")
        self.assert_text("收货地", "//span[contains(text(),'收货地')]")
        self.click("//span[contains(text(),'收货地')]")
        sleep(0.5)

        self.assert_text("受影响区域", "//label[@title='受影响区域']")
        self.assert_text("最多可选择50个区/县报备", "//div[contains(text(),'最多可选择50个区/县报备')]")
        sleep(0.5)
        self.click("//div[@class='ant-select-selection-overflow']")
        sleep(0.5)
        self.click("//div[@class='ant-select-tree-list-holder-inner']//div[1]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click(
            "//div[contains(@class,'ant-select-dropdown xrEl8HAvi4PYDbXL7UQ5 ant-tree-select-dropdown ant-select-dropdown-placement-bottomLeft')]//div[2]//span[2]//span[1]//*[name()='svg']")
        sleep(0.5)
        self.click("//span[@title='永宁县']//span[1]")
        sleep(0.5)

        self.assert_text("发货受影响时间段", "//label[@title='发货受影响时间段']")
        self.click("//input[@id='reportingTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。",
                         "//div[contains(text(),'（1）请选择受影响订单的承诺发货时间段，报备通过后平台按照最晚发货时间考核。')]")
        self.assert_text("（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。",
                         "//div[contains(text(),'（2）报备仅支持在报备提交时已支付订单的报备，不含报备提交后新生成的订单。')]")
        self.assert_text("（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。",
                         "//div[contains(text(),'（3）若已使用协商发货时间或无法发货赔付工具，与买家协商成功或协商中，则不在延迟发货报备订单范围内。')]")
        self.assert_text("（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货扣款。",
                         "//div[contains(text(),'（4）因存在人工审核时长，建议最晚在承诺发货日期前1-2天进行报备，如因报备时间过晚可能导致延迟发货')]")

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.assert_text("报备证明图片", "//label[@title='报备证明图片']")
        self.is_element_clickable("//span[@aria-label='plus']//*[name()='svg']")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 履约-新增订单报备-消费者要求延迟发货-白名单中商家有excel入口'
    @pytest.mark.p0
    @pytest.mark.skip
    def test_new_delay_delivery_consumers_demand_add_white(self):
        '''
        https://kconf.corp.kuaishou.com/#/platecoDev/fulfillment/reportBuyerDemand_order_1
        '''

        """延迟发货报备页"""
        self.login("MERCHANT_DOMAIN", "member_account2")
        self.assert_title("快手小店")
        # self.sleep(3)
        self.close_confirm()

        # 保障
        time = 0
        while self.is_element_visible(
                "//div[@id='menu_item_H7aQVESsruU']//span[@class='seller-main-badge']//span[1]") == False and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
        self.click("//div[@id='menu_item_H7aQVESsruU']//span[@class='seller-main-badge']//span[1]")
        time = 0
        while (self.is_element_visible("//div[@id='rc-tabs-0-tab-delayReport']") == False and self.is_element_visible("//span[@id='menu_item_oyPhPEhgN6s']") == False) and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
            if time == 4:
                self.refresh()

        # '报备中心'
        self.click("//span[@id='menu_item_oyPhPEhgN6s']")
        self.sleep(2)
        self.refresh()

        time = 0
        while not self.is_element_visible("//div[@id='rc-tabs-0-tab-delayReport']") and time < 10:
            logger.info("页面未加载完成")
            time += 1
            sleep(2)
        assert self.is_element_clickable("//div[@id='rc-tabs-0-tab-delayReport']"), '延迟发货报备按钮不可点击'
        self.click('#rc-tabs-0-panel-delayReport')

        if 's.kwaixiaodian.com' in self.get_current_url():
            env = 'https://s.kwaixiaodian.com/'
        else:
            env = 'https://eshop-s.prt.kwaixiaodian.com/'

        self.wait_for_element("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
        self.click("//div[@class='csGB2vIGnyDlFoZw0OhJ']//div[2]//div[3]//button[1]//span[1]")
        # 进入新增订单报备
        self.wait_for_element("//label[@title='报备原因']")

        self.assert_text("报备原因", "//label[@title='报备原因']")
        self.click("//div[@class='ant-select-selector']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        sleep(1)
        texts = [element.text for element in elements]
        text3 = ['消费者要求延迟发货',
                 '因消费者主动要求延迟发货的场景，建议优先和消费者协商，协商一致后使用协商发货功能用户点击同意来推迟发货，无需平台审核。点击去协商发货 或 查看协商发货指南']
        for i in range(len(text3)):
            assert text3[i] in texts[0], '报备原因下拉选项错误'

        self.is_element_clickable("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'消费者要求延迟发货')]")
        self.click("//div[@class='vXu2jfW9sb20sDurPLwO'][contains(text(),'消费者要求延迟发货')]")
        sleep(1)
        self.click("//div[@class='ant-legacy-form-extra']//div//a[contains(text(),'点击去协商发货')]")
        self.switch_to_window(2)
        sleep(1)
        self.assert_url(env + 'zone/trade/delayship-report/list?tabId=negotiation')
        self.switch_to_window(1)

        self.click("//div[@class='ant-legacy-form-extra']//div//a[contains(text(),'查看协商发货指南')]")
        self.switch_to_window(3)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/bbs/web/article?id=20641&layoutType=4")
        self.switch_to_window(1)

        self.assert_text("订单添加方式", "//label[@title='订单添加方式']")
        self.click("//span[contains(text(),'选择订单')]")
        sleep(1)

        self.assert_text("选择订单", "//div[@class='ant-drawer-title']")
        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-alert-description')
        texts = [element.text for element in elements]
        text4 = ['1.系统已自动筛选出未超过承诺发货时间的订单，商家可选择状态为「可报备」的订单进行报备，最多可选择50条订单',
                 '2.当前系统仅展示近六个月内下单的订单。商家若想针对六个月前生成的订单报备，需在「订单编号」输入框中手动输入订单编号（支持同时输入多个订单编号，以空格区分）',
                 '3.订单报备仅支持同时报备最多50个订单，主要适用于订单收货信息不全等受影响订单较少的情况。若因不可抗力需要报备较多订单，请在「收货/发货地报备」模块进行报备']
        for i in range(len(text4)):
            assert text4[i] in texts[0], '选择订单抽屉公告错误'
        self.assert_text("订单编号", "//label[@title='订单编号']")
        self.assert_text("订单创建时间", "//label[@title='订单创建时间']")
        self.click("//input[@placeholder='开始时间']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("承诺发货时间", "//label[@title='承诺发货时间']")
        self.click("//span[@title='三天内需发货']")
        sleep(1)
        elements = self.driver.find_elements(By.CLASS_NAME, 'rc-virtual-list-holder-inner')
        texts = [element.text for element in elements]
        text5 = '24小时内需发货\n三天内需发货\n七天内需发货\n全部'
        assert text5 in texts[1], '选择订单抽屉承诺发货时间下拉选项错误'
        self.is_element_clickable("//span[contains(text(),'重置')]")
        self.is_element_clickable("//button[@type='submit']")

        elements = self.driver.find_elements(By.CLASS_NAME, 'ant-table-tbody')
        texts = [element.text for element in elements]
        assert len(texts[0]) > 1, '选择订单列表为空'

        self.is_element_clickable("//span[contains(text(),'确认')]")
        self.click("//button[@class='ant-btn nYhbEsBWvMGf7P2q9jAh']//span[contains(text(),'取消')]")
        # self.click("//*[name()='path' and contains(@d,'M216.32 80')]")
        sleep(1)

        self.assert_text("最晚发货时间", "//label[@title='最晚发货时间']")
        self.click("//input[@id='latestPromiseTime']")
        self.is_element_clickable("//button[contains(text(),'2025年')]")
        self.assert_text("报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。",
                         "//div[contains(text(),'报备订单需在最晚发货时间内完成发货，若在该时间内未完成发货，则属于延迟发货。')]")
        self.click("//a[contains(text(),'查看快手电商发货管理规则')]")
        self.switch_to_window(4)
        sleep(1)
        self.assert_url("https://edu.kwaixiaodian.com/rule/web/detail?id=fBRF7UxhU7")
        self.switch_to_window(1)

        self.assert_text("报备说明", "//label[@title='报备说明']")
        self.input("//textarea[@id='applyNote']", 'UI自动化测试')

        self.assert_text("报备证明图片", "//label[@title='报备证明图片']")
        self.is_element_clickable("//*[name()='path' and contains(@d,'M176 474h6')]")

        self.assert_text("报备证明材料", "//label[@title='报备证明材料']")
        self.is_element_clickable("//div[contains(text(),'点击或拖拽文件到此区域上传')]")

        self.is_element_clickable("//span[contains(text(),'提交报备')]")
        self.is_element_clickable("//span[contains(text(),'取消')]")

    # 新增延迟报备页 - 每个tab下的文案和页面展示内容
    @pytest.mark.p1
    @pytest.mark.skip
    def test_new_delay_reported_page(self):
        self.to_delay_delivery()

        # 「新增延迟发货报备」按钮
        assert self.is_element_clickable(".anticon.anticon-system-add-line"), "「新增延迟发货报备」按钮不可点击"
        self.click(".anticon.anticon-system-add-line")
        sleep(2)

        texts_mod=['适用范围：商家因国家/社会大型会议导致快递公司/网点对受影响地区快递明确停止收件，导致商家无法按平台时效要求进行发货履约的。',
                   '适用范围：商家因社会化赛事影响导致快递公司/网点对受影响地区内快递明确停止收件，导致商家无法按平台时效要求进行发货履约的。',
                   '适用范围：商家遇到如水灾、火灾、地震等灾害型事件，导致快递公司/网点对受影响地区内快递明确停止收件，商家无法按平台时效要求进行发货履约的。',
                   '适用范围：\n（1）商家受不可抗力因素影响需要通过商品ID报备的。',
                   '适用范围：\n（1）商家发货地/收货地受不可抗力因素影响发货的。\n（2）商家和消费者对订单发货时间存在另行约定的（注：若您已和买家协商一致，优先建议您通过客服工作台，向用户发起协商发货申请，用户同意后，平台会即刻按照新约定的时间进行考核，无需等待审核。操作指引见：协商发货操作指南）\n（3）若受恶意买家影响，您可前往举报中心进行举报。',
                   '适用范围：\n买家填写的收货地址中存在商家设置不配送或不包邮区域导致无法判断具体发往路向的，商家优先和买家协商解决，协商失败的可进行延迟发货报备。']
        self.click(".ant-select-selector")
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-select-item.ant-select-item-option')
        texts = [element.text for element in elements]
        for i in range(len(elements)):
            self.click(".ant-select-selector")
            sleep(1)
            elements[i].click()
            assert self.get_text(".ant-alert-content")==texts_mod[i], texts[i]+'tab下文案展示不一致\n'+self.get_text(".ant-alert-content")
            assert len(self.get_text(".JfvBKENCflx9HFxicjuH"))>=100, '页面展示不正常\n'+self.get_text(".JfvBKENCflx9HFxicjuH")
            elements_button = self.driver.find_elements(By.CSS_SELECTOR, '.ant-btn')
            texts_button = [element_button.text for element_button in elements_button]
            for j in range(len(elements_button)):
                if (elements_button[j].is_displayed() and elements_button[j].is_enabled()) == False:
                    assert False, texts_button[j] + '按钮不可点击'


# pc无法发货页模板渲染
class TestPcUnableDeliveryPage(BaseTestCase):
    # 无法发货赔付页面渲染
    @pytest.mark.p0
    @pytest.mark.skip
    def test_customerservice_unabledelivery_page(self):
        self.to_unable_delivery()
        sleep(1)
        # 判断顶部公告文案是否存在
        assert self.get_text("//span[@class='oskt0GT4Uv3bKIeCbXtP']")=="无法发货赔付操作指导", "操作指导标题错误"
        assert self.get_text("//div[@class='e4hIPbzNHmgZVKNDOCYg c5AYr8OdXH8fzfgeJErz']//div[1]//div[2]") == "什么是无法发货赔付", "操作指导错误"
        assert self.get_text("//div[@class='NCuQGmzGcsTsk53Iu0Gf']//div[2]//div[2]") == "无法发货赔付使用范围", "操作指导错误"
        assert self.get_text("//body/div[@id='main-root']/div[@class='seller-main-spin-nested-loading']/div[@class='seller-main-spin-container']/div[@id='main-pc-atmosphere-layout']/div[@id='main-pc-page-layout']/div[@class='HkCTsYZjM3B7nYatmuHR']/div[@class='js-page-content p9K58evEYBgCD6FuDIKq OIk4RvF5SyjZUH1tXi9j undefined']/div[@id='micro-viewport']/div[@id='__qiankun_microapp_wrapper_for_kwaishop_seller_trade_pc__']/div[@id='root']/div/div[@class='acmolUOLneIHB_myxuV9']/div[@class='ant-tabs ant-tabs-top ant-tabs-large']/div[@class='ant-tabs-content-holder']/div[@class='ant-tabs-content ant-tabs-content-top']/div[@id='rc-tabs-0-panel-unableDeliveryNegotiation']/div[@class='uYASJ2COv_xMYV7BjDBA']/div[@class='e4hIPbzNHmgZVKNDOCYg c5AYr8OdXH8fzfgeJErz']/div[@class='NCuQGmzGcsTsk53Iu0Gf']/div[3]/div[2]") == "如何操作无法发货赔付", "操作指导错误"
        assert self.get_text("//div[@id='rc-tabs-0-panel-unableDeliveryNegotiation']//div[4]//div[2]") == "如何查询无法发货赔付记录", "操作指导错误"

        assert self.get_text("//a[contains(text(),'点击查看无法发货赔付功能介绍')]") == "点击查看无法发货赔付功能介绍", "操作指导错误"
        assert self.get_text("//a[contains(text(),'点击查看无法发货赔付功能使用范围')]") == "点击查看无法发货赔付功能使用范围", "操作指导错误"
        assert self.get_text("//a[contains(text(),'点击查看如何单笔订单发起无法发货赔付')]") == "点击查看如何单笔订单发起无法发货赔付", "操作指导错误"
        assert self.get_text("//a[contains(text(),'点击查看如何批量订单发起无法发货赔付')]") == "点击查看如何批量订单发起无法发货赔付", "操作指导错误"
        assert self.get_text("//a[contains(text(),'点击查看如何查询无法发货赔付记录')]") == "点击查看如何查询无法发货赔付记录", "操作指导错误"
        assert self.is_element_clickable("//a[contains(text(),'点击查看无法发货赔付功能介绍')]"), "操作指导不可点击"
        self.click("//a[contains(text(),'点击查看如何查询无法发货赔付记录')]")
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.switch_to_window(0)

        assert self.is_element_clickable("//a[contains(text(),'点击查看无法发货赔付功能使用范围')]"), "操作指导不可点击"
        self.click("//a[contains(text(),'点击查看无法发货赔付功能使用范围')]")
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.switch_to_window(0)

        assert self.is_element_clickable("//a[contains(text(),'点击查看如何单笔订单发起无法发货赔付')]"), "操作指导不可点击"
        self.click("//a[contains(text(),'点击查看如何单笔订单发起无法发货赔付')]")
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.switch_to_window(0)

        assert self.is_element_clickable("//a[contains(text(),'点击查看如何批量订单发起无法发货赔付')]"), "操作指导不可点击"
        self.click("//a[contains(text(),'点击查看如何批量订单发起无法发货赔付')]")
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.switch_to_window(0)

        assert self.is_element_clickable("//a[contains(text(),'点击查看如何查询无法发货赔付记录')]"), "操作指导不可点击"
        self.click("//a[contains(text(),'点击查看如何查询无法发货赔付记录')]")

        self.assert_url("https://docs.qingque.cn/d/home/<USER>")
        self.switch_to_window(0)

        assert self.is_element_visible("//label[@title='订单号']"), "订单号输入框不存在"
        assert self.is_element_visible("//label[@title='协商路径']"), "订单号输入框不存在"
        assert self.is_element_visible("//label[@title='无法发货原因']"), "订单号输入框不存在"
        assert self.is_element_visible("//label[@title='协商状态']"), "订单号输入框不存在"
        assert self.is_element_visible("//label[@title='发起协商时间']"), "订单号输入框不存在"
        assert self.is_element_clickable("//span[contains(text(),'重置')]"), "重置不可点击"
        assert self.is_element_clickable("//button[@type='submit']"), "查询不可点击"
        assert self.is_element_clickable("//span[contains(text(),'批量发送无法发货赔付协商')]"), "按钮不可点击"
        assert self.is_element_clickable("//span[contains(text(),'查看导出报表')]"), "按钮不可点击"
        assert self.is_element_clickable("//span[contains(text(),'导出协商记录')]"), "按钮不可点击"

        elements = self.driver.find_elements(By.CSS_SELECTOR, '.ant-table-thead')
        texts = [element.text for element in elements]
        assert texts[0]=='协商流水号 协商路径 发起协商时间 协商完成时间 订单号 协商状态 无法发货原因 赔付金额', "协商记录流水列表错误"

    # 无法发货赔付-导出协商
    @pytest.mark.p0
    @pytest.mark.skip
    def test_customerservice_unabledelivery_export(self):
        self.to_unable_delivery()
        sleep(1)

        self.click("//span[contains(text(),'导出协商记录')]")
        sleep(1)
        self.assert_text("确认导出当前查询的协商记录？", "//span[@class='ant-modal-confirm-title']")
        self.assert_text(
            "1. 为了保证查询性能，两次导出的时间间隔请保持在5分钟以上\n2. 生成报表后，7天内可以下载\n3. 每次最多导出100万条记录，若超出数量限制，则只导出最近的100万条\n4. 仅支持导出协商结果为「用户已拒绝」「超时未处理协商自动失败」的记录",
            "//div[@class='ant-modal-confirm-content']//div[1]")
        assert self.is_element_clickable("//span[contains(text(),'导 出')]"), "按钮不可点击"
        assert self.is_element_clickable("//span[contains(text(),'取 消')]"), "按钮不可点击"

    # 无法发货赔付-查看导出报表
    @pytest.mark.p0
    @pytest.mark.skip
    def test_customerservice_unabledelivery_watchexport(self):
        self.to_unable_delivery()
        sleep(1)

        self.click("//span[contains(text(),'查看导出报表')]")
        sleep(1)
        self.assert_text("无法发货赔付协商记录导出报表\n只支持下载最近7天的报表", "//div[@class='VxxQeRCv5GIQAqPtxUTu']")
        assert self.is_element_clickable("//*[name()='path' and contains(@d,'M216.32 80')]"), "关闭按钮不可点击"
        assert self.is_element_clickable("//span[@aria-label='system-refresh-line']//*[name()='svg']"), "刷新按钮不可点击"
        self.assert_text("导出时间：", "//div[@class='dw4IhZobkrc10dyqInb8']//div[1]//div[1]")
        self.assert_text("操作人：", "//div[@class='w9vEWoxdILrawD4PNcki']//div[2]//div[1]")
        self.assert_text("订单编号", "//div[contains(text(),'订单编号')]")
        self.assert_text("无法发货原因", "//div[@class='gmDb2H_Rmcmy02zw4jND']//div[2]//div[1]//div[1]")
        self.assert_text("协商状态", "//div[@class='ant-drawer ant-drawer-right ant-drawer-open dhBowQOn2pJtsK3slRxl']//div[3]//div[1]//div[1]")
        self.assert_text("协商路径", "//div[@class='ant-drawer ant-drawer-right ant-drawer-open dhBowQOn2pJtsK3slRxl']//div[4]//div[1]//div[1]")
        self.assert_text("发起协商时间", "//div[5]//div[1]//div[1]")
        assert self.is_element_clickable("//span[contains(text(),'下载报表')]"), "按钮不可点击"

    # 无法发货赔付-「批量发送无法发货协商」页面
    @pytest.mark.p0
    @pytest.mark.skip
    def test_customerservice_batch_unabledelivery(self):
        self.to_unable_delivery()
        sleep(1)

        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if (int(formatted_time[11:13]) >= 23 or int(formatted_time[11:13]) < 8):
            assert self.is_element_clickable("//span[contains(text(),'批量发送无法发货赔付协商')]"), "「批量发送无法发货赔付协商」按钮不可点击"
            self.hover("//span[contains(text(),'批量发送无法发货赔付协商')]")
            assert self.get_text(
                "//div[contains(text(),'每日08:00~23:00可操作')]") == '每日08:00~23:00可操作', "「批量发送无法发货赔付协商」按钮夜间异常"
        else:
            self.click("//span[contains(text(),'批量发送无法发货赔付协商')]")
            self.assert_text("批量发送无法发货赔付协商", ".ant-modal-title")
            self.assert_text("针对高价值用户建议您先联系消费者协商发货时间或进行退款，用户不同意，再通过订单列表与买家发起会话并发送无法发货赔付卡片", "//div[@class='ant-modal-root']//div[@class='ant-alert-content']//div[1]")
            self.assert_text("流程说明", "//body/div/div[@class='ant-modal-root']/div[@role='dialog']/div[@role='document']/div[@class='ant-modal-content']/div[@class='ant-modal-body']/div[2]")
            self.assert_text("下载模板", "//div[@class='xpnaKOhLpx3tbZ9OFUHS']//div[1]//div[1]//div[1]")
            self.assert_text("点击「下载批量导入模板」，将模板文件下载至您的电脑", "//div[@class='xpnaKOhLpx3tbZ9OFUHS']//div[1]//div[1]//div[2]")
            assert self.is_element_clickable("//a[contains(text(),'下载批量导入模板')]"), "下载批量导入模板"
            self.assert_text("填写信息", "//div[@class='xpnaKOhLpx3tbZ9OFUHS']//div[3]//div[1]//div[1]")
            self.assert_text("根据模板注意事项以及示例正确填写订单号、无法发货原因", "//div[@class='xpnaKOhLpx3tbZ9OFUHS']//div[3]//div[1]//div[2]")
            self.assert_text("导入批量无法发货文件", "//div[@class='xpnaKOhLpx3tbZ9OFUHS']//div[5]//div[1]//div[1]")
            self.assert_text("将填写后的文件上传至下方指定位置，完成后系统将自动进行校验，请耐心等待", "//div[@class='xpnaKOhLpx3tbZ9OFUHS']//div[5]//div[1]//div[2]")
            self.assert_text("检查文件处理结果", "//div[7]//div[1]//div[1]")
            self.assert_text("请及时查看文件校验结果，校验失败请下载失败明细并根据提示原因修改后重新上传", "//div[7]//div[1]//div[2]")

            self.assert_text("导入批量无法发货赔付协商文件", "//body/div/div[@class='ant-modal-root']/div[@role='dialog']/div[@role='document']/div[@class='ant-modal-content']/div[@class='ant-modal-body']/div[4]")
            self.assert_text("点击或拖拽文件到此区域上传", "//div[@class='qZXvYdFpkctrNmlR3PDw']")
            self.assert_text("支持xls、xlsx格式文件上传，一次最多上传3个文件，每个文件不超过5000行", "//div[@class='lsQ3lD6W9Z1XJ6a6VRhC']")
            self.assert_text("文件处理结果", "//div[@class='ITIHkLO3Ec6MQckcFVyQ']")
            self.assert_text("仅展示最近三个月的记录", "//div[@class='kLSXixF79_XQF8GzVJSQ']")
            assert self.is_element_clickable("//*[name()='path' and contains(@d,'M170.54 28')]"), "刷新按钮不可点击"

            self.assert_text("文件名称", "//th[contains(text(),'文件名称')]")
            self.assert_text("校验状态", "//th[contains(text(),'校验状态')]")

            self.assert_text("上传时间", "//th[contains(text(),'上传时间')]")
            self.assert_text("完成时间", "//th[contains(text(),'完成时间')]")
            self.assert_text("成功订单 (条)", "//th[contains(text(),'成功订单 (条)')]")
            self.assert_text("失败订单 (条)", "//th[contains(text(),'失败订单 (条)')]")
            self.assert_text("协商赔付金额", "//th[contains(text(),'协商赔付金额')]")
            self.assert_text("操作人", "//th[contains(text(),'操作人')]")
            self.assert_text("操作", "//th[contains(text(),'操作')]")

# 物流工具
class TestPcLogisticsToolsPage(BaseTestCase):
    # 物流工具-识别码查询
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_logistics_tools_sn_query(self):
        self.login('MERCHANT_DOMAIN', 'order_account')
        # self.sleep(3)
        self.close_confirm()

        # 订单
        if self.is_element_visible("//div[@id='menu_item_R553YgpLnhQ']//span[@class='seller-main-badge']//span[1]"):
            self.click("//div[@id='menu_item_R553YgpLnhQ']//span[@class='seller-main-badge']//span[1]")
            time = 0
            while self.is_element_visible(
                    "//div[@id='menu_item_H7aQVESsruU']//span[@class='seller-main-badge']//span[1]") == False and time < 10:
                logger.info("页面未加载完成")
                time += 1
                sleep(1)

        # 点击物流工具
        if self.is_element_visible("//span[@id='menu_item_0TvWDFe0-Ws']"):
            self.click("//span[@id='menu_item_0TvWDFe0-Ws']")
        else:
            self.click("//span[@id='pinned_menu_for_intersectionObserver_0TvWDFe0-Ws_under_R553YgpLnhQ']")
        self.sleep(1)
        # 点击识别码查询
        self.click("//div[@id='rc-tabs-0-tab-4']")
        # 公告
        self.assert_text("查询结果仅供参考，最终是否有效请以实际发货结果为准。识别码具体要求请见：", "//div[contains(text(),'查询结果仅供参考，最终是否有效请以实际发货结果为准。识别码具体要求请见：')]")
        self.assert_text("大牌大补订单校验商品识别码公告", "//a[@target='_blank']")
        self.click("//a[@target='_blank']")
        self.switch_to_window(-1)
        self.assert_url("https://docs.qingque.cn/d/home/<USER>")

        # 关闭当前页，还要switch_to_window(-1)定位下
        self.driver.close()
        self.switch_to_window(-1)

        self.assert_text("类目", "//label[@title='类目']")
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.kwaishop-trade-fufill-identification-code-pc-select-selector')
        texts = [element.text for element in elements]

        text0 = ['请选择类目', '请选择品牌', '请选择码类型']
        text1 = ['智能手表', '平板电脑', '智能手机', '笔记本电脑', '茅台', '贵州茅台']
        text2 = ['iQOO', 'OPPO', 'vivo', 'W&O', '爱国者', '华为/HUAWEI', '苹果/Apple', '荣耀/honor', '三星/SAMSUNG', '小霸王', '小米/MI', '小天才', '一加/OnePlus', '红米/REDMI', '联想/LENOVO', '努比亚/nubia', '金立/GIONEE', '魅族/MEIZU', '玩家国度/ROG', '真我/realme', '戴睿/DERE', '华硕/ASUS', '惠普/HP', '机械革命/MECHREVO', '雷神/THUNDEROBOT', '七彩虹/COLORFUL', '神舟/HASEE', '吾空/WOOKING', '茅台', '贵州茅台']
        text3 = ['SN码', 'IMEI码']
        text_total = [text1, text2, text3]
        path_total = ["//div[@title='{}']//div[1]", "//div[@class='kwaishop-trade-fufill-identification-code-pc-select-item kwaishop-trade-fufill-identification-code-pc-select-item-option kwaishop-trade-fufill-identification-code-pc-select-item-option-active']//div[@class='kwaishop-trade-fufill-identification-code-pc-select-item-option-content'][contains(text(),'{}')]"]

        for i in range(len(text0)):
            elements[texts.index(texts[i])].click()

            for j in range(len(text_total[i])):
                path = path_total[0].format(text_total[i][j])
                if self.is_element_clickable(path) == False:
                    path = path_total[1].format(text_total[i][j])
                if self.get_text(path) != text_total[i][-1]:
                    self.hover_down1(1)
                else:
                    self.click(path)

        self.add_text("//textarea[@placeholder='批量查询时请使用“,“间隔，单次最多100个']", "PCUItest")
        self.assert_text("PCUItest", "//textarea[@placeholder='批量查询时请使用“,“间隔，单次最多100个']")

        self.is_element_clickable("//button[@class='kwaishop-trade-fufill-identification-code-pc-btn']//span[contains(text(),'重 置')]")
        self.click("//button[@class='kwaishop-trade-fufill-identification-code-pc-btn kwaishop-trade-fufill-identification-code-pc-btn-secondary']//span[contains(text(),'查 询')]")
        self.sleep(2)
        elements = self.driver.find_elements(By.CSS_SELECTOR, '.kwaishop-trade-fufill-identification-code-pc-table-cell')
        texts = [element.text for element in elements]
        text = ['识别码', '类目', '品牌', '码类型', '是否可用', '原因', 'PCUItest', '贵州茅台', '贵州茅台', 'IMEI码', '不可用', '识别码错误']
        for i in range(len(text)):
            assert text[i] in texts, '记录错误'