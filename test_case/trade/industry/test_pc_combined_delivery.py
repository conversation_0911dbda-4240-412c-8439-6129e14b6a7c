import time
import logging
import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from page_objects.trade_order.trade_order_page import TradeOrderPage
from selenium.webdriver.common.by import By
import re


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)

# 合并发货抽屉页
class TestPcCombinedShipping(BaseTestCase):

    # def to_order_list(self):
    # self.login("MERCHANT_DOMAIN", "order_account")
    # self.assert_title("快手小店")
    # sleep(2)
    # self.open_order_list()

    # 合并发货抽屉页「整单发货」tab
    @pytest.mark.p0
    def test_ship_whole_order_tab(self):
        self.to_combined_shipping()
        # copy selector
        # self.click('#orderPC > section > section > main > div > div > div.ke6lSkPWtH3QNlxE9dSS > div.ant-pro-list.NbuuNVqz_NP5Jx2yR5N0 > div > div:nth-child(1) > div > div.ant-space.ant-space-vertical > div > div > div.ant-pro-toolbar-table-actions > div > button:nth-child(3) > span')
        # copy Xpath
        # self.click('//*[@id="mergeship"]/span')
        self.wait_for_element(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        # 判断选择框是否存在
        self.wait_for_element("//*[@id=\"express-from\"]/div/div[2]/div/div[2]")
        # 判断整单发货按钮是否存在
        # self.assertEquals(self.is_element_clickable('div.kpro-delivery-goods--title'), True)
        elements = self.driver.find_elements(By.CLASS_NAME, 'kpro-delivery-goods--title')
        texts = [element.text for element in elements]
        assert '整单发货' in texts, "整单发货不存在"

        # button_text = self.get_text('/html/body/div[3]/div/div[2]/div/div/div[2]/div/div/div[5]/div[2]/div/div[1]/label/span[2]/div[1]')
        # assert button_text == "整单发货", '整单发货按钮文字对不上'

        # 判断关闭按钮是否存在
        assert self.is_element_visible("//span[@aria-label='system-close-small-line']//*[name()='svg']"), "关闭按钮不存在"

    # 合并发货抽屉页「拆单发货」
    @pytest.mark.p0
    # @pytest.mark.skip
    def test_ship_split_order_tab(self):
        """合并发货页"""
        self.login("MERCHANT_DOMAIN", "logistics_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.close_confirm()
        self.open_order_list_checking_order()
        self.close_confirm()
        self.sleep(2)
        self.wait_for_element("#mergeship")
        assert self.is_element_clickable("#mergeship"), '合并发货按钮不存在'
        self.click("#mergeship")
        self.close_confirm()

        sleep(1)
        self.wait_for_element(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        self.wait_for_element("//span[contains(text(),'取 消')]")
        self.close_confirm()

        # self.assertEquals(self.is_element_clickable(
        #     '/html/body/div[2]/div/div[2]/div/div/div[2]/div/div/div[5]/div[2]/div/div[2]/label/span[1]'), True)
        self.wait_for_element(".kpro-delivery-goods--title")
        elements = self.driver.find_elements(By.CLASS_NAME, 'kpro-delivery-goods--title')
        texts = [element.text for element in elements]
        assert '拆单发货' in texts, "拆单发货不存在"

        # button_text = self.get_text(
        # '/html/body/div[3]/div/div[2]/div/div/div[2]/div/div/div[5]/div[2]/div/div[2]/label/span[2]/div[1]')
        # assert button_text == "拆单发货", '拆单发货按钮文字对不上'

        self.click("//div[@class='kpro-delivery-goods--title'][contains(text(),'拆单发货')]")
        text = self.get_text(
        '//*[@id="deliveryForm"]/div[1]/div[3]/div[1]/div/div[1]/div/div[1]/label')
        assert text == "关联商品", '关联商品文字对不上'
        # 判断订单选择框是否存在
        # assert self.is_element_visible("//input[@id='deliveryForm_packList_0_shippedOrderList_0_mainOrderId']"), "订单选择框不存在"
        # 判断商品选择框是否存在
        assert self.is_element_visible('//*[@id="deliveryForm"]/div[1]/div[3]/div[1]/div/div[2]/div/div/div/div'), "商品选择框不存在"

        # 判断添加关联商品按钮是否存在
        assert self.is_element_visible(
        "div[class='kpro-delivery-goods--addGoodsBtn']"), "添加关联商品按钮不存在"
        self.click("div[class='kpro-delivery-goods--addGoodsBtn']")

        # 判断删除关联商品按钮是否存在
        # assert self.is_element_visible("//div[@class='ant-spin-container']//div[1]//div[3]//div[1]//div[1]//div[4]//div[1]//div[1]//div[1]//div[1]//span[1]//*[name()='svg']//*[name()='path' and contains(@d,'M10 5h4a1 ')]"), "删除关联商品按钮不存在"

        # 判断添加包裹按钮是否存在
        assert self.is_element_visible(
        "button[style='margin-top: 4px;']"), "添加包裹按钮不存在"

        # 判断删除包裹按钮是否存在
        # assert self.is_element_visible(
        # "span[class='anticon anticon-SystemCloseSmallLine kpro-delivery-goods--closeIcon']"), "删除包裹按钮不存在"

    # 合并发货抽屉页「物流单号」输入框
    @pytest.mark.p0
    def test_logistics_number_input_box(self):
        self.to_combined_shipping()
        # copy selector
        # self.click('//*[@id="mergeship"]/span')
        # copy Xpath
        # self.click_xpath('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[3]/span')
        sleep(1)
        self.wait_for_element(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        self.wait_for_element("//span[contains(text(),'取 消')]")
        # 判断输入框是否存在
        assert self.is_element_visible('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input'), "输入框不存在"
        self.add_text('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input', "测试填写")
        res = self.get_text('//*[@id="express-from"]/div/div[1]/div/div[2]/div/div/input')
        assert res == "测试填写", '输入框输入错误'

    # 合并发货抽屉页「物流公司」选择框
    @pytest.mark.p0
    def test_logistics_company_selection_box(self):
        self.to_combined_shipping()
        # copy selector
        # self.click('//*[@id="mergeship"]/span')
        # copy Xpath
        # self.click_xpath('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[3]/span')
        sleep(1)
        self.wait_for_element(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)

        # 判断选择框是否存在
        self.wait_for_element("//*[@id=\"express-from\"]/div/div[2]/div/div[2]")
        assert self.is_element_visible('//*[@id="express-from"]/div/div[2]/div/div[2]'), "选择框不存在"
        res = self.get_text('//*[@id="express-from"]/div/div[2]/div/div[1]/label')
        assert res == "物流公司", '物流公司文字错误'


    # 合并发货抽屉页「取消」按钮
    @pytest.mark.p0
    def test_cancel_button(self):
        self.to_combined_shipping()
        # copy selector
        # self.click('//*[@id="mergeship"]/span')
        # copy Xpath
        # self.click_xpath('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[3]/span')
        sleep(1)
        self.wait_for_element("//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click("//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        self.wait_for_element("//span[contains(text(),'取 消')]")
        # 判断取消按钮是否存在
        assert self.is_element_visible("//span[contains(text(),'取 消')]"), "取消按钮不存在"

        # button_text = self.get_text('/html/body/div[2]/div/div[2]/div/div/div[3]/div/button[1]/span')
        # assert button_text == "取消", '取消按钮文字对不上'

    # 合并发货抽屉页「确认」按钮
    @pytest.mark.p0
    def test_confirm_button(self):
        self.to_combined_shipping()
        # copy selector
        # self.click('//*[@id="mergeship"]/span')
        # copy Xpath
        # self.click_xpath('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[3]/span')
        sleep(1)
        self.wait_for_element(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        sleep(1)
        self.wait_for_element("//span[contains(text(),'取 消')]")
        # 判断确认按钮是否存在
        assert self.is_element_visible("//span[contains(text(),'确 认')]"), "确认按钮不存在"
        #
        # button_text = self.get_text('/html/body/div[2]/div/div[2]/div/div/div[3]/div/button[2]/span')
        # assert button_text == "确认", '确认按钮文字对不上'

    # 合并发货抽屉页 订单号「复制」按钮
    @pytest.mark.p0
    def test_copy_button(self):
        self.to_combined_shipping()
        # copy selector
        # self.click_xpath('//*[@id="mergeship"]/span')
        # copy Xpath
        # self.click_xpath('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[1]/div/div[2]/div/div/div[2]/div/button[3]/span')
        sleep(1)
        self.wait_for_element(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        # 判断选择框是否存在
        self.wait_for_element("//*[@id=\"express-from\"]/div/div[2]/div/div[2]")
        # 判断复制按钮是否存在
        assert self.is_element_clickable("//body//div//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div//div[1]//div[1]//div[2]//div[1]//div[1]//div[2]//span[1]//*[name()='svg']"), "复制按钮不存在"

    # 「合并发货」按钮（订单列表页）p0
    @pytest.mark.p0
    def test_order_list_mergeship_button(self):
        self.to_combined_shipping()

    # # 「查看教程」按钮p1
    # @pytest.mark.p1
    # def test_mergeship_view_tutorial_button(self):
    #     '''
    #     发货中心上线 合并发货老页面的「查看教程」按钮已不可见
    #     '''
    #     self.to_combined_shipping()
    #     # sleep(3)
    #     assert self.is_element_visible(".sc-furwcr.ivJqBN"), "查看教程按钮不存在"


    # 列表展示内容（最好主赠品展示都check）p0
    @pytest.mark.p0
    def test_mergeship_list_show(self):
        self.to_combined_shipping()
        sleep(3)
        self.wait_for_element("//div[@class='kwaishop-trade-shipping-center-component-pc-spin-container']//div[1]//div[1]//div[1]//button[1]//span[1]")
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-shipping-center-component-pc-list-items')
        texts = [element.text for element in elements]
        assert '收货地址' in texts[0], "收货地址不存在"
        assert '订单编号' in texts[0], "订单编号不存在"
        assert '订单创建时间' in texts[0], "订单创建时间不存在"
        assert '商品信息' in texts[0], "商品信息不存在"
        assert '商品金额' in texts[0], "商品金额不存在"
        assert '待发货商品数量' in texts[0], "待发货商品数量不存在"
        # assert self.is_element_visible("span[class='item-tag']"), "赠品标签不存在"

    # 订单编号-点击跳转订详p1
    @pytest.mark.p1
    def test_mergeship_click_num(self):
        self.to_combined_shipping()
        # sleep(3)
        elements = self.driver.find_elements(By.CLASS_NAME, 'kwaishop-trade-shipping-center-component-pc-table-cell')
        texts = [element.text for element in elements]
        # 存在'2433802082305178\n顺丰包邮' 这种情况 正则表达式匹配前面的数字
        oid = re.match(r'\d+', texts[5]).group(0)

        path = "//a[normalize-space()='{}']".format(oid)
        assert self.is_element_visible(path), "订单编号按钮不存在"
        self.click(path)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])

        self.wait_for_element("//th[contains(text(),'商品信息')]")
        url = self.get_current_url()
        assert 'order/detail' in url, "跳转订详失败!跳转至："+url
        # assert self.is_element_present('#seller-main-pc-header > div > div > div.R2pelbq1XUGwZ1BfAx5_ > img'), '快手小店左上角logo无展示'
        # elements = self.driver.find_elements(By.CSS_SELECTOR, '#orderPC')
        # texts = [element.text for element in elements]
        # assert '商品信息' in texts[0], "商品信息不存在"

    # 商品信息-点击跳转商品详情页p1
    @pytest.mark.p1
    def test_mergeship_click_product(self):
        self.to_combined_shipping()
        self.wait_for_element(".item-title")
        assert self.is_element_visible(".item-title"), "商品信息不存在"
        self.click('.item-title')
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        sleep(6)
        self.wait_for_element("//div[@class='title___xpzAB']")
        assert self.is_element_present("//img[@alt='logo']"), '快手小店左上角logo无展示'
        assert '商品详情' == self.get_text("//div[@class='title___xpzAB']"), "商品详情不存在,\n" + self.get_text(
            "//div[@class='r0m914o6IW7LNZxsgiUV']")
        # assert '商品状态：' == self.get_text(
        #     "//div[@id='DetailOperatingArea']//div//div[1]//div[1]//div[1]//div[1]"), "商品详情不存在,\n" + self.get_text(
        #     "//div[@id='DetailOperatingArea']//div//div[1]//div[1]//div[1]//div[1]")
        # assert '商品Id：' == self.get_text(
        #     "//div[@id='DetailOperatingArea']//div[3]//div[1]//div[1]//div[1]"), "商品详情不存在,\n" + self.get_text(
        #     "//div[@id='DetailOperatingArea']//div[3]//div[1]//div[1]//div[1]")
