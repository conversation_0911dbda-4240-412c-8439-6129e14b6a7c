# from time import sleep, time
# from unittest import skip, skipIf
# from ddt import ddt, data, unpack
# from common.Goods.goodsBase import BaseTestCase as BaseCase
#
# from constant.account import get_account_info
# from constant.domain import get_domain
# from test_case.trade.base import BaseTestCase
import time
from time import sleep
from unittest import skip

from selenium.webdriver.common.by import By
from seleniumbase.fixtures.page_actions import wait_for_element

from page_objects.industry.industry_page import EticketPage
from ..base import BaseTestCase

class TestLocallifePage(BaseTestCase):
#
    @skip
    def test_eletric_bike_verify(self):
        """
        电动车卡券核销链路验证/由于电动车卡券类目不支持冲正，所以用一单废一单，没那么多订单支持，先skip吧；需要的时候再打开
        """
        self.login("MERCHANT_DOMAIN", "locallife_account")
        self.driver.get("https://s.kwaixiaodian.com/zone/trade/voucher/verify-list")
        sleep(2)
        verify_button = "//div[contains(@class, 'UsefRhVUnDuV_REg9aNl')]//button[3]//span[contains(text(),'核销')]"
        self.click(verify_button)
        sleep(2)
        once_verify_button = "//span[contains(text(),'单个核销')]"
        self.click(once_verify_button)
        sleep(1)
        ticket_input = "//*[@id='eTicketNo']"
        self.input(ticket_input, "5BCE1A2ADFB30F24")
        query_button = "//button[@class='ant-btn ant-btn-primary ant-btn-lg ant-input-search-button']//span[contains(text(),'查询')]"
        self.click(query_button)
        sleep(1)

        select_store = "//span[@class='ant-select-selection-search']"
        self.click(select_store)
        time.sleep(1)
        select_stores = "hq6czLphv4rJnxpH9hzP"
        elements = self.driver.find_elements(By.CLASS_NAME, select_stores)
        texts = [element.text for element in elements]
        elements[texts.index('暂不选择')].click()
        sleep(1)

        self.input('#carNo', '123456789098765')

        #点击确认按钮
        confirm_button = "//button[@class ='ant-btn ant-btn-primary']//span[contains(text(), '确定')]"
        self.click(confirm_button)
        #券码已核销无法再次核销，请核实后重试
        has_verified_toast = "//span[contains(text(), '券码已核销无法再次核销，请核实后重试')]"
        self.wait_for_element(has_verified_toast)


    def test_ticket_verify(self):
        """
        电子卡券核销链路验证
        """
        ticket_no = "8CD6869CCD042FA9"
        self.login("MERCHANT_DOMAIN", "locallife_account")
        online_path = "https://s.kwaixiaodian.com/zone/trade/voucher/verify-list"
        prt_path = "https://prt-eshop-s.test.gifshow.com/zone/trade/voucher/verify-list"
        curr_path = online_path
        if self.var1 == "prt":
            curr_path = prt_path
        self.driver.get(curr_path)
        sleep(2)
        verify_button = "//div[contains(@class, 'UsefRhVUnDuV_REg9aNl')]//button[3]//span[contains(text(),'核销')]"
        self.click(verify_button)
        sleep(2)
        once_verify_button = "//span[contains(text(),'单个核销')]"
        self.click(once_verify_button)
        sleep(1)
        ticket_input = "//*[@id='eTicketNo']"
        self.input(ticket_input, ticket_no)
        query_button = "//button[@class='ant-btn ant-btn-primary ant-btn-lg ant-input-search-button']//span[contains(text(),'查询')]"
        self.click(query_button)
        sleep(1)

        select_store = "//input[@id='storeId']"
        self.click(select_store)
        time.sleep(1)
        select_stores = "hq6czLphv4rJnxpH9hzP"
        elements = self.driver.find_elements(By.CLASS_NAME, select_stores)
        texts = [element.text for element in elements]
        elements[texts.index('暂不选择')].click()
        sleep(1)

        # 点击确认按钮
        confirm_button = "//button[@class ='ant-btn ant-btn-primary']//span[contains(text(), '确定')]"
        self.click(confirm_button)
        # 券码已核销无法再次核销，请核实后重试
        has_verified_toast = "//span[contains(text(), '核销成功')]"
        self.wait_for_element(has_verified_toast)

        #卡券撤销核销
        assert EticketPage.revert_verify_button, "撤销核销按钮可见"
        self.click(EticketPage.revert_verify_button)
        sleep(2)
        self.click(EticketPage.revert_verify_hover_button)
        sleep(1)
        self.input(EticketPage.revert_verify_input, ticket_no)
        self.click(EticketPage.query_button)
        self.wait_for_element(EticketPage.revert_verify_num)
        self.wait_for_element(EticketPage.revert_verify_nums_label)
        self.click(EticketPage.revert_verify_sure_button)
        self.wait_for_element(EticketPage.revert_verify_toast)

