import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderListSelectionPRT(BaseTestCase):
    """
    对订单详情进行筛选的按钮
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    # 【全 部】按钮
    @pytest.mark.p0
    def test_all_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ALL_ORDER_BUTTON)
        self.assert_no_404_errors()

    # 【待付款】按钮
    @pytest.mark.p0
    def test_awaiting_payment_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.AWAITING_PAYMENT_ORDER_BUTTON)
        self.assert_no_404_errors()

    # 【待发货】按钮
    @pytest.mark.p0
    def test_awaiting_shipment_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        self.assert_no_404_errors()

    # 【已发货】按钮
    @pytest.mark.p0
    def test_shipment_dispatched_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.SHIPMENT_DISPATCHED_ORDER_BUTTON)
        self.assert_no_404_errors()

    # 【已收货】按钮
    @pytest.mark.p0
    def test_received_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.RECEIVED_ORDER_BUTTON)
        self.assert_no_404_errors()

    # 【交易成功】按钮
    @pytest.mark.p0
    def test_successful_transaction_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.SUCCESSFUL_TRANSACTION_ORDER_BUTTON)
        self.assert_no_404_errors()

    # 【订单关闭】按钮
    @pytest.mark.p0
    def test_closed_order(self):
        self.login_prt_env()
        start_time = time.time()
        self.click(TradeOrderPage.CLOSED_ORDER_BUTTON)
        end_time = time.time()
        print(" > click(TradeOrderPage.CLOSED_ORDER_BUTTON) time = {}".format(end_time - start_time))
        start_time = time.time()
        self.assert_no_404_errors()
        end_time = time.time()
        print(" > assert_no_404_errors() time = {}".format(end_time - start_time))

    # 待发货订单【下单/发货时间】下拉框
    @pytest.mark.p0
    def test_payment_delivery_time_selector(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        # 点击【下单/发货时间】下拉框
        self.click_xpath(TradeOrderPage.PAYMENT_DELIVERY_TIME_SELECTOR)
        selector_values = ["最新下单时间在上", "最新下单时间在下", "最早发货超时在上", "最早发货超时在下"]
        selector_content_value_prefix = '/html/body/div[19]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(selector_values)):
            curr_value_path = selector_content_value_prefix + str(i + 1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_path):
                curr_value = self.get_text(curr_value_path, by='xpath')
                self.assertEquals(curr_value, selector_values[i])
        self.assert_no_404_errors()

    # 待发货订单【部分发货】勾选框
    @pytest.mark.p0
    def test_partial_delivery_checkbox(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        # 点击【部分发货】勾选框
        self.click_xpath(TradeOrderPage.PARTIAL_DELIVERY_CHECKBOX)
        self.assert_no_404_errors()

    # 【在线寄件】按钮
    @pytest.mark.p0
    def test_online_delivery(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ONLINE_DELIVERY_BUTTON)
        self.assert_equal(self.get_current_url().__contains__('zone/electronic-sheet/online-parcel-sending'), True,
                          msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 【导出数据】按钮
    @pytest.mark.p0
    def test_order_batch_export(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDER_BATCH_EXPORT_BUTTON)
        self.assert_no_404_errors()

    # 【查看已导出报表】按钮
    @pytest.mark.p0
    def test_view_exported_report(self):
        self.login_prt_env()
        self.click(TradeOrderPage.VIEW_EXPORTED_REPORT_BUTTON)
        sleep(2)
        self.switch_to_window(1)
        self.assert_equal(self.get_current_url().endswith('/zone/order/exportList'), True,
                          msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 【合并发货】按钮
    @pytest.mark.p0
    def test_combined_shipment(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.COMBINED_SHIPMENT_BUTTON)
        sleep(2)
        self.switch_to_window(1)
        self.assert_equal(self.get_current_url().endswith('/zone/order/ship'), True,
                          msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 【批量发货】按钮
    @pytest.mark.p0
    def test_batch_shipment(self):
        self.login_prt_env()
        self.click(TradeOrderPage.BATCH_SHIPMENT_BUTTON)
        sleep(2)
        self.find_element("//button[@class='ant-btn ant-btn-primary ant-btn-sm ant-btn-background-ghost HCwVTfbjy92nWrqKAPwM']")
        self.assert_no_404_errors()

    # 【打单发货】按钮
    @pytest.mark.p0
    def test_print_way_bill(self):
        self.login_prt_env()
        self.click(TradeOrderPage.PRINT_WAYBILL_BUTTON)
        sleep(2)
        self.switch_to_window(1)
        self.assert_equal(self.get_current_url().endswith('/zone/electronic-sheet/print-waybill'), True,
                          msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()