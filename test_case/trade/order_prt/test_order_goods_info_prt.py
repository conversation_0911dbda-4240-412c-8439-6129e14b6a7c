import time
import logging

import pytest
from time import sleep

from ddt import ddt
from requests import request
from seleniumwire import webdriver

from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderGoodsInfoPRT(BaseTestCase):
    """
    订单商品信息
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    # 点击商品名 进入商品详情的按钮
    @pytest.mark.p0
    def test_goods_details(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.GOODS_DETAILS_BUTTON)
        self.sleep(2)
        self.switch_to_window(1)
        self.assert_equal(self.get_current_url().__contains__('/zone/goods/'), True,
                          msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 商品单价
    @pytest.mark.p0
    def test_product_price(self):
        self.login_prt_env()
        self.assertEqual(self.is_element_visible(TradeOrderPage.PRODUCT_PRICE), True, msg="商品单价元素错误")
        product_price = self.get_text(TradeOrderPage.PRODUCT_PRICE, by='xpath')
        self.assertEqual("¥" in product_price, True, msg="当前商品单价 = {}".format(product_price))

    # 商品购买数量
    @pytest.mark.p0
    def test_product_number(self):
        self.login_prt_env()
        self.assertEqual(self.is_element_visible(TradeOrderPage.PRODUCT_NUMBER), True, msg="商品数量元素错误")
        product_number = self.get_text(TradeOrderPage.PRODUCT_NUMBER, by='xpath')
        self.assertEqual("x" in product_number, True, msg="当前购买商品数量 = {}".format(product_number))

    # 商品规格
    @pytest.mark.p0
    def test_product_sku(self):
        self.login_prt_env()
        if self.is_element_visible(TradeOrderPage.PRODUCT_SKU):
            self.assertEqual(self.is_element_visible(TradeOrderPage.PRODUCT_SKU), True, msg="商品规格元素错误")
            product_sku = self.get_text(TradeOrderPage.PRODUCT_SKU, by='xpath')
            self.assertEqual("规格：" in product_sku, True, msg="当前商品规格 = {}".format(product_sku))
        else:
            MyLogger.logger.debug("当前商品没有规格")

    # 商品权益标签
    # TODO

    # 商品卡权益 退货补运费-商家出资
    @pytest.mark.p1
    def test_right_insurance(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        order_id = '2420800130051287'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        self.assertEqual(self.is_element_visible(TradeOrderPage.INSURANCE_BACK_FEE), True, msg="退货补运费权益卡片元素错误")
        insurance_text = self.get_text(TradeOrderPage.INSURANCE_BACK_FEE, by='xpath')
        self.assertEqual(insurance_text, "退货补运费-商家出资", msg="当前权益 = {}".format(insurance_text))
        self.click_xpath(TradeOrderPage.INSURANCE_BACK_FEE)
        self.sleep(2)
        self.assert_equal(self.get_current_url().__contains__('/freight/detail'), True,
                          msg="当前url：{}".format(self.get_current_url()))

    # 赠品订单的【收起共x件赠品】按钮
    @pytest.mark.p1
    def test_gift_order_collapse_info_button(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        # 6个月前订单
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        # 搜索赠品订单
        gift_order_id = 2414900156401553
        self.search_order_by_oid_six_month_before(gift_order_id)
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(curr_oid_text, str(gift_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_oid_text))
        # 获取文本按钮
        curr_collapse_button_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[1]/div/div/div[2]/div[3]'
        curr_button_text = self.get_text(curr_collapse_button_xpath, by='xpath')
        self.assertEqual("收起" in curr_button_text, True,
                         msg="当前oid：{}，按钮：{}".format(curr_oid_text, curr_button_text))
        # 赠品信息原来可以看到
        curr_gift_info_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[1]/div/div/div[2]/div[2]/div[2]/div[3]/div/span'
        self.assert_equal(self.is_element_visible(curr_gift_info_xpath), True,
                          msg="当前oid：{}，赠品信息路径：{}".format(curr_oid_text, curr_gift_info_xpath))
        # 点击【收起共x件赠品】按钮后，就看不到了
        self.click_xpath(curr_collapse_button_xpath)
        sleep(1)
        # 【收起共x件赠品】 看不到了
        self.assert_equal(self.is_element_visible(curr_collapse_button_xpath), False,
                          msg="当前oid：{}，赠品信息路径：{}".format(curr_oid_text, curr_collapse_button_xpath))
        self.assert_no_404_errors()

    # 赠品订单的【展开共x件赠品】按钮
    @pytest.mark.p1
    def test_gift_order_expand_info_button(self):
        self.login_prt_env()
        self.sleep(5)
        self.close_confirm()
        # 6个月前订单
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        # 搜索赠品订单
        gift_order_id = 2414900156401553
        self.search_order_by_oid_six_month_before(gift_order_id)
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(curr_oid_text, str(gift_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_oid_text))
        goods_elements = self.find_element(
            '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[1]/div/div')
        gift_elements = goods_elements.find_elements(by='xpath', value='./div[2]/*')
        # 获取按钮文案 -> 原本是【收起共x件赠品】按钮
        gift_expand_text = gift_elements[-1].text
        self.assertEqual("收起" in gift_expand_text, True, msg="当前oid：{}，按钮：{}".format(curr_oid_text, gift_expand_text))
        # 赠品信息原来可以看到
        self.assertEqual(gift_elements[-2].is_displayed(), True, msg="当前oid：{} 赠品商品定位失败".format(curr_oid_text))
        # 赠品标签原来也可以看到
        gift_label_element = gift_elements[-2].find_elements(by='xpath', value='./div[2]/div[3]/div/span')[0]
        self.assertEqual(gift_label_element.text, "赠品", msg="当前oid：{}，赠品标签没了".format(curr_oid_text))
        # 点击【收起共x件赠品】按钮后
        gift_elements[-1].click()
        self.sleep(2)
        # 获取按钮文案 -> 变成【展开共x件赠品】按钮、
        self.assertEqual("展开" in gift_elements[-1].text, True,
                         msg="当前oid：{}，按钮：{}".format(curr_oid_text, gift_expand_text))
