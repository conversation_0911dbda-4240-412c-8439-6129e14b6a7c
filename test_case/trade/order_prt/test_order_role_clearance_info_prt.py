import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderRoleClearanceInfoPRT(BaseTestCase):
    """
    渠道推广信息 / 清关模式信息
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    # 渠道和推广信息 自营
    @pytest.mark.p1
    def test_order_channel_by_self(self):
        self.login_prt_env()
        order_id = '****************'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[5]/div/div/div[1]'
        self.assertEquals(self.is_element_visible(channel_xpath), True, msg="渠道信息元素报错")
        channel_value = self.get_text(channel_xpath, by='xpath')
        self.assertEquals(channel_value, "自营", msg="自营渠道推广信息 = {} - {}".format(channel_value, order_id))

    # 渠道和推广信息 超级链接竞价
    @pytest.mark.p1
    def test_order_channel_super_link(self):
        self.login_prt_env()
        order_id = '2421200152308825'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div['
        channel_values = ["超级链接竞价", "具体佣金及分佣比例请以“资金-货款账单”为准"]
        for i in range(1, 3):
            channel_xpath = channel_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(channel_xpath), True, msg="渠道信息元素报错")
            channel_value = self.get_text(channel_xpath, by='xpath')
            self.assertEquals(channel_value, channel_values[i-1], msg="超级链接竞价渠道推广信息 = {} - {}".format(channel_value, order_id))

    # 渠道和推广信息 授权推广
    # TODO

    # 渠道和推广信息 佣金托管
    @pytest.mark.p1
    def test_order_channel_commission_host(self):
        self.login_prt_env()
        order_id = '2420700071402260'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div['
        channel_values = ["佣金托管", "具体佣金及分佣比例请以“账户资金-货款账单”为准"]
        for i in range(1, 3):
            channel_xpath = channel_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(channel_xpath), True, msg="渠道信息元素报错")
            channel_value = self.get_text(channel_xpath, by='xpath')
            self.assertEquals(channel_value, channel_values[i-1], msg="佣金托管渠道推广信息 = {} - {}".format(channel_value, order_id))

    # 渠道和推广信息 定价托管
    @pytest.mark.p1
    def test_order_channel_pricing_host(self):
        self.login_prt_env()
        order_id = '2421100066328694'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div['
        channel_values = ["定价托管", "具体佣金及分佣比例请以“账户资金-货款账单”为准"]
        for i in range(1, 3):
            channel_xpath = channel_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(channel_xpath), True, msg="渠道信息元素报错")
            channel_value = self.get_text(channel_xpath, by='xpath')
            self.assertEquals(channel_value, channel_values[i-1], msg="定价托管渠道推广信息 = {} - {}".format(channel_value, order_id))

    # 渠道和推广信息 分销信息+达人信息 预计佣金
    @pytest.mark.p1
    def test_order_channel_distribution_info(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        order_id = '2413430027021342'
        self.search_order_by_oid_six_month_before(order_id)
        order_number_button_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div[1]/div'
        curr_order_id = self.get_text(order_number_button_xpath, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div['
        distribution_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div[2]/div['
        channel_values = ["分销", [["达人ID：", "646818553"], ["达人昵称：", "复合结构规范"], ["预计佣金：", "0.9元"], ["预估分佣比例：", "10%"]]]
        for i in range(1, 3):
            if i == 1:
                channel_xpath = channel_prefix + str(i) + ']'
                self.assertEquals(self.is_element_visible(channel_xpath), True, msg="渠道信息元素报错")
                channel_value = self.get_text(channel_xpath, by='xpath')
                self.assertEquals(channel_value, channel_values[i-1], msg="定价托管渠道推广信息 = {} - {}".format(channel_value, order_id))
            else:
                for j in range(1, 5):
                    for k in range(1, 3):
                        distribution_xpath = distribution_prefix + str(j) + ']/span[' + str(k) + ']'
                        self.assertEquals(self.is_element_visible(distribution_xpath), True, msg="分销信息元素报错")
                        distribution_value = self.get_text(distribution_xpath, by='xpath')
                        self.assertEquals(distribution_value, channel_values[i-1][j-1][k-1],
                                          msg="定价托管渠道推广信息 = {} - {}".format(distribution_value, order_id))

    # 渠道和推广信息 分销信息+达人信息 阶梯佣金
    @pytest.mark.p1
    def test_order_channel_distribution_staircase_info(self):
        self.login_prt_env()
        order_id = '2418000162455160'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div['
        distribution_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div[2]/div['
        channel_values = ["分销", [["达人ID：", "1126082053"], ["达人昵称：", "提提奖。"], ["阶梯佣金预估：", "基础佣金：5元;附加佣金：1.8元"], ["阶梯分佣比例：", "前1单：25%; 1单后：35%"]]]
        for i in range(1, 3):
            if i == 1:
                channel_xpath = channel_prefix + str(i) + ']'
                self.assertEquals(self.is_element_visible(channel_xpath), True, msg="渠道信息元素报错")
                channel_value = self.get_text(channel_xpath, by='xpath')
                self.assertEquals(channel_value, channel_values[i-1], msg="定价托管渠道推广信息 = {} - {}".format(channel_value, order_id))
            else:
                for j in range(1, 5):
                    for k in range(1, 3):
                        distribution_xpath = distribution_prefix + str(j) + ']/span[' + str(k) + ']'
                        self.assertEquals(self.is_element_visible(distribution_xpath), True, msg="分销信息元素报错")
                        distribution_value = self.get_text(distribution_xpath, by='xpath')
                        self.assertEquals(distribution_value, channel_values[i-1][j-1][k-1],
                                          msg="定价托管渠道推广信息 = {} - {}".format(distribution_value, order_id))

    # 渠道和推广信息 分销信息+团长信息
    @pytest.mark.p1
    def test_order_channel_distribution_tz(self):
        # 登陆分销账号
        self.login_prt("wb_huoyangyang")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        order_id = '2425800140311732'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_part = self.find_element('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]')
        channel_info = channel_part.find_elements(by='xpath', value='./*')[0].text.split("\n")
        tz_infos = ['分销', '团长ID：2428997147', '团长昵称：多乐乐乐乐改', '查看全部']
        for i in range(4):
            self.assertEqual(channel_info[i], tz_infos[i], msg="{} - {}".format(channel_info[i], curr_order_id))

    # 渠道和推广信息 分销信息+快赚客信息
    @pytest.mark.p1
    def test_order_channel_distribution_kzk(self):
        self.login_prt("wb_huoyangyang")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        order_id = '2426800583283732'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        channel_part = self.find_element('//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]')
        channel_info = channel_part.find_elements(by='xpath', value='./*')[0].text.split("\n")
        kzk_infos = ['分销', '快赚客ID：**********', '快赚客昵称：陈舒展']
        for i in range(3):
            self.assertEqual(channel_info[i], kzk_infos[i], msg="{} - {}".format(channel_info[i], curr_order_id))

    # 清关模式 BBC保税仓 + 待清关
    @pytest.mark.p1
    def test_clearance_bbc_mode(self):
        self.login_prt("clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        clearance_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]'
        clearance_content = self.get_text(clearance_xpath).split("\n")
        self.assertEquals(clearance_content[0], "BBC保税仓", msg="当前BC直邮清关模式 = {}".format(clearance_content[0]))
        self.assertEquals(clearance_content[1], "待清关", msg="当前待清关清关状态 = {}".format(clearance_content[1]))

    # 清关模式 CC行邮
    @pytest.mark.p1
    def test_clearance_cc_mode(self):
        self.login_prt("clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        clearance_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]'
        clearance_content = self.get_text(clearance_xpath)
        self.assertEquals(clearance_content, "CC行邮", msg="当前CC行邮清关模式 = {}".format(clearance_content))

    # 清关模式 状态-BC直邮+待清关
    @pytest.mark.p1
    def test_clearance_bc_wait_clearance(self):
        self.login_prt("clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        clearance_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]'
        clearance_content = self.get_text(clearance_xpath).split("\n")
        self.assertEquals(clearance_content[0], "BC直邮", msg="当前BC直邮清关模式 = {}".format(clearance_content[0]))

    # 清关模式 状态-清关中
    # TODO

    # 清关模式 状态-清关失败
    def test_clearance_failed(self):
        self.login_prt("clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        clearance_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]'
        clearance_content = self.get_text(clearance_xpath).split("\n")
        self.assertEquals(clearance_content[0], "BBC保税仓", msg="当前BBC保税仓清关模式 = {}".format(clearance_content[0]))
        self.assertEquals(clearance_content[1], "清关失败-保税仓库存不足，物流公司未接单", msg="当前清关状态 = {}".format(clearance_content[1]))

    # 清关模式 状态-清关成功
    @pytest.mark.p1
    def test_clearance_success(self):
        self.login_prt("clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        clearance_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]'
        clearance_content = self.get_text(clearance_xpath).split("\n")
        self.assertEquals(clearance_content[0], "BBC保税仓", msg="当前BBC保税仓清关模式 = {}".format(clearance_content[0]))
        self.assertEquals(clearance_content[1], "清关成功", msg="当前清关状态 = {}".format(clearance_content[1]))

    # 清关模式 状态 - 保税仓发货中
    # TODO