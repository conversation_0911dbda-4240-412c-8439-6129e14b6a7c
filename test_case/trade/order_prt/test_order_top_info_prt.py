import re
import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderTopInfoPRT(BaseTestCase):
    """
    订单卡片上方信息
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    # 【新疆集运】标签 & 中转收货信息和最终收货信息
    @pytest.mark.p1
    def test_xinjiang_delivery_order_tag_info(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        self.sleep(2)
        # 订单编号
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        # 搜索新疆集运订单
        north_west_order_id = ****************
        self.search_order_by_oid(north_west_order_id)
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(curr_oid_text, str(north_west_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_oid_text))
        # 西北集运 标签 -> 已变更为 新疆集运
        north_west_tag_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        north_west_tag_suffix = ']/div[1]/div/div/div[1]/div[1]/div'
        curr_tag_xpath = north_west_tag_prefix + str(1) + north_west_tag_suffix
        self.assert_equal(self.is_element_visible(curr_tag_xpath), True,
                          msg="当前oid：{}, 新疆集运：{}".format(curr_oid_text, curr_tag_xpath))
        curr_tag_value = self.get_text(curr_tag_xpath)
        self.assert_equal(curr_tag_value, "新疆集运",
                          msg="当前oid：{}, 新疆集运：{}".format(curr_oid_text, curr_tag_value))
        # 西北集运 中转收货信息
        north_west_middle_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        north_west_middle_suffix = ']/div[2]/div[3]/div/div/div[1]/div[1]'
        curr_middle_xpath = north_west_middle_prefix + str(1) + north_west_middle_suffix
        curr_middle_value = self.get_text(curr_middle_xpath)
        self.assertEqual("中转收货信息" in curr_middle_value, True,
                         msg="新疆集运订单中转收货信息文案错误，当前文案：{}".format(curr_middle_value))
        # 西北集运 最终收货信息
        north_west_final_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        north_west_final_suffix = ']/div[2]/div[3]/div/div/div[2]/div[1]/span'
        curr_final_xpath = north_west_final_prefix + str(1) + north_west_final_suffix
        self.assert_equal(self.is_element_visible(curr_final_xpath), True,
                          msg="当前oid：{}，最终收货信息路径：{}".format(curr_oid_text, curr_final_xpath))
        curr_final_value = self.get_text(curr_final_xpath)
        self.assert_equal("最终收货信息" in curr_final_value, True,
                          msg="当前oid：{}，最终收货信息标题：{}".format(curr_oid_text, curr_final_value))

    # 【内蒙古集运】标签
    @pytest.mark.p1
    def test_inner_mongolia_delivery_order_tag_info(self):
        # 登陆供应链账号
        self.login("MERCHANT_DOMAIN", "supply_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        # 内蒙古集运订单
        inner_mongolia_order_id = '****************'
        self.search_order_by_oid(inner_mongolia_order_id)
        sleep(5)
        # 订单编号
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        # 获取订单ID
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        inner_mongolia_tag_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        inner_mongolia_tag_suffix = ']/div[1]/div/div/div[1]/div[1]/div'
        curr_tag_xpath = inner_mongolia_tag_prefix + str(1) + inner_mongolia_tag_suffix
        self.assert_equal(self.is_element_visible(curr_tag_xpath), True,
                          msg="当前oid：{}, 内蒙古集运：{}".format(curr_oid_text, curr_tag_xpath))
        curr_tag_value = self.get_text(curr_tag_xpath)
        self.assert_equal(curr_tag_value, "内蒙古集运",
                          msg="当前oid：{}, 内蒙古集运：{}".format(curr_oid_text, curr_tag_value))
        inner_mongolia_logistics = self.find_element(
            '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[3]/div/div')
        middle_info = inner_mongolia_logistics.find_elements(by='xpath', value='./*')[0].text
        final_info = inner_mongolia_logistics.find_elements(by='xpath', value='./*')[1].text
        middle_infos = middle_info.split("\n")
        final_infos = final_info.split("\n")
        MyLogger.logger.debug(middle_infos[0])
        MyLogger.logger.debug(middle_infos[1])
        MyLogger.logger.debug(middle_infos[2])
        MyLogger.logger.debug(final_infos[0])
        MyLogger.logger.debug(final_infos[1])
        MyLogger.logger.debug(final_infos[2])
        self.assertEqual("中转收货信息" in middle_infos[0], True,
                         msg="内蒙古集运订单中转收货信息文案错误，当前文案：{}".format(middle_infos[0]))
        self.assertEqual("最终收货信息" in final_infos[0], True,
                         msg="内蒙古集运订单最终收货信息文案错误，当前文案：{}".format(final_infos[0]))

    # 【买样后返】标签
    @pytest.mark.p1
    def test_buy_sample_return(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        sample_order_id = '2420100085942342'
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        pre_order_tag_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        pre_order_tag_suffix = ']/div[1]/div/div/div[1]/div[1]/img'
        # 搜索买样后返订单
        self.search_order_by_oid(sample_order_id)
        sleep(5)
        oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_order_id = self.is_element_visible(oid_xpath)
        curr_order_img_xpath = pre_order_tag_prefix + str(1) + pre_order_tag_suffix
        curr_img_object = self.find_element(curr_order_img_xpath, by='xpath')
        curr_img_link = curr_img_object.get_attribute('src')
        target_img_link = 'sample-return-tag'
        self.assertEquals(target_img_link in curr_img_link, True,
                          msg="预售订单标签链接错误，当前oid：{}，当前链接：{}".format(curr_order_id, curr_img_link))
        self.assert_no_404_errors()

    # 【快手优选】标签 & 渠道推广信息
    @pytest.mark.p1
    def test_kuaishou_good_choose_order_tag_info(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        # 搜索快手优选订单
        good_choose_order_id = 2413530202143342
        self.search_order_by_oid_six_month_before(good_choose_order_id)
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div[1]/div'
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(curr_oid_text, str(good_choose_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_oid_text))
        # 定价托管 标题
        curr_good_choose_pro_title_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div[1]'
        self.assert_equal(self.is_element_visible(curr_good_choose_pro_title_xpath), True,
                          msg="当前oid：{}，定价托管标题路径：{}".format(curr_oid_text, curr_good_choose_pro_title_xpath))
        curr_pro_title = self.get_text(curr_good_choose_pro_title_xpath, by='xpath')
        self.assert_equal(curr_pro_title, "定价托管",
                          msg="当前oid：{}，当前定价托管标题：{}".format(curr_oid_text, curr_pro_title))
        # 定价托管 正文
        curr_pro_info_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[5]/div/div/div[2]'
        self.assert_equal(self.is_element_visible(curr_pro_info_xpath), True,
                          msg="当前oid：{}，定价托管正文路径：{}".format(curr_oid_text, curr_pro_info_xpath))
        curr_pro_info = self.get_text(curr_pro_info_xpath, by='xpath')
        self.assert_equal(curr_pro_info, "具体佣金及分佣比例请以“账户资金-货款账单”为准",
                          msg="当前oid：{}，当前定价托管正文：{}".format(curr_oid_text, curr_pro_info))

    # 拼团订单的【拼团】标签
    @pytest.mark.p1
    def test_group_purchase_order(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        self.sleep(2)
        self.close_confirm()
        group_order_id = '2413530048303287'
        # 搜索拼团订单
        self.search_order_by_oid_six_month_before(group_order_id)
        sleep(5)
        oid_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div[1]/div/a'
        curr_order_id = self.get_text(oid_xpath, by='xpath')
        curr_order_img_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div[1]/img'
        curr_img_object = self.find_element(curr_order_img_xpath, by='xpath')
        curr_img_link = curr_img_object.get_attribute('src')
        target_img_link = 'https://u2-201.ecukwai.com/kos/nlav12333/trade-assets/h16.89ef1b2b54868de9.png'
        self.assertEquals(curr_img_link, target_img_link,
                          msg="拼团订单标签链接错误，当前oid：{}，当前链接：{}".format(curr_order_id, curr_img_link))
        self.assert_no_404_errors()

    # 预售订单的【预售】标签
    @pytest.mark.p1
    def test_pre_order_tag(self):
        self.login_prt_env()
        pre_order_id = '2433002560075531'
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        pre_order_tag_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        pre_order_tag_suffix = ']/div[1]/div/div/div[1]/div[1]/img'
        # 搜索预售订单
        self.search_order_by_oid(pre_order_id)
        sleep(5)
        oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_order_id = self.is_element_visible(oid_xpath)
        curr_order_img_xpath = pre_order_tag_prefix + str(1) + pre_order_tag_suffix
        curr_img_object = self.find_element(curr_order_img_xpath, by='xpath')
        curr_img_link = curr_img_object.get_attribute('src')
        target_img_link = 'https://u2-201.ecukwai.com/kos/nlav11921/yushou/yushou16.png'
        self.assertEquals(curr_img_link, target_img_link,
                          msg="预售订单标签链接错误，当前oid：{}，当前链接：{}".format(curr_order_id, curr_img_link))
        self.assert_no_404_errors()

    # 订单编号
    @pytest.mark.p0
    def test_order_id(self):
        self.login_prt_env()
        search_oid = '2433702561283959'
        self.search_order_by_oid(search_oid)
        sleep(5)
        order_id_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        result_oid = self.get_text(order_id_xpath, by='xpath')
        self.assertEquals(search_oid, result_oid, msg="搜索结果错误，当前搜索结果为：{}".format(result_oid))

    # 订单创建时间
    @pytest.mark.p0
    def test_order_create_time(self):
        self.login_prt_env()
        is_valid_time = lambda s: bool(re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$', s))
        order_create_time_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[1]/div/div/div[1]/div/div[2]'
        order_create_time_element = self.find_element(order_create_time_xpath)
        order_create_content = order_create_time_element.find_elements(by='xpath', value='./*')
        self.assertEqual(order_create_content[1].text, "订单创建时间：", msg="当前文案 = {}".format(order_create_content[0].text))
        self.assertEqual(is_valid_time(order_create_content[2].text), True, msg="当前时间 = {}".format(order_create_content[1].text))

    # 【承诺发货时间：xxx】标签，订单状态副标题为：请在xxx内发货（待发货）
    @pytest.mark.p1
    def test_promised_shipping_time_tag(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        sleep(2)
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        promised_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        promised_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/span[2]'
        delivery_time_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_time_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/div/span'
        delivery_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
        order_sub_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_sub_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[2]/div/div/span'
        for i in range(1, 11):
            curr_promised_tag_path = promised_tag_xpath_prefix + str(i) + promised_tag_xpath_suffix
            if not self.is_element_visible(curr_promised_tag_path):
                continue
            curr_promised_text = self.get_text(curr_promised_tag_path, by='xpath')
            curr_tag_path = delivery_time_tag_xpath_prefix + str(i) + delivery_time_tag_xpath_suffix
            if not self.is_element_visible(curr_tag_path):
                continue
            curr_tag_text = self.get_text(curr_tag_path, by='xpath')
            if curr_promised_text == "承诺发货时间：" and "已逾期" not in curr_tag_text:
                # 获取订单状态
                curr_delivery_status_path = delivery_status_xpath_prefix + str(i) + delivery_status_xpath_suffix
                curr_delivery_status = self.get_text(curr_delivery_status_path, by='xpath')
                if curr_delivery_status == "待发货":
                    # 获取订单ID
                    curr_oid_xpath = oid_xpath_prefix + str(i) + oid_xpath_suffix
                    curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
                    # 获取副标题文案
                    curr_order_sub_status_path = order_sub_status_xpath_prefix + str(i) + order_sub_status_xpath_suffix
                    curr_order_sub = self.get_text(curr_order_sub_status_path, by='xpath')
                    self.assert_equal("请在" in curr_order_sub and "内发货" in curr_order_sub, True,
                                      msg="当前oid：{}，副标题文案：{}".format(curr_oid_text, curr_order_sub))
                    break
        self.assert_no_404_errors()

    # 【承诺发货时间：xxx】标签，订单状态副标题为：请在xxx内发货（部分发货）
    @pytest.mark.p1
    def test_promised_shipping_time_tag_part(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        sleep(1)
        # 点击【部分发货】勾选框
        self.click_xpath(TradeOrderPage.PARTIAL_DELIVERY_CHECKBOX)
        sleep(2)
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        promised_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        promised_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/span[2]'
        delivery_time_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_time_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/div/span'
        delivery_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
        order_sub_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_sub_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[3]/div/div/span'
        for i in range(1, 11):
            curr_promised_tag_path = promised_tag_xpath_prefix + str(i) + promised_tag_xpath_suffix
            if not self.is_element_visible(curr_promised_tag_path):
                continue
            curr_promised_text = self.get_text(curr_promised_tag_path, by='xpath')
            curr_tag_path = delivery_time_tag_xpath_prefix + str(i) + delivery_time_tag_xpath_suffix
            if not self.is_element_visible(curr_tag_path):
                continue
            curr_tag_text = self.get_text(curr_tag_path, by='xpath')
            if curr_promised_text == "承诺发货时间：" and "已逾期" not in curr_tag_text:
                # 获取订单状态
                curr_delivery_status_path = delivery_status_xpath_prefix + str(i) + delivery_status_xpath_suffix
                curr_delivery_status = self.get_text(curr_delivery_status_path, by='xpath')
                if curr_delivery_status == "部分发货":
                    # 获取订单ID
                    curr_oid_xpath = oid_xpath_prefix + str(i) + oid_xpath_suffix
                    curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
                    # 获取副标题文案
                    curr_order_sub_status_path = order_sub_status_xpath_prefix + str(i) + order_sub_status_xpath_suffix
                    curr_order_sub = self.get_text(curr_order_sub_status_path, by='xpath')
                    self.assert_equal("请在" in curr_order_sub and "内发货" in curr_order_sub, True,
                                      msg="当前oid：{}，副标题文案：{}".format(curr_oid_text, curr_order_sub))
                    break
        self.assert_no_404_errors()

    # 【承诺发货时间：xxx：已逾期】标签，订单状态副标题为：已超过承诺发货时间xx天 待发货
    @pytest.mark.p1
    def test_shipment_time_overdue_tag(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        sleep(2)
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        delivery_time_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_time_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/div/span'
        delivery_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
        order_sub_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_sub_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[2]/div/div/span'
        for i in range(1, 11):
            curr_tag_path = delivery_time_tag_xpath_prefix + str(i) + delivery_time_tag_xpath_suffix
            if self.is_element_visible(curr_tag_path):
                curr_tag_text = self.get_text(curr_tag_path, by='xpath')
                if "已逾期" in curr_tag_text:
                    # 获取发货状态文案
                    curr_delivery_status_path = delivery_status_xpath_prefix + str(i) + delivery_status_xpath_suffix
                    curr_delivery_status = self.get_text(curr_delivery_status_path, by='xpath')
                    # 如果是待发货状态
                    if curr_delivery_status == "待发货":
                        # 获取订单ID
                        curr_oid_xpath = oid_xpath_prefix + str(i) + oid_xpath_suffix
                        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
                        # 获取副标题文案
                        curr_order_sub_status_path = order_sub_status_xpath_prefix + str(i) + order_sub_status_xpath_suffix
                        curr_order_sub = self.get_text(curr_order_sub_status_path, by='xpath')
                        self.assert_equal("已超过承诺发货时间" in curr_order_sub, True,
                                          msg="当前oid：{}，副标题文案：{}".format(curr_oid_text, curr_order_sub))
                        break
        self.assert_no_404_errors()

    # 【承诺发货时间：xxx：已逾期】标签，订单状态副标题为：已超过承诺发货时间xx天 部分发货
    @pytest.mark.p1
    def test_shipment_time_overdue_tag_part(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        sleep(2)
        # 点击【部分发货】勾选框
        self.click_xpath(TradeOrderPage.PARTIAL_DELIVERY_CHECKBOX)
        sleep(2)
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        delivery_time_tag_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_time_tag_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[3]/div/span'
        delivery_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        delivery_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[1]/div/div'
        order_sub_status_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_sub_status_xpath_suffix = ']/div[2]/div[2]/div/div/div[3]/div/div/span'
        for i in range(1, 11):
            curr_tag_path = delivery_time_tag_xpath_prefix + str(i) + delivery_time_tag_xpath_suffix
            if self.is_element_visible(curr_tag_path):
                curr_tag_text = self.get_text(curr_tag_path, by='xpath')
                if "已逾期" in curr_tag_text:
                    # 获取发货状态文案
                    curr_delivery_status_path = delivery_status_xpath_prefix + str(i) + delivery_status_xpath_suffix
                    curr_delivery_status = self.get_text(curr_delivery_status_path, by='xpath')
                    # 如果是待发货状态
                    if curr_delivery_status == "部分发货":
                        # 获取订单ID
                        curr_oid_xpath = oid_xpath_prefix + str(i) + oid_xpath_suffix
                        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
                        # 获取副标题文案
                        curr_order_sub_status_path = order_sub_status_xpath_prefix + str(i) + order_sub_status_xpath_suffix
                        curr_order_sub = self.get_text(curr_order_sub_status_path, by='xpath')
                        self.assert_equal("已超过承诺发货时间" in curr_order_sub, True,
                                          msg="当前oid：{}，副标题文案：{}".format(curr_oid_text, curr_order_sub))
                        break
        self.assert_no_404_errors()

    # 【延迟发货时间：xxx】+【平台审核通过】/【平台免考核】/【报备通过】标签
    @pytest.mark.p1
    def test_platform_review_pass_tag(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        self.sleep(2)
        # 延迟发货通过没有逾期的订单
        target_order_id = '2415500000841183'
        self.search_order_by_oid_six_month_before(target_order_id)
        self.sleep(5)
        # 获取订单ID
        curr_oid_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div/a'
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        # 判断延迟发货时间
        extend_deliver_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[3]/span[2]'
        extend_deliver_text = self.get_text(extend_deliver_xpath)
        self.assertEqual("延迟发货时间" in extend_deliver_text, True,
                         msg="该订单没有延迟发货时间，当前oid：{}，文案:{}".format(curr_oid_text, extend_deliver_text))
        # 判断订单发货是否逾期
        promise_time_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[3]/div/span'
        promise_time_text = self.get_text(promise_time_xpath)
        self.assertEqual("已逾期" not in promise_time_text, True,
                         msg="该订单已逾期发货，当前oid：{}，文案:{}".format(curr_oid_text, promise_time_text))
        # 获取标签
        curr_review_path = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[3]/div/div/div/span[1]'
        curr_review_text = self.get_text(curr_review_path, by='xpath')
        self.assert_equal(curr_review_text in ["平台审核通过", "平台免考核", "报备通过", "协商发货"], True,
                          msg="当前oid：{}，延迟发货原因:{}".format(curr_oid_text, curr_review_text))
        self.assert_no_404_errors()

    # 【延迟发货时间】：xxx : 已逾期 + 【平台审核通过】/【平台免考核】/【报备通过】标签
    @pytest.mark.p1
    def test_platform_review_pass_tag_overdue(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        # 延迟发货通过后又逾期的订单
        target_order_id = '2428902240171528'
        self.input('//*[@id="valueMixed"]', text=target_order_id)
        self.click_xpath('//*[@id="pro-form-wrapper"]/div[3]/div[4]/div/div[1]/div[2]/button')
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[1]/div'
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(curr_oid_text, target_order_id, msg="订单搜索结果不对")
        # 延迟发货时间 文案
        curr_promised_path = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[3]/span[2]'
        curr_promised_text = self.get_text(curr_promised_path, by='xpath')
        self.assertEqual(curr_promised_text, "延迟发货时间：",
                         msg="延迟发货时间文案不对，当前oid:{}, 文案:{}".format(curr_oid_text, curr_promised_text))
        # 判断订单发货是否逾期
        promise_time_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[3]/div/span'
        promise_time_text = self.get_text(promise_time_xpath)
        self.assertEqual("已逾期" in promise_time_text, True,
                         msg="该订单未逾期发货，当前oid：{}，文案:{}".format(curr_oid_text, promise_time_text))
        # 获取标签
        curr_review_path = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div/div[3]/div/div/div/span[1]'
        curr_review_text = self.get_text(curr_review_path, by='xpath')
        self.assert_equal(curr_review_text in ["平台审核通过", "平台免考核", "报备通过", "协商发货"], True,
                          msg="当前oid：{}，延迟发货原因:{}".format(curr_oid_text, curr_review_text))

    # 查看历史备注【一面小旗】按钮
    @pytest.mark.p0
    def test_view_historical_notes(self):
        self.login_prt_env()
        self.click(TradeOrderPage.VIEW_HISTORICAL_NOTES_BUTTON, By.CLASS_NAME)
        sleep(2)
        self.find_elements(TradeOrderPage.VIEW_HISTORICAL_NOTES_POPUP, By.CLASS_NAME)
        self.assert_no_404_errors()