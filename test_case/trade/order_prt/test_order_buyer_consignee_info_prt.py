import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderBuyerConsigneeInfoPRT(BaseTestCase):
    """
    订单收货人/地址信息
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    # 客服按钮
    @pytest.mark.p0
    def test_orderlist_to_customer(self):
        self.login_prt_env()
        self.click(TradeOrderPage.customer_service_enter)
        sleep(2)
        self.maximize_window()
        self.assert_equal(self.get_current_url().__contains__("from=sellerOrder"), True, msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 西北集运 中转收货信息和最终收货信息
    @pytest.mark.p1
    def test_north_west_delivery_order_tag_info(self):
        self.login_prt_env()
        # 订单编号
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        # 搜索新疆集运订单
        north_west_order_id = 2428302192850087
        self.search_order_by_oid(north_west_order_id)
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_oid_text = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(curr_oid_text, str(north_west_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_oid_text))
        # 西北集运 中转收货信息
        north_west_middle_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        north_west_middle_suffix = ']/div[2]/div[3]/div/div/div[1]/div[1]'
        curr_middle_xpath = north_west_middle_prefix + str(1) + north_west_middle_suffix
        curr_middle_value = self.get_text(curr_middle_xpath)
        self.assertEqual("中转收货信息" in curr_middle_value, True,
                         msg="新疆集运订单中转收获信息文案错误，当前文案：{}".format(curr_middle_value))
        # 西北集运 最终收货信息
        north_west_final_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        north_west_final_suffix = ']/div[2]/div[3]/div/div/div[2]/div[1]/span'
        curr_final_xpath = north_west_final_prefix + str(1) + north_west_final_suffix
        self.assert_equal(self.is_element_visible(curr_final_xpath), True,
                          msg="当前oid：{}，最终收获信息路径：{}".format(curr_oid_text, curr_final_xpath))
        curr_final_value = self.get_text(curr_final_xpath)
        self.assert_equal("最终收货信息" in curr_final_value, True,
                          msg="当前oid：{}，最终收获信息标题：{}".format(curr_oid_text, curr_final_value))

    # 已改地址订单的【已改地址】标签 & 改地址备注
    @pytest.mark.p1
    def test_modified_address_order_tag_remark(self):
        self.login_prt_env()
        order_id = '2426902178488305'
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        self.search_order_by_oid(order_id)
        self.click(TradeOrderPage.ALL_ORDER_BUTTON)
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_order_id = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        sleep(5)
        # 已改地址 标签
        modified_address_tag_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        modified_address_tag_suffix = ']/div[2]/div[3]/div/div/div[2]/div'
        # 改地址备注
        modified_address_remark_title_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        modified_address_remark_title_suffix = ']/div[3]/div/span[1]'
        modified_address_remark_content_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        modified_address_remark_content_suffix = ']/div[3]/div/span[2]'
        modified_address_remark_button_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        modified_address_remark_button_suffix = ']/div[3]/div/div'
        modified_address_remark_popup_xpath = "//div[@id='rcDialogTitle0']"
        # 已改地址 标签
        curr_tag_xpath = modified_address_tag_prefix + str(1) + modified_address_tag_suffix
        curr_tag_text = self.get_text(curr_tag_xpath, by='xpath')
        self.assertEqual(curr_tag_text, "已改地址")

        # 插旗备注 标题
        curr_title_xpath = modified_address_remark_title_prefix + str(1) + modified_address_remark_title_suffix
        curr_title_text = self.get_text(curr_title_xpath, by='xpath')
        self.assert_equal(curr_title_text, "插旗备注：",
                          msg="当前oid：{}，插旗备注 标题：{}".format(curr_order_id, curr_title_text))

        # 改地址备注 内容
        curr_content_xpath = modified_address_remark_content_prefix + str(1) + modified_address_remark_content_suffix
        curr_content_text = self.get_text(curr_content_xpath, by='xpath')
        self.assert_equal(curr_content_text, "买家收货地址已发生修改，最新地址请查看“订单详情”部分“收货地址”内容",
                          msg="当前oid：{}，改地址备注内容：{}".format(curr_order_id, curr_content_text))

        # 改地址备注 编辑按钮
        curr_button_xpath = modified_address_remark_button_prefix + str(1) + modified_address_remark_button_suffix
        self.click_xpath(curr_button_xpath)
        self.sleep(2)
        self.assert_equal(self.is_element_visible(modified_address_remark_popup_xpath), True,
                          msg="当前oid：{}，点击改地址备注编辑按钮后的弹窗：{}".format(curr_order_id,
                                                                                    modified_address_remark_popup_xpath))

    # 店铺新客 标签
    def test_shop_new_buyer(self):
        self.login_prt_env()
        order_id = '2425700180887067'
        self.search_order_by_oid(order_id)
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_order_id = self.get_text(curr_oid_xpath, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        new_buyer_xpath = "//span[contains(text(),'店铺新客')]"
        new_buyer_text = self.get_text(new_buyer_xpath, by='xpath')
        self.assertEqual(new_buyer_text, "店铺新客", msg="当前oid：{}，当前标签文案：{}".format(curr_order_id, new_buyer_text))

    # 黑卡会员标签
    @pytest.mark.p1
    def test_black_vip_tag(self):
        self.login_prt_env()
        # 订单编号
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        black_vip_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        black_vip_suffix = ']/div[2]/div[3]/div/div/div[2]/div[2]/div'
        # 黑卡会员订单
        black_vip_order_id = '2418600082611706'
        self.search_order_by_oid(black_vip_order_id)
        sleep(5)
        # 获取订单ID
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        # 黑卡会员文案
        curr_black_vip_xpath = black_vip_prefix + str(1) + black_vip_suffix
        black_vip_text = self.get_text(curr_black_vip_xpath, by='xpath')
        self.assert_equal(black_vip_text, "黑卡会员",
                          msg="当前oid：{}，当前按钮文案：{}".format(curr_oid_text, black_vip_text))

    # 待发货订单地址下方 【官方在线寄件】按钮
    @pytest.mark.p1
    def test_official_send_online_button(self):
        self.login_prt_env()
        # 待发货订单 Tab
        self.click(TradeOrderPage.AWAITING_SHIPMENT_ORDER_BUTTON)
        self.sleep(2)
        order_status_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        order_status_suffix = ']/div[2]/div[2]/div/div/div'
        consignee_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        consignee_suffix = ']/div[2]/div[3]/div/div'
        official_send_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        official_send_suffix = ']/div[2]/div[3]/div/div/div[2]/button'
        for i in range(1, 11):
            order_status_xpath = order_status_prefix + str(i) + order_status_suffix
            order_status = self.get_text(order_status_xpath, by='xpath')
            if order_status != "待发货":
                MyLogger.logger.debug("{} - {}".format(i, order_status))
                continue
            consignee_xpath = consignee_prefix + str(i) + consignee_suffix
            consignee_object = self.find_element(consignee_xpath)
            consignee_element = consignee_object.find_elements(by='xpath', value='./*')
            if len(consignee_element) < 2:
                MyLogger.logger.debug("{} - {}".format(i, consignee_element[0].text))
                continue
            if len(consignee_element) > 2:
                MyLogger.logger.debug("{} - {}".format(i, consignee_element[0].text))
                continue
            official_send_xpath = official_send_prefix + str(i) + official_send_suffix
            self.assertEqual(self.is_element_visible(official_send_xpath), True)
            self.assertEqual(self.get_text(official_send_xpath), "官方在线寄件，快递费超低价 >", msg="当前文案 = {}".format(self.get_text(official_send_xpath)))
            self.click_xpath(official_send_xpath)
            self.sleep(2)
            self.driver.switch_to.window(self.driver.window_handles[1])
            self.assert_equal(self.get_current_url().__contains__('zone/electronic-sheet/online-parcel-sending'), True,
                              msg="当前url：{}".format(self.get_current_url()))
            self.assert_no_404_errors()
            break