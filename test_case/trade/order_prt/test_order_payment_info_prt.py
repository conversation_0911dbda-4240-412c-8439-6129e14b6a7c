import time
import logging

import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


class TestOrderPaymentInfoPRT(BaseTestCase):
    """
    订单实收款信息
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    # 实收款 在线支付
    def test_paid_details_no_subsidy(self):
        self.login_prt_env()
        order_id = '****************'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥0.50"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 3):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 实收款 在线支付 + 平台补贴
    def test_paid_details_platform_subsidy(self):
        self.login_prt_env()
        order_id = '2422600011187986'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥5.01", "含平台补贴", "￥5.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 5):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 实收款 在线支付 + 店铺优惠
    def test_paid_details_shop_discount(self):
        self.login_prt_env()
        order_id = '2421800141568342'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥1.00", "含店铺优惠", "￥19.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 5):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 实收款 在线支付 + 店铺优惠 + 平台补贴
    def test_paid_details_platform_subsidy_shop_discount(self):
        self.login_prt_env()
        order_id = '2422500105703553'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥1.00", "含店铺优惠", "￥2.00", "含平台补贴", "￥0.10"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 7):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 实收款 在线支付 + 商品运费 + 修改包邮链接 + 店铺优惠
    def test_paid_details_delivery_fee(self):
        self.login_prt_env()
        order_id = '2421000063413287'
        self.search_order_by_oid(order_id)
        self.sleep(2)
        self.click_xpath("//div[@id='rc-tabs-1-tab-0']")
        self.sleep(2)
        curr_order_id = self.get_text("//a[@class='S83nu6wVlp8Dz2t5shb6']", by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥12.00", "含商品运费", "￥10.00", "修改为包邮，享体验分加分", "含店铺优惠", "￥1.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 8):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 实收款 在线支付 + 店铺优惠 + 主播补贴
    def test_paid_details_shop_discount_liver_subsidy(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        order_id = '2413530048303287'
        self.search_order_by_oid_six_month_before(order_id)
        order_number_button_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[1]/div/div/div[1]/div[2]/div[1]/div'
        curr_order_id = self.get_text(order_number_button_xpath, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥1.00", "含店铺优惠", "￥5.00", "含主播补贴", "￥0.05"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 7):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 实收款 在线支付 + 先用后付抵扣
    def test_paid_details_use_before_pay(self):
        self.login_prt_env()
        order_id = '2419100053909092'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        detail_text = ['在线支付', "￥12.04", "含先用后付抵扣", "￥10.00"]
        detail_text_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]/div/div/div['
        for i in range(1, 5):
            detail_text_xpath = detail_text_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(detail_text_xpath), True, msg="实收款元素报错")
            detail_value = self.get_text(detail_text_xpath, by='xpath')
            self.assertEquals(detail_value, detail_text[i-1], msg="{}实收款内容 = {}".format(detail_text[i-1], detail_value))

    # 定金预售 待收定金 + 待收尾款 / 已收定金 + 待收尾款
    def test_pre_sale_order_paid_details(self):
        self.login_prt_env()
        paid_detail_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[4]/div/div/div['
        order_id = '2423900007213959'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        details = ["待收定金", "￥2.00", "待收尾款", "￥7.00"]
        for i in range(1, 5):
            paid_detail_xpath = paid_detail_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(paid_detail_xpath), True, msg="实收款明细元素报错")
            paid_detail_text = self.get_text(paid_detail_xpath, by='xpath')
            self.assertEquals(paid_detail_text, details[i-1], msg="实收款明细 = {} - {}".format(paid_detail_text, order_id))
        order_id = '2420500090585183'
        self.search_order_by_oid(order_id)
        curr_order_id = self.get_text(TradeOrderPage.ORDER_NUMBER_BUTTON, by='xpath')
        self.assertEqual(order_id, str(curr_order_id), msg="搜索结果错误，当前订单号：{}".format(curr_order_id))
        details = ["已收定金", "￥2.00", "待收尾款", "￥8.00"]
        for i in range(1, 5):
            paid_detail_xpath = paid_detail_prefix + str(i) + ']'
            self.assertEquals(self.is_element_visible(paid_detail_xpath), True, msg="实收款明细元素报错")
            paid_detail_text = self.get_text(paid_detail_xpath, by='xpath')
            self.assertEquals(paid_detail_text, details[i-1], msg="实收款明细 = {} - {}".format(paid_detail_text, order_id))

    # 含支付优惠
    # TODO

    # 含税费
    def test_payment_include_tax(self):
        self.login_prt("clearance_account")
        self.assert_title("快手小店")
        self.sleep(2)
        self.open_order_list()
        if self.is_element_visible('#root > div > img'):
            self.click('#root > div > img')
        order_id = '****************'
        order_input_box_xpath = '//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div/input'
        self.input(order_input_box_xpath, order_id)
        search_button_xpath = '//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button'
        self.click_xpath(search_button_xpath)
        self.click_xpath(search_button_xpath)
        self.sleep(2)
        payment_info_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[4]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[4]'
        payment_info = self.get_text(payment_info_xpath).split("\n")
        self.assertEquals(payment_info[2] + payment_info[3], "含税费￥0.46", msg="当前实收款 = {}".format(payment_info))

    # 买家手续费
    # TODO