import time
import logging

# import document
import pytest
from time import sleep

from ddt import ddt
from ..base import BaseTestCase
from page_objects.trade_order.trade_order_page import TradeOrderPage


class MyLogger:
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)


def time_decarator(func):
    def calculate(*args, **kwargs):
        total_start = time.time()
        result = func(*args, **kwargs)
        total_end = time.time()
        MyLogger.logger.debug("Total time = {}".format(total_end - total_start))
        return result
    return calculate


@ddt
class TestOrderListMenuPRT(BaseTestCase):
    """
    订单列表上面的菜单栏
    """

    def login_prt_env(self):
        self.login_prt("order_account")
        MyLogger.logger.debug("Login PRT Environment ... ")
        self.assert_title("快手小店")
        self.sleep(5)
        self.open_order_list()

    def __init__(self, *args, **kwargs):
        # 创建日志记录器
        super().__init__(*args, **kwargs)
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)
        # 创建控制台处理器并设置级别
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        # 将处理器添加到日志记录器
        self.logger.addHandler(console_handler)

    # 【协商发货时间】按钮
    @pytest.mark.p0
    def test_negotiate_shipping_button(self):
        self.login_prt_env()
        self.click(TradeOrderPage.NEGOTIATE_SHIPPING_BUTTON)
        sleep(2)
        self.switch_to_window(1)
        self.assert_equal(self.get_current_url().endswith('/zone/trade/delayship-report/list?tabId=negotiation'), True, msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 【延迟发货报备】按钮
    @pytest.mark.p0
    def test_delay_shipping_button(self):
        self.login_prt_env()
        self.click(TradeOrderPage.DELAY_SHIPPING_BUTTON)
        sleep(2)
        self.assert_equal(self.get_current_url().endswith('/zone/trade/delayship-report/list'), True, msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 【优先发货设置】按钮
    @pytest.mark.p0
    def test_priority_ship_setting_button(self):
        self.login_prt_env()
        self.click(TradeOrderPage.PRIORITY_SHIPPING_SETTING_BUTTON)
        sleep(2)
        self.switch_to_window(1)
        self.assert_equal(self.get_current_url().endswith('/zone/order/ship-setting'), True, msg="当前url：{}".format(self.get_current_url()))
        self.assert_no_404_errors()

    # 【近6个月订单】按钮
    @pytest.mark.p0
    def test_orders_recent_six_month(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_RECENT_SIX_MONTH_BUTTON)
        sleep(2)
        self.assert_no_404_errors()

    # 【6个月前订单】按钮
    @pytest.mark.p0
    def test_orders_before_six_month(self):
        self.login_prt_env()
        self.click(TradeOrderPage.ORDERS_BEFORE_SIX_MONTH_BUTTON)
        sleep(2)
        self.assert_no_404_errors()

    # 【买家催发货】按钮
    @pytest.mark.p0
    def test_expedite_shipping_selector(self):
        self.login_prt_env()
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.EXPEDITE_SHIPPING_SELECTOR)
        sleep(2)
        self.assert_no_404_errors()

    # 【24h需发货】按钮
    @pytest.mark.p0
    def test_required_shipment_in_24hours(self):
        self.login_prt_env()
        self.click(TradeOrderPage.REQUIRED_SHIPMENT_IN_24HOURS_BUTTON)
        sleep(2)
        self.assert_no_404_errors()

    # 【超时未发货】按钮
    @pytest.mark.p0
    def test_no_shipment_after_time_limit(self):
        self.login_prt_env()
        self.click(TradeOrderPage.NO_SHIPMENT_AFTER_TIME_LIMIT_BUTTON)
        sleep(2)
        self.assert_no_404_errors()

    # 【优先发货】按钮
    @pytest.mark.p0
    def test_priority_shipping_selector(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.PRIORITY_SHIPPING_SELECTOR)
        sleep(2)
        self.assert_no_404_errors()

    # 【订单编号】输入框
    @pytest.mark.p0
    def test_order_id_input_box(self):
        self.login_prt_env()
        search_oid = '2433702561283959'
        self.input(TradeOrderPage.ORDER_ID_INPUT_BOX_SELECTOR, text=search_oid)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        sleep(5)
        order_id_xpath = "//a[@class='S83nu6wVlp8Dz2t5shb6']"
        result_oid = self.get_text(order_id_xpath, by='xpath')
        self.assertEquals(search_oid, result_oid, msg="搜索结果错误，当前搜索结果为：{}".format(result_oid))

    # 【订单状态】下拉框
    @pytest.mark.p0
    def test_order_status_selector(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.ORDER_STATUS_SELECTOR)
        order_status_values = ["全部", "待付款", "待发货", "已发货", "已收货", "交易成功", "订单关闭", "已付定金，待付尾款",
                              "未支付尾款，已协商/自动退定金", "超时未付尾款订单关闭"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'

        for i in range(len(order_status_values)):
            curr_value_xpath = selector_content_value_prefix + str(i+1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_order_value = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_order_value, order_status_values[i])
        sleep(2)
        self.assert_no_404_errors()

    # 【售后状态】下拉框
    @pytest.mark.p0
    def test_after_sales_selector(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.AFTER_SALES_SELECTOR)
        after_sales_values = ["无售后", "售后中", "同意退款，退款中", "售后成功", "售后关闭"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'

        for i in range(len(after_sales_values)):
            curr_value_xpath = selector_content_value_prefix + str(i+1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_after_sales_value = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_after_sales_value, after_sales_values[i])
        sleep(2)
        self.assert_no_404_errors()

    # 【插旗备注】下拉框
    @pytest.mark.p0
    def test_insert_flag_remarks_selector(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        self.sleep(2)
        insert_flag_remarks_text = self.get_text(TradeOrderPage.INSERT_FLAG_REMARKS_TEXT)
        self.assertEquals(insert_flag_remarks_text, "插旗备注")
        self.click_xpath(TradeOrderPage.INSERT_FLAG_REMARKS_SELECTOR)
        sleep(2)
        flag_color = ['red', 'orange', 'green', 'blue', 'purple', 'grey']
        selector_flag_img_prefix1 = '/html/body/div[9]/div/div/div/div[2]/div[1]/div/div/div['
        selector_flag_img_suffix = ']/label/img'
        for i in range(len(flag_color)):
            curr_flag_img_xpath = selector_flag_img_prefix1 + str(i+1) + selector_flag_img_suffix
            curr_img_object = self.find_element(curr_flag_img_xpath)
            curr_img_value = curr_img_object.get_attribute('src')
            self.assertEquals(flag_color[i] in curr_img_value, True)
        sleep(1)
        self.assert_no_404_errors()

    # 【商品名称】输入框
    @pytest.mark.p0
    def test_goods_name_input_box(self):
        self.login_prt_env()
        self.sleep(2)
        self.close_confirm()
        self.sleep(2)
        search_goods_name = "AIR ANGEL赠品活动自动化勿动"
        self.input(TradeOrderPage.GOODS_NAME_INPUT_BOX_SELECTOR, text=search_goods_name)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        sleep(5)
        # 订单编号
        oid_xpath_prefix = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div['
        oid_xpath_suffix = ']/div[1]/div/div/div[1]/div/div[1]/div/a'
        # 获取订单ID
        curr_oid_xpath = oid_xpath_prefix + str(1) + oid_xpath_suffix
        curr_oid_text = self.get_text(curr_oid_xpath, by='xpath')
        # 商品名称
        goods_name_xpath = "/html[1]/body[1]/div[5]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[1]/div[1]/div[2]/div[5]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/a[1]"
        result_goods_name = self.get_text(goods_name_xpath, by='xpath')
        self.assertEquals(search_goods_name, result_goods_name,
                          msg="搜索结果错误，当前oid：{}，当前搜索结果：{}".format(curr_oid_text, result_goods_name))

    # 【渠道】下拉框
    @pytest.mark.p0
    def test_channel_selector(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.CHANNEL_SELECTOR)
        channel_values = ["全部", "自营", "分销", "授权推广", "佣金托管", "定价托管"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(channel_values)):
            curr_value_xpath = selector_content_value_prefix + str(i+1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_channel_value = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_channel_value, channel_values[i])
        sleep(2)
        self.assert_no_404_errors()

    # 【快递单号】输入框
    @pytest.mark.p0
    def test_delivery_id_input_box(self):
        self.login_prt_env()
        search_delivery_id = 'SF2433560032528'
        self.input(TradeOrderPage.DELIVERY_ID_INPUT_BOX_SELECTOR, text=search_delivery_id)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        sleep(5)
        # 查看物流按钮
        look_logistics_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div/div[2]/div[2]/div/div/div[2]/span'
        self.click_xpath(look_logistics_xpath)
        # 物流详情页的物流单号
        delivery_id_xpath = "//div[normalize-space()='SF2433560032528']"
        result_delivery_id = self.get_text(delivery_id_xpath, by='xpath')
        self.assertEquals(search_delivery_id, result_delivery_id, msg="搜索结果错误，当前结果为：{}".format(result_delivery_id))

    # 【收货人姓名/手机号】输入框
    @pytest.mark.p0
    def test_name_or_phone_input_box(self):
        self.login_prt_env()
        search_name = "张小贤"
        self.input(TradeOrderPage.NAME_OR_PHONE_INPUT_BOX_SELECTOR, text=search_name)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        self.click(TradeOrderPage.SEARCH_BUTTON)
        sleep(5)
        # 获取买家昵称
        result_nick_name_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[3]/div/div'
        result_nick_name = self.get_text(result_nick_name_xpath, by='xpath').split("\n")[-1]  # 张**\n1********65\n北京市北京市昌平区回龙观街道**********\ntrust9573
        target_nick_name = "trust9573"
        self.assertEquals(target_nick_name, result_nick_name, msg="搜索结果错误，当前结果为：{}".format(result_nick_name))

    # 【推广者ID】下拉框
    @pytest.mark.p0
    def test_promoter_id_selector(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.PROMOTER_ID_SELECTOR)
        promoter_id_values = ["CPS达人ID", "团长ID", "快赚客ID", "授权推广者ID"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(promoter_id_values)):
            curr_value_xpath = selector_content_value_prefix + str(i+1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_promoter_id = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_promoter_id, promoter_id_values[i])
        sleep(1)
        self.assert_no_404_errors()

    # 【活动类型】下拉框
    @pytest.mark.p0
    def test_activity_order_selector(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.ACTIVITY_ORDER_SELECTOR)
        activity_kinds = ["全部", "非活动", "抽奖", "好运来", "买样后返", "拼团", "万人团", "n元m件"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(activity_kinds)):
            curr_value_xpath = selector_content_value_prefix + str(i + 1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_activity_value = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_activity_value, activity_kinds[i])
        sleep(2)
        self.assert_no_404_errors()

    # 【待发货时间】下拉框
    @pytest.mark.p0
    def test_shipping_time_selector(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.SHIPPING_TIME_SELECTOR)
        shipping_time_values = ["全部", "已过承诺发货时间", "需12小时内发货", "需24小时内发货", "需48小时内发货",
                                "需72小时内发货", "未设置承诺发货时间", "设置预售发货时间", "设置指定日期发货时间"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(shipping_time_values)):
            curr_value_xpath = selector_content_value_prefix + str(i + 1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_shipping_time = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_shipping_time, shipping_time_values[i])
        sleep(1)
        self.assert_no_404_errors()

    # 【买家昵称/ID】输入框
    @pytest.mark.p0
    def test_buyer_nickname_or_buyer_id(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        search_buyer_id = '2269857430'
        search_buyer_nick_name = 'fantasy17'
        # 通过 buyer_id 查询
        self.input(TradeOrderPage.BUYER_NICKNAME_ID_INPUT_BOX_SELECTOR, text=search_buyer_id)
        self.click_xpath("//button[@type='submit']//span[contains(text(),'查询')]")
        self.click_xpath("//button[@type='submit']//span[contains(text(),'查询')]")
        sleep(5)
        buyer_info_hover_xpath = '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div[5]/div/div[2]/div/div/div/div/div[2]/div[1]/div[2]/div[3]/div/div/div/div/div/div'
        result_nick_name = self.get_text(buyer_info_hover_xpath, by='xpath')
        self.assertEqual(result_nick_name, search_buyer_nick_name, msg="搜索错误，当前昵称：{}".format(result_nick_name))

    # 【订单标签】下拉框
    @pytest.mark.p0
    def test_order_label_selector(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.assert_no_404_errors()
        order_label_text = self.get_text(TradeOrderPage.ORDER_LABEL_TEXT, by='xpath')
        self.assertEquals(order_label_text, "订单标签")
        self.click_xpath(TradeOrderPage.ORDER_LABEL_SELECTOR)
        order_label_values = ['全部', '新疆集运', '内蒙古集运', '主动报备通过', '平台免考核', '与买家协商一致', '催发货', '优先发货', '已改地址']
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']/label'
        for i in range(len(order_label_values)):
            curr_value_xpath = selector_content_value_prefix + str(i+1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_order_label = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_order_label, order_label_values[i])
        sleep(1)
        self.assert_no_404_errors()

    # 【延长自动收货时间】下拉框
    @pytest.mark.p0
    def test_extend_auto_signed_time_selector(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.EXTEND_AUTO_SIGNED_TIME_SELECTOR)
        extend_signed_values = ["全部", "已延长自动收货时间", "未延长自动收货时间"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(extend_signed_values)):
            curr_value_xpath = selector_content_value_prefix + str(i + 1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_extend_type = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_extend_type, extend_signed_values[i])
        sleep(2)
        self.assert_no_404_errors()

    # 【隐私授权状态】下拉框
    @pytest.mark.p0
    def test_privacy_authorization_status_selector(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.PRIVACY_AUTHORIZATION_STATUS)
        privacy_authorization_values = ["全部", "未授权", "已授权"]
        selector_content_value_prefix = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div['
        selector_content_value_suffix = ']'
        for i in range(len(privacy_authorization_values)):
            curr_value_xpath = selector_content_value_prefix + str(i + 1) + selector_content_value_suffix
            if self.is_element_visible(curr_value_xpath):
                curr_authorization_value = self.get_text(curr_value_xpath, by='xpath')
                self.assertEquals(curr_authorization_value, privacy_authorization_values[i])
        sleep(2)
        self.assert_no_404_errors()

    # 【保证金退款】勾选框
    @pytest.mark.p0
    def test_security_deposit_checkbox(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.SECURITY_DEPOSIT_CHECKBOX)
        sleep(2)

    # 【退货补运费】勾选框
    @pytest.mark.p0
    def test_return_shipping_supplement_checkbox(self):
        self.login_prt_env()
        # 先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        self.click_xpath(TradeOrderPage.RETURN_SHIPPING_SUPPLEMENT_CHECKBOX)
        sleep(2)
        self.assert_no_404_errors()

    # 【展开全部】按钮
    @pytest.mark.p0
    def test_more_filtering(self):
        self.login_prt_env()
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        # 展开后可以看到【活动类型】筛选框了，以此来判断是否有点击成功
        self.assertEquals(self.is_element_visible(TradeOrderPage.ACTIVITY_ORDER_SELECTOR), True)
        self.assert_no_404_errors()

    # 【收起筛选】按钮
    @pytest.mark.p0
    def test_collapse_filtering_button(self):
        self.login_prt_env()
        # 需要先点击【展开全部】按钮
        self.click(TradeOrderPage.MORE_FILTERING_BUTTON)
        sleep(2)
        # 点击【收起筛选】按钮
        self.click_xpath(TradeOrderPage.COLLAPSE_FILTERING_BUTTON)
        # 收起后就看不到【活动类型】筛选框了，以此来判断是否有点击成功
        self.assertEquals(self.is_element_visible(TradeOrderPage.ACTIVITY_ORDER_SELECTOR), False)
        self.assert_no_404_errors()

    # 【重 置】按钮
    @pytest.mark.p0
    def test_re_setting(self):
        self.login_prt_env()
        self.click(TradeOrderPage.RESETTING_BUTTON)
        sleep(2)
        self.assert_no_404_errors()

    # 【查 询】按钮
    @pytest.mark.p0
    def test_search_order(self):
        self.login_prt_env()
        self.click_xpath(TradeOrderPage.SEARCH_BUTTON)
        sleep(2)
        self.assert_no_404_errors()