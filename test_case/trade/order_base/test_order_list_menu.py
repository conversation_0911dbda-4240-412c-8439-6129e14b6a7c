import pytest

from test_case.trade.order_base.base.check_method import <PERSON><PERSON>ethod
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf


class TestOrderListMenu(CheckMethod):

    def setUp(self):
        super(TestOrderList<PERSON>enu, self).setUp()
        self.to_order_list()
        self.click_element_by_element_name('展开全部按钮')
        # self.element_config = get_config_from_kconf('kwaishop.orderService.pcUiElementCheck')

    # 待发货：买家催发货按钮
    def test_unshipped_order_remind_delivery(self):
        self.click_and_check_button_by_element_name("待发货Tab")
        self.click_and_check_button_by_element_name("买家催发货按钮")
        self.check_search_result_by_case_name("催发货")

    # 待发货: 24小时需发货按钮
    def test_unshipped_order_24_hours_delivery(self):
        return

    # 订单状态下拉框查看
    def test_order_box_order_status(self):
        self.check_drop_down_box_by_element_name('订单状态下拉框')

    # 订单状态下拉框交互
    def test_order_box_order_status_interaction(self):
        self.menu_search_order("订单状态下拉框", "待发货")
        self.check_element_by_element_name("待发货订单状态")

    # 商品id+名称 交互
    def test_menu_order_item_id_name(self):
        self.menu_search_order("商品名称/ID输入框", "24741421654200")
        self.check_element_by_element_name("商品名称：南红（玛瑙）套链黄金测试类目30739")

    # 售后状态下拉框
    def test_order_box_after_sale_status(self):
        self.check_drop_down_box_by_element_name('售后状态下拉框')

    # 售后状态下拉框交互
    def test_order_box_after_sale_status_interaction(self):
        self.menu_search_order("售后状态下拉框", "售后成功")
        try:
            self.check_search_result_by_case_name("售后 ：售后成功1")
        except:
            self.check_search_result_by_case_name("售后 ：售后成功2")

    # 渠道下拉框
    def test_order_box_channel(self):
        self.check_drop_down_box_by_element_name('渠道下拉框')

    # 渠道下拉框交互
    def test_order_box_channel_interaction(self):
        self.menu_search_order("渠道下拉框", "超级链接竞价")
        self.check_search_result_by_case_name("超级链接竞价")

    # 推广者id类别下拉框
    def test_order_box_promoter_id_type(self):
        self.check_drop_down_box_by_element_name('推广者ID类别下拉框')

    # 推广者id类别输入框交互
    def test_order_box_promoter_id_type_interaction(self):
        self.menu_search_order("推广者ID类别输入框", "2465049547")
        self.check_search_result_by_case_name("推广者：分销")

    # 活动类型下拉框
    def test_order_box_activity_type(self):
        self.check_drop_down_box_by_element_name('活动类型下拉框')

    # 活动类型下拉框交互
    def test_order_box_activity_type_interaction(self):
        self.menu_search_order("活动类型下拉框", "买样后返")
        self.check_search_result_by_case_name("买样后返")

    # 待发货时间下拉框
    def test_order_box_delivery_time(self):
        self.check_drop_down_box_by_element_name('待发货时间下拉框')

    # 待发货时间下拉框交互
    def test_order_box_delivery_time_interaction(self):
        self.menu_search_order("待发货时间下拉框", "已过承诺发货时间")
        self.check_search_result_by_case_name("已过承诺发货时间")


    # 订单标签下拉框
    def test_order_box_order_label(self):
        self.check_drop_down_box_by_element_name('订单标签下拉框')


    # 延长自动收货时间下拉框
    def test_order_box_extend_receipt_time(self):
        self.check_drop_down_box_by_element_name('延长自动收货时间下拉框')

    # 隐私授权状态下拉框
    def test_order_box_privacy_authorization_status(self):
        self.check_drop_down_box_by_element_name('隐私授权状态下拉框')

    # 报备状态下拉框
    def test_order_box_report_status(self):
        self.check_drop_down_box_by_element_name('报备状态下拉框')

    # 订单状态下拉框-待付款
    def test_menu_order_status_unpaid(self):
        self.menu_search_order("订单状态下拉框", "待付款")
        self.check_element_by_element_name("待付款订单状态")


