import pytest

from test_case.trade.order_base.base.check_method import <PERSON><PERSON>ethod
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf


class TestOrderButton(CheckMethod):

    def setUp(self):
        super(Test<PERSON><PERSON>r<PERSON><PERSON><PERSON>, self).setUp()
        self.element_config = get_config_from_kconf('kwaishop.orderService.pcUiElementCheck')
        self.to_order_list()

    # 无法发货解决方案按钮
    def test_unable_deliver_solution(self):
        self.click_and_check_button_by_element_name("无法发货解决方案按钮")

    # 优先发货按钮
    def test_priority_delivery(self):
        self.click_and_check_button_by_element_name("优先发货设置按钮")

    # 待付款 修改价格按钮
    def test_unpaid_order_price_change(self):
        oid = 2506302560321957
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("修改价格按钮")
        print(1)

    # 待付款 关闭交易按钮
    def test_unpaid_order_close_transaction(self):
        oid = 2506302560321957
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("关闭交易按钮")

    # 待付款 订单详情按钮
    def test_unpaid_order_detail(self):
        oid = 2506302560321957
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("订单详情按钮")

    # 待发货 官方在线寄件按钮
    def test_unpaid_order_official_delivery(self):
        oid = 2520200482100287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("官方在线寄件按钮")
        print(1)

    # 待发货 发货按钮
    def test_undelivered_order_deliver(self):
        oid = 2520200482100287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("发货按钮")

    # 待发货 合并发货按钮
    def test_undelivered_order_merge_deliver(self):
        oid = 2520200482100287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("合并发货按钮")

    # 待发货 修改地址按钮
    def test_undelivered_order_modify_address(self):
        oid = 2520200482100287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("修改地址按钮")

    # 待发货 订单详情按钮
    def test_undelivered_order_detail(self):
        oid = 2520200482100287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("订单详情按钮")

    # 已发货 订单详情按钮
    def test_delivered_order_detail(self):
        oid = 2516801935201287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("订单详情按钮")
    # 已发货 延长收货时间按钮
    def test_delivered_order_extend_receipt_time(self):
        oid = 2516801935201287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("延长收货时间按钮")

    # 已发货 修改物流按钮
    def test_delivered_order_modify_logistics(self):
        oid = 2516801935201287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("修改物流按钮")

    # 已发货 追加包裹按钮
    def test_delivered_order_add_package(self):
        oid = 2519800168325287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("追加包裹按钮")

    # 已收货 订单详情按钮
    def test_received_order_detail(self):
        oid = 2507602560912458
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("订单详情按钮")

    # 已收货 修改物流按钮
    def test_received_order_modify_logistics(self):
        oid = 2507602560912458
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("修改物流按钮")

    # 已收货,追加包裹按钮
    def test_received_order_add_package(self):
        oid = 2521002322026841
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("追加包裹按钮")

    # 交易成功 订单详情按钮
    def test_success_order_detail(self):
        oid = 2515502560059287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("订单详情按钮")
        print(1)

    # 交易成功 打开售后入口按钮
    def test_success_order_modify_logistics(self):
        oid = 2515502560059287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("打开售后入口按钮")
        print(1)

    # 交易成功 追加包裹按钮
    @pytest.mark.skip
    def test_success_order_add_package(self):
        oid = 2515502560059287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("追加包裹按钮")
        print(1)

    # 查看物流左侧按钮
    def test_logistics_left_button(self):
        oid = 2516801935201287
        self.search_order(order_id=oid)
        self.click_and_check_button_by_element_name("查看物流左侧按钮")
        print(1)




