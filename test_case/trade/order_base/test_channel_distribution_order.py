from test_case.trade.order_base.base.check_method import Check<PERSON>ethod
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf

class TestChannelDistributionOrder(CheckMethod):

    def setUp(self):
        super(TestChannelDistributionOrder, self).setUp()
        self.special_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiSpecialOrder")
        self.element_config = get_config_from_kconf('kwaishop.orderService.pcUiElementCheck')
        self.normal_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiNormalOrder")

    # 渠道和推广信息-自营
    def test_channel_info_order(self):
        self.to_order_list()
        oid = 2515601783203706
        self.search_order(order_id=oid)
        self.check_order("渠道和推广信息-自营")

    # 渠道和推广信息-大链接
    def test_channel_super_link_order(self):
        self.to_order_list()
        oid = 2504202560177957
        self.search_order(order_id=oid)
        self.check_order("渠道和推广信息-大链接")

    # 渠道和推广信息-佣金托管
    def test_channel_commission_order(self):
        self.to_order_list()
        oid = 2430902560049160
        self.search_order(order_id=oid, before_six=True)
        self.check_order("渠道和推广信息-佣金托管")

    # 渠道和推广信息-定价托管
    def test_channel_pricing_order(self):
        self.to_order_list()
        oid = 2421100066328694
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("渠道和推广信息-定价托管")

    # 渠道和推广信息-分销+达人+预计佣金
    def test_channel_distribution_order(self):
        self.to_order_list()
        oid = 2501702181707458
        self.search_order(order_id=oid, before_six=True)
        self.check_order("渠道和推广信息-分销+达人+预计佣金")

    # 渠道和推广信息：分销+达人+阶梯佣金
    def test_channel_distribution_staircase_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True)
        self.check_order("渠道和推广信息：分销+达人+阶梯佣金")

    # 渠道和推广信息：分销信息+快赚客信息
    def test_channel_distribution_kzk_order(self):
        self.to_order_list(account='wb_huoyangyang')
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("渠道和推广信息：分销信息+快赚客信息")

    # 渠道和推广信息：分销信息+团长信息
    def test_channel_distribution_tz_order(self):
        oid = ****************
        self.to_order_list(account='wb_huoyangyang')
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("渠道和推广信息：分销信息+团长信息")