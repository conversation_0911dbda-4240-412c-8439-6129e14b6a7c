import logging
from time import sleep

from page_objects.trade_order.trade_order_page import TradeOrderPage
from test_case.trade.base import BaseTestCase
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf
from selenium.webdriver.common.keys import Keys
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 创建控制台处理器并设置级别
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
# 将处理器添加到日志记录器
logger.addHandler(console_handler)

class BaseMethod(BaseTestCase):

    def check_element_by_element_name(self, element_name, config=get_config_from_kconf()):
        element = config.get(element_name)
        self.check_element(element, element_name)

    def check_element(self, element, element_name):
        if element is not None:
            element_type = element.get('type')
            element_path = element.get('path')
            if element_path is None:
                raise Exception('element_path is None')
            if element_type == 'button':
                self.click_and_check_button(button=element, element_name=element_name)
            elif element_type == 'drop_down_box':
                self.check_drop_down_box(drop_down_box=element, element_name=element_name)
            else:
                self.check_element_base_info(element=element, element_name=element_name)
        else:
            raise Exception('元素名:{} 不存在'.format(element_name))

    def click_and_check_button_by_element_name(self, element_name, config=get_config_from_kconf()):
        button = config.get(element_name)
        self.click_and_check_button(button, element_name)

    def click_and_check_button(self, button, element_name):
        if button is None:
            raise Exception('button_name:{} 不存在'.format(element_name))
        button_path = button.get('path')
        if button.get('text') is not None:
            self.assert_text(button.get('text'), button_path)
            logger.debug('校验元素【{}】文案成功:{}'.format(element_name, button.get('text')))
        self.click_element(button, element_name)
        check_click = button.get('check_click')
        if check_click is None:
            return
        if check_click.get('check_404') is not None and check_click.get('check_404') == True:
            self.assert_no_404_errors()
            logger.debug('【{}】校验404成功'.format(element_name))
        if check_click.get('check_url') is not None:
            self.assert_equal(self.get_current_url().__contains__(check_click.get('check_url')), True,
                              msg='校验url失败,当前url:' + self.get_current_url() + ',预期url:' + check_click.get(
                                  'check_url'))
            logger.debug('【{}】校验url成功:url中包含{}'.format(element_name, check_click.get('check_url')))
        click_check_list = check_click.get('click_check_list')
        if click_check_list is not None:
            for check_item in click_check_list:
                if check_item.get('check_path') is None:
                    raise Exception('check_path is None')
                else:
                    if check_item.get('check_text') is not None:
                        try:
                            self.assert_text(check_item['check_text'], check_item['check_path'])
                            logger.debug(
                                '校验元素【{}】点击后内容成功:验证文本为:{}'.format(element_name, check_item['check_text']))
                        except:
                            raise Exception('校验元素【{}】点击后内容失败:验证文本为:{}'.format(element_name, check_item['check_text']))
                    else:
                        self.is_element_visible(check_item['check_path'])
                        logger.debug('校验元素【{}】点击事件成功'.format(element_name))
        return_event = check_click.get('return_event')
        if return_event is not None:
            event_type = return_event.get('event_type')
            if event_type is None:
                raise Exception('event_type is None')
            if event_type == 'switch':
                self.switch_to_tab(0)
                logger.debug(self.driver.window_handles)
                sleep(3)
            elif event_type == 'click':
                self.click_element(return_event.get('click_element'), "返回上级")
            else:
                raise Exception('event_type is error')

    def check_drop_down_box_by_element_name(self, element_name, config=get_config_from_kconf()):
        drop_down_box = config.get(element_name)
        self.check_drop_down_box(drop_down_box=drop_down_box, element_name=element_name)

    def check_drop_down_box(self, drop_down_box, element_name):
        if drop_down_box is None:
            raise Exception('元素名:{} 不存在'.format(element_name))
        drop_down_box_path = drop_down_box.get('path')

        if drop_down_box_path is None:
            raise Exception('path不存在')
        # 点击下拉框，展开全部
        self.click_element(drop_down_box, element_name)
        check_box = drop_down_box.get('check_box')
        if check_box is None:
            return
        box_path = "//div[@class='rc-virtual-list-holder-inner']"
        box_contents = check_box.get('box_contents')
        if box_path is None or box_contents is None:
            raise Exception('box_path or box_content is None')
        for i,box_content in enumerate(box_contents):
            full_path = box_path+ f'/div[{str(i+1)}]'
            if self.is_element_visible(full_path):
                cur_value = self.get_text(full_path)
                try:
                    self.assert_equal(cur_value,box_content)
                    logger.debug('校验下拉框内容成功:' + box_content)
                except:
                    raise Exception('校验下拉框内容失败:' + box_content)
            else:
                logger.debug('下拉框内容不可见:' + box_content)

    def scroll_drop_down_box(self, scroll_path):
        if scroll_path is None:
            raise Exception('scroll_path is None')
        # 滚动下拉框
        scrollable_div = self.find_element(scroll_path)
        self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollTop + 50;", scrollable_div)

    def check_elements_list_by_elements_name_list(self, elements_name_list, config=get_config_from_kconf()):
        for element_name in elements_name_list:
            self.check_element_by_element_name(element_name, config)

    def check_element_base_info_by_element_name(self, element_name, config=get_config_from_kconf()):
        element =config.get(element_name)
        self.check_element_base_info(element=element, element_name=element_name)

    def check_element_base_info(self, element, element_name):
        if element is not None:
            if element.get('path') is not None:
                path = element.get('path')
                if element.get('text') is not None:
                    text = element.get('text')
                    try:
                        element_text = self.get_text(path)
                        logger.debug("配置中：{},实际文案：{}".format(text, element_text))
                        self.assert_equal(text in element_text, True)
                        logger.debug('校验元素{}文案成功:{}' .format(element_name, text))
                    except:
                        logger.debug('校验元素{}文案失败:{}' .format(element_name, text))
                        raise Exception('校验元素{}文案失败:{}' .format(element_name, text))
                else:
                    self.is_element_visible(element.get('path'))
                    logger.debug('校验元素存在成功:' + element_name)
            else:
                raise Exception('元素名:{} 或其path不存在'.format(element_name))
        else:
            raise Exception('元素名:{} 不存在'.format(element_name))

    # 根据元素名，从配置获取元素，点击元素
    def click_element_by_element_name(self, element_name, config=get_config_from_kconf()):
        element = config.get(element_name)
        self.click_element(element=element,element_name=element_name)
    # 点击元素
    def click_element(self, element, element_name):
        before_handles = self.driver.window_handles
        if element is None:
            raise Exception('元素名:{} 不存在'.format(element_name))
        element_path = element.get('path')
        if element is not None and element_path is not None:
            logger.debug('点击元素:' + element_name)
            self.click(element_path)
            sleep(3)
            after_handles = self.driver.window_handles
            logger.debug(before_handles)
            logger.debug(after_handles)
            diff = list(set(after_handles) - set(before_handles))
            logger.debug(diff)
            if diff:
                new_window = diff[0]
                # 切换到新窗口
                self.driver.switch_to.window(new_window)
        else:
            raise Exception('元素名:{} 或其path不存在'.format(element_name))

    def input_element_by_element_name(self, element_name, text, config=get_config_from_kconf()):
        element = config.get(element_name)
        self.input_element(element=element, element_name=element_name, text=text)

    def input_element(self, element, element_name, text):
        if element is None:
            raise Exception('元素名:{} 不存在'.format(element_name))
        input_path = element.get('path')
        input_element = self.find_element(input_path)
        input_element.send_keys(Keys.CONTROL,"a")
        input_element.send_keys(Keys.BACKSPACE)
        self.input(input_path, text)

    # todo
    def loop_orders_find_element(self,element_name,order_tail,need_click=False):
        for i in range(1,10):
            order_path = TradeOrderPage.order_prev+str(i)+order_tail
            if not self.is_element_clickable(order_path):
                self.assert_text(element_name, order_path)
                if need_click:
                    self.click(order_path)
            else:
                raise Exception('未找到元素:'+element_name)

    def choose_or_input_element_by_element_name(self, element_name, text, config=get_config_from_kconf()):
        element = config.get(element_name)
        self.choose_or_input_element(element=element, element_name=element_name, text=text)

    # 根据元素名，填充对应文案
    def choose_or_input_element(self, element, element_name, text):
        element_type = element.get('type')
        if element_type is not None and element_type == 'drop_down_box':
            self.choose_drop_down_box(element, element_name, text)
        else:
            self.input_element(element, element_name, text)

    def choose_drop_down_box(self,drop_down_box, element_name, text):
        if drop_down_box is None:
            raise Exception('元素名:{} 不存在'.format(element_name))
        self.click_element(drop_down_box, element_name)
        check_box = drop_down_box.get('check_box')
        if check_box is None:
            return
        box_path = "//div[@class='rc-virtual-list-holder-inner']"
        box_contents = check_box.get('box_contents')
        if box_path is None or box_contents is None:
            raise Exception('box_path or box_content is None')
        for i,box_content in enumerate(box_contents):
            full_path = box_path+ f'/div[{str(i+1)}]'
            if box_content == text:
                self.click(full_path)
                return
        raise Exception('未在{}中找到{}对应的元素:'.format(element_name,text))





