import logging

from test_case.trade.order_base.base.base_method import BaseMethod
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf


class CheckMethod(BaseMethod):
    special_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiSpecialOrder")
    normal_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiNormalOrder")



    def check_elements_list(self, elements_list):
        if elements_list is None or type(elements_list) != dict:
            raise Exception('elements_list is None or type(elements_list) != dict')
        for element_name, element in elements_list.items():
            self.check_element(element, element_name)

    # 根据订单号查找订单
    def search_order(self,order_id, before_six=False,activity=True, start_date=None, config=get_config_from_kconf()):
        if before_six:
            self.click_element_by_element_name('6个月前订单')
            self.input_element_by_element_name('订单编号输入框', order_id, config)
            self.input_element_by_element_name('下单开始时间输入框', '2024-01-01 00:00:00')
            self.click_element_by_element_name('下单时间确定按钮')
            # self.input_element_by_element_name('下单结束时间输入框', end_date)
            self.click_element_by_element_name('下单时间确定按钮')
        else:
            self.click_element_by_element_name("近6个月订单")
            if activity:
                self.input_element_by_element_name('订单/活动编号输入框', order_id, config)
            else:
                self.input_element_by_element_name('订单编号输入框', order_id, config)
        if start_date is not None:
            self.input_element_by_element_name('下单开始时间输入框', start_date)
            self.click_element_by_element_name('下单时间确定按钮')
            # self.input_element_by_element_name('下单结束时间输入框', end_date)
            self.click_element_by_element_name('下单时间确定按钮')
        self.click_element_by_element_name('查询按钮')

    # def search_order(self,order_id,input_path=None, before_six=False):
    #     if before_six:
    #         self.click_element_by_element_name('6个月前订单')
    #         if input_path is not None:
    #             self.input(input_path, str(order_id))
    #         else:
    #             self.input_element_by_element_name('订单编号输入框超六', order_id)
    #     else:
    #         self.click_element_by_element_name("近6个月订单")
    #         if input_path is not None:
    #             self.input(input_path, str(order_id))
    #         else:
    #             self.input_element_by_element_name('订单编号输入框', order_id)
    #     self.click_element_by_element_name('查询按钮')


    def get_normal_elements_name_list(self, order):
        order_status = order.get('order_type')
        exclude_element_name_list = order.get('exclude_element_name_list')
        elements_list = []
        if order_status is not None:
            normal_elements_list = self.normal_order_kconf.get(order_status)
            if normal_elements_list is not None:
                elements_list.extend(normal_elements_list)
        if exclude_element_name_list is not None:
            elements_list = list(set(elements_list).difference(set(exclude_element_name_list)))
        # text_element_list = ["订单号", "订单创建时间", "承诺发货时间", "商品名称", "商品价格", "商品数量", "订单状态", "买家名称", "手机号", "地址信息", "在线支付价格"]
        # if before_six:
        #     # 给elements_list中每个在text_element_list中的元素后面追加'超六'
        #     elements_list = [element + '超六' if element in text_element_list else element for element in elements_list]
        return elements_list

    def check_order(self, order_name):
        order = self.special_order_kconf.get(order_name)
        if order is None:
            raise Exception("kconf中没有{}的配置".format(order_name))
        # 检查订单基本信息
        normal_elements_list = self.get_normal_elements_name_list(order)
        self.check_elements_list_by_elements_name_list(normal_elements_list)
        # 检查订单特殊信息
        elements_list = order.get('check_elements')
        self.check_elements_list(elements_list)

    def menu_search_order(self, element_name, text, config=get_config_from_kconf()):
        self.choose_or_input_element_by_element_name(element_name, text, config)
        self.click_element_by_element_name('查询按钮')

    def check_search_result_by_case_name(self, case_name, config=get_config_from_kconf(config='kwaishop.orderService.orderMenuSearchAndCheck')):
        case = config.get(case_name)
        if case is None:
            raise Exception("kconf中没有{}的配置".format(case_name))
        self.check_search_result(case)

    def check_search_result(self, case):
        skip = case.get('need_skip')
        if skip is not None and skip:
            return
        elements_list = case.get('check_elements')
        self.check_elements_list(elements_list)

