import pytest

from test_case.trade.order_base.base.check_method import <PERSON><PERSON>ethod
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf

class TestSpecialOrder(CheckMethod):

    def setUp(self):
        super(TestSpecialOrder, self).setUp()
        self.special_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiSpecialOrder")
        self.element_config = get_config_from_kconf('kwaishop.orderService.pcUiElementCheck')
        self.normal_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiNormalOrder")

    # # 西北集运
    # def test_xibei_delivery_order(self):
    #     oid = ****************
    #     self.search_order(order_id=oid, before_six=True)
    #     self.check_order("西北集运")

    # 新疆集运
    def test_xinjiang_delivery_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("新疆集运")

    # 内蒙古集运
    def test_neimeng_delivery_order(self):
        self.to_order_list("supply_express_account")
        oid = ****************
        self.search_order(order_id=oid, before_six=True)
        self.check_order("内蒙古集运")

    # 已改地址
    def test_modify_address_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("已改地址")

    # 店铺新客
    def test_new_customer_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("店铺新客")

    # 定金预售-待付定金
    def test_deposit_unpaid_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True)
        self.check_order("定金预售-待付定金")

    # 定金预售-待付尾款
    def test_balance_unpaid_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True)
        self.check_order("定金预售-待付尾款")

    # 定金预售-未付尾款，协商退定金
    def test_balance_unpaid_refunded_order(self):
        self.to_order_list()
        oid = 2435903444033458
        self.search_order(order_id=oid, before_six=True)
        self.check_order("定金预售-未付尾款，协商退定金")

    # 定金预售-超时未付定金
    def test_deposit_unpaid_overtime_order(self):
        self.to_order_list()
        oid = 2505502561179959
        self.search_order(order_id=oid)
        self.check_order("定金预售-超时未付定金")

    @pytest.mark.skip
    # 定金预售-超时未付尾款
    def test_balance_unpaid_overtime_order(self):
        self.to_order_list()
        oid = 2500902560003342
        self.search_order(order_id=oid)
        self.check_order("定金预售-超时未付尾款")

    # 在线支付优惠信息
    def test_online_pay_discount_order(self):
        self.to_order_list()
        oid = 2516902496003706
        self.search_order(order_id=oid)
        self.check_order("在线支付优惠信息")

    # 以旧换新先用后付抵扣
    def test_trade_in_pay_after_use_order(self):
        self.to_order_list()
        oid = 2426901854760079
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("以旧换新先用后付抵扣")

    # 待付款，剩余xx时间关闭
    def test_unpaid_not_close_order(self):
        self.to_order_list()
        oid = 2506302560321957
        self.search_order(order_id=oid)
        self.check_order("待付款，剩余xx时间关闭")

    # 待发货, 请在xx内发货
    def test_unship_not_shipping_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("待发货, 请在xx内发货")

    # 待发货，已超过承诺发货时间xx
    def test_unship_over_shipping_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("待发货，已超过承诺发货时间xx")

    # 待发货，配货中
    def test_unship_assembly_order(self):
        self.to_order_list("member_account2")
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("待发货，配货中")

    # 待发货，支付成功待用户签署协议 无订单

    # 催发货
    def test_urge_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("催发货")

    # 加急催发货
    def test_urgent_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("加急催发货")

    # 优先发货
    def test_priority_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("优先发货")

    @pytest.mark.skip
    # 部分发货
    def test_partial_ship_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("部分发货")

    # 已发货，确认收货还剩xx
    def test_shipped_not_receipt_order(self):
        self.to_order_list()
        oid = 2516801935201287
        self.search_order(order_id=oid)
        self.check_order("已发货，确认收货还剩xx")

    # 已发货，物流异常
    def test_shipped_logistics_exception_order(self):
        self.to_order_list()
        oid = 2508502240003342
        self.search_order(order_id=oid)
        self.check_order("已发货，物流异常")

    # 交易关闭，用户超时未签署协议
    def test_close_not_sign_order(self):
        self.to_order_list()
        oid = 2430402260808183
        self.search_order(order_id=oid, before_six=True)
        self.check_order("交易关闭，用户超时未签署协议")

    # 拼团，超时未拼团成功或拼团中订单已退款
    def test_team_purchase_over_time_order(self):
        self.to_order_list()
        oid = 2434702330409183
        self.search_order(order_id=oid, before_six=True)
        self.check_order("拼团，超时未拼团成功或拼团中订单已退款")

    # 买样后返
    def test_sample_return_order(self):
        self.to_order_list()
        oid = 2501502560000342
        self.search_order(order_id=oid, before_six=True)
        self.check_order("买样后返")

    # 顺丰包邮
    def test_sf_free_order(self):
        self.to_order_list()
        oid = 2514302081962222
        self.search_order(order_id=oid)
        self.check_order("顺丰包邮")

    # 推荐使用顺丰发货
    @pytest.mark.skip
    def test_sf_free_recommend_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("推荐使用顺丰发货")

        # 上门安装
    def test_installation_at_home(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True)
        self.check_order("上门安装")

    # 生鲜到家
    def test_fresh_delivery_order(self):
        self.to_order_list("supply_express_account")
        oid = ****************
        self.search_order(order_id=oid, before_six=True)
        self.check_order("生鲜到家")

    # 延迟发货，发货协商成功
    def test_delay_delivery_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("延迟发货，发货协商成功")

    # 延迟发货，发货免考核，已逾期
    def test_delay_delivery_overdue_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid)
        self.check_order("延迟发货，发货免考核，已逾期")

    # 赠品
    def test_gift_with_purchase_order(self):
        self.to_order_list()
        oid = ****************
        self.search_order(order_id=oid, before_six=True, start_date='2024-01-01 00:00:00')
        self.check_order("赠品")