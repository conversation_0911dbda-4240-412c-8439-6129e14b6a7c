from test_case.trade.order_base.base.check_method import <PERSON><PERSON>ethod
from test_case.trade.order_base.base.kconf_utils import get_config_from_kconf

class TestCrossBorderOrder(CheckMethod):

    def setUp(self):
        super(TestCrossBorderOrder, self).setUp()
        self.special_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiSpecialOrder")
        self.element_config = get_config_from_kconf('kwaishop.orderService.pcUiElementCheck')
        self.normal_order_kconf = get_config_from_kconf("kwaishop.orderService.pcUiNormalOrder")

    # 含税费
    def test_tax_order(self):
        self.to_order_list("clearance_account")
        oid = ****************
        self.search_order(order_id=oid, before_six=True, start_date='2025-01-01 00:00:00')
        self.check_order("含税费")

    # 清关模式：BBC保税仓
    def test_clearance_bbc_mode_order(self):
        self.to_order_list("clearance_account")
        oid = ****************
        self.search_order(oid, activity=False)
        self.check_order("清关模式：BBC保税仓")

    # 清关模式：CC行邮
    def test_clearance_cc_mode_order(self):
        self.to_order_list("clearance_account")
        oid = ****************
        self.search_order(oid, activity=False)
        self.check_order("清关模式：CC行邮")

    # 清关模式：BC直邮
    def test_clearance_bc_mode_order(self):
        self.to_order_list("clearance_account")
        oid = ****************
        self.search_order(oid, before_six=True)
        self.check_order("清关模式：BC直邮")
