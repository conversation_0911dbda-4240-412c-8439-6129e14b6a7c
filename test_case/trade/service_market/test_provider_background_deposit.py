import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_deposit import ProviderBackgroundDeposit
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundDeposit(BaseTestCase):
    """服务商后台-保证金页面"""

    # def test_apply_deposit_withdraw(self):
    #     # 申请保证金提退
    #
    #     # 登录
    #     self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")
    #
    #     # 点击"保证金"
    #     self.click(ProviderBackgroundLeftTab.deposit_tab)
    #     time.sleep(2)
    #
    #     # 点击"提退"
    #     self.click(ProviderBackgroundDeposit.deposit_withdraw_button)
    #     time.sleep(1)
    #
    #     # 点击"确认"
    #     self.click(ProviderBackgroundDeposit.popup_confirm_button)
    #     time.sleep(1)
    #
    #     # 输入支付宝账号
    #     self.type(ProviderBackgroundDeposit.ali_pay_account_input, ***********)
    #
    #     # 输入提退金额金额
    #     self.type(ProviderBackgroundDeposit.withdraw_money_input, 1)
    #
    #     # 输入验证码
    #     self.type(ProviderBackgroundDeposit.check_code_input, 888888)
    #
    #     # 点击"提退"
    #     self.click(ProviderBackgroundDeposit.deposit_withdraw_popup_withdraw_button)


    # 保证金加白功能有问题，先注释掉
    def test_create_deposit_order(self):
        # 创建保证金充值订单

        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(2)

        # 点击"充值"
        self.click(ProviderBackgroundDeposit.deposit_recharge_button)
        time.sleep(2)

        # 输入充值的金额
        self.type(ProviderBackgroundDeposit.deposit_recharge_popup_input, 1)
        self.assert_text("1.01元", ProviderBackgroundDeposit.deposit_recharge_popup_all_payment)

        # 点击"充值"
        self.click(ProviderBackgroundDeposit.deposit_recharge_popup_recharge_button)
        self.assert_text("支付", ProviderBackgroundDeposit.pc_cash_register_title)

    def test_normal_problem(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(2)

        # 点击常见问题
        self.click(ProviderBackgroundDeposit.normal_problem)
        time.sleep(5)

        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])

        current_url = self.get_current_url()


        assert current_url == 'https://open.kwaixiaodian.com/zone/new/docs/dev?pageSign=a96cb18622af748c96fb61d56d94c4461614263703283', "跳转链接错误"

    def test_select_electronic_certificate(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(5)

        # 获取电子凭证操作列
        select_electronic_certificate_list = self.find_elements(
            ProviderBackgroundDeposit.select_electronic_certificate_list)

        if len(select_electronic_certificate_list) > 0:
            select_electronic_certificate_list[0].click()
            time.sleep(1)

            self.assert_element_present(ProviderBackgroundDeposit.payment_account)

    def test_export_record(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(5)

        # 点击导出记录button
        self.click(ProviderBackgroundDeposit.export_record_button)
        time.sleep(3)
        download_button_list = self.find_elements(ProviderBackgroundDeposit.download_button)
        download_fail_button_list = self.find_elements(ProviderBackgroundDeposit.download_fail_button)
        assert len(download_button_list) > 0 or len(download_fail_button_list) > 0, "页面加载失败"

        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/exportRecord?exportType=1', "跳转链接错误"

    def test_export_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(2)

        # 点击导出button
        self.click(ProviderBackgroundDeposit.export_button)

    def test_select_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(2)

        # 点击操作类型下拉框
        self.click(ProviderBackgroundDeposit.operate_down_box)
        time.sleep(1)

        # 点击充值
        self.click(ProviderBackgroundDeposit.operate_charge)
        time.sleep(2)

        operate_type = self.get_text(ProviderBackgroundDeposit.operate_down_box)
        assert operate_type == '充值', "筛选类型错误"

        # 点击查询
        self.click(ProviderBackgroundDeposit.select_button)
        time.sleep(2)

        # 获取操作类型列
        operate_type_list = self.find_elements(ProviderBackgroundDeposit.operate_type_list)

        for i in range(len(operate_type_list)):
            assert operate_type_list[i].text == '充值', '查询结果错误'

    def test_remark_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(2)

        # 点击操作类型下拉框
        self.click(ProviderBackgroundDeposit.operate_down_box)
        time.sleep(1)

        # 点击充值
        self.click(ProviderBackgroundDeposit.operate_charge)
        time.sleep(2)

        operate_type = self.get_text(ProviderBackgroundDeposit.operate_down_box)
        assert operate_type == '充值', "筛选类型错误"

        # 点击重置
        self.click(ProviderBackgroundDeposit.remark_button)
        time.sleep(1)

        operate_type = self.get_text(ProviderBackgroundDeposit.operate_down_box)
        assert operate_type == '请选择操作类型', "筛选类型错误"
