import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_message_center import ProviderBackgroundMessageCenter
from .base import BaseTestCase


class TestProviderBackgroundMessageCenter(BaseTestCase):
    """服务商后台-消息中心页面"""

    def test_change_tab(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"消息中心"
        self.click(ProviderBackgroundLeftTab.message_center_tab)
        time.sleep(2)

        # 点击未读tab
        self.click(ProviderBackgroundMessageCenter.unread_tab)
        time.sleep(2)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

        # 点击已读tab
        self.click(ProviderBackgroundMessageCenter.read_tab)
        time.sleep(2)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

    def test_change_message_tab(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"消息中心"
        self.click(ProviderBackgroundLeftTab.message_center_tab)
        time.sleep(2)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

        # 点击系统消息tab
        self.click(ProviderBackgroundMessageCenter.system_message_tab)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

        # 点击平台公告tab
        self.click(ProviderBackgroundMessageCenter.platform_announcement_tab)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

        # 点击活动消息tab
        self.click(ProviderBackgroundMessageCenter.event_news_tab)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

        # 点击产品消息tab
        self.click(ProviderBackgroundMessageCenter.product_news_tab)

        message_list = self.find_elements(ProviderBackgroundMessageCenter.message)
        assert len(message_list) > 0, "页面加载失败"

    def test_read_message(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"消息中心"
        self.click(ProviderBackgroundLeftTab.message_center_tab)
        time.sleep(2)

        # 获取消息标题列
        message_title_list = self.find_elements(ProviderBackgroundMessageCenter.message_title_list)

        assert len(message_title_list) > 0, "获取消息列表失败"

        message_title_list[0].click()
        time.sleep(2)

        self.assert_element_present(ProviderBackgroundMessageCenter.message_detail)
        current_url = self.get_current_url()
        assert "https://fuwu.kwaixiaodian.com/fuwuMessageDetail?id=" in current_url, "跳转链接错误"
