import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_agent_operation_service import \
    MerchantBackgroundAgentOperationService
from page_objects.service_market.merchant_background_customer_service_outsourcing import \
    MerchantBackgroundCustomerServiceOutsourcing
from page_objects.service_market.merchant_background_left_tab import MerchantBackgroundLeftTab
from page_objects.service_market.merchant_background_my_discount_coupon import MerchantBackgroundMyDiscountCoupon
from page_objects.service_market.merchant_background_my_order import MerchantBackgroundMyOrder
from page_objects.service_market.merchant_background_picture_video_service import MerchantBackgroundPictureVideoService
from page_objects.service_market.merchant_background_tool_service import MerchantBackgroundToolService
from .base import BaseTestCase


class TestMerchantBackgroundEntrance(BaseTestCase):
    """商家后台-首页各页面入口"""

    def test_merchant_background_entrance(self):
        # 商家后台各页面切换

        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundToolService.service_info_title)

        # 点击"我的订单"
        self.click(MerchantBackgroundLeftTab.my_order_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundMyOrder.my_order_title)

        # 点击"工具服务"
        self.click(MerchantBackgroundLeftTab.tool_service_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundToolService.service_info_title)

        # 点击"客服外包"
        self.click(MerchantBackgroundLeftTab.customer_service_outsourcing_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundCustomerServiceOutsourcing.customer_service_outsourcing_title)

        # 点击"代运营服务"
        self.click(MerchantBackgroundLeftTab.agent_operation_service_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundAgentOperationService.agent_operation_service_title)

        # 点击"图片视频服务"
        self.click(MerchantBackgroundLeftTab.picture_video_service_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundPictureVideoService.picture_video_service_title)

        # 点击"我的优惠券"
        self.click(MerchantBackgroundLeftTab.my_discount_coupon_tab)
        time.sleep(3)

        self.assert_element_visible(MerchantBackgroundMyDiscountCoupon.my_coupon_title)
