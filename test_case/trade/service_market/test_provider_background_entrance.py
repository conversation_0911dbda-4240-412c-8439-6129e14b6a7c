import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundEntrance(BaseTestCase):
    """服务商后台-首页各页面入口"""

    def test_provider_background_entrance(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"客服外包数据"
        self.click(ProviderBackgroundLeftTab.customer_service_outsourcing_data_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.customer_service_outsourcing_data_title)

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.order_tracking_title)

        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.comment_management_title)

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.service_list_title)

        # 点击"我的资质"
        self.click(ProviderBackgroundLeftTab.my_qualifications_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.my_qualifications_title)

        # 点击"子账号管理"
        self.click(ProviderBackgroundLeftTab.subAccount_management_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.subAccount_management_title)

        # 点击"店铺信息"
        self.click(ProviderBackgroundLeftTab.shop_information_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.shop_information_title)

        # 点击"企业信息"
        self.click(ProviderBackgroundLeftTab.corporate_information_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundLeftTab.corporate_information_title)

        # 点击"服务商主页"
        self.click(ProviderBackgroundLeftTab.provider_homepage_tab)
        time.sleep(1)

        self.click(ProviderBackgroundLeftTab.provider_homepage_title)

        # 点击"保证金"
        self.click(ProviderBackgroundLeftTab.deposit_tab)
        time.sleep(1)

        self.click(ProviderBackgroundLeftTab.deposit_title)

        # 点击"发票管理"
        self.click(ProviderBackgroundLeftTab.invoice_management_tab)
        time.sleep(1)

        self.click(ProviderBackgroundLeftTab.invoice_management_title)

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(1)

        self.click(ProviderBackgroundLeftTab.fund_overview_title)

        # 点击"消息中心"
        self.click(ProviderBackgroundLeftTab.message_center_tab)
        time.sleep(1)

        self.click(ProviderBackgroundLeftTab.message_center_title)

        # 点击"客服工作台"
        self.click(ProviderBackgroundLeftTab.customer_service_desk_tab)
        time.sleep(1)

        # 切换到第二个标签页（客服工作台标签页）
        self.switch_to_window(1)

        # 切换到第一个标签页（服务商后台标签页）
        self.switch_to_window(0)

        self.click(ProviderBackgroundLeftTab.customer_service_desk_title)

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)
        time.sleep(1)

        self.click(ProviderBackgroundLeftTab.customer_service_settings_title)
