import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_agent_operation_service import \
    MerchantBackgroundAgentOperationService
from page_objects.service_market.merchant_background_left_tab import MerchantBackgroundLeftTab
from .base import BaseTestCase


class TestMerchantBackgroundAgentOperationService(BaseTestCase):
    """商家后台-代运营服务页面"""

    def test_change_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        # 点击"代运营服务"
        self.click(MerchantBackgroundLeftTab.agent_operation_service_tab)
        time.sleep(2)

        # 点击待支付tab
        self.click(MerchantBackgroundAgentOperationService.wait_pay_order_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '待付款', "订单状态错误"

        # 点击合同准备中tab
        self.click(MerchantBackgroundAgentOperationService.prepare_contract_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '合同准备中', "订单状态错误"

        # 点击合同待签署tab
        self.click(MerchantBackgroundAgentOperationService.wait_sign_contract_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '合同待签署', "订单状态错误"

        # 点击待服务tab
        self.click(MerchantBackgroundAgentOperationService.wait_served_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '待服务', "订单状态错误"

        # 点击服务中tab
        self.click(MerchantBackgroundAgentOperationService.in_service_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '服务中', "订单状态错误"

        # 点击已结束tab
        self.click(MerchantBackgroundAgentOperationService.close_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '已结束', "订单状态错误"

        # 点击已售后tab
        self.click(MerchantBackgroundAgentOperationService.refund_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_list_order_status_list = self.find_elements(
            MerchantBackgroundAgentOperationService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '退款成功', "订单状态错误"
