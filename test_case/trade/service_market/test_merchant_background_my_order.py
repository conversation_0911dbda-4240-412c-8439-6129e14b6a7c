import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_my_order import MerchantBackgroundMyOrder
from .base import BaseTestCase


class TestMerchantBackgroundMyOrder(BaseTestCase):
    """商家后台-我的订单页面"""

    def test_change_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

        # 点击"待评价"
        self.click(HomePage.wait_comment_button)
        time.sleep(2)

        # 点击待支付tab
        self.click(MerchantBackgroundMyOrder.wait_pay_order_status_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_status_list)

        for i in range(len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '待付款', "订单状态错误"

        # 点击支付成功tab
        self.click(MerchantBackgroundMyOrder.success_pay_order_status_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_status_list)

        for i in range(len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '支付成功', "订单状态错误"

        # 点击已售后tab
        self.click(MerchantBackgroundMyOrder.refund_order_status_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_status_list)

        for i in range(len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '退款成功', "订单状态错误"

        # 点击已关闭tab
        self.click(MerchantBackgroundMyOrder.close_order_status_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_status_list)

        for i in range(len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '已关闭', "订单状态错误"

    def test_select_button(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"待评价"
        self.click(HomePage.wait_comment_button)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 获取第一行的订单号
        order_id = order_list_order_id_list[0].text

        # 输入订单号
        self.type(MerchantBackgroundMyOrder.order_id_input, order_id)

        # 点击查询
        self.click(MerchantBackgroundMyOrder.order_list_select_button)
        time.sleep(3)

        order_list_order_id_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_id_list)
        assert len(order_list_order_id_list) == 1 and order_list_order_id_list[0].text == order_id, "获取订单列表错误"

    def test_remark_button(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"待评价"
        self.click(HomePage.wait_comment_button)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        order_id = order_list_order_id_list[0].text

        # 输入订单号
        self.type(MerchantBackgroundMyOrder.order_id_input, order_id)
        text = self.get_attribute(MerchantBackgroundMyOrder.order_id_input, "value")
        assert text == order_id, "订单编号输入错误"

        # 点击重置
        self.click(MerchantBackgroundMyOrder.remark_button)
        time.sleep(3)

        text_2 = self.get_attribute(MerchantBackgroundMyOrder.order_id_input, "value")
        assert text_2 == '', "重置按钮清空内容失败"

    def test_copy_button(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"待评价"
        self.click(HomePage.wait_comment_button)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(MerchantBackgroundMyOrder.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        copy_button_list = self.find_elements(MerchantBackgroundMyOrder.copy_button_list)

        # 点击第一行的复制按钮
        copy_button_list[0].click()
        time.sleep(0.5)
