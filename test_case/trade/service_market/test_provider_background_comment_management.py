import time
from unittest import skip

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_comment_management import ProviderBackgroundCommentManagement
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundCommentManager(BaseTestCase):
    """服务商后台-评价管理页面"""

    def test_select_by_order_id(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(2)

        # 获取评价列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundCommentManagement.order_id_list)
        assert len(order_id_list) > 0, "页面资源未加载成功或没有评价数据"

        # 获取第一行的订单号
        first_order_id = order_id_list[0].text.split(": ")[1]

        # 输入订单号
        self.type(ProviderBackgroundCommentManagement.order_id_input, first_order_id)
        time.sleep(2)

        # 点击查询
        self.click(ProviderBackgroundCommentManagement.select_button)
        time.sleep(2)

        # 获取订单列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundCommentManagement.order_id_list)
        assert len(order_id_list) == 1, "页面资源未加载成功或没有订单数据"
        assert "订单编号: " + first_order_id == order_id_list[0].text, "评价查询错误"

    def test_remark_button(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")
        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(2)

        # 获取评价列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundCommentManagement.order_id_list)
        assert len(order_id_list) > 0, "页面资源未加载成功或没有评价数据"

        # 获取第一行的订单号
        first_order_id = order_id_list[0].text.split(": ")[1]

        # 输入订单号
        self.type(ProviderBackgroundCommentManagement.order_id_input, first_order_id)

        # 点击查询
        self.click(ProviderBackgroundCommentManagement.select_button)
        time.sleep(1)

        text = self.get_attribute(ProviderBackgroundCommentManagement.order_id_input, 'value')

        assert text == first_order_id, "订单编号错误"

        # 点击重置按钮
        self.click(ProviderBackgroundCommentManagement.remark_button)
        time.sleep(2)

        text = self.get_attribute(ProviderBackgroundCommentManagement.order_id_input, 'value')

        assert text == '', "重置清空错误"

    def test_select_by_service_name(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(2)

        # 点击服务名称下拉框
        self.click(ProviderBackgroundCommentManagement.service_name_down_box)
        time.sleep(2)

        service_name_down_box_content_list = self.find_elements(
            ProviderBackgroundCommentManagement.service_name_down_box_content_list)

        for i in range(len(service_name_down_box_content_list)):
            if service_name_down_box_content_list[i].text == 'prt自动化-更新服务-勿动':
                service_name_down_box_content_list[i].click()
                time.sleep(2)

                # 点击查询
                self.click(ProviderBackgroundCommentManagement.select_button)
                time.sleep(2)

                comment_list_service_name_list = self.find_elements(
                    ProviderBackgroundCommentManagement.comment_list_service_name_list)

                for j in range(len(comment_list_service_name_list)):
                    assert comment_list_service_name_list[j].text.split("：")[1] == 'prt自动化-更新服务-勿动', "评论搜索结果服务名称错误"
                break
    @skip
    def test_select_by_service_function_score(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(2)

        # 点击服务功能下拉框
        self.click(ProviderBackgroundCommentManagement.function_down_box)
        time.sleep(5)

        service_function_down_box_content_list = self.find_elements(
            ProviderBackgroundCommentManagement.service_function_down_box_content_list)

        assert service_function_down_box_content_list[0].text == '1星', "下拉框内容获取错误"

        service_function_down_box_content_list[0].click()
        time.sleep(2)

        # 点击查询
        self.click(ProviderBackgroundCommentManagement.select_button)
        time.sleep(2)

        score_status = self.get_attribute(
            ProviderBackgroundCommentManagement.comment_list_first_row_service_function_second_score, "aria-checked")

        assert score_status == 'false', "按照服务功能查询结果错误"

    @skip
    def test_select_by_service_effect_score(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(2)

        # 点击使用效果下拉框
        self.click(ProviderBackgroundCommentManagement.effect_down_box)
        time.sleep(2)

        effect_down_box_content_list = self.find_elements(
            ProviderBackgroundCommentManagement.effect_down_box_content_list)

        assert effect_down_box_content_list[0].text == '1星', "下拉框内容获取错误"

        effect_down_box_content_list[0].click()
        time.sleep(2)

        # 点击查询
        self.click(ProviderBackgroundCommentManagement.select_button)
        time.sleep(2)

        score_status = self.get_attribute(
            ProviderBackgroundCommentManagement.comment_list_first_row_effect_second_score, "aria-checked")

        assert score_status == 'false', "按照使用效果查询结果错误"

    @skip
    def test_select_by_service_attitude_score(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"评价管理"
        self.click(ProviderBackgroundLeftTab.comment_management_tab)
        time.sleep(2)

        # 点击服务态度下拉框
        self.click(ProviderBackgroundCommentManagement.attitude_down_box)
        time.sleep(5)

        attitude_down_box_content_list = self.find_elements(
            ProviderBackgroundCommentManagement.attitude_down_box_content_list)

        assert attitude_down_box_content_list[0].text == '1星', "下拉框内容获取错误"

        attitude_down_box_content_list[0].click()
        time.sleep(2)

        # 点击查询
        self.click(ProviderBackgroundCommentManagement.select_button)
        time.sleep(2)

        score_status = self.get_attribute(
            ProviderBackgroundCommentManagement.comment_list_first_row_attitude_second_score, "aria-checked")

        assert score_status == 'false', "按照使用效果查询结果错误"
