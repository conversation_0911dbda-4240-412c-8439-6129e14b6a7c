import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_home import ProviderBackgroundHome
from .base import BaseTestCase


class TestProviderBackgroundHome(BaseTestCase):
    """服务商后台-首页页面"""

    def test_banner_jump(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击banner
        self.click(ProviderBackgroundHome.banner)
        time.sleep(4)

        # 跳转到开放平台公告页面
        current_url = self.get_current_url()
        assert current_url == 'https://open.kwaixiaodian.com/zone/new/announcement/detail?cateId=all&pageSign=a0ceed935a01b7714d4ddcea801cabd71678332413818'

    def test_select_all(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击查看全部button
        self.click(ProviderBackgroundHome.select_all_button)
        time.sleep(2)

        # 跳转到消息中心页面
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/fuwuMessageCenter'

    def test_go_to_fund_withdraw(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击去提现button
        self.click(ProviderBackgroundHome.go_to_fund_withdraw_button)
        time.sleep(2)

        # 跳转到资金总览页面
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/funds'

    def test_today_order_area(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击今日销售单量区域
        self.click(ProviderBackgroundHome.today_order_area)
        time.sleep(2)

        # 跳转到订单查询页面
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/orderManage'

    def test_today_money_area(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击今日销售金额区域
        self.click(ProviderBackgroundHome.today_money_area)
        time.sleep(2)

        # 跳转到订单查询页面
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/orderManage'
