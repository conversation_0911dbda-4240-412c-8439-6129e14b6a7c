import datetime
import time

import pytest

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.im import Im
from page_objects.service_market.merchant_background_my_order import MerchantBackgroundMyOrder
from page_objects.service_market.provider_homepage import ProviderHomepage
from page_objects.service_market.service_detail import ServiceDetail
from .base import BaseTestCase


class TestMerchantIm(BaseTestCase):
    """商家端进行IM会话"""
    @pytest.mark.skip("找不到元素暂时skip")
    def test_my_order(self):
        # 我的订单页-联系服务商入口

        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"待评价"
        self.click(HomePage.wait_comment_button)

        time.sleep(2)

        # 获取联系服务商按钮
        im_button_list = self.find_elements(MerchantBackgroundMyOrder.im_button)
        assert len(im_button_list) > 0, "订单列表加载失败"

        # 点击"联系服务商"
        im_button_list[0].click()
        time.sleep(5)

        # 切换到第二个tab
        self.switch_to_window(1)

        message = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S_%f')

        # 输入内容
        # self.type(Im.im_input, message)

        # 点击"发送"
        self.click(Im.im_send_button)
        time.sleep(2)

        # 获取IM聊天框的消息列表
        im_message_list = self.find_elements(Im.im_message_list)

        # 检查发送的最后一条消息
        assert im_message_list[-1].text == message, 'im 消息核对失败'

    def test_service_detail(self):
        # 服务详情页-联系服务商入口

        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        service_url = 'https://fuwu.kwaixiaodian.com/detail?id=15329376526103'

        # 进入服务详情页
        self.open(service_url)
        time.sleep(2)

        # 点击"联系服务商"
        self.click(ServiceDetail.contact_service_provider_button)
        time.sleep(2)

        # 切换到第二个tab
        self.switch_to_window(1)

        message = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S_%f')

        # 输入内容
        self.type(Im.im_input, message)

        # 点击"发送"
        self.click(Im.im_send_button)
        time.sleep(2)

        # 获取IM聊天框的消息列表
        im_message_list = self.find_elements(Im.im_message_list)

        # 检查发送的最后一条消息
        assert im_message_list[-1].text == message, 'im 消息核对失败'

    def test_provider_homepage(self):
        # 服务商主页-联系服务商入口

        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        service_url = 'https://fuwu.kwaixiaodian.com/detail?id=15329376526103'

        # 进入服务详情页
        self.open(service_url)
        time.sleep(2)

        # 点击服务商头像
        self.click(ServiceDetail.provider_logo)

        # # 切换到第二个标签页（服务商主页标签页）
        # self.switch_to_window(1)
        # time.sleep(2)

        # 点击"联系服务商"
        self.click(ProviderHomepage.contact_service_provider_button)
        time.sleep(2)

        message = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S_%f')

        # 输入内容
        self.type(Im.im_input, message)

        # 点击"发送"
        self.click(Im.im_send_button)
        time.sleep(2)

        # 获取IM聊天框的消息列表
        im_message_list = self.find_elements(Im.im_message_list)

        # 检查发送的最后一条消息
        assert im_message_list[-1].text == message, 'im 消息核对失败'
