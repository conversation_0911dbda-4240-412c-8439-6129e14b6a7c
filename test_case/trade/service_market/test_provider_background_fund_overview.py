import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_fund_overview import ProviderBackgroundFundOverview
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundFundOverview(BaseTestCase):
    """服务商后台-资金总览页面"""

    def test_apply_fund_withdraw(self):
        # 申请余额提现

        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)
        unsettled_amount = float(self.get_text(ProviderBackgroundFundOverview.unsettled_amount))

        # 点击"提现"
        self.click(ProviderBackgroundFundOverview.withdraw_button)

        # 输入支付宝账号
        self.type(ProviderBackgroundFundOverview.ali_pay_account_input, ***********)

        # 输入提退金额金额
        self.type(ProviderBackgroundFundOverview.withdraw_money_input, 1)

        # 输入验证码
        self.type(ProviderBackgroundFundOverview.check_code_input, 888888)

        # 点击"提现"
        self.click(ProviderBackgroundFundOverview.fund_withdraw_popup_withdraw_button)
        if unsettled_amount >= 1:
            self.assert_text("申请提现成功", ProviderBackgroundFundOverview.message)
        else:
            self.assert_text("店铺资金余额不足，无法提现！", ProviderBackgroundFundOverview.message)

    def test_select_by_order_id(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        order_id = order_list_order_id_list[0].text.split(": ")[1]

        # 输入订单编号
        self.type(ProviderBackgroundFundOverview.order_id_input, order_id)

        # 点击查询
        self.click(ProviderBackgroundFundOverview.select_button)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) == 1 and \
               order_list_order_id_list[0].text.split(": ")[1] == order_id, "根据订单编号查询错误"

    def test_select_by_settlement_status(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 点击结算状态下拉框
        self.click(ProviderBackgroundFundOverview.settlement_status_down_box)
        time.sleep(2)

        # 点击已入账
        self.click(ProviderBackgroundFundOverview.settlement_status_completed)

        # 点击查询
        self.click(ProviderBackgroundFundOverview.select_button)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        order_list_settlement_status_list = self.find_elements(
            ProviderBackgroundFundOverview.order_list_settlement_status_list)

        for i in range(len(order_list_settlement_status_list)):
            assert order_list_settlement_status_list[i].text == '已入账', "订单结算状态错误"

    def test_remark_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        order_id = order_list_order_id_list[0].text.split(": ")[1]

        # 输入订单编号
        self.type(ProviderBackgroundFundOverview.order_id_input, order_id)

        text = self.get_attribute(ProviderBackgroundFundOverview.order_id_input, 'value')

        assert text == order_id, "输入订单号错误"

        # 点击重置button
        self.click(ProviderBackgroundFundOverview.remark_button)
        time.sleep(2)

        text_2 = self.get_attribute(ProviderBackgroundFundOverview.order_id_input, 'value')

        assert text_2 == '', "重置按钮清空错误"

    def test_export_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 点击导出button
        self.click(ProviderBackgroundFundOverview.export_button)
        self.assert_text("申请导出成功", ProviderBackgroundFundOverview.export_success_message)

    def test_export_record_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 点击导出记录button
        self.click(ProviderBackgroundFundOverview.export_record_button)
        time.sleep(2)

        current_url = self.get_current_url()

        assert current_url == 'https://fuwu.kwaixiaodian.com/exportRecord?exportType=3', "跳转链接错误"

        download_button_list = self.find_elements(ProviderBackgroundFundOverview.download_button)

        assert len(download_button_list) > 0, "页面加载失败"

    def test_withdraw_record_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 点击提现记录button
        self.click(ProviderBackgroundFundOverview.withdraw_record_button)
        time.sleep(2)

        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/withdrawRecord', "跳转链接错误"

        # self.assert_element_present(ProviderBackgroundFundOverview.withdraw_record_list)

    def test_change_tab(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 点击每月账单tab
        self.click(ProviderBackgroundFundOverview.month_bill_tab)
        time.sleep(2)

        export_bill_button_list = self.find_elements(ProviderBackgroundFundOverview.export_bill_button)
        assert len(export_bill_button_list) > 0, "页面加载失败"

    def test_month_bill_export_record_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"资金总览"
        self.click(ProviderBackgroundLeftTab.fund_overview_tab)
        time.sleep(2)

        order_list_order_id_list = self.find_elements(ProviderBackgroundFundOverview.order_list_order_id_list)

        assert len(order_list_order_id_list) > 0, "获取订单列表失败"

        # 点击每月账单tab
        self.click(ProviderBackgroundFundOverview.month_bill_tab)
        time.sleep(2)

        # 点击导出记录button
        self.click(ProviderBackgroundFundOverview.month_bill_export_record_button)
        time.sleep(2)

        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/exportRecord?exportType=8', "跳转链接错误"

        self.assert_element_present(ProviderBackgroundFundOverview.month_bill_export_record_apply_export_time)
