import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_left_tab import MerchantBackgroundLeftTab
from page_objects.service_market.merchant_background_picture_video_service import MerchantBackgroundPictureVideoService
from .base import BaseTestCase


class TestMerchantBackgroundPictureVideoService(BaseTestCase):
    """商家后台-图片视频服务"""

    def test_change_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        # 点击"图片视频服务"
        self.click(MerchantBackgroundLeftTab.picture_video_service_tab)
        time.sleep(2)

        # 点击待付款
        self.click(MerchantBackgroundPictureVideoService.wait_pay_order_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(
            MerchantBackgroundPictureVideoService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '待付款', "订单状态错误"

        # 点击待验收
        self.click(MerchantBackgroundPictureVideoService.to_be_accepted_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(
            MerchantBackgroundPictureVideoService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '待验收', "订单状态错误"

        # 点击已验收
        self.click(MerchantBackgroundPictureVideoService.accepted_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(
            MerchantBackgroundPictureVideoService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '已验收', "订单状态错误"

        # 点击已取消
        self.click(MerchantBackgroundPictureVideoService.close_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(
            MerchantBackgroundPictureVideoService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '已取消', "订单状态错误"

        # 点击已售后
        self.click(MerchantBackgroundPictureVideoService.refund_tab)
        time.sleep(2)

        order_list_order_status_list = self.find_elements(
            MerchantBackgroundPictureVideoService.order_list_order_status_list)

        for i in range(1, len(order_list_order_status_list)):
            assert order_list_order_status_list[i].text == '退款成功', "订单状态错误"
