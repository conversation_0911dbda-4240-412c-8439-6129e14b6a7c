import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_customer_service_desk import ProviderBackgroundCustomerServiceDesk
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundCustomerServiceDesk(BaseTestCase):
    """服务商后台-客服工作台页面"""

    def test_jump_url(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"客服工作台"
        self.click(ProviderBackgroundLeftTab.customer_service_desk_tab)

        # 切换到第二个标签页（客服工作台标签页）
        self.switch_to_window(1)
        time.sleep(2)

        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/zone/dashboard/im/workbanch?isMainAccount=1&isChildAccount=0', "跳转链接错误"
