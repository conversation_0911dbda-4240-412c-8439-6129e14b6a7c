import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_customer_service_outsourcing import \
    MerchantBackgroundCustomerServiceOutsourcing
from page_objects.service_market.merchant_background_left_tab import MerchantBackgroundLeftTab
from .base import BaseTestCase


class TestMerchantBackgroundCustomerServiceOutsourcing(BaseTestCase):
    """商家后台-客服外包页面"""

    def test_change_order_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")
        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        # 点击"客服外包"
        self.click(MerchantBackgroundLeftTab.customer_service_outsourcing_tab)
        time.sleep(2)

        # 点击待支付tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.wait_pay_order_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '待付款', "订单状态错误"

        # 点击合同准备中tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.prepare_contract_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '合同准备中', "订单状态错误"

        # 点击合同待签署tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.wait_sign_contract_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '甲方已签署,待乙方签署', "订单状态错误"

        # 点击待服务tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.wait_served_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '乙方已签署,待服务', "订单状态错误"

        # 点击服务中tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.in_service_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '服务中', "订单状态错误"

        # 点击已结束tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.close_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '已结束', "订单状态错误"

        # 点击已售后tab
        self.click(MerchantBackgroundCustomerServiceOutsourcing.refund_tab)
        time.sleep(2)

        # 获取订单列表的订单状态
        order_status_list = self.find_elements(MerchantBackgroundCustomerServiceOutsourcing.order_status_list)

        for i in range(1, len(order_status_list)):
            assert order_status_list[i].text == '退款成功', "订单状态错误"

    def test_change_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        # 点击"客服外包"
        self.click(MerchantBackgroundLeftTab.customer_service_outsourcing_tab)
        time.sleep(2)

        # 点击效果数据
        self.click(MerchantBackgroundCustomerServiceOutsourcing.perform_data_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundCustomerServiceOutsourcing.sub_account_box)

        # 点击订单预览
        self.click(MerchantBackgroundCustomerServiceOutsourcing.order_overview_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundCustomerServiceOutsourcing.all_order_tab)

    def test_check_perform_data_by_order_id(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        # 点击"客服外包"
        self.click(MerchantBackgroundLeftTab.customer_service_outsourcing_tab)
        time.sleep(2)

        # 点击效果数据
        self.click(MerchantBackgroundCustomerServiceOutsourcing.perform_data_tab)
        time.sleep(10)

        # 点击订单号下拉框
        self.click(MerchantBackgroundCustomerServiceOutsourcing.order_id_down_box)
        time.sleep(3)

        order_id_down_box_content_list = self.find_elements(
            MerchantBackgroundCustomerServiceOutsourcing.order_id_down_box_content_list)

        assert len(order_id_down_box_content_list) > 0, "获取订单号列表失败"

        order_id_down_box_content_list[0].click()
        time.sleep(2)

        self.assert_element_present(MerchantBackgroundCustomerServiceOutsourcing.customer_service_undertaker)
