import time

import pytest

from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_subAccount_management import ProviderBackgroundSubAccountManagement
from .base import BaseTestCase


class TestProviderBackgroundSubAccountManagement(BaseTestCase):
    """服务商后台-子账号管理页面"""

    @pytest.mark.skip
    def test_create_and_delete_role(self):
        # 创建/删除角色

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"子账号管理"
        self.click(ProviderBackgroundLeftTab.subAccount_management_tab)
        time.sleep(2)

        # 点击"角色"
        self.click(ProviderBackgroundSubAccountManagement.role_tab)
        time.sleep(2)

        # 获取角色列表的角色名称列
        role_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.role_name_list)

        # 获取角色列表的删除按钮列
        delete_button_list = self.find_elements(ProviderBackgroundSubAccountManagement.role_delete_button_list)

        for i in range(len(role_name_list)):
            # 如果"UI自动化"同名角色存在
            if role_name_list[i].text == 'UI自动化':
                # 点击对应行的"删除"
                delete_button_list[i - 1].click()
                # 点击"确认"
                self.click(ProviderBackgroundSubAccountManagement.popup_confirm_button)
                self.assert_text("操作成功!", ProviderBackgroundSubAccountManagement.message)
                time.sleep(2)

        # 点击"新增角色"
        self.click(ProviderBackgroundSubAccountManagement.add_role_button)

        # 输入角色名称
        self.type(ProviderBackgroundSubAccountManagement.role_name_input, 'UI自动化')

        # 输入角色简介
        self.type(ProviderBackgroundSubAccountManagement.role_profile_input, 'UI自动化')

        # 点击角色权限下拉框
        self.click(ProviderBackgroundSubAccountManagement.role_permissions_drop_down_box)
        time.sleep(2)

        # 选择"订单管理"权限
        self.click(ProviderBackgroundSubAccountManagement.order_management_role_permissions)

        # 点击"确定"
        self.click(ProviderBackgroundSubAccountManagement.add_role_popup_confirm_button)
        time.sleep(0.5)
        self.assert_text("操作成功!", ProviderBackgroundSubAccountManagement.message)

    def test_create_and_delete_subAccount(self):
        # 创建/删除子账号

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"子账号管理"
        self.click(ProviderBackgroundLeftTab.subAccount_management_tab)
        time.sleep(2)

        # 获取子账号列表的账号名称列
        subAccount_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_name_list)

        # 获取子账号列表的删除按钮列
        delete_button_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_delete_button_list)

        for i in range(len(subAccount_name_list)):
            # 如果"UI自动化"同名角色存在
            if subAccount_name_list[i].text == 'UI自动化':
                # 点击对应行的"删除"
                delete_button_list[i].click()
                time.sleep(2)
                # 点击"确认"
                self.click(ProviderBackgroundSubAccountManagement.popup_confirm_button)
                time.sleep(0.5)
                self.assert_text("操作成功!", ProviderBackgroundSubAccountManagement.message)
                time.sleep(2)

        # 点击"创建子账号"
        self.click(ProviderBackgroundSubAccountManagement.create_subAccount_button)

        # 输入账号名称
        self.type(ProviderBackgroundSubAccountManagement.subAccount_name_input, 'UI自动化')

        # 输入角色简介
        self.type(ProviderBackgroundSubAccountManagement.remark_input, 'UI自动化')

        # 点击角色权限下拉框
        self.click(ProviderBackgroundSubAccountManagement.role_drop_down_box)
        time.sleep(5)

        assert self.is_element_visible(
            ProviderBackgroundSubAccountManagement.role_drop_down_box_role_list) == True, "角色权限获取失败"

        # 点击角色权限
        self.click(ProviderBackgroundSubAccountManagement.role_drop_down_box_role_list)

        # 输入登录手机
        self.type(ProviderBackgroundSubAccountManagement.login_phone_input, ***********)

        # 点击"确定"
        self.click(ProviderBackgroundSubAccountManagement.create_subAccount_popup_confirm_button)
        time.sleep(0.5)
        # self.assert_text("操作成功!", ProviderBackgroundSubAccountManagement.message)

    def test_enable_and_disable_subAccount(self):
        # 启用/禁用子账号

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"子账号管理"
        self.click(ProviderBackgroundLeftTab.subAccount_management_tab)
        time.sleep(2)

        # 获取子账号列表的账号名称列
        subAccount_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_name_list)

        # 获取子账号列表的启用/停用按钮列
        subuAccount_enable_and_disable_button_list = self.find_elements(
            ProviderBackgroundSubAccountManagement.subAccount_enable_and_disable_button_list)

        # 获取子账号状态列
        subAccount_status_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_status_list)

        for i in range(len(subAccount_name_list)):
            # 如果"UI自动化"同名角色存在
            if subAccount_name_list[i].text == '自动化启用停用':
                if subAccount_status_list[i].text == '启用':
                    subuAccount_enable_and_disable_button_list[i].click()
                    time.sleep(2)
                    self.click(ProviderBackgroundSubAccountManagement.popup_confirm_button)
                    time.sleep(5)

                    # 获取子账号列表的账号名称列
                    subAccount_name_list = self.find_elements(
                        ProviderBackgroundSubAccountManagement.subAccount_name_list)

                    # 获取子账号状态列
                    subAccount_status_list = self.find_elements(
                        ProviderBackgroundSubAccountManagement.subAccount_status_list)

                    for j in range(len(subAccount_name_list)):
                        if subAccount_name_list[i].text == '自动化启用停用':
                            assert subAccount_status_list[i].text == '停用', '子账号状态错误'
                            break
                    break
                elif subAccount_status_list[i].text == '停用':
                    subuAccount_enable_and_disable_button_list[i].click()
                    time.sleep(2)
                    self.click(ProviderBackgroundSubAccountManagement.popup_confirm_button)
                    time.sleep(5)

                    # 获取子账号列表的账号名称列
                    subAccount_name_list = self.find_elements(
                        ProviderBackgroundSubAccountManagement.subAccount_name_list)

                    # 获取子账号状态列
                    subAccount_status_list = self.find_elements(
                        ProviderBackgroundSubAccountManagement.subAccount_status_list)

                    for j in range(len(subAccount_name_list)):
                        if subAccount_name_list[i].text == '自动化启用停用':
                            assert subAccount_status_list[i].text == '启用', '子账号状态错误'
                            break
                    break

    def test_edit_subAccount(self):
        # 编辑子账号

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"子账号管理"
        self.click(ProviderBackgroundLeftTab.subAccount_management_tab)
        time.sleep(2)

        # 获取子账号列表的账号名称列
        subAccount_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_name_list)

        # 获取子账号列表的启用/停用按钮列
        subAccount_edit_button_list_list = self.find_elements(
            ProviderBackgroundSubAccountManagement.subAccount_edit_button_list)

        for i in range(len(subAccount_name_list)):
            if subAccount_name_list[i].text == '自动化已编辑':
                subAccount_edit_button_list_list[i].click()
                time.sleep(2)
                # 输入账号名称
                self.type(ProviderBackgroundSubAccountManagement.subAccount_name_input, '自动化未编辑')
                self.click(ProviderBackgroundSubAccountManagement.create_subAccount_popup_confirm_button)
                time.sleep(5)

                # 获取子账号列表的账号名称列
                subAccount_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_name_list)

                for j in range(len(subAccount_name_list)):
                    if subAccount_name_list[j].text == '自动化未编辑':
                        break
                    assert j != len(subAccount_name_list) - 1, "子账号编辑失败"

                break
            elif subAccount_name_list[i].text == '自动化未编辑':
                subAccount_edit_button_list_list[i].click()
                time.sleep(2)
                # 输入账号名称
                self.type(ProviderBackgroundSubAccountManagement.subAccount_name_input, '自动化已编辑')
                self.click(ProviderBackgroundSubAccountManagement.create_subAccount_popup_confirm_button)
                time.sleep(5)

                # 获取子账号列表的账号名称列
                subAccount_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.subAccount_name_list)

                for j in range(len(subAccount_name_list)):
                    if subAccount_name_list[j].text == '自动化已编辑':
                        break
                    assert j != len(subAccount_name_list) - 1, "子账号编辑失败"

                break

    def test_edit_role(self):
        # 编辑角色

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"子账号管理"
        self.click(ProviderBackgroundLeftTab.subAccount_management_tab)
        time.sleep(2)

        # 点击"角色"
        self.click(ProviderBackgroundSubAccountManagement.role_tab)
        time.sleep(2)

        # 获取角色列表的角色名称列
        role_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.role_name_list)

        # 获取角色列表的编辑按钮列
        edit_button_list = self.find_elements(ProviderBackgroundSubAccountManagement.role_edit_button_list)

        for i in range(len(role_name_list)):
            if role_name_list[i].text == 'UI自动化已编辑':
                # 点击对应行的"删除"
                time.sleep(2)
                edit_button_list[i - 1].click()
                time.sleep(2)

                # 输入角色名称
                self.type(ProviderBackgroundSubAccountManagement.role_name_input, 'UI自动化未编辑')

                # 点击"确定"
                self.click(ProviderBackgroundSubAccountManagement.add_role_popup_confirm_button)

                time.sleep(5)

                # 获取角色列表的角色名称列
                role_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.role_name_list)

                for j in range(len(role_name_list)):
                    if role_name_list[j].text == 'UI自动化未编辑':
                        break
                    assert j != len(role_name_list) - 1, "编辑角色失败"
                break
            elif role_name_list[i].text == 'UI自动化未编辑':
                # 点击对应行的"删除"
                time.sleep(2)
                edit_button_list[i - 1].click()
                time.sleep(2)

                # 输入角色名称
                self.type(ProviderBackgroundSubAccountManagement.role_name_input, 'UI自动化已编辑')

                # 点击"确定"
                self.click(ProviderBackgroundSubAccountManagement.add_role_popup_confirm_button)

                time.sleep(5)

                # 获取角色列表的角色名称列
                role_name_list = self.find_elements(ProviderBackgroundSubAccountManagement.role_name_list)

                for j in range(len(role_name_list)):
                    if role_name_list[j].text == 'UI自动化已编辑':
                        break
                    assert j != len(role_name_list) - 1, "编辑角色失败"
                break
