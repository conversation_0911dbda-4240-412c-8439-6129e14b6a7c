import time

import pytest
from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain
from page_objects.service_market.login_page import LoginPage

# 获取账号信息
provider_account_data = get_account_info("service_market_provider")
merchant_account_data = get_account_info("service_market_merchant")
provider_child_account_data = get_account_info("service_market_provider_child")
merchant_child_account_data = get_account_info("service_market_merchant_child")
host = get_domain("SERVICE_MARKET_LOGIN_DOMAIN")


class TestLogin(BaseCase):
    """服务市场登录"""

    def login_page(self):
        # 主账号登录

        self.open(host)

        # 点击"我是店主或服务商"
        self.click(LoginPage.login_button)

        # 点击"手机号登录"
        self.click(LoginPage.phone_num_login_tab)

    def subAccount_login_page(self):
        # 子账号登录

        self.open(host)

        # 点击"我是员工"
        self.click(LoginPage.subAccount_login_button)

        # 点击"账号登录"
        self.click(LoginPage.account_login_tab)

    def test_provider_login_success(self):
        # 服务商主账号密码登录

        self.login_page()

        # 输入手机号
        self.type(LoginPage.phone_input, provider_account_data['account'])

        # 输入密码
        self.type(LoginPage.password_input, provider_account_data['password'])
        time.sleep(2)

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(3)

        current_url = self.get_current_url()
        assert "fuwu.kwaixiaodian.com" in current_url

    def test_merchant_login_success(self):
        # 商家主账号密码登录

        self.login_page()

        # 输入手机号
        self.type(LoginPage.phone_input, merchant_account_data['account'])

        # 输入密码
        self.type(LoginPage.password_input, merchant_account_data['password'])
        time.sleep(1)

        # 点击"登录"
        self.click(LoginPage.login_button_2)

        current_url = self.get_current_url()
        assert "fuwu.kwaixiaodian.com" in current_url

    def test_provider_child_login_success(self):
        # 服务商子账号密码登录

        self.subAccount_login_page()

        # 输入手机号
        self.type(LoginPage.phone_input, provider_child_account_data['account'])

        # 输入密码
        self.type(LoginPage.password_input, provider_child_account_data['password'])
        time.sleep(1)

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(2)

        # 选择服务商子账号
        self.click(provider_child_account_data['default_log_select_page'])
        time.sleep(3)

        current_url = self.get_current_url()
        assert "fuwu.kwaixiaodian.com" in current_url

    def test_merchant_child_login_success(self):
        # 商家子账号密码登录

        self.subAccount_login_page()

        # 输入手机号
        self.type(LoginPage.phone_input, merchant_child_account_data['account'])

        # 输入密码
        self.type(LoginPage.password_input, merchant_child_account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(2)

        # 选择商家子账号
        self.click(merchant_child_account_data['default_log_select_page'])
        time.sleep(2)

        current_url = self.get_current_url()
        assert "fuwu.kwaixiaodian.com" in current_url
