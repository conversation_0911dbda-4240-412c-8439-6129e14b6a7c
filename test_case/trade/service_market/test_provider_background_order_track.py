import time
from unittest import skip

import requests

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_home import ProviderBackgroundHome
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_order_track import ProviderBackgroundOrderTrack
from page_objects.service_market.service_detail import ServiceDetail
from page_objects.service_market.submit_order import SubmitOrder
from .base import BaseTestCase


class TestProviderBackgroundOrderTrack(BaseTestCase):
    """服务商后台-订单查询页面"""

    def pay_order(self, order_id):
        # 支付订单

        url = 'https://datafactory.staging.kuaishou.com/v1/trade/payOrder'

        data = {
            'oid': order_id
        }

        headers = {
            'Content-Type': 'application/json'
        }

        cookie = {
            'cooike': 'apdid=********-5525-4adf-896a-41f346d46426db39115e111d27e43adf056e180ca82b:**********:1; hdige2wqwoino=fzrfzzyaZTn5dP5NZmaTrnaB2YQs7Sywdbe9a564; did=web_72452603147d05f9d457f24d228e7006; soft_did=*************; sigUserName=2d00b133cf8418ba207179ea26bcde55; _did=web_896914919F65692; didv=*************; sid=kuaishou.shop.b; kuaishou.shop.b_ph=fa6ab6a9ac68134e5e314f98a9cd0a154ae1; account_id=544; userId=**********; kuaishou.plateco.service.market.admin_st=CihrdWFpc2hvdS5wbGF0ZWNvLnNlcnZpY2UubWFya2V0LmFkbWluLnN0ErABcfsISwFPZLev8nsCxIQTIQZlxG4FtyfabJgSbYvHLSaiiG93yiAUopOLT1L-as3_hy-S8KIkHP7xpAG7QvO3Nzkixk9xQKRXykNx09hpMGmz_1waVhcrCv9qmloiMelrAvQWWgF1Izt_D_exi1ecPUHQy6YJRbRF9iO4byxuPY6Pn4HZnaNVPcBkct9Gdp4ZTezM98q4CoBvpo5XZsan7_od9-u_UyFrSQilVYwXveoaEvAOZ0_ui4vMlG4CBUkzQhu-ESIgV8s5z5n9bCngxbzTSHCZgKMjLBZAKLT8MSzVWjRP_GYoDzAB; kuaishou.plateco.service.market.admin_ph=4ba5897466bf8bb917c84bb7da89e3fe37a5; pa-gateway-token=bb1f449b-bf00-4f05-ab9f-9815fbe579b7; username=weijingxiao; userName=weijingxiao; accessproxy_session=0a1be089-9ac8-4732-aede-2a097c053b43'
        }

        requests.request(url=url, method="POST", json=data, cookies=cookie, headers=headers)

    @skip("测试账号加白es，不同步订单信息暂时skip")
    def test_order_modify_price(self):
        # 订单改价

        # 商家登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

        service_url = 'https://fuwu.kwaixiaodian.com/detail?id=15494358715103'

        # 进入服务详情页
        self.open(service_url)
        time.sleep(2)
        self.assert_text("回测服务类次数套餐服务时间", ServiceDetail.service_name)

        # 点击数字选择器的+号
        self.click(ServiceDetail.num_service_add_num_button)

        # 点击"立即购买"
        self.click(ServiceDetail.submit_order_button)
        self.assert_text("111", SubmitOrder.service_package_name)
        self.assert_text("0.02", SubmitOrder.service_price)

        # 点击提交订单
        self.click(SubmitOrder.submit_order_button)
        self.assert_text("支付", SubmitOrder.pc_cash_register_title)

        # 获取订单号
        order_id = self.get_text(SubmitOrder.pc_cash_register_order_id)

        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(2)

        # 输入订单号
        self.type(ProviderBackgroundOrderTrack.order_id_input, order_id)

        # 点击查询
        self.click(ProviderBackgroundOrderTrack.track_button)
        time.sleep(2)
        order_id_list = self.find_elements(ProviderBackgroundOrderTrack.order_id_list)
        assert len(order_id_list) == 1, "页面资源未加载成功或没有订单数据"
        assert "订单编号：" + order_id == order_id_list[0].text, "订单查询错误"

        # 点击改价
        self.click(ProviderBackgroundOrderTrack.change_order_price_button)

        # 输入修改后的价格
        self.type(ProviderBackgroundOrderTrack.change_order_price_input, 1)

        # 点击确定
        self.click(ProviderBackgroundOrderTrack.modify_price_popup_confirm_button)
        self.assert_text("改价成功", ProviderBackgroundOrderTrack.modify_price_success_popup_message)
        time.sleep(1)

        # 点击确定
        self.click(ProviderBackgroundOrderTrack.modify_price_success_popup_confirm_button)
        self.assert_text("1.00", ProviderBackgroundOrderTrack.order_payment)

    # @skip
    # def test_order_refund(self):
    #     # 订单退款
    #
    #     # 商家登录
    #     self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")
    #
    #     service_url = 'https://eshop-fuwu.staging.kuaishou.com/detail?id=20000662360610'
    #     # 进入服务详情页
    #     self.open(service_url)
    #     self.assert_text("UI自动化-次数类型服务-勿动", ServiceDetail.service_name)
    #
    #     # 点击数字选择器的+号
    #     self.click(ServiceDetail.num_service_add_num_button)
    #
    #     # 点击"提交订单"
    #     self.click(ServiceDetail.submit_order_button)
    #     self.assert_title(" 购买服务-快手服务市场-快手电商官网 ")
    #     self.assert_text("111", SubmitOrder.service_package_name)
    #     self.assert_text("0.02", SubmitOrder.service_price)
    #
    #     # 点击协议勾选框
    #     self.click(SubmitOrder.check_box, delay=0.5)
    #
    #     # 点击提交订单
    #     self.click(SubmitOrder.submit_order_button, delay=1)
    #     self.assert_text("支付", SubmitOrder.pc_cash_register_title)
    #
    #     # 获取订单号
    #     order_id = self.get_text(SubmitOrder.pc_cash_register_order_id)
    #
    #     # 订单支付
    #     self.pay_order(order_id)
    #
    #     # 服务商登录
    #     self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")
    #     self.assert_title("首页-快手服务市场-快手电商官网")
    #     self.assert_text("我的服务台", HomePage.my_service_desk)
    #
    #     # 点击"我的服务台"
    #     self.click(HomePage.my_service_desk)
    #     self.assert_title("首页-快手服务市场-快手电商官网")
    #     self.assert_text("首页", ProviderBackgroundHome.home_title)
    #
    #     # 点击"订单查询"
    #     self.click(ProviderBackgroundLeftTab.order_tracking_tab)
    #     self.assert_title(" 订单查询-快手服务市场-快手电商官网 ")
    #     self.assert_text("订单查询", ProviderBackgroundOrderTrack.order_track_title)
    #
    #     # 输入订单号
    #     self.type("input[placeholder='请输入订单编号']", order_id)
    #
    #     # 点击查询
    #     self.click(ProviderBackgroundOrderTrack.track_button)
    #     self.assert_text("订单编号：" + str(order_id), ProviderBackgroundOrderTrack.order_list_order_id)
    #
    #     # 点击申请售后
    #     self.click(ProviderBackgroundOrderTrack.order_list_right_button, delay=0.5)
    #     self.assert_text("申请售后", ProviderBackgroundOrderTrack.popup_title)
    #
    #     # 点击售后原因下拉框
    #     self.click(ProviderBackgroundOrderTrack.apply_refund_popup_refund_reason_down_box)
    #
    #     # 点击第一个原因
    #     self.click(ProviderBackgroundOrderTrack.apply_refund_popup_refund_reason_down_boxopup_first_reason)
    #
    #     # 输入售后说明
    #     self.type(ProviderBackgroundOrderTrack.apply_refund_popup_refund_instruction_input, "123")
    #
    #     # 点击确定
    #     self.click(ProviderBackgroundOrderTrack.apply_refund_popup_confirm_button, delay=2)
    #
    #     # 点击查询
    #     self.click(ProviderBackgroundOrderTrack.track_button)
    #     self.assert_text("退款到账中", ProviderBackgroundOrderTrack.order_list_order_status)

    def test_select_by_order_id(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(5)

        # 获取订单列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundOrderTrack.order_id_list)
        assert len(order_id_list) > 0, "页面资源未加载成功或没有订单数据"

        # 获取第一行的订单号
        first_order_id = order_id_list[0].text.split("：")[1]

        # 输入订单号
        self.type(ProviderBackgroundOrderTrack.order_id_input, first_order_id)

        # 点击查询
        self.click(ProviderBackgroundOrderTrack.track_button)
        time.sleep(3)

        # 获取订单列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundOrderTrack.order_id_list)
        assert len(order_id_list) == 1, "页面资源未加载成功或没有订单数据"
        assert "订单编号：" + first_order_id == order_id_list[0].text, "订单查询错误"

    def test_remark_button(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(2)

        # 输入订单号
        self.type(ProviderBackgroundOrderTrack.order_id_input, "123")

        # 点击查询
        self.click(ProviderBackgroundOrderTrack.track_button)
        time.sleep(3)

        # 获取订单列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundOrderTrack.order_id_list)
        assert len(order_id_list) == 0, "订单查询错误"

        # 点击重置按钮
        self.click(ProviderBackgroundOrderTrack.remark_button)
        time.sleep(2)

        # 获取订单列表的订单编号列
        order_id_list = self.find_elements(ProviderBackgroundOrderTrack.order_id_list)
        assert len(order_id_list) > 0, "页面资源未加载成功或没有订单数据"

    def test_export_button(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(2)

        # 点击导出按钮
        self.click(ProviderBackgroundOrderTrack.export_button)
        self.assert_text("申请导出成功", ProviderBackgroundOrderTrack.export_success_message)

    def test_export_record_button(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(2)

        # 点击导出按钮
        self.click(ProviderBackgroundOrderTrack.export_record_button)
        time.sleep(2)
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/exportRecord?exportType=4'

        download_button_list = self.find_elements(ProviderBackgroundOrderTrack.download_button)
        assert len(download_button_list) > 0, "获取订单导出记录失败"

    # @skip("暂时skip")
    def test_change_order_status_tab(self):
        # 服务商登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"订单查询"
        self.click(ProviderBackgroundLeftTab.order_tracking_tab)
        time.sleep(2)

        # 点击待支付tab
        self.click(ProviderBackgroundOrderTrack.wait_pay_tab)
        time.sleep(2)

        # 获取订单列表的订单状态列
        order_status_list = self.find_elements(ProviderBackgroundOrderTrack.order_status_list)

        for i in range(len(order_status_list)):
            assert order_status_list[i].text == '待支付', "订单状态错误"

        # 点击支付成功tab
        self.click(ProviderBackgroundOrderTrack.pay_success_tab)
        time.sleep(2)

        # 获取订单列表的订单状态列
        order_status_list = self.find_elements(ProviderBackgroundOrderTrack.order_status_list)

        for i in range(len(order_status_list)):
            assert order_status_list[i].text == '支付成功', "订单状态错误"

        # 点击已售后tab
        self.click(ProviderBackgroundOrderTrack.refund_tab)
        time.sleep(2)

        # 获取订单列表的订单状态列
        order_status_list = self.find_elements(ProviderBackgroundOrderTrack.order_status_list)

        for i in range(len(order_status_list)):
            assert order_status_list[i].text == '退款成功', "订单状态错误"

        # 点击已关闭tab
        self.click(ProviderBackgroundOrderTrack.closed_tab)
        time.sleep(2)

        # 获取订单列表的订单状态列
        order_status_list = self.find_elements(ProviderBackgroundOrderTrack.order_status_list)

        for i in range(len(order_status_list)):
            assert order_status_list[i].text == '已关闭', "订单状态错误"
    # 慢sql，年后优化
    # def test_select_by_merchant_name(self):
    #     # 服务商登录
    #     self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")
    #
    #     # 点击"订单查询"
    #     self.click(ProviderBackgroundLeftTab.order_tracking_tab)
    #     time.sleep(2)
    #
    #     # 获取订单列表的商家名称列
    #     merchant_name_list = self.find_elements(ProviderBackgroundOrderTrack.merchant_name_list)
    #     assert len(merchant_name_list) > 0, "页面资源未加载成功或没有订单数据"
    #
    #     # 获取第一行的订单的商家名称
    #     first_merchant_name = merchant_name_list[0].text
    #
    #     # 输入商家名称
    #     self.type(ProviderBackgroundOrderTrack.merchant_name_input, first_merchant_name)
    #
    #     # 点击查询
    #     self.click(ProviderBackgroundOrderTrack.track_button)
    #     time.sleep(3)
    #
    #     # 获取订单列表的商家名称列
    #     merchant_name_list = self.find_elements(ProviderBackgroundOrderTrack.merchant_name_list)
    #     assert len(merchant_name_list) > 0, "页面资源未加载成功或没有订单数据"
    #     for i in range(len(merchant_name_list)):
    #         assert first_merchant_name == merchant_name_list[i].text, "订单查询错误"

    # def test_select_by_service_name(self):
    #     # 服务商登录
    #     self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")
    #
    #     # 点击"订单查询"
    #     self.click(ProviderBackgroundLeftTab.order_tracking_tab)
    #     time.sleep(5)
    #
    #     # 获取订单列表的服务名称列
    #     service_name_list = self.find_elements(ProviderBackgroundOrderTrack.service_name_list)
    #     assert len(service_name_list) > 0, "页面资源未加载成功或没有订单数据"
    #
    #     # 获取第一行的订单的服务名称
    #     first_service_name = service_name_list[0].text
    #
    #     # 输入服务名称
    #     self.type(ProviderBackgroundOrderTrack.service_name_input, first_service_name)
    #
    #     # 点击查询
    #     self.click(ProviderBackgroundOrderTrack.track_button)
    #     time.sleep(3)
    #
    #     # 获取订单列表的服务名称列
    #     service_name_list = self.find_elements(ProviderBackgroundOrderTrack.service_name_list)
    #     assert len(service_name_list) > 0, "页面资源未加载成功或没有订单数据"
    #     for i in range(len(service_name_list)):
    #         assert first_service_name == service_name_list[i].text, "订单查询错误"
