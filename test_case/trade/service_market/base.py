#!/usr/bin/env python
# -*-coding:utf-8 -*-

import json
import os
import time

import requests
from selenium.webdriver.common.action_chains import ActionChains
import cv2
import numpy as np
from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain
from page_objects.service_market.home_page import HomePage
from page_objects.service_market.login_page import LoginPage


def find_white_background(imgpath, threshold):
    imgArr = cv2.imread(imgpath)
    background = np.array([255, 255, 255])
    percent = (imgArr == background).sum() / imgArr.size
    print("当前页面白屏百分比为:%.2f%%" % (percent * 100))
    if percent >= threshold:
        return True
    else:
        return False


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        # 主账号登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        self.open(host)

        self.driver.maximize_window()

        # 点击"我是店主或服务商"
        self.click(LoginPage.login_button)

        # 点击"手机号登录"
        self.click(LoginPage.phone_num_login_tab)

        # 输入手机号
        self.type(LoginPage.phone_input, account_data['account'])

        # 输入密码
        self.type(LoginPage.password_input, account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(3)

        current_url = self.get_current_url()

        if "login.kwaixiaodian.com" not in current_url:
            pass
        else:
            # 判断是否需选择账号
            self.click(LoginPage.choochoose_account)
            self.click(LoginPage.login_button_3)
            time.sleep(3)



        current_url = self.get_current_url()
        assert "fuwu.kwaixiaodian.com" in current_url

        for _ in range(3):
            if self.is_element_visible("button > span > span > svg > path"):
                self.click("button > span > span > svg > path")
                time.sleep(2)
                break
        # todo: yutong03 验证新版
        # 返回旧版
        self.back_to_old()
    def login_2(self, domain, account):
        # 主账号登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        self.open(host)

        self.driver.maximize_window()

        # 点击"我是店主或服务商"
        self.click(LoginPage.login_button)

        # 点击"手机号登录"
        self.click(LoginPage.phone_num_login_tab)

        # 输入手机号
        self.type(LoginPage.phone_input, account_data['account'])

        # 输入密码
        self.type(LoginPage.password_input, account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(1)

        # 点击"确定"
        self.click(LoginPage.confirm_button)
        time.sleep(3)

        current_url = self.get_current_url()
        assert "fuwu.kwaixiaodian.com" in current_url

        for _ in range(3):
            if self.is_element_visible("button > span > span > svg > path"):
                self.click("button > span > span > svg > path")
                time.sleep(2)
                break
        # todo: yutong03 验证新版
        # 返回旧版
        self.back_to_old()

    # def child_login(self, domain, account):
    #     # 子账号登录
    #
    #     account_data = get_account_info(account)
    #
    #     host = get_domain(domain)
    #
    #     self.open(host)
    #
    #     self.driver.maximize_window()
    #
    #     # 点击"我是员工"
    #     self.click(LoginPage.subAccount_login_button)
    #
    #     # 点击"账号登录"
    #     self.click(LoginPage.account_login_tab)
    #
    #     # 输入手机号
    #     self.type(LoginPage.phone_input, account_data['account'])
    #
    #     # 输入密码
    #     self.type(LoginPage.password_input, account_data['password'])
    #
    #     # 点击"登录"
    #     self.click(LoginPage.login_button_2)
    #
    #     # 选择子账号
    #     self.click(account_data['default_log_select_page'])
    #     time.sleep(2)
    #
    #     current_url = self.get_current_url()
    #     assert "fuwu.kwaixiaodian.com" in current_url
    #
    #     for _ in range(3):
    #         if self.is_element_visible("button > span > span > svg > path"):
    #             self.click("button > span > span > svg > path")
    #             time.sleep(2)
    #             break

    def open_platform_login(self, domain, account):
        # 开放平台登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        self.open(host)

        self.driver.maximize_window()

        self.click(LoginPage.open_platform_phone_num_login_tab)

        # 输入手机号
        self.type("input[placeholder='请输入手机号']", account_data['account'])

        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(5)
        open_platform_select_account_email_list = self.find_elements(LoginPage.open_platform_select_account_email_list)

        for i in range(len(open_platform_select_account_email_list)):
            if open_platform_select_account_email_list[i].text == '<EMAIL>':
                open_platform_select_account_email_list[i].click()
                time.sleep(2)

                self.click(LoginPage.open_platform_select_account_confirm_button)
                time.sleep(2)
                break
            assert i < len(open_platform_select_account_email_list), "开放平台测试登陆账号不存在"

    def save_cur_page(self):
        filename = time.strftime("%Y%m%d-%H%M%S") + "error_page.png"
        file_path = './test_data/img/' + filename
        self.save_screenshot(file_path)

        return file_path

    def check_white_background(self, img_path, url, threshold):
        check_result = find_white_background(str(img_path), threshold)
        assert check_result is not True, url + ",当前页面存在白屏"
        os.remove(img_path)

    def check_white_page(self, url, white_page_max_time):
        navigation_start = self.execute_script("return window.performance.timing.navigationStart")
        response_start = self.execute_script("return window.performance.timing.responseStart")

        blank_time = response_start - navigation_start
        print("当前页面加载时间为:%.3f秒" % (blank_time / 1000))
        assert blank_time < white_page_max_time * 1000, url + ",当前页面加载时间超过设定的时间"

    def check_page_error(self, url, white_page_max_time=1, threshold=0.9):
        self.open(url)

        self.wait_for_ready_state_complete()

        self.check_white_page(url, white_page_max_time)

        # 访问页面状态是否404
        self.assert_link_status_code_is_not_404(url)

        # # 验证没有js错误
        # self.assert_no_js_errors()

        # 验证没有断开的链接
        self.assert_no_404_errors(url)

        file_path = self.save_cur_page()

        # 检测页面是否白屏
        self.check_white_background(file_path, url, threshold)

    def assert_interface_response_status_is_200(self, ignore=[]):
        resources = self.driver.execute_script("return window.performance.getEntriesByType('resource')")
        for resource in resources:
            if resource['initiatorType'] == 'xmlhttprequest' and resource['name'] not in ignore:
                # request_status_code = requests.get(resource['name'])
                # print(resource['name'])
                # print(request_status_code.text)
                assert resource['responseStatus'] == 200, resource['name'] + '的http状态码为' + \
                                                          str(resource['responseStatus']) + ',不等于200'

    def ocr_image(self, user_id, img_path):
        """
        ocr识别图片
        :param user_id: 高达平台的user id
        :param img_path: ocr识别图片的路径
        :return:[[关键字,横坐标,纵坐标]...]
        """
        object_coordinate_list = []
        # 截图
        self.driver.get_screenshot_as_file(img_path)

        url = "https://video-flask.corp.kuaishou.com/gtrm/cap/ocr/detect"
        payload = {'userId': user_id,
                   'resultType': '1'}
        img_name = img_path.split('/')[1]
        files = [
            ('file', (img_name, open(img_path, 'rb'), 'image/png'))
        ]
        response = json.loads(requests.request("POST", url, data=payload, files=files).text)['data']['detail']

        for i in range(len(response)):
            x = int((response[i]['bounds']['xmin'] + response[i]['bounds']['xmax']) / 2)
            y = int((response[i]['bounds']['ymin'] + response[i]['bounds']['ymax']) / 2)
            object_coordinate_list.append([response[i]['words'], x, y])
        return object_coordinate_list

    def click_by_coordinate(self, x, y, click_method='left'):
        """
        根据屏幕坐标进行点击
        :param x: 屏幕上的横坐标
        :param y: 屏幕上的纵坐标
        :param click_method: 点击方式，'left'：鼠标左击；'right':鼠标右击
        :return:
        """
        if click_method == 'left':
            ActionChains(self.driver).move_by_offset(x, y).click().perform()
        elif click_method == 'right':
            ActionChains(self.driver).move_by_offset(x, y).context_click().perform()
        else:
            raise

    def provider_login(self, domain, account):
        # 服务商登录

        self.login(domain, account)

        # 点击"我的服务台"
        self.click(HomePage.my_service_desk)
        time.sleep(5)

        if self.is_element_visible("button > span > span > svg > path"):
            self.click("button > span > span > svg > path")

    def provider_login_2(self, domain, account):
        # 服务商登录

        self.login_2(domain, account)

        # 点击"我的服务台"
        self.click(HomePage.my_service_desk)
        time.sleep(5)

        for _ in range(3):
            if self.is_element_visible("button > span > span > svg > path"):
                self.click("button > span > span > svg > path")
                time.sleep(2)
                break

    def back_to_old(self):
        if self.find_element("(//div[@class='backOldVersion___jM5Ab'])[1]"):
            # 新版页面
            self.click("(//div[@class='backOldVersion___jM5Ab'])[1]")
            self.click("(//div[contains(text(),'页面样式不喜欢')])[1]")
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
            time.sleep(2)
        else:
            # 旧版页面
            pass



