import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_provider_homepage import ProviderbackgroundProviderHomepage
from .base import BaseTestCase


class TestProviderBackgroundProviderHomepage(BaseTestCase):
    """服务商后台-服务商主页页面"""

    def test_look_my_homepage_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"服务商主页"
        self.click(ProviderBackgroundLeftTab.provider_homepage_tab)
        time.sleep(2)

        # 点击查看我的主页
        self.click(ProviderbackgroundProviderHomepage.look_my_homepage_button)
        time.sleep(2)

        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/new/provider/homepage?id=**********', "跳转链接错误"
