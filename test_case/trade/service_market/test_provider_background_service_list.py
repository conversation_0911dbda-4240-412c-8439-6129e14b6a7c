import os
import time

import pytest

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.management_left_tab import Management<PERSON>eftTab
from page_objects.service_market.management_login import ManagementLogin
from page_objects.service_market.managment_service_review import ManagementServiceReview
from page_objects.service_market.provider_background_home import ProviderBackgroundHome
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_service_list import ProviderBackgroundServiceList
from .base import BaseTestCase


# !/usr/bin/env/ python
# coding:utf-8


class TestProviderBackgroundServiceList(BaseTestCase):
    """服务商后台-服务列表页面"""
    @pytest.mark.skip("创建服务接口报错超时，新需求暂时skip")
    def test_add_and_delete_service(self):
        # 新增/删除服务

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(5)

        # 获取服务列表的服务名字列
        service_name_list = self.find_elements(ProviderBackgroundServiceList.service_name_list)

        # 获取服务列表的服务状态列
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)

        # 获取服务列表的操作第一列button
        operate_button_list = self.find_elements(ProviderBackgroundServiceList.operate_button_first_list)

        assert len(service_name_list) > 0, "页面资源未加载成功或创建服务失败"
        for i in range(len(service_name_list)):
            # 如果"UI自动化"同名服务存在
            if service_name_list[i].text == 'UI自动化autotest':
                assert service_status_list[i].text == '待审核', '服务状态错误'
                assert operate_button_list[i].text == '删除', '服务操作按钮错误'
                # 点击对应行的"删除"
                operate_button_list[i].click()
                # 点击确认
                self.click(ProviderBackgroundServiceList.delete_popup_confirm_button)

                self.refresh()
                time.sleep(3)

        # 点击"新增服务"
        self.click(ProviderBackgroundServiceList.add_service_button)
        time.sleep(2)

        # 点击服务分类下拉框
        self.click(ProviderBackgroundServiceList.service_category_down_box)

        # 点击店铺服务
        self.click(ProviderBackgroundServiceList.service_category_shop_service)

        # 点击内容运营
        self.click(ProviderBackgroundServiceList.service_category_shop_service_content_operation)

        # 上传服务主图
        curPath = os.path.abspath(os.path.dirname(__file__))
        img_path = curPath + '/service_market_image.jpg'
        # path = os.path.abspath("..")
        # img_path = path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg'
        self.choose_file(ProviderBackgroundServiceList.service_main_image_upload_image_box, img_path)
        time.sleep(1)

        # 输入服务名称
        self.type(ProviderBackgroundServiceList.service_name_input, "UI自动化autotest")

        # 输入服务描述
        self.type(ProviderBackgroundServiceList.service_description_input, "UI自动化-1")

        # 上传服务详情图片
        self.choose_file(ProviderBackgroundServiceList.service_detail_image_upload_image_box, img_path)
        time.sleep(1)

        # 输入使用教程
        self.type(ProviderBackgroundServiceList.tutorial_input, "UI自动化-1")
        time.sleep(1)

        # 点击不公开服务
        self.click(ProviderBackgroundServiceList.hidden_service_button, delay=1)

        # 点击新增服务套餐
        self.click(ProviderBackgroundServiceList.add_service_package_button)

        # 输入套餐名称
        self.type(ProviderBackgroundServiceList.package_name_input, "UI自动化")

        # 点击套餐有效期下拉框
        self.click(ProviderBackgroundServiceList.package_time_down_box)

        # 点击一个月
        self.click(ProviderBackgroundServiceList.package_time_one_month)

        # 输入套餐价格
        self.type(ProviderBackgroundServiceList.package_price_input, 0.01)

        # 输入客服电话
        self.type(ProviderBackgroundServiceList.customer_service_phone_input, 1)

        # 输入客服邮箱
        self.type(ProviderBackgroundServiceList.customer_service_email_input, 1)

        # 点击提交审核
        self.click(ProviderBackgroundServiceList.submit_review_button)
        time.sleep(5)

        # 刷新页面
        self.refresh()
        time.sleep(2)

        # 获取服务列表的服务名字列
        service_name_list = self.find_elements(ProviderBackgroundServiceList.service_name_list)

        # 获取服务列表的服务状态列
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)

        # 获取服务列表的操作第一列button
        operate_button_list = self.find_elements(ProviderBackgroundServiceList.operate_button_first_list)

        assert len(service_name_list) > 0, "页面资源未加载成功或创建服务失败"
        for i in range(len(service_name_list)):
            # 如果"UI自动化"同名服务存在
            if service_name_list[i].text == 'UI自动化autotest':
                assert service_status_list[i].text == '待审核', '服务状态错误'
                assert operate_button_list[i].text == '删除', '服务操作按钮错误'
                # 点击对应行的"删除"
                operate_button_list[i].click()
                # 点击确认
                self.click(ProviderBackgroundServiceList.delete_popup_confirm_button)
            assert i < len(service_name_list), "待删除服务不存在"

    def test_edit_service(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(5)

        # 获取服务列表的服务名字列
        service_name_list = self.find_elements(ProviderBackgroundServiceList.service_name_list)

        # 获取服务列表的服务状态列
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)

        # 获取服务列表的操作第一列button
        operate_button_list = self.find_elements(ProviderBackgroundServiceList.operate_button_second_list)

        assert len(service_name_list) > 0, "页面资源未加载成功或创建服务失败"
        for i in range(len(service_name_list)):
            # 如果"UI自动化-编辑服务-勿动"同名服务存在
            if service_name_list[i].text == 'UI自动化-编辑服务-勿动':
                if service_status_list[i].text == '已上架':
                    # assert service_status_list[i].text == '已上架', '服务状态错误'
                    if operate_button_list[i].text == '编辑':
                        # assert operate_button_list[i].text == '编辑', '服务操作按钮错误'
                        # 点击对应行的"编辑"
                        operate_button_list[i].click()
                        time.sleep(3)

                        self.click(ProviderBackgroundServiceList.submit_review_button)
                        time.sleep(10)

                # 打开服务市场运营后台
                self.open("https://fuwu-management-prt-preonline.corp.kuaishou.com/")

                # 刷新页面
                self.refresh()

                # 点击进入后台
                self.click(ManagementLogin.enter_background_button)

                # 点击服务管理tab
                self.click(ManagementLeftTab.service_management_tab)

                # 点击服务审核
                self.click(ManagementLeftTab.service_review_tab)

                for j in range(3):
                    # 输入服务商ID
                    self.type("input[placeholder='请输入服务ID']", 20679475704184)

                    # 点击搜索
                    self.click(ManagementServiceReview.search_button)
                    time.sleep(3)

                    # 获取服务列表的服务状态列
                    management_service_status_list = self.find_elements(ManagementServiceReview.service_status_list)

                    # 获取服务列表的操作第三列button
                    management_operate_button_third_list = self.find_elements(
                        ManagementServiceReview.operate_button_third_list)

                    if management_service_status_list[0].text == '等待审批':
                        # 点击对应行的"通过"
                        management_operate_button_third_list[0].click()

                        self.click(ManagementServiceReview.service_review_allow_popup_confirm_button)
                        time.sleep(5)

                        self.refresh()
                        time.sleep(3)
                    elif management_service_status_list[0].text == '审批通过':
                        break
                break

    def test_change_order_status_tab(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(2)

        # 点击已上架
        self.click(ProviderBackgroundServiceList.online_tab)
        time.sleep(2)
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)
        for i in range(len(service_status_list)):
            assert service_status_list[i].text == '已上架', "服务状态错误"

        # 点击待上架
        self.click(ProviderBackgroundServiceList.wait_online_tab)
        time.sleep(2)
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)
        for i in range(len(service_status_list)):
            assert service_status_list[i].text == '待上架', "服务状态错误"

        # 点击待审核
        self.click(ProviderBackgroundServiceList.wait_review_tab)
        time.sleep(2)
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)
        for i in range(len(service_status_list)):
            assert service_status_list[i].text == '待审核', "服务状态错误"

        # 点击审核待修改
        self.click(ProviderBackgroundServiceList.wait_edit_tab)
        time.sleep(2)
        service_status_list = self.find_elements(ProviderBackgroundServiceList.service_status_list)
        for i in range(len(service_status_list)):
            assert service_status_list[i].text == '审核待修改', "服务状态错误"

    def test_generate_qr_code_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(2)

        # 点击已上架
        self.click(ProviderBackgroundServiceList.online_tab)
        time.sleep(2)

        # 获取生成二维码按钮列
        generate_qr_code_button_list = self.find_elements(ProviderBackgroundServiceList.generate_qr_code_button_list)
        assert len(generate_qr_code_button_list) > 0, "获取服务列表失败"

        # 点击第一行的生成二维码按钮
        generate_qr_code_button_list[0].click()
        time.sleep(2)

        # 点击生成二维码
        self.click(ProviderBackgroundServiceList.generate_qr_code_popup_button)

        self.assert_element_visible(ProviderBackgroundServiceList.save_qr_code_button)

    def test_tag_management_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(2)

        # 点击已上架
        self.click(ProviderBackgroundServiceList.online_tab)
        time.sleep(2)

        # 获取标签管理按钮列
        tag_management_button_list = self.find_elements(ProviderBackgroundServiceList.tag_management_button_list)
        assert len(tag_management_button_list) > 0, "获取服务列表失败"

        # 点击第一行的标签管理按钮
        tag_management_button_list[0].click()

        # 点击确定提交
        self.click(ProviderBackgroundServiceList.tag_management_popup_confirm_button)

    def test_detail_button_list(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"服务列表"
        self.click(ProviderBackgroundLeftTab.service_list_tab)
        time.sleep(2)

        # 点击已上架
        self.click(ProviderBackgroundServiceList.online_tab)
        time.sleep(2)

        # 获取服务id列
        service_id_list = self.find_elements(ProviderBackgroundServiceList.service_id_list)
        first_service_id = service_id_list[0].text.split(":")[1]

        # 获取详情按钮列
        detail_button_list = self.find_elements(ProviderBackgroundServiceList.detail_button_list)
        assert len(detail_button_list) > 0, "获取服务列表失败"

        # 点击第一行的详情按钮
        if detail_button_list[0].text == '详情':
            detail_button_list[0].click()
            time.sleep(2)

            current_url = self.get_current_url()
            assert current_url == 'https://fuwu.kwaixiaodian.com/serviceDetail?serviceId=' + str(
                first_service_id), '跳转链接错误'
