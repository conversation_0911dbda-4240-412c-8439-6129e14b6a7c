import time
from unittest import skipIf, skip

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_invoice_management import ProviderBackgroundInvoiceManagement
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundInvoiceManagement(BaseTestCase):
    """服务商后台-发票管理页面"""

    def test_normal_problem(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"发票管理"
        self.click(ProviderBackgroundLeftTab.invoice_management_tab)
        time.sleep(2)

        # 点击常见问题
        self.click(ProviderBackgroundInvoiceManagement.normal_problem)
        time.sleep(5)

        current_url = self.get_current_url()
        assert current_url == 'https://docs.qingque.cn/d/home/<USER>', "跳转链接错误"

    def test_export_record_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"发票管理"
        self.click(ProviderBackgroundLeftTab.invoice_management_tab)
        time.sleep(1)

        # 点击导出记录
        self.click(ProviderBackgroundInvoiceManagement.export_record_button)
        time.sleep(2)

        self.assert_element_present(ProviderBackgroundInvoiceManagement.popup_title)

    @skip
    def test_export_detail_button(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"发票管理"
        self.click(ProviderBackgroundLeftTab.invoice_management_tab)
        time.sleep(2)

        export_detail_button_list = self.find_elements(ProviderBackgroundInvoiceManagement.export_detail_button)

        if len(export_detail_button_list) > 0:
            # 点击导出明细
            self.click(ProviderBackgroundInvoiceManagement.export_detail_button)
            self.assert_text("导出已受理，请点击“查看导出记录”后下载", ProviderBackgroundInvoiceManagement.export_success_message)

    def test_change_tab(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"发票管理"
        self.click(ProviderBackgroundLeftTab.invoice_management_tab)
        time.sleep(2)

        # 点击申请记录tab
        self.click(ProviderBackgroundInvoiceManagement.apply_record_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundInvoiceManagement.apply_time)

        # 点击发票申请tab
        self.click(ProviderBackgroundInvoiceManagement.invoice_apply_tab)
        time.sleep(1)

        self.assert_element_visible(ProviderBackgroundInvoiceManagement.invoice_subject)
