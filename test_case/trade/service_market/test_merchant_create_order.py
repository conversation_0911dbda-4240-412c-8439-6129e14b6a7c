import datetime
import time

from page_objects.service_market.merchant_background_my_order import MerchantBackgroundMyOrder
from page_objects.service_market.service_detail import ServiceDetail
from page_objects.service_market.submit_order import SubmitOrder
from .base import BaseTestCase


class TestMerchantCreateOrder(BaseTestCase):
    """商家创建订单"""

    def test_create_time_order(self):
        # 时间类型服务

        # 登录
        hour = datetime.datetime.now().hour
        if hour==11:
            self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

            time_service_url = 'https://fuwu.kwaixiaodian.com/detail?id=20539761868184'
            # 进入时间类型服务详情页
            self.open(time_service_url)
            time.sleep(2)
            self.assert_text("UI自动化-时间类型服务-勿动", ServiceDetail.service_name)

            # 点击套餐2
            self.click(ServiceDetail.time_service_second_package)

            # 点击周期2
            self.click(ServiceDetail.time_service_second_package_second_cycle)

            # 点击"立即购买"
            self.click(ServiceDetail.submit_order_button)
            time.sleep(1)
            self.assert_text("222", SubmitOrder.service_package_name)
            self.assert_text("3个月", SubmitOrder.time_service_service_cycle)
            self.assert_text("0.02", SubmitOrder.service_price)

            # 点击"立即购买"
            self.click(SubmitOrder.submit_order_button)
            time.sleep(2)
            self.assert_text("支付", SubmitOrder.pc_cash_register_title)

            # 获取订单号
            order_id = self.get_text(SubmitOrder.pc_cash_register_order_id)

            # 进入我的订单页
            self.open('https://fuwu.kwaixiaodian.com/myOrder')

            # 输入订单号
            self.type(MerchantBackgroundMyOrder.order_id_input, order_id)

            # 点击查询
            self.click(MerchantBackgroundMyOrder.order_list_select_button)
            time.sleep(1)
            self.assert_text(str(order_id), MerchantBackgroundMyOrder.order_list_order_id_list)

    def test_create_num_order(self):
        # 次数类型服务
        hour = datetime.datetime.now().hour
        if hour == 11:
            # 登录
            self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

            num_service_url = 'https://fuwu.kwaixiaodian.com/detail?id=20541067529184'
            # 进入次数类型服务详情页
            self.open(num_service_url)
            time.sleep(2)
            self.assert_text("UI自动化-次数类型服务-勿动", ServiceDetail.service_name)

            # 点击数字选择器的+号
            self.click(ServiceDetail.num_service_add_num_button)

            # 点击"提交订单"
            self.click(ServiceDetail.submit_order_button)
            time.sleep(1)
            self.assert_text("111", SubmitOrder.service_package_name)
            self.assert_text("0.02", SubmitOrder.service_price)

            # 点击"立即购买"
            self.click(SubmitOrder.submit_order_button)
            time.sleep(2)
            self.assert_text("支付", SubmitOrder.pc_cash_register_title)

            # 获取订单号
            order_id = self.get_text(SubmitOrder.pc_cash_register_order_id)

            # 进入我的订单页
            self.open('https://fuwu.kwaixiaodian.com/myOrder')

            # 输入订单号
            self.type(MerchantBackgroundMyOrder.order_id_input, order_id)

            # 点击查询
            self.click(MerchantBackgroundMyOrder.order_list_select_button)
            time.sleep(1)
            self.assert_text(str(order_id), MerchantBackgroundMyOrder.order_list_order_id_list)

    def test_create_customer_service_outsourcing_service_order(self):
        # 客服外包服务
        hour = datetime.datetime.now().hour
        if hour == 11:

            # 登录
            self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

            customer_service_outsourcing_service_url = 'https://fuwu.kwaixiaodian.com/detail?id=20541262279184'
            # 进入客服外包服务详情页
            self.open(customer_service_outsourcing_service_url)
            time.sleep(2)
            self.assert_text("UI自动化-客服外包服务-勿动", ServiceDetail.service_name)

            # 点击服务人数数字选择器的+号
            self.click(ServiceDetail.customer_service_outsourcing_service_people_add_num_button)

            # 点击服务周期数字选择器的+号
            self.click(ServiceDetail.customer_service_outsourcing_service_cycle_add_num_button)

            # 点击服务时间选择框
            self.click(ServiceDetail.customer_service_outsourcing_service_time_select_box)
            time.sleep(2)

            # 点击今天
            self.click(ServiceDetail.customer_service_outsourcing_service_time_select_box_today_button)

            # 点击"立即购买"
            self.click(ServiceDetail.submit_order_button)
            time.sleep(1)
            self.assert_text("111", SubmitOrder.service_package_name)
            self.assert_text("0.04", SubmitOrder.customer_service_outsourcing_service_price)

            # 点击"立即购买"
            self.click(SubmitOrder.submit_order_button)
            time.sleep(2)
            self.assert_text("支付", SubmitOrder.pc_cash_register_title)

            # 获取订单号
            order_id = self.get_text(SubmitOrder.pc_cash_register_order_id)

            # 进入我的订单页
            self.open('https://fuwu.kwaixiaodian.com/myOrder')

            # 输入订单号
            self.type(MerchantBackgroundMyOrder.order_id_input, order_id)

            # 点击查询
            self.click(MerchantBackgroundMyOrder.order_list_select_button)
            time.sleep(1)
            self.assert_text(str(order_id), MerchantBackgroundMyOrder.order_list_order_id_list)

    def test_create_agent_operation_service_order(self):
        # 代运营服务（按合同收费）
        hour = datetime.datetime.now().hour
        if hour == 11:

            # 登录
            self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

            agent_operation_service_url = 'https://fuwu.kwaixiaodian.com/detail?id=20666948430184'
            # 进入代运营服务详情页
            self.open(agent_operation_service_url)
            time.sleep(2)
            self.assert_text("UI自动化-代运营服务-勿动", ServiceDetail.service_name)
            self.assert_text("0元下单后，服务商会联系您签订服务合同。服务时间、服务效果、服务价格以最终合同为准。",
                             ServiceDetail.agent_operation_service_charge_by_contract_message)

            # 点击"0元下单"
            self.click(ServiceDetail.free_submit_order_button)
            time.sleep(1)
            self.assert_text("111", SubmitOrder.service_package_name)
            self.assert_text("0.00", SubmitOrder.service_price)

            # 输入详细需求
            self.type(SubmitOrder.agent_operation_service_detail_demand_input, 123)

            # 点击提交订单
            self.click(SubmitOrder.free_submit_order_button)
            self.assert_text("订单提交成功，待签署合同", SubmitOrder.agent_operation_service_sign_contract_message)

    def test_create_picture_and_video_service_order(self):
        # 图片视频服务
        hour = datetime.datetime.now().hour
        if hour == 11:
            # 登录
            self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_2")

            picture_and_video_service_url = 'https://fuwu.kwaixiaodian.com/detail?id=20667115546184'
            # 进入图片视频服务详情页
            self.open(picture_and_video_service_url)
            time.sleep(2)
            self.assert_text("UI自动化-短视频服务-勿动", ServiceDetail.service_name)
            self.assert_text("该服务商的其他视频", ServiceDetail.picture_and_video_service_other_video_title)

            # 点击"立即购买"
            self.click(ServiceDetail.submit_order_button)
            time.sleep(1)
            self.assert_text("111", SubmitOrder.service_package_name)
            self.assert_text("0.01", SubmitOrder.service_price)

            # 点击提交订单
            self.click(SubmitOrder.submit_order_button)
            time.sleep(2)
            self.assert_text("支付", SubmitOrder.pc_cash_register_title)

            # 获取订单号
            order_id = self.get_text(SubmitOrder.pc_cash_register_order_id)

            # 进入我的订单页
            self.open('https://fuwu.kwaixiaodian.com/myOrder')

            # 输入订单号
            self.type(MerchantBackgroundMyOrder.order_id_input, order_id)

            # 点击查询
            self.click(MerchantBackgroundMyOrder.order_list_select_button)
            time.sleep(1)
            self.assert_text(str(order_id), MerchantBackgroundMyOrder.order_list_order_id_list)
