import os
import time
import pytest
from page_objects.service_market.home_page import HomePage
from page_objects.service_market.management_left_tab import Management<PERSON>eftTab
from page_objects.service_market.management_login import ManagementLogin
from page_objects.service_market.management_qualifications_review import ManagementQualificationsReview
from page_objects.service_market.provider_background_home import ProviderBackgroundHome
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_my_qualifications import ProviderBackgroundMyQualifications
from .base import BaseTestCase


class TestProviderBackgroundMyQualifications(BaseTestCase):
    """服务商后台-我的资质页面"""

    @pytest.mark.skip
    def test_apply_qualifications(self):
        # 申请资质

        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"我的资质"
        self.click(ProviderBackgroundLeftTab.my_qualifications_tab)
        time.sleep(2)

        # 获取资质列表的资质名称列
        # qualifications_name_list = self.find_elements(ProviderBackgroundMyQualifications.qualifications_name_list)

        # 获取资质列表的资质类型列
        qualifications_type_list = self.find_elements(ProviderBackgroundMyQualifications.qualifications_type_list)

        # 获取资质列表的申请状态列
        qualifications_apply_status_list = self.find_elements(
            ProviderBackgroundMyQualifications.qualifications_apply_status_list)

        # 获取资质列表的操作按钮列
        qualifications_operate_button_list = self.find_elements(
            ProviderBackgroundMyQualifications.qualifications_operate_button_list)

        for i in range(len(qualifications_apply_status_list)):
            # 如果资质审核拒绝或未申请
            if (qualifications_apply_status_list[i].text == '审核拒绝' or
                qualifications_apply_status_list[i].text == '未申请') and \
                    qualifications_type_list[i].text == '服务类':
                # 点击对应行的"申请"
                qualifications_operate_button_list[i].click()

                # 输入服务简介
                self.type(ProviderBackgroundMyQualifications.apply_qualifications_popup_service_info_input, "UI自动化")

                # 输入申请理由
                self.type(ProviderBackgroundMyQualifications.apply_qualifications_popup_apply_reason_input, "UI自动化")

                time.sleep(2)
                # 上传补充资料
                curPath = os.path.abspath(os.path.dirname(__file__))
                img_path = curPath + '/service_market_image.jpg'
                self.choose_file(
                    ProviderBackgroundMyQualifications.apply_qualifications_popup_additional_materials_upload_box,
                    img_path)
                time.sleep(2)

                # 上传服务关系证明文件
                img_path = curPath + '/service_testInfo.docx'
                self.choose_file(
                    ProviderBackgroundMyQualifications.apply_qualifications_popup_additional_materials_upload_flie_box,
                    img_path)
                time.sleep(2)

                # 点击确定
                self.click(ProviderBackgroundMyQualifications.apply_qualifications_popup_confirm_button)
                self.assert_text("等待审核", ProviderBackgroundMyQualifications.qualifications_list_first_row_apply_status)

                # qualifications_name = qualifications_name_list[i].text
                break

        # 打开服务市场运营后台
        self.open("https://fuwu-management-prt-preonline.corp.kuaishou.com/")

        # 刷新页面
        self.refresh()

        # 点击进入后台
        self.click(ManagementLogin.enter_background_button)

        # 点击资质审核tab
        self.click(ManagementLeftTab.qualifications_review_tab)

        # 输入服务商ID
        self.type(ManagementQualificationsReview.provider_id_input, **********)

        # 点击搜索
        self.click(ManagementQualificationsReview.search_button)

        # 获取运营后台资质审核列表的资质名称列
        management_qualifications_name_list = self.find_elements(
            ManagementQualificationsReview.qualifications_name_list)

        # 获取运营后台资质审核列表的资质状态列
        management_qualifications_status_list = self.find_elements(
            ManagementQualificationsReview.qualifications_status_list)

        # 获取运营后台资质审核列表的拒绝button列
        management_qualifications_reject_button_list = self.find_elements(
            ManagementQualificationsReview.reject_button_list)

        for i in range(len(management_qualifications_name_list)):
            # 核对资质名称/状态是否一致
            if management_qualifications_status_list[i].text == '等待审核':
                # 点击驳回
                management_qualifications_reject_button_list[i].click()

                # 输入审核理由
                self.type(ManagementQualificationsReview.select_review_reason_popup_review_reason_input, "UI自动化")

                # 点击确定
                self.click(ManagementQualificationsReview.select_review_reason_popup_confirm_button)
                break
            assert i < len(management_qualifications_name_list), "待审核资质不存在"

    def test_change_qualifications_status_tab(self):

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"我的资质"
        self.click(ProviderBackgroundLeftTab.my_qualifications_tab)
        time.sleep(2)

        # 获取资质列表的申请状态列
        qualifications_apply_status_list = self.find_elements(
            ProviderBackgroundMyQualifications.qualifications_apply_status_list)

        for i in range(len(qualifications_apply_status_list)):
            assert qualifications_apply_status_list[i].text in ['等待审核', '未申请', '审核拒绝'], '资质状态错误'

        # 点击已申请tab
        self.click(ProviderBackgroundMyQualifications.applied_for_service_qualification_tab)
        time.sleep(2)

        qualifications_apply_status_list = self.find_elements(
            ProviderBackgroundMyQualifications.qualifications_apply_status_list)

        for i in range(len(qualifications_apply_status_list)):
            assert qualifications_apply_status_list[i].text == '审核通过', '资质状态错误'

        # 点击待申请tab
        self.click(ProviderBackgroundMyQualifications.to_be_applied_for_service_qualification_tab)
        time.sleep(2)

        qualifications_apply_status_list = self.find_elements(
            ProviderBackgroundMyQualifications.qualifications_apply_status_list)

        for i in range(len(qualifications_apply_status_list)):
            assert qualifications_apply_status_list[i].text in ['等待审核', '未申请', '审核拒绝'], '资质状态错误'

    def test_view_qualification_requirements_button(self):

        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"我的资质"
        self.click(ProviderBackgroundLeftTab.my_qualifications_tab)
        time.sleep(2)

        # 点击查看资质要求button
        self.click(ProviderBackgroundMyQualifications.view_qualification_requirements_button)
        time.sleep(1)

        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])

        current_url = self.get_current_url()
        assert current_url == 'https://open.kwaixiaodian.com/zone/new/docs/dev?pageSign=699e9501c4651d79eebd7968422c8d451614264337588', '跳转链接错误'
