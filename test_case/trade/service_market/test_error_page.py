# from ddt import ddt, data, unpack
# from constant.account import get_account_info
# from constant.domain import get_domain
# from page_objects.service_market.home_page import HomePage
# from .base import BaseTestCase
# from unittest import skip
#
# # 获取账号信息
# provider_account_data = get_account_info("service_market_provider")
# merchant_account_data = get_account_info("service_market_merchant")
# provider_child_account_data = get_account_info("service_market_provider_child")
# merchant_child_account_data = get_account_info("service_market_merchant_child")
# host = get_domain("SERVICE_MARKET_DOMAIN")
#
#
# @ddt
# @skip
# class TestErrorPage(BaseTestCase):
#     '''服务市场页面错误检测'''
#
#     @data(
#         '/buy?serviceId=**************&skuId=**************&chargeByContract=false',
#         # '/myService',
#         # '/toolService',
#         # '/csOutsourcing',
#         # '/materials',
#         # '/operationAgent',
#         # '/myOrder',
#         # '/myCoupon',
#         # '/demandSquare',
#         # '/demandList',
#         # '/demandDetail?id=3456',
#         # '/demandForm',
#         # '/newFeedback',
#         # '/myFeedback',
#         # '/messageCenter',
#         # '/messageDetail?id=**********',
#         # '/detail?id=**************',
#         # '/zone/provider/homepage?id=**********',
#         # '/list',
#         # '/couponCenter?couponId=ChpzZXJ2aWNlTWFya2V0LnVybENvdXBvbktleRIgBK8irz0fkbG2zFqfbsyg1biYtDJZd3aCmLZsT2GsE3EaEtmBNnkH2LILtGlomnKmwZqp+iIgY7gf0/UjJHcS65IY96AFQemRdDj6Ad11AvNaqdhESs0oBTAB',
#         # '/videotopic?categoryId=1889&type=creator',
#         # '/guide',
#         # '/identity'
#     )
#     def test_merchant_error_page(self, route):
#         # 登录
#         self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")
#         self.assert_title("首页-快手服务市场-快手电商官网")
#         tabs_text = self.get_text(HomePage.merchant_background_entrance_button)
#         tab_list = ["已订购", "待评价", "优惠券"]
#         for item in tab_list:
#             self.assert_in(item, tabs_text)
#
#         check_route = host + route
#         print('当前检测的url为：', check_route)
#         self.check_page_error(check_route)
#
#     # @data(
#     #     '/myService',
#     #     '/toolService',
#     #     '/csOutsourcing',
#     #     '/materials',
#     #     '/operationAgent',
#     #     '/myOrder',
#     #     '/myCoupon',
#     #     '/demandSquare',
#     #     '/demandList',
#     #     '/demandDetail?id=3456',
#     #     '/demandForm',
#     #     '/newFeedback',
#     #     '/myFeedback',
#     #     '/messageCenter',
#     #     '/messageDetail?id=**********',
#     #     '/detail?id=**************',
#     #     '/zone/provider/homepage?id=**********',
#     #     '/list',
#     #     '/couponCenter?couponId=ChpzZXJ2aWNlTWFya2V0LnVybENvdXBvbktleRIgBK8irz0fkbG2zFqfbsyg1biYtDJZd3aCmLZsT2GsE3EaEtmBNnkH2LILtGlomnKmwZqp+iIgY7gf0/UjJHcS65IY96AFQemRdDj6Ad11AvNaqdhESs0oBTAB',
#     #     '/videotopic?categoryId=1889&type=creator',
#     #     '/guide',
#     #     '/identity'
#     # )
#     # def test_merchant_subAccount_error_page(self, route):
#     #
#     #     # 登录
#     #     self.child_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant_child")
#     #     self.assert_title("首页-快手服务市场-快手电商官网")
#     #     tabs_text = self.get_text(HomePage.merchant_background_entrance_button)
#     #     tab_list = ["已订购", "待评价", "优惠券"]
#     #     for item in tab_list:
#     #         self.assert_in(item, tabs_text)
#     #
#     #     check_route = host + route
#     #     print('当前检测的url为：', check_route)
#     #     self.check_page_error(check_route)
#     #
#     # @data(
#     #     '/qualification',
#     #     '/subaccountManage',
#     #     '/serviceManage',
#     #     '/addService',
#     #     '/serviceDetail?serviceId=**************',
#     #     '/serviceEdit?serviceId=**************',
#     #     '/home',
#     #     '/csData',
#     #     '/shop',
#     #     '/company',
#     #     '/zone/dashboard/provider/homepage',
#     #     '/zone/dashboard/provider/homepage/edit',
#     #     '/deposit',
#     #     '/funds',
#     #     '/invoice/index',
#     #     '/invoice/edit',
#     #     '/invoice/problem',
#     #     '/orderManage',
#     #     '/withdrawRecord',
#     #     '/exportRecord?exportType=3',
#     #     '/comment',
#     #     '/fuwuMessageCenter',
#     #     '/fuwuMessageDetail?id=**********',
#     #     '/control',
#     #     '/funds/test',
#     #     '/jump/im',
#     #     '/zone/dashboard/im/setting',
#     #     '/demandSquare',
#     #     '/demandList',
#     #     '/demandDetail?id=3456',
#     #     '/newFeedback',
#     #     '/myFeedback',
#     #     '/detail?id=**************',
#     #     '/zone/provider/homepage?id=**********',
#     #     '/list',
#     #     '/couponCenter?couponId=ChpzZXJ2aWNlTWFya2V0LnVybENvdXBvbktleRIgBK8irz0fkbG2zFqfbsyg1biYtDJZd3aCmLZsT2GsE3EaEtmBNnkH2LILtGlomnKmwZqp+iIgY7gf0/UjJHcS65IY96AFQemRdDj6Ad11AvNaqdhESs0oBTAB',
#     #     '/videotopic?categoryId=1889&type=creator',
#     #     '/guide',
#     #     '/identity',
#     #     '/fill',
#     #     '/zone/settle/fill',
#     #     '/audit',
#     #     '/auth'
#     # )
#     # def test_provider_error_page(self, route):
#     #     # 登录
#     #     self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")
#     #     self.assert_title("首页-快手服务市场-快手电商官网")
#     #     self.assert_text("我的服务台", HomePage.my_service_desk)
#     #
#     #     check_route = host + route
#     #     print('当前检测的url为：', check_route)
#     #     self.check_page_error(check_route)
#     #
#     # @data(
#     #     '/qualification',
#     #     '/subaccountManage',
#     #     '/serviceManage',
#     #     '/addService',
#     #     '/serviceDetail?serviceId=**************',
#     #     '/serviceEdit?serviceId=**************',
#     #     '/home',
#     #     '/csData',
#     #     '/shop',
#     #     '/company',
#     #     '/zone/dashboard/provider/homepage',
#     #     '/zone/dashboard/provider/homepage/edit',
#     #     '/deposit',
#     #     '/funds',
#     #     # '/invoice/index',
#     #     # '/invoice/edit',
#     #     '/invoice/problem',
#     #     '/orderManage',
#     #     '/withdrawRecord',
#     #     '/exportRecord?exportType=3',
#     #     '/comment',
#     #     '/fuwuMessageCenter',
#     #     '/fuwuMessageDetail?id=**********',
#     #     '/control',
#     #     '/funds/test',
#     #     '/jump/im',
#     #     '/zone/dashboard/im/setting',
#     #     '/demandSquare',
#     #     '/demandList',
#     #     '/demandDetail?id=3456',
#     #     '/newFeedback',
#     #     '/myFeedback',
#     #     '/detail?id=**************',
#     #     '/zone/provider/homepage?id=**********',
#     #     '/list',
#     #     '/couponCenter?couponId=ChpzZXJ2aWNlTWFya2V0LnVybENvdXBvbktleRIgBK8irz0fkbG2zFqfbsyg1biYtDJZd3aCmLZsT2GsE3EaEtmBNnkH2LILtGlomnKmwZqp+iIgY7gf0/UjJHcS65IY96AFQemRdDj6Ad11AvNaqdhESs0oBTAB',
#     #     '/videotopic?categoryId=1889&type=creator',
#     #     '/guide',
#     #     '/identity',
#     #     '/fill',
#     #     '/zone/settle/fill',
#     #     '/audit',
#     #     '/auth'
#     # )
#     # def test_provider_subAccount_error_page(self, route):
#     #     # 登录
#     #     self.child_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_child")
#     #     self.assert_title("首页-快手服务市场-快手电商官网")
#     #     self.assert_text("我的服务台", HomePage.my_service_desk)
#     #
#     #     check_route = host + route
#     #     print('当前检测的url为：', check_route)
#     #     self.check_page_error(check_route)
