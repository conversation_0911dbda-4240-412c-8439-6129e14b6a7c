import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from page_objects.service_market.provider_background_shop_information import ProviderBackgroundShopInformation
from .base import BaseTestCase


class TestProviderBackgroundFundOverview(BaseTestCase):
    """服务商后台-店铺信息页面"""

    def test_edit_shop_information(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"店铺信息"
        self.click(ProviderBackgroundLeftTab.shop_information_tab)
        time.sleep(2)

        # 获取店铺简介
        description = self.get_text(ProviderBackgroundShopInformation.description_input)
        # description = self.get_attribute(ProviderBackgroundShopInformation.description_input, 'text')

        # 点击修改
        self.click(ProviderBackgroundShopInformation.edit_button)
        time.sleep(1)

        description_info = ['已编辑', '未编辑']

        if description == description_info[0]:
            self.type(ProviderBackgroundShopInformation.description_input, description_info[1])

            # 点击保存
            self.click(ProviderBackgroundShopInformation.save_button)
            time.sleep(1)

            # 获取店铺简介
            description = self.get_text(ProviderBackgroundShopInformation.description_input)

            assert description == description_info[1], "更新店铺信息失败"

            # 点击修改
            self.click(ProviderBackgroundShopInformation.edit_button)
            time.sleep(1)

            # 点击取消
            self.click(ProviderBackgroundShopInformation.cancel_button)
            time.sleep(1)

            # 获取店铺简介
            description = self.get_text(ProviderBackgroundShopInformation.description_input)

            assert description == description_info[1], "获取店铺信息错误"
        elif description == description_info[1]:
            self.type(ProviderBackgroundShopInformation.description_input, description_info[0])

            # 点击保存
            self.click(ProviderBackgroundShopInformation.save_button)
            time.sleep(1)

            # 获取店铺简介
            description = self.get_text(ProviderBackgroundShopInformation.description_input)

            assert description == description_info[0], "更新店铺信息失败"

            # 点击修改
            self.click(ProviderBackgroundShopInformation.edit_button)
            time.sleep(1)

            # 点击取消
            self.click(ProviderBackgroundShopInformation.cancel_button)
            time.sleep(1)

            # 获取店铺简介
            description = self.get_text(ProviderBackgroundShopInformation.description_input)

            assert description == description_info[0], "获取店铺信息错误"
