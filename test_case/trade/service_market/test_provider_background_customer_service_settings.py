import time

from unittest import skip
from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_customer_service_settings import \
    ProviderBackgroundCustomerServiceSettings
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase

class TestProviderBackgroundCustomerServiceSettings(BaseTestCase):
    """服务商后台-客服设置页面"""

    def test_change_tab(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)

        # 点击离线自动回复tab
        self.click(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        time.sleep(2)

        offline_auto_reply_tab_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        assert len(offline_auto_reply_tab_list) == 2, "页面加载错误"

        # 点击快捷回复tab
        self.click(ProviderBackgroundCustomerServiceSettings.fast_reply_tab)
        time.sleep(2)

        self.assert_element_visible(ProviderBackgroundCustomerServiceSettings.reply_list_title)

    @skip
    def test_save_offline_auto_reply_button_status(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)

        # 点击离线自动回复tab
        self.click(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        time.sleep(2)

        offline_auto_reply_tab_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        assert len(offline_auto_reply_tab_list) == 2, "页面加载错误"

        button_status = self.get_attribute(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_button,
                                           'aria-checked')

        # 点击离线自动回复button
        self.click(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_button)
        time.sleep(2)

        button_status_2 = self.get_attribute(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_button,
                                             'aria-checked')

        # 点击保存并发布
        self.click(ProviderBackgroundCustomerServiceSettings.save_and_publish_button)
        time.sleep(2)

        self.refresh()

        # 点击离线自动回复tab
        self.click(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        time.sleep(2)

        button_status_3 = self.get_attribute(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_button,
                                             'aria-checked')

        if button_status == 'false':
            assert button_status_2 == 'true' and button_status_3 == 'true', '离线自动回复开关状态错误'
        elif button_status == 'true':
            assert button_status_2 == 'false' and button_status_3 == 'false', '离线自动回复开关状态错误'

    def test_save_offline_auto_reply_input(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)

        # 点击离线自动回复tab
        self.click(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        time.sleep(2)

        text = self.get_text(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_input)

        if text == '已编辑':
            self.type(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_input, "未编辑")
            time.sleep(2)

            text_2 = self.get_text(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_input)
            assert text_2 == '未编辑', "文本获取错误"
        elif text == '未编辑':
            self.type(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_input, "已编辑")
            time.sleep(2)

            text_2 = self.get_text(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_input)
            assert text_2 == '已编辑', "文本获取错误"
        # 点击保存并发布
        self.click(ProviderBackgroundCustomerServiceSettings.save_and_publish_button)
        time.sleep(2)

        self.refresh()

        # 点击离线自动回复tab
        self.click(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_tab)
        time.sleep(2)

        text_3 = self.get_text(ProviderBackgroundCustomerServiceSettings.offline_auto_reply_input)

        if text == '已编辑':
            assert text_3 == '未编辑', "文本获取错误"
        elif text == '未编辑':
            assert text_3 == '已编辑', "文本获取错误"
    @skip
    def test_add_and_delete_fast_reply_group(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)
        time.sleep(2)

        fast_reply_group_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

        assert len(fast_reply_group_list) > 0, "获取快捷回复列表失败"

        delete_fast_group_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.delete_fast_reply_group_list)

        for i in range(len(fast_reply_group_list)):
            if fast_reply_group_list[i].text == 'UI自动化':
                # 点击对应行的删除button
                delete_fast_group_list[i].click()
                time.sleep(2)

                self.click(ProviderBackgroundCustomerServiceSettings.popup_delete_button)
                time.sleep(2)

                fast_reply_group_list_2 = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

                assert len(fast_reply_group_list_2) > 0, "获取快捷回复列表失败"

                assert 'UI自动化' not in [fast_reply_group_list_2[j].text for j in
                                       range(len(fast_reply_group_list_2))], "删除快捷回复失败"

                break

        # 点击新增分组button
        self.click(ProviderBackgroundCustomerServiceSettings.add_fast_reply_group_button)
        time.sleep(2)

        # 输入分组名称
        self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_group_popup_input, "UI自动化")

        # 点击确定
        self.click(ProviderBackgroundCustomerServiceSettings.popup_confirm_button)
        time.sleep(2)

        fast_reply_group_list_3 = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

        assert len(fast_reply_group_list_3) > 0, "获取快捷回复列表失败"

        assert 'UI自动化' in [fast_reply_group_list_3[i].text for i in range(len(fast_reply_group_list_3))], "新增快捷回复分组失败"

    @skip
    def test_add_and_delete_fast_reply(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)
        time.sleep(2)

        fast_reply_group_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

        assert len(fast_reply_group_list) > 0, "获取快捷回复列表失败"

        fast_reply_group_row_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_row_list)

        for i in range(len(fast_reply_group_list)):
            if fast_reply_group_list[i].text in ['UI自动化名称未编辑', 'UI自动化名称已编辑']:
                # 如果不是第一行，则点击并展开对应行
                if i != 0:
                    fast_reply_group_row_list[i].click()
                    time.sleep(2)

                fast_reply_message_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                fast_reply_delete_button_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_delete_button_list)

                for j in range(len(fast_reply_message_list)):
                    if fast_reply_message_list[j].text == 'UI自动化':
                        # 点击快捷回复的删除
                        fast_reply_delete_button_list[j].click()
                        time.sleep(2)

                        # 点击删除
                        self.click(ProviderBackgroundCustomerServiceSettings.popup_delete_button)
                        time.sleep(2)

                        fast_reply_message_list_2 = self.find_elements(
                            ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                        assert "UI自动化" not in [fast_reply_message_list_2[k].text for k in
                                               range(len(fast_reply_message_list_2))], "删除快捷回复失败"
                        break

                # 点击新增快捷回复
                self.click(ProviderBackgroundCustomerServiceSettings.add_fast_reply_button)
                time.sleep(2)

                # 点击请选择分组
                self.click(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_select_group_downbox)
                time.sleep(2)

                group_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_select_group_downbox_list)

                for j in range(len(group_list)):
                    if group_list[i].text in ['UI自动化名称未编辑', 'UI自动化名称已编辑']:
                        group_list[i].click()

                        break

                # 输入快捷编码
                self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_fast_code_input, "UI自动化")

                # 输入回复消息
                self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_reply_message_input, "UI自动化")

                # 点击确定
                self.click(ProviderBackgroundCustomerServiceSettings.popup_confirm_button)
                time.sleep(2)

                fast_reply_message_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                assert "UI自动化" in [fast_reply_message_list[j].text for j in
                                   range(len(fast_reply_message_list))], "新增快捷回复失败"

    @skip
    def test_edit_fast_reply_group_name(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)
        time.sleep(2)

        fast_reply_group_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

        assert len(fast_reply_group_list) > 0, "获取快捷回复列表失败"

        fast_reply_group_delete_button_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_delete_button_list)

        for i in range(len(fast_reply_group_list)):
            if fast_reply_group_list[i].text == 'UI自动化名称已编辑':
                fast_reply_group_delete_button_list[i].click()
                time.sleep(2)

                self.type(ProviderBackgroundCustomerServiceSettings.edit_fast_reply_group_name_input, "UI自动化名称未编辑")

                self.click(ProviderBackgroundCustomerServiceSettings.popup_confirm_button)

                time.sleep(2)

                fast_reply_group_list_2 = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

                assert "UI自动化名称未编辑" in [fast_reply_group_list_2[j].text for j in
                                        range(len(fast_reply_group_list_2))], "编辑快捷分组名称失败"
            elif fast_reply_group_list[i].text == 'UI自动化名称未编辑':
                fast_reply_group_delete_button_list[i].click()
                time.sleep(2)

                self.type(ProviderBackgroundCustomerServiceSettings.edit_fast_reply_group_name_input, "UI自动化名称已编辑")

                self.click(ProviderBackgroundCustomerServiceSettings.popup_confirm_button)

                time.sleep(2)

                fast_reply_group_list_2 = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

                assert "UI自动化名称已编辑" in [fast_reply_group_list_2[j].text for j in
                                        range(len(fast_reply_group_list_2))], "编辑快捷分组名称失败"

    @skip
    def test_edit_fast_reply(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)
        time.sleep(2)

        fast_reply_group_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

        assert len(fast_reply_group_list) > 0, "获取快捷回复列表失败"

        fast_reply_group_row_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_row_list)

        for i in range(len(fast_reply_group_list)):
            if fast_reply_group_list[i].text in ['UI自动化名称未编辑', 'UI自动化名称已编辑']:
                # 如果不是第一行，则点击并展开对应行
                if i != 0:
                    fast_reply_group_row_list[i].click()
                    time.sleep(2)

                fast_reply_message_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                fast_reply_edit_button_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_edit_button_list)

                for j in range(len(fast_reply_message_list)):
                    if fast_reply_message_list[j].text == 'UI自动化已编辑':
                        # 点击快捷回复的修改
                        fast_reply_edit_button_list[j].click()
                        time.sleep(2)

                        self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_fast_code_input,
                                  "UI自动化未编辑")

                        self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_reply_message_input,
                                  "UI自动化未编辑")

                        self.click(ProviderBackgroundCustomerServiceSettings.popup_confirm_button)

                        time.sleep(2)

                        fast_reply_message_list_2 = self.find_elements(
                            ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                        assert "UI自动化未编辑" in [fast_reply_message_list_2[k].text for k in
                                              range(len(fast_reply_message_list_2))], "修改快捷回复失败"

                        break
                    elif fast_reply_message_list[j].text == 'UI自动化未编辑':
                        # 点击快捷回复的修改
                        fast_reply_edit_button_list[j].click()
                        time.sleep(2)

                        self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_fast_code_input,
                                  "UI自动化已编辑")

                        self.type(ProviderBackgroundCustomerServiceSettings.add_fast_reply_popup_reply_message_input,
                                  "UI自动化已编辑")

                        self.click(ProviderBackgroundCustomerServiceSettings.popup_confirm_button)

                        time.sleep(2)

                        fast_reply_message_list_2 = self.find_elements(
                            ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                        assert "UI自动化已编辑" in [fast_reply_message_list_2[k].text for k in
                                              range(len(fast_reply_message_list_2))], "修改快捷回复失败"

                        break

                break
    @skip
    def test_move_fast_reply(self):
        # 登录
        self.provider_login_2("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider_2")

        # 点击"客服设置"
        self.click(ProviderBackgroundLeftTab.customer_service_settings_tab)
        time.sleep(2)

        fast_reply_group_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_title_list)

        assert len(fast_reply_group_list) > 0, "获取快捷回复列表失败"

        fast_reply_group_row_list = self.find_elements(
            ProviderBackgroundCustomerServiceSettings.fast_reply_group_row_list)

        for i in range(len(fast_reply_group_list)):
            if fast_reply_group_list[i].text in ['UI自动化名称未编辑', 'UI自动化名称已编辑']:
                # 如果不是第一行，则点击并展开对应行
                if i != 0:
                    fast_reply_group_row_list[i].click()
                    time.sleep(2)

                fast_reply_message_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                fast_reply_move_button_list = self.find_elements(
                    ProviderBackgroundCustomerServiceSettings.fast_reply_move_button_list)

                if len(fast_reply_message_list) > 1:
                    fast_reply_message_text_list = [fast_reply_message_list[j].text for j in
                                                    range(len(fast_reply_message_list))]
                    text_1 = fast_reply_message_text_list[0]
                    text_2 = fast_reply_message_text_list[1]

                    # 点击第一行的移动button
                    fast_reply_move_button_list[0].click()
                    time.sleep(5)

                    fast_reply_message_list_2 = self.find_elements(
                        ProviderBackgroundCustomerServiceSettings.fast_reply_message_list)

                    fast_reply_message_text_list_2 = [fast_reply_message_list_2[j].text for j in
                                                      range(len(fast_reply_message_list_2))]
                    text_1_new = fast_reply_message_text_list_2[0]
                    text_2_new = fast_reply_message_text_list_2[1]

                    assert text_1 == text_2_new and text_2 == text_1_new, "移动快捷回复失败"
