import time

import pytest

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_tool_service import MerchantBackgroundToolService
from .base import BaseTestCase


class TestMerchantBackgroundToolService(BaseTestCase):
    """商家后台-工具服务页面"""
    @pytest.mark.skip("风控暂时未修复")
    def test_authorized_and_use_service(self):
        # 授权并使用服务

        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        use_service_button_list = self.find_elements(MerchantBackgroundToolService.use_service_button)
        assert len(use_service_button_list) > 0, "获取服务列表错误"

        use_service_button_list[0].click()
        time.sleep(5)

        self.switch_to_window(1)
        current_url = self.get_current_url()
        # assert "json.com" or "open.kwaixiaodian.com" in current_url

        assert ("json.com" in current_url) or ("open.kwaixiaodian.com" in current_url)

    def test_change_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        effecting_time_list = self.find_elements(MerchantBackgroundToolService.effecting_time_list)

        assert len(effecting_time_list) > 0, "获取工具服务列表失败"

        # 点击已过期tab
        self.click(MerchantBackgroundToolService.expired_tab)
        time.sleep(2)

        expired_go_to_buy_button_list = self.find_elements(MerchantBackgroundToolService.expired_go_to_buy_button)

        assert len(expired_go_to_buy_button_list) > 0, "获取工具服务列表失败"

        # 点击已失效tab
        self.click(MerchantBackgroundToolService.lose_effect_tab)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundToolService.lose_effect_tab_message)

    def test_select_button(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        service_list_service_name_list = self.find_elements(
            MerchantBackgroundToolService.service_list_service_name_list)

        assert len(service_list_service_name_list) > 0, "获取服务列表失败"

        # 获取第一行的服务名称
        service_name = service_list_service_name_list[0].text

        # 输入服务名称
        self.type(MerchantBackgroundToolService.service_name_input, service_name)

        # 点击查询
        self.click(MerchantBackgroundToolService.select_button)
        time.sleep(2)

        service_list_service_name_list = self.find_elements(
            MerchantBackgroundToolService.service_list_service_name_list)

        assert len(service_list_service_name_list) == 1 and \
               service_list_service_name_list[0].text == service_name, "获取服务列表错误"

    def test_remark_button(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")


        # 点击"已订购"
        self.click(HomePage.ordered_button)
        time.sleep(2)

        service_list_service_name_list = self.find_elements(
            MerchantBackgroundToolService.service_list_service_name_list)

        assert len(service_list_service_name_list) > 0, "获取服务列表失败"

        service_name = service_list_service_name_list[0].text

        # 输入服务名称
        self.type(MerchantBackgroundToolService.service_name_input, service_name)

        text = self.get_attribute(MerchantBackgroundToolService.service_name_input, "value")

        assert text == service_name, "服务名称输入错误"

        # 点击重置
        self.click(MerchantBackgroundToolService.remark_button)
        time.sleep(2)

        text_2 = self.get_attribute(MerchantBackgroundToolService.service_name_input, "value")
        assert text_2 == '', "重置按钮清空内容失败"
