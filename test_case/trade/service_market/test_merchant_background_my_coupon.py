import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.merchant_background_my_discount_coupon import MerchantBackgroundMyDiscountCoupon
from .base import BaseTestCase


class TestMerchantBackgroundMyCoupon(BaseTestCase):
    """商家后台-我的优惠券"""

    def test_change_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"优惠券"
        self.click(HomePage.coupon_button)
        time.sleep(2)

        self.assert_element_visible(MerchantBackgroundMyDiscountCoupon.my_coupon_title)

        coupon_go_to_use_button_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.go_to_use_button)

        for i in range(len(coupon_go_to_use_button_list)):
            assert coupon_go_to_use_button_list[i].text == '去使用', '去使用button错误'

        # 点击已使用tab
        self.click(MerchantBackgroundMyDiscountCoupon.used_tab)
        time.sleep(2)

        img_url_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.used_or_expired_img_list)
        for i in range(len(img_url_list)):
            img_src = img_url_list[i].get_attribute('src')[21:]
            assert img_src == '/kos/nlav11347/kwaishop-fuwu-h5/coupon-used.png', "优惠券已使用图标错误"

        # 点击已过期tab
        self.click(MerchantBackgroundMyDiscountCoupon.expired_tab)
        time.sleep(2)

        img_url_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.used_or_expired_img_list)
        for i in range(len(img_url_list)):
            img_src = img_url_list[i].get_attribute('src')[21:]
            assert img_src == '/kos/nlav11347/kwaishop-fuwu-market/expired.png', "优惠券已过期图标错误"

    def test_change_coupon_tab(self):
        # 登录
        self.login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_merchant")

        # 点击"优惠券"
        self.click(HomePage.coupon_button)
        time.sleep(2)

        coupon_card_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.coupon_card_list)
        assert len(coupon_card_list) > 0, "获取优惠券列表失败"

        coupon_go_to_use_button_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.go_to_use_button)

        for i in range(len(coupon_go_to_use_button_list)):
            assert coupon_go_to_use_button_list[i].text == '去使用', '去使用button错误'

        # 点击商家任务奖励tab
        self.click(MerchantBackgroundMyDiscountCoupon.merchant_task_tab)
        time.sleep(3)

        coupon_card_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.coupon_card_list)
        assert len(coupon_card_list) > 0, "获取优惠券列表失败"

        coupon_go_to_use_button_list = self.find_elements(MerchantBackgroundMyDiscountCoupon.go_to_use_button)

        for i in range(len(coupon_go_to_use_button_list)):
            assert coupon_go_to_use_button_list[i].text == '去使用', '去使用button错误'
