import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_customer_service_outsourcing_data import \
    ProviderBackgroundCustomerServiceOutsourcingData
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundCustomerServiceOutsourcing(BaseTestCase):
    """服务商后台-客服外包数据页面"""

    def test_subAccount_detail_data_download(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"客服外包数据"
        self.click(ProviderBackgroundLeftTab.customer_service_outsourcing_data_tab)
        time.sleep(2)

        # 点击子账号详细数据tab
        self.click(ProviderBackgroundCustomerServiceOutsourcingData.subAccount_detail_tab)
        time.sleep(2)

        # 点击数据下载
        self.click(ProviderBackgroundCustomerServiceOutsourcingData.subAccount_download_data_button)
        self.assert_text("申请导出成功", ProviderBackgroundCustomerServiceOutsourcingData.export_success_message)

    def test_subAccount_detail_data_export_record(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"客服外包数据"
        self.click(ProviderBackgroundLeftTab.customer_service_outsourcing_data_tab)
        time.sleep(2)

        # 点击子账号详细数据tab
        self.click(ProviderBackgroundCustomerServiceOutsourcingData.subAccount_detail_tab)
        time.sleep(2)

        # 点击导出记录
        self.click(ProviderBackgroundCustomerServiceOutsourcingData.subAccount_export_record_button)
        time.sleep(5)

        download_button_list = self.find_elements(
            ProviderBackgroundCustomerServiceOutsourcingData.export_record_download_button)
        assert len(download_button_list) > 0, "页面加载失败"
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/exportRecord?exportType=7'

    def test_merchant_detail_data_download(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")
        # 点击"客服外包数据"
        self.click(ProviderBackgroundLeftTab.customer_service_outsourcing_data_tab)
        time.sleep(2)

        # 点击数据下载
        self.click(ProviderBackgroundCustomerServiceOutsourcingData.download_data_button)
        self.assert_text("申请导出成功", ProviderBackgroundCustomerServiceOutsourcingData.export_success_message)

    def test_merchant_detail_data_export_record(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"客服外包数据"
        self.click(ProviderBackgroundLeftTab.customer_service_outsourcing_data_tab)
        time.sleep(2)

        # 点击导出记录
        self.click(ProviderBackgroundCustomerServiceOutsourcingData.export_record_button)
        time.sleep(5)

        download_button_list = self.find_elements(
            ProviderBackgroundCustomerServiceOutsourcingData.export_record_download_button)
        assert len(download_button_list) > 0, "页面加载失败"
        current_url = self.get_current_url()
        assert current_url == 'https://fuwu.kwaixiaodian.com/exportRecord?exportType=6'
