import time

from page_objects.service_market.home_page import HomePage
from page_objects.service_market.provider_background_corporate_information import ProviderBackgroundCorporateInformation
from page_objects.service_market.provider_background_left_tab import ProviderBackgroundLeftTab
from .base import BaseTestCase


class TestProviderBackgroundCorporateInformation(BaseTestCase):
    """服务商后台-企业信息页面"""

    def test_edit_shop_information(self):
        # 登录
        self.provider_login("SERVICE_MARKET_LOGIN_DOMAIN", "service_market_provider")

        # 点击"企业信息"
        self.click(ProviderBackgroundLeftTab.corporate_information_tab)
        time.sleep(2)

        # 获取页面内信息
        information_key_list = self.find_elements(ProviderBackgroundCorporateInformation.information_key_list)
        information_value_list = self.find_elements(ProviderBackgroundCorporateInformation.information_value_list)

        assert len(information_key_list) == 17 and len(information_value_list) == 17, "获取内容个数错误"
