from utils.kconf_help import get_staging_kconf

kconf_content = get_staging_kconf("kwaishop.kwaishop-qa-manufacture-service.AftersalesUIAutoTestConfig")
kconf_content_pc = get_staging_kconf("kwaishop.kwaishop-qa-manufacture-service.AftersalesUIAutoTestConfig")["PC_UI"]

# operate_RefundId_TestSellerRefundDetail_RefundOnly = kconf_content['operate_RefundId_TestSellerRefundDetail_RefundOnly']
# operate_OrderId_TestSellerRefundDetail_RefundOnly = kconf_content['operate_OrderId_TestSellerRefundDetail_RefundOnly']

# operate_RefundId_TestSellerRefundDetail_SellerNoHandleOver21Day_RefundOnly = kconf_content[
#     'operate_RefundId_TestSellerRefundDetail_SellerNoHandleOver21Day_RefundOnly']
# operate_RefundId_TestSellerRefundDetail_RefundAndReturn_Stage1 = kconf_content[
#     'operate_RefundId_TestSellerRefundDetail_RefundAndReturn_Stage1']
# operate_RefundId_TestSellerRefundDetail_RefundAndReturn_Stage2 = kconf_content[
#     'operate_RefundId_TestSellerRefundDetail_RefundAndReturn_Stage2']
# operate_Oid_TestSelleOrderRefundList_BlockExchangeHasApply = kconf_content[
#     'operate_Oid_TestSelleOrderRefundList_BlockExchangeHasApply']

seller_insurance_order_id = kconf_content['seller_insurance_order_id']
buyer_insurance_order_id = kconf_content['buyer_insurance_order_id']
no_insurance_order_id = kconf_content['no_insurance_order_id']

common_refund_id = kconf_content['common_refund_id']
Oid_Stage1 = kconf_content['order_id_stage1']
Oid_Stage2 = kconf_content['order_id_stage2']
Refund_id_stage1 = kconf_content['refund_id_stage1']
Refund_id_stage2 = kconf_content['refund_id_stage2']
official_refund_id = kconf_content['official_refund_id']
seller_duty_refund_id = kconf_content['seller_duty_refund_id']
seller_duty_edit_refund_id = kconf_content['seller_duty_edit_refund_id']
full_back_refund_id = kconf_content['full_back_refund_id']
use_now_pay_later_refund_id = kconf_content['use_now_pay_later_refund_id']
deposit_pre_sale_refund_id = kconf_content['deposit_pre_sale_refund_id']
discount_overlay_refund_id = kconf_content['discount_overlay_refund_id']
aftersales_coupon_refund_id = kconf_content['aftersales_coupon_refund_id']
gift_refund_id = kconf_content['gift_refund_id']
instant_refund_id = kconf_content['instant_refund_id']
flash_refund_id = kconf_content['flash_refund_id']
price_protect_refund_id = kconf_content['price_protect_refund_id']
special_privileges_refund_id = kconf_content['special_privileges_refund_id']
freight_insurance_refund_id = kconf_content['freight_insurance_refund_id']
self_return_seller_duty_refund_id = kconf_content['self_return_seller_duty_refund_id']
pick_up_detail_refund_id = kconf_content['pick_up_detail_refund_id']
station_detail_refund_id = kconf_content['station_detail_refund_id']
#
# refund_only_refuse = kconf_content['refund_only_refuse']
# refund_only_to_refund_return = kconf_content['refund_only_to_refund_return']
# refund_refuse_delivery = kconf_content['refund_refuse_delivery']
# refund_only_part = kconf_content['refund_only_part']
#
# refund_return_stage_one_refuse = kconf_content['refund_return_stage_one_refuse']
# refund_return_to_refund_only = kconf_content['refund_return_to_refund_only']
# refund_return_stage_two_refuse = kconf_content['refund_return_stage_two_refuse']

# exchange_stage_one_wait_seller_handle = kconf_content['exchange_stage_one_wait_seller_handle']
# exchange_stage_one_buyer_return = kconf_content['exchange_stage_one_buyer_return']
exchange_stage_two_seller_confirm_receipt = kconf_content['exchange_stage_two_seller_confirm_receipt']
exchange_stage_two_buyer_confirm_receipt = kconf_content['exchange_stage_two_buyer_confirm_receipt']
exchange_stage_one_seller_refuse = kconf_content['exchange_stage_one_seller_refuse']
exchange_stage_two_seller_refuse = kconf_content['exchange_stage_two_seller_refuse']
exchange_to_refund_and_return = kconf_content['exchange_to_refund_and_return']

negotiate_modify_refund = kconf_content['negotiate_modify_refund']
negotiate_supplement_evidence = kconf_content['negotiate_supplement_evidence']
negotiate_supplement_logistics = kconf_content['negotiate_supplement_logistics']


def is_correct_url(path, url, env=None):
    """
    判断url是否符合预期
    """
    if env == 'prt':
        domain = 'https://eshop-s.prt.kwaixiaodian.com/'
    else:
        domain = 'https://s.kwaixiaodian.com/'
    cur_url = domain + path
    return url == cur_url
