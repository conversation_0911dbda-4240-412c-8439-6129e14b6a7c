import random
from time import sleep
import unittest

import pytest
from ddt import ddt

from page_objects.refund.refund import *
from ..base import BaseTestCase


# 快手小店-退货补运费
@ddt
class TestRefundInsurance(BaseTestCase):
    def setup_class(self):
        self.dataSet = {
            'Invalid': {
                'oid': ****************,
                'status': '已失效'
            },
            'ercilipei': {
                'oid': ****************,
                'status': '未通过'
            },
            'pass_highrisk': {
                'oid': ****************,
                'status': '补偿成功'
            }
        }

    # 跳转到退货补运费
    def to_Insurance(self, account='refund_account', choice=0):
        """
        account: account.py中的key
        choice: 0-单账户登录； 1-多账户的第一个； 2-多账户的第二个
        """
        if choice == 0:
            self.login("MERCHANT_DOMAIN", account)
        else:
            self.multiLogin(domain="MERCHANT_DOMAIN", account=account, choice=choice)
        self.assert_title("快手小店")
        sleep(3)
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(5)
        self.click(RefundPage.left_refund_btn)
        self.click(RefundPage.left_refund_btn)
        self.click(RefundPage.left_insurance)
        sleep(3)
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(8)
        self.assert_text("退货补运费", RefundPage.left_insurance)
        sleep(2)
        # self.assert_text("新功能引导", Insurance.new_title)
        # self.click(Insurance.new_X)
        # 窗口最大化，防止后面经营助手挡住按钮
        self.driver.maximize_window()
        self.close_merchant_assistant()

    # 列表页展示订单
    def find_order(self, oid):
        self.click(Insurance.tab_record)
        self.type(Insurance.list_oidInput, oid)
        self.click(Insurance.list_queryBnt)
        sleep(1)
        self.assert_element(Insurance.list_oid, oid)

    def to_fwsm(self):
        self.to_Insurance()
        self.assert_text('服务说明', Insurance.fwsm)
        self.click(Insurance.fwsm)
        self.assert_text('服务说明', Insurance.fwsm_title)

    def fwsm_subpage(self, entrance, checkText):
        self.click(entrance)
        sleep(5)
        page_source = self.driver.page_source
        assert checkText in page_source
        self.driver.back()
        sleep(5)

    def randomcity(self):
        cityList = ['北京', '上海', '浙江', '天津', '四川', '重庆', '安徽', '福建', '甘肃']
        return cityList[random.randint(0, len(cityList)) - 1]

    '''
    【未开通】【余额不足】二次弹窗，引导跳转充值页
    【已开通】自选权益开通关闭
    充值提现记录，点击跳转
    自动充值，开通关闭
    提现按钮，点击跳转提现页
    充值按钮，点击跳转充值页
    常见问题按钮点击弹窗
    【已开通】暂停服务按钮
    服务记录tab点击- 筛选 √
    服务列表页，高风险标 √
    服务说明按钮，点击跳转服务说明页 √
    新增：定向商品页面、
    '''

    def test_zxqy(self):
        # 退货补运费->自选权益
        self.to_Insurance()
        try:
            self.assert_text('自选权益设置', Insurance.zx_title)
            # self.assert_text('高风险率开通退货补运费服务', Insurance.zx_highrisk_text)
            self.assert_text('小额商品退货补运费服务', Insurance.zx_sku_text)
            self.assert_text("大件商品退货补运费服务", Insurance.zx_big_sku_text)
            self.assert_text("《大件商品服务费收取规则》", Insurance.zx_big_link_1)
            self.assert_text('《大件商品支持类目范围》', Insurance.zx_big_link_2)

        except:
            if not self.is_element_visible("//span[contains(text(),'立即开通')]"):
                raise Exception('退货补运费页面不符合预期')
            else:
                print('商家未开通退货补运费')
        else:
            print('商家开通退货补运费')

    def test_open_close(self):
        self.to_Insurance('huwen', 0)
        status = self.get_text(Insurance.open_status)
        if status == '服务生效中':
            assert '服务开通于' in self.get_text("//span[@class='status-title-periodOfValidity']")
        elif status == "定向商品服务生效中，全店服务未开通":
            self.assert_text('立即开通', "//span[contains(text(),'立即开通')]")

    def test_insurance_record_show(self):
        """
        退货补运费-服务记录-展示
        """
        self.to_Insurance('huwen', 0)
        self.click(Insurance.tab_record)
        sleep(2)
        assert self.is_element_visible("//span[contains(text(),'退货补运费服务记录')]")
        assert self.is_element_visible("//span[@class='dRJ_EqmYtXfOQRGYlNJL'][contains(text(),'全部')]")
        assert self.is_element_visible("//span[@class='dRJ_EqmYtXfOQRGYlNJL'][contains(text(),'生效中')]")
        assert self.is_element_visible("//span[contains(text(),'审核中')]")
        assert self.is_element_visible("//span[contains(text(),'重 置')]")

    def test_insurance_record_query(self):
        """
        退货补运费-服务记录-查询
        """
        self.to_Insurance('huwen', 0)
        self.click(Insurance.tab_record)
        sleep(2)
        self.type('#orderId', 2425300029110169)
        self.click("//span[contains(text(),'查 询')]")
        sleep(2)
        assert self.is_element_visible("//td[contains(text(),'补偿成功')]"), '不符合预期'

    def test_insurance_export(self):
        """
        退货补运费-服务记录-批量导出
        """
        self.to_Insurance('huwen', 0)
        self.click(Insurance.tab_record)
        sleep(2)
        self.click("//span[contains(text(),'批量导出')]")
        self.assert_text('导出退货补运费服务记录提示', "//div[@class='X8m0VM2kJs0XL090OTeF']")
        assert self.is_element_visible("//span[contains(text(),'生成报表')]")
        assert self.is_element_visible("//span[contains(text(),'取 消')]")

    def test_insurance_export_record(self):
        """
        退货补运费-服务记录-查看已导出报表
        """
        self.to_Insurance('huwen', 0)
        self.click(Insurance.tab_record)
        sleep(2)
        self.click("//span[contains(text(),'查看已导出报表')]")
        sleep(2)
        assert self.is_element_visible("//div[@class='gqmMJVHAL13YMaIeetIu']//span[contains(text(),'退货补运费服务记录报表')]")
        assert self.is_element_visible("//span[contains(text(),'报表查询时间')]")
        assert self.is_element_visible("//span[contains(text(),'处理状态')]")
        assert self.is_element_visible("//span[contains(text(),'操作')]")

    def test_switch_record_button(self):
        """
        退货补运费-开关记录
        """
        self.to_Insurance('huwen', 0)
        self.click("//span[contains(text(),'开关记录')]")
        sleep(2)
        assert self.is_element_visible("//div[@class='gqmMJVHAL13YMaIeetIu']//span[contains(text(),'开关记录')]")

    def test_switch_record_insurance(self):
        """
        开关记录页面-退货补运费服务
        """
        self.to_Insurance('huwen', 0)
        self.click("//span[contains(text(),'开关记录')]")
        sleep(2)
        assert self.is_element_visible("//span[contains(text(),'退货补运费服务')]")
        assert self.is_element_visible("//span[contains(text(),'重 置')]")
        assert self.is_element_visible("//span[contains(text(),'查 询')]")
        assert self.assert_text('退货补运费服务',
                                '//*[@id="root"]/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[3]')

    def test_switch_record_query(self):
        """
        开关记录页面-退货补运费服务-查询功能
        """
        self.to_Insurance('huwen', 0)
        self.click("//span[contains(text(),'开关记录')]")
        sleep(2)
        self.click(
            '#pro-form-wrapper > div:nth-child(2) > div:nth-child(2) > div > div.kwaishop-seller-insurance-pc-col.kwaishop-seller-insurance-pc-form-item-control > div')
        self.click("//div[@title='商家主账号']//div[1]")
        self.click("//span[contains(text(),'查 询')]")
        sleep(2)
        self.assert_text('商家主账号',
                         '//*[@id="root"]/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]')

    def test_switch_record_self(self):
        """
        开关记录页面-自选权益
        """
        self.to_Insurance('huwen', 0)
        self.click("//span[contains(text(),'开关记录')]")
        sleep(2)
        self.click("//span[contains(text(),'自选权益')]")
        sleep(2)
        assert self.is_element_visible("//span[contains(text(),'重 置')]")
        assert self.is_element_visible("//span[contains(text(),'查 询')]")
        text = self.get_text(
            '//*[@id="root"]/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[3]')
        assert text == '小额免退货补运费服务' or text == '高风险率版本退货补运费服务', '不符合预期'

    def test_notenoughmoney(self):
        self.to_Insurance("syt_account_zl")
        self.click(Insurance.gxk_btn)
        sleep(2)
        self.click(Insurance.open_btn)
        sleep(2)
        assert self.is_element_visible(
            "//div[contains(text(),'您的余额不足500元，无法开通退货补运费服务，点击「立即充值」完成账户充值后即可开通。')]")
        self.click(Insurance.box_btn_two)
        sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/flow/insurance-payment?mode=ACCOUNT_TYPE_PREMIUM&originURL=/zone/insurance/freight/index'
        prt_url = 'https://prt-eshop-s.test.gifshow.com/zone/fund/flow/insurance-payment?mode=ACCOUNT_TYPE_PREMIUM&originURL=/zone/insurance/freight/index'
        if self.var1 == 'prt':
            assert self.driver.current_url == prt_url, '余额不足时点击充值按钮跳转页面不正确'
        else:
            assert self.driver.current_url == url, '余额不足时点击充值按钮跳转页面不正确'

    # 定向商品服务
    def test_directed_product_services(self):
        self.to_Insurance('huwen', 0)
        self.assert_text('定向商品服务', "//span[@class='GdKgB5H_PebZgnHIp3XY']")
        self.click("//button[@type='button']//span[contains(text(),'立即查看')]")
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        assert self.is_element_visible("//span[contains(text(),'定向商品服务：服务生效中')]")
        if self.var1 == 'prt':
            url = 'https://eshop-s.prt.kwaixiaodian.com/zone/insurance/freight/goods-freight?channel=eshop&insuranceCode=5004'
            assert self.get_current_url() == url, '定向商品服务页面地址错误，请检查'
        else:
            url = 'https://s.kwaixiaodian.com/zone/insurance/freight/goods-freight?channel=eshop&insuranceCode=5004'
            assert self.get_current_url() == url, '定向商品服务页面地址错误，请检查'

    def _to_targeted_product_launch_page(self):
        self.login("MERCHANT_DOMAIN", 'huwen')
        if self.var1 == 'prt':
            url = 'https://eshop-s.prt.kwaixiaodian.com/zone/insurance/freight/goods-freight?channel=eshop&insuranceCode=5004'
        else:
            url = 'https://s.kwaixiaodian.com/zone/insurance/freight/goods-freight?channel=eshop&insuranceCode=5004'
        self.open(url)
        sleep(3)

    # 定向商品开通页-页面展示
    def test_targeted_product_launch_page(self):
        self._to_targeted_product_launch_page()
        self.click("//span[@class='BdE_2tEf8mW_CTgl_4hw']")
        sleep(1)
        assert self.is_element_visible("//div[@class='gqmMJVHAL13YMaIeetIu']//span[contains(text(),'定向商品开通')]")
        assert self.is_element_visible(
            "//div[contains(text(),'定向商品退货补运费服务，是针对部分满足条件的货主找50w粉以上达人/主播带货场景下，仅需将被带货的商')]")
        assert self.is_element_visible("//th[contains(text(),'商品信息')]")
        assert self.is_element_visible("//th[contains(text(),'开通状态')]")
        assert self.is_element_visible("//th[contains(text(),'变更原因')]")
        assert self.is_element_visible("//th[contains(text(),'变更时间')]")
        assert self.is_element_visible("//th[contains(text(),'操作')]")

    # 定向商品开通页-查询
    def test_targeted_product_launch_query(self):
        self._to_targeted_product_launch_page()
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/span/input', '21255860124765')
        self.click("//span[contains(text(),'查 询')]")
        sleep(2)
        self.assert_text("请勿操作这个订单", "//div[@class='fHRvFoWuteBxZw8jiAY9 fgYpeslJL7u7LzCYMyx5']")
        # 开通服务弹窗
        self.click("//button[@type='button']//span[contains(text(),'开通服务')]")
        sleep(1)
        self.assert_text("确认开通该商品的退货补运费服务吗？", "//span[@class='kwaishop-seller-insurance-pc-modal-confirm-title']")
        assert self.is_element_visible("//div[contains(text(),'开通服务后，该商品符合条件的订单将带退货补运费服务。')]")
        assert self.is_element_visible("//button[@type='button']//span[contains(text(),'确认开通')]")

    # 定向商品开通页-未开通
    def test_targeted_product_launch_tab_not_activate(self):
        self._to_targeted_product_launch_page()
        self.click(
            "//div[@class='kwaishop-seller-insurance-pc-pro-checkableTag kwaishop-seller-insurance-pc-pro-checkableTag__default kwaishop-seller-insurance-pc-pro-checkableTag__middle']//span[contains(text(),'未开通')]")
        sleep(1)
        assert self.is_element_visible("//button[@type='button']//span[contains(text(),'开通服务')]")

    # 定向商品开通页-已开通
    def test_targeted_product_launch_tab_activate(self):
        self._to_targeted_product_launch_page()
        self.click(
            "//div[@class='kwaishop-seller-insurance-pc-pro-checkableTag kwaishop-seller-insurance-pc-pro-checkableTag__default kwaishop-seller-insurance-pc-pro-checkableTag__middle']//span[contains(text(),'已开通')]")
        sleep(1)
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/span/input', '21643183054765')
        self.click("//span[contains(text(),'查 询')]")
        sleep(2)
        self.click("//span[contains(text(),'关闭服务')]")
        sleep(1)
        self.assert_text("确认关闭该商品的退货补运费服务吗？", "//span[@class='kwaishop-seller-insurance-pc-modal-confirm-title']")
        assert self.is_element_visible("//div[contains(text(),'关闭服务后，该商品的订单将不再支持退货补运费服务。')]")
        assert self.is_element_visible("//button[@type='button']//span[contains(text(),'确认关闭')]")

    # 定向商品开通页-开关记录抽屉
    def test_targeted_product_launch_record(self):
        self._to_targeted_product_launch_page()
        self.click(
            "//div[@class='kwaishop-seller-insurance-pc-pro-checkableTag kwaishop-seller-insurance-pc-pro-checkableTag__default kwaishop-seller-insurance-pc-pro-checkableTag__middle']//span[contains(text(),'已开通')]")
        sleep(1)
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/span/input', '21643183054765')
        self.click("//span[contains(text(),'查 询')]")
        sleep(1)
        self.click(
            '//*[@id="root"]/div/div/div/div/div[3]/div[3]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/button[2]/span')
        sleep(1)
        assert self.is_element_visible("//div[@class='kwaishop-seller-insurance-pc-drawer-title']")
        self.assert_text("商品开关记录", "//div[@class='kwaishop-seller-insurance-pc-drawer-title']")
        assert self.is_element_visible("//th[contains(text(),'变更后状态')]")
        assert self.is_element_visible("//span[contains(text(),'我知道了')]")

    # 补贴活动管理页-跳转
    def test_subsidy_activity_management_page(self):
        self.to_Insurance('huwen', 0)
        self.click("//span[contains(text(),'补贴活动管理')]")
        sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        if self.var1 == 'prt':
            url = 'https://eshop-s.prt.kwaixiaodian.com/zone/insurance/freight/activity-manage'
            assert self.get_current_url() == url, '补贴活动管理页面地址错误，请检查'
        else:
            url = 'https://s.kwaixiaodian.com/zone/insurance/freight/activity-manage'
            assert self.get_current_url() == url, '补贴活动管理页面地址错误，请检查'

    # 补贴活动管理页-元素校验
    def test_subsidy_activity_management_element(self):
        self.to_Insurance('huwen', 0)
        self.click("//span[contains(text(),'补贴活动管理')]")
        sleep(2)
        self.assert_text("已参与补贴活动", "//div[@class='kwaishop-seller-insurance-pc-pro-title-title']")
        assert self.is_element_visible("//div[@title='全部']")
        assert self.is_element_visible("//div[@title='参与中']")
        assert self.is_element_visible("//div[@title='已退出']")
        assert self.is_element_visible("//th[contains(text(),'活动参与状态')]")
        assert self.is_element_visible("//th[contains(text(),'已发放补贴金额(元)')]")

    # 资金模块
    # @unittest.skip
    def test_zijin_qr(self):
        self.to_Insurance()
        # 充值按钮
        self.click(Insurance.zj_recharge)
        if self.var1 == 'prt':
            assert self.driver.current_url == 'https://prt-eshop-s.test.gifshow.com/zone/fund/flow/insurance-payment?mode=ACCOUNT_TYPE_PREMIUM&originURL=/zone/insurance/freight/index', '资金模块-充值按钮跳转链接错误'
        else:
            url_r = 'https://s.kwaixiaodian.com/zone/fund/flow/insurance-payment?mode=ACCOUNT_TYPE_PREMIUM&originURL=/zone/insurance/freight/index'
            assert self.driver.current_url == url_r, '资金模块-充值按钮跳转链接错误'

    def test_zijin_rw(self):
        self.to_Insurance()
        # 提现按钮暂时无法校验
        # self.click(Insurance.zj_withdraw)
        # 充值提现记录
        self.click(Insurance.zj_rwrecord)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 != 'prt':
            assert url == 'https://s.kwaixiaodian.com/zone/fund/flow/insurance-history', '资金模块-充值提现记录按钮跳转链接错误'

    def test_zijin_auto(self):
        self.to_Insurance()
        # 开通自动充值
        self.click(Insurance.zj_autorecharge)
        self.assert_text('自动充值说明', Insurance.zj_auto_title)

    def test_zijin_pt(self):
        self.to_Insurance()
        # 平台补贴账户
        self.assert_text('平台补贴', Insurance.zj_ptbt)

    def test_change_detail(self):
        """
        退货补运费-账户余额-变动明细
        """
        self.to_Insurance()
        self.click("//span[contains(text(),'退货补运费变动明细')]")
        sleep(2)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text('退货补运费变动明细', "//h3[@class='title__content___zCCN7']")

    # 常见问题-收费规则
    @pytest.mark.skip
    def test_common_problems(self):
        # 退货补运费->服务说明
        self.to_Insurance()
        self.click(Insurance.cjwt_btn)
        self.assert_text('常见问题', Insurance.cjwt_title)
        self.click(Insurance.cjwt_url)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 == 'prt':
            assert url == 'https://prt-eshop-s.test.gifshow.com/zone/insurance/freight/category-rules', '按钮【服务说明—收费规则-PC商家版】跳转链接错误'
        else:
            assert url == 'https://s.kwaixiaodian.com/zone/insurance/freight/category-rules', '按钮【服务说明—收费规则-PC商家版】跳转链接错误'

    # 常见问题-服务说明
    def test_common_problems2fwsm(self):
        self.to_Insurance()
        self.click(Insurance.cjwt_btn)
        self.assert_text('常见问题', Insurance.cjwt_title)
        self.click(Insurance.cjwt_fwsm)
        # 跳转到新页面
        self.assert_text('服务说明', Insurance.fwsm_title)

    # 退货补运费->服务说明
    def test_service_desc(self):
        # 退货补运费->服务说明
        self.to_fwsm()
        # 1.1 支持类目
        self.fwsm_subpage(Insurance.fwsm_support_category, '服饰/鞋靴')
        # 1.2 积木链接
        # self.fwsm_subpage(Insurance.fwsm_ppg, '快手电商消费者体验提升计划')
        # 3.2 收费规则
        # self.fwsm_subpage(Insurance.fwsm_sfgz, '99%&lt;N≤100%')
        # 4.4 商友圈
        # self.fwsm_subpage(Insurance.fwsm_syq, '安心钱包功能解读')

    # 退货补运费->列表查询->运费险详情页—>补偿标准->钱款去向
    def test_detail(self):
        self.to_Insurance()
        self.find_order(self.dataSet['pass_highrisk']['oid'])
        # 校验一下高风险标——没有标
        # self.assert_text('高风险率价', Insurance.list_highRiskTag)
        # 退货补运费->运费险详情
        self.assert_text('查看详情', Insurance.list_lookDetail)
        self.click(Insurance.list_lookDetail)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text(self.dataSet['pass_highrisk']['status'], Insurance.detail_status)
        self.assert_text('服务详情', Insurance.detail_fwxq)
        self.assert_text('补偿流程', Insurance.detail_bclc)

        # 补偿标准侧边栏
        self.click(Insurance.bcbz_entrance)
        self.assert_text('退货补运费补偿标准', Insurance.bcbz_title)
        city1, city2 = self.randomcity(), self.randomcity()
        self.type(Insurance.bcbz_input_from, city1)
        self.type(Insurance.bcbz_input_to, city2)
        self.click(Insurance.bcbz_querybtn)
        self.assert_text(city1, Insurance.bcbz_from)
        self.assert_text(city2, Insurance.bcbz_to)
        self.click(Insurance.bcbz_X)

        # 钱款去向侧边栏
        self.click(Insurance.qkqx_entrance)
        self.assert_text('钱款去向', Insurance.qkqx_title)
