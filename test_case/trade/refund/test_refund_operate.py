import logging
import time
from time import sleep

import pytest

from .aftersales_constant import *
from page_objects.refund.refund import RefundListElement
from utils.time_help import timing_decorator
from ..base import BaseTestCase

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 创建控制台处理器并设置级别
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
# 将处理器添加到日志记录器
logger.addHandler(console_handler)

'''
列表页/申请页按钮操作
    拦截快递
    同意退款
    同意拒收后退款
    
    退货退款：
    同意退货
    
    确认收货
    帮买家上传单号
    延长收货 

    换货：
    同意换货

    立即换货
    帮买家上传单号
'''


# 快手小店-售后工作台
class TestRefundOperate(BaseTestCase):
    def setup_class(self):
        self.element = RefundListElement()
        # 这里是订单号
        self.refund_only = kconf_content_pc['refund_only_oid']
        self.refund_and_return = kconf_content_pc['refund_and_return_oid']
        self.refund_and_return_1 = kconf_content_pc['refund_and_return_1_oid']
        self.refund_and_return_2 = kconf_content_pc['refund_and_return_2_oid']  # 买家已回寄，待卖家处理
        self.refund_exchange = kconf_content_pc['refund_exchange_oid']  # 换货待卖家处理申请
        self.refund_exchange_2 = kconf_content_pc['refund_exchange_2_oid']  # 换货二阶段
        #  下边是售后单号
        self.refund_only_id = 2506401709609965
        self.refund_and_return_id = 2515402005610965
        self.refund_and_return_1_id = 2512002164320965
        self.refund_and_return_2_id = 2504902338402965  # 买家已回寄，待卖家处理
        self.refund_exchange_id = 2422000079412477
        self.refund_exchange_2_id = ****************  # 换货二阶段

    def close_refund_detail_popover(self):
        # 详情页处理 -后续在这里处理
        # 关闭协商引导弹层
        sleep(5)
        while self.is_element_visible('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(5)

    def base_refund_only(self, is_detail=False):
        self.to_workbench(account='huwen')
        self.find_order(self.refund_only)
        if is_detail:
            self.click(self.element.refund_detail_btn)
            self.close_refund_detail_popover()

    def base_refund_and_return(self, to_detail=False):
        self.to_workbench(account='huwen')
        self.find_order(self.refund_and_return)
        if to_detail:
            self.click(self.element.refund_and_return_detail)
            self.close_refund_detail_popover()

    def base_refund_and_return_1(self, to_detail=False):
        self.to_workbench(account='huwen')
        self.find_order(self.refund_and_return_1)
        if to_detail:
            self.click(self.element.refund_and_return_2_detail)
            self.close_refund_detail_popover()

    def base_refund_and_return_2(self, to_detail=False):
        self.to_workbench(account='huwen')
        self.find_order(self.refund_and_return_2)
        if to_detail:
            self.click(self.element.refund_and_return_2_detail)
            self.close_refund_detail_popover()

    def base_exchange_goods(self, to_detail=False):
        self.to_workbench(account='huwen')
        self.find_order(self.refund_exchange)
        if to_detail:
            self.click(self.element.refund_and_return_detail)
            self.close_refund_detail_popover()

    def base_exchange_goods2(self, to_detail=False):
        self.to_workbench(account='huwen')
        self.find_order(self.refund_exchange_2)
        if to_detail:
            self.click(self.element.refund_and_return_detail)
            self.close_refund_detail_popover()

    @timing_decorator
    def test_refund_only_agree(self):
        """
        售后列表页-仅退款-同意退款
        """
        self.base_refund_only()
        self.click("//button[@id='AGREE_REFUND']//span[contains(text(),'同意退款')]")
        sleep(2)
        self.assert_text('同意退款', self.element.popup_title)
        content = '退款金额￥0.01元，确认后不可撤销，将在1-3个工作日内完成退款。'
        self.assert_text(content, self.element.popup_content)
        logger.info("case over" + str(time.time()))
        # 点击关闭弹窗
        # self.click(self.element.popup_close)

    @timing_decorator
    def test_refund_intercept_logistics(self):
        """
        售后列表页-仅退款-拦截快递
        """
        self.base_refund_only()
        self.click(self.element.intercept_logistics)
        sleep(2)
        try:
            self.assert_text('拦截快递', self.element.popup_title)
            content = ('1. 操作后，进行中的协商方案将会失效。\n'
                       '2. 拦截时间共5天，拦截成功系统自动退款，拦截失败，还请您在倒计时内及时处理，否则超时系统自动同意退款\n'
                       '3. 快递拦截费用，需要您和快递公司线下结算。')
            self.assert_text(content, self.element.popup_content)
        except:
            try:
                print('置灰的情况要看下')
            except:
                raise Exception('点击按钮与预期不符')

    @timing_decorator
    def test_refund_agree_refuse_delivery(self):
        """
        售后列表页-仅退款-同意拒收后退款
        """
        self.base_refund_only()
        self.click(self.element.agree_refuse_delivery)
        sleep(2)
        try:
            self.assert_text('同意拒收后退款', self.element.popup_title)
            content = ('1. 操作后，进行中的协商方案将会失效。\n'
                       '2. 操作后，需要您在5天内主动拦截物流并及时关注买家拒收信息，如您超时未处理系统将自动同意退款。\n'
                       '3. 系统会监测物流状态，识别到买家成功拒收信息后将自动同意退款。 风险提示\n'
                       '4. 处理时间将计入店铺分考核 点击查看考核规则\n'
                       '5. 平台推荐您开通拒收自动退策略，降低售后处理时长，如您的账号已有【售后-退款设置】权限， 点击去开通')
            self.assert_text(content, self.element.popup_content)
            self.click("//a[contains(text(),'点击去开通')]")
            sleep(3)
            handles = self.driver.window_handles
            self.driver.switch_to.window(handles[-1])
            url = self.get_current_url()
            assert url == 'https://s.kwaixiaodian.com/zone/refund/auto-refund/strategy-select', 'url不符合预期，请检查'
        except:
            try:
                self.click(self.element.agree_refuse_delivery)
                self.wait_for_element(self.element.transport_stagnation)
                # toast_text = '运输中停滞'
                # self.assert_text(toast_text, self.element.toast)
            except:
                raise Exception('点击按钮与预期不符')

    @timing_decorator
    def test_refund_and_return_agree(self):
        """
        售后列表页-退货退款一阶段-同意退货
        """
        self.base_refund_and_return()
        self.click(self.element.agree_refund_return)
        sleep(2)
        self.assert_text('同意退货', self.element.popup_title)
        self.assert_text('退货地址', self.element.return_goods_address)

    @timing_decorator
    def test_refund_confirm_receive(self):
        """
        售后列表页-退货退款一阶段待买家回寄-确认收货
        """
        operate_name = '确认收货'
        content = '买家还未上传退货单号，确认收货后退货补运费不会理赔，退款金额0.01元将退款给买家，建议您先帮买家上传单号。确认收货吗？'
        self.base_refund_and_return_1()
        self.click(self.element.confirm_receive_list)
        sleep(2)
        self.assert_text(operate_name, self.element.confirm_receive_popup_title)
        # 校验弹窗内容
        self.assert_text(content, self.element.confirm_receive_popup_content)

    @timing_decorator
    def test_refund_submit_return_info(self):
        """
        售后列表页-退货退款一阶段待买家回寄-帮买家上传单号
        """
        operate_name = '帮买家上传单号'
        self.base_refund_and_return_1()
        self.click(self.element.help_buyer_upload_number)
        sleep(2)
        self.assert_text(operate_name, self.element.popup_title)
        # 校验弹窗字段
        self.assert_text('退货单号', self.element.return_goods_number)
        self.assert_text('快递公司', self.element.logistics_company_1)

    @timing_decorator
    def test_refund_confirm_receive_2(self):
        """
        售后列表页-退货退款二阶段待卖家处理-确认收货
        """
        operate_name = '确认收货'
        content = '确认收货后退款金额￥0.01将退至买家账户。确认收货吗？'
        self.base_refund_and_return_2()
        self.click(self.element.confirm_receive_list)
        sleep(2)
        self.assert_text(operate_name, self.element.confirm_receive_popup_title)
        # 校验弹窗内容
        self.assert_text(content, self.element.confirm_receive_popup_content)

    # @skipIf(get_env() == "prt", "线上不运行")
    # def test_refund_extend_confirm_receipt(self):
    #     """
    #     售后列表页-退货退款二阶段-延长收货
    #     只有prt可满足条件
    #     """
    #     # operate_name = '延长收货'
    #     # driver = webdriver.Chrome()
    #     self.to_workbech()
    #     self.find_order(self.refund_and_return_extend_receipt)
    # options = get_driver().create_options()
    # options.add_argument("trace-context={'laneId':'PRT.yctest'}")
    # self.open('https://eshop-s.prt.kwaixiaodian.com/zone/refund/refund-workbench/index')
    # self.assert_text(operate_name, self.element.help_buyer_shipped_text)
    # self.click(self.element.help_buyer_shipped_btn)
    # sleep(2)
    # self.assert_text(operate_name, self.element.popup_title)
    # # 校验弹窗字段
    # content = '您可以延长一次收货时间，支持延长3天，请在延长后的时间内尽快完成售后处理，否则超时系统将自动为用户退款'
    @timing_decorator
    def test_refund_exchange_goods(self):
        """
        售后列表页-换货一阶段-同意换货
        """
        self.base_exchange_goods()
        self.click(self.element.exchange_goods_agree)
        sleep(2)
        self.assert_text('同意换货', self.element.popup_title)
        self.assert_text('退货地址', self.element.return_goods_address)

    @timing_decorator
    def test_refund_confirm_exchange(self):
        """
        售后列表页-换货二阶段-立即换货
        """
        operate_name = '立即换货'
        self.base_exchange_goods2()
        self.click(self.element.exchange_goods_immediately)
        sleep(2)
        self.assert_text(operate_name, self.element.popup_title)
        # 校验弹窗字段
        self.assert_text('物流单号', self.element.logistics_id)
        self.assert_text('物流公司', self.element.logistics_company)

    @timing_decorator
    def test_refund_exchange_submit_return_info(self):
        """
        售后列表页-换货二阶段-帮买家上传单号
        """
        operate_name = '帮买家上传单号'
        self.base_exchange_goods2()
        self.assert_text(operate_name, self.element.help_buyer_upload_number)
        self.click(self.element.help_buyer_upload_number)
        sleep(2)
        self.assert_text(operate_name, self.element.popup_title)
        # 校验弹窗字段
        self.assert_text('退货单号', self.element.return_goods_number)
        self.assert_text('快递公司', self.element.logistics_company_1)

    @timing_decorator
    def test_refund_workbench_batch_refund(self):
        """
        售后单管理-批量同意退款/退货
        """
        self.base_refund_only()
        # 点击勾选和操作
        self.click(self.element.select_first)
        text = '批量同意退款/退货'
        sleep(1)
        self.click(self.element.batch_agree_refund)
        sleep(2)
        # todo 校验弹窗
        self.assert_text(text, self.element.popup_title)
        # self.assert_text("预估退款金额¥0.01元","//body/div/div[@class='ant-modal-root']/div[@role='dialog']/div[@role='document']/div[@class='ant-modal-content']/div[@class='ant-modal-body']/div[@class='OBpuYBQ0jo6op6bBRKL7']/div[2]/span[1]")
        # assert self.is_element_visible("//div[@class='ant-modal-root']//div[3]//div[1]')]"), '退款单号不存在，请检查'
        # a = self.get_text("/html/body/div[9]/div/div[2]/div/div[2]/div[2]/div[3]/div[1]")
        # print(a)
        # # //body/div/div[@class='ant-modal-root']/div[@role='dialog']/div[@role='document']/div[@class='ant-modal-content']/div[@class='ant-modal-body']/div[@class='ant-row']/div[2]
        # assert self.is_element_visible("//div[@class='ant-modal-root']//div[3]//div[2]"), '订单号不存在，请检查'
        # assert self.is_element_visible("//div[@class='ant-modal-root']//div[3]//div[3]"), '退款金额不存在，请检查'
        # assert self.is_element_visible("//div[@class='ant-modal-root']//div[3]//div[4]"), '售后类型不存在，请检查'
        # assert self.is_element_visible("//div[@class='ant-col ant-col-4 hMTW1di3lyU05XGLwzdb'][contains(text(),'买家昵称')]"), '买家昵称不存在，请检查'
        # assert self.is_element_visible("//div[@class='ant-modal-root']//div[3]//div[6]"), '处理状态不存在，请检查'

        self.click("(//a[contains(text(),'点击去开通')])[1]")

        sleep(4)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        if self.var1 == 'prt':
            assert self.get_current_url() == 'https://eshop-s.prt.kwaixiaodian.com/zone/refund/auto-refund/strategy-select', '跳转策略页面不符合预期，请检查'
        else:
            url = 'https://s.kwaixiaodian.com/zone/refund/auto-refund/strategy-select'
            assert self.get_current_url() == url, '跳转策略页面不符合预期，请检查'

    @timing_decorator
    # 批量确认收货
    def test_refund_workbench_batch_confirm_receipt(self):
        """
        售后单管理-批量确认收货
        """
        self.base_refund_and_return_1()
        self.click(self.element.select_first)
        text = '批量确认收货'
        sleep(1)
        self.click(self.element.batch_confirm_receipt)
        sleep(2)
        self.assert_text(text, self.element.popup_title)

    @timing_decorator
    def test_refund_workbench_remarks(self):
        """
        售后单管理-备注信息
        """
        self.base_refund_and_return_1()
        self.click(self.element.remarks_button)
        sleep(2)
        text = '标记与备注\n订单标记与备注由商家添加，仅平台客服和商家可见'
        self.assert_text(text, self.element.remarks_popup)
        self.assert_text('订单标记：', self.element.remarks_order_mark)

    @timing_decorator
    def test_refund_only_agree_detail(self):
        """
        售后详情页-仅退款
        """
        text = '同意退款'
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.click(self.element.agree_refund)
        sleep(2)
        self.assert_text(text, self.element.popup_title)
        content = '退款金额￥0.01元，确认后不可撤销，将在1-3个工作日内完成退款。'
        self.assert_text(content, self.element.detail_popup_title)

    @timing_decorator
    def test_refund_intercept_logistics_detail(self):
        """
        售后详情页-仅退款-拦截快递
        """
        text = '拦截快递'
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        try:
            self.click(self.element.intercept_logistics)
            sleep(1)
            self.assert_text(text, self.element.popup_title)
        except:
            try:
                self.assert_text(text, self.element.intercept_logistics_ash_text)
                self.hover_on_element(self.element.intercept_logistics_ash_button)
                # toast_text = '当前物流公司不支持快递拦截服务'
                # self.assert_text(toast_text, self.element.detail_toast)
            except:
                raise Exception('操作按钮不符合预期')

    @timing_decorator
    def test_refund_agree_refuse_delivery_detail(self):
        """
        售后详情页-仅退款-同意拒收后退款
        """
        text = '同意拒收后退款'
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        try:
            self.click(self.element.agree_refuse_delivery)
            sleep(1)
            self.assert_text(text, self.element.popup_title)
        except:
            try:
                self.assert_text(text, self.element.agree_refuse_delivery_ash_text)
                self.hover_on_element(self.element.agree_refuse_delivery_ash_button)
                toast_text = '运输中停滞'
                self.assert_text(toast_text, "//div[@role='tooltip']")
            except:
                raise Exception('操作按钮不符合预期')

    @timing_decorator
    def test_refund_more_detail(self):
        """
        售后详情页-仅退款-更多-拒绝退款：未命中强制协商
        """
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.click(self.element.more_btn)
        sleep(2)
        self.click("//span[contains(text(),'拒绝退款')]")
        sleep(2)
        # 弹窗校验,限制拒绝后推荐方案抽屉展示
        self.assert_text('拒绝售后', self.element.reject_plan_title)
        self.assert_text('拒绝建议', self.element.reject_plan_suggestion)
        reject_message = '无故拒绝会影响售后服务分，建议优先与买家协商售后或使用商家工具'
        self.assert_text(reject_message, self.element.reject_plan_message)
        self.assert_text('售后处理推荐方案', self.element.reject_refund_recommend_plan_text)
        # self.assert_text('拦截快递', "//div[@class='ant-space-item']//span[contains(text(),'拦截快递')]")
        self.assert_text('协商修改售后', "//div[@class='ant-space-item']//span[contains(text(),'协商修改售后')]")
        self.assert_text('取消', "//span[contains(text(),'取消')]")
        self.click("//span[contains(text(),'仍拒绝')]")
        sleep(2)
        self.assert_text("确认拒绝退款吗？", self.element.detail_reject_tips)
        assert self.is_element_visible(
            "//p[contains(text(),'平台将全程监测您的售后行为，请在与买家协商一致后操作，切勿直接拒绝退款。若产生不合理拒绝，平台将视情')]"), '弹窗说明不符合预期'
        assert self.is_element_visible("//span[contains(text(),'确认拒绝')]")

    def test_refund_only_cannot_reject(self):
        """
        售后详情页-仅退款-更多-拒绝退款：命中强制协商
        """
        self.to_refund_detail_new(refund_id=2506902248043965)
        self.click(self.element.more_btn)
        sleep(2)
        self.click("//span[contains(text(),'拒绝退款')]")
        sleep(2)
        # 弹窗校验,限制拒绝后推荐方案抽屉展示
        self.assert_text('拒绝售后', self.element.reject_plan_title)
        self.assert_text('拒绝建议', self.element.reject_plan_suggestion)
        reject_message = '非特殊类目/物流未签收不支持拒绝退款，您可以使用“同意拒收后退款”或“快递拦截”。查看 《未收到货仅退款处理规则》'
        self.assert_text(reject_message, self.element.reject_plan_message)
        self.assert_text('售后处理推荐方案', self.element.reject_refund_recommend_plan_text)
        self.assert_text('拦截快递', "//div[@class='ant-space-item']//span[contains(text(),'拦截快递')]")
        self.assert_text('协商修改售后', "//div[@class='ant-space-item']//span[contains(text(),'协商修改售后')]")
        self.assert_text('我知道了', "//span[contains(text(),'我知道了')]")
        self.click("//span[contains(text(),'我知道了')]")
        sleep(1)
        assert not self.is_element_visible(self.element.reject_plan_title)

    def test_platform_promises_to_intercept(self):
        """
        平台承诺拦截
        """
        refund_id = 2506902248011965
        self.to_refund_detail_new(refund_id)
        assert self.is_element_visible(
            "//div[contains(text(),'平台已成功向快递公司发起物流拦截，自动同意退款。若商品退回失败（丢失&损坏&买家签收），请您及时联系')]")
        # todo 不确定为啥没有按钮了
        # self.click("//a[contains(text(),'去申诉')]")
        # self.assert_text("不可申诉说明", "//span[@class='ant-modal-confirm-title']")
        # assert self.is_element_visible("//span[contains(text(),'当前售后单不支持申诉，因已超出可申诉时间')]")
        # assert self.is_element_visible("//span[contains(text(),'知道了')]")

        # cur_url = self.get_new_page_url()
        # path = f'zone/appeal/home?afterSaleNo={refund_id}&source=21&scene=5&status=0'
        # assert is_correct_url(path, cur_url, self.var1)

    @timing_decorator
    def test_refund_only_negotiate_detail(self):
        """
        售后详情页-仅退款-协商修改售后
        """
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.assert_text('协商修改售后', self.element.detail_negotiate_btn)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('协商方案设置', self.element.negotiate_popup_title)

    @timing_decorator
    def test_refund_and_return_agree_detail(self):
        """
        售后详情页-退货退款一阶段-同意退货
        """
        text = '同意退货'
        self.to_refund_detail_new(refund_id=self.refund_and_return_id)
        self.click(self.element.agree_refund_return)
        self.assert_text(text, self.element.popup_title)
        self.assert_text('退货地址', self.element.return_goods_address)

    @timing_decorator
    def test_refund_and_return_negotiate_detail(self):
        """
        售后详情页-退货退款一阶段-协商修改售后
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_id)
        self.assert_text('协商修改售后', self.element.detail_negotiate_btn)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('协商方案设置', self.element.negotiate_popup_title)

    @timing_decorator
    def test_reject_refund_and_return_detail(self):
        """
        售后详情页-退货退款一阶段-拒绝退货退款
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_id)
        self.click(self.element.more_btn)
        sleep(1)
        self.click(self.element.reject_refund_and_return)
        sleep(2)

        # 拒绝推荐方案抽屉
        self.assert_text('拒绝售后', self.element.reject_plan_title)
        self.assert_text('拒绝建议', self.element.reject_plan_suggestion)
        reject_message = '为避免产生不必要的纠纷，请您及时点击下方“协商修改售后”按钮，联系买家进行协商处理。查看 《协商期说明》'
        self.assert_text(reject_message, self.element.reject_plan_message)
        self.assert_text('我知道了', self.element.detail_i_know)

        # 命中强制协商
        self.click("//div[@class='ant-space-item']//span[contains(text(),'协商修改售后')]")
        sleep(1)
        self.assert_text('协商方案设置', self.element.negotiate_popup_title)

        #  拒绝弹窗-未命中强制协商时
        # self.click("//form[@class='ant-form ant-form-horizontal']//button[1]")
        # sleep(2)
        # self.assert_text('确认拒绝退款吗？', self.element.detail_reject_tips)
        # self.assert_text('取 消', self.element.cancel)
        # self.click(self.element.confirm_reject)
        # sleep(2)
        #
        # # 拒绝抽屉
        # self.assert_text('拒绝原因', self.element.reject_reason)
        # self.assert_text('拒绝说明', self.element.reject_explain)
        # self.assert_text('上传凭证', self.element.upload_evidence)

    @timing_decorator
    def test_apply_for_platform_detail(self):
        """
        售后详情页-退货退款一阶段-申请平台介入
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_id)
        self.click(self.element.apply_for_platform)
        sleep(2)
        self.assert_text('温馨提示', self.element.detail_kid_tips)
        assert self.is_element_visible(
            "//div[contains(text(),'请先处理买家的退款申请。若未协商一致，您可以先操作拒绝，待买家修改申请后，您再申请平台介入处理。')]")
        self.click(self.element.detail_i_know)

    @timing_decorator
    def test_check_order_info_detail(self):
        """
        售后详情页-退货退款一阶段-查看订单详情
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_id)
        self.click(self.element.check_order_info)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 == 'prt':
            assert f'https://eshop-s.prt.kwaixiaodian.com/zone/order/detail?id=' in url, 'url不符合预期，请检查'
        else:
            assert f'https://s.kwaixiaodian.com/zone/order/detail?id=' in url, 'url不符合预期，请检查'

    @timing_decorator
    def test_refund_confirm_receive_detail(self):
        """
        售后详情页-退货退款一阶段待买家回寄-确认收货
        """
        text = '确认收货'
        self.to_refund_detail_new(refund_id=self.refund_and_return_1_id)
        self.click(self.element.confirm_receive)
        sleep(2)
        self.assert_text(text, self.element.popup_title)
        self.click(self.element.cancel)

    @timing_decorator
    def test_refund_submit_return_info_detail(self):
        """
        售后详情页-退货退款一阶段待买家回寄-帮买家上传单号
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_1_id)
        operate_name = '帮买家上传单号'
        self.click(self.element.help_buyer_upload_number)
        sleep(2)
        self.assert_text(operate_name, self.element.popup_title)
        # 校验弹窗字段
        self.assert_text('退货单号', self.element.return_goods_number)
        self.assert_text('快递公司', self.element.logistics_company_1)

    @timing_decorator
    def test_refund_and_return_2_negotiate_detail(self):
        """
        售后详情页-退货退款二阶段-协商修改售后
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_2_id)
        self.assert_text('协商修改售后', self.element.detail_negotiate_btn)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('协商方案设置', self.element.negotiate_popup_title)

    @timing_decorator
    def test_refund_and_return_confirm_receive_detail(self):
        """
        售后详情页-退货退款二阶段-确认收货
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_2_id)
        text = '确认收货'
        self.click(self.element.confirm_receive)
        sleep(2)
        self.assert_text(text, self.element.popup_title)

    def test_seller_detail_seller_duty_agree_and_refuse(self):
        """
        无运费险卖家可拒绝退货运费
        """
        self.to_refund_detail_new(refund_id=seller_duty_refund_id)
        self.click(self.element.confirm_receive)
        sleep(2)
        # 确认收货半屏校验
        self.assert_text('确认收货', self.element.popup_title)
        assert self.is_element_visible(self.element.tips_seller_duty), 'tips不符合预期'
        self.assert_text("运费意见", self.element.text_freight_advice)
        self.assert_text("确认收货，预计退还买家 25.01 元", self.element.text_agree_confirm_receive)
        assert self.is_element_visible(self.element.btn_agree_freight), '同意退运费按钮不存在'
        self.click(self.element.btn_not_agree_freight)
        sleep(3)
        self.assert_text('请务必和买家达成一致后操作，否则可能面临平台罚单和扣款。', self.element.tips_not_agree)
        assert self.is_element_visible(self.element.label_explain), '说明不存在'
        assert self.is_element_visible(self.element.label_upload_evidence), '上传凭证不存在'
        self.assert_text("确认收货，预计退还买家 0.01 元", self.element.text_agree_confirm_receive)
        assert self.is_element_visible(self.element.cancel)
        assert self.is_element_visible(self.element.confirm)

    def test_seller_detail_seller_duty_agree_and_edit(self):
        """
        有运费险卖家可修改退货邮费
        """
        self.to_refund_detail_new(refund_id=seller_duty_edit_refund_id)
        self.click(self.element.confirm_receive)
        sleep(2)
        # 确认收货半屏校验
        self.assert_text('确认收货', self.element.popup_title)
        assert self.is_element_visible(self.element.tips_seller_duty), 'tips不符合预期'
        self.assert_text("运费意见", self.element.text_freight_advice)
        self.assert_text("确认收货，预计退还买家 15.80 元", self.element.text_agree_confirm_receive)
        assert self.is_element_visible(self.element.btn_agree_freight), '同意退运费按钮不存在'
        self.click(self.element.btn_agree_with_edit_freight)
        sleep(3)
        self.assert_text('请务必和买家达成一致后操作，否则可能面临平台罚单和扣款。', self.element.tips_not_agree)
        assert self.is_element_visible(self.element.label_edit_money), '修改金额不存在'
        assert self.is_element_visible(self.element.label_explain), '说明不存在'
        assert self.is_element_visible(self.element.label_upload_evidence), '上传凭证不存在'
        self.type("//input[@id='modifyFee']", 5)
        sleep(1)
        self.assert_text("确认收货，预计退还买家 5.01 元", self.element.text_agree_confirm_receive)
        assert self.is_element_visible(self.element.cancel)
        assert self.is_element_visible(self.element.confirm)

    @timing_decorator
    def test_refund_and_return_extend_receive_detail(self):
        """
        售后详情页-退货退款二阶段-延长收货
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_2_id)
        text = '延长收货'
        self.assert_text(text, self.element.extend_receive_btn)

    @timing_decorator
    def test_refund_and_retail_reject_apply_detail(self):
        """
        售后详情页-退货退款二阶段-驳回申请
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_2_id)
        self.click(self.element.reject_application)
        sleep(2)
        self.assert_text('拒绝售后', self.element.reject_plan_title)
        self.assert_text('拒绝建议', self.element.reject_plan_suggestion)
        reject_message = '无故拒绝会影响售后服务分，建议优先与买家协商售后'
        self.assert_text(reject_message, self.element.reject_plan_message)
        self.assert_text('协商修改售后', "//div[@class='ant-space-item']//span[contains(text(),'协商修改售后')]")
        self.assert_text('取消', "//span[contains(text(),'取消')]")
        self.click("//span[contains(text(),'仍拒绝')]")
        sleep(2)
        title = '确认驳回申请吗？'
        self.assert_text(title, self.element.detail_reject_tips)
        self.click("//span[contains(text(),'确认驳回')]")
        sleep(2)
        self.assert_text("驳回申请", "//div[@class='ant-drawer-title'][contains(text(),'驳回申请')]")
        assert self.is_element_visible("//label[@title='驳回原因']")
        assert self.is_element_visible("//label[@title='驳回说明']")
        assert self.is_element_visible("//label[@title='上传凭证']")

    @timing_decorator
    def test_refund_and_retail_apply_dispute_detail(self):
        """
        售后详情页-退货退款二阶段-申请平台介入
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_2_id)
        self.click(self.element.more_btn)
        time.sleep(2)
        self.click("//div[@class='button___FhW9l']")
        sleep(2)
        self.assert_text('温馨提示', "//div[@class='title___kHJ6i']")
        assert self.is_element_visible(
            "//div[contains(text(),'请先处理买家的退款申请。若未协商一致，您可以先操作拒绝，待买家修改申请后，您再申请平台介入处理。')]")
        self.click(self.element.detail_i_know)

    @timing_decorator
    def test_refund_exchange_goods_detail(self):
        """
        售后详情页-换货一阶段-同意换货
        """
        context = '同意换货'
        self.to_refund_detail_new(refund_id=self.refund_exchange_id)
        self.click(self.element.exchange_goods_agree)
        sleep(2)
        self.assert_text(context, self.element.popup_title)

    @timing_decorator
    def test_refund_exchange_goods_reject_detail(self):
        """
        售后详情页-换货一阶段-拒绝换货
        """
        self.to_refund_detail_new(refund_id=self.refund_exchange_id)
        self.click(self.element.more_btn)
        sleep(1)
        self.click(self.element.exchange_goods_refuse)
        sleep(2)
        # 方案抽屉
        self.assert_text('拒绝售后', self.element.reject_plan_title)
        self.assert_text('拒绝建议', self.element.reject_plan_suggestion)
        reject_message = '无故拒绝会影响售后服务分，建议优先与买家协商售后或使用商家工具'
        self.assert_text(reject_message, self.element.reject_plan_message)
        self.click("//span[contains(text(),'仍拒绝')]")
        sleep(1)
        self.assert_text('确认拒绝退款吗？', self.element.detail_reject_tips)
        self.click(self.element.confirm_reject)
        sleep(2)
        self.assert_text('拒绝原因', self.element.reject_reason)
        self.assert_text('拒绝说明', self.element.reject_explain)
        self.assert_text('上传凭证', self.element.upload_evidence)

    @timing_decorator
    def test_refund_exchange_goods_to_refund_only_detail(self):
        """
        售后详情页-换货一阶段-直接退款
        """
        context = '直接退款'
        self.to_refund_detail_new(refund_id=self.refund_exchange_id)
        self.click(self.element.direct_refund)
        sleep(2)
        self.assert_text(context, self.element.popup_title)

    # 换货一阶段:申请平台介入-查看订单详情
    @timing_decorator
    def test_refund_confirm_exchange_detail(self):
        """
        售后详情页-换货二阶段-立即换货
        """
        context = '立即换货'
        self.to_refund_detail_new(refund_id=self.refund_exchange_2_id)
        self.click(self.element.exchange_goods_immediately)
        sleep(2)
        # 校验弹窗字段
        self.assert_text(context, self.element.popup_title)
        self.assert_text('物流单号', self.element.logistics_id)
        self.assert_text('物流公司', self.element.logistics_company)

    @timing_decorator
    def test_refund_exchange_submit_return_info_detail(self):
        """
        售后详情页-换货二阶段-帮买家上传单号
        """
        context = '帮买家上传单号'
        self.to_refund_detail_new(refund_id=self.refund_exchange_2_id)
        self.click(self.element.help_buyer_upload_number)
        sleep(2)
        self.assert_text(context, self.element.popup_title)
        # 校验弹窗字段
        self.assert_text('退货单号', self.element.return_goods_number)
        self.assert_text('快递公司', self.element.logistics_company_1)

    @timing_decorator
    def test_refund_exchange_goods2_to_refund_only_detail(self):
        """
        售后详情页-换货二阶段-直接退款
        """
        context = '直接退款'
        self.to_refund_detail_new(refund_id=self.refund_exchange_2_id)
        self.click(self.element.exchange_goods_to_refund)
        sleep(2)
        self.assert_text(context, self.element.popup_title)

    def test_exchange_stage_two_seller_confirm_receipt(self):
        """
        换货买家寄出后-元素校验
        """
        self.to_refund_detail_new(refund_id=exchange_stage_two_seller_confirm_receipt)
        assert self.is_element_visible(self.element.exchange_goods_immediately), '立即换货按钮不存在'
        assert self.is_element_visible(self.element.exchange_reject), '拒绝按钮不存在'
        assert self.is_element_visible(self.element.exchange_goods_to_refund), '直接退款按钮不存在'
        assert self.is_element_visible(self.element.apply_for_platform), '申请平台介入按钮不存在'
        assert self.is_element_visible(self.element.check_order_info), '查看订单详情按钮不存在'
        assert self.get_text(self.element.exchange_extend) == '延长收货', '延长收货按钮不存在'

    def test_exchange_reject_apply(self):
        """
        换货买家寄出后-拒绝弹窗和抽屉校验
        """
        self.to_refund_detail_new(refund_id=exchange_stage_two_seller_confirm_receipt)
        self.click(self.element.exchange_reject)
        sleep(2)
        # 弹窗校验
        self.assert_text('确认驳回申请吗？', self.element.detail_reject_tips)
        assert self.is_element_visible(
            "//p[contains(text(),'您驳回后，买家可以要求快手介入处理。如果快手核实是您的问题，将影响您店铺的纠纷退款率。驳回代表同时驳')]"), '说明文案不符合预期'
        # 拒绝抽屉
        self.click(self.element.exchange_confirm_reject)
        sleep(2)
        self.assert_text('拒绝', self.element.reject_title)
        assert self.is_element_visible("//label[@title='驳回原因']")
        assert self.is_element_visible("//label[@title='驳回说明']")
        assert self.is_element_visible("//label[@title='上传凭证']")
        assert self.is_element_visible("//div[@class='ant-space-item']//span[contains(text(),'取 消')]")
        assert self.is_element_visible(self.element.confirm)
        # self.click("//input[@id='rc_select_2']")
        # sleep(1)
        # self.click("//div[@title='与您协商商品不做换货']//div[1]")

    def test_exchange_apply_platform(self):
        """
        换货买家寄出后-平台介入弹窗
        """
        self.to_refund_detail_new(refund_id=exchange_stage_two_seller_confirm_receipt)
        self.click(self.element.apply_for_platform)
        sleep(2)
        self.assert_text('温馨提示', self.element.detail_kid_tips)
        self.click(self.element.detail_i_know)

    def test_exchange_stage_one_seller_refuse(self):
        """
        卖家不同意换货申请-元素校验
        """
        self.to_refund_detail_new(refund_id=exchange_stage_one_seller_refuse)
        assert self.is_element_visible("//span[contains(text(),'卖家拒绝换货，等待买家响应')]"), '状态描述不正确'
        assert self.is_element_visible(self.element.exchange_goods_agree), '同意换货按钮不存在'
        assert self.is_element_visible(self.element.exchange_goods_to_refund), '直接退款按钮不存在'
        assert self.is_element_visible(self.element.check_order_info), '查看订单详情按钮不存在'

    def test_exchange_stage_two_buyer_confirm_receipt(self):
        """
        卖家已发出换货-元素校验
        """
        self.to_refund_detail_new(refund_id=exchange_stage_two_buyer_confirm_receipt)
        assert self.is_element_visible("//span[contains(text(),'卖家已发出换货商品，待买家收货')]"), '状态描述不正确'
        assert self.is_element_visible(self.element.check_order_info), '查看订单详情按钮不存在'

    def test_exchange_stage_two_seller_refuse(self):
        """
        卖家驳回换货（买家已回寄时）-元素校验
        """
        self.to_refund_detail_new(refund_id=exchange_stage_two_seller_refuse)
        assert self.is_element_visible("//span[contains(text(),'卖家驳回换货申请，等待买家响应')]"), '状态描述不正确'
        assert self.is_element_visible(self.element.exchange_goods_immediately), '立即换货按钮不存在'
        assert self.is_element_visible(self.element.exchange_goods_to_refund), '直接退款按钮不存在'
        assert self.is_element_visible(self.element.check_order_info), '查看订单详情按钮不存在'

    @timing_decorator
    def test_refund_negotiate_detail(self):
        """
        售后协商板块展示
        """
        self.to_refund_detail_new(refund_id="2422200105802477")
        self.close_refund_detail_popover()
        self.assert_text('协商方案', self.element.negotiate_title)
        self.assert_text('买家确认中', self.element.negotiate_status)
        self.assert_text('协商类型', self.element.negotiate_type)
        self.assert_text('售后信息修改', self.element.negotiate_type_vale)
        self.assert_text('售后类型', self.element.negotiate_refund_type)
        self.assert_text('仅退款（原退货退款）', self.element.negotiate_refund_type_value)
        self.assert_text('退款金额', self.element.negotiate_refund_money)
        self.assert_text('￥0.01（无改动）', self.element.negotiate_refund_money_value)
        self.assert_text('货物状态', self.element.negotiate_shipped_status)
        self.assert_text('买家已收到货（无改动）', self.element.negotiate_shipped_status_value)
        self.assert_text('售后原因', self.element.negotiate_refund_reason)
        self.assert_text('买卖双方协商一致退款（原其他）', self.element.negotiate_refund_reason_value)

    @timing_decorator
    def test_negotiate_rule_description(self):
        """
        售后详情页-协商弹层-说明及跳转
        """
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.assert_text('协商修改售后', self.element.detail_negotiate_btn)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('协商工具说明', self.element.negotiate_rule_description)
        self.click(self.element.negotiate_rule_description)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert 'https://docs.qingque.cn/d/home/<USER>' == url, 'url不符合预期，请检查'

    @timing_decorator
    def test_modify_negotiate_button(self):
        """
        售后详情页-修改协商方案-跳转,不展示补充凭证
        """
        self.to_refund_detail_new(refund_id="2422200105802477")
        sleep(2)
        self.close_refund_detail_popover()
        self.click(self.element.detail_modify_negotiate_btn)
        sleep(2)
        self.assert_text('协商方案设置', self.element.negotiate_popup_title)
        if self.is_element_visible(self.element.negotiate_popup_2) or self.is_element_visible(self.element.negotiate_popup_3):
            raise Exception('修改协商方案的弹层不应有有补充凭证或下发话术的tab')

    @timing_decorator
    def test_negotiate_update_refund_info(self):
        """
        售后详情页-协商修改售后-修改售后信息
        """
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.assert_text('协商修改售后', self.element.detail_negotiate_btn)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('售后信息修改', self.element.negotiate_popup)
        self.assert_text('售后类型', self.element.negotiate_set_refund_type)
        self.assert_text('货物状态', self.element.negotiate_set_shipped_status)
        self.assert_text('退款原因', self.element.negotiate_set_refund_reason)
        self.assert_text('退款金额', self.element.negotiate_set_refund_fee)
        self.assert_text('协商方案', self.element.negotiate_set_refund_suggestion_remark)

    @timing_decorator
    def test_negotiate_supplement_info(self):
        """
        售后详情页-协商修改售后-邀请用户补充信息
        """
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('邀请用户补充信息', self.element.negotiate_popup_2)
        self.click(self.element.negotiate_popup_2)
        sleep(2)
        self.assert_text('补充场景', self.element.negotiate_supplement_type)
        self.assert_text('补充售后凭证', self.element.negotiate_supplement_type_value1)

    @timing_decorator
    def test_negotiate_update_logistics(self):
        """
        售后详情页-协商修改售后-修改物流信息
        """
        self.to_refund_detail_new(refund_id=self.refund_and_return_2_id)
        self.assert_text('协商修改售后', self.element.detail_negotiate_btn)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('邀请用户补充信息', self.element.negotiate_popup_2)
        self.click(self.element.negotiate_popup_2)
        sleep(2)
        self.assert_text('补充场景', self.element.negotiate_supplement_type)
        self.assert_text('核对/变更退货物流信息', self.element.negotiate_supplement_type_value2)
        self.assert_text('协商方案', self.element.negotiate_plan)

    @timing_decorator
    def test_negotiate_send_suggestion(self):
        """
        售后详情页-协商修改售后-推荐协商话术
        """
        self.to_refund_detail_new(refund_id=self.refund_only_id)
        self.click(self.element.detail_negotiate_btn)
        sleep(2)
        self.assert_text('推荐协商话术', self.element.negotiate_popup_3)
        self.click(self.element.negotiate_popup_3)
        sleep(2)
        self.assert_text('协商场景', self.element.negotiate_scene)
        self.assert_text('协商方案', self.element.negotiate_plan)


if __name__ == '__main__':
    test = TestRefundOperate()
