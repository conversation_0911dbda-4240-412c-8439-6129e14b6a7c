from time import sleep

from ddt import ddt

from ..base import BaseTestCase


# 售后详情页
@ddt
class TestRefundDetail(BaseTestCase):
    def setup_class(self):
        self.order_id = ****************
        self.refund_id = ****************
        self.gift_order_id = ****************

    # 售后标题
    def test_refund_title(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.assert_text('退款已到账', '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/span[1]')

    # 售后服务规则
    def test_refund_service_rule(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        refund_service_rule = "//a[contains(text(),'售后服务规则')]"
        self.assert_text('售后服务规则', refund_service_rule)
        self.click(refund_service_rule)
        sleep(2)
        # self.assert_text('快手小店售后服务管理规则', "//span[@class='detail-title-text']")

    # 备注信息
    def test_refund_note(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        refund_note = '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/div[3]/div[1]/span/div/span[2]'
        self.assert_text('添加备注', refund_note)
        self.assert_text('test', '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/div[3]/div[2]/div/div[2]')
        self.click(refund_note)
        self.assert_text('订单标记与备注由商家添加，仅平台客服和商家可见', '//*[@id="rcDialogTitle0"]/div/span')
        self.assert_text('订单标记：', '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/div/div[1]/span')
        self.assert_text('备注信息：', '/html/body/div[last()]/div/div[2]/div/div[2]/div[2]/div/div[2]/span')

    # 发货信息
    def test_logistics_info(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.click('//*[@id="rc-tabs-0-tab-发货物流"]')
        self.assert_text('发货物流', '//*[@id="rc-tabs-0-tab-发货物流"]')
        sleep(2)
        self.assert_text('其他', "//div[contains(text(),'其他')]")
        self.assert_text('****************', "//div[contains(text(),'****************')]")
        self.assert_text('已发货', "//span[@class='title____XNj5']")
        self.assert_text('包裹正在等待揽收', "//div[@class='ant-steps-item-description']")

    # 退货物流
    def test_refund_logistics_info(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.assert_text('退货物流', '//*[@id="rc-tabs-0-tab-退货物流"]')
        self.assert_text('EMS：****************',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[1]/div[2]/div[1]/div[3]/span')
        self.assert_text('已签收2024-08-20 11:21:53',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div/div/div/div[3]/div[1]')
        self.assert_text('运输中',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[2]/div[2]/div/div/div/div/div/div[3]/div[2]')

    # 赠品订单
    def test_refund_gift_goods(self):
        self.to_refund_detail_new(refund_id=self.gift_order_id, account="refund_account")
        self.assert_text('三环主品带赠品',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[1]/div[2]/a/div')

    # 售后信息
    def test_refund_info(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.assert_text('售后类型', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.assert_text('退货退款',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[1]/div[2]/span')
        self.assert_text('售后单号', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[2]/div[1]')
        self.assert_text('申请时间', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[3]/div[1]')
        self.assert_text('2024-08-21 12:59:33',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[3]/div[2]/span')
        self.assert_text('收货状态', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[4]/div[1]')
        self.assert_text('买家已收到货',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[4]/div[2]/span')
        self.assert_text('退款金额', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[5]/div[1]')
        self.assert_text('售后原因', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[6]/div[1]')
        self.assert_text('不喜欢/不想要了',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[6]/div[2]/span')
        self.assert_text('原因标签', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[7]/div[1]')
        self.assert_text('-', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[7]/div[2]/span')
        self.assert_text('客服仲裁', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[8]/div[1]')
        self.assert_text('-', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[8]/div[2]/span')
        self.assert_text('买家凭证', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[9]/div[1]')
        self.assert_text('-', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[9]/div[2]/span')
        self.assert_text('退货方式',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[10]/div[1]')
        self.assert_text('自行寄回',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[10]/div[2]/span')
        self.assert_text('运费服务',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[11]/div[1]')
        self.assert_text('支持退货补运费',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[11]/div[2]/span[1]')
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[11]/div[2]/span[2]/a')

    # 协商历史
    def test_negotiation_history(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.assert_text('协商历史', '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[3]/div/div[1]/div/div')
        self.assert_text('买家申请退货退款2024-08-21 12:59:33',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[3]/div/div[2]/div[5]/div[2]/div[1]')

    # 买家信息
    def test_order_info(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.assert_text('买家信息', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[1]/div[1]')
        self.assert_text('景云',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[1]/div[2]/div/div')
        self.assert_text('买家ID', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/div[1]')
        self.assert_text('**********',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/div[2]/span')
        self.assert_text('订单编号', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[1]')
        self.assert_text('****************',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
        self.assert_text('订单状态', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[4]/div[1]')
        self.assert_text('订单关闭',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[4]/div[2]/span')
        self.assert_text('实收金额', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[5]/div[1]')
        self.assert_text('￥0.01 （含运费: ￥0 平台补贴: ￥0 主播红包: ￥0）',
                         '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[5]/div[2]/span')

    # 状态节点
    def test_refund_status(self):
        self.to_refund_detail_new(refund_id=self.refund_id, account="refund_account")
        self.assert_text('买家申请退货退款', '//*[@id="root"]/div/div/div/div/div[1]/div/div[1]/div/div[3]/div')
        self.assert_text('处理退货申请', '//*[@id="root"]/div/div/div/div/div[1]/div/div[2]/div/div[3]/div')
        self.assert_text('买家退货', '//*[@id="root"]/div/div/div/div/div[1]/div/div[3]/div/div[3]/div')
        self.assert_text('处理退货', '//*[@id="root"]/div/div/div/div/div[1]/div/div[4]/div/div[3]/div')
        self.assert_text('售后结果', '//*[@id="root"]/div/div/div/div/div[1]/div/div[5]/div/div[3]/div')

    # 历史售后
    def test_refund_history(self):
        self.to_refund_detail_new(refund_id="****************", account="refund_account")
        self.assert_text('历史售后', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[1]/div[3]/button/span')
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[1]/div[3]/button/span')
        sleep(2)
        self.assert_text('历史售后', "//div[@class='header___yYSMo']//span[contains(text(),'历史售后')]")
        self.assert_text('退款成功', "//span[@class='title___qYtWM']")

    def test_refund_history_detail(self):
        """
        历史售后
        """
        self.to_refund_detail_new(refund_id="****************", account="refund_account")
        self.click("//span[contains(text(),'历史售后')]")
        sleep(2)
        self.assert_text("退款成功", "//span[@class='title___qYtWM']")
        self.click("//div[@role='button']")
        sleep(2)
        self.assert_text('售后类型', "//div[@class='label___ke3Jm'][contains(text(),'售后类型')]")
        self.assert_text('仅退款', "//div[@class='content____G9Ug']//span//span[contains(text(),'仅退款')]")
        self.assert_text('售后单号', "//div[@class='label___ke3Jm'][contains(text(),'售后单号')]")
        self.assert_text('****************', "//span[@class='jumpUrl___L4LXv']")
        self.assert_text('平台处理退款', "//span[@class='ant-tag ant-tag-blue']")
        self.assert_text('退款金额', "//div[@class='label___ke3Jm'][contains(text(),'退款金额')]")
        self.assert_text("￥0.31退还买家¥0.31", "//span[contains(text(),'￥0.31退还买家¥0.31')]")
        self.assert_text('申请时间', "//div[@class='label___ke3Jm'][contains(text(),'申请时间')]")
        self.assert_text('2024-08-28 22:08:30', "//div[@class='content____G9Ug']//span//span[contains(text(),'2024-08-28 22:08:30')]")
        self.assert_text('售后原因', "//div[@class='label___ke3Jm'][contains(text(),'售后原因')]")
        self.assert_text('极致保障', "//div[@class='content____G9Ug']//span//span[contains(text(),'极致保障')]")

    def test_refund_history_jump(self):
        self.to_refund_detail_new(refund_id="****************", account="refund_account")
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[1]/div[3]/button/span')
        sleep(3)
        self.click("(//div[@role='button'])[1]")
        self.assert_text('****************', "//span[@class='jumpUrl___L4LXv']")
        self.click("//span[@class='jumpUrl___L4LXv']")

    # 跳转客服工作台
    def test_to_customer_service_workbench(self):
        self.to_refund_detail_new(refund_id="****************", account="refund_account")
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[1]/div[2]/div/div')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        if self.var1 == 'prt':
            assert self.get_current_url() == 'https://eshop-im.prt.kwaixiaodian.com/pc#sellerId=**********&ud=**********&userId=**********&from=sellerRefund', '客服工作台页面地址错误，请检查'
        else:
            url = 'https://im.kwaixiaodian.com/pc#sellerId=**********&ud=**********&userId=**********&from=sellerRefund'
            assert self.get_current_url() == url, '客服工作台页面地址错误，请检查'

    def test_to_order_detail(self):
        self.to_refund_detail_new(refund_id="****************", account="refund_account")
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        if self.var1 == 'prt':
            assert self.get_current_url() == 'https://eshop-s.prt.kwaixiaodian.com/zone/order/detail?id=****************', '订单详情页面地址错误，请检查'
        else:
            url = 'https://s.kwaixiaodian.com/zone/order/detail?id=****************'
            assert self.get_current_url() == url, '订单详情页面地址错误，请检查'
