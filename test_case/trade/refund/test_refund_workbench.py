from time import sleep

import pytest
from ddt import ddt

from page_objects.refund.refund import *
from ..base import BaseTestCase


# 快手小店-售后工作台
@ddt
class TestRefundWorkbench(BaseTestCase):
    def setup_class(self):
        self.order_id = 2512602088024477
        self.refund_id = 2512602090033477
        self.express_id = 46372746767658
        self.phone_num = 15723319444
        self.name = 'hw'
        self.nick_name = 'Glitter'
        self.item_id = 20120688088866
        # 售后单管理-基础元素
        self.base_query = '//*[@id="root"]/div/div/div/div/div[4]/div/div'
        # 售后单管理-查询结果
        self.base_manage = '//*[@id="root"]/div/div/div/div/div[4]/div/div/div[4]/div[1]/div/ul/div'
        # 筛选按钮
        self.filter_button = '//*[@id="kwaishop-seller-refund-pc-combinationFilter"]/div/div[9]/button[1]'
        # 点击展开后，筛选按钮
        self.expand_filter_button = '#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(21) > button.ant-btn.ant-btn-primary'

    '''
#   售后工作台->退款详情
#   售后工作台->订单详情
#   售后工作台->商品详情
#   售后工作台->物流详情
#   售后工作台->退款详情->订单详情
#   售后工作台->退款详情->运费险详情
#   售后工作台->退款详情->店铺客服im
#   售后工作台->退款详情->物流详情
#   售后工作台->售后单查询->各个筛选项
#   售后工作台->服务分板块
    '''

    # 售后工作台->售后详情
    def test_refund_detail(self):
        self.to_workbench()
        self.find_order(self.order_id)
        # 售后详情
        self.assert_text('售后详情', self.base_manage + '/div[2]/div[7]/div/div/a/button/span')
        self.click(self.base_manage + '/div[2]/div[7]/div/div/a/button/span')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text('退款已到账', "//span[contains(text(),'退款已到账')]")
        self.assert_text('发货物流', "//div[@id='rc-tabs-0-tab-发货物流']")
        self.assert_text('协商历史', "//div[@class='ant-card-head-title']")
        self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')

    # 售后工作台->订单详情
    def test_order_detail(self):
        self.to_workbench()
        self.find_order(self.order_id)
        # 订单编号——订单详情
        self.assert_text(self.order_id, self.base_manage + '/div[1]/div[3]/a')
        self.click(self.base_manage + '/div[1]/div[3]/a')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text('退款成功，此交易关闭', "//span[contains(text(),'退款成功，此交易关闭')]")
        self.assert_text('物流信息', "//div[@class='zpD691Rhqm7w8ZwdeBG_']")
        self.assert_text('买家信息',
                         "//body/div[@id='main-root']/div[@class='seller-main-spin-nested-loading']/div[@class='seller-main-spin-container']/div[@id='main-pc-atmosphere-layout']/div[@id='main-pc-page-layout']/div[@class='page-wrap___tCy1d']/div[@class='js-page-content pageContent___fL7CS scrollbar___tjMWl undefined']/div[@id='micro-viewport']/div[@id='__qiankun_microapp_wrapper_for_kwaishop_seller_order_pc__']/div[@id='orderPC']/section[@class='ant-layout']/section[@class='ant-layout']/main[@class='ant-layout-content js-content']/div/div/div[@class='ant-row']/div[2]/div[2]/div[1]")
        self.assert_text('商品信息',
                         "//body/div[@id='main-root']/div[@class='seller-main-spin-nested-loading']/div[@class='seller-main-spin-container']/div[@id='main-pc-atmosphere-layout']/div[@id='main-pc-page-layout']/div[@class='page-wrap___tCy1d']/div[@class='js-page-content pageContent___fL7CS scrollbar___tjMWl undefined']/div[@id='micro-viewport']/div[@id='__qiankun_microapp_wrapper_for_kwaishop_seller_order_pc__']/div[@id='orderPC']/section[@class='ant-layout']/section[@class='ant-layout']/main[@class='ant-layout-content js-content']/div/div/div[@class='ant-row']/div[@class='ant-col']/div[3]/div[1]")

    # 售后工作台->商品详情
    def test_item_detail(self):
        self.to_workbench()
        self.find_order(self.order_id)
        # 商品名称——商品详情
        self.assert_text('【信任】主播实在宝测试商品复制1', self.base_manage + '/div[2]/div[1]/div/div/div[2]/a/div')
        self.click(self.base_manage + '/div[2]/div[1]/div/div/div[2]/a/div')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text('商品详情', "//div[@class='title___xpzAB']")
        self.assert_text('基础信息', "//p[contains(text(),'基础信息')]")
        self.assert_text('商品图文', "//p[contains(text(),'商品图文')]")

    # 售后工作台->物流详情->发货
    def test_express_detail(self):
        self.to_workbench()
        self.find_order(self.order_id)
        # 售后工作台->物流详情
        self.assert_text('已发货',
                         '//*[@id="root"]/div/div/div[last()]/div/div[last()]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[6]/div/div[2]/div/div/div/div/span')
        self.click("//span[contains(text(),'已发货')]")
        sleep(2)
        self.assert_text('物流详情', "//div[@class='ant-drawer-title']")

    # 售后工作台->物流详情->退货
    def test_express_return_detail(self):
        self.to_workbench()
        self.find_order(2511102240186477)
        # 售后工作台->物流详情
        self.assert_text('退货', "//span[contains(text(),'退货:')]")
        self.click("//div[@class='ant-list ant-list-vertical ant-list-split ant-list-something-after-last-item']//span[2]")
        sleep(2)
        self.assert_text('退货物流可能存在异常，揽收时间异常，物流还未签收，请仔细核对', "//div[@class='ant-alert-message']")
        self.assert_text('未获取到物流轨迹，请复制单号自行查询', "//div[@class='ant-steps-item-title']")
        self.click("//a[contains(text(),'物流详情')]")
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 != 'prt':
            assert url == 'https://s.kwaixiaodian.com/zone/supply/express/detail?id=77020451519477&oid=2511102240186477', 'url不符合预期，请检查'

    # 售后工作台->售后详情->订单详情
    def test_refund_order_detail(self):
        self.to_workbench()
        self.find_order(self.order_id)
        # 售后详情
        self.assert_text(self.refund_id, self.base_manage + '/div[1]/div[2]/a')
        self.click(self.base_manage + '/div[1]/div[2]/a')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text(self.order_id, f"//a[normalize-space()='{self.order_id}']")
        # 订单详情
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 != 'prt':
            assert url == f'https://s.kwaixiaodian.com/zone/order/detail?id={self.order_id}', '跳转到订单详情页不符合预期'

    # 售后工作台->退款详情->客服IM
    def test_refund_detail_im(self):
        self.to_workbench()
        self.find_order(self.order_id)
        # 售后详情
        self.assert_text(self.refund_id, self.base_manage + '/div[1]/div[2]/a')
        # 客服im按钮
        self.click(self.base_manage + '/div[1]/div[4]/div/div')

    # 售后工作台->退款详情->运费险详情
    def test_refund_detail_insurance(self):
        self.to_workbench()
        order_id = 2519001704811477
        refund_id = 2519001704819477
        self.find_order(order_id)  # 有运费险的单子
        # 售后详情
        self.assert_text(refund_id, self.base_manage + '/div[1]/div[2]/a')
        self.click(self.base_manage + '/div[2]/div[7]/div/div/a/button/span')
        sleep(2)
        # 进入售后详情页
        self.assert_text(order_id, '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
        self.scroll_to('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[5]/div[1]')
        # 运费险详情
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[last()]/div[2]/span[2]/a')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 != 'prt':
            assert url == f'https://s.kwaixiaodian.com/zone/insurance/freight/detail?orderId={order_id}&policyHolderId=1275816866', 'url不符合预期，请检查'

    # 售后工作台->快速筛选
    def test_refund_quick_filter(self):
        self.to_workbench()
        self.maximize_window()
        quickFilter = '//*[@id="kwaishop-seller-refund-pc-quickFilter"]/div/div/div/div/div[1]/div'
        num = self.get_text(quickFilter + '/span')
        self.click(quickFilter)
        sleep(2)
        self.click(quickFilter)
        sleep(2)
        self.assert_text(num, self.base_query + '/div[1]/div/span/span')
        assert self.get_text(quickFilter) == "24小时内逾期" + num

    # 售后工作台->快捷筛选设置
    def test_quick_filter_setting(self):
        self.to_workbench()
        self.assert_text('设置', "//div[@class='label___w5Dka']")
        self.click("//div[@class='label___w5Dka']")
        sleep(2)
        self.assert_text('快捷筛选设置', "//span[contains(text(),'快捷筛选设置')]")
        # assert self.is_element_visible("//span[contains(text(),'快捷筛选明细')]")
        # assert self.is_element_visible("//span[contains(text(),'已选快捷筛选项')]")
        assert self.is_element_visible("//span[contains(text(),'取 消')]")
        assert self.is_element_visible("//span[contains(text(),'恢复默认设置')]")
        assert self.is_element_visible("//span[contains(text(),'确 认')]")

    # 售后工作台->筛选项
    def test_refund_filter_order_no(self):
        """
        售后工作台->筛选项->订单号
        """
        self.to_workbench()
        # 订单号
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", self.order_id)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button.ant-btn.ant-btn-primary')
        self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[4]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[3]/a')
        sleep(3)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button:nth-child(2)')

    def test_refund_filter_refund_id(self):
        '''
        售后工作台->筛选项->售后单号
        '''
        self.to_workbench()
        self.maximize_window()
        self.click('#kwaishop-seller-refund-pc-expandFilterForm')
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", self.refund_id)
        self.click(self.expand_filter_button)
        self.assert_text(self.refund_id, self.base_manage + '/div[1]/div[2]/a')

    @pytest.mark.p0
    def test_refund_filter_express_no(self):
        """
        售后工作台->筛选项->退货物流单号
        """
        self.to_workbench()
        self.maximize_window()
        self.type('#control-hooks_expressNoList', self.express_id)
        # sleep(100)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button.ant-btn.ant-btn-primary')
        self.sleep(3)
        self.click(self.base_manage + '/div[2]/div[6]/div/div[1]/div/div/span')
        self.assert_text('EMS：46372746767658',
                         '/html/body/div[last()]/div/div/div/div[2]/div/div/div/div/div/div/div[1]/div[1]/span')

    def test_refund_filter_time_range(self):
        """
        售后工作台->筛选项->申请时间
        """
        self.to_workbench()
        self.maximize_window()
        # 最近7天
        self.click(
            '#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(3) > div > div.ant-col.ant-col-17.ant-form-item-control > div > div > div > div > div.ant-picker-input.ant-picker-input-active')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[2]/ul/li[3]/span')
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button.ant-btn.ant-btn-primary')
        sleep(3)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button:nth-child(2)')

    def test_refund_filter_mobile_phone(self):
        """
        售后工作台->筛选项->收货人/手机号
        """
        self.to_workbench()
        self.maximize_window()
        # 手机号
        self.type("//input[@id='control-hooks_receiverNameOrPhone']", self.phone_num)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button.ant-btn.ant-btn-primary')
        sleep(3)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button:nth-child(2)')

    def test_refund_filter_refund_status(self):
        """
        售后工作台->筛选项->售后状态
        """
        self.to_workbench()
        self.maximize_window()
        # 售后状态
        selector = '/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[1]/div'
        self.click('#control-hooks_type > div > div > span.ant-select-selection-search')
        self.click(selector)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button.ant-btn.ant-btn-primary')
        self.assert_text('售后申请拒绝，待买家处理', "//span[@title='售后申请拒绝，待买家处理']")

    def test_refund_filter_refund_expess_no(self):
        """
        售后工作台->筛选项->退货物流状态
        """
        self.to_workbench()
        self.maximize_window()
        self.click('#kwaishop-seller-refund-pc-filter-logistics-status > div > div > span.ant-select-selection-search')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]')
        sleep(2)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button.ant-btn.ant-btn-primary')
        sleep(3)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button:nth-child(2)')

    def test_refund_filter_name(self):
        """
        售后工作台->筛选项->收货人姓名
        """
        self.to_workbench()
        self.maximize_window()
        self.click('#kwaishop-seller-refund-pc-expandFilterForm')
        # 姓名
        self.type("//input[@id='control-hooks_receiverNameOrPhone']", self.name)
        self.click(self.expand_filter_button)
        sleep(3)
        # self.assert_text('//*[@id="root"]/div/div/div[2]/div/div[4]/div/div/div[4]/div[1]/div/ul/div[1]/div[1]/div[4]/div/div', '景云')

    def test_refund_filter_refund_type(self):
        """
        售后工作台->筛选项->售后类型
        """
        self.to_workbench()
        self.maximize_window()
        self.click('#kwaishop-seller-refund-pc-expandFilterForm')
        # 售后类型
        self.click('#control-hooks_handlingWay > div > div > span.ant-select-selection-search')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[1]/div')
        self.click(self.expand_filter_button)
        sleep(3)
        selector = '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div[1]/div[2]/div[4]/div/div/div[1]'
        if self.is_element_visible(selector):
            self.assert_text('类型：仅退款', selector)

    """
    商品名称/id
    订单状态
    买家昵称/id
    售后原因
    纠纷介入
    售后协商
    售后操作
    服务保障
    完结时间
    备注旗帜
    退款方式
    其他筛选-时效调整/商责退货运费申请
    """

    def base_expand_more_filters(self):
        # 进入售后工作台，展开更多筛选
        self.to_workbench()
        self.click('#kwaishop-seller-refund-pc-expandFilterForm')

    def test_query_by_item_name(self):
        """
        售后工作台->筛选项->商品名称
        """
        self.base_expand_more_filters()
        item_name = '【信任】主播实在宝测试商品复制1'
        self.type("//input[@id='control-hooks_itemTitleOrId']", item_name)
        self.click(self.expand_filter_button)
        sleep(2)
        self.assert_text(item_name, self.base_query + '/div[4]/div[1]/div/ul/div[1]/div[2]/div[1]/div/div/div[2]/a/div')

    def test_query_by_item_id(self):
        """
        售后工作台->筛选项->商品id
        """
        self.base_expand_more_filters()
        item_name = '【信任】主播实在宝测试商品复制1'
        self.type("//input[@id='control-hooks_itemTitleOrId']", self.item_id)
        self.click(self.expand_filter_button)
        sleep(2)
        self.assert_text(item_name, self.base_query + '/div[4]/div[1]/div/ul/div[1]/div[2]/div[1]/div/div/div[2]/a/div')

    def test_query_by_order_status(self):
        """
        售后工作台->筛选项->订单状态
        """
        self.base_expand_more_filters()
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", self.order_id)
        self.click('//*[@id="control-hooks_orderViewStatus"]/div/div')
        self.click("//div[@class='ant-select-item-option-content'][contains(text(),'订单关闭')]")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计 1 条' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_buyer_name(self):
        """
        售后工作台->筛选项->买家昵称
        """
        self.base_expand_more_filters()
        self.type("//input[@id='control-hooks_buyerNickOrId']", self.nick_name)
        self.click(self.expand_filter_button)
        sleep(2)
        assert self.nick_name in self.get_text(self.base_query + '/div[4]/div[1]/div/ul/div[1]/div[1]/div[4]/div/div')

    def test_query_by_buyer_id(self):
        """
        售后工作台->筛选项->买家id
        """
        self.base_expand_more_filters()
        self.type("//input[@id='control-hooks_buyerNickOrId']", '2695585838')
        self.click(self.expand_filter_button)
        sleep(2)
        assert '呱呱吃鱼' in self.get_text(self.base_query + '/div[4]/div[1]/div/ul/div[1]/div[1]/div[4]/div/div')

    def test_query_by_refund_reason(self):
        """
        售后工作台->筛选项->售后原因
        """
        self.base_expand_more_filters()
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", self.order_id)
        self.click('#control-hooks_refundReasonClassify > div')
        self.click("//div[@title='不想要、不喜欢、拍多、拍错']//div[1]")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计 1 条' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_dispute_intervention(self):
        """
        售后工作台->筛选项->纠纷介入
        """
        self.base_expand_more_filters()
        self.click('#control-hooks_disputeResult > div')
        self.click("//div[@title='商家责任']//div[1]")
        self.click(self.expand_filter_button)
        sleep(2)

    def test_query_by_negotiate(self):
        """
        售后工作台->筛选项->售后协商
        """
        self.base_expand_more_filters()
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", 2511502240033477)
        self.click('#control-hooks_suggestionSituation > div')
        self.click("//div[@class='ant-select-item-option-content'][contains(text(),'协商成功')]")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计 1 条' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_operate(self):
        """
        售后工作台->筛选项->售后操作
        """
        self.base_expand_more_filters()
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", 2507001624905477)
        self.click('#control-hooks_aftersalesOperation > div > div')
        self.click("//div[@title='换货转退款']//div[1]")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计 1 条' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_service_guarantee(self):
        """
        售后工作台->筛选项->服务保障
        """
        self.base_expand_more_filters()
        self.type("//textarea[@id='control-hooks_orderOrRefundIdList']", 2510502116050477)
        self.click('#control-hooks_serviceGuarantee > div > div')
        self.click("//div[@title='极速退款']//div[1]")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计 1 条' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_end_time(self):
        """
        售后工作台->筛选项->完结时间
        """
        self.base_expand_more_filters()
        # 最近7天
        self.click(
            '#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(16) > div > div.ant-col.ant-col-17.ant-form-item-control')
        sleep(1)
        self.click("//span[contains(text(),'最近7天')]")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_note_flag(self):
        """
        售后工作台->筛选项->备注旗帜
        """
        self.base_expand_more_filters()
        # 最近7天
        self.click(
            '#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(17) > div > div.ant-col.ant-col-17.ant-form-item-control > div > div')
        sleep(1)
        self.click(
            "//div[@class='ant-select-item ant-select-item-option ant-select-item-option-active']//div[@class='ant-row ant-row-middle']")
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_refund_type(self):
        """
        售后工作台->筛选项->退款方式
        """
        self.base_expand_more_filters()
        self.click(
            '#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(18) > div > div.ant-col.ant-col-17.ant-form-item-control > div > div')
        sleep(1)
        self.click("//div[@title='ATOM自动售后']//div[1]")
        self.click(self.expand_filter_button)
        sleep(2)
        # todo 加校验
        # self.assert_text('售后成功', "//div[@class='ant-spin-container']//div[1]//div[2]//div[5]//div[1]//div[1]")

    def test_query_by_timeliness_adjustment(self):
        """
        售后工作台->筛选项->时效调整
        """
        self.base_expand_more_filters()
        self.assert_text('时效调整', "//span[contains(text(),'时效调整')]")
        self.click('#control-hooks_boolCondition > label:nth-child(1) > span.ant-checkbox')
        sleep(1)
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    def test_query_by_product_liability_apply(self):
        """
        售后工作台->筛选项->商责退货运费申请
        """
        self.base_expand_more_filters()
        self.assert_text('商责退货运费申请', "//span[contains(text(),'商责退货运费申请')]")
        self.click('#control-hooks_boolCondition > label:nth-child(2) > span.ant-checkbox')
        self.click(self.expand_filter_button)
        sleep(2)
        assert '共计' in self.get_text("//div[@class='ant-list-pagination']//div//div[1]")

    # 6个月前售后单
    def test_six_months_ago(self):
        self.to_workbench()
        # 选择 6 个月前
        self.click('//*[@id="root"]/div/div/div/div/div[3]/div[1]/div[2]/div/div/div/span[2]')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]')
        sleep(2)
        self.assert_text("6个月前售后单", '//*[@id="root"]/div/div/div/div/div[3]/div[1]/div[2]/div/div/div/span[2]')
        sleep(2)
        # 点击筛选按钮
        self.click(self.filter_button)

    # 6个月前售后单->筛选项
    def test_six_months_ago_filter(self):
        self.to_workbench()
        # 选择 6 个月前
        self.click('//*[@id="root"]/div/div/div/div/div[3]/div[1]/div[2]/div/div/div/span[2]')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]')
        sleep(2)
        assert self.is_element_visible("//label[@title='订单编号']")
        assert self.is_element_visible("//label[@title='申请时间']")
        assert self.is_element_visible("//label[@title='收货人手机号']")
        assert self.is_element_visible("//label[@title='售后状态']")
        assert self.is_element_visible("//label[@title='售后单号']")
        assert self.is_element_visible("//label[@title='买家昵称']")

    # 6个月前售后单->更多筛选项
    def test_six_months_ago_filter_more(self):
        self.to_workbench()
        self.click("//span[@id='kwaishop-seller-refund-pc-expandFilterForm']")
        sleep(2)
        assert self.is_element_visible("//label[@title='服务保障']")
        assert self.is_element_visible("//label[@title='其他筛选']")
        assert self.is_element_visible("//span[contains(text(),'时效调整')]")
        assert self.is_element_visible("//span[contains(text(),'商责退货运费申请')]")

    # 批量导出
    def test_batch_export(self):
        self.to_workbench()
        self.maximize_window()
        self.find_order(self.order_id)
        self.click('#kwaishop-seller-refund-pc-combinationFilter > div > div:nth-child(9) > button:nth-child(3) > span')
        self.click('/html/body/div[last()]/div/div[2]/div/div[2]/div[3]/button[2]/span')

    # 查看导出页面
    def test_export_list(self):
        self.to_workbench()
        self.maximize_window()
        self.click('//*[@id="kwaishop-seller-refund-pc-combinationFilter"]/div/div[9]/a/button/span')
        sleep(4)
        if self.var1 != "prt":
            self.assert_text('下载', '//*[@id="root"]/div/div/div[2]/div/div/div/div/div/table/tbody/tr[1]/td[4]/a/span')

    # 排序方式下拉框
    def test_sort_list(self):
        self.to_workbench()
        self.maximize_window()
        self.click('#kwaishop-seller-refund-pc-expandFilterForm')
        self.assert_text('临近逾期排序', self.base_query + '/div[2]/div[2]/div/div/div/div/span[2]')
        # 点击下拉框
        self.click('//*[@id="root"]/div/div/div/div/div[last()]/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        # 选择申请时间排序
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        self.assert_text('申请时间倒序', self.base_query + '/div[2]/div[2]/div/div/div/div/span[2]')
        # 点击筛选
        self.click('//*[@id="kwaishop-seller-refund-pc-combinationFilter"]/div/div[21]/button[1]')
        sleep(2)

    # 去设置自动退款
    def test_set_fast_refund(self):
        self.to_workbench()
        self.maximize_window()
        self.assert_text('去设置自动退款', self.base_query + '/div[2]/div[2]/div/span/a')
        self.click(self.base_query + '/div[2]/div[2]/div/span/a')
        # 跳转到atom页面
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        if self.var1 != 'prt':
            assert url == 'https://s.kwaixiaodian.com/zone/refund/auto-refund/index', 'url不符合预期，请检查'

    # 订单规格
    def test_refund_order_specification(self):
        self.to_workbench()
        self.maximize_window()
        self.find_order(self.order_id)
        self.assert_text('测试1 (上课的女) ×1', self.base_manage + '/div[2]/div[1]/div/div/div[2]/div[2]/div')

    # 金额实收退款含运费
    def test_refund_price(self):
        self.to_workbench()
        self.maximize_window()
        self.find_order('2505001613376477')
        sleep(2)
        self.assert_text('实收： ¥0.02', self.base_manage + '/div[2]/div[2]/div/div[1]/span')
        self.assert_text('含运费： ¥0.01', self.base_manage + '/div[2]/div[2]/div/div[2]')
        self.assert_text('退款： ¥0.02', self.base_manage + '/div[2]/div[2]/div/div[3]/span')

    # 订单状态展示
    def test_refund_order_status(self):
        self.to_workbench()
        self.maximize_window()
        self.find_order(self.order_id)
        self.assert_text('订单关闭', self.base_manage + '/div[2]/div[3]/div/div')

    # 售后类型和原因展示
    def test_refund_type_and_refund_reason(self):
        self.to_workbench()
        self.maximize_window()
        self.find_order(self.order_id)
        self.assert_text('类型：退货退款', self.base_manage + '/div[2]/div[4]/div/div/div[1]')
        self.assert_text('原因：商品不合适（颜色/尺码/款式等）', self.base_manage + '/div[2]/div[4]/div/div/div[2]')

    # 售后状态
    def test_refund_status(self):
        self.to_workbench()
        self.find_order(self.order_id)
        self.assert_text('售后成功', self.base_manage + '/div[2]/div[5]/div/div')

    # 售后单条数
    def test_refund_count(self):
        self.to_workbench()
        self.find_order(self.order_id)
        self.assert_text('（共1条）', self.base_query + '/div[1]/div/span')

    # 部分发货
    def test_part_ship(self):
        self.to_workbench()
        self.find_order('2516102246609477')
        self.assert_text('部分发货',
                         self.base_manage + '/div[2]/div[6]/div/div/div/div/div/div/span')

    def test_refund_button(self):
        self.to_workbench()
        self.find_order(self.order_id)
        self.assert_text('售后详情', self.base_manage + '/div[2]/div[7]/div/div/a/button/span')

    # 退货物流
    def test_return_express(self):
        self.to_workbench()
        self.find_order('2504800000012173')
        sleep(2)
        self.assert_text('已签收', self.base_manage + '/div[2]/div[6]/div/div[1]/div/div/span')
        self.click(self.base_manage + '/div[2]/div[6]/div/div[1]/div/div/span')
        self.assert_text('其他：hggggh7666',
                         '/html/body/div[last()]/div/div/div/div[2]/div/div/div/div/div/div/div[1]/div[1]/span')
        self.assert_text('物流详情',
                         '/html/body/div[last()]/div/div/div/div[2]/div/div/div/div/div/div/div[1]/div[2]/span/a')

    # 赠品订单
    def test_refund_gift(self):
        self.to_workbench()
        self.find_order('2505001945546477')
        sleep(2)
        self.assert_text('商家品-娜丝奴黑钻童装', self.base_manage + '/div[2]/div[1]/div/div[1]/div[2]/a/div')
        self.assert_text('展开共1件赠品', self.base_manage + '/div[2]/div[1]/div/section')
        self.click(self.base_manage + '/div[2]/div[1]/div/section')

    def test_service_score_show(self):
        """
        售后工作台-服务分-板块展示，分数展示
        """
        self.to_workbench()
        assert self.is_element_visible("//span[@class='kpro-lego-store-score-overview-myStoreScore-titleContainer-title']")
        self.assert_text("售后服务分诊断", "//span[@class='kpro-lego-store-score-overview-myStoreScore-titleContainer-title']")
        self.assert_text("人工客服满意率", "//span[contains(text(),'人工客服满意率')]")
        self.assert_text("售后处理时长达成率", "//span[contains(text(),'售后处理时长达成率')]")
        self.assert_text("平台求助率", "//span[contains(text(),'平台求助率')]")
        value = self.get_text("//span[@class='kpro-lego-store-score-overview-myStoreScore-valueContainer-number']")
        assert value != '', '服务分不能没有分数'

    def test_service_score_to_shop_point(self):
        """
        售后工作台-服务分-跳转店铺体验分
        """
        self.to_workbench()
        assert self.is_element_visible("//span[contains(text(),'当前板块仅展示售后相关指标，全部指标可前往')]"), '不符合预期'
        self.click("//a[contains(text(),'店铺体验分')]")
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == "https://s.kwaixiaodian.com/zone/governance-score/era/store-score-v4-pc", "跳转到店铺体验分url不符合预期，请检查"

    def test_service_score_base_show(self):
        """
        售后工作台-服务分-兜底展示
        """
        self.to_workbench(account='huwen')
        self.assert_text("服务分诊断", "//div[@class='DE0sEaziiJcJMp_szGkF']")
        assert self.is_element_visible(
            "//span[contains(text(),'亲爱的商家朋友，您近30天内支付金额大于2元的自建订单不足30笔，暂无法统计店铺体验分')]")
        # 刷新页面按钮
        self.assert_text("刷新页面", "//button[@class='kwaishop-lego-store-points-pc-btn']")
        self.click("//span[contains(text(),'了解更多')]")
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == "https://edu.kwaixiaodian.com/rule/web/detail?id=S5axsrunig"

    def test_service_score_check_reason(self):
        """
        售后工作台-服务分-查看原因分析
        """
        self.to_workbench()
        self.click("//span[contains(text(),'查看原因分析及改进建议&去提升服务分')]")
        sleep(2)
        # 服务分诊断抽屉展示
        assert self.is_element_visible(
            "//div[@class='ant-row ant-row-space-between ant-row-middle']//div[contains(text(),'服务分诊断')]"), "服务分诊断抽屉展示不符合预期"
        self.assert_text("自主工具开通-助力指标直接提升",
                         "//body/div/div[@class='ant-drawer ant-drawer-right ant-drawer-open']/div[@class='ant-drawer-content-wrapper']/div[@class='ant-drawer-content']/div[@class='ant-drawer-wrapper-body']/div[@class='ant-drawer-body']/div[@class='childrenList___Z6fcB']/div[1]/div[1]")
        self.assert_text("权益开通-服务分附加加分",
                         "//body/div/div[@class='ant-drawer ant-drawer-right ant-drawer-open']/div[@class='ant-drawer-content-wrapper']/div[@class='ant-drawer-content']/div[@class='ant-drawer-wrapper-body']/div[@class='ant-drawer-body']/div[@class='childrenList___Z6fcB']/div[2]/div[1]")
        self.click("//span[contains(text(),'查看所有平台处理售后单')]")
        sleep(2)
        self.assert_text("如何查看全量平台处理售后单", "//span[@class='ant-modal-confirm-title']")
        self.click("//span[contains(text(),'确 认')]")
        sleep(2)
        # self.click("//td[@class='ant-table-cell ant-table-cell-row-hover']//a[contains(text(),'查看详情')]")
        # sleep(2)
        # url = self.get_current_url()
        # assert "https://s.kwaixiaodian.com/zone/refund/detail?refundId=" in url, '未跳转到售后详情页'
