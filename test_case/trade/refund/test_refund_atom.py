import time

import pytest
from ddt import ddt
from time import sleep
from page_objects.refund.refund import *
from ..base import BaseTestCase
from seleniumwire import webdriver


# 快手小店-售后助手
@ddt
class TestRefundAtom(BaseTestCase):

    # 跳转到售后助手
    def to_atom(self, account="huwen"):
        self.login("MERCHANT_DOMAIN", account)
        self.assert_title("快手小店")
        sleep(2)
        # self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(5)
        while self.is_element_visible(RefundPage.first_btn):
            self.click(RefundPage.first_btn)
            sleep(2)
        self.click(RefundPage.left_refund_btn)
        if self.is_element_visible('#kpro-tool-box--sellerHelperBox > div.VpIP1yFmUG94lwOfFD5b > img'):
            self.click('#kpro-tool-box--sellerHelperBox > div.VpIP1yFmUG94lwOfFD5b > img')
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(5)
        self.assert_text("售后助手", RefundPage.left_atom_btn)
        self.click(RefundPage.left_atom_btn)
        time.sleep(5)
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
            sleep(5)
        self.close_merchant_assistant()

    '''
    售后助手->添加策略
    售后助手->使用说明
    售后助手->编辑策略
    '''

    # 售后助手->添加策略
    def test_add_strategy(self):
        self.to_atom()
        self.assert_text('添加策略', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[1]/button/span')
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[1]/button/span')
        self.assert_text('策略选择', '//*[@id="root"]/div/div[1]/span[2]/span[1]')

    # 售后助手->使用说明
    def test_instructions(self):
        self.to_atom()
        self.assert_text('使用说明', '//*[@id="root"]/div/div[2]/div/div[1]/div[3]/a')
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div[3]/a')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == 'https://university.kwaixiaodian.com/kwaishop/newKnowledge/589531969383026718/581287853570379856', '页面地址错误，请检查'

    # 售后助手Tab
    def test_refund_switch_tab(self):
        self.to_atom()
        self.assert_text('自动化策略配置', '//*[@id="rc-tabs-0-tab-1"]')
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.assert_text('自动化策略监控', '//*[@id="rc-tabs-0-tab-2"]')

    # 使用说明
    def test_use_instrument(self):
        self.to_atom()
        self.assert_text('使用说明', '//*[@id="root"]/div/div[2]/div/div[1]/div[3]/a')
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div[3]/a')

    # 优先级提示文案
    def test_priority_text(self):
        self.to_atom()
        self.assert_text('i可拖动策略调整优先级，顺序越靠前优先级越高，执行顺序越靠前；每次调整优先级或开关策略会有一定延迟',
                         '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/p')

    # 发货前atom策略表格
    def test_before_ship_atom_table(self):
        self.to_atom()
        self.assert_text('优先级', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('策略名称', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('策略类型', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('策略有效期', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('状态', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('额度消耗', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('操作', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[7]')

    # 发货后atom策略表格
    def test_after_ship_atom_table(self):
        self.to_atom()
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[1]/div/label[2]/span[2]')
        self.assert_text('优先级', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('策略名称', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('策略类型', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('策略有效期', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('状态', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('额度消耗', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('操作', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/thead/tr/th[7]')

    def test_atom_strategy_monitor(self):
        self.to_atom()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.assert_text('售后单申请单号', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('退款申请时间', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('退款原因', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('审核（执行）时间', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('申请金额', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[5]')
        self.assert_text('执行动作', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[6]')
        self.assert_text('订单号', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[7]')
        self.assert_text('执行策略', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/thead/tr/th[8]')

    # 添加策略
    def test_new_strategy(self):
        self.to_atom()
        self.assert_text('添加策略', "//span[contains(text(),'添加策略')]")
        self.click("//span[contains(text(),'添加策略')]")
        self.assert_text('推荐策略', "//p[contains(text(),'推荐策略')]")
        self.assert_text('极速退款', "//span[contains(text(),'极速退款')]")
        self.assert_text('拒收自动退', "//span[contains(text(),'拒收自动退')]")
        self.assert_text('自定义策略', "//p[contains(text(),'自定义策略')]")
        self.assert_text('发货前-自动仅退款', "//span[contains(text(),'发货前-自动仅退款')]")
        self.assert_text('发货后-自动仅退款', "//span[contains(text(),'发货后-自动仅退款')]")

    # 热门策略
    def test_hot_strategy(self):
        self.to_atom()
        self.assert_text('热门策略', "//p[contains(text(),'热门策略')]")
        self.assert_text('极速退款', "//span[contains(text(),'极速退款')]")
        assert self.is_element_visible(
            "//p[contains(text(),'适用于非虚拟商品、订单金额小于500元、且商家还未发货，买家申请退款时将执行极速退款。')]"), '文案有变更'

    # 下班自动退
    def test_off_work_auto_refund_only(self):
        self.to_atom()
        try:
            self.assert_text('下班自动退', "//span[contains(text(),'下班自动退')]")
        except:
            print('热门策略里没有<下班自动退>，请检查是否符合预期')
        else:
            assert self.is_element_visible("//p[contains(text(),'针对订单未发货，且消费者在下班时间内申请退款的场景，可以自动同意退款。')]"),'下班自动退文案有变化'
            # self.click("//div[@class='main-panel']//div[3]//div[1]//div[1]//div[2]//button[1]//span[1]")
            # sleep(3)
            # handles = self.driver.window_handles
            # self.driver.switch_to.window(handles[-1])
            # url = self.get_current_url()
            # assert url == 'https://s.kwaixiaodian.com/zone/refund/auto-refund/create-strategy?templateCode=off_work_auto_refund_only&strategyType=1&strategyId=5438501', 'url不符合预期，请检查'

    # 七天无理由
    def test_seven_day_refund_without_reason(self):
        self.to_atom()
        try:
            self.assert_text('7天无理由自动退货退款', "//span[contains(text(),'7天无理由自动退货退款')]")
        except:
            print('热门策略里没有<7天无理由自动退货退款>，请检查是否符合预期')
        else:
            self.click("//div[@class='main-panel']//div[3]//div[1]//div[1]//div[2]//button[1]//span[1]")
            sleep(3)
            self.assert_text("7天无理由自动退货退款", "//span[@class='refund-common-page-head']")

    # 更多按钮
    def test_atom_more_button(self):
        self.to_atom()
        self.assert_text('更多', '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[1]/a')
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[1]/a')
        self.assert_text('策略选择', '//*[@id="root"]/div/div[1]/span[2]/span[1]')

    # 审核/执行时间
    def test_atom_filter_time(self):
        self.to_atom()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.click('//*[@id="strategyMonitor"]/div[1]/div[1]/div/div[2]/div/div/div')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[2]/ul/li[4]/span')
        self.click('//*[@id="strategyMonitor"]/div[3]/div/div/button[1]')

    # 策略场景
    def test_atom_filter_scene(self):
        self.to_atom()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.click('//*[@id="strategyMonitor"]/div[1]/div[2]/div/div[2]/div/div/div/div/span[2]')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        self.click('//*[@id="strategyMonitor"]/div[3]/div/div/button[1]')

    # 执行策略
    def test_atom_filter_execute(self):
        self.to_atom()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.click('//*[@id="strategyMonitor"]/div[1]/div[3]/div/div[2]/div/div/div/div')
        self.click('/html/body/div[last()]/div/div/div/div/ul[1]/li/div[1]')
        self.click("//li[@title='下班自动退']//div[1]")

    # 退款原因
    def test_atom_filter_refund_reason(self):
        self.to_atom()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.click('//*[@id="strategyMonitor"]/div[2]/div[1]/div/div[2]/div/div/div/div/span[2]')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[3]/div')
        self.click('//*[@id="strategyMonitor"]/div[3]/div/div/button[1]')

    # 退款单号
    # 三个月会过期
    def test_atom_filter_refundId(self):
        self.to_atom(account="refund_account")
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        sleep(1)
        self.click('//*[@id="strategyMonitor"]/div[1]/div[2]/div/div[2]/div/div/div/div/span[2]')
        sleep(1)
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        sleep(1)
        self.type('//*[@id="strategyMonitor_refundId"]', ****************)
        self.click('//*[@id="strategyMonitor"]/div[3]/div/div/button[1]')
        sleep(2)
        self.assert_text('测试-111', "//td[contains(text(),'测试-111')]")

    # 订单号
    # 订单三个月会过期
    def test_atom_filter_orderId(self):
        self.to_atom(account="refund_account")
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.click('//*[@id="strategyMonitor"]/div[1]/div[2]/div/div[2]/div/div/div/div/span[2]')
        self.click('/html/body/div[last()]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        self.type('//*[@id="strategyMonitor_orderId"]', ****************)
        self.click("//span[contains(text(),'筛 选')]")
        sleep(2)
        self.assert_text('7天无理由退货', "//td[contains(text(),'7天无理由退货')]")
        self.assert_text('同意退货申请', "//td[contains(text(),'同意退货申请')]")
