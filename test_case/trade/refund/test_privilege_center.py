import time

import pytest
from ddt import ddt
from page_objects.refund.refund import *
from ..base import BaseTestCase
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase
from selenium.webdriver.common.action_chains import ActionChains
from time import sleep
from unittest import skip


# 权益-权益中心
@ddt
class TestPrivilegeCenter(BaseTestCase):

    def urlCheck(self, btn, url):
        self.click(btn)
        time.sleep(2)
        assert self.driver.current_url == url, '页面地址不匹配'
        self.driver.close()

    '''
    权益展示 √
    假一赔十开通关闭弹窗 √
    极速退款开通交易弹窗 √
    退货补运费跳转 √
    主播实在宝跳转 √
    换货开通关闭弹窗 √
    七天价保跳转 √
    退款不退货点击弹窗 √
    先用后付点击弹窗 √
    分期免息点击弹窗 √
    13个详细说明的跳转链接check √
    信任购商家后台页面
    '''

    def test_privilege_show(self):
        """
        权益中心-三个权益模块的权益展示
        """
        self.to_Privilege()
        self.assert_text('通用权益', Privilege.GENERAL)
        self.assert_text('品类特色权益', Privilege.BRAND)
        self.assert_text('其他权益', Privilege.OTHER)

    def test_fakeonepaynine(self):
        """
        权益中心-假一赔十开通
        不需要反复开通关闭，会拦住
        """
        self.to_Privilege()
        btn_text = self.get_text(Privilege.fakeone_btn)
        self.click(Privilege.fakeone_btn)
        sleep(1)
        if btn_text == '立即开通':
            self.assert_text('开通假一赔十', Privilege.popup_title0)
            self.assert_text('立即开通', Privilege.fakeone_open_btn)
            self.click(Privilege.fakeone_open_btn)
            self.assert_text('假一赔十开通失败，店铺指标不符合规则，请联系平台客服', Privilege.fakeone_toast)
        elif btn_text == '退出权益服务':
            self.assert_text('退出假一赔十', Privilege.popup_title0)
            self.assert_text('确认关闭', Privilege.fakeone_close_btn)

    def test_fastrefund(self):
        """
        权益中心-极速退款开通关闭
        """
        self.to_Privilege()
        assert self.is_element_visible(
            "//span[contains(text(),'除虚拟商品订单外，处于未发货状态（以快手商家后台显示为准）且实付金额小于500元的订单，消费者申请退')]"), "文案有变动"
        for i in range(2):
            btn_text = self.get_text(Privilege.fastrefund_btn)
            self.click(Privilege.fastrefund_btn)
            open = Privilege.fastrefund_open_btn
            close = Privilege.fastrefund_close_btn
            if i == 0:
                title = Privilege.fastrefund_title0
            else:
                title = Privilege.fastrefund_title1
            if btn_text == '立即开通':
                self.assert_text('开通极速退款', title)
                self.assert_text('立即开通', open)
                self.click(open)
            elif btn_text == '退出权益服务':
                self.assert_text('暂停极速退款服务将会失去以下权益，确认暂停吗？', title)
                self.assert_text('暂停服务', close)
                self.click(close)
            sleep(5)

    def test_toinsurance(self):
        """
        权益中心-退货补运费页面跳转
        """
        self.to_Privilege()
        self.click(Privilege.insurance_btn)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == 'https://s.kwaixiaodian.com/zone/insurance/freight/index?channel=5', '退货补运费页面地址错误，请检查'
        self.assert_no_404_errors()

    def test_exchange(self):
        """
        权益中心-换货开通关闭弹窗
        """
        self.to_Privilege()
        for i in range(2):
            btn_text = self.get_text(Privilege.exchange_btn)
            self.click(Privilege.exchange_btn)
            open = Privilege.exchange_open_btn
            close = Privilege.exchange_close_btn
            if i == 0:
                title = Privilege.exchange_title0
            else:
                title = Privilege.exchange_title1
            if btn_text == '立即开通':
                self.assert_text('开通换货能力', title)
                self.assert_text('确认开通', open)
                self.click(open)
            elif btn_text == '退出权益服务':
                self.assert_text('退出换货？', title)
                self.assert_text('确认关闭', close)
                self.click(close)
            sleep(2)

    def test_priceprotect(self):
        """
        权益中心-七天价保
        """
        self.to_Privilege()
        self.click(Privilege.priceprotect_btn)
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == 'https://s.kwaixiaodian.com/zone/refund/protectivePrice/guide?channel=PRIVILEGE_CENTER', '七天价保页面地址错误，请检查'
        self.assert_no_404_errors()

    # def test_refundnoreturn(self):
    #     """
    #     权益中心-退款不退货
    #     """
    #     self.to_Privilege()
    #     self.click(Privilege.refundnoreturn_btn)
    #     self.assert_text('开通退款不退货', Privilege.refundnoreturn_title)
    #     self.assert_text('知道了', Privilege.refundnoreturn_popup_btn)
    #     self.click(Privilege.refundnoreturn_popup_btn)
    #     self.assert_no_404_errors()

    def test_payafteruse(self):
        """
        权益中心-先用后付
        """
        self.to_Privilege()
        self.click(Privilege.payafteruse_btn)
        self.assert_text('退出先用后付', Privilege.payafteruse_title)
        self.assert_text('知道了', Privilege.payafteruse_popup_btn)
        self.click(Privilege.payafteruse_popup_btn)
        self.assert_no_404_errors()

    def test_nointerest(self):
        """
        权益中心-分期免息
        """
        self.to_Privilege()
        self.click(Privilege.nointerest_btn)
        self.assert_text('开通分期免息', Privilege.nointerest_title)
        self.assert_text('知道了', Privilege.nointerest_popup_btn)
        self.click(Privilege.nointerest_popup_btn)
        self.assert_no_404_errors()

    def test_crab_worry_free(self):
        """
        蟹无忧
        """
        self.to_Privilege()
        assert self.is_element_visible("//span[contains(text(),'蟹无忧')]")
        self.click('//div[7]//div[2]//div[1]')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == 'https://edu.kwaixiaodian.com/rule/web/detail?id=9Cs3hUooNt', 'url不符合预期，请检查'

    def test_full_pound(self):
        """
        足斤足两
        """
        self.to_Privilege()
        # assert self.is_element_visible("//span[@class='BBHe4wh5s0yjHeTTary7'][contains(text(),'足斤足两')]")
        self.click('//div[6]//div[2]//div[1]')
        sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        url = self.get_current_url()
        assert url == 'https://edu.kwaixiaodian.com/rule/web/detail?id=sg4FHiKqRM', 'url不符合预期，请检查'

    def test_smaz(self):
        """
        上门安装
        """
        self.to_Privilege()
        if self.var1=='prt':
            self.urlCheck(Privilege.smaz_btn, Privilege.smaz_prt_url)
        else:
            self.urlCheck(Privilege.smaz_btn, Privilege.smaz_url)

    def test_psbt(self):
        """
        破损包退_普通
        """
        self.to_Privilege()
        self.urlCheck(Privilege.psbt_btn, Privilege.psbt_url)


    '''
    十三个链接（放一起就会失败，很神奇，效率down）
    '''

    def test_linkcheck1(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn1, Privilege.url1)

    def test_linkcheck2(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn2, Privilege.url2)

    def test_linkcheck3(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn3, Privilege.url3)

    def test_linkcheck4(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn4, Privilege.url4)

    def test_linkcheck5(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn5, Privilege.url5)

    # def test_linkcheck6(self):
    #     self.to_Privilege()
    #     self.urlCheck(Privilege.link_btn6, Privilege.url6)

    # 换货
    def test_linkcheck7(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn7, Privilege.url7)

    def test_linkcheck8(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn8, Privilege.url8)

    def test_linkcheck9(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn9, Privilege.url9)

    def test_linkcheck10(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn10, Privilege.url10)

    def test_linkcheck11(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn11, Privilege.url11)

    def test_linkcheck12(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn12, Privilege.url12)

    # @skip
    # 主播是实在报-已下线
    # def test_linkcheck13(self):
    #     self.to_Privilege()
    #     self.urlCheck(Privilege.link_btn13, Privilege.url13)

    def test_linkcheck14(self):
        self.to_Privilege()
        self.urlCheck(Privilege.link_btn14, Privilege.url14)
