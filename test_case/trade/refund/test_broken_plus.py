import time
import unittest

from selenium.webdriver.common.by import By

from page_objects.refund.privilege_detail import PrivilegeDetailPage
from ..base import BaseTestCase


class TestPrivilegeDetail(BaseTestCase):

    def setUp(self):
        """
        Setup the test case
        完成登陆小店，进入目标页面操作
        """
        super().setUp()
        self.login('MERCHANT_DOMAIN', account='huwen')
        self.driver.get(
            "https://s.kwaixiaodian.com/zone/privileges/privilege-detail?privilegeHomePageKey=brokenRefundV2PrivilegeHomePageView")
        time.sleep(2)
        self.page = PrivilegeDetailPage(self.driver)

    def test_service_status_display(self):
        """
        服务状态
        """
        expected_date = "服务生效中"
        actual_date = self.page.service_status.text
        self.assertEqual(actual_date, expected_date)

    def test_service_description_button(self):
        """
        跳转-服务说明
        """
        self.page.click_service_description()
        time.sleep(2)
        url = self.get_current_url()
        assert url == "https://s.kwaixiaodian.com/zone/privileges/privilege-detail?privilegeHomePageKey=brokenRefundV2PrivilegeHomePageView", "跳转规则落地页不符合预期"

    def test_pause_service_confirmation(self):
        """
        暂停服务-确认弹窗
        """
        self.page.click_pause_service()
        time.sleep(2)
        expected_title = "确定暂停破损包退-品退率免考核服务？"
        actual_title = self.page.pause_popup_title.text
        self.assertEqual(actual_title, expected_title, "标题未正确显示")
        time.sleep(2)
        self.assertTrue(self.page.is_confirm_button_enabled(), "‘确定’按钮应可用")
        self.page.click_cancel_button()

    def test_service_config_details(self):
        """
        服务配置详情-页面表达
        """
        details = self.page.service_config_details
        self.assertEqual(details[0].text, "仅针对单笔订单支付金额在10元以上的订单赔付\n否")
        self.assertEqual(details[1].text, "单日赔付总上限\n无上限")

    def test_modify_config_button(self):
        """
        修改配置-弹窗
        """
        self.page.click_modify_config()
        time.sleep(2)
        self.page.select_compensation_option(1)
        self.assertTrue(self.page.is_compensation_option_selected(1), "Failed to select '是' for compensation.")
        time.sleep(2)
        self.page.select_compensation_option(0)
        self.assertTrue(self.page.is_compensation_option_selected(0), "Failed to select '否' for compensation.")
        self.assertTrue(self.page.is_confirm_button_enabled(), "‘确定’按钮应可用")
        self.page.click_cancel_button()

    def test_faq_section_display(self):
        faqs = self.page.faq_section
        self.assertGreater(len(faqs), 0)
        # Additional assertions can be added to check specific questions and answers


if __name__ == "__main__":
    unittest.main()
