from time import sleep

from ddt import ddt

from page_objects.refund.refund import *
from ..base import BaseTestCase


# 快手小店-小额打款
# 快手小店-快递拦截服务
@ddt
class TestRefundTransfer(BaseTestCase):
    def setup_class(self):
        self.order_id = 2315700198276229
        self.item_name = '上限2「测试商品」带运费险的休闲裤'
        self.item_id = 5030941975866

    # 小额打款页面
    def to_transfer(self):
        self.to_workbench()
        self.click(RefundPage.left_refund_btn)
        self.assert_text('小额打款', "//span[@id='menu_item_zwucQX-jO5s']")
        self.click("//span[@id='menu_item_zwucQX-jO5s']")
        sleep(2)

    # 跳转到小额打款
    def test_to_transfer(self):
        self.to_transfer()
        self.assert_text("立即使用", "//span[contains(text(),'立即使用')]")

    '''
    小额打款->发起打款
    小额打款->打款记录
    小额打款->待处理打款申请
    小额打款->打款配额

    '''

    # 跳转到小额打款->发起打款
    def test_initiate_transfer(self):
        self.to_transfer()
        if self.is_element_visible("//span[contains(text(),'立即使用')]"):
            self.click("//span[contains(text(),'立即使用')]")
            sleep(3)
        self.assert_text('发起打款', Transfer.First_tab)
        self.click(Transfer.First_tab)
        self.type("//input[@id='orderId']", self.order_id)
        self.click("//span[contains(text(),'查 询')]")
        sleep(2)
        assert self.is_element_visible("//td[normalize-space()='2315700198276229']"), '未找到对应元素'
        assert self.is_element_visible("//td[normalize-space()='5030941975866']")
        assert self.is_element_visible("//td[contains(text(),'上限2「测试商品」带运费险的休闲裤')]")
        assert self.is_element_visible("//td[normalize-space()='0.01']")
        assert self.is_element_visible("//td[contains(text(),'订单关闭')]")

    # 跳转到小额打款->打款记录
    def test_transfer_record(self):
        self.to_transfer()
        if self.is_element_visible("//span[contains(text(),'立即使用')]"):
            self.click("//span[contains(text(),'立即使用')]")
            sleep(3)
        self.assert_text('打款记录', Transfer.Second_tab)
        self.click(Transfer.Second_tab)

    # 跳转到快递拦截
    def test_intercept_logistics_service(self):
        self.to_workbench()
        self.click(RefundPage.left_refund_btn)
        self.assert_text('快递拦截服务', Transfer.package_interception)
        self.click(Transfer.package_interception)
        sleep(2)
        try:
            self.assert_text('快递拦截服务', "//span[@class='Og0co9ZPv8p0oZkU75aK']")
            self.click("//span[contains(text(),'立即开通')]")
            sleep(2)
            self.assert_text('签署协议', "//span[contains(text(),'签署协议')]")
        except:
            print("页面展示不符合")
        else:
            self.assert_text('订购服务管理', "//div[@id='rc-tabs-0-tab-1']")
            self.assert_text('拦截服务单查询', "//div[@id='rc-tabs-0-tab-2']")

    # @pytest.mark.skip
    # def test_firstTab(self):
    #     self.to_transfer()
    #
    #     # 小额打款->发起打款
    #     self.click(Transfer.First_tab)
    #     self.type(Transfer.First_tab_input, self.order_id)
    #     self.click(Transfer.First_tab_query)
    #     self.assert_text('发起打款', Transfer.payment_btn)
    #
    #     # 小额打款->打款记录
    #     self.assert_text('打款记录', Transfer.Second_tab)
    #     self.click('#rc-tabs-0-tab-2')
    #     self.type(Transfer.Second_tab_oidinput, self.order_id)
    #     self.click(Transfer.Second_tab_query)
    #     self.assert_element(Transfer.Second_tab_res, self.order_id)
    #
    #     # 小额打款->待处理打款申请
    #     self.click(Transfer.Third_tab)
    #     self.assert_text('待处理打款申请', Transfer.Third_tab)
    #
    #     # 小额打款->打款配额
    #     self.assert_text('打款配额', Transfer.Fourth_tab)
    #     self.click('//*[@id="rc-tabs-0-tab-4"]')
