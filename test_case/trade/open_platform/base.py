#!/usr/bin/env python
# -*-coding:utf-8 -*-

import os
import time

# import cv2
# import numpy as np
from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain
from page_objects.service_market.login_page import LoginPage
from page_objects.open_platform.login_page import LoginPage as gateway_login


def find_white_background(imgpath, threshold):
    imgArr = cv2.imread(imgpath)
    background = np.array([255, 255, 255])
    percent = (imgArr == background).sum() / imgArr.size
    print("当前页面白屏百分比为:%.2f%%" % (percent * 100))
    if percent >= threshold:
        return True
    else:
        return False


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        # 主账号登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        self.open(host)

        self.driver.maximize_window()

        # 点击"我是店主或服务商"
        self.click(LoginPage.login_button)
        self.assert_text("扫码登录", LoginPage.scan_code_login_tab)
        self.assert_text("手机号登录", LoginPage.phone_num_login_tab)

        # 点击"手机号登录"
        self.click(LoginPage.phone_num_login_tab)

        # 输入手机号
        self.type("input[placeholder='请输入手机号']", account_data['account'])

        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)

    def login_2(self, domain, account):
        # 主账号登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        from seleniumwire import webdriver

        self.open(host)

        self.driver.maximize_window()

        # 点击"我是店主或服务商"
        self.click(LoginPage.login_button)
        self.assert_text("扫码登录", LoginPage.scan_code_login_tab)
        self.assert_text("手机号登录", LoginPage.phone_num_login_tab)

        # 点击"手机号登录"
        self.click(LoginPage.phone_num_login_tab)

        # 输入手机号
        self.type("input[placeholder='请输入手机号']", account_data['account'])

        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(1)

        # 点击"确定"
        self.click(LoginPage.login_button_2)

    def child_login(self, domain, account):
        # 子账号登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        self.open(host)

        self.driver.maximize_window()

        # 点击"我是员工"
        self.click(LoginPage.subAccount_login_button)
        self.assert_text("账号登录", LoginPage.account_login_tab)
        self.assert_text("验证码登录", LoginPage.verification_code_login_tab)

        # 点击"账号登录"
        self.click(LoginPage.account_login_tab)

        # 输入手机号
        self.type("input[placeholder='请输入手机号']", account_data['account'])

        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)

        # 选择子账号
        self.click(account_data['default_log_select_page'])

    def open_platform_login(self, domain, account):
        # 开放平台登录

        account_data = get_account_info(account)

        host = get_domain(domain)

        self.open(host)

        self.driver.maximize_window()

        self.click(LoginPage.open_platform_phone_num_login_tab)

        # 输入手机号
        self.type("input[placeholder='请输入手机号']", account_data['account'])

        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(LoginPage.login_button_2)
        time.sleep(5)
        open_platform_select_account_email_list = self.find_elements(LoginPage.open_platform_select_account_email_list)

        for i in range(len(open_platform_select_account_email_list)):
            if open_platform_select_account_email_list[i].text == '<EMAIL>':
                open_platform_select_account_email_list[i].click()
                time.sleep(2)

                self.click(LoginPage.open_platform_select_account_confirm_button)
                time.sleep(2)
                break
            assert i < len(open_platform_select_account_email_list), "开放平台测试登陆账号不存在"

    #  登陆网关
    def login_gateway(self,account=None, domain=None):
        domain = "GATEWAY_APICENTER_DOMAIN"
        account = 'sso_yanghaiyang03'
        os.environ['RUN_ENV'] = 'staging'
        account_data = get_account_info(account)

        # from browsermobproxy import Server
        # # 代理抓包API请求数据
        # bmp_path = '/Users/<USER>/Downloads/browsermob-proxy-2.1.4/bin/browsermob-proxy'
        # server = Server(bmp_path, options={"--use-littleproxy": False})
        #
        #
        # server.start()
        # # 创建一个新的代理
        # proxy = server.create_proxy()
        # # 配置 Selenium WebDriver 以使用 BrowserMob Proxy
        # chrome_options = self.driver.webdriver.ChromeOptions()
        # chrome_options.add_argument("--proxy-server={0}".format(proxy.proxy))
        #
        # # 初始化 WebDriver
        # self.driver.webdriver.Chrome(chrome_options=chrome_options)

        host = get_domain(domain)
        self.open(host)
        self.driver.maximize_window()
        self.driver.switch_to.frame(0)

        # 输入账号
        self.type("input[placeholder='请输入邮箱前缀']", account_data['account'])
        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(gateway_login.login_button)
        time.sleep(5)

        # self.find_elements()

    #  登陆网关
    def login_gateway(self,account=None, domain=None):
        domain = "GATEWAY_APICENTER_DOMAIN"
        account = 'sso_yanghaiyang03'
        os.environ['RUN_ENV'] = 'staging'
        account_data = get_account_info(account)

        # from seleniumwire import webdriver
        # driver = webdriver.Chrome()
        #
        # def interceptor(request):
        #     # 修改请求的 headers
        #     request.headers['User-Agent'] = 'Custom User-Agent String'
        #     request.headers['Custom-Header'] = 'Custom Header Value'
        # driver.request_interceptor=interceptor

        # self.driver = driver

        # driver.get("https://open.kwaixiaodian.com/zone/new/docs/api?name=open.item.autopass.edit&version=1")


        host = get_domain(domain)
        self.open(host)
        self.driver.maximize_window()
        self.driver.switch_to.frame(0)

        # 输入账号
        self.type("input[placeholder='请输入邮箱前缀']", account_data['account'])
        # 输入密码
        self.type("input[placeholder='请输入密码']", account_data['password'])

        # 点击"登录"
        self.click(gateway_login.login_button)
        time.sleep(5)

        # self.find_elements()

    def save_cur_page(self):
        filename = time.strftime("%Y%m%d-%H%M%S") + "error_page.png"
        file_path = './test_data/img/' + filename
        self.save_screenshot(file_path)

        return file_path

    def check_white_background(self, img_path, url, threshold):
        check_result = find_white_background(str(img_path), threshold)
        assert check_result is not True, url + ",当前页面存在白屏"
        os.remove(img_path)

    def check_white_page(self, url, white_page_max_time):
        navigation_start = self.execute_script("return window.performance.timing.navigationStart")
        response_start = self.execute_script("return window.performance.timing.responseStart")

        blank_time = response_start - navigation_start
        print("当前页面加载时间为:%.3f秒" % (blank_time / 1000))
        assert blank_time < white_page_max_time * 1000, url + ",当前页面加载时间超过设定的时间"

    def check_page_error(self, url, white_page_max_time=1, threshold=0.9):
        self.open(url)

        self.wait_for_ready_state_complete()

        self.check_white_page(url, white_page_max_time)

        # 访问页面状态是否404
        self.assert_link_status_code_is_not_404(url)

        # # 验证没有js错误
        # self.assert_no_js_errors()

        # 验证没有断开的链接
        self.assert_no_404_errors(url)

        file_path = self.save_cur_page()

        # 检测页面是否白屏
        self.check_white_background(file_path, url, threshold)
