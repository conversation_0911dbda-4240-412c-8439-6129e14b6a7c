import os
import ast
import pandas as pd
from collections import defaultdict

def find_duplicate_methods_in_classes(directory):
    class_methods = defaultdict(list)

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                print(f"Processing file: {file_path}")
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                        node = ast.parse(f.read(), filename=file_path)
                        for n in node.body:
                            if isinstance(n, ast.ClassDef):
                                class_name = n.name
                                for item in n.body:
                                    if isinstance(item, ast.FunctionDef):
                                        class_methods[(file_path, class_name)].append(item.name)
                except (SyntaxError, UnicodeDecodeError) as e:
                    print(f"Error in {file_path}: {e}")

    duplicates = defaultdict(list)
    for (file_path, class_name), methods in class_methods.items():
        method_count = defaultdict(int)
        for method in methods:
            method_count[method] += 1
        for method, count in method_count.items():
            if count > 1:
                duplicates[class_name].append((method, file_path))

    # 准备导出到 Excel
    export_data = []
    for class_name, methods in duplicates.items():
        for method, file_path in methods:
            export_data.append({'Class': class_name, 'Method': method, 'File': file_path})

    # 创建 DataFrame 并导出到 Excel
    df = pd.DataFrame(export_data)
    output_file = 'duplicate_methods.xlsx'
    df.to_excel(output_file, index=False)

    print(f"Exported duplicate methods to {output_file}")

# 使用当前目录
current_directory = os.getcwd()
find_duplicate_methods_in_classes(current_directory)