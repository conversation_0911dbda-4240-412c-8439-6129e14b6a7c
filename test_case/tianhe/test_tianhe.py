import pytest
import cv2
import numpy as np
import requests
import time
import sys

from test_case.growth.kuaishouxiaodian.base import BaseTestCase
from utils.env_help import get_put_test
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestTianhe(BaseTestCase):
    """
    python3 -m pytest test_case/distribution/talent/test_talent_home.py --html=test_data/leader_report.html --headless -n=3
    """

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    def init_sso(self):
        # sso_host = "https://sso.corp.kuaishou.com/cas/login"
        # self.login3(sso_host, "zhanghongbin", "tfBW09#yiCQ3")
        apsso_jost = "https://apsso.corp.kuaishou.com/apsso"
        self.login4(apsso_jost, "<EMAIL>", "Collins0721")
        time.sleep(3)

    def del_pop(self):
        while self.is_element_visible('//button[contains(text(),\'知道了\')]'):
            self.click('//button[contains(text(),\'知道了\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'下一步\')]'):
            self.click('//button[contains(text(),\'下一步\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'完成\')]'):
            self.click('//button[contains(text(),\'完成\')]')
            time.sleep(1)

        while self.is_element_visible('//button[contains(text(),\'暂不处理\')]'):
            self.click('//button[contains(text(),\'暂不处理\')]')
            time.sleep(1)

    def common_page_solve(self, delay_time):
        time.sleep(5)
        self.refresh()
        time.sleep(delay_time)
        self.del_pop()

    @pytest.mark.p0
    def test_tianhe(self):
        """ 根据账号进行登录 tianhe登陆只需要访问内网环境"""
        isrun, accountNo, pwd, prtUrl, onlineUrl, storeName, executeId, sampleId = self.get_excute_info()
        if (isrun == 'false'):
            return
        # basePath = "C:\\Users\\<USER>\\Desktop\\kwaishopuiautotest\\"
        basePath = "C:\\Users\\<USER>\\Desktop\\kwaishopuiautotest\\tianhe\\"
        # basePath = "/Users/<USER>/Desktop/tianhe"
        img_paths = []
        self.init_sso()
        self.login2("MERCHANT_HOME_DOMAIN", accountNo, pwd)
        time.sleep(3)
        """  跳转 prtUrl """
        self.open(prtUrl)
        self.common_page_solve(15)
        """  截图 """
        # prt_path = "/Users/<USER>/Desktop/prt.png"
        prt_path = basePath + 'prt_' + storeName
        self.save_screenshot(prt_path)
        logger.info("保存图片到本地成功")
        img_paths.append(prt_path)

        """  跳转 onlineUrl """
        self.open(onlineUrl)
        self.common_page_solve(15)
        """  截图 """
        # onlie_path = "/Users/<USER>/Desktop/online.png"
        onlie_path = basePath + 'base_' + storeName
#         img_paths.append(onlie_path)
        self.save_screenshot(onlie_path)

        """  图片比对 """
        """ resize下再处理，不然会有点慢 """
        detect_img, diffRate = self.detect(prt_path, onlie_path)

        detect_path = basePath + 'online_' + storeName

        cv2.imwrite(detect_path, detect_img)
        img_paths.append(detect_path)

        if (self.uploadMultiCdnFile(img_paths)):
            self.sendExecuteInfo(executeId, sampleId, diffRate)
        else:
            print ("图片上传失败")

    def sendExecuteInfo(self, executeId, sampleId, diffRate):
        """ 消息发送处理 暂时换成api 调用"""
        res_url = "https://merchant-lego.corp.kuaishou.com/gateway/qa/risk/sample/execute/finish"
        data = {
            "executeId":executeId,
            "list": "[{\"originImage\": \"\",\"sampleId\": "+sampleId+",\"diffRate\": \""+ str(diffRate) +"\",\"detectImage\": \"\"}]"
        }
        response = requests.request("POST", res_url, data=data).json()
        # headers = {
        #     'trace-context': '{"laneId":"PRT.lqy"}'
        # }
        # response = requests.request("POST", res_url, headers= headers, data=data).json()
        print (" response ", response)
        file_path = "C:\\Users\\<USER>\\Desktop\\tianhe\\log2.txt"
        with open(file_path, 'w+') as file:
            file.write(str(data) + '\n')
            file.write(str(response) + '\n')

    def detect(self, src_img_path, dst_img_path):

        src_img = cv2.imread(src_img_path)
        dst_img = cv2.imread(dst_img_path)
        ori_dst_img = cv2.imread(dst_img_path)

        src_img = cv2.GaussianBlur(src_img, [5, 5], 0)
        dst_img = cv2.GaussianBlur(dst_img, [5, 5], 0)

        diff = cv2. absdiff(src_img, dst_img)

        gray = cv2. cvtColor(diff, cv2.COLOR_BGR2GRAY)

        _, result = cv2.threshold(gray, 20, 255, cv2.THRESH_BINARY)

        num_255_pixels = np.sum(result == 255)
        height, width = result.shape
        total_pixels = height * width
        # print ("num_255_pixels", num_255_pixels)
        # print ("total_pixels", total_pixels)
        result = cv2.dilate(result, np.ones([5, 5]))

        contours, _ = cv2.findContours(result, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        areas = []

        for c in contours:
            area = cv2.contourArea(c)
            areas.append(area)
        areas = np.array(areas)

        index = np.argsort(areas)[-5:]
        top5_contours = []
        rect_pos = []
        len = index.shape[0]
        for i in range(len):
            top5_contours.append(contours[index[i]])

        for c in top5_contours:
            rect_pos.append(cv2.boundingRect(c))

        for x,y,w,h in rect_pos:
            cv2.rectangle(ori_dst_img, [x, y], [x + w, y + h], [0, 0, 255], 3)

        return ori_dst_img, round((1.0 * num_255_pixels / total_pixels) * 100, 2)

    #  kcdn 平台文档 ttps://kcdn.corp.kuaishou.com/doc/#2_RESTfulapi_shangchuanwenjian
    def uploadMultiCdnFile(self, img_paths):
        # img_paths = ["/Users/<USER>/Desktop/testaa.png", "/Users/<USER>/Desktop/testbb.png"]
        try:
            url = 'https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/multiple'
            params = {
                'token' : '112363_767ee3ffce9f13f8f1612c42a8baafae'
            }
            data = {
                'pid' : 'tianheTestPaltform',
                'allowHash': 'false',
                'allowRewrite': 'false',
                'allowMD5': 'false',
                'requestInfo.uploaderType': 2,
                'requestInfo.serviceName':'public-uploadTest',
                'requestInfo.requestUri':'/rest/upolad1',
                'requestInfo.fileExt':'jpg,mp4,png'
            }
            files = [('files[]', open(file_path, 'rb')) for file_path in img_paths]
            response = requests.post(url, params=params, data=data, files=files).json()
            logger.info("kcdn 返回结果 {}".format(response))
            return response
        except Exception as e:
            print (e)
            return None

    def uploadSingleCdnFile(self, filename, img_path):
        # img_path = "/Users/<USER>/Desktop/testd.png"
        # filename = 'testd.png'
        url = 'https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/single?'
        params = {
            'token' : '112363_767ee3ffce9f13f8f1612c42a8baafae'
        }
        data = {
            'pid' : 'tianheTestPaltform',
            'filename' : filename,
            'allowHash': 'false',
            'allowRewrite': 'false',
            'allowMD5': 'false',
            'requestInfo.uploaderType': 2,
            'requestInfo.serviceName':'public-uploadTest',
            'requestInfo.requestUri':'/rest/upolad1',
            'requestInfo.fileExt':'jpg,mp4,png'
        }
        with open(img_path, 'rb') as f:
            files = {'file': f}  # 'file' 是表单字段的名称
            response = requests.post(url, params=params, data=data, files=files).json()
            print ("response, ", response)

    def get_excute_info(self):
        config_str = self.var1
        # file_path = "C:\\Users\\<USER>\\Desktop\\tianhe\\log2.txt"
        # with open(file_path, 'w+') as file:
        #     file.write(config_str + '\n')
        config_str = config_str.replace("@@@@","&")
        return config_str.split('@@@')
