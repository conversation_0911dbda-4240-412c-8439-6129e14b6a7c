#!/usr/bin/env python
import random
import time
import logging

from infra.kafka import KafkaProducers



logger = logging.getLogger(__name__)


def setup_logger():
    fmt_str = ('%(asctime)s.%(msecs)03d %(levelname)7s '
               '[%(thread)d][%(process)d] %(message)s')
    fmt = logging.Formatter(fmt_str, datefmt='%H:%M:%S')
    handler = logging.StreamHandler()
    handler.setFormatter(fmt)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


if __name__ == "__main__":
    setup_logger()
    topic = 'tianhe_test_topic'

    for i in range(2):
        msg = bytes(f'fa bu liao zhong wen ma{i}', 'utf8')
        # 对同一个topic，SDK与每个broker只维护一个连接，多次向同一个topic发送消息时复用之前的连接。
        KafkaProducers.send(topic, msg)    # 异步发送消息
        logger.info(f'send message: {msg}.')
        time.sleep(random.random())

    # 发送生产结束消息
    msg = bytes('produce henji test over', 'utf8')
    KafkaProducers.send(topic, msg).get()    # 同步阻塞发送消息
    logger.info(msg)