"""
# Author     ：author <PERSON><PERSON><PERSON><PERSON><PERSON>
# Description：
"""
import time
from time import sleep
import datetime
import unittest

import pytest as pytest
from ddt import ddt, data

from utils.account_help import get_account_detail
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
from constant.domain import get_domain, get_domain_by_env
marketing_prem_manage_url = 'https://s.kwaixiaodian.com/zone/marketing/group-buy/manage'

# @pytest.mark.skip
class TestMerchantPintuan(BaseTestCase):
    # @pytest.mark.skip
    def kwaixiaodian_login(self, account):
        """ PRT快手小店登录 """
        self.maximize_window()
        env = 'online'
        domain = 'MARKETING_TOOLS_ALL_DOMAIN'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    # 进入全部商家营销工具页面，判断拼团卡片是否存在，存在的话进入活动管理页面
    # @pytest.mark.skip
    def test_enter_marketing_tools_all_management(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login(self.account_login)
        sleep(2)
        # self.skip_guide()
        #判断拼团卡片是否存在，以及按钮是否能正常点击跳转
        self.check_pintuan_card()
        # 第二步，点击活动管理
        self.click('/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/button[2]/span[1]')


    # 进入全部商家营销工具页面，判断拼团卡片是否存在，存在的话进入活动创建页面
    # @pytest.mark.skip
    def test_enter_marketing_tools_all_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login(self.account_login)
        sleep(2)
        # self.skip_guide()
        # 判断拼团卡片是否存在，以及按钮是否能正常点击跳转
        self.check_pintuan_card()
        # 第二步，点击活动创建
        self.click('/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/button[1]')

    # 进入判断拼团活动管理页面
    def test_check_pintuan_manage(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login(self.account_login)
        sleep(2)
        self.open(marketing_prem_manage_url)
        sleep(3)
        # self.skip_guide()
        # 判断拼团卡片是否存在，以及按钮是否能正常点击跳转
        self.check_pintuan_manage()

    # 判断拼团卡片是否存在
    def check_pintuan_card(self):
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        # 第一步，判断拼团卡片是否存在，判断拼团卡片内文案信息
        sleep(2)
        self.assert_element('/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]')
        self.assert_text('拼团', "(//span[contains(text(),'拼团')])[1]")
        self.hover('//body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]')

        self.assert_text('立即创建', '/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/button[1]/span[1]')
        self.assert_text('活动管理', '/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/button[2]/span[1]')

    # 判断拼团管理页面是否加载成功
    def check_pintuan_manage(self):
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        # 第一步，判断拼团卡片是否存在，判断拼团卡片内文案信息
        sleep(1)
        self.assert_text('专属福利', '//*[@id="rc-tabs-0-panel-1"]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.assert_text('活动中', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[1]/span[2]')
        self.assert_text('待开始', '//*[@id="rc-tabs-0-panel-1"]/div[2]/div[2]/div/div[1]/div/label[2]/span[2]')



    def skip_guide(self):
        # self.click('html body div#driver-popover-item div.driver-clearfix.driver-popover-footer button.driver-close-btn.driver-close-only-btn')
        self.refresh()
        sleep(1)
        self.refresh()
        sleep(1)
        self.refresh()
        sleep(1)



    def judge_element(self, xpath):
        time.sleep(2)
        try:
            self.assert_element(xpath)
            return True
        except  Exception as e:
            print(e)
            return False



if __name__ == "__main__":
    unittest.main()
