from time import sleep

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


# 获取账号信息
account_data = get_account_info("zengjingwen")
host = get_domain("MARKETING_TOOLS_ALL_DOMAIN")

class BaseTestCase(BaseCase):

    account_login = "zengjingwen"
    marketing_tool_domain = "MARKETING_TOOLS_ALL_DOMAIN"

    def login(self, domain, account):

        account_data = get_account_info(account)
        host = get_domain(domain)

        self.open(host)
        self.click('div#root > div > div.kXJNlxi8r__5YQNj0ulH > div > div.m08GxklhGsyYvXKxct6Q > div > div > div.VHlFARsRF1ajCufMnu_B > div.qnZ_lpDp8ZC0ElTowll7 > div.eZ9cy14KB5940_xH0_jR > div:nth-child(1)')
        sleep(0.1)
        self.assert_text("扫码登录", 'div#root > div > div.kXJNlxi8r__5YQNj0ulH > div > div.m08GxklhGsyYvXKxct6Q > div > div > div.VHlFARsRF1ajCufMnu_B > div.qnZ_lpDp8ZC0ElTowll7 > div.eZ9cy14KB5940_xH0_jR > div:nth-child(1)')  # div标签中的第一个元素
        self.assert_text("验证码登录", '#root > div > div.kXJNlxi8r__5YQNj0ulH > div > div.m08GxklhGsyYvXKxct6Q > div > div > div.VHlFARsRF1ajCufMnu_B > div.qnZ_lpDp8ZC0ElTowll7 > div.eZ9cy14KB5940_xH0_jR > div:nth-child(2)')

        # self.click("div.choseTab--1_6F7 > div:nth-child(1)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='submit']")
        sleep(2)
