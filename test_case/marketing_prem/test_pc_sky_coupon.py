from time import sleep

import pytest
from ddt import ddt
from constant.account import get_account_info
from test_case.marketing_prem.base import BaseTestCase
from constant.domain import get_domain, get_domain_by_env
from constant.account import get_account_info

SKYFALL_COUPON_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/skyfall-coupon/management?entry_src=toolsv2_all_marketing_tools'


@ddt
class TestPcSkyCoupon(BaseTestCase):

    def kwaixiaodian_login(self, account):
        """ PRT快手小店登录 """
        self.maximize_window()
        env = 'online'
        domain = 'MARKETING_DOMAIN'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    # 天降红包
    # @pytest.mark.skip
    @pytest.mark.p0
    def test_skyfall_coupon_page(self):
        print('say skyfall_coupon_manage')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(SKYFALL_COUPON_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('快手官方天降红包活动管理', '//*[@id="root"]/section/main/div/div[1]/div/h1')
        # 分别点击进行中，已结束，全部。
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[1]/div/label[2]/span[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[1]/div/label[3]/span[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[1]/div/label[4]/span[2]')
        # 点击查询
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div[1]/div[2]/button')
