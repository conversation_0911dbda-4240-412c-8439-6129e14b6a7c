import random
import pytest
import requests
import time
import json

from test_case.intelligentize.base import BaseIntelligentizeTestCase
import logging
from moviepy import ImageSequenceClip
import threading
import imageio.v3 as iio
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 基础保存路径
basePath = "/Users/<USER>/pcUI/"

"""
UI智能平台
"""


class TestIntelligentize(BaseIntelligentizeTestCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()
        self.sleep(0.5)
        # 初始化视频帧列表
        self.frames = []
        # 启动时间戳
        self.start_time = datetime.now()
        # 截图活动标志
        self.screenshot_active = True
        # 启动截图线程
        self.capture_thread = threading.Thread(target=self.capture_screenshots)
        self.capture_thread.daemon = True  # 后台线程
        self.capture_thread.start()

        self.maximize_window()

    def capture_screenshots(self):
        """后台线程：每秒自动截图一次"""
        while self.screenshot_active:
            try:
                # 捕获当前屏幕
                screenshot = self.driver.get_screenshot_as_png()  # 使用底层driver方法
                frame = iio.imread(screenshot)
                self.frames.append(frame)
                print(f"已截取第 {len(self.frames)} 帧")
            except Exception as e:
                print(f"截图失败: {str(e)}")

            # 每秒一次
            self.sleep(0.5)

    def uploadSingleCdnFile(self, filename, img_path):
        url = 'https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/single?'
        params = {
            'token': '112579_e9624b36688f08a192702ff9f674c1a0'
        }
        data = {
            'pid': 'UIAgentTest',
            'filename': filename,
            'allowHash': 'false',
            'allowRewrite': 'false',
            'allowMD5': 'false',
            'requestInfo.uploaderType': 2,
            'requestInfo.serviceName': 'public-uploadTest',
            'requestInfo.requestUri': '/rest/upolad1',
            'requestInfo.fileExt': 'jpg,mp4,png'
        }
        with open(img_path, 'rb') as f:
            files = {'file': f}  # 'file' 是表单字段的名称
            response = requests.post(url, params=params, data=data, files=files).json()
            print("response, ", response)
            return response.get('data').get('cdnUrl')

    def test_intelligentize(self):
        """ 运行模板"""

        # 传递运行日志
        params = {
            "uiCaseResult": {
                "id": self.var2,
                "outId": self.data,
                "processPath": "http://pc-ui-intelligentize.staging.kuaishou.com/job/for_intelligentize/" + str(self.data)
                               + "/console"
            }
        }
        # 回调结果
        saveResult = str(requests.post(
            "https://lego.staging.kuaishou.com/gateway/qa/risk/ai/case/result/save", json=params))
        print("请求参数 " + str(params))
        print("回调结果:  " + str(saveResult))

        # 请求case内容
        api_response = ''''''
        api_response = requests.get(
            "https://lego.staging.kuaishou.com/gateway/qa/risk/ai/case/content/detail?uiCaseId=" + str(
                self.var2))
        print("请求case内容结果:" + str(api_response.json()))

        # 基础保存路径
        basePath = "/Users/<USER>/pcUI/"
        # 调试专用
        # self.var1 = "***********;test123456"
        user = self.var1.split(';')
        account = user[0]
        password = user[1]
        # 换成接口调用
        caseId = self.var2
        flag = self.var3.split(';')
        # flag = "runEvn;backEvn;normal;"
        runActionTimes = 0

        runEvn = flag[0]
        backEvn = flag[1]
        landId = flag[2]
        # 判断结果是否失败,默认为通过
        runResult = 3

        # 错误信息记录
        caseResultDesc = ""

        # 图片地址
        imgs = ""

        # 报告地址
        report = "pc-ui-intelligentize-report.staging.kuaishou.com/intelligentize/report/report" + str(caseId) + ".html"

        # 登陆
        self.loginAI("https://s.kwaixiaodian.com/zone/home", account, password)
        self.sleep(2)

        # 运行泳道
        if len(landId) > 0 and landId != 'normal':
            self.driver.header_overrides = {"trace-context": '{"laneId":' + landId + '}'}

        # 解析JSON数据
        try:
            data = api_response.json()  # 直接使用.json()方法返回的字典
            # 提取compileData字符串
            compile_data_str = data['data']['compileData']

            # 按换行符分割成数组，并过滤空行
            command_lines = [line.strip() for line in compile_data_str.split('\n') if line.strip()]
            print(command_lines)

            # 逐行执行命令
            for cmd in command_lines:
                runActionTimes = runActionTimes + 1
                try:
                    # 使用exec执行代码
                    exec(cmd, {'self': self})  # 将self作为局部变量传入
                    print(f"Executed: {cmd}")
                    self.sleep(0.5)
                    if 'self.open' in cmd:
                        self.refresh()
                        self.sleep(3)
                    if 'click' in cmd:
                        # 截图
                        self.save_screenshot(basePath + str(caseId) + '_' + str(runActionTimes) + 'action')
                        # 上传CDN
                        img = self.uploadSingleCdnFile(str(caseId) + '_' + str(runActionTimes) + '_AITest.png',
                                                       basePath + str(caseId) + '_' + str(runActionTimes) + 'action.png')
                        if runActionTimes == 2:
                            imgs = img
                        else:
                            imgs = imgs + ";" + img
                except Exception as e:
                    runResult = -1
                    print(f"Error executing '{cmd}': {str(e)}")
                    caseResultDesc = str(e)
                    print(f"错误信息: {caseResultDesc}")

                    """测试结束后停止截图并生成视频"""
                    # 停止截图线程
                    self.screenshot_active = False
                    self.capture_thread.join(timeout=5)  # 最多等待5秒线程结束

                    # 计算测试时长
                    duration = (datetime.now() - self.start_time).total_seconds()
                    print(f"测试完成! 总时长: {duration:.1f}秒, 截取帧数: {len(self.frames)}")

                    if self.frames:
                        try:
                            video_path = basePath + "test_recording_video" + ".mp4"

                            # 将帧转换为RGB格式
                            rgb_frames = [frame[:, :, :3] for frame in self.frames]

                            # 创建并写入视频
                            clip = ImageSequenceClip(rgb_frames, fps=1)
                            clip.write_videofile(video_path, codec="libx264", audio=False)

                            print(f"视频已保存至: {video_path}")
                        except Exception as e:
                            print(f"视频保存失败: {str(e)}")
                            self.save_frames_as_images()

                    # 视频上传CDN
                    video = self.uploadSingleCdnFile(str(caseId) + '_' + '_AITestVideo.mp4',
                                                     basePath + "test_recording_video" + ".mp4")

                    params = {
                        "uiCaseResult": {
                            "caseStepImages": str(imgs),
                            "finishTime": str(int(time.time() * 1000)),
                            "status": runResult,
                            "path": report,
                            "id": caseId,
                            "outId": self.data,
                            "caseResultDesc": caseResultDesc,
                            "video": video,
                            "processPath": "http://pc-ui-intelligentize.staging.kuaishou.com/job/for_intelligentize/" + str(
                                self.data) + "/console"
                        }
                    }

                    # 回调结果
                    saveResult = str(requests.post(
                        "https://lego.staging.kuaishou.com/gateway/qa/risk/ai/case/result/save", json=params))
                    print("请求参数 " + str(params))
                    print("回调结果:  " + str(saveResult))
                    pytest.fail()

        except json.JSONDecodeError:
            print("Invalid JSON format")
        except KeyError:
            print("Missing required fields in JSON")

        # 图片地址
        print(imgs)

        """测试结束后停止截图并生成视频"""
        # 停止截图线程
        self.screenshot_active = False
        self.capture_thread.join(timeout=5)  # 最多等待5秒线程结束

        # 计算测试时长
        duration = (datetime.now() - self.start_time).total_seconds()
        print(f"测试完成! 总时长: {duration: .1f}秒, 截取帧数: {len(self.frames)}")

        if self.frames:
            try:
                video_path = basePath + "test_recording_video" + ".mp4"

                # 将帧转换为RGB格式
                rgb_frames = [frame[:, :, :3] for frame in self.frames]

                # 创建并写入视频
                clip = ImageSequenceClip(rgb_frames, fps=1)
                clip.write_videofile(video_path, codec="libx264", audio=False)

                print(f"视频已保存至: {video_path}")
            except Exception as e:
                print(f"视频保存失败: {str(e)}")
                self.save_frames_as_images()

        # 视频上传CDN
        video = self.uploadSingleCdnFile(str(caseId) + '_' + '_AITestVideo.mp4',
                                         basePath + "test_recording_video" + ".mp4")

        params = {
            "uiCaseResult": {
                "caseStepImages": str(imgs),
                "finishTime": str(int(time.time() * 1000)),
                "status": runResult,
                "path": report,
                "id": caseId,
                "outId": self.data,
                "caseResultDesc": caseResultDesc,
                "video": video,
                "processPath": "http://pc-ui-intelligentize.staging.kuaishou.com/job/for_intelligentize/" + str(
                    self.data) + "/console"

            }
        }

        # 回调结果
        saveResult = str(requests.post(
            "https://lego.staging.kuaishou.com/gateway/qa/risk/ai/case/result/save", json=params))
        print("请求参数 " + str(params))
        print("回调结果:  " + str(saveResult))