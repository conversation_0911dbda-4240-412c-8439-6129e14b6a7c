import random
from time import sleep

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By
from .base import BaseTestCase
import time
from datetime import datetime

@ddt
class TestServiceReach(BaseTestCase):
    def test_service_reach(self):
        self.login("MERCHANT_HOME_DOMAIN", "member_account2")
        target_url = "https://s.kwaixiaodian.com/zone/crm/serviceReach"
        self.open(target_url)
        self.wait_for_text(text="客服触达",timeout=30)
        self.close_popup()

        """校验客服触达中的主要字段"""
        required_texts = ["基础信息", "选择发送人群", "选择权益类型","添加权益内容","选择触达渠道"]
        all_texts_found = True
        for text in required_texts:
            if not self.is_text_visible(text):
                print(f"未找到文本: '{text}'")
                all_texts_found = False
            else:
                print(f"找到文本: '{text}'")
        if not all_texts_found:
            self.fail("部分必需字段未找到！")
        else:
            print("所有关键字段均已校验")

        """选择人群"""
        self.click("//span[contains(text(),'选择人群')]")
        self.assert_text("全部人群包")
        self.click('//div[@class="zone-crm-table-body"]//span[contains(@class, "zone-crm-radio-inner")]')
        self.click("//span[contains(text(),'确 定')]")

        """添加优惠券"""
        self.click("//span[contains(text(),'选择优惠券')]")
        self.assert_text("优惠券名称")
        self.click('//div[@class="zone-crm-table-body"]//span[contains(@class, "zone-crm-radio-inner")]')
        current_date = datetime.now().strftime("%m%d")
        test_text = f"autotest_{current_date}"
        self.type('//input[@placeholder="请输入"]', test_text)
        self.click("//span[contains(text(),'确 定')]")
        self.assert_text(test_text)

        """选择触达渠道"""
        self.click("//span[contains(text(),'客服消息推送权益')]")

        """校验发送"""
        self.click("//span[contains(text(),'立即发送')]")
        self.assert_text("发送测试消息")
        success_msg = "//span[contains(text(),'确定发送')]"
        self.assert_element(success_msg)