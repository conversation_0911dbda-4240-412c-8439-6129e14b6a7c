#
# from ddt import ddt
# from .base import BaseTestCase
# from time import sleep
#
#
# @ddt
# class TestCrmMyCrowd(BaseTestCase):
#     def test_mycrowd(self):
#         self.login("MERCHANT_DOMAIN", "member_account")
#
#         self.open("https://prt-eshop-crm.test.gifshow.com/zone/crm/crowd/list?tab=crowd")
#         self.assert_text("自定义", '//*[text()="自定义"]')
#         self.assert_text("查看教程",'//*[text()="查看教程"]')
#         self.assert_text("上传人群包", '//*[text()="上传人群包"]')
#         self.assert_text("+新增自定义人群", '//*[text()="+新增自定义人群"]')
#         self.click('//*[text()="自定义"]')
#         self.click('//tbody[@class="zone-crm-table-tbody"]/tr[@class="zone-crm-table-row zone-crm-table-row-level-0"][1]/td[6]/div/div[1]')
#         sleep(3)
#         self.click('//tbody[@class="zone-crm-table-tbody"]/tr[@class="zone-crm-table-row zone-crm-table-row-level-0"][1]/td[6]/div/div[2]')
#
#         self.click('//*[text()="官方推荐"]')
#         self.click('//tbody[@class="zone-crm-table-tbody"]/tr[@class="zone-crm-table-row zone-crm-table-row-level-0"][1]/td[6]/div/div[2]')
#         self.click('//tbody[@class="zone-crm-table-tbody"]/tr[@class="zone-crm-table-row zone-crm-table-row-level-0"][1]/td[6]/div/div[3]')
#         self.click('//tbody[@class="zone-crm-table-tbody"]/tr[@class="zone-crm-table-row zone-crm-table-row-level-0"][1]/td[6]/div/div[4]')
#
#
#
#
#
#
#
#
#
