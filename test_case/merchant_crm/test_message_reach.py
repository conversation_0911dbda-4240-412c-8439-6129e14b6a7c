import random
from time import sleep

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By
from .base import BaseTestCase
import time
from datetime import datetime

@ddt
class TestMessageReach(BaseTestCase):
    def test_message_reach(self):
        self.login("MERCHANT_HOME_DOMAIN", "member_account2")
        target_url = "https://s.kwaixiaodian.com/zone/crm/msgReach"
        self.open(target_url)
        self.wait_for_text(text="短信触达",timeout=30)
        self.close_popup()

        """校验短信触达中的主要字段"""
        required_texts = ["选择发送人群", "添加优惠券", "短信设置","发送测试短信"]
        all_texts_found = True
        for text in required_texts:
            if not self.is_text_visible(text):
                print(f"未找到文本: '{text}'")
                all_texts_found = False
            else:
                print(f"找到文本: '{text}'")
        if not all_texts_found:
            self.fail("部分必需字段未找到！")
        else:
            print("所有关键字段均已校验")

        """选择人群"""
        self.click("//span[contains(text(),'选择人群')]")
        self.assert_text("全部人群包")
        self.click('//div[@class="zone-crm-table-body"]//span[contains(@class, "zone-crm-radio-inner")]')
        self.click("//span[contains(text(),'确 定')]")
        self.assert_text("发送前系统将自动更新人群包")

        """添加优惠券"""
        self.click("//span[contains(text(),'选择优惠券')]")
        self.assert_text("优惠券名称")
        self.click('//div[@class="zone-crm-table-body"]//span[contains(@class, "zone-crm-radio-inner")]')
        current_date = datetime.now().strftime("%m%d")
        test_text = f"autotest_{current_date}"
        self.type('//input[@placeholder="请输入"]', test_text)
        self.click("//span[contains(text(),'确 定')]")
        self.assert_text(test_text)

        """短信设置"""
        # self.click("//div[contains(text(),'选择短信模板')]")
        # self.click('//*[@id="root"]/div/div/div[2]/div/div/div[2]/div[3]/div/div[3]/div[2]/div[1]/div[1]/div[1]/div[2]')
        # self.wait_for_text(text="短信模板选择",timeout=15)
        # self.click('//div[@class="zone-crm-drawer-content"]//span[contains(@class, "zone-crm-radio-inner")]')
        # self.click("//span[contains(text(),'确 定')]")
        # self.assert_text("拒收请回复R")

        """校验发送"""
        self.click("//span[contains(text(),'立即发送')]")
        self.assert_text("发送测试短信")
        success_msg = "//span[contains(text(),'提 交')]"
        self.assert_element(success_msg)