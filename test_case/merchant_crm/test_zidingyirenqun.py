import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time


@ddt
class TestMemberCommon(BaseTestCase):
    def test_member_common_all_tab1(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/crm-crowd/crowd-edit")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)

        # 点击小黄车行为
        self.click("//span[contains(text(),'1、小黄车行为')]")
        self.assert_text("小黄车行为","//span[contains(text(),'1、小黄车行为')]")
        sleep(2)

        # 点击直播间消费与互动行为
        self.click("//span[contains(text(),'2、直播间消费与互动行为')]")
        self.assert_text("直播间消费与互动行为", "//span[contains(text(),'2、直播间消费与互动行为')]")
        sleep(2)

        # 点击店铺交易行为
        self.click("//span[contains(text(),'3、店铺交易行为')]")
        self.assert_text("店铺交易行为", "//span[contains(text(),'3、店铺交易行为')]")
        sleep(2)

        # 点击会员标签
        self.click("//span[contains(text(),'4、会员标签')]")
        self.assert_text("会员标签", "//span[contains(text(),'4、会员标签')]")
        sleep(2)

        # 点击行业偏好标签
        self.click("//span[contains(text(),'5、行业偏好标签')]")
        self.assert_text("行业偏好标签", "//span[contains(text(),'5、行业偏好标签')]")
        sleep(2)

        # 点击基础信息
        self.click("//span[contains(text(),'6、基础信息')]")
        self.assert_text("基础信息", "//span[contains(text(),'6、基础信息')]")
        sleep(2)

        # 点击并集
        self.click("//div[@class='zone-crowd-edit-radio-group zone-crowd-edit-radio-group-solid zone-crowd-edit-radio-group-small']//span[contains(text(),'并集')]")
        self.assert_text("基础信息", "//span[contains(text(),'6、基础信息')]")
        sleep(2)

        # 点击更新数据
        self.click("//span[contains(@class,'uKKmx1rY5sZYoE0XJ1_8')]")
        self.assert_text("根据标签选择人群", "//div[@class='uHb4Duh1iWoaawx2tkwy']")
        sleep(2)

        # 点击如何选择人群
        self.click("//span[contains(text(),'如何选择人群')]")
        self.assert_text("1、重点运营人群", "//strong[contains(text(),'1、重点运营人群')]")
        sleep(2)
        self.switch_to_window(0)

        #点击圈选池1
        self.click("//div[contains(@class,'drop-target eTNtjVrWKQ6B0WCbyjGf ywf4nxIKayzMzkA3EGzQ')]")
        self.assert_text("根据标签选择人群", "//div[@class='uHb4Duh1iWoaawx2tkwy']")
        sleep(2)

        # 点击保存人群
        self.click("//span[contains(text(),'保存人群')]")
        self.assert_text("人群名称", "//label[contains(text(),'人群名称')]")
        sleep(2)

        # 点击取消保存人群
        self.click("//div[@class='Vj6nr9qB2Z9B2PAFLpFA']//button[@type='button']")
        self.assert_text("自定义标签", "//div[contains(@class,'WoP0_5oTTJ3xEOW35F7Z')]")
        sleep(2)



