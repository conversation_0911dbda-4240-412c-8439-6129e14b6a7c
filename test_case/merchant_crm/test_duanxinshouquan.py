import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time


@ddt
class TestMemberCommon(BaseTestCase):
    def test_member_common_all_tab1(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/crm-protocol/MsgProtocolAuth")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)

        # 点击短信营销工具实名信息授权协议
        self.click("//span[contains(text(),'短信营销工具实名信息授权协议')]")
        self.assert_text("短信营销工具实名信息授权协议", "//span[contains(text(),'短信营销工具实名信息授权协议')]")
        sleep(2)


