import random
from time import sleep

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By
from .base import BaseTestCase
import time
from datetime import datetime

@ddt
class TestCrowdList(BaseTestCase):
    def test_crowd_list(self):
        self.login("MERCHANT_HOME_DOMAIN", "member_account2")
        target_url = "https://s.kwaixiaodian.com/zone/crm/crowd/list"
        self.open(target_url)
        self.wait_for_text(text="人群管理",timeout=30)
        self.close_popup()

        """校验人群管理table中的主要字段"""
        required_texts = ["潜客转化", "老客复购", "会员转化","购物团转化","我的人群","人群包名称"]
        all_texts_found = True
        for text in required_texts:
            if not self.is_text_visible(text):
                print(f"未找到文本: '{text}'")
                all_texts_found = False
            else:
                print(f"找到文本: '{text}'")
        if not all_texts_found:
            self.fail("部分必需字段未找到！")
        else:
            print("所有关键字段均已校验")

        """上传人群包"""
        self.click("//span[contains(text(),'上传人群包')]")
        self.assert_text("我已阅读人群上传")
        self.click("//span[contains(text(),'取 消')]")

        """新增自定义人群"""
        self.click("//span[contains(text(),'新增自定义人群')]")
        self.wait_for_text(text="自定义标签",timeout=30)
        self.click("//div[contains(text(),'近半年消费金额')]")
        inputs = self.find_elements(".zone-crowd-edit-input")
        inputs[0].send_keys("10")  # 第一个输入框输入10
        inputs[1].send_keys("20")  # 第二个输入框输入20
        self.click("//span[contains(text(),'确 认')]")
        self.click("//span[contains(text(),'保存人群')]")
        self.type('//input[@id="crowdName"]', "autotest")
        self.type('//textarea[@id="crowdDesc"]', "ui_autotest_4^&^#1@241!5$3!")
        self.click("//span[contains(text(),'确 认')]")
        sleep(5)
        self.wait_for_text(text="我的人群",timeout=30)

        """删除自定义人群"""
        self.click('(//span[contains(text(),"自定义")])[2]')
        sleep(3)
        self.click("//a[contains(text(),'删除')]")
        self.click("//span[contains(text(),'确 定')]")

        """官方推荐人群"""
        self.click("//span[contains(text(),'官方推荐')]")
        self.assert_text("客服触达")
        self.assert_text("发送短信")

        """校验人群包规则"""
        self.click("//span[contains(text(),'人群包规则')]")
        self.assert_text("查看教程")
        self.click("//a[contains(text(),'查看教程')]")
        self.switch_to_window(1)
        self.wait_for_text("快手商家CRM操作手册", timeout=15)
        self.driver.close()
        self.switch_to_window(0)