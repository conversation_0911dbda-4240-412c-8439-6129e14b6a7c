#
# from ddt import ddt
# from .base import BaseTestCase
# from time import sleep
#
#
# @ddt
# class TestCrmSelf(BaseTestCase):
#     def test_selfCrowd(self):
#         self.login("MERCHANT_DOMAIN", "member_account")
#         self.open("https://crm.kwaixiaodian.com/zone/crm-crowd/crowd-edit")
#         self.assert_text("自定义标签", '//*[text()="自定义标签"]')
#         self.click('//div[@class="vDWF39zWPRcemWgwSpsO"]/div[2]/div[1]/div[2]/div[1]')
#         sleep(2)
#         self.click('//div[@class="zone-crowd-edit-modal-footer"]/button[@class="zone-crowd-edit-btn"]')
#         ## 直播间消费与互动行为
#         self.click('//div[@class="vDWF39zWPRcemWgwSpsO"]/div[2]/div[2]/div[2]/div[2]')
#         ## 店铺交易行为
#         self.click('//div[@class="vDWF39zWPRcemWgwSpsO"]/div[2]/div[3]/div[2]/div[2]')
#         ## 会员标签
#         self.click('//div[@class="vDWF39zWPRcemWgwSpsO"]/div[2]/div[4]/div[2]/div[2]')
#         self.assert_text("新开通的会员商家需等24h后才有数据",'//div[@class="JZeQ2YO_oWNEXnFeYsLb"]')
#         ## 基础信息
#         self.click('//div[@class="vDWF39zWPRcemWgwSpsO"]/div[2]/div[5]/div[2]/div[2]')
#
#
#
#
#
#
#
