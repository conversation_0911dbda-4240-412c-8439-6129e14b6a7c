import random

import pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from .base import BaseTestCase
from time import sleep
import time


@ddt
class TestMemberCommon(BaseTestCase):
    def test_member_common_all_tab1(self):
        self.login("MERCHANT_DOMAIN", "member_account2")
        sleep(1)
        self.open("https://s.kwaixiaodian.com/zone/crm/chat/group-sending")
        print("页面title:", self.get_page_title())
        sleep(3)
        for i in range(7):
            if self.is_exact_text_visible("关闭", "//button[text()='关闭']"):
                self.click("//button[contains(text(),'关闭')]")
                sleep(2)

        # 点击定时群发设置
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/div')
        self.assert_text("定时群发设置", '//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/div')
        sleep(2)

        # 点击选择群聊
        self.click("//div[@class='zone-crm-select-selector']")
        self.assert_text("选择群聊", "//label[contains(text(),'选择群聊')]")
        sleep(2)

        # 点击选择全部
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/form/div[1]/div[2]/div/div/div/div')
        self.assert_text("选择群聊", "//label[contains(text(),'选择群聊')]")
        sleep(2)

        # 点击文本
        self.click('//*[@id="input"]')
        self.assert_text("选择群聊", "//label[contains(text(),'选择群聊')]")
        sleep(2)

        # 点击选择商品
        # self.click('//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/form/div[4]/div[2]/div/div/div/div/button/span')
        # self.assert_text("添加商品", '//*[@id="rcDialogTitle4"]')
        # sleep(2)

        # 点击查询
        # self.click("//button[@type='submit']]")
        # self.assert_text("添加商品", '//*[@id="rcDialogTitle1"]')
        # sleep(2)


