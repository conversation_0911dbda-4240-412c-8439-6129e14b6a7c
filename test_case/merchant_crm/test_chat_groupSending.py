import random
import pytest
from ddt import ddt
from selenium.webdriver.common.by import By
from .base import BaseTestCase
import time
from datetime import datetime

@ddt
class TestChatGroupSending(BaseTestCase):
    def test_send_group_message(self):
        """登录系统，跳转至目标URL"""
        self.login("MERCHANT_HOME_DOMAIN", "member_account2")
        target_url = "https://s.kwaixiaodian.com/zone/crm/chat/group-sending"
        self.open(target_url)
        self.wait_for_text(text="定时群发设置",timeout=60)
        self.close_popup()

        """选择群聊"""
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[1]/div[1]/form/div[1]/div[2]/div/div/div/div/div')
        self.click('(//div[contains(@class, "zone-crm-select-item zone-crm-select-item-option")])')

        """输入测试文本"""
        current_date = datetime.now().strftime("%Y%m%d")
        test_text = f"ui_autotest_{current_date}"
        self.type('//textarea[@id="input"]', test_text)

        """选择商品"""
        self.click("//span[contains(text(),'选择商品')]")
        time.sleep(2)
        self.click('(//label[contains(@class, "zone-crm-checkbox-wrapper")])[5]')
        self.click('(//label[contains(@class, "zone-crm-checkbox-wrapper")])[6]')
        self.click("//span[contains(text(),'确 认')]")

        """选择优惠券"""
        self.click('(//button[contains(@class, "zone-crm-btn zone-crm-btn-secondary")])[1]')
        self.wait_for_text(text="优惠券名称",timeout=15)
        self.click('(//td[contains(@class, "zone-crm-table-cell zone-crm-table-selection-column")])')
        self.click("//span[contains(text(),'确 认')]")

        """发送优惠券"""
        self.click("//span[contains(text(),'立即发送')]")
        success_msg = "//span[contains(text(),'确 定')]"
        self.assert_element(success_msg)
        # 由于自动化每两小时运行一次，会超出每日发券上限，因此暂不发送，仅校验流程
        # self.click("//span[contains(text(),'确 定')]")
        # success_msg = "//*[contains(text(),'设置成功')]"
        # self.assert_element(success_msg)

