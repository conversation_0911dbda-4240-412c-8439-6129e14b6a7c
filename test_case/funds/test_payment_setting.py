"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@Date: 2024/11/12
"""
from unittest import skip

from ddt import ddt
from .base import FundsBaseTestCase
import pytest


@ddt
class TestPaymentSetting(FundsBaseTestCase):

    @skip("账号问题，构造账号后恢复")
    def test_payment_C_01(self):
        """ 个人店-未进件-已认真"""
        self.login("funds_account_05")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/receipt-setup"
        self.open(url)
        self.driver.refresh()
        self.sleep(2)
        # 断言页面元素是否存在
        #点击微信进件
        self.assert_text('立即开通', "//div[@class='zone-fund-accounting-row']//div[1]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.click("//div[@class='zone-fund-accounting-row']//div[1]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.sleep(3)
        self.assert_text('张平慧', "//div[@class='zone-fund-accounting-modal-root']//span[2]")
        self.click("//span[contains(text(),'确 认')]")
        self.sleep(5)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("手机号： +86159****2366","//p[contains(text(),'手机号： +86159****2366')]")
        self.sleep(3)
        self.click("//p[contains(text(),'手机验证')]")
        #点击支付宝进件
        self.click("//div[@class='HkCTsYZjM3B7nYatmuHR']//div[2]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.sleep(3)
        self.assert_text('张平慧', "//div[@class='zone-fund-accounting-modal-root']//span[2]")
        self.click("//span[contains(text(),'取 消')]")
        self.click("//div[@class='HkCTsYZjM3B7nYatmuHR']//div[2]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.sleep(3)
        self.click("//span[contains(text(),'确 认')]")

    def test_payment_C_02(self):
        """ 个人店-已进件-已认真"""
        self.login("funds_account_08")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/receipt-setup"
        self.open(url)
        self.driver.refresh()
        self.sleep(2)
        # 断言页面元素是否存在
        #点击微信进件
        self.assert_text('查看详情', "//div[@class='zone-fund-accounting-row']//div[1]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.click("//div[@class='zone-fund-accounting-row']//div[1]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("手机验证","//h1[contains(text(),'手机验证')]")
        self.click("//p[contains(text(),'手机验证')]")
        self.sleep(3)
        #点击支付宝进件
        # self.assert_text('查看详情', "//div[@class='zone-fund-accounting-row']//div[2]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        # self.click("//div[@class='zone-fund-accounting-row']//div[2]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
    def test_payment_B_01(self):
        """ 旗舰店-已进件-已认真"""
        self.login("funds_account_08")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/receipt-setup"
        self.open(url)
        self.driver.refresh()
        self.sleep(2)
        # 断言页面元素是否存在
        # 点击微信进件
        self.assert_text('查看详情',
                         "//div[@class='zone-fund-accounting-row']//div[1]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.click(
            "//div[@class='zone-fund-accounting-row']//div[1]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.sleep(3)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("手机验证", "//h1[contains(text(),'手机验证')]")
        self.click("//p[contains(text(),'手机验证')]")
        self.sleep(3)
        # 点击支付宝进件
        self.assert_text('立即开通', "//div[@class='zone-fund-accounting-row']//div[2]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
        self.click("//div[@class='zone-fund-accounting-row']//div[2]//article[1]//section[1]//div[1]//div[1]//button[1]//span[1]")
