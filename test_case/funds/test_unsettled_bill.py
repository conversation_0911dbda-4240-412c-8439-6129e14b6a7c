"""
@Author: yangping
@Date: 2024/1/16
"""

from ddt import ddt
from .base import FundsBaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from seleniumbase.common.exceptions import NoSuchElementException
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException
import pytest

@ddt
class TestUnsettledBill(FundsBaseTestCase):

    def test_self_unsettled_bill_01(self):
        """ 待结算流水-流水列表"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.driver.refresh()
        self.sleep(2)
        # 断言页面元素是否存在
        self.assert_text('待结算流水', '//*[@id="root"]/div/article/div/div[2]/div[1]')
        self.sleep(3)

    def test_self_unsettled_bill_02(self):
        """ 待结算流水-切换笔数分布/金额分布"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 判断 笔数分布 元素
        self.assert_text('笔数分布',"//div[@title='笔数分布']")
        # 点击金额分布
        self.click("//div[@title='金额分布']")

    def test_self_unsettled_bill_03(self):
        """ 待结算流水-资金小助手"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 定位小助手元素
        self.assert_text('资金小助手', "//div[@class='helperTitle___U4opD']")

    def test_self_unsettled_bill_04(self):
        """ 待结算流水-导出操作"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 点击导出
        self.click("//span[contains(text(),'导 出')]")
        self.sleep(2)

    def test_self_unsettled_bill_05(self):
        """ 待结算流水-导出记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        self.click("//span[contains(text(),'查看导出记录')]")
        self.sleep(1)
        self.assert_text("已导出的报表","//div[@class='title-style___LHx4K']")
        self.sleep(2)

    def test_self_unsettled_bill_06(self):
        """ 待结算流水-查询"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/span/input', **************)
        # 点击查询
        self.click("//*[@id='pro-form-wrapper']/div[2]/div[2]/div/div/div[2]/button")
        self.sleep(2)

    def test_self_unsettled_bill_07(self):
        """ 待结算流水-重置"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 输入商品ID
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/span/input', **************)
        # 点击查询
        self.click("//*[@id='pro-form-wrapper']/div[2]/div[2]/div/div/div[2]/button")
        self.sleep(1)
        self.click("//*[@id='pro-form-wrapper']/div[2]/div[2]/div/div/div[1]/button")
        self.sleep(1)

    def test_self_unsettled_bill_08(self):
        """ 待结算流水-货款结算规则"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 点击货款结算规则，预期跳转
        self.click("//span[contains(text(),'待结算流水使用指南')]")
        self.sleep(2)
        self.assert_text('【功能操作】待结算流水使用指南', "//div[@class='article-title']")

    def test_self_unsettled_bill_09(self):
        """ 待结算流水-流水 点击订单号，跳转成功"""
        self.login("funds_account_01")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 点击点击订单号，预期跳转
        # self.click("(//div[contains(text(),'测试商品-通用万人团-无子活动')])[1]")
        # self.sleep(2)
        # # 校验跳转到订单页面
        # self.assert_text('订单信息', "//div[contains(text(),'订单信息')]")

    def test_self_unsettled_bill_10(self):
        """ 待结算流水-点击商家对快手商品的技术服务费收取有疑问，预期出现弹窗"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 点击具体问题
        try:
            first_element = self.driver.find_element(By.XPATH, '//*[@id="root"]/div/article/div/div[3]/div/div[2]/div/div[1]/div[1]/div[1]')
            first_element.click()
            self.sleep(5)
            print('点击到了具体问题')
        except NoSuchElementException:
            print('走到了吗？')
            # 如果第一个元素不存在，则尝试点击第二个元素
            try:
                second_element = self.driver.find_element(By.XPATH, '//*[@id="root"]/div/article/div/div[3]/div/div[2]/div/div[1]/div[2]/div[1]/span')
                second_element.click()
            except NoSuchElementException as e:
                print(f"测试失败: {str(e)}")

    def click_element(self, by, locator, timeout=10):
        try:
            # 等待元素可点击
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, locator))
            )
            element.click()
            self.sleep(2)
            return True
        except TimeoutException:
            print(f"元素定位超时: {by}={locator}")
            return False
        except ElementClickInterceptedException:
            print(f"元素点击被拦截: {by}={locator}")
            return False
        except Exception as e:
            print(f"操作元素时发生错误: {str(e)}")
            return False

    def test_self_unsettled_bill_11(self):
        """ 待结算流水-点击商家对快手商品的技术服务费收取有疑问，预期出现弹窗"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/unsettled-bill"
        self.open(url)
        self.sleep(2)
        # 点击具体问题
        try:
            # 第一个元素：
            first_element = (By.XPATH, '//*[@id="root"]/div/article/div/div[3]/div/div[2]/div/div[1]/div[1]/div[1]')
            if self.click_element(*first_element):
                print('第一个元素定位并点击成功')
            else:
                print('第一个元素未定位到，尝试第二个元素')
                second_locator = (By.XPATH, '//*[@id="root"]/div/article/div/div[3]/div/div[2]/div/div[1]/div[2]/div[1]/span')
                if self.click_element(*second_locator):
                    print("第二个元素定位并点击成功")
                else:
                    print("两个元素均未定位或点击失败")
                    return False
        except NoSuchElementException:
            return False


if __name__ == '__main__':
    pytest.main()






