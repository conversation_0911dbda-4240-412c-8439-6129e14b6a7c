"""
 @Author: yangping
 @Date: 2024/1/16
"""
from ddt import ddt
from .base import FundsBaseTestCase
from selenium import webdriver
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException


@ddt
class TestAccountCenter(FundsBaseTestCase):

    def test_self_account_center_info(self):
        """ 账户中心-页面信息展示"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.driver.refresh()
        self.sleep(5)
        # 断言页面元素是否存在
        # 判断是否有安心钱包充值方式
        self.assert_text('可用金额', "//div[@class='text___PN6KL']//span[contains(text(),'可用金额')]")
        self.assert_text('提现中金额', "//span[contains(text(),'提现中金额')]")
        self.assert_text('限制提现金额', "//span[contains(text(),'限制提现金额(元)')]")
        self.assert_text('安心钱包', "//span[@class='safe-wallet___zb6ZM']")
        self.assert_text('其他账户', "//div[contains(text(),'其他账户')]")
        self.assert_text('资金小助手', "//div[@class='helperTitle___U4opD']")
        self.assert_text('店铺保证金', "//span[contains(text(),'店铺保证金')]")
        self.assert_text('推广保证金',"//span[contains(text(),'推广保证金')]")
        self.assert_text('退货补运费账户', "//span[contains(text(),'退货补运费账户')]")
        # self.assert_text('宠爱红包', '//*[@id="root"]/div/article/div/div[3]/div[3]/div/div[1]/section/div[1]')

    # todo 平台系统问题，解决中
    @pytest.mark.skip
    def test_self_account_center_green_right(self):
        """ 绿色通道页面"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/green-right"
        self.open(url)
        self.driver.refresh()
        self.sleep(5)
        # 断言页面元素是否存在
        # 判断是否有安心钱包充值方式
        self.assert_text('缩短订单自动确认收货时间', "//span[contains(text(),'缩短订单自动确认收货时间')]")
        self.assert_text('帮助资金周转', "//span[contains(text(),'帮助资金周转')]")
        self.assert_text('降低资金流转压力', "//span[contains(text(),'降低资金流转压力')]")
        # self.click('绿色通道权益', "// span[contains(text(), '绿色通道权益')]")
        self.sleep(5)

        # 点击重启服务
        self.assert_text('重启服务', "//*[@id='root']/div/div/footer/button/span")
        # self.click("//button[@class='zone-fund-accounting-btn zone-fund-accounting-btn-primary OFgEYWjHIgieXdukX59o']")


    def test_self_threshold_record(self):
        """账户中心-资金管控记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.driver.refresh()
        self.sleep(3)
        # 点击限制提现金记录入口
        self.click_if_visible("//span[contains(text(),'停提现记录')]")
        self.sleep(3)
        self.assert_text('账户资金管控记录', "//div[@class='title-style___LHx4K']")
        # 切换tab
        self.click("//div[@id='rc-tabs-0-tab-threshold']")
        self.assert_text('限制账户', "//div[@id='rc-tabs-0-panel-threshold']//th[@class='zone-fund-accounting-table-cell'][contains(text(),'限制账户')]")

    def test_self_account_bill_01(self):
        """账户中心-安心钱包-提现记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        # 点击提现记录，校验提现弹窗内容
        # self.click('//*[@id="root"]/div/article/div/div[1]/div[4]/div[1]/div[1]/div/div[2]/div[1]/div[1]/span[2]/button[1]/span')
        self.click("(//span[contains(text(),'提现记录')])[2]")
        try:
            element2 = WebDriverWait(self.driver, 3).until(EC.presence_of_element_located((By.XPATH, "//div[@id='rcDialogTitle0']")))
            self.assert_text('提现记录', "//div[@id='rcDialogTitle0']")
        except TimeoutException:
            print("有弹窗")
            self.driver.quit()


    def test_self_account_bill_03(self):
        """账户中心-安心钱包-收支明细"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.driver.refresh()
        self.sleep(3)
        # 点击收支明细
        # self.click('//*[@id="root"]/div/article/div/div[1]/div[4]/div[1]/div[1]/div/div[2]/div[1]/div[1]/span[2]/button[2]/span')
        self.click("(//span[contains(text(),'收支明细')])[1]")
        self.sleep(3)
        # 校验页面跳转成功
        self.assert_text('安心钱包资金账单明细', "//div[@class='title-style___LHx4K']")
        self.assert_text('账户状态', "//span[contains(text(),'账户状态')]")
        # 点击日账单
        self.click("//div[@id='rc-tabs-0-tab-DAILY']")
        self.assert_text('到账日期', '//*[@id="rc-tabs-0-panel-DAILY"]/form/div[1]/div[1]/label')
        self.click('//*[@id="rc-tabs-0-panel-DAILY"]/form/div[2]/div/div/div/div/div[1]/button/span')
        self.sleep(2)
        # 点击月账单
        self.click('//*[@id="rc-tabs-0-tab-MONTH"]')
        # 点击财务明细
        self.click('//*[@id="rc-tabs-0-tab-DETAIL"]')
        self.assert_text('业务类型', '//*[@id="rc-tabs-0-panel-DETAIL"]/div[1]/form/div[1]/div[1]/label')
        # self.click('//*[@id="rc-tabs-0-panel-DETAIL"]/div[1]/form/div[4]/div/div/div/div/div[2]/button/span')
        # self.sleep(3)
        # self.assert_text('已导出的报表','//*[@id="root"]/div/div/div[1]')


    def test_self_account_bill_04(self):
        """账户中心-安心钱包-磁力金牛"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.driver.refresh()
        self.sleep(3)
        # 点击转入磁力金牛
        # self.click('//*[@id="root"]/div/article/div/div[1]/div[4]/div[1]/div[1]/div/div[2]/div[1]/div[3]/div/button')
        self.click("(//span[contains(text(),'转入金牛账户')])[1]")
        self.sleep(5)
        # 校验页面跳转成功  断言最好不要用顶部文案
        self.assert_text('转入金额', "//label[contains(text(),'转入金额')]")
        self.type('//*[@id="amount"]', 1)
        self.type('//*[@id="verificationCode"]', 1111111)
        self.click('//*[@id="root"]/div/div/footer/button')
        self.sleep(3)

    def test_self_account_bill_05(self):
        """账户中心-安心钱包-换绑卡"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.driver.refresh()
        self.sleep(3)
        self.click("(//span[contains(text(),'换绑卡')])[1]")
        self.sleep(3)
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//*[@id="rcDialogTitle0"]')))
            print("换绑卡弹窗 弹出")
            self.assert_text('绑定&换绑银行卡', '//*[@id="rcDialogTitle0"]')
        except TimeoutException:
            print("有遮挡")
            self.driver.quit()


    def test_self_account_bill_07(self):
        """账户中心-安心钱包-资金小助手"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.driver.refresh()
        self.sleep(3)
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        self.sleep(3)
        # self.assert_text('资金小助手', '//*[@id="root"]/div/article/div/div[3]/div/div[1]/div[2]'
        self.assert_text('资金小助手', "//div[@class='helperTitle___U4opD']")



    def test_self_account_bill_08(self):
        """账户中心-店铺保证金"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.maximize_window()
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        self.driver.refresh()
        self.sleep(3)
        # 滑动页面
        # 控制滚动条到最下方
        # 1、定义js
        js = "window.scrollTo(0, 2000)"
        # 2、执行JS
        self.driver.execute_script(js)
        self.sleep(3)
        # self.assert_text('充值', '//*[@id="root"]/div/article/div/div[3]/div[2]/div[1]/div[3]/div/div[1]/a/span')
        # self.assert_text('提现', '//*[@id="root"]/div/article/div/div[3]/div[2]/div[1]/div[3]/div/div[2]/a/span')
        # self.assert_text('查看明细', '//*[@id="root"]/div/article/div/div[3]/div[2]/div[1]/div[3]/div/div[3]/button/span')
        # self.click('/*[@id="root"]/div/article/div/div[3]/div[2]/div[1]/div[3]/div/div[3]/button/span')
        # self.sleep(3)
        # self.assert_text('店铺保证金', '//*[@id="root"]/div/div/div[2]/div[1]/div[1]/div[1]/div[1]/span')

    def test_self_account_bill1_08(self):
        """账户中心-供应商钱包元素存在"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        self.open(url)
        self.sleep(2)
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # 平台运行时以小窗口模式，按钮此时是invisible的，需要通过js_click
        # self.js_click('//*[@id="root"]/div/article/div/div[4]/div[3]/div[3]/div[1]/section/div[1]')
        self.js_click("//span[contains(text(),'查看钱包')]")
        self.sleep(2)

    def test_self_account_bill_09(self):
        """账户中心-供应商钱包账户中心"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/provider_fund/accounting/overview?accountRole=ACCOUNT_ROLE_SUPPLIER_RECEIVER&userRole=USER_ROLE_SUPPLIER_RECEIVER&roleType=DELIVERY_SUPPLIER"
        self.open(url)
        self.sleep(2)
        self.assert_text('账户中心', '//*[@id="root"]/article/div/div/div[1]')

    def test_self_account_bill_10(self):
        """账户中心-供应商钱包提现明细"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/provider_fund/accounting/overview?accountRole=ACCOUNT_ROLE_SUPPLIER_RECEIVER&userRole=USER_ROLE_SUPPLIER_RECEIVER&roleType=DELIVERY_SUPPLIER"
        self.open(url)
        self.sleep(2)
        # 点击进入提现明细弹窗
        self.click('//*[@id="root"]/article/div/div/div[3]/div[1]/div[1]/div/div[2]/div[1]/div[1]/span[2]/button[1]/span')
        self.sleep(2)

    def test_self_account_bill_11(self):
        """账户中心-供应商安心钱包账单明细"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/provider_fund/accounting/overview?accountRole=ACCOUNT_ROLE_SUPPLIER_RECEIVER&userRole=USER_ROLE_SUPPLIER_RECEIVER&roleType=DELIVERY_SUPPLIER"
        self.open(url)
        self.sleep(2)
        # 点击 安心钱包查看明细
        self.click('//*[@id="root"]/article/div/div/div[3]/div[1]/div[2]/div/div[1]/button/span')
        self.sleep(2)
        self.assert_text('安心钱包资金账单明细','//*[@id="root"]/article/div/div[1]/div[1]')
        # 点击日账单
        self.click('//*[@id="root"]/article/div/div[2]/div/div[1]/div[1]/div/div[1]')
        # 查询日账单数据
        self.click('/html/body/div[1]/div/div/div[1]/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div/div[1]/form/div[2]/div/div/div/div/div[1]/button')
        # 点击月账单
        self.click('/html/body/div[1]/div/div/div[1]/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[1]/div[1]/div/div[2]/div')
        # 点击财务明细
        self.click('/html/body/div[1]/div/div/div[1]/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[1]/div[1]/div/div[3]/div')
        # 查询财务明细
        self.click('/html/body/div[1]/div/div/div[1]/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div/div[3]/div[1]/form/div[4]/div/div/div/div/div[4]/button')
        self.sleep(2)
        # 导出记录
        self.click('/html/body/div[1]/div/div/div[1]/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div/div[3]/div[1]/form/div[4]/div/div/div/div/div[2]/button')
        self.sleep(3)

    def test_self_account_bill_12(self):
        """账户中心-供应商安心钱包-换绑卡"""
        self.login("funds_account_03")
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/provider_fund/accounting/overview?accountRole=ACCOUNT_ROLE_SUPPLIER_RECEIVER&userRole=USER_ROLE_SUPPLIER_RECEIVER&roleType=DELIVERY_SUPPLIER"
        self.open(url)
        self.sleep(2)
        # 点击 换绑卡
        self.click('//*[@id="root"]/article/div/div/div[3]/div[1]/div[1]/div/div[2]/div[2]/div[1]/button')
        self.sleep(2)

    def test_self_account_bill_13(self):
        """账户中心-B补"""
        self.login("funds_account_04")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/recovery"
        self.open(url)
        self.sleep(3)
        self.assert_text('待缴纳管理', '//*[@id="root"]/div/div/div[1]/div[1]/div/div')
        self.assert_text('缴纳明细', '//*[@id="root"]/div/div/div[2]/div[1]/div/div')

    def test_self_account_bill_14(self):
        """账户中心-账号申诉历史界面"""
        self.login("funds_account_03")
        url = "http://s.kwaixiaodian.com/zone/fund/accounting/account-appeal-history"
        self.open(url)
        self.sleep(3)
        self.assert_text('申诉单号', '//*[@id="root"]/div/div/div/div[3]/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_element('//*[@id="root"]/div/div/div/div[3]/div/div/div/div/div/div/table/tbody/tr[1]/td[8]/div/div/button/span')

    def test_self_account_bill_15(self):
        """账户中心-资金工具"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/financial-tool"
        self.open(url)
        self.sleep(3)
        self.assert_text('分期免息', '//*[@id="root"]/div/div/div[2]/div/div/div[1]/div[1]/span/span[1]')
        self.assert_text('极速回款', '//*[@id="root"]/div/div/div[1]/div[1]')
        self.assert_element('//*[@id="root"]/div/div/div[2]/div/div/div[1]/div[3]/button/span')
        self.assert_element('//*[@id="root"]/div/div/div[1]/div[2]/div/div[2]/button/span')

    def test_self_account_bill_16(self):
        """账户中心-代扣授权记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/loan/authorization"
        self.open(url)
        self.sleep(3)
        self.assert_text('代扣授权记录', '//*[@id="root"]/div/article/div/div[3]/div[1]')
        self.assert_text('1', '//*[@id="root"]/div/article/div/div[3]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[1]')
        self.assert_element('//*[@id="root"]/div/article/div/div[3]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[10]/button/span')

    def test_self_account_bill_17(self):
        """账户中心-收款设置"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/pay-method"
        self.open(url)
        self.sleep(3)
        self.assert_text('收款设置', '//*[@id="root"]/div/div/div[1]')
        self.assert_text('微信收款','//*[@id="root"]/div/div/div[2]/div[2]/div/div[1]/span[1]')
        self.assert_element('//*[@id="root"]/div/div/div[2]/div[1]/div/div[2]/div[1]/div/div[3]/div/div/button/span')

    def test_self_account_bill_18(self):
        """账户中心-账户开通记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/account-opening-log"
        self.open(url)
        self.sleep(3)
        self.assert_text('账户开通记录', '//*[@id="root"]/div/div/div[1]')
        self.assert_text('主体账号','//*[@id="root"]/div/div/div[2]/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')

    def test_self_account_bill_19(self):
        """账户中心-生意卡"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/business-card"
        self.open(url)
        self.sleep(3)
        self.assert_text('生意卡', '//*[@id="root"]/div/div/div[1]/div[1]/div')
        self.assert_text('立即开通','//*[@id="root"]/div/div/div[1]/button/span')

    def test_self_account_bill_20(self):
        """账户中心-待缴纳管理"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/recovery"
        self.open(url)
        self.sleep(3)
        self.assert_text('待缴纳管理', '//*[@id="root"]/div/div/div[1]/div[1]/div/div')
        self.assert_text('待缴纳金额(元)','//*[@id="root"]/div/div/div[1]/div[3]/div[1]/div[1]/span[1]')
        self.assert_text('缴纳类型','//*[@id="root"]/div/div/div[2]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_element('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button/span')
        self.assert_element('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[1]/button/span')
        self.assert_element('//*[@id="root"]/div/div/div[2]/div[3]/div[1]/div[2]/div[1]/button/span')
        self.assert_element('//*[@id="root"]/div/div/div[2]/div[3]/div[1]/div[2]/div[2]/button/span')
        self.assert_element('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/div/div')

    def test_self_account_bill_21(self):
        """账户中心-跨境订单管理"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/orders"
        self.open(url)
        self.sleep(3)
        self.assert_text('结算明细', '//*[@id="root"]/div/div/div[2]/div/div[1]')
        self.assert_text('订单信息','//*[@id="tableId"]/div/div[1]/table/thead/tr/th[1]/span')
        self.assert_text('已结算','//*[@id="root"]/div/div/div[2]/div/div[2]/div/div/div[1]/div/div[1]/span')
        self.click('//*[@id="orderId"]')
        self.input('//*[@id="orderId"]',"1223")
        self.click('//*[@id="batchNo"]')
        self.input('//*[@id="batchNo"]',"1223")
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div[3]/input')
        self.click('//*[@id="rc-tabs-0-tab-2"]/div')
        self.sleep(3)
        self.click('//*[@id="rc-tabs-0-tab-3"]/div')

    def test_self_account_bill_22(self):
        """账户中心-小店余额"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/deal/records?accountChannel=BALANCE"
        self.open(url)
        self.sleep(3)
        # 获取当前所有句柄，返回一个列表
        handles = self.driver.window_handles
        print(handles)
        # 切换到最新打开的窗口
        self.driver.switch_to.window(handles[-1])
        self.assert_text('订单编号', '//*[@id="root"]/section/main/div/form/div[1]/div[1]/div/div[1]/label')
        self.assert_text('订单信息', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('结算时间', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('入账金额', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('提现记录', '//*[@id="root"]/section/main/div/div[2]/div/div/div/div[1]/div[2]/a/button/span')

    def test_self_account_bill_23(self):
        """账户中心-小店余额-提现记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/deal/records?accountChannel=BALANCE"
        self.open(url)
        self.sleep(3)
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div/div/div[1]/div[2]/a/button')
        self.sleep(3)
        # 获取当前所有句柄，返回一个列表
        handles = self.driver.window_handles
        print(handles)
        # 切换到最新打开的窗口
        self.driver.switch_to.window(handles[-1])
        self.assert_text('提现流水号', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[1]')
        self.assert_text('收款账户', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[2]')
        self.assert_text('提现时间', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[3]')
        self.assert_text('到账时间', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[4]')
        self.assert_text('提现状态', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[6]')




if __name__ == '__main__':
    pytest.main()