"""
 @Author: yin<PERSON><PERSON>
 @Date: 2022/12/14
"""
from ddt import ddt
from .base import FundsBaseTestCase
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException


@ddt
class TestReceiptPayFunds(FundsBaseTestCase):
    @pytest.mark.skip("账号问题")
    def test_receipt_negative_pay(self):
        """ 收款中心-负向罚单主动支付"""
        self.login("funds_account_01")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/pay?originUrl=https://prt-eshop-s.test.gifshow.com//&receiptNo=*****************"
        self.open(url)
        self.sleep(3)
        self.assert_text('0.01', '//*[@id="root"]/article/article/main/section[1]/p/span')
        # self.assert_text('微信或支付宝扫码支付', '//*[@id="root"]/article/article/main/section[2]/div/div/label[1]/span[2]/article/section/span[1]')
        # 点击支付按钮
        self.js_click('//*[@id="root"]/article/article/footer/div/article/section[2]/button')
        self.sleep(3)
        self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
        self.assert_text('0.01', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")


    def test_receipt_negative_pay02(self):
        """ 收款中心-负向罚单"""
        self.login("funds_account_01")
        self.open('https://s.kwaixiaodian.com/zone/fund/accounting/recovery')
        self.sleep(3)
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div/div[2]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/button[2]')))
            print("有待缴纳罚单，继续操作")
            self.click(element)
            self.sleep(2)
        except TimeoutException:
            print("无待缴纳罚单，终止流程")
            self.driver.quit()


if __name__ == '__main__':
    pytest.main()
