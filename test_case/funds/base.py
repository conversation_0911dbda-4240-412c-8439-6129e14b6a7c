"""
 @Author: yin<PERSON><PERSON>
 @Date: 2022/12/13
"""
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase, get_driver
from selenium import webdriver
from time import sleep
from json import dumps
# from seleniumwire import webdriver


class FundsBaseTestCase(BaseCase):

    def setUp(self):
        super(FundsBaseTestCase, self).setUp()
        # self.maximize_window()
        # 以下设置泳道方法都不生效
        # lane_id = "PRT.test"
        # get_driver().add_cookie({"name": "trace-context", "value": 'laneId:PRT.test'})
        # self.open("https://webdriver.modheader.com/add?trace-context=%7B%22laneId%22%3A%22{}%22%7D".format(lane_id))
        # options = get_driver().create_options()
        # options.add_argument("trace-context={'laneId':'PRT.test'}")

    def login(self, account):
        # self.driver.quit()
        # self.driver = webdriver.Chrome()
        # self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.maximize_window()
        account_data = get_account_info(account)
        host = get_domain('FUNDS_PRT_DOMAIN')
        self.open(host)
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        # self.assert_text("扫码登录", '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]')  # div标签中的第一个元素
        # self.assert_text("手机号登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]')
        # sleep(5)
        # self.type("input[placeholder='请输入手机号']", account_data['account'])
        # self.type("input[placeholder='请输入密码']", account_data['password'])
        # # self.click("button[type='button']")
        # self.click("//*[@id='root']/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button")

        self.driver.maximize_window()
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

        # 同一手机号绑定多个账号时需要二次点击登录
        self.sleep(1)
        self.click_if_visible('//*[@id="root"]/div/div[2]/div/div[1]/div/div[2]/div[3]/div[1]')
        # self.click_if_visible('//*[@id="root"]/div/div[2]/div/div/div/div[4]/div[1]/div[1]/div[1]')
        # self.click_if_visible('//*[@id="root"]/div/div[2]/div/div/div/div[4]/div[2]/button')
        # 隐藏新功能引导弹窗
        # merchant_seller_pc_driver_key1 = "merchantSellerPCDriver_333333323455_" + account_data['uid']
        # merchant_seller_pc_driver_key2 = "merchantSellerPCDriver_333333323445_" + account_data['uid']
        # merchant_seller_pc_driver_key3 = "merchantSellerPCDriver_33333332344544342_" + account_data['uid']
        # merchant_seller_pc_driver_key4 = "merchantSellerPCDriver_3333333232445_" + account_data['uid']
        # merchant_seller_pc_driver_key5 = "merchantSellerPCDriver_33333332344544342_" + account_data['uid']
        # merchant_seller_pc_driver_key6 = "merchantSellerPCDriver_kwaishop-seller-home-e2e-pc_post-manage_" + account_data['uid']
        # merchant_seller_pc_driver_key7 = "merchantSellerPCDriver_2222222_" + account_data['uid']
        # self.set_local_storage_item(merchant_seller_pc_driver_key1, 1)
        # self.set_local_storage_item(merchant_seller_pc_driver_key2, 1)
        # self.set_local_storage_item(merchant_seller_pc_driver_key3, 1)
        # self.set_local_storage_item(merchant_seller_pc_driver_key4, 1)
        # self.set_local_storage_item(merchant_seller_pc_driver_key5, 1)
        # self.set_local_storage_item(merchant_seller_pc_driver_key6, 1)
        # self.set_local_storage_item(merchant_seller_pc_driver_key7, 1)
        # modal_value = dumps({str(account_data['uid']): {"value": 1, "maxAge": 0, "createTime": int(time()*1000)}})
        # self.set_local_storage_item("workbench-dirver-modal", modal_value)
        # self.refresh()
        self.sleep(1)
        # 新手引导点击
        for i in range(1, 5):
            sleep(0.5)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
                sleep(0.5)

        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")



