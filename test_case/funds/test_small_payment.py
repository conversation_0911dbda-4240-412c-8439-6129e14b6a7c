"""
 @Author: yangping
 @Date: 2024/06/28
"""
from ddt import ddt
from .base import FundsBaseTestCase
import pytest


@ddt
class TestSmallPayment(FundsBaseTestCase):

    def test_samll_payment_01(self):
        """ 小额打款-查询订单-发起小额打款"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/small-payment"
        self.open(url)
        self.sleep(2)
        # 小额打款功能介绍，点击立即使用
        if self.is_element_visible('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span'):
            self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span')
        # 判断是否进入小额打款页面
        self.assert_text('当前可用小额打款金额', '//*[@id="rc-tabs-0-panel-1"]/article/header/div[1]/h3')
        # 输入订单号
        self.type('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[1]/article/main/div[1]/form/div[1]/div[2]/div/div/input', ****************)
        self.sleep(1)
        # 点击查询
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[1]/article/main/div[1]/form/div[2]/div/div/div/div/div[1]/button/span")
        # 点击发起打款
        self.click('//*[@id="rc-tabs-0-panel-1"]/article/main/div[3]/div/div/div/div/div/table/tbody/tr/td[8]/button')
        self.sleep(1)
        # 判断弹窗是否出现
        self.assert_element('//*[@role="document"]')
        self.sleep(2)


    def test_samll_payment_02(self):
        """ 小额打款-打款记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/small-payment?tabId=2"
        self.open(url)
        self.sleep(2)
        # 小额打款功能介绍，点击立即使用
        if self.is_element_visible(
                '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span'):
            self.click(
                '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span')
        self.sleep(2)
        # 判断是否进入打款记录页面
        self.assert_text('订单编号', '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div[1]/form/div[1]/div[1]/div/div[1]/label')
        # 查询默认时间段打款记录
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div[1]/form/div[4]/div/button[1]")
        self.sleep(1)
        # 查询结果重置
        self.click("//*[@id='rc-tabs-0-panel-2']/div[1]/form/div[4]/div/button[2]")

    def test_samll_payment_03(self):
        """ 小额打款-打款记录-导出"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/small-payment?tabId=2"
        self.open(url)
        self.sleep(2)
        # 小额打款功能介绍，点击立即使用
        if self.is_element_visible(
                '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span'):
            self.click(
                '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span')
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div[1]/form/div[4]/div/button[3]")
        self.sleep(1)
        # 断言导出弹窗
        self.assert_element('//*[@class="fund-flow-pc-modal-content"]')
        self.sleep(2)


    def test_samll_payment_04(self):
        """ 小额打款-打款记录-导出记录下载"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/small-payment?tabId=2"
        self.open(url)
        self.sleep(2)
        # 小额打款功能介绍，点击立即使用
        if self.is_element_visible(
                '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span'):
            self.click(
                '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span')
        # 点击导出记录
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[2]/div[1]/form/div[4]/div/button[4]")
        self.sleep(2)
        # 断言页面跳转
        self.assert_text('已生成小额打款报表', '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[1]')
        self.sleep(2)
        # 点击下载
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div[1]/div/ul/div[1]/div[1]/div/a")
        self.sleep(2)
        #  后面需要增加下载弹窗校验，现在该账号验证这个场景 会报异常

    def test_samll_payment_05(self):
        """ 小额打款-待处理打款申请"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/small-payment?tabId=3"
        self.open(url)
        self.sleep(2)
        # 小额打款功能介绍，点击立即使用
        if self.is_element_visible('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span'):
            self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span')
        # 输入订单号
        self.type('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[3]/div[1]/form/div/div[1]/div/div[2]/div/span/input', ****************)
        # 点击查询
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[3]/div[1]/form/div/div[3]/div/div/div/span/button[1]")
        # 断言结果为空
        self.assert_text("暂无数据","/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[3]/div[2]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td/div")
        self.sleep(2)

    def test_samll_payment_06(self):
        """ 小额打款-打款配额"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/small-payment?tabId=4"
        self.open(url)
        self.sleep(2)
        # 小额打款功能介绍，点击立即使用
        if self.is_element_visible('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span'):
            self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div/div/div/button/span')
        self.sleep(2)
       #断言是否是否进到打款配额页面
        self.assert_text("子账号名称","/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[4]/div/div[2]/div/div/div/div/div/table/thead/tr/th[1]")
        # 新建打款配额
        self.click("/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/article/div/div[2]/div/div[4]/div/button")
        # 断言打款配额弹窗
        self.assert_element('//*[@class="fund-flow-pc-modal-content"]')
        self.sleep(2)



if __name__ == '__main__':
    pytest.main()
