"""
 @Author: yangping
 @Date: 2025/06/19
"""
from ddt import ddt
from .base import FundsBaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.common.exceptions import StaleElementReferenceException
import pytest

import time

from test_case.assistant.base import BaseCase

@ddt
class TestInvoice(FundsBaseTestCase):

    def test_subsidy_01(self):
        """ 以旧换新- 首页"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        self.assert_text('开票规则', '//*[@id="root"]/section/main/div/div/div/div/div[1]/div/div[1]')
        self.assert_element('//*[@id="root"]/section/main/div/div/div/div/div[2]/div[1]/span')
        self.sleep(3)
        self.assert_text('广州宜山科技有限公司', '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[14]/div')

    def test_subsidy_02(self):
        """ 以旧换新- 提交发票"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        """点击提交发票"""
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[1]')))
        except TimeoutException:
            print("无处理中账单，终止流程")

    def test_subsidy_04(self):
        """ 以旧换新-提交开票信息"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        self.sleep(3)
        self.input('//*[@id="relateOrderNo"]', '*************')
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div[1]/div[2]/button')
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]')))
            if element.is_displayed():
           # 对元素进行操作，例如点击
                self.assert_text('提交开票信息', '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]')
                element.click()
                print("已点击提交开票按钮")
                self.sleep(3)
        except StaleElementReferenceException:
            print("重试一次")
            element = self.driver.find_element(*(By.XPATH, '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]'))
            element.click()


    def test_subsidy_05(self):
        """ 以旧换新-查看审计信息"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        self.sleep(3)
        self.input('//*[@id="relateOrderNo"]', '****************')
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div[1]/div[2]/button')
        try:
            element1 = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH,
                                                '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]')))
            if element1.is_displayed():
                # 对元素进行操作，例如点击
                self.assert_text('查看审计信息',
                                 '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]')
                element1.click()
                print("已点击查看审计信息")
                self.sleep(3)
        except StaleElementReferenceException:
            # 重试一次
            print("重试一次")
            element1 = self.driver.find_element(*(By.XPATH,
                                                '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]'))
            element1.click()

    def test_subsidy_06(self):
        """ 以旧换新-提交政府审核"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        self.sleep(3)
        element = self.driver.find_element(By.ID, 'main-root')
        self.input('//*[@id="relateOrderNo"]', '***************')
        # 查询
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div[1]/div[2]/button')
        # 点击提交政府审核按钮
        self.click('//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[2]')
        # 出现弹窗 校验元素
        # 定位元素
        element1 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'ant-modal-confirm-title')))
        # 操作元素，获取文本
        print(element1.text)

    def test_subsidy_07(self):
        """ 以旧换新-补录审计信息"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        self.sleep(3)
        element = self.driver.find_element(By.ID, 'main-root')
        self.input('//*[@id="relateOrderNo"]', '***************')
        # 查询
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div[1]/div[2]/button')
        print('已点击查询')
        # 点击补录审计信息
        element1 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[3]')))
        element1.click()
        print('出现弹窗')
        # 出现弹窗
        element2 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'ant-modal-title')))
        # 操作元素，获取文本
        print(element2.text)

    def test_subsidy_08(self):
        """ 以旧换新-修改开票信息"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        self.sleep(3)
        self.input('//*[@id="relateOrderNo"]', '************')
        # 查询
        self.click('//*[@id="pro-form-wrapper"]/div[4]/div[4]/div/div[1]/div[2]/button')
        print('已点击查询')
        # 点击补录审计信息
        element1 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.XPATH,
                                            '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[4]/div/div/div/div/div/table/tbody/tr[2]/td[24]/div/button[4]')))
        element1.click()
        element2 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'ant-drawer-title')))
        print(element2.text)

    def test_subsidy_09(self):
        """ 以旧换新-下载列表"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        # 点击下载列表
        element1 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.XPATH,
                                            '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[3]/button[2]')))
        element1.click()

    def test_subsidy_10(self):
        """ 以旧换新-批量提交/修改发票信息"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/pay-later-subsidy"
        self.open(url)
        # 点击批量提交/修改发票信息
        element1 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.XPATH,
                                                '//*[@id="root"]/section/main/div/div/div/div/div[2]/div[3]/button[3]')))
        element1.click()
        self.sleep(3)




if __name__ == '__main__':
    pytest.main()
