"""
 @Author: yin<PERSON><PERSON>
 @Date: 2022/12/14
"""
from ddt import ddt
from .base import FundsBaseTestCase
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


from test_case.assistant.base import BaseCase



@ddt
class TestPaymentBill(FundsBaseTestCase):

    def test_payment_bill_01(self):
        """ 结算账单-货款账单"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/payment-bill"
        self.open(url)
        self.sleep(3)
        self.assert_text('1. 货款账单包含在快手电商场景下，交易订单维度数据（订单结算及退款），不包含提现、扣款、转账等记录，因此不可作为账户余额对账依据。', '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div[1]/section/p[1]')

    def test_payment_bill_02(self):
        """ 结算账单-货款账单-查询"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/payment-bill"
        self.open(url)
        self.sleep(3)
        self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div[3]/div[1]/div/form/div[2]/div[2]/div/div/div[2]/button')

    # 在处理
    def test_payment_bill_03(self):
        """ 结算账单-货款账单-资金小助手"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/payment-bill"
        self.open(url)
        self.sleep(3)
        # 校验文案
        self.assert_text('资金小助手', '//*[@id="root"]/div/div/div/div/div[2]/div/div[1]/div[2]')
        # 点击具体问题
        # 定位元素
        element1 = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div/div/div/div[2]/div/div[2]/div/div[1]/div[1]')))
        element1.click()
        self.sleep(3)


    def test_payment_bill_04(self):
        """ 结算账单-货款账单-导出"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/payment-bill"
        self.open(url)
        self.sleep(3)
        # 点击导出
        self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div[3]/div[2]/div/div/div[1]/div/div/div/div/div[2]/div/div[2]/button')
        self.sleep(2)

    def test_payment_bill_05(self):
        """ 结算账单-货款账单-导出记录"""
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/payment-bill"
        self.open(url)
        self.sleep(3)
        # 点击导出记录
        self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div[3]/div[2]/div/div/div[1]/div/div/div/div/div[2]/div/div[1]/button')
        # 校验文案
        self.assert_text('已导出的报表', '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div[1]')
        self.sleep(2)


    def test_payment_bill_06(self):
        """ 超售后结算账单-货款账单-导出记录"""
        # 每天20-40分执行一次
        cur_min = int(time.strftime('%M', time.localtime(time.time())))
        if cur_min < 20 or cur_min > 40:
            return
        self.login("funds_account_01")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/payment-bill"
        self.open(url)
        self.sleep(3)
        # 点击结算后退款订单
        # self.click('//*[@id="root"]/div/div[2]/div/div/ul/li[4]')
        self.click('//*[@id="root"]/div/div/div/div/ul/li[4]')
        # 校验文案
        self.assert_text('超售后期退款', '//*[@id="rc-tabs-0-tab-1"]')
        self.sleep(2)
        # 输入时间
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(2)
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div[1]/input', '2023-11-11 00:00:00')
        # 输入退款单号
        # self.click('/html/body/div[5]/div/div/div/div[2]/div[2]/ul/li/button')
        self.click("//button[@class='zone-fund-accounting-btn zone-fund-accounting-btn-primary zone-fund-accounting-btn-sm']")
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/span/input','****************')
        # 查询+导出
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button')
        self.sleep(2)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button')
        self.sleep(2)
        # self.assert_text('导出成功',"//span[contains(text(),'导出成功')]")
        # self.click("//section[@class='IS2ix6yD0jjKEr2tE70C']//div[@class='zone-fund-accounting-space zone-fund-accounting-space-horizontal zone-fund-accounting-space-align-center']//div[1]//button[1]")

    def test_crossborder_bill_01(self):
        """ 跨境结算账单-货款账单"""
        self.login("funds_account_22")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/orders"
        self.open(url)
        self.sleep(3)
        self.assert_text('跨境订单货款将由微信，支付宝境外平台进行换汇结算。您在结算明细中看到记录即视为微信，支付宝已发起付款，实际到账时间视不同银行而异  ', '//*[@id="root"]/div/div/div[1]/div/div/div')
        self.assert_text('结算明细', '//*[@id="rc-tabs-0-tab-1"]/div')
        self.assert_text('已发起待执行', '//*[@id="rc-tabs-0-tab-2"]/div')
        self.assert_text('待发起结算', '//*[@id="rc-tabs-0-tab-3"]/div')


    def test_crossborder_bill_02(self):
        """ 跨境结算账单-跨境对账指南"""
        self.login("funds_account_22")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/orders"
        self.open(url)
        self.sleep(3)
        # 点击跨境对账指南
        self.click('//*[@id="root"]/div/div/div[1]/div/div/div/a')
        self.sleep(3)
        self.assert_element('//*[@id="Ec9ee47a6"]')

    def test_crossborder_bill_03(self):
        """ 跨境结算账单-已发起待执行"""
        self.login("funds_account_22")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/orders"
        self.open(url)
        self.sleep(3)
        self.click('//*[@id="rc-tabs-0-tab-2"]/div')

    def test_crossborder_bill_04(self):
        """ 跨境结算账单-查看导出记录"""
        self.login("funds_account_22")
        url = "https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/orders"
        self.open(url)
        self.sleep(3)
        # 查看导出记录
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[1]/div/div/div[3]/div/div/div/div[1]/button')
        self.sleep(3)
        self.assert_text('已导出的报表', '//*[@id="root"]/div/div/div[1]')


if __name__ == '__main__':
    pytest.main()
