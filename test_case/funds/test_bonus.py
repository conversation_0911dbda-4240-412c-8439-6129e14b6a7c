"""
 @Author: yin<PERSON><PERSON>
 @Date: 2022/12/13
"""
from unittest import skip

from ddt import ddt
from .base import FundsBaseTestCase
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException

@ddt
class TestBounsFunds(FundsBaseTestCase):

    def test_bouns_01(self):
        """奖金管理-首页"""
        self.login("funds_account_03")
        url = 'https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus'
        self.open(url)
        self.sleep(2)
        self.assert_text('奖金管理', '//*[@id="root"]/section/main/div/div[1]/h3')
        self.assert_text('待处理金额', '//*[@id="root"]/section/main/div/div[1]/div[2]/div[1]/div/div[1]')
        self.assert_text('审核处理中金额', '//*[@id="root"]/section/main/div/div[1]/div[2]/div[2]/div/div[1]')
        self.assert_text('年框账单', '//*[@id="root"]/section/main/div/section/h3')
        self.assert_text('待提现', '//*[@id="rc-tabs-0-tab-1"]')
        self.assert_text('自动提现', '//*[@id="rc-tabs-0-panel-1"]/div[1]/div')

    def test_bouns_02(self):
        """奖金管理-年框账单"""
        self.login("funds_account_03")
        url = 'https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus'
        self.open(url)
        self.sleep(3)
        current_window_handle = self.driver.current_window_handle
        print("Current Window Handle:", current_window_handle)
        self.assert_text('BILL2450147', '//*[@id="root"]/section/main/div/section/div/section[2]/div/div/div[1]/article/div[1]/div[2]')
        self.click('//*[@id="root"]/section/main/div/section/div/section[2]/div/div/div[1]/article/section[2]/button')
        current_window_handle1 = self.driver.current_window_handle
        print("Current Window Handle1:", current_window_handle1)
        # 切换到最新打开的窗口
        self.driver.switch_to.window(current_window_handle1)
        self.sleep(3)
        self.assert_text('BILL2450147', '//*[@id="root"]/section/main/div/div/div[2]/div/div/form/div[1]/div[2]/div/div')
        self.assert_text('发放成功', '//*[@id="root"]/section/main/div/div/div[2]/div/div/form/div[4]/div[2]/div/div')
        # 点击返回奖金管理
        self.click('//*[@id="root"]/section/main/div/div/div[3]/div/button')
        self.sleep(2)
        self.assert_text('奖金管理', '//*[@id="root"]/section/main/div/div[1]/h3')

    def test_bouns_03(self):
        """奖金管理-首页-链接跳转01"""
        self.login("funds_account_03")
        url = 'https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus'
        self.open(url)
        self.sleep(3)
        self.click('//*[@id="root"]/section/main/div/div[1]/h3/a')
        self.sleep(3)
        current_url = self.driver.current_url
        print(current_url)
        url01 = 'https://docs.qingque.cn/d/home/<USER>'
        self.assertEqual(current_url, url01, '跳转不正确')

    def test_bouns_04(self):
        """奖金管理-首页-链接跳转_奖励金提现大全"""
        self.login("funds_account_03")
        url = 'https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus'
        self.open(url)
        self.sleep(3)
        self.click('//*[@id="root"]/section/main/div/div[1]/h3/a')
        self.sleep(3)
        current_url = self.driver.current_url
        print(current_url)
        url01 = 'https://docs.qingque.cn/d/home/<USER>'
        self.assertEqual(current_url, url01, '跳转不正确')

    def test_bouns_04(self):
        """奖金管理-首页-链接跳转开票指南"""
        self.login("funds_account_03")
        url = 'https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus'
        self.open(url)
        self.sleep(3)
        self.click('//*[@id="root"]/section/main/div/div[1]/div[1]/div/div/a')
        self.sleep(3)
        current_url = self.driver.current_url
        print(current_url)
        url01 = 'https://ppg.m.etoote.com/doodle/WqiBbRRr.html'
        self.assertEqual(current_url, url01, '跳转不正确')

    def test_bouns_05(self):
        """奖金管理-发放记录-查看详情"""
        self.login("funds_account_03")
        url = 'https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus'
        self.open(url)
        self.sleep(3)
        # 点击发放记录
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.sleep(2)
        # 点击查看详情
        self.click('//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/table/tbody/tr[2]/td[14]/button')
        # 弹窗元素
        self.driver.find_elements(By.XPATH, '/html/body/div[9]/div/div[2]/div/div[2]')
        print('可以找到元素')

if __name__ == '__main__':
    pytest.main()