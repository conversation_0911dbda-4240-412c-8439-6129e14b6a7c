"""
 @Author: yangping
 @Date: 2025/04/17
"""
from ddt import ddt
from .base import FundsBaseTestCase
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import pytest

import time

from test_case.assistant.base import BaseCase



@ddt
class TestInvoice(FundsBaseTestCase):

    def test_invoice_01(self):
        """ 在线发票申请-发票管理-开票信息"""
        self.login("funds_account_03")
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/invoice/index"
        self.open(url)
        self.sleep(5)
        self.assert_text('发票管理', '//*[@id="root"]/section/main/article/h2')
        self.assert_text('开票信息', '//*[@id="root"]/section/main/article/section[1]/article/div[1]/div[1]/div/span[2]')
        # 点击修改信息，进入开票信息页面
        self.click('//*[@id="root"]/section/main/article/section[1]/article/div[1]/div[1]/a/div/span')
        self.assert_text('基础信息', '//*[@id="root"]/section/main/div/div/div/div[1]')
        # 点击修改按钮
        self.click('//*[@id="root"]/section/main/div/div/div/div[6]/button')



    def test_invoice_02(self):
        """在线发票申请 - 发票管理 - 发票规则"""
        self.login("funds_account_03")
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/invoice/index"
        self.open(url)
        self.sleep(2)
        self.assert_text('发票规则', '//*[@id="root"]/section/main/article/section[1]/div/div[1]')

    def test_invoice_03(self):
        self.login("funds_account_03")
        """在线发票申请 - 发票管理 - 常见问题"""
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/invoice/index"
        self.open(url)
        self.sleep(2)
        # 点击常见问题按钮
        self.click('//*[@id="root"]/section/main/article/section[2]/div/div[1]/div[3]/div/button[1]')
        # 页面跳转
        self.sleep(2)

    def test_invoice_04(self):
        self.login("funds_account_03")
        """在线发票申请 - 发票管理 - 查看导出记录"""
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/invoice/index"
        self.open(url)
        self.sleep(2)
        # 点击查看导出记录
        self.click('//*[@id="root"]/section/main/article/section[2]/div/div[1]/div[3]/div/button[2]')
        self.sleep(2)
        self.assert_text('导出记录', '//*[@id="rcDialogTitle0"]')

    def test_invoice_05(self):
        self.login("funds_account_03")
        """在线发票申请 - 发票管理 - 申请记录"""
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/invoice/index"
        self.open(url)
        self.sleep(2)
        self.assert_text('申请记录', '//*[@id="rc-tabs-0-tab-2"]')
        # 点击申请记录
        # self.click('//*[@id="rc-tabs-0-tab-2"]"]')
        self.click('//*[@id="root"]/section/main/article/section[2]/div/div[1]/div[1]/div/div[2]')
        self.sleep(2)
        # 跳转到申请记录tab下 断言
        self.assert_text('申请时间', '//*[@id="rc-tabs-0-panel-2"]/div/div[1]/div[1]/div[1]/span')
        self.click('//*[@id="rc-tabs-0-panel-2"]/div/div[1]/div[2]/div[1]/div')

    def test_invoice_06(self):
        self.login("funds_account_03")
        """给平台开票 - 开票规则"""
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/tax-bill/subsidy"
        self.open(url)
        self.sleep(2)
        self.assert_text('开票规则', '//*[@id="root"]/section/main/div/div/div[2]/div/div[1]')
        # 点击发票指南
        self.click('//*[@id="root"]/section/main/div/div/div[2]/div/div[2]/div/div/a[1]')

    def test_invoice_07(self):
        self.login("funds_account_03")
        """给平台开票 - 未开票账单"""
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/tax-bill/subsidy"
        self.open(url)
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div/div[3]/div[2]/div/div[1]/div[1]/div/div[1]')
        # 输入账单编号
        self.type('//*[@id="billId"]', **********)
        self.sleep(2)
        # 点击查询
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        # 点击提交发票
        self.click('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[11]/div/div[1]/button')
        self.sleep(2)

    def test_invoice_08(self):
        self.login("funds_account_02")
        """发票管理 首页"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.sleep(5)
        self.assert_text('平台给我开票（商家）', '//*[@id="root"]/section/main/div/div[2]/div[2]/div[1]/div[2]/div[1]')
        self.assert_text('给平台开票', '//*[@id="root"]/section/main/div/div[2]/div[2]/div[2]/div[2]/div[1]')
        self.assert_text('给达人开票', '//*[@id="root"]/section/main/div/div[2]/div[2]/div[3]/div[2]/div[1]')


    def test_invoice_09(self):
        self.login("funds_account_02")
        """发票管理首页 - 资金小助手"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.sleep(5)
        self.assert_text('平台给商家开票的常见问题', '//*[@id="root"]/section/main/div/div[3]/div/div[2]/div/div[1]/div[1]/div[1]/span')
        self.assert_text('给平台开票被驳回怎么办？', '//*[@id="root"]/section/main/div/div[3]/div/div[2]/div/div[2]/div[1]/div[1]/span')
        self.assert_text('商家给平台开票的常见问题', '//*[@id="root"]/section/main/div/div[3]/div/div[2]/div/div[1]/div[2]/div[1]/span')
        self.assert_text('无法给平台开票怎么办？', '//*[@id="root"]/section/main/div/div[3]/div/div[2]/div/div[2]/div[2]/div[1]/span')

    def test_invoice_10(self):
        self.login("funds_account_02")
        """发票管理首页 - 资金小助手 -点击第一个问题"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.sleep(2)
        # 点击
        self.click('//*[@id="root"]/section/main/div/div[3]/div/div[2]/div/div[1]/div[1]/div[1]/span')
        # 断言 跳转是否成功 没报错
        # self.assert_element('/html/body/div[8]/div/div[2]/div/div[2]/div[1]')
        self.assert_no_js_errors()

    def test_invoice_11(self):
        self.login("funds_account_04")
        """发票管理首页 - 在线发票申请入口"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[1]/div[2]/div[1]')
        self.sleep(2)
        # 断言跳转成功
        self.assert_text('发票管理', '//*[@id="root"]/section/main/article/h2')


    def test_invoice_12(self):
        self.login("funds_account_04")
        """发票管理首页 - 给平台开票"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[2]/div[2]')
        self.sleep(2)
        # 断言跳转成功
        self.assert_text('给平台开票', '//*[@id="root"]/section/main/div/div/div[3]/div[1]/div/span')

    def test_invoice_13(self):
        self.login("funds_account_04")
        """发票管理首页 - 给达人开票"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.sleep(8)
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[3]/div[2]/div[1]')
        # self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[3]/div[2]/div[1]')
        self.sleep(2)
        # 断言跳转成功
        self.assert_text('给达人开票', '//*[@id="root"]/section/main/div/div/div[3]/div[1]/div/span')

    def test_invoice_14(self):
        self.login("funds_account_04")
        """发票管理首页 - 自营开票入口"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[3]/div[2]/div[1]')
        self.sleep(2)
        # 断言不报错
        self.assert_no_js_errors()

    def test_invoice_15(self):
        self.login("funds_account_03")
        """发票管理首页 - 在线发票申请(达人)入口"""
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/index"
        self.open(url)
        element = WebDriverWait(self.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div[2]/div[2]/div[5]/div[2]/div[1]')))
        if element:
            self.assert_text('平台给我开票（达人）', '//*[@id="root"]/section/main/div/div[2]/div[2]/div[5]/div[2]/div[1]')
            self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[5]/div[2]/div[1]')
            self.sleep(3)
            self.assert_text('发票管理', '//*[@id="root"]/section/main/article/h2')
            print('执行成功')
        else:
            return False

    def test_invoice_16(self):
        self.login("funds_account_03")
        """给自营店开票"""
        url = "http://s.kwaixiaodian.com/zone/fund/tax-bill/self-operated-subsidy"
        self.open(url)
        self.sleep(2)
        self.assert_text('给自营店开票', '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div/span')
        self.assert_text('自营货物采购款', '//*[@id="root"]/section/main/div/div/div[3]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[3]/div')
        self.assert_element('//*[@id="customized-form-controls"]/div[2]/div[3]/div/div/div/div/div/div[2]/button')

    def test_talent_subsidy_01(self):
        self.login("funds_account_04")
        """商家给达人开票 """
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/talent-subsidy"
        self.open(url)
        self.sleep(2)
        # 断言
        self.assert_text('开票规则', '//*[@id="root"]/section/main/div/div/div[2]/div/div[1]')
        self.assert_text('待开票', '//*[@id="rc-tabs-0-tab-1"]')

    def test_talent_subsidy_02(self):
        self.login("funds_account_02")
        """商家给达人开票 - 开票指南 """
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/talent-subsidy"
        self.open(url)
        self.sleep(2)
        # 点击开票指南
        self.click('//*[@id="root"]/section/main/div/div/div[3]/div[1]/a')
        self.sleep(2)
        # 断言跳转成功
        self.assert_text('商家给达人开票操作指南', "//span[@class='vodka-wordhtmlgenerator-word-node' and text()='商家给达人开票操作指南']")

    def test_talent_subsidy_03(self):
        self.login("funds_account_03")
        """商家给达人开票 - 提交发票 """
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/talent-subsidy"
        self.open(url)
        print("页面title:", self.get_page_title())
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[11]/a')))
            print("有待处理账单，继续操作")
            self.click('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[11]/a')
            self.sleep(2)
        except TimeoutException:
            print("无待处理账单，终止流程")


    def test_talent_subsidy_04(self):
        self.login("funds_account_03")
        """商家给达人开票 - 筛选 """
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/talent-subsidy"
        self.open(url)
        print("页面title:", self.get_page_title())
        self.type('//*[@id="customized_form_controls_receiverName"]', "宜昌福美园食品有限公司")
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[11]/a')))
            print("查询结果有数据")
            self.click('//*[@id="customized_form_controls"]/div[2]/div[3]/div/div/div/div/div/div[2]/button')
            self.assert_text('宜昌福美园食品有限公司', '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[8]')
            self.sleep(2)
        except TimeoutException:
            print("无待处理账单，终止流程")


    def test_talent_subsidy_05(self):
        self.login("funds_account_03")
        """商家给达人开票 - 查询待处理数据  """
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/talent-subsidy"
        self.open(url)
        self.sleep(2)
        print("页面title:", self.get_page_title())
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        if self.is_element_visible('//*[@id="customized_form_controls_id"]'):
            print('待处理tab下，申请单号元素存在')
            self.type('//*[@id="customized_form_controls_id"]', "**********")
        else:
            pass
        self.type('//*[@id="customized_form_controls_id"]', "**********")
        self.click('//*[@id="customized_form_controls"]/div[2]/div[3]/div/div/div/div/div/div[2]/button')
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[1]')))
            self.assert_text('**********',
                         '//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[1]')
        except TimeoutException:
            print("无处理中账单，终止流程")

    def test_talent_subsidy_06(self):
        self.login("funds_account_04")
        """商家给达人开票 - 查询待处理数据  """
        url = "https://s.kwaixiaodian.com/zone/fund/tax-bill/talent-subsidy"
        self.open(url)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        if self.is_element_visible('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[10]/a'):
            print('有历史数据')
        else:
            pass
        self.click('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[10]/a')
        self.sleep(2)


    def test_pay_result_01(self):
        """给平台开票 - 点击缴纳违约金  """
        self.login("funds_account_03")
        url = "https://eshop-s.prt.kwaixiaodian.com/zone/fund/tax-bill/subsidy"
        self.open(url)
        self.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div/div[3]/div[2]/div/div[1]/div[1]/div/div[1]')
        # 输入账单编号
        self.type('//*[@id="billId"]', **********)
        self.sleep(2)
        # 点击查询
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        self.sleep(2)
        # self.click('//*[@id="root"]/section/main/div/div/div[4]/div[2]/div[3]/div/div/div/div/div/table/tbody/tr[2]/td[11]/div/div[1]/button')
        # self.click('/html/body/div[8]/div/div[2]/div/div[2]/div[3]/button[2]')

    def test_pay_result_02(self):
        """给平台开票 - 缴纳违约金  """
        self.login("funds_account_03")
        url = "https://s.kwaixiaodian.com/zone/fund/flow/pay?originUrl=%2Fzone%2Ffund%2Ftax-bill%2Fsubsidy&receiptNo=******************"
        self.open(url)
        self.sleep(3)
        self.assert_text('发票补贴账单缴税', '//*[@id="root"]/article/article/main/h3')
        self.assert_text('选择付款方式', '//*[@id="root"]/article/article/main/section[2]/h4')


if __name__ == '__main__':
    pytest.main()

