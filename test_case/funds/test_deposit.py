"""
 @Author: y<PERSON><PERSON><PERSON>
 @Date: 2022/12/13
"""
from unittest import skip

from ddt import ddt
from .base import FundsBaseTestCase
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException



@ddt
class TestDepositFunds(FundsBaseTestCase):
    DEPOSIT_URL = "https://s.kwaixiaodian.com/zone/fund/accounting/deposit/recharge/home"
    DARENDEPOSIT_URL = "https://cps.kwaixiaodian.com/pc/promoter/fund/accounting/daren_deposit/recharge/home"
    CROSSBORDER_URL = 'https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/deposit/recharge/home'

    def test_self_deposit_01(self):
        """店铺保证金-页面"""
        self.login("funds_account_03")
        self.sleep(2)
        self.open(self.DEPOSIT_URL)
        self.sleep(2)
        # 校验财务明细
        self.assert_text('全部', "//*[@id='rc-tabs-0-tab-1']")
        self.assert_text('可开收据', "//*[@id='rc-tabs-0-tab-2']")
        # 点击查询
        # self.click('//*[@id="rc-tabs-0-panel-1"]/div[1]/form/div[4]/div/div/div/div/div[4]/button')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        self.sleep(2)

    def test_self_deposit_02(self):
        """店铺保证金-常见问题页面"""
        self.login("funds_account_03")
        self.sleep(2)
        self.open('https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/deposit-commonproblem')
        self.sleep(2)
        # 校验文本显示
        self.assert_text('保证金常见问题', '//*[@id="root"]/section/main/div/div/div[2]')
        self.assert_elements('/html/body/div/div[1]')

    def test_self_deposit_pay_01(self):
        """店铺保证金-扫码支付"""
        self.login("funds_account_22")
        self.sleep(2)
        self.open(self.DEPOSIT_URL)
        self.sleep(2)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[3]/div[1]/button[1]/span')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[1]/div')
        # 平台运行时以小窗口模式，按钮此时是invisible的，需要通过js_click
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
        self.assert_text('1', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")

    def test_self_deposit_pay_02(self):
        """店铺保证金-网银支付"""
        self.login("funds_account_22")
        self.open(self.DEPOSIT_URL)
        self.sleep(2)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[3]/div[1]/button[1]/span')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[2]/div')
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
        self.assert_text('1.01', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")
        self.assert_text('个人网上银行', "/html/body//li[@class='tab-item selected svelte-pzcz0v']")
        self.assert_text('企业网上银行', "/html/body//li[@class='tab-item  svelte-pzcz0v']")
    #
    @skip("被风控暂时下掉该支付方式")
    def test_self_deposit_pay_03(self):
        """店铺保证金-线下打款"""
        self.login("funds_account_01")
        # 点击工作台入口
        self.click('//*[@id="first_menu_for_intersectionObserver_M3ys9CDkE00"]/span/span')
        self.sleep(3)
        self.click('//*[@id="menu_item_wtJ83cDIo1E"]')
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[3]/div[1]/button[1]/span')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/div/div/section[1]/div[2]/div[1]/div[2]/span/span/input', 1)
        # 选择付款方式
        self.click('//*[@id="root"]/div/div/section[2]/div[2]/div[3]/div')
        self.js_click('//*[@id="root"]/div/div/section[3]/button/span')
        self.sleep(2)
        self.assert_text('支付金额', '/html/body/div[3]/div/div/div[2]/div/dl[1]/dt')
        self.assert_text('1', '/html/body/div[3]/div/div/div[2]/div/dl[1]/dd')
        self.assert_text('对公汇款', '/html/body/div[3]/div/div/div[3]/ul/li')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/form/div[2]/div[2]/div/div/div/button')

    def test_self_deposit_recharge_01(self):
        """店铺保证金-进入充值页面"""
        self.login("funds_account_08")
        self.sleep(2)
        self.open(self.DEPOSIT_URL)
        self.sleep(2)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[3]/div[1]/button[1]/span')
        self.sleep(2)
        # 断言
        self.assert_text('店铺保证金充值', '//*[@id="root"]/article/article/div/main/div/h3')

    def test_self_deposit_recharge_02(self):
        """店铺保证金-充值-安心钱包付款 """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/flow/recharge?mode=ACCOUNT_TYPE_DEPOSIT_SHOP'
        self.open(url)
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式-安心钱包
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[3]/div/div/section/div')
        # 点击确认支付
        # 平台运行时以小窗口模式，按钮此时是invisible的，需要通过js_click
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        self.sleep(2)
        # 以下运行case对安心钱包金额有要求， 先不执行，测试账号数据变动
        # # 弹窗
        # self.assert_text('短信验证', '//*[@id="rcDialogTitle0"]')
        # # 输入验证码
        # self.type('//*[@id="sms_sms"]/input', 111111)
        # # 点击确认
        # self.click('//*[@id="sms"]/footer/div/div[2]/button')

    def test_self_deposit_recharge_03(self):
        """店铺保证金-充值页面-保证金充值手册 """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/flow/recharge?mode=ACCOUNT_TYPE_DEPOSIT_SHOP'
        self.open(url)
        self.sleep(2)
        # 点击手册
        self.click('//*[@id="root"]/article/article/div/main/div/a[1]')
        self.sleep(2)

    def test_self_deposit_recharge_04(self):
        """店铺保证金-充值页面- 风险申诉记录 """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/flow/recharge?mode=ACCOUNT_TYPE_DEPOSIT_SHOP'
        self.open(url)
        self.sleep(2)
        # 点击风险申诉记录
        self.click('//*[@id="root"]/article/article/div/main/div/a[2]')
        self.sleep(2)
        # 断言跳转成功
        self.assert_text('账号风险异常申诉', '//*[@id="root"]/div/div/div/div[1]')

    def test_self_deposit_recharge_05(self):
        """店铺保证金-充值结果页 """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/accounting/deposit/recharge/result?method=1&amount=10&mode=ACCOUNT_TYPE_DEPOSIT_SHOP'
        self.open(url)
        self.sleep(2)
        self.assert_text('充值成功', '//*[@id="root"]/div/div/div/div[2]')

    def test_self_deposit_recharge_06(self):
        """店铺保证金-充值结果页-回到首页 """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/accounting/deposit/recharge/result?method=1&amount=10&mode=ACCOUNT_TYPE_DEPOSIT_SHOP'
        self.open(url)
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div[4]/button[2]')
        self.sleep(2)
        self.assert_text('保证金', '//*[@id="root"]/div/div/div[1]/div[1]/span[1]')

    def test_self_deposit_recharge_07(self):
        """
        店铺保证金-充值结果页-继续充值
        点击继续充值，页面跳转后没法断言，因为前端实现点击后是回到上一个页面；自动化是通过直接进到结果页
        """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/accounting/deposit/recharge/result?method=1&amount=10&mode=ACCOUNT_TYPE_DEPOSIT_SHOP'
        self.open(url)
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div[4]/button[1]')
        self.sleep(2)

    def test_self_deposit_recharge_08(self):
        """
        店铺保证金-操作记录导出
        """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'http://s.kwaixiaodian.com/zone/fund/transfer/store/funds/deposit-exportrecord'
        self.open(url)
        self.sleep(2)
        self.assert_text("已导出的历史记录",'//*[@id="root"]/section/main/div/div[1]')
        self.assert_element('//*[@id="root"]/section/main/div/div[2]/div[1]/div/ul/div[1]/div[1]/div/a/span')

    def test_self_deposit_recharge_09(self):
        """
        店铺保证金-保证金阈值查询
        """
        self.login("funds_account_03")
        self.sleep(2)
        url = 'https://s.kwaixiaodian.com/zone/fund/accounting/deposit/threshold'
        self.open(url)
        self.sleep(5)
        self.assert_text("类目选择",'//*[@id="root"]/div/article/div/div[1]/div/span[1]')
        self.assert_element('//*[@id="root"]/div/article/div/div[1]/form/div/div/div[4]/div/div/div/button')


    # @skip("跳过推广充值后续不用了")
    # def test_third_deposit_pay_01(self):
    #     """推广保证金-扫码支付"""
    #     self.login("funds_account_01")
    #     self.open(self.DEPOSIT_URL)
    #     self.sleep(2)
    #     # 点击充值
    #     self.click('//*[@id="root"]/div/div/div[2]/div[2]/div[3]/div[1]/button[1]/span')
    #     self.sleep(2)
    #     # 输入金额
    #     self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
    #     # 选择付款方式
    #     self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[1]/div')
    #     self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
    #     self.sleep(2)
    #     self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
    #     self.assert_text('1.01', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")

    # @skip("跳过推广充值后续不用了")
    # def test_third_deposit_pay_02(self):
    #     """推广保证金-网银支付"""
    #     self.login("funds_account_01")
    #     self.open(self.DEPOSIT_URL)
    #     self.sleep(2)
    #     # 点击充值
    #     self.click('//*[@id="root"]/div/div/div[2]/div[2]/div[3]/div[1]/button[1]/span')
    #     self.sleep(2)
    #     # 输入金额
    #     self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
    #     # 选择付款方式
    #     self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[2]/div')
    #     self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
    #     self.sleep(2)
    #     self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
    #     self.assert_text('1.01', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")
    #     self.assert_text('个人网上银行', "/html/body//li[@class='tab-item selected svelte-pzcz0v']")
    #     self.assert_text('企业网上银行', "/html/body//li[@class='tab-item  svelte-pzcz0v']")

    # @skip("推广保证金充值后续不用了")
    # def test_third_deposit_pay_03(self):
    #     """推广保证金-线下打款"""
    #     self.login("funds_account_01")
    #     self.open(self.DEPOSIT_URL)
    #     self.sleep(2)
    #     # 点击充值
    #     self.click('//*[@id="root"]/div/div/div[2]/div[2]/div[3]/div[1]/button[1]/span')
    #     self.sleep(2)
    #     # 输入金额
    #     self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
    #     # 选择付款方式
    #     self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[3]/div')
    #     self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
    #     self.sleep(2)
    #     self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
    #     self.assert_text('1', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")
    #     self.assert_text('对公汇款', "/html/body//li[@class='tab-item selected svelte-pzcz0v']")

    def test_cross_deposit_pay_01(self):
        """跨境保证金-扫码支付"""
        self.login("funds_account_22")
        self.open(self.DEPOSIT_URL)
        self.sleep(2)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/div/button[1]')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[1]/div')
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
        self.assert_text('1', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")

    def test_cross_deposit_pay_02(self):
        """跨境保证金-网银支付"""
        self.login("funds_account_22")
        self.open(self.DEPOSIT_URL)
        self.sleep(2)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[3]/div[1]/button[1]/span')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[2]/div')
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
        self.assert_text('1.01', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")
        self.assert_text('个人网上银行', "/html/body//li[@class='tab-item selected svelte-pzcz0v']")
        self.assert_text('企业网上银行', "/html/body//li[@class='tab-item  svelte-pzcz0v']")

    def test_cross_deposit_pay_03(self):
        """跨境保证金-查看导出记录"""
        self.login("funds_account_02")
        self.open('https://s.kwaixiaodian.com/zone/fund/accounting/crossborder/deposit/recharge/home')
        self.sleep(2)
        self.assert_text('查看导出记录', '//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[4]/button/span')
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[4]/button')
        self.sleep(2)
        self.assert_text('已导出的历史记录', '//*[@id="root"]/section/main/div/div[1]')

    def test_self_crossborder_002(self):
        """跨境保证金-充值"""
        self.login("funds_account_22")
        self.open(self.CROSSBORDER_URL)
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/div/button')
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div[1]/div[2]/span/span/input', 1)
        # 选择付款方式
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[1]/div')
        # 平台运行时以小窗口模式，按钮此时是invisible的，需要通过js_click
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button/span')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dt")
        self.assert_text('1', "/html/body//dl[@class='ebank-pay-info-detail-amount svelte-1azb9y5']/dd")

    def test_self_crossborder_001(self):
        """跨境保证金首页"""
        self.login("funds_account_22")
        self.open(self.CROSSBORDER_URL)
        self.sleep(2)
        self.assert_text("跨境保证金", '//*[@id="root"]/div/div/div[1]/div[1]')
        self.assert_text("标准额度(元)", '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[3]/div[1]/span[1]')
        self.assert_text("交易记录", '//*[@id="root"]/div/div/div[3]/div[1]')

    def test_self_crossborder_003(self):
        """跨境保证金充值结果页"""
        self.login("funds_account_22")
        self.open('https://s.kwaixiaodian.com/zone/fund/accounting/deposit/recharge/result?method=1&amount=10&mode=ACCOUNT_TYPE_DEPOSIT_CROSS')
        self.sleep(2)
        self.assert_text("充值成功", '//*[@id="root"]/div/div/div/div[2]')
        self.assert_element('//*[@id="root"]/div/div/div/div[4]/button[1]')
        self.assert_element('//*[@id="root"]/div/div/div/div[4]/button[2]')

    def test_self_crossborder_004(self):
        """跨境保证金- 标准额度查询"""
        self.login("funds_account_22")
        self.open(self.CROSSBORDER_URL)
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[1]/div[2]/a[1]')
        self.sleep(2)
        self.assert_text('类目选择', '//*[@id="root"]/div/article/div/div[1]/div/span[1]')
        self.click('//*[@id="shopType"]/label[1]/span[1]/span')
        self.sleep(2)
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH,
                                            '//*[@id="rc_select_0"]')))
            self.assert_text('请选择店铺的主要经营类目',
                             '//*[@id="root"]/div/article/div/div[1]/form/div/div/div[3]/div[2]/div/div/div/div/span[2]')
            element.click()
            self.sleep(2)
            element2 = self.driver.find_elements(By.XPATH, '/html/body/div[9]/div/div/div/div/ul[1]/li[1]')
            if element2:
                print('元素存在')
                element2.click()
                element3 = WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.XPATH,
                                                    '/html/body/div[9]/div/div/div/div/ul[2]/li[1]')))
                element3.click()
                self.sleep(2)
            else:
                self.sleep(2)
                self.assert_text('所需保证金标准额度及预计技术服务费率', '//*[@id="root"]/div/article/div/div[2]/div[1]')
                print('还是没有定位到下拉框元素,断言页面其他元素')
        except NoSuchElementException as e:
            print(f"测试失败: {str(e)}")

    def test_self_crossborder_005(self):
        """跨境保证金-常见问题 """
        self.login("funds_account_22")
        self.open(self.CROSSBORDER_URL)
        self.sleep(2)
        windows = self.driver.window_handles
        print(windows)  # 输出所有窗口的句柄列表
        self.driver.switch_to.window(windows[-1])
        # 获取当前窗口的句柄
        self.click('//*[@id="root"]/div/div/div[1]/div[2]/a[2]')
        current_window_handle = self.driver.current_window_handle
        print("Current Window Handle:", current_window_handle)
        self.assert_text('保证金常见问题', '//*[@id="root"]/section/main/div/div/div[2]')
        # self.assert_text('保证金介绍', '/html/body/div/div/div/div/div/p[1]/strong')
        # self.assert_elements('/html/body/div/div/div/div/div/p[1]/strong')


    def test_self_darendeposit_pay_01(self):
        """达人保证金-扫码支付"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        self.driver.refresh()
        self.sleep(3)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/div/button')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式-扫码付
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[2]/div/div/section/div/div[1]')
        # 点击确认支付
        # 平台运行时以小窗口模式，按钮此时是invisible的，需要通过js_click
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body/div[2]/div/div/div[2]/div[1]/dl[1]/dt")
        self.assert_text('1.01', "/html/body/div[2]/div/div/div[2]/div[1]/dl[1]/dd")


    def test_self_darendeposit_pay_02(self):
        """达人保证金-网银支付"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        self.driver.refresh()
        self.sleep(3)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/div/button')
        self.sleep(2)
        # 输入金额
        self.type('//*[@id="root"]/article/article/div/main/section[1]/div/div/div[2]/span/span/input', 1)
        # 选择付款方式-网银支付
        self.click('//*[@id="root"]/article/article/div/main/section[2]/div/div[3]/div/div/section/div/div[1]')
        # 点击确认支付
        # 平台运行时以小窗口模式，按钮此时是invisible的，需要通过js_click
        self.js_click('//*[@id="root"]/article/article/div/footer/div/article/section[2]/button')
        self.sleep(2)
        self.assert_text('支付金额', "/html/body/div[2]/div/div/div[2]/div/dl[1]/dt")
        self.assert_text('1.01', "/html/body/div[2]/div/div/div[2]/div/dl[1]/dd")
        self.assert_text('个人网上银行', "/html/body/div[2]/div/div/div[3]/ul[1]/li")


    def test_self_darendeposit_pay_03(self):
        """达人保证金-安心钱包支付"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        self.driver.refresh()
        self.sleep(3)
        # 点击充值
        self.click('//*[@id="root"]/div/div/div[2]/div/div[3]/div/button')
        self.sleep(2)
        # 判断是否有安心钱包充值方式
        self.assert_text('切换钱包', '//*[@id="root"]/article/article/div/main/section[2]/div/div[1]/div/div/section/button/span')

    def test_self_darendeposit_01(self):
        """达人保证金-账户记录查询"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        self.assert_text('全部', "//*[@id='rc-tabs-0-tab-1']")
        self.assert_text('可开收据', "//*[@id='rc-tabs-0-tab-2']")
        # 点击查询
        # self.click('//*[@id="rc-tabs-0-panel-1"]/div[1]/form/div[4]/div/div/div/div/div[4]/button')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        self.sleep(2)

    def test_self_darendeposit_02(self):
        """达人保证金-账务明细导出记录"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        # 点击导出记录
        self.click("(//span[contains(text(),'导出记录')])[1]")
        self.sleep(2)
        # 页面跳转到导出记录，定位【已导出的报表】元素
        self.assert_text("已导出的报表", "//*[@id='root']/div/div/div[1]")
        self.sleep(2)

    def test_self_darendeposit_03(self):
        """达人保证金-保证金规则是否存在 链接跳转"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        # 判断是否有达人保证金管理规则
        self.assert_text("《达人保证金管理规则》", "//span[contains(text(),'《达人保证金管理规则》')]")
        # 点击文件 预期跳转
        self.click("//span[contains(text(),'《达人保证金管理规则》')]")
        # 判断跳转后元素
        self.assert_text("关于《快手小店达人保证金管理规则》新增公告", "//span[@class='detail-title-text']")
        self.sleep(1)

    def test_self_darendeposit_04(self):
        """达人保证金-我要全额退保是否存在 链接跳转"""
        self.login("funds_account_03")
        self.open(self.DARENDEPOSIT_URL)
        self.sleep(2)
        # 判断 我要全额退保元素是否存在
        self.assert_text("我要全额退保", "//span[contains(text(),'我要全额退保')]")
        # 点击元素预期跳转
        self.click("//span[contains(text(),'我要全额退保')]")
        self.sleep(2)








if __name__ == '__main__':
    pytest.main()