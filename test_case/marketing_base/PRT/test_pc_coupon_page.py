import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from seleniumwire import webdriver
from test_case.marketing_base.base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain, get_domain_by_env
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env


COUPON_CENTER_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/manage/v2'
SHOP_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create?type=1&hideChangeCouponTypeBtn=1'
ITEM_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create?type=2&hideChangeCouponTypeBtn=1'
FANS_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create?type=4&hideChangeCouponTypeBtn=1'
FANS_GROUP_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create/seller-fans-level'
ANCHOR_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create/anchor'
RIVER_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create/stream-user-coupon'
WATCH_LIVE_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create/live'
NEW_USER_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/new-user-coupon/create'
CROWD_COUPON_CREATE_PAGE_URL = 'https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/create/coupon-in-crowd-package'
INTELLIGENT_FULL_DISCOUNT_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/intelligent-full-discount/create'
INTELLIGENT_FULL_DISCOUNT_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/intelligent-full-discount/manage'
TIMEING_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/chakra/create?activityType=1&amp;entrance=1'
TIMEING_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/chakra/index?activityType=1'
MEMBER_COUPON_PAGE_URL ='https://s.kwaixiaodian.com/zone/member/rights?tab=entry&autoOpen=1718876203713'
MEMBER_EXCLUSIVE_COUPON_PAGE_URL ='https://s.kwaixiaodian.com/zone/member/rights?tab=exclusive'
MEMBER_ITEM_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights?tab=memberGoods&autoOpen=1718883654444'
HEALTHCENTER_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/healthcenter/index'
HEALTHCENTER_RULE_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/healthcenter/rule'
ACTIVITY_LIST='https://s.kwaixiaodian.com/zone/business-invitation/record/list'
Coupon_ADDITEM_URL='https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=2&hideChangeCouponTypeBtn=1'
GROUP_CHAT_COUPON_URL='https://eshop-s.prt.kwaixiaodian.com/zone/crm/chat/management'
XIAOFEIJIN_URL='https://s.kwaixiaodian.com/zone/marketing/recharge/manage'
COUPON_MANAGE_PAGE_URL='https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/manage/v2?hideChangeCouponTypeBtn=1&entry_src=toolsv2_all_marketing_tools'
COUPON_DATA_PAGE_URL='https://eshop-s.prt.kwaixiaodian.com/zone/marketing/coupon/manage/v2?hideChangeCouponTypeBtn=1&entry_src=toolsv2_all_marketing_tools'
LOVE_HONGBAO_URL='https://eshop-s.prt.kwaixiaodian.com/zone/marketing/pet/manage?entry_src=toolsv2_all_marketing_tools'
from seleniumwire import webdriver
@ddt
class TestPCCouponCreatePage(BaseTestCase):

    # #@pytest.mark.skip
    # def setup_class(self):
    #     print('say hi setup')
    #
    #     # 浏览器最大化
    #     #self.maximize_window()
    #     self.login("MARKETING_DOMAIN", "marketing")

    @pytest.mark.skip
    def kwaixiaodian_login(self, account):
        """ PRT快手小店登录 """
        self.maximize_window()
        env = 'online'
        domain = 'MARKETING_DOMAIN_PRT'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_coupon_center(self):
        print('say center')
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.kwaixiaodian_login("marketing")
        self.open(COUPON_CENTER_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('优惠券概览', '//div[@id="root"]/section/main/div/div[2]/div/div/div/div/div[1]/div')
        self.assert_text('管理优惠券', '//div[@id="root"]/section/main/div/div[2]/div/div/div/div/div[2]/div')
        self.assert_text('数据效果', '//div[@id="root"]/section/main/div/div[2]/div/div/div/div/div[3]/div')

        self.assert_text('营销效果',
                         '//div[@id="root"]/section/main/div/div[2]/div/div[2]/div/div/div/div/div/div/span')


    #@pytest.mark.skip
    @pytest.mark.p0
    def test_shop_coupon_create(self):

        print('say shop_coupon_create')
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(SHOP_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_shop_coupon_info()

    @pytest.mark.skip
    @pytest.mark.p0
    def test_item_coupon_create(self):
        print('say item_coupon_create')
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(ITEM_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_item_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_fans_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FANS_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_fans_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_fans_group_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(FANS_GROUP_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_fans_group_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_anchor_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(ANCHOR_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_anchor_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_river_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(RIVER_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_river_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_watch_live_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(WATCH_LIVE_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        sleep(2)
        self.check_fill_in_watch_live_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_new_user_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(NEW_USER_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_new_user_coupon_info()


    #特定人群直发券
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_crowd_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(CROWD_COUPON_CREATE_PAGE_URL)
        self.open(CROWD_COUPON_CREATE_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_crowd_coupon_info()
    #入会券
    @pytest.mark.p0
    def test_member_create(self):
            # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MEMBER_COUPON_PAGE_URL)
        self.open(MEMBER_COUPON_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_coupon_info()
    #会员专享券
    @pytest.mark.p0
    def test_member_exclusive_coupon_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MEMBER_EXCLUSIVE_COUPON_PAGE_URL)
        self.open(MEMBER_EXCLUSIVE_COUPON_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_exclusive_coupon_info()
    #会员专属商品
    @pytest.mark.p0
    def test_member_exclusive_item_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MEMBER_ITEM_PAGE_URL)
        self.open(MEMBER_ITEM_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_exclusive_item_info()
    #会员特价商品
    @pytest.mark.p0
    def test_member_special_offer_item_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(MEMBER_ITEM_PAGE_URL)
        self.open(MEMBER_ITEM_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_special_offer_item_info()
    #营销健康活动查询
    @pytest.mark.p0
    def test_health_center_index(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(HEALTHCENTER_PAGE_URL)
        self.open(HEALTHCENTER_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_health_center_index_info()
    #营销健康规则查询
    @pytest.mark.p0
    def test_health_center_rule(self):
         # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(HEALTHCENTER_RULE_PAGE_URL)
        self.open(HEALTHCENTER_RULE_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_health_center_rule_info()

    #已报名活动列表
    @pytest.mark.p0
    def test_record_list(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(ACTIVITY_LIST)
        self.open(ACTIVITY_LIST)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_record_list_info()
    #券部分商品列表
    @pytest.mark.p0
    def test_coupon_additem(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(Coupon_ADDITEM_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_coupon_additem_info()
    #群聊券
    @pytest.mark.p0
    @pytest.mark.skip
    def test_group_chat_coupon(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing_member")
        self.open(GROUP_CHAT_COUPON_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_group_chat_coupon_info()

    @pytest.mark.p0
    @pytest.mark.skip("账号问题")
    def test_xiaofeijin_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "funds_account_01")
        self.open(XIAOFEIJIN_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_xiaofeijin_create_info()

    @pytest.mark.p0
    @pytest.mark.skip("账号问题")
    def test_xiaofeijin_guanli(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "funds_account_01")
        self.open(XIAOFEIJIN_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_xiaofeijin_guanli_info()

    @pytest.mark.p0
    def test_intelligent_full_discount_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(INTELLIGENT_FULL_DISCOUNT_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")


    @pytest.mark.p0
    def test_intelligent_full_discount_coupon_manage(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(INTELLIGENT_FULL_DISCOUNT_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_timing_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(TIMEING_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_timing_coupon_manage(self):

        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(TIMEING_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_coupon_manage(self):
        # 浏览器最大化
        self.maximize_window()
        self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_coupon_manage_info()

    def test_coupon_data(self):
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(COUPON_DATA_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_coupon_data_info()
    def test_love_hongbao(self):
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(LOVE_HONGBAO_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_love_hongbao_info()
    def test_love_hongbao_data(self):
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(LOVE_HONGBAO_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_love_hongbao_data_info()

    def test_switch_type(self):
        # 浏览器最大化
        self.maximize_window()
        self.login("MARKETING_DOMAIN", "marketing")
        self.open(TIMEING_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_switch_type_info()
    def check_fill_in_shop_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[2]')
       # self.click('//*[@id="root"]/section/main/div/div[2]/form/div[11]/button[1]/span')

    def check_fill_in_item_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//div[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/form/div[12]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/form/div[12]/button[2]/span')
        self.click('//*[@id="root"]/section/main/div/div[2]/form/div[12]/button[2]/span')

    def check_fill_in_fans_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/form/div[14]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/form/div[14]/button[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/form/div[14]/button[1]')

    def check_fill_in_fans_group_coupon_info(self):
        time.sleep(2)
        self.assert_text('创 建', '//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')
        self.assert_text('取 消', '//div[@class="marketing-paas-btn-wrapper"]/button[2]/span')
        self.click('//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')

    def check_fill_in_anchor_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//div[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//div[@id="root"]/section/main/div/div[2]/div[2]/button[1]/span')
        self.assert_text('取 消', '//div[@id="root"]/section/main/div/div[2]/div[2]/button[2]/span')
        self.click('//div[@class="btn-group"]/button[1]/span')

    def check_fill_in_river_coupon_info(self):
        time.sleep(2)
        self.assert_text('创 建', '//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')
        self.assert_text('取 消', '//div[@class="marketing-paas-btn-wrapper"]/button[2]/span')
        self.click('//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')

    def check_fill_in_watch_live_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[2]')

    def check_fill_in_new_user_coupon_info(self):
        time.sleep(15)
        self.assert_text('创 建', '//*[@id="root"]/div/div[3]/button[2]/span')
        self.assert_text('取 消', '//*[@id="root"]/div/div[3]/button[1]/span')
        self.click('//*[@id="root"]/div/div[3]/button[1]/span')

    def check_fill_in_crowd_coupon_info(self):
        time.sleep(10)
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/div[2]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/div[2]/button[2]/span')
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/button[1]/span')
    def check_fill_in_member_coupon_info(self):
        time.sleep(2)
        #self.assert_text('确认','/html/body/div[5]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        #self.click('/html/body/div[5]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        #self.assert_text('取消','/html/body/div[5]/div/div[2]/div/div/div[3]/div/div/div[1]/button/span')
        self.assert_text('全部推广渠道','//*[@id="receiveChannel"]/div/label/span[2]/span')

    def check_fill_in_member_exclusive_coupon_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-exclusive"]/div/div/div/div[1]/div/div[2]/button/span')
        time.sleep(2)
        self.assert_text('全部推广渠道', '//*[@id="root"]/div/div/div/form/div[3]/div[2]/div/div/div/label/span[2]')
        self.assert_text('可领取会员等级', '//*[@id="root"]/div/div/div/form/div[4]/div[1]/div/span/label')
        #self.assert_text('确认','/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        #self.click('/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
    def check_fill_in_member_exclusive_item_info(self):

        time.sleep(2)
        self.assert_text('会员专属商品', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[1]/div/span[1]')
        self.assert_text('选择商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        self.assert_text('确认','/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        self.click('/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
    def check_fill_in_member_special_offer_item_info(self):
        time.sleep(2)
        self.assert_text('会员特价商品', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[2]/div/span[1]')
        self.assert_text('选择商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        self.assert_text('确认','/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        self.click('/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
    def check_fill_in_health_center_index_info(self):
        time.sleep(2)
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button/span')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button/span')
    def check_fill_in_test_health_center_rule_info(self):
        time.sleep(2)
        self.assert_text('优惠叠加查询', '//*[@id="root"]/div/div[1]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[4]/div[2]/button[1]/span')
    def check_fill_in_test_record_list_info(self):
        time.sleep(2)
        self.assert_text('活动待开始','//*[@id="rc-tabs-0-tab-1"]')
        #self.assert_text('审核通过', '//*[@id="root"]/section/section/main/div/div/div[3]/div[1]/div[2]/div[1]/span[2]/span')
    def check_fill_in_test_coupon_additem_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[2]/form/div[7]/div[2]/div/span/div[2]/div/button')
        #time.sleep(10)
        #self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div[1]/ul/li[3]/a')
    def check_fill_in_test_group_chat_coupon_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-op"]/div/div[1]/div/div[2]/div[5]/div/div/div[1]/div[2]/div/a')
        time.sleep(2)
        #self.click('/html/body/div[2]/div/div[2]/div/div/div[2]/div[1]/button/span')

    def check_fill_in_test_xiaofeijin_create_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div/div/div[4]/div[2]/form/div/div[2]/div/div/div/div/div[2]/button/span')
        time.sleep(2)

    def check_fill_in_test_xiaofeijin_guanli_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div/div/div[1]/div[1]/div[2]/button/span')

    def check_fill_in_test_coupon_manage_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        time.sleep(2)
        self.assert_text('优惠券红包名称','//*[@id="rc-tabs-0-panel-2"]/div/section[1]/div/div/div/div/div/div/table/thead/tr/th[1]')
    def check_fill_in_test_coupon_data_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        time.sleep(2)
        self.assert_text('数据总览','//*[@id="rc-tabs-0-panel-3"]/div/div[1]/div/div/div[1]/div[1]')

    def check_fill_in_test_love_hongbao_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        time.sleep(2)
        self.assert_text('红包ID', '//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')
    def check_fill_in_test_love_hongbao_data_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        time.sleep(2)
        self.assert_text('宠爱红包数据管理', '//*[@id="rc-tabs-0-panel-3"]/div/div[1]/div[1]')

    def check_fill_in_switch_type_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[2]/div[2]/div/div/div/button/span')
        time.sleep(2)
        self.assert_text('请选择优惠券类型', '//*[@id="rcDialogTitle0"]')
