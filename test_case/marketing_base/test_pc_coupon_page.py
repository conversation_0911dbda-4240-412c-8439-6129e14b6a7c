import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from utils.kwaixiaodianUtils import KwaiXiaoDianToolTest

from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
from ..marketing.test_pc_marketing_tools_page import SECKILL_CREATE_PAGE_URL, SECKILL_MANAGE_PAGE_URL, \
    MULTI_DISCOUNT_CREATE_PAGE_URL

COUPON_CENTER_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/manage/v2'
SHOP_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=1'
ITEM_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=2'
FANS_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=4'
FANS_GROUP_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/seller-fans-level'
ANCHOR_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/anchor'
RIVER_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/stream-user-coupon'
WATCH_LIVE_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/live'
NEW_USER_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/new-user-coupon/create'
CROWD_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/coupon/create/coupon-in-crowd-package'
INTELLIGENT_FULL_DISCOUNT_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/intelligent-full-discount/create'
INTELLIGENT_FULL_DISCOUNT_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/intelligent-full-discount/manage'
TIMEING_COUPON_CREATE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/chakra/create?activityType=1&amp;entrance=1'
TIMEING_COUPON_MANAGE_PAGE_URL = 'https://s.kwaixiaodian.com/zone/marketing/chakra/index?activityType=1'
MEMBER_COUPON_PAGE_URL ='https://s.kwaixiaodian.com/zone/member/rights?tab=entry&autoOpen=1718876203713'
MEMBER_EXCLUSIVE_COUPON_PAGE_URL ='https://s.kwaixiaodian.com/zone/member/rights?tab=exclusive'
MEMBER_ITEM_PAGE_URL = 'https://s.kwaixiaodian.com/zone/member/rights?tab=memberGoods&autoOpen=1718883654444'
HEALTHCENTER_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/healthcenter/index'
HEALTHCENTER_RULE_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/healthcenter/rule'
ACTIVITY_LIST='https://s.kwaixiaodian.com/zone/business-invitation/record/list'
Coupon_ADDITEM_URL='https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=2&hideChangeCouponTypeBtn=1'
GROUP_CHAT_COUPON_URL='https://s.kwaixiaodian.com/zone/crm/chat/management'
XIAOFEIJIN_URL='https://s.kwaixiaodian.com/zone/marketing/recharge/manage'
COUPON_MANAGE_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/coupon/manage/v2?hideChangeCouponTypeBtn=1&entry_src=toolsv2_common_tools'
COUPON_DATA_PAGE_URL='https://s.kwaixiaodian.com/zone/marketing/coupon/manage/v2?hideChangeCouponTypeBtn=1&entry_src=toolsv2_common_tools'
LOVE_HONGBAO_URL='https://s.kwaixiaodian.com/zone/marketing/pet/manage?entry_src=toolsv2_all_marketing_tools'
FENSITUAN_URL='https://s.kwaixiaodian.com/zone/marketing/coupon/create/seller-fans-level'
NEW_USER_COUPON_MANAGE_PAGE_URL= 'https://s.kwaixiaodian.com/zone/marketing/new-user-coupon/manage'
DUOJIANYOUHUI_PAGR='https://s.kwaixiaodian.com/zone/marketing/bulk-discount/manage?entry_src=toolsv2_common_tools'
MAKETING_TOOLS_PAGE='https://s.kwaixiaodian.com/zone/marketing/tools/v2?from=kwaixiaodian_market_pc'
ANCHOR_NEWBIE_PAGE='https://s.kwaixiaodian.com/zone/marketing/new-user-coupon/manage?entry_src=toolsv2_common_tools'
@ddt
class TestPCCouponCreatePage(KwaiXiaoDianToolTest):

    # #@pytest.mark.skip
    # def setup_class(self):
    #     print('say hi setup')
    #
    #     # 浏览器最大化
    #     #self.maximize_window()
    #     self.login("MARKETING_DOMAIN", "marketing")

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_coupon_center(self):

        print('say center')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(COUPON_CENTER_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('优惠券概览', '//div[@id="root"]/section/main/div/div[2]/div/div/div/div/div[1]/div')
        self.assert_text('管理优惠券', '//div[@id="root"]/section/main/div/div[2]/div/div/div/div/div[2]/div')
        self.assert_text('数据效果', '//div[@id="root"]/section/main/div/div[2]/div/div/div/div/div[3]/div')

        self.assert_text('营销效果', '//div[@id="root"]/section/main/div/div[2]/div/div[2]/div/div/div/div/div/div/span')

    @pytest.mark.p0
    @pytest.mark.skip
    def test_marketing_tools_center(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MAKETING_TOOLS_PAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('引流互动', '//*[@id="homePosition"]/div[2]/div/div[1]/div/div[2]/div[2]/div')
        self.assert_text('提升转化', '//*[@id="homePosition"]/div[2]/div/div[1]/div/div[2]/div[3]/div')
        self.assert_text('提升客单', '//*[@id="homePosition"]/div[2]/div/div[1]/div/div[2]/div[4]/div')
        self.click('//*[@id="homePosition"]/div[2]/div/div[1]/div/div[2]/div[2]/div')
        self.assert_no_404_errors()
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_shop_coupon_create(self):

        print('say shop_coupon_create')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(SHOP_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_shop_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_item_coupon_create(self):
        print('say item_coupon_create')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(ITEM_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        sleep(10)
        self.check_fill_in_item_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_fans_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FANS_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_fans_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_fans_group_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FANS_GROUP_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_fans_group_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_anchor_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(ANCHOR_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_anchor_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_river_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(RIVER_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_river_coupon_info()

    #@pytest.mark.skip
    @pytest.mark.p0
    def test_watch_live_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(WATCH_LIVE_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_watch_live_coupon_info()

    @pytest.mark.skip
    @pytest.mark.p0
    def test_new_user_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(NEW_USER_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_new_user_coupon_info()


    #特定人群直发券
    #@pytest.mark.skip
    @pytest.mark.p0
    @pytest.mark.skip
    def test_crowd_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(CROWD_COUPON_CREATE_PAGE_URL)
        self.open(CROWD_COUPON_CREATE_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_crowd_coupon_info()
    #入会券
    @pytest.mark.p0
    def test_member_create(self):
            # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MEMBER_COUPON_PAGE_URL)
        self.open(MEMBER_COUPON_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_coupon_info()
    #会员专享券
    @pytest.mark.p0
    def test_member_exclusive_coupon_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MEMBER_EXCLUSIVE_COUPON_PAGE_URL)
        self.open(MEMBER_EXCLUSIVE_COUPON_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_exclusive_coupon_info()
    #会员专属商品
    @pytest.mark.p0
    def test_member_exclusive_item_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MEMBER_ITEM_PAGE_URL)
        self.open(MEMBER_ITEM_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_exclusive_item_info()
    #会员特价商品
    @pytest.mark.p0
    def test_member_special_offer_item_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MEMBER_ITEM_PAGE_URL)
        self.open(MEMBER_ITEM_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_member_special_offer_item_info()
    #营销健康活动查询
    @pytest.mark.p0
    def test_health_center_index(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(HEALTHCENTER_PAGE_URL)
        self.open(HEALTHCENTER_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_health_center_index_info()
    #营销健康规则查询
    @pytest.mark.p0
    def test_health_center_rule(self):
         # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(HEALTHCENTER_RULE_PAGE_URL)
        self.open(HEALTHCENTER_RULE_PAGE_URL)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_health_center_rule_info()

    #已报名活动列表
    @pytest.mark.p0
    def test_record_list(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(ACTIVITY_LIST)
        self.open(ACTIVITY_LIST)

        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_record_list_info()
    #券部分商品列表
    @pytest.mark.p0
    def test_coupon_additem(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(Coupon_ADDITEM_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_coupon_additem_info()
    #群聊券
    @pytest.mark.p0
    @pytest.mark.skip
    def test_group_chat_coupon(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing_member")
        self.open(GROUP_CHAT_COUPON_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_group_chat_coupon_info()

    @pytest.mark.p0
    @pytest.mark.skip("账号问题")
    def test_xiaofeijin_create(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("funds_account_01")
        self.open(XIAOFEIJIN_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_xiaofeijin_create_info()

    @pytest.mark.p0
    @pytest.mark.skip("账号问题")
    def test_xiaofeijin_guanli(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("funds_account_01")
        self.open(XIAOFEIJIN_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")
        self.check_fill_in_test_xiaofeijin_guanli_info()

    @pytest.mark.p0
    def test_intelligent_full_discount_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(INTELLIGENT_FULL_DISCOUNT_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")


    @pytest.mark.p0
    def test_intelligent_full_discount_coupon_manage(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(INTELLIGENT_FULL_DISCOUNT_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_timing_coupon_create(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(TIMEING_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_timing_coupon_manage(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(TIMEING_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_title("快手小店")

    @pytest.mark.p0
    def test_coupon_manage(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_coupon_manage_info()

    def test_coupon_data(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(COUPON_DATA_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_coupon_data_info()
    def test_love_hongbao(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(LOVE_HONGBAO_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_love_hongbao_info()
    def test_love_hongbao_data(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(LOVE_HONGBAO_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_test_love_hongbao_data_info()

    def test_switch_type(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(TIMEING_COUPON_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.check_fill_in_switch_type_info()

    def test_zhiboyuyue_coupon(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open('https://s.kwaixiaodian.com/zone/marketing/coupon/create?type=1&hideChangeCouponTypeBtn=1')
        sleep(2)
        self.assert_no_404_errors()
        self.check_zhiboyuyue_info()

    def test_zhiboyuyue_redpackage(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open('https://s.kwaixiaodian.com/zone/marketing/coupon/create/anchor')
        sleep(2)
        self.assert_no_404_errors()
        self.check_zhiboyuyue_redpackag_info()

    def test_zhiboyuyue_redpackage_banping(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open('https://s.kwaixiaodian.com/zone/live/preview/previewCreate?isRank=1')
        sleep(2)
        self.assert_no_404_errors()
        self.check_zhiboyuyue_redpackag_banping_info()

    # 优惠券类型
    def test_gouwutuan_CouponType(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_CouponType_info()

    # 指定商品按钮
    def test_gouwutuan_DesignatedItem(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_DesignatedItem_info()

    # 全店商品按钮
    def test_gouwutuan_EntireShopItems(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_EntireShopItems_info()

    # 固定时间段按钮
    def test_gouwutuan_FixedTimePeriod(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_FixedTimePeriod_info()

    # 领取后有限时间内有效按钮
    def test_gouwutuan_AfterClaim(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_AfterClaim_info()

    # 与领取时间一致按钮
    def test_gouwutuan_ConsistentClaimTime(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_ConsistentClaimTime_info()

    # 无门槛券按钮
    def test_gouwutuan_NoThresholdCoupon(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_NoThresholdCoupon_info()

    # 满减券按钮
    def test_gouwutuan_FullReductionCoupon(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_FullReductionCoupon_info()

    # 优惠券名称
    def test_gouwutuan_CouponName(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_CouponName_info()

    # # 添加商品按钮
    # def test_gouwutuan_InsertItem(self):
    #     self.maximize_window()
    #     self.kwaixiaodian_login("marketing")
    #     self.open(FENSITUAN_URL)
    #     self.assert_no_404_errors()
    #     self.check_gouwutuan_InsertItem_info()

    # 优惠券面额
    def test_gouwutuan_CouponDenomination(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_CouponDenomination_info()

    # 使用条件
    def test_gouwutuan_UsageConditions(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_UsageConditions_info()

    # 发放张数
    def test_gouwutuan_DistributedCount(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_DistributedCount_info()

    # 每人限领
    def test_gouwutuan_PerUserLimit(self):
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(FENSITUAN_URL)
        self.assert_no_404_errors()
        self.check_gouwutuan_PerUserLimit_info()

    @pytest.mark.p0
    def test_seckill_create_page(self):

        print('say seckill_create')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(SECKILL_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('生效渠道', '//*[@id="root"]/section/main/div/div[3]/div/div/div[3]/div[1]')
        self.click('//*[@id="root"]/section/main/div/div[3]/div/div/div[3]/div[2]/div/label[2]/span[1]/span')
        self.assert_text('仅直播间', '//*[@id="root"]/section/main/div/div[3]/div/div/div[3]/div[2]/div/label[2]/span[2]')
        self.assert_text('参与条件', '//*[@id="root"]/section/main/div/div[3]/div/div/div[4]/div[1]')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[3]/div/div/div[8]/div[1]/button/span')
        self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[3]/div/div/div[1]/div[4]/div[2]/div[1]/div/span[2]')
        self.click('/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/section/main/div/div[3]/div/div/div[1]/div[4]/div[2]/div[1]/div/span[2]')
        sleep(5)

    @pytest.mark.p0
    def test_seckill_manage_page(self):

        print('say seckill_manage')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(SECKILL_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        sleep(10)
        self.assert_text('创建秒杀', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/button/span')
        self.assert_text('生效渠道', '//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[4]/div/div/div/div/div/table/thead/tr/th[7]')
        self.click('//*[@id="seckillStatus"]/label[4]/span[2]')

    @pytest.mark.p1
    def test_seckill_manage_switch_page(self):

        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(SECKILL_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.click('//*[@id="seckillStatus"]/label[4]/span[2]')
        sleep(5)
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[5]/ul/li[10]/div/div[1]')
        sleep(5)
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div[5]/ul/li[10]/div')
        self.assert_no_404_errors()
    def test_seckill_data_statics(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(SECKILL_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        sleep(5)
        self.click('//*[@id="rc-tabs-0-panel-2"]/div/div/div[1]/div[1]/div[2]/div/div/label[2]/span[2]')
        self.click('//*[@id="rc-tabs-1-tab-2"]/div')
        self.assert_no_404_errors()
    @pytest.mark.p0
    def test_multi_discount_create_page(self):

        print('say multi_discount_create')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MULTI_DISCOUNT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div[2]/div/div[3]/div[1]/div/div/div/div[2]/div[1]/button')
        self.click('//*[@id="root"]/div[2]/div/div[3]/div[1]/div/div/div/div[2]/div[2]/button')
    def test_new_coupon_manage_page(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(NEW_USER_COUPON_MANAGE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div/div[3]/div[2]/div[1]')
        self.is_element_visible('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button/span')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/div[2]/button/span')
    #多件优惠创建
    @pytest.mark.p0
    def test_multi_discount_create_page(self):

        print('say multi_discount_create')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MULTI_DISCOUNT_CREATE_PAGE_URL)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="root"]/div[2]/div/div[3]/div[1]/div/div/div/div[2]/div[1]/button')
        self.click("(//button[@class='kwaishop-marketing-bulk-discount-pc-btn T4s3z0wPlml5nPDnwf0s'])[1]")
    #多件优惠管理--查看详情
    @pytest.mark.p0
    @pytest.mark.skip
    def test_multi_discount_manage_page(self):

        print('say multi_discount_manage')
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(DUOJIANYOUHUI_PAGR)
        sleep(2)
        self.assert_no_404_errors()
        self.is_element_visible('//*[@id="rc-tabs-0-tab-1"]')
        self.click('//*[@id="rc-tabs-0-tab-1"]')
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div/div[1]/div/label[1]/span[2]')
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[5]/div/div/a')
        self.is_element_visible('//*[@id="root"]/div[2]/div/div[1]')
        self.is_element_visible('//*[@id="root"]/div/div/form/div[3]/div/div[2]')
        self.click('//*[@id="root"]/div[2]/div/div[2]/div[1]/div/div/div/div[2]/div/button/span')

    def test_tool_bar(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MAKETING_TOOLS_PAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('购物团', '//*[@id="menu_item_-XyKlrYJR3M"]')
        self.assert_text('优惠券','//*[@id="menu_item_GHYvij7n02M"]')
        self.assert_text('新人优惠','//*[@id="menu_item_Qq0dnegib3E"]')
        self.assert_text('全部工具','//*[@id="menu_item_vfJkUODk1UQ"]')
        self.click('//*[@id="menu_item_Qq0dnegib3E"]')
        self.assert_text('店铺新人券','//*[@id="root"]/div/div[2]/div/div[1]/div[1]')
    def test_tool_bar_health(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(MAKETING_TOOLS_PAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_text('营销健康', '//*[@id="menu_item_pUMGh4GaJoE"]')
        self.assert_text('营销活动查询','//*[@id="menu_item_w-HyvcImpjw"]')
        self.assert_text('营销规则查询','//*[@id="menu_item_diBtRA_RCis"]')
        self.click('//*[@id="menu_item_diBtRA_RCis"]')
        self.assert_text('优惠叠加查询','//*[@id="root"]/div/div[1]/div[1]')
        self.assert_text('查询区域','//*[@id="root"]/div/div[2]/div[1]')
    def test_anchor_newbie(self):
        # 浏览器最大化
        self.maximize_window()
        self.kwaixiaodian_login("marketing")
        self.open(ANCHOR_NEWBIE_PAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/span')
        sleep(10)
    def check_fill_in_shop_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[2]')
       # self.click('//*[@id="root"]/section/main/div/div[2]/form/div[11]/button[1]/span')

    def check_fill_in_item_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//div[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[2]/span')
        self.click('//*[@id="root"]/section/main/div/div[2]/form/div[13]/button[2]/span')

    def check_fill_in_fans_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/form/div[14]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/form/div[14]/button[2]/span')
        self.click('//*[@id="root"]/section/main/div/div[2]/form/div[14]/button[2]/span')

    def check_fill_in_fans_group_coupon_info(self):
        time.sleep(2)
        self.assert_text('创 建', '//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/div[2]/button[2]/span')
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/button[2]/span')

    def check_fill_in_anchor_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//div[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//div[@id="root"]/section/main/div/div[2]/div[2]/button[1]/span')
        self.assert_text('取 消', '//div[@id="root"]/section/main/div/div[2]/div[2]/button[2]/span')
        self.click('//div[@class="btn-group"]/button[1]/span')

    def check_fill_in_river_coupon_info(self):
        time.sleep(2)
        self.assert_text('创 建', '//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')
        self.assert_text('取 消', '//div[@class="marketing-paas-btn-wrapper"]/button[2]/span')
        self.click('//div[@class="marketing-paas-btn-wrapper"]/button[1]/span')

    def check_fill_in_watch_live_coupon_info(self):
        time.sleep(2)
        self.assert_text('填写优惠信息', '//*[@id="root"]/section/main/div/div[2]/h3')
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/div/div/div[2]/button[2]')
    def check_fill_in_new_user_coupon_info(self):
        time.sleep(20)
        self.assert_text('创 建', '//*[@id="root"]/div[2]/div[3]/button[1]/span')
        self.assert_text('取 消', '//*[@id="root"]/div[2]/div[3]/button[2]/span')
        self.click('//*[@id="root"]/div[2]/div[3]/button[2]/span')

    def check_fill_in_crowd_coupon_info(self):
        time.sleep(20)
        self.assert_text('创 建', '//*[@id="root"]/section/main/div/div[2]/div[2]/button[1]')
        self.assert_text('取 消', '//*[@id="root"]/section/main/div/div[2]/div[2]/button[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/button[2]')
    def check_fill_in_member_coupon_info(self):
        time.sleep(2)
        #self.assert_text('确认','/html/body/div[5]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        #self.click('/html/body/div[5]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        #self.assert_text('取消','/html/body/div[5]/div/div[2]/div/div/div[3]/div/div/div[1]/button/span')
        self.assert_text('全部推广渠道','//*[@id="receiveChannel"]/div/label/span[2]/span')

    def check_fill_in_member_exclusive_coupon_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-exclusive"]/div/div/div/div[1]/div/div[2]/button/span')
        time.sleep(20)
        self.assert_text('全部推广渠道','//*[@id="root"]/div/div/div/form/div[3]/div[2]/div/div/div/label/span[2]')
        self.assert_text('可领取会员等级','//*[@id="root"]/div/div/div/form/div[4]/div[1]/div/span/label')
        #self.assert_text('确认','/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        #self.click('/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
    def check_fill_in_member_exclusive_item_info(self):

        time.sleep(2)
        self.assert_text('会员专属商品', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[1]/div/span[1]')
        self.assert_text('选择商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        self.assert_text('确认','/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        self.click('/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
    def check_fill_in_member_special_offer_item_info(self):
        time.sleep(2)
        self.assert_text('会员特价商品', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[2]/div/span[1]')
        self.assert_text('选择商品', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/button/span')
        self.assert_text('确认','/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
        self.click('/html/body/div[2]/div/div[2]/div/div/div[3]/div/div/div[2]/button/span')
    def check_fill_in_health_center_index_info(self):
        time.sleep(2)
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button/span')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div/div[2]/button/span')
    def check_fill_in_test_health_center_rule_info(self):
        time.sleep(2)
        self.assert_text('优惠叠加查询', '//*[@id="root"]/div/div[1]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div[2]/div[4]/div[2]/button[1]/span')
    def check_fill_in_test_record_list_info(self):
        time.sleep(2)
        self.assert_text('活动待开始','//*[@id="rc-tabs-0-tab-1"]')
        #self.assert_text('审核通过', '//*[@id="root"]/section/section/main/div/div/div[3]/div[1]/div[2]/div[1]/span[2]/span')
    def check_fill_in_test_coupon_additem_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/section/main/div/div[2]/form/div[7]/div[2]/div/span/div[2]/div/button')
        #time.sleep(10)
        #self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/div[1]/ul/li[3]/a')
    def check_fill_in_test_group_chat_coupon_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-panel-op"]/div/div[1]/div/div[2]/div[5]/div/div/div[1]/div[2]/div/a')
        time.sleep(2)
        #self.click('/html/body/div[2]/div/div[2]/div/div/div[2]/div[1]/button/span')

    def check_fill_in_test_xiaofeijin_create_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div/div/div[4]/div[2]/form/div/div[2]/div/div/div/div/div[2]/button/span')
        time.sleep(2)

    def check_fill_in_test_xiaofeijin_guanli_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div/div/div[1]/div[1]/div[2]/button/span')

    def check_fill_in_test_coupon_manage_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        time.sleep(2)
        self.assert_text('优惠券红包名称','//*[@id="rc-tabs-0-panel-2"]/div/section[1]/div/div/div/div/div/div/table/thead/tr/th[1]')
    def check_fill_in_test_coupon_data_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        time.sleep(2)
        self.assert_text('数据总览','//*[@id="rc-tabs-0-panel-3"]/div/div[1]/div/div/div[1]/div[1]')

    def check_fill_in_test_love_hongbao_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        time.sleep(2)
        self.assert_text('红包ID', '//*[@id="rc-tabs-0-panel-2"]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')
    def check_fill_in_test_love_hongbao_data_info(self):
        time.sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-3"]')
        time.sleep(2)
        self.assert_text('宠爱红包数据管理', '//*[@id="rc-tabs-0-panel-3"]/div/div[1]/div[1]')

    def check_fill_in_switch_type_info(self):
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/form/div[2]/div[2]/div/div/div/button/span')
        time.sleep(2)
        self.assert_text('请选择优惠券类型', '//*[@id="rcDialogTitle0"]')


    def check_zhiboyuyue_info(self):
        time.sleep(2)
        self.assert_text('直播预约', '//*[@id="receiveChannel"]/div[5]/label/span[2]/span')
        self.click('//*[@id="receiveChannel"]/div[5]/label/span[2]/span')
        time.sleep(5)
        self.assert_text('仅直播间', '//*[@id="useScene"]/div[2]/label/span[2]/span')
        self.assert_text('全部渠道', '//*[@id="useScene"]/div[1]/label/span[2]/span')

    def check_zhiboyuyue_redpackag_info(self):
        time.sleep(2)
        self.assert_text('直播预约', '//*[@id="root"]/section/main/div/div[2]/div[1]/form/div[5]/div[2]/div/div/div/div/label[3]/span[2]')
        self.click('//*[@id="root"]/section/main/div/div[2]/div[1]/form/div[5]/div[2]/div/div/div/div/label[3]/span[2]')
        time.sleep(5)
        self.assert_text('仅直播间', '//*[@id="root"]/section/main/div/div[2]/div[1]/form/div[6]/div[2]/div/div/div/label[2]/span[2]')
        self.assert_text('全部渠道', '//*[@id="root"]/section/main/div/div[2]/div[1]/form/div[6]/div[2]/div/div/div/label[1]/span[2]')
    def check_zhiboyuyue_redpackag_banping_info(self):
        time.sleep(2)
        self.assert_text('预约买家可领优惠券','//*[@id="live-preview-root"]/div[1]/div[2]/div[1]/div/div[9]/div[2]/div[2]/div[1]/span')
        self.click('//*[@id="live-preview-root"]/div[1]/div[2]/div[1]/div/div[9]/div[2]/div[2]/div[1]/span')

    def check_gouwutuan_CouponType_info(self):
        self.assert_text('购物团专属券', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[1]/div[2]')
        self.assert_text('入团优惠券', '//*[@id="root"]/div/div/form/div[1]/div[2]/div/div/div/div[2]/div[1]')

    def check_gouwutuan_DesignatedItem_info(self):
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/label[2]/span[1]')
        self.assert_text('指定商品(商品券)', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/label[2]/span[2]')

    def check_gouwutuan_EntireShopItems_info(self):
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/label[1]/span[1]')
        self.assert_text('全店商品(店铺券)', '//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/label[1]/span[2]')

    def check_gouwutuan_FixedTimePeriod_info(self):
        self.click('//*[@id="root"]/div/div/form/div[8]/div[2]/div/div/div/label[2]/span[1]')
        self.assert_text('固定时间段','//*[@id="root"]/div/div/form/div[8]/div[2]/div/div/div/label[2]/span[2]')

    def check_gouwutuan_AfterClaim_info(self):
        self.click('//*[@id="root"]/div/div/form/div[8]/div[2]/div/div/div/label[3]/span[1]')
        self.assert_text('领取后有限时间内有效','//*[@id="root"]/div/div/form/div[8]/div[2]/div/div/div/label[3]/span[2]')

    def check_gouwutuan_ConsistentClaimTime_info(self):
        self.click('//*[@id="root"]/div/div/form/div[8]/div[2]/div/div/div/label[1]/span[1]')
        self.assert_text('与领取时间一致', '//*[@id="root"]/div/div/form/div[8]/div[2]/div/div/div/label[1]/span[2]')

    def check_gouwutuan_NoThresholdCoupon_info(self):
        self.click('//*[@id="root"]/div/div/form/div[9]/div[2]/div/div/div/label[2]/span[1]/span')
        self.assert_text('无门槛券', '//*[@id="root"]/div/div/form/div[9]/div[2]/div/div/div/label[2]/span[2]')

    def check_gouwutuan_FullReductionCoupon_info(self):
        self.click('//*[@id="root"]/div/div/form/div[9]/div[2]/div/div/div/label[1]/span[1]')
        self.assert_text('满减券','//*[@id="root"]/div/div/form/div[9]/div[2]/div/div/div/label[1]/span[2]')

    def check_gouwutuan_CouponName_info(self):
        self.send_keys('//*[@id="root"]/div/div/form/div[2]/div[2]/div[1]/div/span/input','99')
        assert len(self.find_element('//*[@id="root"]/div/div/form/div[2]/div[2]/div[1]/div/span/input').get_attribute('value')) <= 15

    def check_gouwutuan_InsertItem_info(self):
        self.click('//*[@id="root"]/div/div/form/div[3]/div[2]/div/div/div/label[2]/span[1]')
        self.click('//*[@id="root"]/div/div/form/div[4]/div[2]/div/div/button')
        self.assert_text('添加商品', '//*[@id="root"]/div/div/form/div[4]/div[2]/div/div/button/span')
        # 添加商品框内
        self.send_keys('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/span/input','11111')
        self.send_keys('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/div/div/input','22222')
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[1]/button')
        self.click('//*[@id="pro-form-wrapper"]/div/div[3]/div/div/div[2]/button')
        xpath1 = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div[1]/table/thead/tr/th[1]/div/label/span'
        xpath2_template = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div[1]/div[2]/div/div[2]/div/div/div/div/div/div/div/ul/li[{}]/a'
        for option in range(4, 9):
            self.click(xpath1)
            self.click(xpath2_template.format(option))
        for _ in range(7):
            self.click(xpath1)
            self.click(xpath2_template.format(8))
        assert  "已选商品 96/100" == self.find_element('/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div[2]/div[1]/span').text
        self.click("//button[@class='marketing-paas-modal-close' and @aria-label='Close']")

    def check_gouwutuan_CouponDenomination_info(self):
        self.send_keys('//*[@id="root"]/div/div/form/div[10]/div[2]/div/div/div/div[2]/input', '10000.00')
        assert self.find_element('//*[@id="root"]/div/div/form/div[10]/div[2]/div/div/div/div[2]/input').get_attribute(
            'value') == "10000.00"

    def check_gouwutuan_UsageConditions_info(self):
        self.send_keys('//*[@id="root"]/div/div/form/div[11]/div[2]/div[1]/div/div/div[2]/input', '100000.00')
        assert self.find_element('//*[@id="root"]/div/div/form/div[11]/div[2]/div[1]/div/div/div[2]/input').get_attribute(
            'value') == "100000.00"

    def check_gouwutuan_DistributedCount_info(self):
        self.send_keys('//*[@id="root"]/div/div/form/div[12]/div[2]/div[1]/div/span/input', '5000001')
        assert "张数最小为1，最大为5000000" == self.find_element('//*[@id="root"]/div/div/form/div[12]/div[2]/div[2]').text

    def check_gouwutuan_PerUserLimit_info(self):
        self.send_keys('//*[@id="root"]/div/div/form/div[13]/div[2]/div[1]/div/span/input', '6')
        assert "张数最小为1，最大为5" == self.find_element('//*[@id="root"]/div/div/form/div[13]/div[2]/div[2]').text

 # self.assert_text('+ 设置购物团等级','//*[@id="root"]/div/div/form/div[5]/div[2]/div/div/button/span')