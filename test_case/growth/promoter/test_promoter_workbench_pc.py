import pytest

from test_case.distribution.distribution_base_case import DistributionToolTest


class TestDistributionHome(DistributionToolTest):
    """
    python3 -m pytest test_case/distribution/talent/test_talent_home.py --html=test_data/leader_report.html --headless -n=3
    """

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    @pytest.mark.p0
    def test_enter_the_activity_square(self):
        """活动广场：判断是否进入活动广场"""
        self.talent_login("DISTRIBUTION_PROMTER", 'chenshuzhan')
        self.sleep(2)
        self.click("//span[contains(text(),'活动广场')]")
        self.sleep(2)
        self.assert_text("抢先参与", "//div[contains(text(),'抢先参与')]")
        self.assert_text("已参与", "//div[contains(text(),'已参与')]")
        self.assert_text("进行中", "//div[contains(text(),'进行中')]")

    @pytest.mark.p0
    def test_enter_the_activity_square_to_take_part_in(self):
        """活动广场：判断是否进入活动广场"""
        self.talent_login("DISTRIBUTION_PROMTER", 'chenshuzhan')
        self.sleep(2)
        self.click("//span[contains(text(),'活动广场')]")
        self.sleep(2)
        self.assert_text("抢先参与", "//div[contains(text(),'抢先参与')]")
        self.assert_text("已参与", "//div[contains(text(),'已参与')]")
        self.assert_text("进行中", "//div[contains(text(),'进行中')]")

        self.click("//div[@id='rc-tabs-0-tab-1']")
        self.sleep(2)
        if self.is_element_visible("//span[contains(text(),'扫码查看')]"):
            self.click("//span[contains(text(),'扫码查看')]")
    @pytest.mark.p0
    def test_enter_the_activity_square_doing(self):
        """活动广场：判断是否进入活动广场"""
        self.talent_login("DISTRIBUTION_PROMTER", 'chenshuzhan')
        self.sleep(2)
        self.click("//span[contains(text(),'活动广场')]")
        self.sleep(2)
        self.assert_text("抢先参与", "//div[contains(text(),'抢先参与')]")
        self.assert_text("已参与", "//div[contains(text(),'已参与')]")
        self.assert_text("进行中", "//div[contains(text(),'进行中')]")

        self.click("//div[@id='rc-tabs-0-tab-2']") #进行中
        self.sleep(2)
        if self.is_element_visible("//span[contains(text(),'扫码查看')]"):
            self.click("//span[contains(text(),'扫码查看')]")
    @pytest.mark.p0
    def test_enter_the_activity_square_done(self):
        """活动广场：判断是否进入活动广场"""
        self.talent_login("DISTRIBUTION_PROMTER", 'chenshuzhan')
        self.sleep(2)
        self.click("//span[contains(text(),'活动广场')]")
        self.sleep(2)
        self.assert_text("抢先参与", "//div[contains(text(),'抢先参与')]")
        self.assert_text("已参与", "//div[contains(text(),'已参与')]")
        self.assert_text("进行中", "//div[contains(text(),'进行中')]")

        self.click("//div[@id='rc-tabs-0-tab-3']")  # 已参与
        self.sleep(2)
        if self.is_element_visible("//span[contains(text(),'扫码查看')]"):
            self.click("//span[contains(text(),'扫码查看')]")