import pytest
from ddt import ddt
#from .base import BaseTestCase
from test_case.growth.kuaishouxiaodian.base import BaseTestCase
from utils.http_help import BaseHttpRequest
@pytest.mark.env("prt")
class TestTaskCenter(BaseTestCase):
    def checkout_module(self):
        self.login("MERCHANT_TASK_CENTER_DOMAIN","zhengguihua")
        self.driver.maximize_window()

    @pytest.mark.skip
    def test_taskCenter_award(self):
        """标题卡、奖励记录"""
        # 登录并打开任务中心页
        self.checkout_module()
        self.sleep(1)
        # 点击去掉弹窗
        self.kuaishou_shop_pop_up_click()

        self.assert_text("任务中心","//div[text()='任务中心']")
        self.assert_text("丰厚任务奖励，助力店铺经营", "//span[text()='丰厚任务奖励，助力店铺经营']")

        # 打开奖励记录抽屉页
        self.click("#root > div > div.taskAward___airv6 > div > div.record___xC77Q")
        self.sleep(2)
        self.assert_text("奖励记录","(//div[@class='kwaishop-seller-task-center-pc-drawer-title'][contains(text(),'奖励记录')])[1]")
        self.assert_text("查看余额","//span[text()='查看余额']")
        self.assert_text("已过期", "//div[text()='已过期']")
        # 奖励icon是否存在
        self.assert_element("//div[@class='list___PivMC']//div[1]//div[1]//div[1]//picture[1]//img[1]")
        # 发奖时间、奖励来源是否存在
        self.assert_element("(//div[@class='bottom___QRabe'])[1]")

        # 校验hover直播间曝光的查看余额是否展示二维码
        self.hover("(//span[contains(text(),'查看余额')])[1]")
        self.sleep(1)
        self.assert_element("(//div[contains(text(),'请使用快手App')])[1]")
        self.sleep(1)

        # 切换按钮-磁力金牛
        self.click("(//div[@id='rc-tabs-2-tab-3'])[1]")
        self.sleep(1)
        self.assert_text("1元磁力金牛（自助户）","//div[text()='1元磁力金牛（自助户）']")
        self.sleep(1)

        # 点击磁力金牛的查看余额按钮
        self.click("(//span[contains(text(),'查看余额')])[2]")
        self.sleep(2)
        self.assert_equal("https://niu.e.kuaishou.com/newFinance/accountWallet", self.get_current_url())
        self.sleep(1)
        self.switch_to_window(0)
        self.sleep(1)

        # 点击快币的查看余额按钮
        self.click("(//div[@id='rc-tabs-2-tab-2'])[1]")
        self.sleep(1)
        self.hover("(//span[contains(text(),'查看余额')])[3]")
        self.sleep(1)
        self.assert_element("//div[@class='kwaishop-seller-task-center-pc-dropdown kwaishop-seller-task-center-pc-dropdown-show-arrow kwaishop-seller-task-center-pc-dropdown-placement-bottomCenter ']//div[contains(text(),'请使用快手App')]")
        self.sleep(1)

        # 点击短视频的查看余额按钮
        self.click("(//div[@id='rc-tabs-2-tab-15'])[1]")
        self.sleep(1)
        self.hover("(//span[contains(text(),'查看余额')])[2]")
        self.sleep(1)
        self.assert_element("div[class='kwaishop-seller-task-center-pc-dropdown kwaishop-seller-task-center-pc-dropdown-show-arrow kwaishop-seller-task-center-pc-dropdown-placement-bottomCenter '] canvas")
        self.sleep(1)

        # 关闭奖励记录抽屉
        self.click("(//img[@class='closeIcon___D1qrX'])[1]")
        self.sleep(1)
        self.assert_element_not_visible("html > div > div > div.kwaishop-seller-task-center-pc-drawer-content-wrapper > div > div > div.kwaishop-seller-task-center-pc-drawer-body")


    def test_taskCenter_task(self):
        """成长任务列表"""
        self.checkout_module()
        self.sleep(1)
        self.kuaishou_shop_pop_up_click()

        # 待报名列表
        self.assert_text("待报名", "(//div[@id='rc-tabs-0-tab-drawing'])[1]")
        self.sleep(2)
        # 判断当前进入的列表是进行中还是待报名
        if self.is_element_visible("(//span[contains(text(),'已报名')])[11]") == True:
            # 切换到待报名列表
            self.click("(//div[@id='rc-tabs-0-tab-drawing'])[1]")
            self.sleep(1)
            self.assert_text("暂无待报名活动", "(//div[@class='kwaishop-seller-task-center-pc-empty-description'][contains(text(),'暂无待报名活动')])[1]")
            self.sleep(1)
        else:
            self.assert_text("未报名", "//span[text()='未报名']")
            self.assert_text("免费报名","(//span[contains(text(),'免费报名')])[1]")
            if "更多" in self.get_text("(//div[@class='bottom___gq8IC'])[1]"):
                self.hover("#rc-tabs-0-panel-drawing > div > div.taskList___qYuK9 > div:nth-child(1) > div > div.bottom___gq8IC > span.more___z_y81")
                self.sleep(1)
                self.assert_element("(//div)[214]")

            self.hover("(//span[contains(text(),'免费报名')])[1]")
            self.sleep(1)
            if self.is_element_visible("(//div[@class='Y5ow5B6HbSENITpaXz8q'])[1]") == True:

                self.assert_text("扫码查看", "(//div[contains(text(),'扫码查看')])[1]")
            else:
                # 点击免费报名按钮
                self.click(
                    "(//button[@class='kwaishop-seller-task-center-pc-btn kwaishop-seller-task-center-pc-btn-primary'])[1]")
                self.sleep(1)
                # 报名弹窗
                self.assert_text("立即免费报名","//span[text()='立即免费报名']")
                self.assert_text("查看详情","//span[text()='查看详情']")
                self.assert_text("报名即为同意活动规则","//div[text()='报名即为同意活动规则']")
                self.assert_text("查看活动协议", "//span[text()='查看活动协议']")
                # 点击查看协议
                self.click("(//span[@class='kpro-seller-promotion-task-card--modal--bot--highlight'])[1]")
                self.sleep(1)
                self.assert_equal("https://fangzhou.kwaixiaodian.com/kh5/GaQExMZRBMSn/EaWgOguiHuQw?layoutType=4&hyId=EaWgOguiHuQw", self.get_current_url())
                # 切换回任务中心
                self.switch_to_window(0)
                self.sleep(1)
                # hover查看详情
                self.hover("(//span[contains(text(),'查看详情')])[1]")
                self.sleep(1)
                # 判断是否出现二维码
                if self.is_element_visible(".kpro-seller-promotion-task-card--qrcode") == True:
                    self.assert_text("扫码查看", "(//div[contains(text(),'扫码查看')])[1]")
                    self.sleep(1)
                else:
                    # 点击查看详情
                    self.click("(//button[@class='kwaishop-seller-task-center-pc-btn'])[1]")
                    self.sleep(1)
                    self.assert_text("活动详情","//div[text()='活动详情']")
                    self.assert_text("火热报名中","(//div[@class='tag___Fpsl5'])[1]")
                    self.assert_text("任务列表", "(//div[@class='text___aJhe9'])[1]")
                    self.hover("/html/div/div/div[2]/div/div/div[2]/div/div/div[2]/div[2]")
                    self.sleep(1)
                    self.assert_text("暂未获得奖励","(//div[@class='awardTitle___kEarU'])[1]")
                    self.click("html > div > div > div.kwaishop-seller-task-center-pc-drawer-content-wrapper > div > div > div.kwaishop-seller-task-center-pc-drawer-body > div > div > div.taskDeatilcard___uXxKw > div.bottom___k7d5P > button")
                    self.sleep(2)
                    self.assert_text("立即免费报名","(//span[contains(text(),'立即免费报名')])[2]")
                    self.click("(//span[contains(text(),'查看详情')])[2]")
                    self.click("html > div > div > div.kwaishop-seller-task-center-pc-drawer-content-wrapper > div > div > div.kwaishop-seller-task-center-pc-drawer-header > div.kwaishop-seller-task-center-pc-drawer-extra > picture > img")

                    self.sleep(1)

        # 点击进行中tab
        self.click("(//div[@id='rc-tabs-0-tab-processing'])[1]")
        self.sleep(1)
        self.assert_text("查看详情","(//span[contains(text(),'查看详情')])[11]")
        # 点击已结束tab
        self.click("(//div[@id='rc-tabs-0-tab-finish'])[1]")
        self.sleep(1)
        if "活动已结束" in self.get_text("#rc-tabs-0-panel-finish > div > div.taskList___qYuK9 > div:nth-child(1) > div > div.content___P3Zka > div.left___dmc5N > div.timeLine___Bhn7X"):
            self.assert_true(1)


    def test_taskCenter_dingZhiJiHua(self):
        """定制计划"""
        self.login("MERCHANT_TASK_CENTER_DOMAIN", "csr")
        self.driver.maximize_window()
        self.kuaishou_shop_pop_up_click()

        self.click("(//div[@id='rc-tabs-1-tab-autoTest'])[1]")
        self.sleep(1)
        self.assert_text("火热报名中","(//div[@class='tag____9T0F'])[1]")
        self.click("(//span[contains(text(),'免费报名')])[1]")
        self.sleep(1)
        self.assert_text("立即免费报名","(//span[contains(text(),'立即免费报名')])[1]")
        self.click("(//*[name()='svg'][@class='kwaishop-seller-task-center-pc-modal-close-icon'])[1]")
        self.sleep(1)
        self.assert_text("未报名",'//*[@id="rc-tabs-1-panel-autoTest"]/div/div[2]/div[2]/div[1]/div/div/button')
        # 切换到大爆发计划
        self.click("(//div[@id='rc-tabs-1-tab-daBaoFaGrowth'])[1]")
        self.sleep(1)
        self.assert_text("展开更多任务","(//div[@class='text___AWmnO'])[1]")
        self.click("(//div[@class='text___AWmnO'])[1]")
        self.sleep(1)
        self.assert_text("收起","(//div[@class='text___AWmnO'])[1]")
        self.sleep(1)

        self.click("(//div[@id='rc-tabs-1-tab-daBaoFaGrowth'])[1]")
        self.sleep(1)
        self.assert_text("活动已结束","(//div[@class='end___ACySp'])[1]")


    def kuaishou_shop_pop_up_click(self):
        self.sleep(1)
        self.click("(//div[@class='header___QvD3I'])[1]")
        self.click("(//div[@class='header___QvD3I'])[1]")
        self.click("(//div[@class='header___QvD3I'])[1]")
        self.sleep(1)
        self.click("(//div[@class='header___QvD3I'])[1]")
        self.click("(//div[@class='header___QvD3I'])[1]")
        self.sleep(1)














