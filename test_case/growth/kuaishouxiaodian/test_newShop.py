from unittest import skip

import pytest
from ddt import ddt
from test_case.growth.kuaishouxiaodian.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
@pytest.mark.env("prt")
class TestNewShop(BaseTestCase):


    def checkout_moudle(self):
        self.login("MERCHANT_HOME_DOMAIN", "zgh_beidian")
        self.driver.maximize_window()



    def test_newShop_1(self):
        """备店一阶段"""
        self.checkout_moudle()
        self.sleep(5)

        # 校验是否展示备店任务一阶段模块
        self.is_element_visible('document.querySelector("#NewSellerMustTaskPc\\#generalModel > div > div")')
        # 点击查看教程
        self.click("(//a[contains(text(),'查看教程')])[1]")
        self.sleep(2)
        # 校验跳转后的链接是否正确
        self.assertEqual("https://university.kwaixiaodian.com/kwaishop/newKnowledge/676145180626022415/581287704722800678", self.get_current_url())
        self.sleep(1)
        self.switch_to_window(0)
        self.sleep(2)
        # hover 去认证按钮
        self.hover('#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(1) > div > div.kpro-store-novice-task-step-item-right > span')
        self.sleep(2)
        if self.is_element_visible("#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(1) > div > div.kpro-store-novice-task-step-item-right > div > div > div > div > div.ant-tooltip-inner"):
            self.assert_text("资质审核通过超过90天，不可操作", "#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(1) > div > div.kpro-store-novice-task-step-item-right > div > div > div > div > div.ant-tooltip-inner")
        else:
            # 点击去认证按钮
            self.click('//*[@id="NewSellerMustTaskPc#generalModel"]/div/div/div/div[1]/div[2]/button')
            self.sleep(2)
            # 校验是否进入真实性认证页
            self.assert_text("法人认证", '//span[text()="法人认证"]')
            # 点击退出认证
            self.click("(//button[contains(@class,'kwaishop-kcube-qualification-pc-btn kwaishop-kcube-qualification-pc-btn-round')])[1]")
            self.sleep(1)
            # 校验是否弹出弹窗
            self.assert_text("退出", '//div[text()="退出"]')
            # 点击关闭弹窗
            self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")
            self.sleep(1)
            # 校验弹窗是否关闭
            self.assert_element_not_visible("(//div[@class='kwaishop-kcube-qualification-pc-modal-body'])[1]")
            # 再次点击退出认证
            self.click(
                "(//button[contains(@class,'kwaishop-kcube-qualification-pc-btn kwaishop-kcube-qualification-pc-btn-round')])[1]")
            self.sleep(1)
            # 点击不保存按钮
            self.click("(//button[@class='kwaishop-kcube-qualification-pc-btn kwaishop-kcube-qualification-pc-btn-sm'])[1]")
            self.sleep(3)
            # 校验是否退出真实性认证页
            self.assert_element_not_visible('document.querySelector("#root > div > div.kwaishop-kcube-qualification-pc-spin-nested-loading.formPageMain > div > div > div > div.kwaishop-kcube-qualification-pc-formily-item.kwaishop-kcube-qualification-pc-formily-item-layout-horizontal.kwaishop-kcube-qualification-pc-formily-item-feedback-layout-loose.kwaishop-kcube-qualification-pc-formily-item-label-align-right.kwaishop-kcube-qualification-pc-formily-item-control-align-left > div > div > div > div > div:nth-child(2) > div > span.roleTitle")')

        # 点击查看0元开店规则
        self.click("(//a[contains(text(),'查看0元开店规则')])[1]")
        self.sleep(2)
        # 校验跳转后的链接是否正确
        self.assertEqual("https://edu.kwaixiaodian.com/bbs/web/article?id=20731&layoutType=4", self.get_current_url())
        self.switch_to_window(0)
        # 点击缴纳店铺保证金视频
        self.click("#sellerPrepareShopPc\#C1 > div > div > div > div.kpro-store-novice-task-step-item.kpro-store-novice-task-step-item-done > div > div.kpro-store-novice-task-step-item-left > img")
        self.sleep(2)
        # 校验跳转后链接是否正确
        self.assertEqual("https://university.kwaixiaodian.com/kwaishop/courseDetail?courseId=89b9660d5066469f96ea957e3db79e50&layoutType=4", self.get_current_url())
        self.switch_to_window(0)

        # 点击开通收款账户视频
        # self.click("(//img[@alt='开通收款账户'])[1]")
        # self.sleep(2)
        # # 校验跳转后链接是否正确 链接过期，更新后打开
        # # self.assertEqual("https://www.kuaishou.com/short-video/3xrzvrkrapjbwee?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=NEBULA&subBiz=PHOTO&photoId=3xrzvrkrapjbwee&shareId=17982044088558&shareToken=X-293sKbI5BKy21E&shareResourceType=PHOTO_OTHER&userId=3x5xw9pp8wf6nkm&shareType=1&et=1_a%252F0_unknown0&shareMode=APP&efid=3xgsh3yrw52qkvm&originShareId=17982044088558&appType=21&shareObjectId=129053438847&shareUrlOpened=0&timestamp=1721181716035&utm_source=app_share&utm_medium=app_share&utm_campaign=app_share&location=app_share", self.get_current_url())
        # self.switch_to_window(0)
        # 点击攻略
        self.click("(//a[contains(text(),'攻略')])[1]")
        self.sleep(2)
        # 校验跳转后的链接是否正确
        self.assertEqual("https://fangzhou.kwaixiaodian.com/kh5/OONWltQqAgWR/fMblOYcgXidk?layoutType=4&hyId=fMblOYcgXidk", self.get_current_url())
        self.switch_to_window(0)

        # 缩小经营助手
        self.click("#kpro-tool-box--sellerHelperBox > div.middleBoxTitle___Q8qIR > img")
        self.sleep(1)

        # 悬停校验去开通按钮出现禁用提示
        self.hover('#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(3) > div > div.kpro-store-novice-task-step-item-right > span')
        self.sleep(1)
        # self.assert_false(self.is_element_clickable("#NewSellerMustTaskPc\#generalModel > div > div > div > div:nth-child(3) > div.kpro-store-novice-task-step-item-right > span"))
        self.assert_text("请先完成真实性认证", "#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(3) > div > div.kpro-store-novice-task-step-item-right > div > div > div > div > div.ant-tooltip-inner")
        self.sleep(1)
        # 按钮禁用效果下次补充

        # 点击去核对
        self.click('#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(4) > div > div.kpro-store-novice-task-step-item-right > button')
        self.sleep(1)
        # 校验是否弹出弹窗
        self.assert_text("请核对以下信息是否正确、真实，避免影响商品正常售卖及售后；", '//div[text()="请核对以下信息是否正确、真实，避免影响商品正常售卖及售后；"]')
        # 点击关闭按钮
        self.click("(//span[@class='ant-modal-close-x'])[1]")
        self.sleep(1)
        # 校验弹窗是否关闭
        self.assert_element_not_visible('/html/body/div[10]/div/div[2]/div/div[2]/div[2]/div[2]/div/div')

        # 点击去完善
        self.click('#sellerPrepareShopPc\#C1 > div > div > div > div:nth-child(5) > div > div.kpro-store-novice-task-step-item-right > button')
        self.sleep(1)
        # 校验弹窗是否打开
        self.is_element_visible('/html/body/div[10]/div/div[2]/div/div[2]/div/div/div/div[1]/div[1]')
        # 点击关闭按钮
        self.click("(//span[@class='ant-modal-close-x'])[1]")
        self.sleep(1)
        # 校验是否弹出二次确认弹窗
        self.is_element_visible('/html/body/div[11]/div/div[2]/div/div[2]/div/div/div[1]/div')
        # 确认关闭
        self.click("(//button[@class='ant-btn'])[1]")
        self.sleep(1)
        # 校验弹窗是否关闭
        self.assert_element_not_visible("/html/body/div[10]/div/div[2]/div/div[2]/div/div/div/div[1]/div[1]")
        self.sleep(1)

        # 还原经营助手
        self.click("#kpro-tool-box--sellerHelperBox > span")
        self.sleep(2)



    @pytest.mark.skip
    def test_newShop_2(self):
        """备店二阶段"""
        self.checkout_moudle()
        self.sleep(5)

        # 页面向下滚动
        self.driver.execute_script("window.scrollBy(0, 30000)")
        self.sleep(1)
        # 校验备店二阶段是否展示
        self.assert_text("二、新开店设置完成后，即可发布商品，获取流量", "(//h2[@class='kpro-store-novice-publish-task-main-title'])[1]")

        # 点击发布商品视频
        self.click("(//img[@alt='完成新开店设置，即可发布商品'])[1]")
        self.sleep(2)
        # 校验跳转后链接是否正确
        self.assertEqual("https://university.kwaixiaodian.com/kwaishop/courseDetail?courseId=6cdf215a815a47dfbbbecc7297a79374&layoutType=4", self.get_current_url())
        self.switch_to_window(0)

        # 点击导入站外商品的查看攻略
        self.click("(//span[contains(text(),'查看攻略')])[1]")
        self.sleep(2)
        # 校验跳转后链接是否正确
        self.assertEqual("https://docs.qingque.cn/d/home/<USER>", self.get_current_url())
        self.switch_to_window(0)

        # 点击手动发布商品的查看攻略
        self.click("(//span[contains(text(),'查看攻略')])[2]")
        self.sleep(2)
        # 校验跳转后链接是否正确
        self.assertEqual("https://university.kwaixiaodian.com/kwaishop/newKnowledge/581515700746567740/581287737803169849", self.get_current_url())
        self.switch_to_window(0)

        # 一键商品搬家 按钮是否禁用
        self.sleep(1)
        self.assert_false(self.is_element_clickable('#NewSellerPublishTask\#generalModelPc > div > div > div > div:nth-child(2) > div.kpro-store-novice-publish-task-way-item-header > div.kpro-store-novice-publish-task-way-item-header-right > button'))
        # 立即发布 按钮是否禁用
        self.assert_false(self.is_element_clickable('#NewSellerPublishTask\#generalModelPc > div > div > div > div:nth-child(3) > div > div.kpro-store-novice-publish-task-way-item-header-right > button'))


