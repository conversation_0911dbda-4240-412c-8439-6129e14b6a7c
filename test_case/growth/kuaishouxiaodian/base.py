from time import sleep

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain
# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env


class BaseTestCase(BaseCase):

        def login(self, domain, account):
            account_data = get_account_info(account)
            # 用户名 account_data['account']
            # 密码 account_data['password']
            host = get_domain(domain)

            self.open(host)
            self.sleep(2)

            if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                    "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
                self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
                self.type("input[placeholder='请输入手机号']", account_data['account'])
                self.sleep(1)
                self.type("input[placeholder='请输入密码']", account_data['password'])
                self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
                self.sleep(2)
            else:
                self.click("(//span[contains(text(),'密码登录')])[1]")
                self.sleep(1)
                self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
                self.sleep(1)
                self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
                self.sleep(1)
                self.click(
                    "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
                self.sleep(2)

        def login2(self, domain, accountNo, pwd):
            # account_data = get_account_info(account)
            # 用户名 account_data['account']
            # 密码 account_data['password']
            host = get_domain(domain)

            self.open(host)
            self.sleep(3)
            # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            # self.click('//*[@id="username"]')
            # self.type('//*[@id="username"]', accountNo)
            # self.click('//*[@id="password"]')
            # self.type('//*[@id="password"]', pwd)
            # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", accountNo)
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", pwd)
            self.sleep(1)
            self.click("(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        # 天河测试平台使用勿动
        def login3(self, host, accountNo, pwd):
            # "https://sso.corp.kuaishou.com/cas/login"
            self.open(host)
            self.sleep(1)
            self.click("//div[@id='loginTabSso']")
            self.click('//input[@id=\'ssoUsernameInput\']')
            self.type('//input[@id=\'ssoUsernameInput\']', accountNo)
            self.click('//input[@id=\'ssoPasswordInput\']')
            self.type('//input[@id=\'ssoPasswordInput\']', pwd)
            self.click('//button[@id=\'ssoSubmit\']')

        def login4(self, host, accountNo, pwd):
            #https://apsso.corp.kuaishou.com/apsso
            self.open(host)
            self.sleep(2)
            # 点击账号密码
            self.click("//div[@id='loginTabSso']")
            self.click("//input[@id='ssoUsernameInput']")
            self.type("//input[@id='ssoUsernameInput']", accountNo)
            self.click("//input[@id='ssoPasswordInput']")
            self.type("//input[@id='ssoPasswordInput']", pwd)
            self.click("//label[@id='ssoSubmitLabel']")

        def loginAI(self, url):
            if "cps" in url:
                account_data = get_account_info("wb_caijinwei")

                self.open(url)
                self.sleep(1)

                if self.is_element_visible("(//span[contains(text(),'密码登录')])[1]") or self.is_element_visible(
                        "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
                    self.click("(//span[contains(text(),'密码登录')])[1]")
                    self.sleep(1)
                    self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
                    self.sleep(1)
                    self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
                    self.sleep(1)
                    self.click(
                        "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
                    self.sleep(2)
                else:
                    self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
                    # self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
                    # self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
                    self.sleep(2)
                    self.click("//div[@class='choseTab___okqX0']//div[2]")
                    self.type("input[placeholder='请输入手机号']", account_data['account'])
                    self.sleep(1)
                    self.type("input[placeholder='请输入密码']", account_data['password'])
                    self.click("button[type='button']")
                    self.sleep(5)
                self.sleep(3)
                self.refresh()

        # 天河测试平台使用勿动
        def login3(self, host, accountNo, pwd):
            # "https://sso.corp.kuaishou.com/cas/login"
            self.open(host)
            self.sleep(0.5)
            self.click('//*[@id="loginTabSso"]')
            self.click('//*[@id="ssoUsernameInput"]')
            self.type('//*[@id="ssoUsernameInput"]', accountNo)
            self.click('//*[@id="ssoPasswordInput"]')
            self.type('//*[@id="ssoPasswordInput"]', pwd)
            self.click('//*[@id="ssoSubmit"]')
