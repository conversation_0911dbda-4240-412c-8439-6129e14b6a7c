from unittest import skip

import pytest
import re
from ddt import ddt
from test_case.growth.kuaishouxiaodian.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
@pytest.mark.env("prt")
class TestFuLiKaPian(BaseTestCase):


    def checkout_moudle(self):
        self.login("MERCHANT_HOME_DOMAIN", "zhengguihua")
        self.driver.maximize_window()

    def kuaishou_shop_pop_up_click(self):
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.sleep(1)
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.click("(//img[@alt='logo'])[1]")
        self.sleep(1)


    def close_pop_click(self):
        self.sleep(1)
        # 如果会员过期提示窗存在，关掉
        if self.is_element_visible("(//div[@class='kwaishop-seller-clean-member-pc-modal-body'])[1]"):
            self.click("(//*[name()='svg'][@clip-rule='evenodd'])[1]")
            self.sleep(1)

        # 如果经营待办弹窗存在
        if self.is_element_visible(
                '#main-pc-page-layout > div.HkCTsYZjM3B7nYatmuHR > div > div:nth-child(2) > div > div > div > div.ant-popover-inner > div > div > div:nth-child(1)') == True:
            # 关掉经营待办的弹窗
            self.click(
                '//*[@id="main-pc-page-layout"]/div[2]/div/div[2]/div/div/div/div[2]/div/div/div[2]/div')
            self.sleep(1)

        # 如果新商成长计划弹窗存在
        if self.is_element_visible("#main-pc-atmosphere-layout > div.seller-main-modal-root > div.seller-main-modal-wrap.kpro-notice-modal-pictext-wrap.seller-main-modal-centered > div > div.kpro-notice-modal-pictext-box > div.kpro-notice-modal-pictext-cont"):
            # 关掉弹窗
            self.click("#main-pc-atmosphere-layout > div.seller-main-modal-root > div.seller-main-modal-wrap.kpro-notice-modal-pictext-wrap.seller-main-modal-centered > div > div.kpro-notice-modal-pictext-box > div.kpro-notice-modal-pictext-cont > div.kpro-notice-modal-pictext-btn > button")



    def test_fulikapian(self):
        self.checkout_moudle()
        self.sleep(5)

        self.kuaishou_shop_pop_up_click()
        # 如果定制计划卡出现，执行一下操作
        if self.is_element_visible("(//img[@class='kpro-seller-promotion-task-card--banner'])[1]"):

            if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
                self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
            self.assert_text("任务中心", "//div[text()='任务中心']")
            self.sleep(5)

            self.kuaishou_shop_pop_up_click()

            if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
                self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")

            self.close_pop_click()

            self.click('//*[@id="TaskAggregationPc#C1"]/div/div/div[1]/div[2]')
            self.sleep(1)
            self.assertEqual("https://s.kwaixiaodian.com/zone/cultivation/taskCenter/page?source=card_view_more", self.get_current_url())
            self.sleep(1)
            self.switch_to_window(0)

            # 点击页面来跳过弹窗
            self.kuaishou_shop_pop_up_click()


            self.sleep(1)
            #点击定制计划卡片
            self.click("(//img[@class='kpro-seller-promotion-task-card--banner'])[1]")
            self.sleep(1)
            self.assertEqual("https://s.kwaixiaodian.com/zone/cultivation/taskCenter/page?isPlanExpand=true&source=card_banner", self.get_current_url())
            self.sleep(1)
            self.switch_to_window(0)
            self.sleep(2)

            # 免费报名按钮-点击出弹窗
            d1 = "(//div[@class='kpro-seller-promotion-task-card--module--info--button'])[1]"
            # 免费报名按钮-点击出二维码
            d2 = '//*[@id="TaskAggregationPc#C1"]/div/div/div[2]/div[3]/div/div[1]/span'

            if self.is_element_visible('//*[@id="TaskAggregationPc#C1"]/div/div/div[2]/div[3]/div/div[1]/div[2]') == True:
                # 判断是否为待报名活动
                if self.get_text(d1) == "免费报名":
                    # 判断有奖励的活动弹窗
                    if self.is_element_visible('//*[@id="TaskAggregationPc#C1"]/div/div/div[2]/div[3]/div/div[3]'):

                        self.close_pop_click()

                        self.click(d1)
                        self.sleep(1)
                        self.assert_text("立即免费报名", '//span[text()="立即免费报名"]')
                        self.assert_text("查看详情", "//span[text()='查看详情']")
                        if self.is_element_visible("(//p[@class='kpro-seller-promotion-task-card--modal--content--left--title'])[1]") == True:
                            self.assert_text("活动可获得奖励","(//p[@class='kpro-seller-promotion-task-card--modal--content--left--title'])[1]")
                        else:
                            self.assert_text("请选择奖励形式","(//p[@class='kpro-seller-promotion-task-card--modal--check'])[1]")
                        self.sleep(2)
                        # hover查看详情按钮
                        self.hover("//button[@type='button']//span[contains(text(),'查看详情')]")
                        self.sleep(1)
                        # 判断二维码是否出现
                        if self.is_element_visible("(//div[@class='kpro-seller-promotion-task-card--qrcode'])[1]") == True:
                            self.assert_element("(//div[contains(text(),'扫码查看')])[1]")
                        else:
                            self.click("(//button[@class='kpro-seller-promotion-task-card-btn'])[1]")
                            self.sleep(2)
                            self.assert_element("/html/div/div/div[2]/div/div/div[2]")
                            self.switch_to_window(0)
                            self.sleep(1)

                    else:
                        # 无奖励的活动弹窗
                        self.close_pop_click()
                        self.click(d1)
                        self.sleep(1)
                        self.assert_text("立即免费报名", '//span[text()="立即免费报名"]')
                        self.assert_text("查看详情", "//span[text()='查看详情']")
                        self.assert_text_not_visible("活动可获得奖励",
                                                 "/html/body/div[6]/div/div[2]/div/div[2]/div[2]")
                        self.sleep(2)

                else:
                    self.close_pop_click()
                    if self.is_element_visible('//*[@id="TaskAggregationPc#C1"]/div/div/div[2]/div[3]/div/div[1]/div[2]') == True:
                        self.click('//*[@id="TaskAggregationPc#C1"]/div/div/div[2]/div[3]/div/div[1]/div[2]')
                        self.sleep(2)
                        self.assert_element("/html/div/div/div[2]/div/div/div[2]")
                        self.switch_to_window(0)
                        self.sleep(1)
                    else:
                        self.close_pop_click()
                        #二维码浮窗
                        self.hover('//*[@id="TaskAggregationPc#C1"]/div/div/div[2]/div[2]/div/div[1]/span')
                        self.sleep(1)
                        self.assert_text("扫码查看", "(//div[contains(text(),'扫码查看')])[1]")

            else:
                self.close_pop_click()
                self.hover(d2)
                self.sleep(1)
                self.assert_text("扫码查看","(//div[contains(text(),'扫码查看')])[1]")

        else:
            # 大促活动卡文案是否正确
            self.assert_text("做大促任务 得奖励", "//div[text()='做大促任务 得奖励']")
            # 点击更多任务按钮
            self.click("#TaskAggregationPc\#C1 > div > div > div.kpro-seller-promotion-reward-card--header > div.kpro-seller-promotion-reward-card--header--subtitle")
            self.assertEqual("https://s.kwaixiaodian.com/zone/cultivation/taskCenter/page?source=card_view_more", self.get_current_url())
            self.switch_to_window(0)
            self.sleep(1)
            # 活动标题
            self.hover("#TaskAggregationPc\#C1 > div > div > div.kpro-seller-promotion-reward-card--content > div > div.kpro-seller-promotion-reward-card--content--item--header > div.kpro-seller-promotion-task-card-dropdown-trigger.kpro-seller-promotion-reward-card--content--item--header--title")
            if self.is_element_visible("(//div[@class='kpro-seller-promotion-reward-card--qrContainer'])[1]"):
                self.assert_text("进入任务详情","//div[text()='进入任务详情']")
            # 活动倒计时
            self.assert_text("距活动结束还剩", "//div[text()='距活动结束还剩']")
            # 任务标题
            self.assert_text("做任务 得", "//div[text()='做任务 得']")
            # 指标
            self.assert_text("目标", "//div[text()='目标']")
            # 获取任务标题
            te = self.get_text(
                '#TaskAggregationPc\#C1 > div > div > div.kpro-seller-promotion-reward-card--content > div > div.kpro-seller-promotion-reward-card--body > div:nth-child(1) > div > div.kpro-seller-promotion-reward-card--card--content > div.kpro-seller-promotion-reward-card--card--content--title > div.kpro-seller-promotion-reward-card--card--content--title--test')
            # 剔除标题其他字段得到奖励
            te1 = re.sub("做任务 得", '', te)
            # 获取hover中的奖励
            self.hover(
                '#TaskAggregationPc\#C1 > div > div > div.kpro-seller-promotion-reward-card--content > div > div.kpro-seller-promotion-reward-card--body > div:nth-child(1) > div > div.kpro-seller-promotion-reward-card--card--content > div.kpro-seller-promotion-reward-card--card--content--title > div.kpro-seller-promotion-reward-card--card--content--title--test')
            self.sleep(1)
            te2 = self.get_text(
                "(//div[@class='kpro-seller-promotion-task-card-popover-inner-content'][contains(text(),'2000 次直播间曝光')])[1]")
            # 对比hover奖励是否包含标题奖励
            assert te1 in te2

