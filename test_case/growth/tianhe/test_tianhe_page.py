import pytest

from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from test_case.growth.kuaishouxiaodian.base import BaseTestCase

class TestTianHeHomePage(BaseTestCase):

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()

    def open_tianhe_homepage(self):
        """打开天河首页"""
        sso_host = "https://sso.corp.kuaishou.com/cas/login"
        self.login3(sso_host, "wb_chen<PERSON><PERSON>", "2475962Yy")
        self.get("https://tianhe.corp.kuaishou.com/zone/tianhe/my_workbench")
        self.sleep(3)
        self.assert_text("我的迭代", "//div[contains(text(),'我的迭代')]")
        self.assert_text("应用列表", "//div[contains(text(),'应用列表')]")
        self.assert_text("为你推荐", "//div[@class='kpro-common-tianhe-workbenches-application-recommend-card-panel-head-title']")


    def webdriver_wait(self, method, element, timeout=10):
        """元素隐式等待封装，默认限制10s"""
        WebDriverWait(self.driver, timeout).until(EC.invisibility_of_element_located((method, element)))
        return

    @pytest.mark.p0
    def test_service_construction(self):
        """点击「我的迭代」模块下，「更多迭代」按钮，搜索"ui自动化"，点击开发，跳转至应用详情"""
        self.open_tianhe_homepage()
        # self.click("(//div[@class='kpro-common-tianhe-workbenches-iterative-card--body'])[1]")
        self.click("//span[contains(text(),'更多迭代 >')]")
        self.send_keys("//input[@id='changeName']", "ui自动化")
        self.click("//button[@type='button']//span[contains(text(),'开发')]")
        self.sleep(2)
        self.click("//div[contains(text(),'服务搭建')]")
        self.assert_text("服务搭建", "//div[contains(text(),'服务搭建')]")
        self.assert_text("页面搭建", "//div[contains(text(),'页面搭建')]")
        self.assert_text("部署发布", "//div[contains(text(),'部署发布')]")

    def test_knowledge_tab01(self):
        """点击首页顶部创建应用，跳转至文档说明"""
        self.open_tianhe_homepage()
        self.click("//div[@class='kpro-common-tianhe-workbenches-head-card--right'][contains(text(),'创建应用')]")
        self.assert_text("创建入口", "//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'创建入口')]")

    def test_knowledge_tab02(self):
        """点击首页顶部发布管理，跳转至文档说明"""
        self.open_tianhe_homepage()
        self.click("//div[contains(text(),'发布管理')]")
        self.assert_text("单资源部署", "//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'单资源部署')]")

    def test_page_construction(self):
        """点击「页面搭建」,跳转至「页面搭建」页面"""
        self.test_service_construction()
        self.click("//div[contains(text(),'页面搭建')]")
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/span/button[2]'):
                self.click('//*[@id="driver-popover-item"]/div[4]/span/button[2]')
        else:
            pass
        # 判断是否有 其他人正在编辑的弹窗
        if self.is_element_visible("//span[@class='tianhe-main-pc-modal-confirm-title']"):
            self.click("//button[@class='tianhe-main-pc-btn tianhe-main-pc-btn-primary']")
        else:
            pass
        self.sleep(3)
        self.assert_text("MUI", "//div[@class='ant-tabs ant-tabs-top assets-list-header-tab']//div[@class='ant-tabs-tab ant-tabs-tab-active']")
        self.assert_text("高级", "//div[@id='kael-skeleton-left-side-panel']//div[@class='ant-tabs-nav-wrap']//div[2]")
        self.click("//span[contains(text(),'导入')]")
        self.sleep(2)
        self.assert_text("物料导入", "//div[@class='ant-modal-header']")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_deploy_release(self):
        """进入迭代详情后，点击「部署发布」按钮，跳转至「部署发布」页面"""
        self.test_service_construction()
        self.click("//div[contains(text(),'部署发布')]")
        self.sleep(2)
        self.assert_text("变更资源明细", "//span[@class='VgvGt8RIRWOTkWMuO0LF']")

    def test_stage_switching(self):
        """应用管理：部署进度模块 - 阶段的切换（1开发阶段；2测试阶段；3发布阶段）"""
        self.test_deploy_release()
        #开发阶段切换
        self.click("//div[contains(text(),'开发阶段')]")
        self.assert_text("一键部署", "//span[contains(text(),'一键部署')]")
        self.assert_text("提交测试", "//span[contains(text(),'提交测试')]")
        #测试阶段切换
        self.click("//div[contains(text(),'测试阶段')]")
        self.assert_text("退回开发阶段", "//span[contains(text(),'退回开发阶段')]")
        self.assert_text("测试准出", "//span[contains(text(),'测试准出')]")
        #发布阶段切换
        self.click("//div[contains(text(),'发布阶段')]")
        self.assert_text("发布进度", "//div[@class='_header_odqmv_1 lingzhu-sdk']")

    def test_edit_button(self):
        """应用管理：校验 变更详情模块 - 点击 右上角「编辑」icon弹窗"""
        self.test_deploy_release()
        self.click("//span[@aria-label='edit']//*[name()='svg']")
        self.assert_text("修改变更单", "//body/div/div[@class='tianhe-main-pc-modal-root']/div[2]/div[1]/div[2]/div[1]")
        self.assert_text("变更名称", "//label[contains(text(),'变更名称')]")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_one_click_deployment01(self):
        """应用管理：校验 开发阶段 - 点击 「一键部署」button弹窗"""
        self.test_deploy_release()
        self.click("//div[contains(text(),'发布阶段')]")
        if self.is_element_visible("//span[contains(text(),'退回开发阶段')]"):
            self.click("//span[contains(text(),'退回开发阶段')]")
            self.click("//span[contains(text(),'确 定')]")
        else:
            self.sleep(2)
        self.click("//div[contains(text(),'开发阶段')]")
        self.click("//span[contains(text(),'一键部署')]")
        self.assert_text("部署", "//div[@class='tianhe-main-pc-modal lingzhu-sdk']//div[@class='tianhe-main-pc-modal-header']")
        self.assert_text("部署环境", "//label[contains(text(),'部署环境')]")
        self.assert_text("部署泳道", "//label[contains(text(),'部署泳道')]")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_submit_test(self):
        """应用管理：校验 开发阶段 - 点击 「提交测试」button弹窗"""
        self.test_deploy_release()
        self.click("//div[contains(text(),'开发阶段')]")
        self.click("//span[contains(text(),'提交测试')]")
        self.assert_text("确认提交测试吗？", "//div[@class='tianhe-main-pc-popover-message']")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_return_development_phase(self):
        """应用管理：校验 测试阶段- 点击 「退回开发阶段」button弹窗"""
        self.test_deploy_release()
        self.click("//div[contains(text(),'测试阶段')]")
        self.click("//span[contains(text(),'退回开发阶段')]")
        self.assert_text("确认退回开发阶段？执行后变更和资源都会回退到开发阶段", "//div[@class='tianhe-main-pc-popover-message']")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_one_click_deployment02(self):
        """应用管理：校验 测试阶段- 点击 「一键部署」button弹窗"""
        self.test_deploy_release()
        self.click("//div[contains(text(),'发布阶段')]")
        if self.is_element_visible("//span[contains(text(),'退回开发阶段')]"):
            self.click("//span[contains(text(),'退回开发阶段')]")
            self.click("//span[contains(text(),'确 定')]")
        else:
            self.sleep(2)

        self.click("//div[contains(text(),'开发阶段')]")
        self.sleep(2)
        self.click("//span[contains(text(),'提交测试')]")
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//div[contains(text(),'测试阶段')]")
        self.click("//span[contains(text(),'一键部署')]")
        self.assert_text("部署", "//div[@class='tianhe-main-pc-modal-header']")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_test_out_accurately(self):
        """应用管理：校验 测试阶段- 点击 「测试准出」button弹窗"""
        self.test_deploy_release()
        self.click("//div[contains(text(),'发布阶段')]")
        if self.is_element_visible("//span[contains(text(),'退回开发阶段')]"):
            self.click("//span[contains(text(),'退回开发阶段')]")
            self.click("//span[contains(text(),'确 定')]")
        else:
            self.sleep(2)

        self.click("//div[contains(text(),'开发阶段')]")
        self.click("//span[contains(text(),'提交测试')]")
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)
        self.click("//div[contains(text(),'测试阶段')]")
        self.click("//span[contains(text(),'测试准出')]")
        self.assert_text("确定进入测试准出流程?", "//div[@class='tianhe-main-pc-popover-message-title']")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def create_an_online_order(self):
        """应用管理：校验 发布阶段- 点击 「创建线上单」弹窗及流程"""
        # 点击创建线上单的流程-1.变更点确认
        self.click("//span[contains(text(),'创建上线单')]")
        self.sleep(2)
        self.click("//div[@class='tianhe-main-pc-select-selector']")
        self.click("//div[@class='tianhe-main-pc-select-item-option-content'][normalize-space()='2025-01-15 15:29:47--tc6F5zeswy--dailu03--release_tianhe-2435801020963']")
        # self.click("//div[@class='tianhe-main-pc-row tianhe-main-pc-form-item tianhe-main-pc-form-item-with-help']//label[1]//span[1]//span[1]")
        self.click("//span[contains(text(),'下一步')]")
        # 点击创建线上单的流程-2.发布顺序
        self.click("//div[@class='_container_e7j7b_1']//div[1]//button[1]")
        self.click("//div[@class='tianhe-main-pc-table-selection']//label[@class='tianhe-main-pc-checkbox-wrapper']")
        self.sleep(2)
        self.click("(//div[@class='tianhe-main-pc-tabs-tab'])[6]")
        self.click("//div[@class='tianhe-main-pc-table-selection']//label[@class='tianhe-main-pc-checkbox-wrapper']")
        self.click("(//div[@class='tianhe-main-pc-tabs-tab'])[7]")
        self.click("//div[@class='tianhe-main-pc-table-selection']//label[@class='tianhe-main-pc-checkbox-wrapper']")
        self.click("//div[@class='tianhe-main-pc-modal-content']//div[2]//button[2]")
        self.click("//span[contains(text(),'下一步')]")
        # 点击创建线上单的流程-3.灰度配置
        self.send_keys("//input[@id='grayConfig_grayConfigs_0_grayStageConfig_0_grayValue']", "100")
        # self.send_keys("//input[@id='grayConfig_grayConfigs_1_grayStageConfig_0_grayValue']", "100")
        # self.send_keys("//input[@id='grayConfig_grayConfigs_2_grayStageConfig_0_grayValue']", "100")
        self.click("//span[contains(text(),'下一步')]")
        # 点击创建线上单的流程-4.审批内容页面校验
        self.assert_text("变更名称", "//label[contains(text(),'变更名称')]")
        self.assert_text("二级审批人", "//label[contains(text(),'二级审批人')]")

    def tijiao_zhunchu(self):
        """开发阶段提交流程，直至重新校验创建线上单的流程"""
        self.click("//div[contains(text(),'开发阶段')]")
        self.click("//span[contains(text(),'提交测试')]")
        self.click("//span[contains(text(),'确 定')]")
        self.click("//div[contains(text(),'测试阶段')]")
        self.click("//span[contains(text(),'测试准出')]")
        self.click("//div[@class='tianhe-main-pc-space tianhe-main-pc-space-vertical']//div[1]//label[1]//span[1]//span[1]")
        self.click("//div[@class='tianhe-main-pc-col tianhe-main-pc-form-item-control']//div[2]//label[1]//span[1]//span[1]")
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(2)

    def test_create_online_order(self):
        """应用管理：校验 发布阶段- 点击 「创建线上单」button弹窗"""
        self.test_deploy_release()
        self.click("//div[contains(text(),'发布阶段')]")
        self.sleep(5)
        #校验「发布阶段」tab下是否存在「创建线上单」按钮
        if self.is_element_visible("//span[contains(text(),'创建上线单')]"):
            #如果存在「创建线上单」按钮，则直接跑创建的流程
            self.create_an_online_order()
        else:
            #如果不存在「创建线上单」按钮，则重新从开发阶段提交流程，直至重新校验创建线上单的流程
            self.tijiao_zhunchu()
            #创建线上单的固定流程
            self.create_an_online_order()

    def test_access_links(self):
        """校验：变更资源明细模块 -「页面」tab -「访问链接」button弹窗"""
        self.test_deploy_release()
        self.sleep(2)
        self.click("//span[contains(text(),'访问链接')]")
        self.assert_text("访问链接(平台如果有访问权限，请配置后再访问)", "//div[@class='tianhe-main-pc-modal-header']")

    def test_MDS_tab(self):
        """校验：变更资源明细模块-「MDS」tab 页面切换"""
        self.test_deploy_release()
        self.sleep(2)
        self.click('//*[@id="tianhe-ide"]/div/div/div/div/div[3]/div[2]/div[1]/div[1]/div/div[2]')
        self.assert_text("服务标识", "//th[contains(text(),'服务标识')]")
        self.assert_text("部署当前类别", "//span[contains(text(),'部署当前类别')]")
        self.assert_text("新 建", "//span[contains(text(),'新 建')]")

    def test_deploy_the_current_category(self):
        """校验：变更资源明细模块-「MDS」tab - 点击「部署当前类别」弹窗"""
        self.test_MDS_tab()
        self.click("//span[contains(text(),'部署当前类别')]")
        self.sleep(2)
        #判断上一步的「部署当前类别」按钮是否置灰，若不置灰则，弹窗元素存在
        if self.is_element_visible("//div[@class='tianhe-main-pc-modal-header']", "部署"):
            #弹窗元素存在，直接校验页面
            self.assert_text("资源类型", "//label[contains(text(),'资源类型')]")
            self.assert_text("部署环境", "//label[contains(text(),'部署环境')]")
            self.click("//span[contains(text(),'取 消')]")
            self.tijiao_zhunchu()
        else:
            #按钮置灰不可点击，导致 弹窗元素是否存在 校验失败，执行「退回开发阶段」操作，再校验其他场景
            self.click("//div[contains(text(),'部署发布')]")
            self.click("//div[contains(text(),'发布阶段')]")
            self.click("//span[contains(text(),'退回开发阶段')]")
            self.click("//span[contains(text(),'确 定')]")
            self.refresh()
            self.click('//*[@id="tianhe-ide"]/div/div/div/div/div[3]/div[2]/div[1]/div[1]/div/div[2]')

            #校验：变更资源明细模块-「MDS」tab - 点击「部署当前类别」弹窗
            self.click("//span[contains(text(),'部署当前类别')]")
            self.assert_text("部署", "//div[@class='tianhe-main-pc-modal-header']")
            self.assert_text("资源类型", "//label[contains(text(),'资源类型')]")
            self.assert_text("部署环境", "//label[contains(text(),'部署环境')]")
            self.click("//span[contains(text(),'取 消')]")

            #校验：变更资源明细模块-「MDS」tab - 点击「新建」button弹窗
            self.click("//span[contains(text(),'新 建')]")
            self.assert_text("新增MDS服务", "//div[@class='tianhe-main-pc-modal-header']")
            self.assert_text("MDS服务", "//label[contains(text(),'MDS服务')]")
            self.assert_text("使 用", "//form[@class='tianhe-main-pc-form tianhe-main-pc-form-horizontal']//span[contains(text(),'使 用')]")
            self.assert_text("使用空模版", "//span[contains(text(),'使用空模版')]")

            #校验：变更资源明细模块-「MDS」tab - 点击「新建」button -点击「使用空模板」弹窗
            self.click("//span[contains(text(),'使用空模版')]")
            self.assert_text("服务code", "//label[contains(text(),'服务code')]")
            self.assert_text("绑定子team", "(//label[contains(text(),'绑定子team')])[1]")
            self.assert_text("确 定", "//span[contains(text(),'确 定')]")
            self.click("//span[contains(text(),'取 消')]")

            #校验：变更资源明细模块-「页面」tab - 点击「撤销资源」button 弹窗
            self.click("(//div[@class='tianhe-main-pc-tabs-tab'])[1]")
            self.click("//span[contains(text(),'撤销资源')]")
            self.assert_text("确定要撤销吗？", "//span[@class='tianhe-main-pc-modal-confirm-title']")
            self.assert_text("确 定", "//span[contains(text(),'确 定')]")
            self.click("//span[contains(text(),'取 消')]")

            #校验：变更资源明细模块 -「页面」tab -「编辑」button弹窗
            self.click("//span[contains(text(),'编辑')]")
            self.assert_text("更新页面", "//div[@class='tianhe-main-pc-modal-header']")
            self.assert_text("页面名称", "//label[contains(text(),'页面名称')]")
            self.assert_text("更 新", "//span[contains(text(),'更 新')]")
            self.click("//span[contains(text(),'取 消')]")

            #校验：变更资源明细模块-「页面」tab -「复制」button弹窗
            self.click("//span[contains(text(),'复制')]")
            self.assert_text("复制页面——UI自动化测试勿删", "//div[@class='tianhe-main-pc-modal-header']")
            self.assert_text("页面名称", "//label[contains(text(),'页面名称')]")
            self.assert_text("创 建", "//span[contains(text(),'创 建')]")
            self.click("//span[contains(text(),'取 消')]")

            #校验：应用管理：应用概述页面-校验 变更资源明细模块 「页面」tab - 点击「开发」btn，跳转至调试页面 - 调试页面「导入」弹窗校验
            self.click("//button[@type='button']//span[contains(text(),'开发')]")
            self.sleep(5)
            # 判断是否有 提示弹窗
            if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/span/button[2]'):
                self.click('//*[@id="driver-popover-item"]/div[4]/span/button[2]')
            else:
                pass
            # 判断是否有 其他人正在编辑的弹窗
            if self.is_element_visible("//span[@class='tianhe-main-pc-modal-confirm-title']"):
                self.click("//button[@class='tianhe-main-pc-btn tianhe-main-pc-btn-primary']")
            else:
                pass
            self.sleep(3)
            self.assert_text("MUI", "//div[@class='ant-tabs ant-tabs-top assets-list-header-tab']//div[@class='ant-tabs-tab ant-tabs-tab-active']")
            self.assert_text("高级", "//div[@id='kael-skeleton-left-side-panel']//div[@class='ant-tabs-nav-wrap']//div[2]")
            self.click("//span[contains(text(),'导入')]")
            self.sleep(2)
            self.assert_text("物料导入", "//div[@class='ant-modal-header']")
            self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_application_list_search(self):
        """校验：「应用列表」模块下，搜索功能正常"""
        self.open_tianhe_homepage()
        self.send_keys("//input[@placeholder='请输入应用名称']", "UI自动化测试勿删")
        self.click("//button[@class='kwaishop-tianhe-tianhe_platform-pc-btn kwaishop-tianhe-tianhe_platform-pc-btn-icon-only kwaishop-tianhe-tianhe_platform-pc-input-search-button']")
        self.assert_text("UI自动化测试勿删", "//div[@class='kpro-common-tianhe-workbenches-application-card-appliction-card-text']")

    def test_application_list(self):
        """校验：「应用列表」模块下，功能区展示正常"""
        self.open_tianhe_homepage()
        #1.校验「我的应用」下拉框存在
        self.click("//span[@title='我的应用']")
        self.assert_text("全部应用", "//div[@title='全部应用']//div[1]")
        self.assert_text("我的应用", "//div[@title='我的应用']//div[1]")
        #2.校验业务域选择下拉框存在
        self.click("//span[@title='全部业务域'][contains(text(),'全部业务域')]")
        self.assert_text("测试业务域（面向平台开发测试）", "//span[@title='测试业务域（面向平台开发测试）']//span[1]")
        self.assert_text("风控", "//span[@title='风控']")
        #3.校验列表排序方式下拉框存在
        self.click("//div[@class='search-bar']//div[4]//div[1]//div[1]")
        self.assert_text("创建时间降序", "//div[@title='创建时间降序']//div[1]")
        self.assert_text("创建时间升序", "//div[@title='创建时间升序']//div[1]")

    @pytest.mark.p0
    def test_application_detail_page(self):
        """校验：搜索「UI自动化测试勿删」应用后，点击进入应用详情页"""
        self.test_application_list_search()
        self.click("//div[@class='kpro-common-tianhe-workbenches-application-card-appliction-card-info']")
        self.sleep(3)
        self.assert_text("数据统计", "//span[contains(text(),'数据统计')]")
        self.assert_text("变更记录", "//div[contains(text(),'变更记录')]")
        self.assert_text("资源列表", "//span[@class='EE_sA7F0BLZ13NLyG9Hw']")

    def test_project_search_build(self):
        """进入「UI自动化页面搭建测试勿动勿删」项目-展开对此模块页面搭建的自动化编写"""
        self.open_tianhe_homepage()
        self.send_keys("//input[@placeholder='请输入应用名称']", "UI自动化页面搭建")
        self.sleep(2)
        self.click("//div[@class='kpro-common-tianhe-workbenches-application-card-appliction-card-label'][contains(text(),'创建时间：')]")
        #进入开发页面
        self.sleep(2)
        self.click("//div[@title='页面搭建测试']")
        self.sleep(2)
        self.click("//div[contains(text(),'页面搭建')]")
        # 判断是否有 提示弹窗
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/span/button[2]'):
            self.click('//*[@id="driver-popover-item"]/div[4]/span/button[2]')
        else:
            pass
        self.assert_text("自定义", "(//div[@id='rc-tabs-0-tab-CHITU_MATERIAL'])[1]")

    def test_up_tab_switch(self):
        self.test_project_search_build()
        self.sleep(2)
        self.switch_to_frame(0)
        # self.click("//div[contains(text(),'发布管理')]")
        self.is_element_enabled("我是文字", "//span[contains(text(),'我是文字')]")

    @pytest.mark.skip #元素定位不到，暂时跳过
    def test_outline_tree(self):
        """点击「大纲树」tab，校验场景列表"""
        self.test_project_search_build()
        self.sleep(5)
        self.click('//*[@id="kael-skeleton-tab -designer-outline-tree"]/div[1]/div[1]')
        self.sleep(2)
        self.assert_text("大纲", "//div[@class='outline-tree-second-title'][contains(text(),'大纲')]")

    @pytest.mark.skip #元素定位不到，暂时跳过
    def test_popup_window(self):
        """点击「场景列表」-「弹窗」按钮，校验页面弹窗，正常加载"""
        self.test_outline_tree()
        self.click("//div[@class='outline-tree-scene-list-wrapper']//div[2]//div[2]//div[1]//div[1]")
        self.switch_to_frame(0)
        self.assert_text("弹窗", "//span[contains(text(),'弹窗')]")
        self.assert_text("名称", "//div[contains(text(),'名称')]")
        self.assert_text("确 定", "//span[contains(text(),'确 定')]")

    def test_whole_API_agreement_details(self):
        """服务搭建-全部-APItab-当前版本-协议详情功能校验"""
        self.test_service_construction()
        self.click("//div[contains(text(),'服务搭建')]")
        self.click("//div[contains(text(),'全部')]")
        self.click("//div[contains(text(),'UI自动化-API')]")
        self.assert_text("请求参数", "//div[contains(text(),'请求参数')]")

    def test_change_history(self):
        """服务搭建-全部-APItab-变更历史tab功能校验"""
        self.test_whole_API_agreement_details()
        self.click('//*[@id="sdk-wrapper"]/div/div/div/div[3]/div/div/div[2]/div[1]/div[1]/div/div[2]')
        self.assert_text("协议版本", "//th[contains(text(),'协议版本')]")

    def test_API_request_parameter_editing(self):
        """服务搭建-全部-APItab--请求参数编辑功能"""
        self.test_whole_API_agreement_details()
        self.sleep(2)
        self.click("(//a[contains(text(),'编辑')])[1]")
        self.assert_text("保存", "//a[contains(text(),'保存')]")
        self.click("//span[contains(text(),'取消')]")
        self.assert_text("编辑", "(//a[contains(text(),'编辑')])[1]")

    def test_service_implementation(self):
        """服务搭建-全部-APItab-当前版本-服务实现功能校验"""
        self.test_whole_API_agreement_details()
        self.sleep(3)
        self.click("(//div[@class='kwaishop-tianhe-sop-pc-tabs-tab'])[2]")
        self.assert_text("HTTP", "//div[normalize-space()='HTTP']")
        self.assert_text("流水线", "//div[@class='fI5rdAb_5NCA0WPRpz6Q'][contains(text(),'流水线')]")