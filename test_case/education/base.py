import time

from constant.account import get_account_info
from constant.domain import get_domain
import logging
from utils.env_help import get_env
from selenium import webdriver
from time import sleep

from seleniumbase import BaseCase


from constant.domain import get_domain_by_env
from page_objects.trade_order.trade_order_page import TradeOrderPage

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        """登入快手小店"""
        account_data = get_account_info(account)
        host = get_domain(domain)

    def setUp(self, **kwargs):
        super().setUp()
        self.maximize_window()


     # 学习基地平台测试勿动
    def login(self, host, accountNo, pwd):
        self.open(host)
        self.sleep(1)
        self.click("//div[contains(text(),'我是店主')]")
        self.click("//div[contains(text(),'手机号登录')]")
        self.type("//input[@placeholder='请输入手机号']", accountNo)
        self.click("//input[@placeholder='请输入密码']")
        self.type("//input[@placeholder='请输入密码']", pwd)
        self.click("//button[@type='button']")


   # 快手小店
    def login2(self, host, accountNo, pwd):
        self.open(host)
        self.sleep(1)
        # self.click("//div[contains(text(),'我是店主')]")
        # self.click("//div[contains(text(),'手机号登录')]")
        # self.sleep(1)
        self.click("(//span[contains(text(),'密码登录')])[1]")
        self.sleep(3)
        self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", accountNo)
        self.sleep(1)
        self.type("(//input[@placeholder='请输入密码'])[1]", pwd)
        self.sleep(1)
        self.click("(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
        # 学习基地平台测试勿动

    def login03(self, host, accountNo, pwd):
        self.open(host)
        self.sleep(1)
        self.click("(//div[contains(text(),'手机号登录')])[1]")
        self.sleep(1)
        self.type("(//input[@placeholder='请输入手机号'])[1]", accountNo)
        self.sleep(1)
        self.type("(//input[@placeholder='请输入密码'])[1]", pwd)
        self.sleep(1)
        self.click("(//button[@type='button'])[1]")




