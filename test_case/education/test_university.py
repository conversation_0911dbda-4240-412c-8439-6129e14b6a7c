import pytest
import time
from unittest import skip, skipIf

from ddt import ddt
from test_case.education.base import BaseTestCase
# from test_case.growth.kuaishouxiaodian.base import BaseTestCase


@ddt
@pytest.mark.p1
class UniversityHome(BaseTestCase):

    def test_kwaishop_homepage(self):
        """打开学习基地首页"""
        sso_host = "https://login-prt.test.gifshow.com/?biz=university&redirect_url=https%3A%2F%2Fprt-kwaishop-university-c-test.test.gifshow.com%2Fkwaishop%2Findex"
        self.login(sso_host, "15194641697", "hkk.123456")
        self.get("https://prt-kwaishop-university-c-test.test.gifshow.com/kwaishop/index")
        self.sleep(10)

        # 首页判断元素是否存在
        self.assert_text('首页', "//a[contains(text(),'首页')]")
        self.assert_text('课程中心', "//a[contains(text(),'课程中心')]")
        self.assert_text('功能中心', "//a[contains(text(),'功能中心')]")
        self.assert_text('商友圈', "//a[contains(text(),'商友圈')]")
        self.assert_text('生态伙伴 🔽', "//a[contains(text(),'生态伙伴 🔽')]")
        self.assert_text('0元开店🔥', "//a[contains(text(),'0元开店🔥')]")
        self.sleep(5)
        # 点击直播云课程查看更多
        self.click("//div[@class='index-module-header__right--text']")
        self.sleep(5)
        #self.assert_text('女娲数字人专场来袭', "//article[contains(text(),'女娲数字人专场来袭')]")
        self.sleep(2)
        # 点击回放
        self.click("(//button[@type='button'][contains(text(),'直播回看')])[1]")
        self.sleep(5)

        # 直播详情页
        self.assert_text('快手商家成长小课堂', "//div[@class='Aahl7Khh93scQ2PcHTrm'][contains(text(),'快手商家成长小课堂')]")
        self.assert_text('直播材料', "//div[contains(text(),'直播材料')]")
        self.assert_text('女娲数字人专场来袭', "//div[contains(text(),'女娲数字人专场来袭')]")
        self.assert_text('直播讲师', "//div[contains(text(),'直播讲师')]")

    def test_course(self):
        """打开学习基地首页课程"""
        sso_host = "https://login-prt.test.gifshow.com/?biz=university&redirect_url=https%3A%2F%2Fprt-kwaishop-university-c-test.test.gifshow.com%2Fkwaishop%2Findex"
        self.login(sso_host, "15194641697", "hkk.123456")
        self.get("https://prt-kwaishop-university-c-test.test.gifshow.com/kwaishop/index")
        self.sleep(10)


        # 点击课程tab
        self.click("//a[contains(text(),'课程中心')]")
        self.sleep(5)

        self.click("//li[contains(text(),'达人必修')]")
        self.sleep(5)

        self.assert_text('开单第一步', "//div[contains(text(),'开单第一步')]")

        self.click("(//span[contains(text(),'去学习')])[1]")
        self.sleep(8)

        self.assert_text('课程介绍', "//div[@class='OZKhQIp35iUMrjRNwHqA']")
        self.assert_text('互动评价', "//div[@id='feedBackWrapTop']")
        self.assert_text('更多推荐', "//div[contains(text(),'更多推荐')]")
        self.sleep(5)

    def test_newKnowledge(self):
        """打开学习基地首页功能中心"""
        sso_host = "https://login-prt.test.gifshow.com/?biz=university&redirect_url=https%3A%2F%2Fprt-kwaishop-university-c-test.test.gifshow.com%2Fkwaishop%2Findex"
        self.login(sso_host, "15194641697", "hkk.123456")
        self.get("https://prt-kwaishop-university-c-test.test.gifshow.com/kwaishop/index")
        self.sleep(10)

        # 点击功能tab
        self.click("//a[contains(text(),'功能中心')]")
        self.sleep(5)

       # 点击文章
        self.click("//div[contains(text(),'【快分销入驻】教程')]")
        self.assert_text('文章大纲', "//div[@class='mt1tr1BVj_uO0xA2v25p']")
        self.assert_text('新人必读', "//span[@class='kwaishop-seller-edu-common-resource-detail-page-pc-tree-title'][contains(text(),'1、新人必读')]")
        self.sleep(5)

    def test_newKnowledge(self):
        """打开学习基地点击商友圈tab"""
        sso_host = "https://login-prt.test.gifshow.com/?biz=university&redirect_url=https%3A%2F%2Fprt-kwaishop-university-c-test.test.gifshow.com%2Fkwaishop%2Findex"
        self.login(sso_host, "15194641697", "hkk.123456")
        self.get("https://prt-kwaishop-university-c-test.test.gifshow.com/kwaishop/index")
        self.sleep(10)

        self.click("//a[contains(text(),'商友圈')]")
        self.sleep(5)




     #   助手
    @skip("弹窗导致报错")
    def test_kwaixiaodian_home(self):
        """ 进入快手小店售后 """
        sso_host = "https://eshop-s.prt.kwaixiaodian.com/zone/refund/refund-workbench/index"
        self.login2(sso_host, "15194641697", "hkk.123456")
        time.sleep(6)

        # self.click("//span[@class='bASqM1kzJlIX6ZNynVIc'][contains(text(),'售后')]")
        # time.sleep(3)


        # 点击经营助手挂件
        # self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # time.sleep(2)
        # if self.is_element_visible("(//button[contains(text(),'关闭')])[1]"):
        #     self.click("(//button[contains(text(),'关闭')])[1]")
        # else:
        #     pass
        # # 判断弹窗
        # if self.is_element_visible("(//button[contains(text(),'关闭')])[1]"):
        #     self.click("(//button[contains(text(),'关闭')])[1]")
        # else:
        #     pass
        # time.sleep(6)

        self.click("(//div[@class='moreButton___DIUQa'])[1]")
        time.sleep(3)

        self.assert_text('自助工具', "//span[contains(text(),'自助工具')]")
        self.assert_text('操作指南', "//span[contains(text(),'操作指南')]")
        self.assert_text('视频课程', "//span[contains(text(),'视频课程')]")

       # 点击自助工具
        self.click("(//div[@class='selfServiceItem___ZBBxZ'])[2]")
        time.sleep(2)

    def test_kwaixiaodian_home02(self):
        """ 进入快手小店首页 """
        sso_host = "https://eshop-s.prt.kwaixiaodian.com/zone/home"
        self.login2(sso_host, "15194641697", "hkk.123456")
        time.sleep(6)

        self.click("//span[contains(text(),'我要反馈')]")
        time.sleep(3)
        self.assert_text('问题描述', "//label[contains(text(),'问题描述')]")
        self.assert_text('问题类型', "//label[contains(text(),'问题类型')]")
        self.assert_text('反馈模块', "//label[contains(text(),'反馈模块')]")
        time.sleep(1)
        self.click("//span[contains(text(),'历史反馈')]")

    @skip("弹窗导致报错")
    def test_merchant_assistant(self):
        """ 助手 """
        sso_host = "https://eshop-s.prt.kwaixiaodian.com/zone/order/list"
        self.login2(sso_host, "15194641697", "hkk.123456")
        time.sleep(8)

        # self.click("//span[@class='bASqM1kzJlIX6ZNynVIc'][contains(text(),'订单')]")
        # time.sleep(5)
        # self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # time.sleep(2)

        # 点击经营助手挂件

        self.click("(//div[@class='moreButton___DIUQa'])[1]")
        time.sleep(2)

        # 点击操作指南
        self.click("//span[contains(text(),'【订单管理】使用教程')]")
        time.sleep(2)

        self.click("//span[contains(text(),'返回')]")
        # self.go_back()
        time.sleep(2)
        # 点击猜你想问
        self.click("//span[contains(text(),'怎么搜索/查询订单？')]")
        time.sleep(2)
        self.click("//span[contains(text(),'返回')]")
        # self.go_back()
        time.sleep(2)
    # 点击视频课程

        self.click("//span[contains(text(),'发货前必做的几件事')]")

    @skip("弹窗导致报错")
    def test_merchant_assistant02(self):
        """ 助手小二 """
        sso_host = "https://s.kwaixiaodian.com/zone/order/list"
        self.login2(sso_host, "15224963039", "ks1234567")
        time.sleep(8)

        # self.click("//span[@class='bASqM1kzJlIX6ZNynVIc'][contains(text(),'订单')]")
        # time.sleep(5)


        # 点击经营助手挂件

        self.click("(//span[contains(text(),'经营助手')])[1]")
        time.sleep(2)
        

        # 点击联系小二
        self.click("//span[contains(text(),'联系小二')]")
        self.assert_text('请使用微信扫描二维码，联系您的专属小二', "//div[@class='qrCodeDesc____4VF8']")
        time.sleep(2)
        # 点击联系客服
        self.click("(//div[@class='contactService___d0mZr'])[1]")
        time.sleep(2)



















