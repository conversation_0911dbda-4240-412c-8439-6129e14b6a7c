import pytest
import time
from unittest import skip, skipIf
from ddt import ddt
from test_case.education.base import BaseTestCase



@ddt
@pytest.mark.p1
class Service_Provider(BaseTestCase):

    def test_kwaixiaodian_homepage(self):
        """打开服务商首页"""
        sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
        self.login03(sso_host, "15194641697", "hkk.123456")
        self.sleep(6)

        # 首页点击tab
        self.click("(//span[contains(text(),'更多功能')])[1]")
        self.sleep(3)
        self.click("(//span[contains(text(),'代运营管理')])[1]")
        self.sleep(3)

        # 点击成员运营客户管理页
        self.click("(//span[contains(text(),'成员运营')])[1]")
        self.sleep(3)

        self.click("(//span[contains(text(),'客户管理')])[1]")
        self.sleep(3)

        self.assert_text('商家等级', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/span[1]")
        self.assert_text('签约日期', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[1]/div[1]/span[1]")
        self.assert_text('自动续约', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/span[1]")
        self.assert_text('首销日期', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/table[1]/tbody[1]/tr[4]/td[1]/div[1]/span[1]")
        self.assert_text('归属城市', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/span[1]")
        self.assert_text('主营行业', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[2]/td[1]/div[1]/span[1]")
        self.assert_text('运营标签', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/span[1]")
        self.assert_text('归属品牌', "//body[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]/div[1]/div[1]/div[1]/div[1]/label[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[4]/td[1]/div[1]/span[1]")
        # self.assert_text('是否新商', "//label[@for='select-checkbox-1722091396']//div[@class='PS0RSSvdRqm4bysu86x3']//div[@class='treNVhYAE3iWFj0eTtST']//div[@class='LuN_aFRHi3_FrxLdCLaN']//div//div[@class='NISpCGcwgpe2sNHhsjwq'][contains(text(),'是否新商')]")
        # self.assert_text('是否符合政策', "//label[@for='select-checkbox-1275829847']//div[@class='PS0RSSvdRqm4bysu86x3']//div[@class='treNVhYAE3iWFj0eTtST']//div[@class='LuN_aFRHi3_FrxLdCLaN']//div//div[@class='NISpCGcwgpe2sNHhsjwq'][contains(text(),'是否符合政策')]")
        # self.assert_text("首销近30天GMV", "//label[@for='select-checkbox-1275829847']//div[@class='PS0RSSvdRqm4bysu86x3']//div[@class='treNVhYAE3iWFj0eTtST']//div[@class='LuN_aFRHi3_FrxLdCLaN']//div//div[@class='NISpCGcwgpe2sNHhsjwq'][contains(text(),'首销近30天GMV')]")
        # self.assert_text("首销近90天GMV", "//label[@for='select-checkbox-1275829847']//div[@class='PS0RSSvdRqm4bysu86x3']//div[@class='treNVhYAE3iWFj0eTtST']//div[@class='LuN_aFRHi3_FrxLdCLaN']//div//div[@class='NISpCGcwgpe2sNHhsjwq'][contains(text(),'首销近90天GMV')]")
        self.sleep(5)
        # 点击查看更多
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)


    def test_livehistory(self):
        """打开直播记录页"""
        sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/live-history"
        self.login03(sso_host, "15194641697", "hkk.123456")
        self.sleep(6)
        # 关闭弹窗
        if self.is_element_visible("(//button[@class='kp-main-btn'])[1]"):
            self.click("(//button[@class='kp-main-btn'])[1]")
        else:
            pass
        self.sleep(5)
        self.click("//div[@class='ant-tabs-tab']")
        self.sleep(3)
        self.click("(//div[@class='ant-tabs-tab'])[1]")
        self.sleep(3)

        self.assert_text('商家信息', "//th[contains(text(),'商家信息')]")
        self.assert_text('开播场次', "//th[contains(text(),'开播场次')]")
        self.assert_text('累计直播时长', "//th[contains(text(),'累计直播时长')]")
        self.assert_text('累计销售额', "//th[contains(text(),'累计销售额')]")

        self.click("(//a[contains(text(),'查看场次')])[1]")
        self.sleep(3)

    @skip("没定位到")
    def test_eShopWorkBenchClue_ClueAllot(self):
        """线索页"""
        sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone/provider/eShopWorkBenchClue/ClueAllot"
        self.login03(sso_host, "15224963039", "ks1234567")
        self.sleep(6)

        # 点击查看更多
        self.click("(//button[@type='button'])[2]")
        self.sleep(3)
        self.assert_text('经营信息', "//div[contains(text(),'经营信息')]")
        self.assert_text('联系人信息', "//div[contains(text(),'联系人信息')]")

    def test_providers(self):
        """选择服务商页面"""
        sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/providers"
        self.login03(sso_host, "15194641697", "hkk.123456")
        self.sleep(6)

        self.click("(//div[@title='快手电商服务商1'])[1]")
        self.sleep(3)

        # 点击直播监控菜单
        self.click("(//span[contains(text(),'直播操盘')])[1]")
        self.sleep(3)
        self.click("(//span[contains(text(),'直播监控(电商)')])[1]")
        self.sleep(3)

        # 点击直播大屏

        self.click("(//span[contains(text(),'电商直播')])[3]")
        self.click("(//span[contains(text(),'进入直播大屏')])[1]")
        self.sleep(3)


    def test_providers02(self):
        """选择服务商页面"""
        sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/providers"
        self.login03(sso_host, "15194641697", "hkk.123456")
        self.sleep(6)

        self.click("(//div[@title='快手电商服务商1'])[1]")
        self.sleep(3)

        # 点击直播监控菜单
        self.click("(//span[contains(text(),'直播操盘')])[1]")
        self.sleep(3)
        self.click("(//span[contains(text(),'直播监控(电商)')])[1]")
        self.sleep(3)

        # 点击直播间

        self.click("(//span[contains(text(),'电商直播')])[3]")
        self.click("(//span[contains(text(),'进入直播间')])[1]")
        self.sleep(3)

    # def test_homepage01(self):
    #      """"服务商首页核心数据"""
    #      sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #      self.login03(sso_host, "15194641697", "hkk.123456")
    #      self.sleep(5)

    #      # 核心数据

    #      self.assert_text('支付GMV(元)', "//div[contains(text(),'支付GMV(元)')]")
    #      self.assert_text('绑定成员数', "//div[contains(text(),'绑定成员数')]")
    #      self.assert_text('动销成员数', "//div[contains(text(),'动销成员数')]")
    #      self.assert_text('实际开播率', "//div[contains(text(),'实际开播率')]")
    #      self.assert_text('开播成员使用计划占比', "//div[contains(text(),'开播成员使用计划占比')]")
    #      self.assert_text('开播场次使用计划占比', "//div[contains(text(),'开播场次使用计划占比')]")
    #      self.sleep(5)
    #     # 点击切换日期

    #      self.click("(//span[contains(text(),'近7天')])[1]")
    #      self.sleep(3)
    #      self.click("(//span[contains(text(),'近30天')])[1]")
    #      self.sleep(3)
    #      self.click("(//span[contains(text(),'本月')])[1]")
    #      self.sleep(3)

    #     # 点击支付gmv
    #      self.click("(//div[@class='u_fZrLogj3bS3cfh_7MU'])[1]")
    #      self.sleep(3)



    # def test_homepage02(self):
    #     """服务商首页核心数据"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #     self.click("(//div[@class='u_fZrLogj3bS3cfh_7MU'])[2]")
    #     self.sleep(5)

    # def test_homepage03(self):
    #      """服务商首页核心数据"""
    #      sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #      self.login03(sso_host, "15194641697", "hkk.123456")
    #      self.sleep(5)

    #     # 点击动销成员数
    #      self.click("(//div[@class='u_fZrLogj3bS3cfh_7MU'])[3]")
    #      self.sleep(5)

    # def test_homepage04(self):
    #     """服务商首页核心数据"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #     # 点击实际开播率
    #     self.click("(//div[@class='u_fZrLogj3bS3cfh_7MU'])[4]")
    #     self.sleep(3)


    # def test_homepage06(self):
    #     """服务商首页核心数据"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #     # 开播场次使用计划占比
    #     self.click("(//div[@class='u_fZrLogj3bS3cfh_7MU'])[6]")
    #     self.sleep(3)


    # def test_homepage07(self):
    #      """服务商首页业务数据"""
    #      sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #      self.login03(sso_host, "15194641697", "hkk.123456")
    #      self.sleep(5)

    #      # 业务数据
    #      self.assert_text('新商数', "//div[@class='j_GRw9i7XWjklOMqZ81T']//div[1]//div[1]")
    #      self.assert_text('新商GMV（元）', "//div[@class='j_GRw9i7XWjklOMqZ81T']//div[2]//div[1]")
    #      self.assert_text('符合政策新商数', "(//div[@class='I5dJKOQNLYxuo0AWv9Kd'])[3]")
    #      self.assert_text('符合政策新商GMV（元）', "(//div[@class='I5dJKOQNLYxuo0AWv9Kd'])[4]")
    #      self.sleep(2)

    #      # 点击新商数去查看

    #      self.click("(//div[@class='nhRFVnf2Lu2Vs7vh21Fa'][contains(text(),'去查看')])[1]")
    #      self.sleep(3)

    # def test_homepage08(self):
    #     """服务商首页业务数据"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #     # 点击新商gmv去查看
    #     self.click("(//div[@class='nhRFVnf2Lu2Vs7vh21Fa'][contains(text(),'去查看')])[2]")
    #     self.sleep(3)


    # def test_homepage09(self):
    #     """服务商首页业务数据"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #     # 点击符合政策新商数去查看
    #     self.click("(//div[@class='nhRFVnf2Lu2Vs7vh21Fa'][contains(text(),'去查看')])[3]")
    #     self.sleep(3)


    # def test_homepage10(self):
    #     """服务商首页业务数据"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #    # 点击符合政策新商gmv去查看

    #     self.click("(//div[@class='nhRFVnf2Lu2Vs7vh21Fa'][contains(text(),'去查看')])[4]")
    #     self.sleep(3)

    # def test_homepage11(self):
    #     """服务商首页今日代办"""
    #     sso_host = "https://eshop-kp.prt.kwaixiaodian.com/zone-service/home"
    #     self.login03(sso_host, "15194641697", "hkk.123456")
    #     self.sleep(5)

    #     # 今日待办
    #     self.assert_text('今日待操盘场次数', "//div[contains(text(),'今日待操盘场次数')]")
    #     self.assert_text('待续约成员数', "//div[contains(text(),'待续约成员数')]")
    #     self.assert_text('待解约成员数', "//div[contains(text(),'待解约成员数')]")
    #     self.assert_text('待首次诊断成员数', "//div[contains(text(),'待首次诊断成员数')]")
    #     self.assert_text('今日新增处罚成员数', "//div[contains(text(),'今日新增处罚成员数')]")
    #     self.assert_text('即将超时不可申诉处罚数', "//div[contains(text(),'即将超时不可申诉处罚数')]")
    #     self.sleep(2)

    # #   点击操盘管理
    #     self.click("(//div[contains(text(),'操盘管理')])[1]")
    #     self.sleep(3)































