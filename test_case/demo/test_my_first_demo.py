from unittest import skip, skipIf

from ddt import ddt, data, unpack
from seleniumbase import BaseCase

from constant.domain import get_domain
from utils.csv_help import get_csv_data
from utils.env_help import get_env

host = get_domain("BAIDU_DOMAIN")


@skip
@ddt
class MyTestClass(BaseCase):

    @skip
    def test_swag_labs(self):
        self.open("https://www.saucedemo.com")
        self.type("#user-name", "standard_user")
        self.type("#password", "secret_sauce\n")
        self.assert_element("#inventory_container")
        self.assert_text("PRODUCTS", "span.title")
        self.click('button[name*="backpack"]')
        self.click("#shopping_cart_container a")
        self.assert_text("YOUR CART", "span.title")
        self.assert_text("Backpack", "div.cart_item")
        self.click("button#checkout")
        self.type("#first-name", "SeleniumBase")
        self.type("#last-name", "Automation")
        self.type("#postal-code", "77123")
        self.click("input#continue")
        self.assert_text("CHECKOUT: OVERVIEW")
        self.assert_text("Backpack", "div.cart_item")
        self.click("button#finish")
        self.assert_exact_text("THANK YOU FOR YOUR ORDER", "h2")
        self.assert_element('img[alt="Pony Express"]')
        self.js_click("a#logout_sidebar_link")

    @skip
    @data("快手", "商家", "生意通")
    def test_baidu(self, value):
        self.open(host)
        self.type("#kw", value)
        self.click("input#su")
        self.assert_title(str(value) + "_百度搜索")

    @skip
    @data(("数据", "驱动"), ("商家", "生意通"))
    @unpack
    def test_baidu2(self, value1, value2):
        self.open(host)
        self.type("#kw", value1)
        self.click("input#su")
        self.assert_title(str(value1) + "_百度搜索")
        self.open(host)
        self.type("#kw", value2)
        self.click("input#su")
        self.assert_title(str(value2) + "_百度搜索")

    @data({'name': 'tom', 'age': 20}, {'name': 'kite', 'age': 30})
    @unpack
    @skip("直接跳过")
    def test_baidu3(self, name, age):
        self.open(host)
        self.type("#kw", name)
        self.click("input#su")
        self.assert_title(str(name) + "_百度搜索")
        self.open(host)
        self.type("#kw", age)
        self.click("input#su")
        self.assert_title(str(age) + "_百度搜索")

    @data(*get_csv_data('test_data/demo.csv'))
    @unpack
    @skipIf(get_env() == "staging", "按条件跳过")
    def test_baidu4(self, name, age):
        self.open(host)
        self.type("#kw", name)
        self.click("input#su")
        self.assert_title(str(name) + "_百度搜索")
        self.open(host)
        self.type("#kw", age)
        self.click("input#su")
        self.assert_title(str(age) + "_百度搜索")
