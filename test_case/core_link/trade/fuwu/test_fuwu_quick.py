from unittest import skip

from ddt import ddt
from test_case.core_link.trade.refund.base import BaseTestCase
from time import sleep


# 快手小店-快速打单
@ddt
class TestQuick(BaseTestCase):
    def setup_class(self):
        self.true = True

    # 跳转到售后助手
    def to_quick(self):
        self.login("MERCHANT_DOMAIN", "refund_account")
        self.assert_title("快手小店")
        sleep(2)
        #self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.assert_text("快速打单", '//*[@id="menu_item_67"]/span/span')
        self.click('//*[@id="menu_item_67"]/span/span')

    '''
    快速打单->进入服务市场
    快速打单->应用推荐
    '''

    # # 快速打单->进入服务市场
    # def test_enter_fuwu(self):
    #     self.to_quick()
    #     self.assert_text('进入服务市场', '//*[@id="root"]/section/main/div/div[1]/div[3]/button/span')
    #     self.click('//*[@id="root"]/section/main/div/div[1]/div[3]/button/span')
    #     self.assert_text('快手服务市场', '//*[@id="app"]/section/header/div[1]/div/ul[1]/li[1]/h1')
    #
    # # 快速打单->应用推荐
    # def test_yingyongtuijian(self):
    #     self.to_quick()
    #     self.click('//*[@id="root"]/section/main/div/div[2]/div[2]/div[1]')
    #     self.assert_text('快手服务市场', '//*[@id="app"]/section/header/div[1]/div/ul[1]/li[1]/h1')
