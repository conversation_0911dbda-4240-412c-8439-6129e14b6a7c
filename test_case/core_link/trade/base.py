# -*- coding: utf-8 -*-
# @Time : 2022/7/29 8:35 PM
# <AUTHOR> xietingting
# @File : base.py.py
# @Project : kwaishopuiautotest
from behave.formatter import null
from seleniumbase import BaseCase
from constant.account import get_account_info
from constant.domain import get_domain
from time import sleep

class BaseTestCase(BaseCase):
    def login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        # self.assert_text("扫码登录", "div.choseTab--12n2b > div:nth-child(1)")  # div标签中的第一个元素
        # self.assert_text("手机号登录", "div.choseTab--12n2b > div:nth-child(2)")
        #self.click("div.choseTab--12n2b > div:nth-child(2)")
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')

        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")

    def open_orderList(self):
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.assert_element('//*[@id="node_menu_for_intersectionObserver_1"]')
        self.assert_text('订单查询', '//*[@id="menu_item_1"]')
        # 点击订单查询打开列表
        self.click('#menu_item_1 > span > span')
        sleep(3)
        # 关闭弹窗
        if self.is_element_visible('body > div:nth-child(18) > div > div.ant-modal-wrap > div > div.ant-modal-content'):
            self.click(
                'body > div:nth-child(18) > div > div.ant-modal-wrap > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button:nth-child(1)')
        if self.is_element_visible('/html/body/div[5]/div/div[2]/div/div[2]/div/div'):
            self.click('/html/body/div[5]/div/div[2]/div/div[2]/div/div/div[2]/button[1]')
        self.assert_equal(self.get_current_url().endswith('/zone/order/list'), True)
    def open_orderDetail(self):
        order_id = self.get_text(
            '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item-title > div.left > a',
            by="css selector", timeout=None)
        sleep(2)
        self.click(
            '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item-title > div.left > a')
        sleep(5)
        self.switch_to_window(1)
        # 弹窗
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        order_detail_url = '/zone/order/detail?id=' + order_id
        # 订单详情页面+元素检查
        self.assert_equal(self.get_current_url().endswith(order_detail_url), True)
        self.assert_element('#root > section > section > main > div:nth-child(3) > div.sc-dJjYzT.ioeRJz')






