from unittest import skip

from ddt import ddt
from .base import BaseTestCase
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase
from time import sleep


# 快手小店-售后工作台
@ddt
class TestRefundWorkbench(BaseTestCase):
    def setup_class(self):
        self.order_id = ****************
        self.refund_id = ****************
        self.item_name = '「测试商品」带运费险的休闲裤'
        self.item_id = *************

    # 跳转到售后工作台
    def to_workbech(self):
        self.login("MERCHANT_DOMAIN", "refund_account")
        self.assert_title("快手小店")
        sleep(2)
        #self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        self.assert_text("售后工作台", '/html/body/div[2]/div/div/div/div[2]/div[1]/ul/div/div/li[5]/ul/li[1]/span/span')
        self.click('/html/body/div[2]/div/div/div/div[2]/div[1]/ul/div/div/li[5]/ul/li[1]/span/span')

    # 列表页展示订单
    def find_order(self):
        self.type("input[placeholder='请填写完整订单编号']", self.order_id)
        self.click('//*[@id="control-hooks"]/div/div[9]/button[1]')
        sleep(1)

    '''
#   售后工作台->退款详情
#   售后工作台->订单详情
#   售后工作台->商品详情
#   售后工作台->物流详情
#   售后工作台->退款详情->订单详情
#   售后工作台->退款详情->运费险详情
#   售后工作台->退款详情->店铺客服im
#   售后工作台->退款详情->物流详情
    '''
    # # 售后工作台->售后详情
    # def test_refund_detail(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 售后详情
    #     self.assert_text('售后详情', '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[6]/div/div/a/button/span')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[6]/div/div/a/button/span')
    #     sleep(2)
    #     self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
    #     page_source = self.driver.page_source
    #     assert '退货物流' in page_source
    #
    #
    # # 售后工作台->订单详情
    # def test_order_detail(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 订单编号——订单详情
    #     self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[3]/a')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[3]/a')
    #     self.assert_text(self.order_id, '//*[@id="root"]/section/section/main/div[4]/div[3]/div/table/tbody/tr[1]/td/div/span[2]')
    #
    # # 售后工作台->商品详情
    # def test_item_detail(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 商品名称——商品详情
    #     self.assert_text(self.item_name, '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[1]/div/div[2]/a/div')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[1]/div/div[2]/a/div')
    #     self.assert_text(self.item_id, '//*[@id="root"]/section/main/div[3]/div/div[2]/table/tbody/tr[1]/td/span[2]')
    #
    # # 售后工作台->物流详情
    # def test_express_detail(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 售后工作台->物流详情
    #     self.assert_text('已发货', '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[4]/span[2]')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[4]/span[2]')
    #     sleep(1)
    #     page_source = self.driver.page_source
    #     assert '物流详情' in page_source
    #     self.click('/html/body/div[4]/div/div[2]/div/div/div[2]/div/div/div/div[1]/div[2]/span/a')
    #
    # # 售后工作台->售后详情->订单详情
    # def test_refund_order_detail(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 售后详情
    #     self.assert_text(self.refund_id, '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[2]/a')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[6]/div/div/a/button/span')
    #     sleep(2)
    #     self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
    #     # 订单详情
    #     self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
    #     self.assert_text(self.order_id, '//*[@id="root"]/section/section/main/div[4]/div[3]/div/table/tbody/tr[1]/td/div/span[2]')
    #
    # # 售后工作台->退款详情->客服IM
    # def test_refund_detail_im(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 售后详情
    #     self.assert_text(self.refund_id, '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[2]/a')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[6]/div/div/a/button/span')
    #     sleep(2)
    #     self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
    #     # 客服IM
    #     self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/div[2]/a/img')
    #
    # # 售后工作台->退款详情->运费险详情
    # def test_refund_detail_insurance(self):
    #     self.to_workbech()
    #     self.find_order()
    #     # 售后详情
    #     self.assert_text(self.refund_id, '//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[1]/div[2]/a')
    #     self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div[4]/div[1]/div/ul/div/div[2]/div[6]/div/div/a/button/span')
    #     sleep(2)
    #     self.assert_text(self.order_id, '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span[1]/a')
    #     # 运费险详情
    #     #self.assert_text(' 查看详情 ', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[9]/div[2]/span[2]/a')
    #     self.click('//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[2]/div[2]/div[10]/div[2]/span[2]/a')
    #     sleep(3)
    #     self.assert_text('服务详情', '//*[@id="root"]/div/div/div/div[2]/div[1]/div[1]/span')
