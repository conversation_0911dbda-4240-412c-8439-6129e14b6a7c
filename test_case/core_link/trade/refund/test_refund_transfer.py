from unittest import skip

from ddt import ddt
from .base import BaseTestCase
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase
from time import sleep


# 快手小店-小额打款
@ddt
class TestRefundTransfer(BaseTestCase):
    def setup_class(self):
        self.order_id = ****************
        self.refund_id = ****************
        self.item_name = '「测试商品」带运费险的休闲裤'
        self.item_id = *************

    # 跳转到小额打款
    def to_transfer(self):
        self.login("MERCHANT_DOMAIN", "refund_account")
        self.assert_title("快手小店")
        sleep(2)
        #self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.assert_text("小额打款", '//*[@id="menu_item_16"]/span/span')
        self.click('//*[@id="menu_item_16"]/span/span')

    '''
    小额打款->发起打款
    小额打款->打款记录
    小额打款->待处理打款申请
    小额打款->打款配额
    
    '''
    # # 小额打款
    # def test_small_transfer(self):
    #     self.to_transfer()
    #     page_source = self.driver.page_source
    #     if '立即使用' in page_source:
    #         self.click('//*[@id="root"]/section/section/main/div/div/div/div/button/span')
    #     sleep(2)
    #     # 小额打款->发起打款
    # 
    #     self.assert_element('发起打款', '//*[@id="rc-tabs-0-tab-1"]')
    #     self.click('//*[@id="rc-tabs-0-tab-1"]')
    #     # 小额打款->打款记录
    #     self.assert_element('打款记录', '//*[@id="rc-tabs-0-tab-2"]')
    #     self.click('//*[@id="rc-tabs-0-tab-2"]')
    #     # 小额打款->待处理打款申请
    #     self.assert_element('待处理打款申请', '//*[@id="rc-tabs-0-tab-3"]')
    #     self.click('//*[@id="rc-tabs-0-tab-3"]')
    #     # 小额打款->打款配额
    #     self.assert_element('打款配额', '//*[@id="rc-tabs-0-tab-4"]')
    #     self.click('//*[@id="rc-tabs-0-tab-4"]')

