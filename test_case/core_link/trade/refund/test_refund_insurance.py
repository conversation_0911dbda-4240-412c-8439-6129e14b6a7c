from unittest import skip

from ddt import ddt
from .base import BaseTestCase
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase
from time import sleep


# 快手小店-退货补运费
@ddt
class TestRefundInsurance(BaseTestCase):
    def setup_class(self):
        self.order_id = ****************

    # 跳转到退货补运费
    def to_Insurance(self):
        self.login("MERCHANT_DOMAIN", "refund_account")
        self.assert_title("快手小店")
        sleep(2)
        #self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.assert_text("退货补运费", '//*[@id="menu_item_14"]/span/span')
        self.click('//*[@id="menu_item_14"]/span/span')

    # 列表页展示订单
    def find_order(self):
        self.type('input[placeholder="请输入完整订单编号"]', self.order_id)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div[2]/form/div/div[3]/div/div/div/div/button[2]')
        sleep(1)

    '''
    退货补运费->运费险详情
    退货补运费->服务说明
    '''

    # # 退货补运费->服务说明
    # def test_service_desc(self):
    #     self.to_Insurance()
    #     # 退货补运费->服务说明
    #     fwsm = '//*[@id="root"]/div/div/div/div[1]/div/div[2]/div/div/div[2]/button/span'
    #     self.assert_text('服务说明', fwsm)
    #     self.click(fwsm)
    #     self.assert_text('服务说明', '//*[@id="root"]/div/div/div/div[2]/div/div[2]/h2')
    #
    # # 退货补运费->运费险详情
    # def test_Insurance_detail(self):
    #     self.to_Insurance()
    #     self.find_order()
    #     # 退货补运费->运费险详情
    #     self.assert_text('查看详情', '//*[@id="root"]/div/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/table/tbody/tr/td[6]/button/span')
    #     self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/table/tbody/tr/td[6]/button/span')
    #     sleep(3)
    #     self.assert_text('服务详情', '//*[@id="root"]/div/div/div/div[2]/div[1]/div[1]/span')
