from unittest import skip

from ddt import ddt
from .base import BaseTestCase
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase
from time import sleep


# 快手小店-售后助手
@ddt
class TestRefundAtom(BaseTestCase):
    def setup_class(self):
        self.order_id = ****************
        self.refund_id = ****************
        self.item_name = '「测试商品」带运费险的休闲裤'
        self.item_id = *************

    # 跳转到售后助手
    def to_atom(self):
        self.login("MERCHANT_DOMAIN", "refund_account")
        self.assert_title("快手小店")
        sleep(2)
        #self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.assert_text("售后助手", '//*[@id="menu_item_89"]/span/span')
        self.click('//*[@id="menu_item_89"]/span/span')

    '''
    售后助手->添加策略
    售后助手->使用说明
    售后助手->编辑策略
    '''

    # # 售后助手->添加策略
    # def test_add_strategy(self):
    #     self.to_atom()
    #     self.assert_text('添加策略', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[1]/button/span')
    #     self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[1]/button/span')
    #     self.assert_text('策略选择', '//*[@id="root"]/div/div[1]/span[2]/span[1]')
    #
    # # 售后助手->使用说明
    # def test_instructions(self):
    #     self.to_atom()
    #     self.assert_text('使用说明', '//*[@id="root"]/div/div[2]/div/div[1]/div[3]/a')
    #     self.click('//*[@id="root"]/div/div[2]/div/div[1]/div[3]/a')
    #     self.assert_text('售后小助手（ATOM）功能说明', '//*[@id="app"]/section/main/div/div/div[2]/div[1]/p[1]/span/strong')
    #
    # # 售后助手->编辑策略
    # def test_edit_strategy(self):
    #     self.to_atom()
    #     self.assert_text('编辑', '//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/div/div[1]/button/span')
    #     self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[1]/div[2]/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/div/div[1]/button/span')
    #     self.assert_text('发货前-自动仅退款', '//*[@id="root"]/div/div[2]/div/div/div/span[1]')
