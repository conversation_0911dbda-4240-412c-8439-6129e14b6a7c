# -*- coding: utf-8 -*-
# @Time : 2022/7/29 8:41 PM
# <AUTHOR> xietingting
# @File : test_order_list.py
# @Project : kwaishopuiautotest
# import pytest
# from time import sleep
#
# from ddt import ddt
# from filetype.types import document
#
# from test_case.core_link.trade.base import BaseTestCase
# from selenium.webdriver.support.select import Select
#
# @ddt
# class MyTestClass(BaseTestCase):
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_to_detail(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#
#
#         # 点击收起
#         self.click(
#             '#root > section > section > main > div > div > div.sc-kfPuZi.fehKFO.sc-gWXbKe.cobgVh > div.sc-kLwhqv.foBIhK > div.pack-up-button > span')
#         sleep(2)
#         #订单列表->订单详情
#         self.open_orderDetail()
#
#
#
#     # 订单列表->商详
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_to_product(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         # 点击收起
#         self.click(
#             '#root > section > section > main > div > div > div.sc-kfPuZi.fehKFO.sc-gWXbKe.cobgVh > div.sc-kLwhqv.foBIhK > div.pack-up-button > span')
#         sleep(2)
#         self.click('#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item > div > div.ant-col.ant-col-5.order-item-goods > div.sc-eCImPb.igzRmY > div > div.goods-title > a')
#         sleep(5)
#         #self.wait_for_element_visible('#driver-popover-item')
#         if self.is_element_visible('#driver-popover-item'):
#             self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
#         # 商品状态
#         self.assert_element('#root > section > main > div:nth-child(2) > div > div > table > tbody > tr:nth-child(1) > td > span.ant-descriptions-item-label.ant-descriptions-item-colon')
#         #商品属性
#         self.assert_element('#root > section > main > div:nth-child(3) > div > div.ant-descriptions-title')
#         self.assert_true('/zone/goods/detail?' in (self.get_current_url()))
#
#
#      #已导出报表
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_to_exportPort(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         self.click( '//*[@id="root"]/section/section/main/div/div/div[2]/div/div[1]/form/div[3]/div/a/button/span')
#         sleep(5)
#         self.assert_text('已导出的订单报表','//*[@id="root"]/section/section/main/div/div[1]')
#         self.assert_equal(self.get_current_url().endswith('/zone/order/export'), True)
#
#      #客服
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_to_customer(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         self.click('#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item-title > div.right > span.sc-pVTFL.bZvsln')
#         sleep(2)
#         self.assert_title("快手小店客服")
#      #退款详情
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_to_refundDetail(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         # 找个有退款记录的订单
#         self.type('#oid', '****************')
#         self.click('//*[@id="root"]/section/section/main/div/div/div[2]/div/div[1]/form/div[3]/div/button[1]')
#         sleep(2)
#         if self.is_element_visible('#driver-popover-item'):
#             self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
#         #退款ID
#         refund_id = self.get_text(
#             '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div > div.order-list-item > div > div:nth-child(3) > div > a',by="css selector", timeout=None)
#         refund_detail_url = '/zone/refund/detail?refundId=****************'
#         print('refund_detail_url'+refund_detail_url)
#         self.click('#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div > div.order-list-item > div > div:nth-child(3) > div > a')
#
#         self.switch_to_window(1)
#         sleep(5)
#         # 弹窗
#         if self.is_element_visible('#driver-popover-item'):
#             self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
#         # 订单详情页面+元素检查
#         self.assert_equal(self.get_current_url().endswith(refund_detail_url), True)
#         self.assert_text('售后信息', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[1]/div[1]/span')
#         self.assert_text('订单信息', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[1]/div[1]/span')
#
#     #订单-->发货
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_sendGoods(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         self.click('#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list-tab > div > ul > li:nth-child(3) > div')
#         sleep(5)
#         self.click('//*[@id="root"]/section/section/main/div/div/div[4]/div/div/div[2]/div/div[2]/div[1]/div[2]/div/div[8]/button')
#         sleep(3)
#         #退款拦截
#         if self.is_element_visible('body > div:nth-child(19) > div > div.ant-modal-wrap > div > div.ant-modal-content'):
#             self.click('body > div:nth-child(19) > div > div.ant-modal-wrap > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button.ant-btn.ant-btn-primary')
#
#
#         self.assert_element('body > div:nth-child(12) > div > div.ant-modal-wrap > div > div.ant-modal-content > div.ant-modal-footer > button.ant-btn.ant-btn-primary')
#     #
#
#
#     #订单-->通知中心
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderlist_broadcasts(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[3]/div[6]/div/span/a')
#         sleep(2)
#         self.assert_equal(self.get_current_url().endswith('/notification/list'), True)
#     #订单详情->商品详情
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderdetail_to_product(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         sleep(5)
#         # 点击收起
#         self.click('#root > section > section > main > div > div > div.sc-kfPuZi.fehKFO.sc-gWXbKe.cobgVh > div.sc-kLwhqv.foBIhK > div.pack-up-button > span')
#         sleep(2)
#         self.open_orderDetail()
#         self.switch_to_window(1)
#         sleep(5)
#         self.click('#root > section > section > main > div:nth-child(3) > div.ant-descriptions.ant-descriptions-bordered > div > table > tbody > tr:nth-child(2) > td:nth-child(1) > span > div > div > div > a')
#         #self.wait_for_element_visible('#driver-popover-item')
#         self.switch_to_window(2)
#         if self.is_element_visible('#driver-popover-item'):
#             self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
#         sleep(5)
#         url=self.get_current_url()
#         print(url)
#         self.assert_true('/zone/goods/detail?' in (self.get_current_url()))
#      # 商品状态
#         self.assert_element(
#             '//*[@id="root"]/section/main/div[2]/div/div/table/tbody/tr[1]/td/span[1]')
#
#
#
# #订单详情->客服页
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderdetail_to_custome(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open_orderList()
#         sleep(5)
#         sleep(5)
#         # 点击收起
#         self.click('#root > section > section > main > div > div > div.sc-kfPuZi.fehKFO.sc-gWXbKe.cobgVh > div.sc-kLwhqv.foBIhK > div.pack-up-button > span')
#         sleep(2)
#         self.open_orderDetail()
#         self.switch_to_window(1)
#         sleep(5)
#         self.click('#root > section > section > main > div:nth-child(3) > div:nth-child(4) > div > table > tbody > tr:nth-child(3) > td > div > span.ant-descriptions-item-content > span')
#         #self.switch_to_window(1)
#        # self.wait_for_element_visible('#driver-popover-item')
#         if self.is_element_visible('#driver-popover-item'):
#             self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
#         self.assert_title("快手小店客服")
#
# # 订单详情->售后详情
#     @pytest.mark.p0
#     @pytest.mark.skip
#     def test_orderdetail_to_refund(self):
#         self.login("MERCHANT_DOMAIN", "order_account")
#         self.assert_title("快手小店")
#         sleep(10)
#         self.open('https://prt-eshop-s.test.gifshow.com/zone/order/detail?id=****************')
#         sleep(5)
#         self.click('#root > section > section > main > div:nth-child(3) > div.ant-descriptions.ant-descriptions-bordered > div > table > tbody > tr:nth-child(2) > td:nth-child(5) > span > div > a')
#         sleep(5)
#         self.switch_to_window(1)
#         if self.is_element_visible('#driver-popover-item'):
#             self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
#         refund_detail_url='/zone/refund/detail?refundId=****************'
#         # 订单详情页面+元素检查
#         #self.switch_to_window(1)
#         self.assert_equal(self.get_current_url().endswith(refund_detail_url), True)
#         self.assert_text('售后信息', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[1]/div[1]/div[1]/span')
#         self.assert_text('订单信息', '//*[@id="root"]/div/div/div/div/div[2]/div[2]/div[2]/div[1]/div[1]/span')
#
#
#
#     def test_order_manage(self):
#         """订单管理页面test"""
#         self.login("MERCHANT_DOMAIN", "zhangli_account")
#         self.assert_title("快手小店")
#
#         sleep(10)
#         self.open('https://prt-eshop-s.test.gifshow.com/zone/order/list')
#         self.type(document.querySelectorAll('.ant-select-selection-item')[1], "非退款")
#



