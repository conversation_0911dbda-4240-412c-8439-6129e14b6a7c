from asyncio import sleep

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.assert_text("扫码登录", "div.choseTab--gqNJB > div:nth-child(1)")  # div标签中的第一个元素
        self.assert_text("手机号登录", "div.choseTab--gqNJB > div:nth-child(2)")
        self.click("div.choseTab--gqNJB > div:nth-child(2)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])

        self.click("button[type='button']")
        sleep(2)
    def open_orderList(self):
        self.wait_for_element_visible('#driver-popover-item')
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.assert_element('//*[@id="node_menu_for_intersectionObserver_1"]')
        self.assert_text('订单查询', '//*[@id="menu_item_1"]')
        # 点击订单查询打开列表
        self.click('#menu_item_1 > span > span')
        sleep(3)
        # 关闭弹窗
        if self.is_element_visible('div.ant-modal-confirm-body-wrapper'):
            self.click(
                'body > div:nth-child(13) > div > div.ant-modal-wrap > div > div.ant-modal-content > div > div > div.ant-modal-confirm-btns > button:nth-child(1) > span')
        self.assert_equal(self.get_current_url().endswith('/zone/order/list'), True)

    def open_orderDetail(self):
        order_id = self.get_text(
            '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item-title > div.left > a',
            by="css selector", timeout=None)
        sleep(2)
        self.click(
            '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item-title > div.left > a')
        sleep(5)
        # 弹窗
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        order_detail_url = '/zone/order/detail?id=' + order_id
        # 订单详情页面+元素检查
        self.assert_equal(self.get_current_url().endswith(order_detail_url), True)
        self.assert_text('订单信息', '#root > section > section > main > div:nth-child(4) > div.sc-ieecCq.cFvzFs')
        # 点击订单查询打开列表
        self.click('#menu_item_1 > span > span')
        sleep(5)

        # 订单列表操作->订单详情
        self.assert_text('订单详情',
                         '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item > div > div:nth-child(8) > a')
        self.click(
            '#root > section > section > main > div > div > div:nth-child(4) > div > div > div.order-list > div > div.lists > div:nth-child(1) > div.order-list-item > div > div:nth-child(8) > a')
        sleep(5)
        self.assert_equal(self.get_current_url().endswith(order_detail_url), True)
