# -*- coding: utf-8 -*-
# @Time : 2022/7/29 8:41 PM
# <AUTHOR> xietingting
# @File : test_order_list.py
# @Project : kwaishopuiautotest
from unittest import skip

import pytest
from time import sleep

from ddt import ddt

from constant.domain import get_domain
from .base import BaseTestCase

@ddt
class TestOrderdeliver(BaseTestCase):
    @pytest.mark.p0
    @skip("测试")
    def test_orderlist_to_detail(self):
        self.login("MERCHANT_DOMAIN", "order_account")
        self.assert_title("快手小店")
        sleep(5)
        self.open_orderList()
        # 点击收起
        self.click(
            '#root > section > section > main > div > div > div.sc-kfPuZi.fehKFO.sc-gWXbKe.cobgVh > div.sc-kLwhqv.foBIhK > div.pack-up-button > span')
        sleep(2)
        #订单列表->订单详情
        self.open_orderDetail()

    #订单-->发货
    @pytest.mark.p0
    # @skip("tiaoguo")
    def test_orderlist_sendGoods(self):
        self.login("PRODUCT_DOMAIN", "logistics_account")
        self.assert_title("快手小店")
        sleep(2)
        # # 等待弹窗可见
        # self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        self.open_orderList()
        self.press_down_arrow(selector="html", times=2, by="css selector")
        # sleep(5)
        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div/div[4]/div/div/div[1]/div[1]/ul/li[3]/div')
        sleep(1)
        #待发货订单数据
        deliver_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div/div[4]/div/div/div[2]/div/div[2]/div[1]/div[2]/div')
        self.assert_in("发货", deliver_text) #有待发货订单
        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div/div[4]/div/div/div[2]/div/div[2]/div[1]/div[2]/div/div[8]/button') #点击发货
        sleep(1)
        # expressNo_text = self.get_text('/html/body/div[3]/div/div[2]/div/div[2]/div[2]/form/span[1]/div/div[1]/div[1]')
        check_text = self.get_text('#rcDialogTitle0') # 获取订单发货页面
        # expressCode_text = self.get_text('/html/body/div[3]/div/div[2]/div/div[2]/div[2]/form/span[1]/div/div[2]/div[1]')
        self.assert_equal("订单发货",check_text)
        # self.assert_equal("物流公司",expressCode_text)
        sleep(1)

        #发货页面校验
        print("--------")
        # self.type('/html/body/div[3]/div/div[2]/div/div[2]/div[2]/form/span[1]/div/div[1]/div[2]/div/span/input', '****************')  #填写运单号
        # self.type('/html/body/div[3]/div/div[2]/div/div[2]/div[2]/form/span[1]/div/div[2]/div[2]/div/span/div/div', account_data['password'])#填写公司
        # #
        sleep(1)
        # self.click('//*[@id="status"]/div/div')
        # self.click('//*[@id="b6d54e8e-daac-4dac-e951-d9006a1da843"]/ul/li[3]')
        # self.click('//*[@id="root"]/section/section/main/div/div/div[2]/div/div[1]/form/div[3]/div/button[1]')
        # self.click('//*[@id="root"]/section/section/main/div/div/div[4]/div/div/div[2]/div/div[2]/div[1]/div[2]/div/div[8]/button')
        # sleep(5)
        # self.assert_element('/html/body/div[4]/div/div[2]/div')
    #订单-->合并发货
    @pytest.mark.p0
    # @skip("tiaoguo")
    def test_orderlist_sendGoods_merge(self):
        self.login("PRODUCT_DOMAIN", "logistics_account")
        self.assert_title("快手小店")
        sleep(2)
        # # 等待弹窗可见
        # self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        sleep(2)
        self.open_orderList()
        self.press_down_arrow(selector="html", times=2, by="css selector")
        # sleep(5)
        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div/div[4]/div/div/div[1]/div[1]/ul/li[3]/div')
        sleep(1)
        # 待发货订单数据
        deliver_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div/div[4]/div/div/div[2]/div/div[2]/div[1]/div[2]/div')
        # print(deliver_text)
        self.assert_in("合并发货", deliver_text) #有待发货订单
        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div/div[4]/div/div/div[2]/div/div[2]/div[1]/div[2]/div/div[8]/a[1]/button') #点击合并发货
        # self.switch_to_window(1) #打开新窗口
        sleep(1)

        mergeDeliver_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div')
        self.assert_in("合并订单发货", mergeDeliver_text) #有待发货订单

        # check_text = self.get_text('#rcDialogTitle0') # 获取订单发货页面
        # self.assert_equal("订单发货",check_text)
        sleep(1)

        merge_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div')
        self.assert_in("收货地址", merge_text) #有待发货订单
        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div/button') #点击合并发货
        sleep(1)

        # check_text = self.get_text('#rcDialogTitle1') # 获取合并订单发货页面
        # self.assert_equal("合并单发货",check_text)
        # sleep(1)

    #订单详情-->发货
    @pytest.mark.p0
    # @skip("tiaoguo")
    def test_orderdetail_sendGoods(self):
        self.login("PRODUCT_DOMAIN", "logistics_account")
        self.assert_title("快手小店")
        sleep(2)
        # # 等待弹窗可见
        # self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        # if self.is_element_visible('#driver-popover-item'):
        #     self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        # sleep(2)
        self.open_orderList()
        self.press_down_arrow(selector="html", times=2, by="css selector")
        # sleep(5)
        self.open("https://prt-eshop-s.test.gifshow.com/zone/order/detail?id=****************")

        sleep(5)
        #待发货订单数据
        print("---------")

        # deliver_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div[1]')
        # print("---------")
        #
        # print(deliver_text)


        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div[1]/div[4]/button[1]') #点击发货
        sleep(1)
        check_text = self.get_text('#rcDialogTitle1')  # 获取订单发货页面
        self.assert_equal("订单发货", check_text)
        sleep(1)

        # merge_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div')
        # self.assert_in("收货地址", merge_text) #有待发货订单
        # self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div/button') #点击合并发货
        # sleep(1)


    #订单详情-->合并发货
    @pytest.mark.p0
    def test_orderdetail_sendGoods_merge(self):
        self.login("PRODUCT_DOMAIN", "logistics_account")
        self.assert_title("快手小店")
        sleep(2)
        # 等待弹窗可见
        self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        # if self.is_element_visible('#driver-popover-item'):
        #     self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        # sleep(2)
        self.open_orderList()
        self.press_down_arrow(selector="html", times=2, by="css selector")
        # sleep(5)
        self.open("https://prt-eshop-s.test.gifshow.com/zone/order/detail?id=****************")

        sleep(5)
        #待发货订单数据
        print("---------")

        # deliver_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div[1]')
        # print("---------")
        #
        # print(deliver_text)


        self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div[1]/div[4]/a/button') #点击发货
        sleep(1)
        mergeDeliver_text = self.get_text(
            '/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div')
        self.assert_in("合并订单发货", mergeDeliver_text)  # 有待发货订单

        # check_text = self.get_text('#rcDialogTitle0') # 获取订单发货页面
        # self.assert_equal("订单发货",check_text)
        sleep(1)

        merge_text = self.get_text(
            '/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div')
        self.assert_in("收货地址", merge_text)  # 有待发货订单
        self.click(
            '/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div/button')  # 点击合并发货
        sleep(1)
        # merge_text = self.get_text('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div')
        # self.assert_in("收货地址", merge_text) #有待发货订单
        # self.click('/html/body/div[2]/div/div/div/div[2]/div[2]/div[1]/div/div/section/section/main/div/div[2]/div/div/ul/li/div/div/div/div/div/div[1]/div/button') #点击合并发货
        # sleep(1)


