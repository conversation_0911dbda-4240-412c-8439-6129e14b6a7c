#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : liugang07
 Time : 2022-08-01 20:51
 DESC :
 
 """
from time import sleep

import pytest

from .base import BaseTestCase


class TestReport(BaseTestCase):

    @pytest.mark.p0
    def test_report_nomepage(self):
        self.login("PRODUCT_DOMAIN", "logistics_account")
        self.assert_title("快手小店")
        sleep(2)
        # 等待弹窗可见
        self.wait_for_element_visible('#driver-popover-item')
        # 判断是否有弹窗，如果有的话点击确定
        if self.is_element_visible('#driver-popover-item'):
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        self.open("https://prt-eshop-s.test.gifshow.com/zone/supply/express/delayship-report")
        # sleep(2)
        # self.click('//*[@id="driver-page-overlay"]')
        # sleep(2)
        # self.click('//*[@id="driver-page-overlay"]')
        self.click('//*[@id="menu_item_12"]')
        # self.switch_to_window(1) #打开新窗口
        report_text = self.get_text('#root > section > main > div')  # 获取延迟报备公告
        print(report_text)
        report_notics1 = "亲爱的商家：最近疫情多发影响发货，平台针对主要疫情区域做了物流考核保护，详情可在规则中心查询关于疫情影响下的发货考核调整公告。若受影响区域不在公告范围内，可申请延迟发货报备。点击查看操作指南"
        # report_notics2 = "2. 延迟发货报备系统功能于3月3日进行了优化升级。3月3日后产生的新订单可正常报备，3月3日之前产生的订单如需报备，请通过[特殊订单]报备类型进行报备。"
        report_notics = [report_notics1]
        for report_notic in report_notics:
            self.assert_in(report_notic, report_text)
        sleep(2)
        # print(self.get_text('#root > section > main >div:nth-child(4) > div:nth-child(2) > div  > div  > div > div > div > table > tbody > tr:nth-child(1)>td:nth-child(5) > button'))
        # 查看报备单
        self.click(
            '#root > section > main >div:nth-child(4) > div:nth-child(2) > div  > div  > div > div > div > table > tbody > tr:nth-child(1)>td:nth-child(5) > button')
        sleep(2)
        check_text = self.get_text('#rcDialogTitle0')  # 获取报备单详情
        self.assert_equal("查看延迟报备单", check_text)
