import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
import random
from time import sleep
from selenium.webdriver.common.by import By
from ddt import ddt
from unittest import skip, skipIf

from test_case.core_link.product_center.base_config import UI_ITEMS_ID
from utils.env_help import get_work_dir
from test_case.core_link.product_center.base import BaseTestCase

class Test_bubtton(BaseTestCase):

    @pytest.mark.p0
    # @pytest.mark.smoke # 其他case test_product_recycle 已覆盖
    def test_product_button(self):
        """
        测试商品上下架
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['up_down']
        self.query_good_by_id(good_id, '全部')

        # 下架
        self.click('//span[contains(text(),"发布相似品")]/../../button/span')  # 下架按钮
        self.click('//span[contains(text(),"确 定")]', delay=1)
        sleep(2)
        self.assert_element('//span[contains(text(),"下架成功")]')
        sleep(5)

        # 上架
        self.click('//span[contains(text(),"发布相似品")]/../../button/span', delay=3)  # 上架按钮
        self.click('//span[contains(text(),"确 定")]', delay=1)
        sleep(2)
        self.assert_text("上架成功",
                         "//div[@class='goods-list-v1-message-custom-content goods-list-v1-message-success']")

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_product_recycle(self):
        """
        商品列表，删除操作的弹窗；
        回收站，恢复商品的弹窗
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id_1 = UI_ITEMS_ID['up_down'] # 下架未删除
        good_id_2 = UI_ITEMS_ID['in_recycling']

        # 下架
        self.query_good_by_id(good_id_1, '全部')
        self.click('//span[contains(text(),"发布相似品")]/../../button/span')  # 下架按钮
        self.click('//span[contains(text(),"确 定")]', delay=1)
        sleep(2)
        self.assert_element('//span[contains(text(),"下架成功")]')
        # 删除商品
        sleep(6)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"更多")]')[-1].click()
        sleep(1)
        self.click('//span[contains(text(),"删除")]')
        self.assert_element('//span[contains(text(),"确定删除该商品吗?")]')
        self.click('//span[contains(text(),"取 消")]')
        # 上架
        self.click('//span[contains(text(),"发布相似品")]/../../button/span', delay=3)  # 上架按钮
        self.click('//span[contains(text(),"确 定")]', delay=1)
        sleep(2)
        self.assert_text("上架成功",
                         "//div[@class='goods-list-v1-message-custom-content goods-list-v1-message-success']")

        #进入回收站
        sleep(3)
        self.driver.find_element(By.XPATH, '//span[contains(text(),"回收站")]').click()
        # 放量状态，有重复跳转，增加时长
        sleep(10)
        self.switch_to_window(1)
        # self.type("//input[@placeholder=\"多条之间用中英文逗号或者空格分隔,最多50个\"]", good_id_2)
        # self.click('//span[contains(text(),"查 询")]')
        # sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"恢复")]')[-1].click()
        sleep(1)
        self.assert_element('//div[contains(text(),"确定恢复该商品吗")]')
        self.click('//span[contains(text(),"取 消")]')










