#!/usr/bin/env/ python
# coding:utf-8
import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
import random
from time import sleep
from ddt import ddt
from unittest import skip, skipIf
# import sys
# sys.path.append("..")
from utils.env_help import get_work_dir
from test_case.core_link.product_center.base import BaseTestCase


class TestProductDiagonise(BaseTestCase):

    @skip("111")
    @pytest.mark.p1
    def test_product_diagonise(self): #todo 适配case
        self.login("PRODUCT_DOMAIN", "product_account_2")
        self.assert_title("快手小店")
        sleep(5)

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        # 页面切换延迟，重复点击
        sleep(2)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click("//span[contains(text(),'商品诊断')]")
        self.driver.maximize_window()
        self.driver.refresh()
        #self.assert_no_404_errors()

        self.assert_text('全部', "//div[contains(text(),'全部')]")
        self.assert_text('警告', "//div[contains(text(),'警告')]")
        self.assert_text('提醒', "//div[contains(text(),'提醒')]")
        self.assert_text('达标', "//div[contains(text(),'达标')]")
        self.assert_text('优秀', "//div[contains(text(),'优秀')]")

        jinggao = self.get_text("//span[text()='警告']/../span[2]")
        tixing = self.get_text("//span[text()='提醒']/../span[2]")
        dabiao = self.get_text("//span[text()='达标']/../span[2]")
        youxiu = self.get_text("//span[text()='优秀']/../span[2]")

        dic = dict(警告=jinggao, 提醒=tixing, 达标=dabiao,优秀=youxiu)
        dic0 = dict()
        dic1 = dict()
        for key in dic.keys():
            if dic[key] == "0":
                dic0[key] = dic[key]
            else:
                dic1[key] = dic[key]


        for key in dic0.keys():
            self.driver.refresh()
            sleep(5)
            self.click("//div[contains(text(),'"+ key +"')]")
            sleep(5)
            self.assert_element_visible("//div[@class='diagnosis-empty-description']")
            sleep(5)

        for key in dic1.keys():
            self.driver.refresh()
            sleep(5)
            self.click("//div[contains(text(),'"+ key +"')]")
            sleep(5)
            self.assert_element_visible("//span[contains(text(),'" + key+ "')]")
            sleep(5)






