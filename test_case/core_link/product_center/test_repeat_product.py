import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
import random
from time import sleep
from selenium.webdriver.common.by import By
from ddt import ddt
from unittest import skip, skipIf
from utils.env_help import get_work_dir
from test_case.core_link.product_center.base import BaseTestCase

class Test_bubtton(BaseTestCase):


    @pytest.mark.p0
    @pytest.mark.smoke
    def test_main_product(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        sleep(2)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        # self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        # self.click('//span[contains(text(),"查 询")]')
        sleep(5)
        #经营助手挪下位置
        self.execute_script(
            'document.getElementById("kpro-tool-box-tools-containner").style="bottom: 95px; right: 4px; z-index: 99; transform: translate(0px, -101px);"')
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查看重复品")]')[0].click()
        sleep(5)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查看重复品")]')[1].click()
        sleep(5)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"设为主推品")]')[0].click()
        self.assert_text('确定将该商品设置为重复品组的主推品吗?', "//span[contains(text(),'确定将该商品设置为重复品组的主推品吗?')]")
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('已设为主推品', "//span[contains(text(),'已设为主推品')]")
        sleep(8)
        self.assert_text('常销主推', "//span[contains(text(),'常销主推')]")

        #取消
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"取消主推品")]')[0].click()
        self.assert_text('取消后不再展示销量聚合卖点', "//div[contains(text(),'取消后不再展示销量聚合卖点')]")
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('已取消主推品', "//span[contains(text(),'已取消主推品')]")
        sleep(8)
        self.assert_text_not_visible('常销主推', "//span[contains(text(),'常销主推')]")

    def test_viewrepeat_product(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(10)
        self.execute_script('document.getElementById("kpro-tool-box-tools-containner").style="bottom: 295px; right: 4px; z-index: 99; transform: translate(0px, -201px);"')
        sleep(2)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查看重复品")]')[0].click()


