import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from unittest import skip
from selenium.webdriver.common.keys import Keys

class TestChildProdcut(BaseTestCase):

    @pytest.mark.smoke
    @pytest.mark.p0
    def test_child_product_list(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        # 展示商品列表
        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        sleep(2)
        self.click("//span[contains(text(),'达人专属品')]")
        sleep(5)
        self.assert_text('达人专属品运营攻略', "//div[contains(text(),'达人专属品运营攻略')]")

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_child_product_edit(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        sleep(2)
        self.click("//span[contains(text(),'达人专属品')]")
        sleep(5)
        self.assert_text('达人专属品运营攻略', "//div[contains(text(),'达人专属品运营攻略')]")
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"编辑")]')[0].click()
        sleep(5)
        self.switch_to_window(1)

        self.assert_no_404_errors()
        self.assert_element('//p[contains(text(),"主商品信息")]')
        self.assert_element('//p[contains(text(),"子商品类型")]')

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_child_product_delete(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        sleep(2)
        self.click("//span[contains(text(),'达人专属品')]")
        sleep(5)
        self.assert_text('达人专属品运营攻略', "//div[contains(text(),'达人专属品运营攻略')]")
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"删除")]')[0].click()
        sleep(3)
        self.assert_element('//span[contains(text(),"确定删除该达人专属品吗？")]')
        self.assert_element('//div[contains(text(),"建议不要在达人带货时删除，以免影响达人带货效果。删除达人专属品后，对应达人售卖时将以主商品信息进行售卖")]')

    @pytest.mark.p1
    def test_child_product_batchDelete(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        sleep(2)
        self.click("//span[contains(text(),'达人专属品')]")
        sleep(5)
        self.assert_text('达人专属品运营攻略', "//div[contains(text(),'达人专属品运营攻略')]")
        self.click("//span[@class='kwaishop-goods-release-multimodal-pc-checkbox-inner']")
        sleep(2)
        self.click("//span[contains(text(),'批量操作')]")
        self.click("//div[contains(text(),'批量删除')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"确定删除全部选中的达人专属品吗？")]')
        self.assert_element(
            '//div[contains(text(),"建议不要在达人带货时删除，以免影响达人带货效果。删除达人专属品后，对应达人售卖时将以主商品信息进行售卖")]')
