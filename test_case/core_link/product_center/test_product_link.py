import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip

class Testproducecorelink(BaseTestCase):


    @pytest.mark.p1
    def test_forbiddon_link(self):
        self.add_page("其他钟表","钟表类目","search")
        self.click("//a[contains(text(),'快手电商违禁词典')]")
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)        # self.assert_text('课程目录', "//span[contains(text(),'课程目录')]")

    @pytest.mark.p1
    def test_feedback_link(self):
        self.add_page("autotest-类目属性校验","属性类目","search")
        # 移动属性到部分
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_goodsAttrs"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)

        self.click("//a[contains(text(),'点击反馈')]")
        sleep(3)
        self.assert_element("//div[contains(text(),'属性意见反馈')]")

    @pytest.mark.p1
    def test_brand_link(self):
        self.add_page("autotest-类目属性校验","属性类目","search")
        # 移动属性到部分
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_goodsAttrs"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        # self.click('//div[@class="goods-formily-grid-layout"]/div/div/span')

        self.click("//a[contains(text(),'申报指南')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)        # self.assert_text('【品牌申报】使用教程', "//div[contains(text(),'【品牌申报】使用教程')]")
        # sleep(10)

    @skip("本地有问题，暂时跳过")
    def test_brand_guide_link(self): # todo 适配case
        self.add_page("autotest-类目属性校验","属性类目","search")
        ele = self.driver.find_element(By.XPATH, '//a[contains(text(),"点击反馈")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.click('//div[@class="goods-formily-grid-layout"]/div/div/span')
        self.driver.find_elements(By.XPATH, '//a[contains(text(),"指南")]')[1].click()
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)

    @pytest.mark.p1
    def test_view_example_prop(self):
        self.add_page("autotest-类目属性校验","属性类目","search")
        # 处理属性折叠
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"重要属性")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        if self.is_element_visible('//span[contains(text(),"展开查看更多属性")]'):
            self.click('//span[contains(text(),"展开查看更多属性")]')
            sleep(1)

        # 移动属性到部分
        ele = self.driver.find_element(By.XPATH, '//label[contains(text(),"适用肤质")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        self.click("//span[contains(text(),'查看示例')]")
        sleep(3)
        self.assert_text('基本要求', "//h4[contains(text(),'基本要求')]")
        sleep(2)
        self.click("//span[contains(text(),'我知道了')]")
        sleep(2)
        self.assert_not_equal('基本要求', "//h4[contains(text(),'基本要求')]")

    @pytest.mark.p1
    def test_view_example_main_photo(self):
        self.add_page("其他钟表","钟表类目","search")
        #主图基本示例
        ele = self.driver.find_element(By.XPATH, '//div[@id="GoodsImg"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)

        self.click("//span[contains(text(),'查看示例')]")
        sleep(3)
        self.assert_text('基本要求', "//h4[contains(text(),'基本要求')]")
        self.assert_text('1. 第一张必须为高清正面商品图。商品主体需清晰完整、并与实物保持一致，勿对商品遮挡、过度处理。勿包含其他平台水印、评价、地址等信息。',
                         "//span[contains(text(),'1. 第一张必须为高清正面商品图。商品主体需清晰完整、并与实物保持一致，勿对商品遮挡、过度处理。勿包含其他平台水印、评价、地址等信息。')]")
        self.assert_text('2. 请上传长宽比为1:1的方形图片，大小不超过2M，支持jpg/jpeg/png格式。',"//span[contains(text(),'2. 请上传长宽比为1:1的方形图片，大小不超过2M，支持jpg/jpeg/png格式。')]")
        self.click("//span[contains(text(),'我知道了')]")
        self.assert_not_equal('基本要求', "//h4[contains(text(),'基本要求')]")


    def test_view_example_material_photo(self):
        self.add_page("其他钟表","钟表类目","search")
        #商品素材示例
        ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'第一张必须为高清正面商品图')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查看示例")]')[1].click()
        sleep(3)
        self.assert_text('基本要求', "//h4[contains(text(),'基本要求')]")
        self.click("//span[contains(text(),'我知道了')]")
        sleep(3)
        self.assert_not_equal('基本要求', "//h4[contains(text(),'基本要求')]")
        sleep(3)

    def test_servicePromise_photo(self):
        self.add_page("其他钟表","钟表类目","search")
        ele = self.driver.find_element(By.XPATH,  "//span[contains(text(),'退货规则')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"查看示例")]')[2].click()
        sleep(3)
        self.assert_text('基本要求', "//h4[contains(text(),'基本要求')]")
        self.click("//span[contains(text(),'我知道了')]")
        self.assert_not_equal('基本要求', "//h4[contains(text(),'基本要求')]")
        sleep(3)

    def test_servicerule(self):
        self.add_page("其他钟表","钟表类目","search")
        # 限购示例
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_spec"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)
        self.click("//a[contains(text(),'《发货规则及设置指南》')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)        # self.assert_text('商品发货规则及设置指南', "//strong[contains(text(),'商品发货规则及设置指南')]")
        # sleep(3)

    def test_presale(self):
        self.add_page("其他钟表","钟表类目","search")
        # 限购示例
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_spec"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)
        self.click("//a[contains(text(),'查看《现货+预售模式使用指南》')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)
        self.switch_to_window(1)
        sleep(3)
        self.assert_text('【现货+预售发货模式】使用教程', "//div[contains(text(),'【现货+预售发货模式】使用教程')]")
        sleep(3)

    def test_expressTemplate(self):
        self.add_page("其他钟表","钟表类目","search")
        # 限购示例
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_serviceRule_deliveryMethod"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)
        self.click("//a[contains(text(),'运费模板使用说明')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)


    def test_sevendays(self):
        self.add_page("其他钟表","钟表类目","search")
        # 限购示例
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_serviceRule_refundRule"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)
        self.click("//a[contains(text(),'7天无理由退货规则')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)        #self.assert_text('快手小店七天无理由退货管理规则', "//span[contains(text(),'快手小店七天无理由退货管理规则")
        sleep(3)

    def test_servicePromise1(self):
        self.add_page("其他钟表","钟表类目","search")
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_serviceRule_refundRule"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)
        self.click("//a[contains(text(),'快手小店坏了包退服务规则')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)
        sleep(3)

    def test_servicePromise2(self):
        self.add_page("其他钟表","钟表类目","search")
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_serviceRule_refundRule"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.click("//a[contains(text(),'快手小店破损包退服务规则')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)


    def test_servicePromise3(self):
        self.add_page("其他钟表","钟表类目","search")
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_serviceRule_refundRule"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.click("//a[contains(text(),'快手小店过敏包退服务规则')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)

    # @skip("111")
    def test_expressTemplate_update(self):
        self.add_page("其他钟表","钟表类目","search")
        ele = self.driver.find_element(By.XPATH, '//p[contains(text(),"服务与售后")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)

        self.driver.find_elements(By.XPATH, '//div[@class="goods-select-selector"]')[4].click()
        sleep(2)
        self.click("//span[contains(text(),'默认全国（非港澳台）包邮模板')]")
        sleep(1)

        self.click("//span[contains(text(),'新增模板')]")
        sleep(3)
        self.switch_to_window(-1)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)

        # 有个弹窗
        self.switch_to_window(0)
        self.assert_element('//span[contains(text(),"确定已完成新运费模板创建?")]')
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()
        sleep(1)

        ele = self.driver.find_element(By.XPATH, '//p[contains(text(),"服务与售后")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"编辑")]')[2].click()
        sleep(3)
        self.switch_to_window(-1)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)





