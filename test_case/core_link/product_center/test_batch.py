import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip

from test_case.core_link.product_center.base_config import UI_ITEMS_ID


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p1
    def test_batch_offline(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量下架")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        self.click('//span[contains(text(),"确 定")]')

        self.assert_text('10个商品批量下架成功！', "//span[contains(text(),'10个商品批量下架成功！')]")
        sleep(5)

    @pytest.mark.p1
    def test_batch_online(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.click('//span[contains(text(),"已下架")]')
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量上架")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        self.click('//span[contains(text(),"确 定")]')

        self.assert_text('10个商品批量上架成功！', "//span[contains(text(),'10个商品批量上架成功！')]")
        sleep(5)

    @skip('商品数据有问题，待确认')
    @pytest.mark.p1
    def test_batch_delete(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        item_name = '测试商品勿拍不发货penapi发品单测商品标题（新增可删除'
        self.query_good_by_name(name=item_name)
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量下架")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('10个商品批量下架成功！', "//span[contains(text(),'10个商品批量下架成功！')]")
        # self.assert_title("快手小店")
        #
        # # 展示商品列表
        # self.click("//span[contains(text(),'商品')]")
        # self.click("//span[contains(text(),'商品列表')]")
        # sleep(2)
        # self.click('//span[contains(text(),"已下架")]')
        # sleep(2)
        # self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化发布同款")
        # self.click('//span[contains(text(),"查 询")]')
        # sleep(2)

        self.query_good_by_name(name=item_name, list_type='已下架')
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量删除")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        self.click('//span[contains(text(),"确 定")]')

        self.assert_text('10个商品批量删除成功！', "//span[contains(text(),'10个商品批量删除成功！')]")
        sleep(5)

    @pytest.mark.p1
    def test_batch_recover(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.open_url("https://s.kwaixiaodian.com/zone/goods/manage/list/recycleBin")
        sleep(2)
        ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'10 条/页')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(2)
        a=self.driver.find_elements(By.XPATH,'//ul[@class="goods-manage-pagination goods-manage-table-pagination goods-manage-table-pagination-right"]/li[8]')[0]
        value_1 = a.get_attribute("title")

        ele = self.driver.find_element(By.XPATH, "//label[contains(text(),'商品标题')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(2)

        self.type("//input[@placeholder=\"请输入关键字查询\"]", "UI自动化发布同款")
        self.click('//span[contains(text(),"筛 选")]')
        sleep(2)

        self.driver.find_elements(By.XPATH, '//input[@class="goods-manage-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量恢复")]')
        self.click('//span[contains(text(),"确 定")]')
        sleep(10)
        self.driver.refresh()
        sleep(2)

        ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'10 条/页')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(2)

        b = self.driver.find_elements(By.XPATH,'//ul[@class="goods-manage-pagination goods-manage-table-pagination goods-manage-table-pagination-right"]/li[8]')[0]
        value_2 = b.get_attribute("title")

        value_3=int(value_1)-int(value_2)
        assert value_3==1
        sleep(5)

    @skip('case内容待确认,先下线')
    def test_batch_recover(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.open_url("https://s.kwaixiaodian.com/zone/goods/manage/list/recycleBin")
        sleep(2)
        ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'10 条/页')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(2)
        a=self.driver.find_elements(By.XPATH,'//ul[@class="goods-manage-pagination goods-manage-table-pagination goods-manage-table-pagination-right"]/li[8]')[0]
        value_1 = a.get_attribute("title")

        ele = self.driver.find_element(By.XPATH, "//label[contains(text(),'商品标题')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(2)

        self.type("//input[@placeholder=\"请输入关键字查询\"]", "UI自动化发布同款")
        self.click('//span[contains(text(),"筛 选")]')
        sleep(2)

        self.driver.find_elements(By.XPATH, '//input[@class="goods-manage-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量恢复")]')
        self.click('//span[contains(text(),"确 定")]')
        sleep(10)
        self.driver.refresh()
        sleep(2)

        ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'10 条/页')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(2)

        b = self.driver.find_elements(By.XPATH,'//ul[@class="goods-manage-pagination goods-manage-table-pagination goods-manage-table-pagination-right"]/li[8]')[0]
        value_2 = b.get_attribute("title")

        value_3=int(value_1)-int(value_2)
        assert value_3==1
        sleep(5)

    @skip("定位不到元素，再观察下")
    def test_batch_size_chart(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        #批量设置尺码表，全部成功
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置尺码表")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(1)
        self.find_elements(By.XPATH,'//span[@class="goods-list-v1-select-arrow"]')[2].click()
        sleep(3)
        self.find_elements(By.XPATH, '//div[@class="goods-list-v1-select-item-option-content"]')[0].click()
        self.assert_text('10个商品批量设置尺码表成功！', "//span[contains(text(),'10个商品批量设置尺码表成功！')]")

        #批量设置尺码表，全部失败
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置尺码表")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(5)
        self.find_elements(By.XPATH,'//span[@class="goods-list-v1-select-arrow"]')[2].click()
        sleep(5)
        self.find_elements(By.XPATH, '//div[@class="goods-list-v1-select-item-option-content"]')[-1].click()
        sleep(1)
        self.assert_text('已成功关联0个商品，10个商品关联失败', "//div[contains(text(),'已成功关联0个商品，10个商品关联失败')]")
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"确 定")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.click('//span[contains(text(),"确 定")]')

    @pytest.mark.p1
    def test_batch_presale(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        #设置预售
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置发货时间")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(3)
        self.click('//span[contains(text(),"全部预售")]')
        sleep(1)
        #预售时间下限
        # 弹窗受发货率影响，有则点击
        if len(self.find_elements('//span[contains(text(),"继续设置预售")]')) > 0:
            self.click('//span[contains(text(),"继续设置预售")]')
        self.type("//input[@placeholder=\"3-15\"]", "2")
        self.assert_text('发货时间为3-15天', "//div[contains(text(),'发货时间为3-15天')]")
        sleep(1)
        self.double_click("//input[@placeholder=\"3-15\"]")
        sleep(1)
        # 预售时间上限
        self.type("//input[@placeholder=\"3-15\"]", "16")
        self.assert_text('发货时间为3-15天', "//div[contains(text(),'发货时间为3-15天')]")
        sleep(1)
        self.double_click("//input[@placeholder=\"3-15\"]")
        sleep(1)
        # 预售时间正常
        self.type("//input[@placeholder=\"3-15\"]", "10")
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('10个商品批量设置发货时间成功！', "//span[contains(text(),'10个商品批量设置发货时间成功！')]")

        #设置现货
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置发货时间")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(3)
        # self.click('//span[contains(text(),"全部预售")]')
        # # sleep(1)
        # self.click('//span[contains(text(),"设置现货")]')
        self.click('//span[contains(text(),"全部现货")]')
        sleep(1)
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('10个商品批量设置发货时间成功！', "//span[contains(text(),'10个商品批量设置发货时间成功！')]")

    @pytest.mark.p1
    def test_batch_stockremind(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        #设置到货提醒
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置到货提醒")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(3)
        #判断下是否为开启状态，如果为开启可能是上次联动失败，先关闭再执行开启流程
        if self.is_element_present('//span[contains(text(),"设置发消息的最低库存门槛")]'):
            self.click('//button[@class="goods-list-v1-switch goods-list-v1-switch-checked"]')
            sleep(2)
            self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()
            sleep(2)
            self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[0].click()
            sleep(2)
            self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
            self.click('//span[contains(text(),"批量操作")]')
            self.click('//div[contains(text(),"批量设置到货提醒")]')
            self.assert_text('已选择', "//div[contains(text(),'已选择')]")
            self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
            sleep(3)
            self.click('//button[@class="goods-list-v1-switch"]')
            self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-input-number-input"]')[-1].send_keys(
                "10")
            self.click('//span[contains(text(),"确 定")]')
            self.assert_text('10个商品批量设置到货提醒成功！', "//span[contains(text(),'10个商品批量设置到货提醒成功！')]")
        else:
            self.click('//button[@class="goods-list-v1-switch"]')
            self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-input-number-input"]')[-1].send_keys("10")
            self.click('//span[contains(text(),"确 定")]')
            self.assert_text('10个商品批量设置到货提醒成功！', "//span[contains(text(),'10个商品批量设置到货提醒成功！')]")
        #关闭到货提醒
        sleep(3)
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置到货提醒")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(3)
        self.click('//button[@class="goods-list-v1-switch goods-list-v1-switch-checked"]')
        self.assert_text('确定关闭商品到货提醒吗？', "//div[contains(text(),'确定关闭商品到货提醒吗？')]")
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[0].click()
        sleep(3)
        self.assert_text('10个商品批量设置到货提醒成功！', "//span[contains(text(),'10个商品批量设置到货提醒成功！')]")

    @skip("远程调试")
    def test_batch_express(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(2)
        self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化新增商品")
        self.click('//span[contains(text(),"查 询")]')
        sleep(2)

        #设置运费模版-第一次
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置运费模板")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(3)
        self.click('//span[contains(text(),"新增测试运费模版")]')
        sleep(1)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-list-v1-select-item-option-content"]')[0].click()
        sleep(1)
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('10个商品批量设置运费模板成功！', "//span[contains(text(),'10个商品批量设置运费模板成功！')]")

        #设置运费模版-第二次
        sleep(1)
        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量设置运费模板")]')
        self.assert_text('已选择', "//div[contains(text(),'已选择')]")
        self.assert_text('10个商品', "//span[contains(text(),'10个商品')]")
        sleep(3)
        self.click('//span[contains(text(),"默认全国（非港澳台）包邮模板")]')
        sleep(1)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-list-v1-select-item-option-content"]')[1].click()
        self.click('//span[contains(text(),"确 定")]')
        self.assert_text('10个商品批量设置运费模板成功！', "//span[contains(text(),'10个商品批量设置运费模板成功！')]")

    @pytest.mark.p2
    # @skip("商品待更换")
    def test_batch_testProduct(self):
        """
        批量测品	正常弹窗
        """
        self.login("PRODUCT_DOMAIN", "product_account_2")
        good_id = UI_ITEMS_ID['listPage_testProduct']
        self.query_good_by_id(good_id)
        self.logger.info("查询到商品 {}".format(good_id))

        self.driver.find_elements(By.XPATH, '//input[@class="goods-list-v1-checkbox-input"]')[0].click()
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量测品")]')
        sleep(3)
        self.assert_element('//div[contains(text(),"提交测品方案")]')

        self.click("//div[@class='goods-list-v1-select goods-list-v1-select-single goods-list-v1-select-allow-clear goods-list-v1-select-show-arrow']")
        self.click("//div[contains(text(),'广告账户ID：********-磁力金牛高级渠道户')]")

        self.click("//span[contains(text(),'取 消')]")

    @pytest.mark.p2
    def test_batch_changeProductInfo(self):
        """
        批量修改商品信息	正常弹窗
        """
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)

        # 检查弹窗、链接
        self.click('//span[contains(text(),"批量操作")]')
        self.click('//div[contains(text(),"批量修改商品信息")]')
        self.assert_element('//span[contains(text(),"请先在商品列表导出需要修改的商品，修改完成后上传文件")]')

        # 导出商品-标题、价格、库存
        self.click("//span[contains(text(),'标题')]")
        self.click("//button[@class='goods-list-v1-btn goods-list-v1-btn-secondary goods-list-v1-btn-sm']//span[contains(text(),'导出商品')]")
        self.assert_element("//span[contains(text(),'导出成功')]")
        sleep(3)

        self.click("//span[contains(text(),'价格')]")
        self.click(
            "//button[@class='goods-list-v1-btn goods-list-v1-btn-secondary goods-list-v1-btn-sm']//span[contains(text(),'导出商品')]")
        self.assert_element("//span[contains(text(),'导出成功')]")
        sleep(3)

        self.click("//label[@class='goods-list-v1-radio-wrapper']//span[contains(text(),'库存')]")
        self.click(
            "//button[@class='goods-list-v1-btn goods-list-v1-btn-secondary goods-list-v1-btn-sm']//span[contains(text(),'导出商品')]")
        self.assert_element("//span[contains(text(),'导出成功')]")
        sleep(3)

        # 导入excel //p[contains(text(),'点击或拖拽文件到此区域上传')]
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/manage_item_batch_update_uiautotest.xlsx')
        sleep(3)
        self.assert_element("//span[contains(text(),'manage_item_batch_update_uiautotest.xlsx')]")

        # 查看操作记录
        self.click("//a[contains(text(),'查看批量操作记录')]")
        sleep(3)
        self.switch_to_window(-1)
        self.assert_element("//div[contains(text(),'批量操作记录')]")
        self.assert_element("//th[contains(text(),'任务状态')]")
        self.assert_element_not_present("//div[contains(text(),'暂无数据')]")

        # 下载导入明细
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'下载导入明细')]")[0].click()
        sleep(3)
















