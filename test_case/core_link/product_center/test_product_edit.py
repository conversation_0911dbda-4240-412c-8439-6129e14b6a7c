#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-21 14:30
 DESC :
 
 """
import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
import random
from time import sleep
from ddt import ddt
from unittest import skip, skipIf

from test_case.core_link.product_center.base_config import UI_ITEMS_ID
# import sys
# sys.path.append("..")
from utils.env_help import get_work_dir
from test_case.core_link.product_center.base import BaseTestCase
from unittest import skip
from selenium.webdriver.common.by import By

class TestSytCustomer(BaseTestCase):


    @skip("111")
    @pytest.mark.p0
    # @pytest.mark.smoke
    def test_edit_product(self): # todo 适配case
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        sleep(5)

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        # 输入固定商品id
        good_id = UI_ITEMS_ID['listPage_query']
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]",good_id)
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)

        #校验功能存在
        self.assert_text('编辑', '//*[text()="编辑"]')
        self.assert_text('免审编辑', '//*[text()="免审编辑"]')
        self.assert_text('发布相似品', '//*[text()="发布相似品"]')
        self.assert_text('下架', '//*[text()="下架"]')
        # self.assert_text('流量推广', '//*[text()="流量推广"]')

        # 普通编辑
        self.click('//span[contains(text(),"发布相似品")]/../../span[1]/button/span') #编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)
        self.assert_no_404_errors()
        self.assert_text('基础信息','//div[contains(text(),"基础信息")]')
        self.assert_text('商品图文','//div[contains(text(),"商品图文")]')
        # self.assert_text('价格库存','//div[contains(text(),"价格库存")]')
        self.assert_text('服务与售后','//div[contains(text(),"服务与售后")]')

        # 停用尺码表
        # size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        # self.driver.execute_script("arguments[0].scrollIntoView();", size)
        # self.click('//span[contains(text(),"停用尺码表")]')
        # sleep(2)
        # self.click('//span[contains(text(),"停 用")]')
        # sleep(2)

        self.click('//span[contains(text(),"提交审核")]')
        sleep(3)
        self.click('//span[contains(text(),"继续发布")]')
        sleep(8)
        # 普通编辑成功页
        self.assert_text('商品编辑成功','//*[text()="商品编辑成功"]')
        self.assert_text('返回商品列表','//*[text()="返回商品列表"]')
        # 普通编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页','//span[contains(text(),"首页")]')

        # 免审编辑，清空备注，列表页清空
        sleep(3)
        self.refresh()
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", good_id)
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)
        self.click('//span[contains(text(),"发布相似品")]/../../span[2]/button/span')

        self.switch_to_window(2)
        sleep(10)
        self.assert_no_404_errors()

        self.assert_text('基础信息','//*[text()="基础信息"]')
        self.assert_text('价格库存', '//*[text()="价格库存"]')
        self.assert_text('服务与售后', '//*[text()="服务与售后"]')
        self.click('//span[contains(text(),"提交审核")]')
        # 免审编辑成功页
        self.assert_text('商品信息更新成功','//*[text()="商品信息更新成功"]')
        self.assert_text('返回商品列表','//*[text()="返回商品列表"]')
        # 免审编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页','//span[contains(text(),"首页")]')

    @skip("远程调试后放开")
    def test_edit_product_goods(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        sleep(5)

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        # 输入固定商品id
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]","**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)

        # 普通编辑-切换为商品模式
        self.click('//span[contains(text(),"发布相似品")]/../../../span[1]/button/span') #编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)
        self.assert_no_404_errors()

        # 停用尺码表
        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)

        #校验货品模式
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"全部预售")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        self.assert_text('前往调整货品库存', "//span[contains(text(),'前往调整货品库存')]")

        #切换为商品模式
        self.click('//div[contains(text(),"商品模式")]')
        sleep(1)
        self.click('//span[contains(text(),"确 认")]')
        sleep(1)
        self.type("//input[@placeholder=\"最多9999999\"]", "100")
        sleep(5)
        self.click('//span[contains(text(),"提交审核")]')
        sleep(5)
        self.click('//span[contains(text(),"继续提交")]')
        sleep(5)

        # 普通编辑成功页
        self.assert_text('商品编辑成功', '//*[text()="商品编辑成功"]')
        self.assert_text('返回商品列表', '//*[text()="返回商品列表"]')
        # 普通编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')

        # 普通编辑-切换为货品
        # 输入固定商品id
        sleep(5)
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)
        self.click('//span[contains(text(),"发布相似品")]/../../../span[1]/button/span')  # 编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)
        self.assert_no_404_errors()

        # 停用尺码表
        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)

        # 校验商品模式
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"全部预售")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        self.assert_not_equal('前往调整货品库存', "//span[contains(text(),'前往调整货品库存')]")

        # 切换为货品模式
        self.click('//div[contains(text(),"货品模式")]')
        sleep(1)
        self.click('//span[contains(text(),"确 认")]')
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'关联货品')]")[0].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, '//input[@class="goods-radio-input"]')[9].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'确 定')]")[1].click()
        sleep(3)

        self.click('//span[contains(text(),"提交审核")]')
        sleep(5)
        self.click('//span[contains(text(),"继续提交")]')
        sleep(10)
        # 普通编辑成功页
        self.assert_text('商品编辑成功', '//*[text()="商品编辑成功"]')
        self.assert_text('返回商品列表', '//*[text()="返回商品列表"]')
        # 普通编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')





