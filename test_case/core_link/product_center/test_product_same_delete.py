import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
from test_case.core_link.supply_chain.base import BaseTestCase
import random
from time import sleep
from ddt import ddt
from unittest import skip, skipIf
from utils.env_help import get_work_dir
from seleniumbase import BaseCase
from test_case.core_link.product_center.base import BaseTestCase
from selenium.webdriver.common.by import By


class Test_bubtton(BaseTestCase):

    @skip("111")
    @pytest.mark.p0
    # @pytest.mark.smoke
    def test_product_button(self): #todo 适配case
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        # 输入固定商品id
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)

        #发布同款
        self.click('//span[contains(text(),"发布相似品")]')
        self.click('//span[contains(text(),"继续发布相似品")]')

        self.switch_to_window(1)
        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]","UI自动化发布同款")

        # 停用尺码表
        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)

        distribute = self.driver.find_elements(By.XPATH, '//span[contains(text(),"分销推广")]')[-1]
        self.driver.execute_script("arguments[0].scrollIntoView();", distribute)
        self.type("//input[@placeholder=\"请输入1-50的整数\"]", "10")
        sleep(2)

        self.click('//span[contains(text(),"提交审核")]')
        sleep(3)
        self.click('//span[contains(text(),"继续发布")]')
        sleep(8)
        self.click('//span[contains(text(),"返回列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')

        # #搜索商品名称，删除商品
        # sleep(3)
        # self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        # self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化发布同款")
        # self.click('//span[contains(text(),"查 询")]')
        # self.driver.maximize_window()
        # sleep(2)
        # # 下架
        # sleep(1)
        # self.click('//span[contains(text(),"发布相似品")]/../../button/span')  # 下架按钮
        # sleep(1)
        # self.click('//span[contains(text(),"确 定")]')
        # sleep(1)
        # self.assert_text('下架成功！', '//*[text()="下架成功！"]')
        #
        # # 在已下架tab中搜索，上架
        # sleep(2)
        # self.click('//span[contains(text(),"已下架")]')
        # sleep(2)
        # self.type("//input[@placeholder=\"请输入商品标题\"]", "UI自动化发布同款")
        # self.click('//span[contains(text(),"查 询")]')
        # self.click('//span[contains(text(),"发布相似品")]/../../button[3]/span')
        # self.click('//span[contains(text(),"确 定")]')
        # self.assert_text('删除成功！', '//*[text()="删除成功！"]')
        # #sleep(10)

