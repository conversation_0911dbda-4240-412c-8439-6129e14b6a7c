#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-08 11:44
 DESC :

 """
from sqlite3.dbapi2 import paramstyle

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain

import time
import os
import time
import logging
from time import sleep
import requests
import base64
from io import StringIO
import hashlib

from selenium.common import *
from selenium.webdriver.chrome import webdriver

# from common.Goods.goodsBase import BaseTestCase as BaseCase
from seleniumbase import BaseCase
from seleniumwire import webdriver
# from seleniumbase import get_driver
from seleniumbase.fixtures import page_actions

from constant.account import get_account_info
from constant.domain import get_domain
from page_objects.trade_order.trade_order_page import TradeOrderPage
from selenium.webdriver.common.by import By

from utils.env_help import get_env
from utils.apiUtils import ApiTools, api_login

# from seleniumwire import webdriver

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        self.logger = logger
        self.api_tool = ApiTools()
        self.live_stream_id = 0

        account_data = get_account_info(account)
        self.account = account
        self.account_data = account_data
        # 用户名 account_data['account']
        # 密码 account_data['password']

        host = get_domain(domain)
        host = self.change_headers()
        self.logger.info("current host url {}".format(host))

        self.open(host)
        self.driver.maximize_window()
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
        # self.__handle_login_again(account_data)

        sleep(8)
        self.__set_localStorage()

        self.__handle_after_login()

    def login_with_interceptor(self, domain, account):
        self.logger = logger

        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']

        host = get_domain(domain)
        self.logger.info("current host url {}".format(host))

        self.change_headers()

        self.open(host)
        self.driver.maximize_window()
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
        self.click('//*[@id="username"]')
        self.type('//*[@id="username"]', account_data['account'])
        self.click('//*[@id="password"]')
        self.type('//*[@id="password"]', account_data['password'])
        self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
        self.sleep(3)
        # self.__handle_login_again(account_data)

        self.__set_localStorage()

        self.__handle_after_login()

    def __handle_login_again(self, account_data):
        if self.is_text_visible("密码登录"):
            print("log：再次登录开始")
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            self.click('//*[@id="username"]')
            self.type('//*[@id="username"]', account_data['account'])
            self.click('//*[@id="password"]')
            self.type('//*[@id="password"]', account_data['password'])
            self.click(
                '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
            self.sleep(3)
            print("log：再次登录结束")
        if self.is_text_visible('返回旧版', '//*[@id="NoviceStageReturnPc#C1"]/div/div/div'):
            self.click('//*[@id="NoviceStageReturnPc#C1"]/div/div/div')
            self.sleep(2)
            self.click('//*[@class="kpro-seller-v2-change-stage-form-item-control-input-content"]/div/div[1]')
            self.sleep(2)
            self.click('//*[@class="kpro-seller-v2-change-stage-btn"]/span')
            self.sleep(8)
        if self.is_text_visible("密码登录"):
            print("log：再次登录开始")
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            self.click('//*[@id="username"]')
            self.type('//*[@id="username"]', account_data['account'])
            self.click('//*[@id="password"]')
            self.type('//*[@id="password"]', account_data['password'])
            self.click(
                '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
            self.sleep(3)
            print("log：再次登录结束")

    def __handle_after_login(self):
        # 新手引导点击
        sleep(5)
        for i in range(3):
            sleep(1)
            if self.is_element_visible("//div[@class='kpro-modal-picture-pic-ver']"):
                self.logger.info("close 邀请开店弹窗")
                self.click("//span[@class='anticon anticon-system-error-circle-line']")
            if self.is_element_visible("//span[contains(text(),'查看更多商品')]"):
                self.logger.info("close 一键降价")
                self.click(
                    "svg[clip-rule='evenodd'][fill-rule='evenodd'][viewBox='0 0 24 24'][focusable='false'][data-icon='system-close-large-line']")
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.sleep(1)
                self.click('//*[@id="driver-page-overlay"]')
                self.sleep(3)
            if self.is_element_visible("//div[contains(text(),'商品出单秘籍')]"):
                self.logger.info("close 商品出单秘籍")
                self.click("//span[@class='goods-list-v1-modal-close-x']")
            if self.is_element_visible("//span[contains(text(),'查看扶持效果')]"):
                self.logger.info("close 查看扶持效果")
                self.click("//span[@class='anticon anticon-system-close-large-line']")
            if self.is_element_visible("//img[@class='closeIcon___TZ6G7']"):
                self.logger.info("close 为你推荐")
                self.click("//img[@class='closeIcon___TZ6G7']")
            if self.is_element_visible("//span[contains(text(),'您有多大程度满意我们的产品')]"):
                self.logger.info("close 您有多大程度满意我们的产品")
                self.click(
                    '#main-pc-atmosphere-layout > div.seller-main-card.rate-dialog > div > div > div.rate-dialog-header > span.rate-dialog-close > span > svg > path')
            if self.is_element_visible("//button[contains(text(),'返回旧版')]"):
                self.logger.info("close 工作台改版")
                self.click("//button[contains(text(),'返回旧版')]")
                self.click("//div[contains(text(),'习惯了以前的布局')]")
                sleep(1)
                self.click("//span[contains(text(),'确定返回旧版')]")
                sleep(3)

        # 处理点击商品列表出现的弹窗
        # self.assert_title("快手小店")
        # self.click("//span[contains(text(),'商品')]")
        # self.click("//span[contains(text(),'商品列表')]")
        # if self.is_element_visible("//span[contains(text(),'继续拿流量')]"):
        #     self.logger.info("close 继续拿流量")
        #     self.click("//*[name()='path' and contains(@d,'M2.56 18.7')]")
        #     sleep(1)

    def __set_localStorage(self):
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-guanligongju-2024-1-31_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-pingtaikefu-jiedaiyouhua-old-sider_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_gouwutiyanxingjiv1_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_fuwushichang-caidanqianyi2_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dianpuxinxishezhi-eduguanli_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_xiaodiankefupingtai-lixiangongneng_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dingdanchaxun-yinsijiami_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_tools-xiaoer-message_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_daifaguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-cs-center_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dianpu-dianputiyanfen-2023-11-16_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_111111112_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_baozhang-yinsizhongxin-2023-08-15_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-kefupingtai-2023-08-16_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo-2023-08-16_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_baozhang-jubaozhongxin-2023-10-13_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-pingtaikefu-2023-10-13_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dianpu-daihuokoubei-2023-11-16_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-fenliufangshi-2023-12-18_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-kefupingtai-2023-12-28_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-zhinengkefu-2023-12-29_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-guanligongju-2024-1-31_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-2024-1-31_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_23_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_23_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_24_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_25_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_26_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_24_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_25_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_26_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_27_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_27_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_28_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_28_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_29_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_29_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_37_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_33_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_37_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_33_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_39_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_39_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_41_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_41_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_42_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_42_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_43_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_43_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_44_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_44_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_45_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_45_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_46_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_46_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_47_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_47_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_48_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_48_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_image-upload-guide_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_compositeImage-upload-guide_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_config-video_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('MATERIAL_CENTER_PRODUCT_SUPPLY','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_config-video_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_image-upload-guide_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_compositeImage-upload-guide_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_goods-decorated-detail_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_goods-decorated-detail_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_72_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_73_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_71_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_78_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_79_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-goods-list-v1-pc_config_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_82_11318492','1');")

        # self.refresh()
        # sleep(3)

    def SDGpage_set_localStorage(self):
        # 闪电购
        self.execute_script(
            "window.localStorage.setItem('workbench-badge-showed-menus','{}');")
        self.execute_script(
            "window.localStorage.setItem('alg','test');")
        self.execute_script(
            "window.localStorage.setItem('X-Entry-Src','ENTRANCE_XIAODIAN_LIVE_HELPER');")
        self.execute_script(
            "window.localStorage.setItem('LiveDecoration-helper','1');")
        self.execute_script(
            "window.localStorage.setItem('guid-ueDataSteps','1');")
        self.execute_script(
            "window.localStorage.setItem('LiveDecoration','1');")
        self.refresh()

    def change_headers(self):
        if self.var1 and self.var1 == 'prt':
            self.logger.info("current env prt with PRT.test")
            self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}  # 可修改为任意泳道
            return "https://eshop-login.prt.kwaixiaodian.com/?biz=zone&redirect_url=https%3A%2F%2Feshop-s.prt.kwaixiaodian.com%2Fzone%2Fgoods%2Fv1%2Flist"
        else:
            current_env = get_env()
            if current_env == 'online':
                self.logger.info("current env online")
                return "https://login.kwaixiaodian.com/?biz=zone&redirect_url=https%3A%2F%2Fs.kwaixiaodian.com%2Fzone%2Fgoods%2Flist"
            # elif current_env == 'prt':
            #     self.logger.info("current env prt with PRT.test")
            #     self.driver.header_overrides = {"trace-context": '{"laneId":"PRT.test"}'}  # 可修改为任意泳道
            #     return "https://eshop-login.prt.kwaixiaodian.com/?biz=zone&redirect_url=https%3A%2F%2Feshop-s.prt.kwaixiaodian.com%2Fzone%2Fgoods%2Fv1%2Flist"

    def login_old(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']

        host = get_domain(domain)

        self.open(host)

        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.assert_text("扫码登录", "//*[text()='扫码登录']")  # div标签中的第一个元素
        self.assert_text("手机号登录", "//*[text()='手机号登录']")
        self.click("//*[text()='手机号登录']")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        time.sleep(5)
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-guanligongju-2024-1-31_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-pingtaikefu-jiedaiyouhua-old-sider_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_gouwutiyanxingjiv1_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_fuwushichang-caidanqianyi2_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dianpuxinxishezhi-eduguanli_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_xiaodiankefupingtai-lixiangongneng_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dingdanchaxun-yinsijiami_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_tools-xiaoer-message_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_daifaguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-cs-center_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dianpu-dianputiyanfen-2023-11-16_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_111111112_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_baozhang-yinsizhongxin-2023-08-15_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kefu-kefupingtai-2023-08-16_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo-2023-08-16_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_baozhang-jubaozhongxin-2023-10-13_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_kefu-pingtaikefu-2023-10-13_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dianpu-daihuokoubei-2023-11-16_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-fenliufangshi-2023-12-18_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-kefupingtai-2023-12-28_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-zhinengkefu-2023-12-29_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-guanligongju-2024-1-31_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kefu-2024-1-31_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_23_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_23_11318492','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_27_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_27_11318492','1');")

        self.refresh()
        self.refresh()
        self.refresh()
        self.refresh()
        time.sleep(2)

    def close_merchant_assistant(self):
        """关闭商家助手"""
        if self.is_element_visible(TradeOrderPage.merchant_assistant):
            merchant_assistant_close_button = self.find_elements(TradeOrderPage.merchant_assistant_close_button)
            if len(merchant_assistant_close_button) > 0:
                merchant_assistant_close_button[-1].click()
            # if len(merchant_assistant_close_button) == 1:
            #     merchant_assistant_close_button[0].click()
            # elif len(merchant_assistant_close_button) == 2:
            #     merchant_assistant_close_button[1].click()

    def choose_category(self,text,text_second='',type=''):
        time.sleep(3)
        if type=="search":
            self.type("//input[@autocomplete='off']",text)
            self.sleep(3)
            self.click("//div[contains(text(),'" + text+ "')]")
            self.sleep(3)
        elif type=="choose":
            category_text = self.get_text("//*[text()='已开通类目']/../div[2]")  # 获取一级类目列表
            category_list = [text]
            for cate in category_list:
                self.assert_in(cate, category_text)
            time.sleep(3)
            # 选择一级类目
            self.click("//span[contains(text(),'" + text+ "')]")
            time.sleep(10)
            # 选择二级类目
            self.click("//span[contains(text(),'" + text_second+ "')]")
            self.sleep(3)

    def add_page(self,text,text_second='',type='', account="product_account"):
        # 上传主图
        self.add_page_uploadImage(account)

        # 选择类目
        self.choose_category(text,text_second,type)

        #下一步
        self.click('//span[contains(text(),"下一步")]')
        self.sleep(3)

        self.product_add_resure()

        # self.click('//button[contains(text(),"知道了")]')
        # self.sleep(2)
        # ele= self.driver.find_element(By.XPATH, '//span[contains(text(),"商品标题")]')
        # self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        # self.sleep(2)

    def product_add_resure(self):
        if self.is_element_visible('//span[contains(text(),"下一步")]') and (not self.is_element_visible('//span[contains(text(),"直接发品")]')):
            self.click('//span[contains(text(),"下一步")]')
            self.sleep(3)

        # 可能出现的弹窗
        if self.is_element_visible('//span[contains(text(),"直接发品")]'):
            self.logger.info("close 已有同品弹窗")
            self.click('//span[contains(text(),"直接发品")]')
            self.sleep(5)

        # 检查是否进入发品页
        self.assert_element('//span[contains(text(),"提交审核")]')

    def add_page_uploadImage(self, account="product_account"):
        """
        仅上传主图、然后聚焦到类目
        """
        self.login("PRODUCT_DOMAIN", account)
        self.assert_title("快手小店")
        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click("//span[contains(text(),'新增商品')]")
        self.driver.maximize_window()
        time.sleep(7)
        self.close_merchant_assistant()
        # 上传主图
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/test_addMainImage2.jpeg')
        sleep(5)
        # self.click("//span[contains(text(),'确认裁剪')]")
        #
        # # 试5次，每次3s，最多15s,裁剪过程时慢时快
        # re_time = 0
        # while (self.is_element_visible("//div[contains(text(),'图片编辑工具')]") and re_time < 5):
        #     sleep(3)
        #     if self.is_element_visible("//span[contains(text(),'应用图片')]"):
        #         self.click("//span[contains(text(),'应用图片')]")
        #     re_time += 1
        # sleep(2)
        ele = self.driver.find_element("class name", "goods-select-selection-search")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)

    def query_good_by_id(self, id, list_type='在售'):
        """
        从小店主页查询商品
        """
        self.assert_title("快手小店")

        # 进入商品列表页, 默认在售
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        if list_type in ['全部', '已下架', '审核中', '审核待修改', '已封禁']:
            self.click("//span[contains(text(),\'{}\')]".format(list_type))
        logger.info("已进入 {} 商品列表".format(list_type))
        sleep(3)

        # 输入固定商品id
        self.type("//input[@placeholder=\"多条之间用中英文逗号或者空格分隔,最多50个\"]", id)
        self.click('//span[contains(text(),"查 询")]')
        sleep(3)

    def query_good_by_name(self, name, list_type='在售'):
        """
        从小店主页查询商品
        """
        self.assert_title("快手小店")

        # 进入商品列表页, 默认在售
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        if list_type in ['全部', '已下架', '审核中', '审核待修改', '已封禁']:
            self.click("//span[contains(text(),\'{}\')]".format(list_type))
        logger.info("已进入 {} 商品列表".format(list_type))
        sleep(3)

        # 输入固定商品id
        self.type("//input[@placeholder=\"请输入商品标题\"]", name)
        self.click('//span[contains(text(),"查 询")]')
        sleep(3)

    def sdg_change_live_status(self, open_it=True):
        """
        data = {'token': '44b226a49dfd4fe38e1f10e7502d3587-1399926167',
                'tokenClientSalt': 'ae70c64dbae2470ee034cabaf44d4b67',
                'userId': 1399926167,
                'isShop': 'true',
                'host': 'http://apissl.gifshow.com',
                'kuaishouApiSt': 'Cg9rdWFpc2hvdS5hcGkuc3QSoAEOmNtRRglqNf8Au6xFn2RNkntiVyFneK7OYXW9toZbXfdd5uCb76rBXupiy8lJJL1i43_1G2JdNZNvdTn8ivslnHUkYChmt8dPqUUuJs8eEbxELx6YA4WCv8g1TJoRtvg48M5pj5hgJwi4xKhctFh3uH6xK_jV8QYB-lP8n-uyWyfqfxKTTYPJNxO7DMj2RGHUgVq0ejxlxg230MGNJYZkGhLZe8NH31JEM6ppJzAS6A8iBVUiIIVqCXxWJmG-EXonFIqVxPsXrsEPr4-uJfbdcbTKYwtCKAUwAQ'}
        url1 = "https://eshop-qa-live-apitest.test.gifshow.com/apiService/startLiveRoom"
        method = 'post'
        params = {'token': '44b226a49dfd4fe38e1f10e7502d3587-1399926167',
                  'tokenClientSalt': 'ae70c64dbae2470ee034cabaf44d4b67',
                  'userId': 1399926167,
                  'isShop': 'true',
                  'host': 'http://apissl.gifshow.com',
                  'kuaishouApiSt': 'Cg9rdWFpc2hvdS5hcGkuc3QSoAEOmNtRRglqNf8Au6xFn2RNkntiVyFneK7OYXW9toZbXfdd5uCb76rBXupiy8lJJL1i43_1G2JdNZNvdTn8ivslnHUkYChmt8dPqUUuJs8eEbxELx6YA4WCv8g1TJoRtvg48M5pj5hgJwi4xKhctFh3uH6xK_jV8QYB-lP8n-uyWyfqfxKTTYPJNxO7DMj2RGHUgVq0ejxlxg230MGNJYZkGhLZe8NH31JEM6ppJzAS6A8iBVUiIIVqCXxWJmG-EXonFIqVxPsXrsEPr4-uJfbdcbTKYwtCKAUwAQ'}
        headers = {'Accept': 'application/json', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36', 'ptp-flag': '1'}

        """
        account_data = self.account_data
        api_token = api_login(account=self.account, password=account_data["password"],
                              account_type=account_data["type"], user_id=account_data["uid"])
        print(str(api_token))
        if open_it:
            url = 'https://eshop-qa-live-apitest.test.gifshow.com/apiService/startLiveRoom'
            sourceType = 98
            params = {
                "token": api_token['token'],
                "tokenClientSalt": api_token['token_client_salt'],
                "userId": api_token['region']['uid'],
                "isShop": "true",
                "host": "http://apissl.gifshow.com",
                'kuaishouApiSt': api_token['kuaishou.api_st']
            }
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36',
                'Accept': 'application/json',
                # 请求头加上压测标识，避免触发waf限流
                'ptp-flag': '1'
            }
            res = requests.get(url, params=params, headers=headers).json()
            self.logger.info(res)
            self.logger.info('完成开播动作')
            self.live_stream_id = res.get('liveStreamId')
        else:
            url = 'https://eshop-qa-live-apitest.test.gifshow.com/apiService/stopLiveRoom'
            params = {
                "token": api_token['token'],
                "tokenClientSalt": api_token['token_client_salt'],
                "userId": api_token['region']['uid'],
                "liveStreamId": self.live_stream_id,
                'kuaishouApiSt': api_token['kuaishou.api_st']
            }
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36',
                'Accept': 'application/json',
                # 请求头加上压测标识，避免触发waf限流
                'ptp-flag': '1'
            }
            res = requests.get(url, params=params, headers=headers).json()
            self.logger.info(res)
            self.logger.info('关闭直播')

