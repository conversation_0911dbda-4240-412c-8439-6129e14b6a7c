import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from unittest import skip, skipIf
from unittest import skip

class TestProp(BaseTestCase):

    @skip("属性重构")
    @pytest.mark.p0
    def test_prop_single(self):
        self.add_page("autotest-类目属性校验", "属性类目", "search")
        # 移动到部分
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_goodsAttrs"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        list = self.driver.find_elements(By.XPATH,'//div[@class="goods-select goods-select-single goods-select-allow-clear goods-select-show-arrow goods-select-show-search"]')
        num = len(list)
        for i in range(0, num):

            self.driver.find_elements(By.XPATH, '//div[@class="goods-select goods-select-single goods-select-allow-clear goods-select-show-arrow goods-select-show-search"]')[i].click()
            sleep(3)
            self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]')[i]
            self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]/div[2]')[i].click()
            print("123",i)

    @skip("属性重构")
    def test_prop_multi(self):
        self.add_page("autotest-类目属性校验", "属性类目", "search")
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_goodsAttrs"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        list_multi=self.driver.find_elements(By.XPATH,'//div[@class="goods-select-selection-overflow-item goods-select-selection-overflow-item-suffix"]')
        num_multi=len(list_multi)
        for i in range(0, num_multi):
            self.driver.find_elements(By.XPATH,'//div[@class="goods-select-selection-overflow-item goods-select-selection-overflow-item-suffix"]')[i].click()
            sleep(3)
            self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]')[i]
            self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]/div[1]')[i].click()
            self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]/div[2]')[i].click()










