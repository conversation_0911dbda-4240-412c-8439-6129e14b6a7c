import pytest
from ddt import ddt, data, unpack
from constant.domain import get_domain
import random
from time import sleep
from selenium.webdriver.common.by import By
from ddt import ddt
from unittest import skip, skipIf

from test_case.core_link.product_center.base_config import UI_ITEMS_ID
from utils.env_help import get_work_dir
from test_case.core_link.product_center.base import BaseTestCase

class Testpriceandstock(BaseTestCase):


    @pytest.mark.p0
    @pytest.mark.smoke
    def test_product_stock(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)

        #价格库存组件
        self.click("span[class='anticon anticon-system-edit-line editButton___YsVYv'] svg")
        sleep(3)

        self.assert_text('到货提醒', '//span[contains(text(),"到货提醒")]')
        self.assert_text('修改库存', '//div[contains(text(),"修改库存")]')
        self.assert_text('现有总库存', '//span[contains(text(),"现有总库存")]')
        self.assert_text('改后总库存', '//span[contains(text(),"改后总库存")]')

        #库存库存上限校验，提示-改后库存
        text_path = '//*[@id="skuTable"]/div/div[2]/table/tbody/tr[2]/td[2]/div/div/div/div/div/div[1]/div/div[2]/div[2]/input'

        self.type(text_path, "********")
        sleep(1)
        self.assert_text('库存最多9999999件','//*[text()="库存最多9999999件"]')

        # 库存少于0，提示
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"减")]')[-1].click()
        sleep(1)
        self.assert_text('请输入大于等于0的整数', '//*[text()="请输入大于等于0的整数"]')

        #正常减少1个库存
        self.type(text_path, "1")
        self.click('//span[contains(text(),"提 交")]')
        self.assert_element('//*[text()="提交编辑成功"]')

        #正常增加1个库存
        sleep(3)
        self.click("span[class='anticon anticon-system-edit-line editButton___YsVYv'] svg")
        sleep(3)
        self.type(text_path, "1")
        self.click('//span[contains(text(),"提 交")]')
        self.assert_element('//*[text()="提交编辑成功"]')

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_product_price(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        good_id = UI_ITEMS_ID['listPage_query']
        self.query_good_by_id(good_id)

        #价格组件
        self.click("span[class='anticon anticon-system-edit-line editButton___G50tz'] svg")
        sleep(3)

        self.assert_text('价格（元）', '//span[contains(text(),"价格（元）")]')
        self.assert_text('SKUID', '//div[contains(text(),"SKUID")]')
        self.assert_text('SKU编码', '//span[contains(text(),"SKU编码")]')
        self.assert_text('预览图', '//div[contains(text(),"预览图")]')
        # self.assert_text('到货提醒', '//span[contains(text(),"到货提醒")]')
        self.assert_text('修改价格', '//div[contains(text(),"修改价格")]')

        # 价格上限校验，提示
        # 价格上限校验，提示
        text_path = "//*[@id='skuTable']/div/div[2]/table/tbody/tr[2]/td[1]/div/div/div[1]/div/div/div/div/div[2]/input"
        self.type(text_path, "9999999")
        sleep(1)
        self.assert_text('单价最高 9999.00 元', '//*[text()="单价最高 9999.00 元"]')

        # 价格下限校验，提示
        self.type(text_path, "0")
        sleep(1)
        self.assert_text('单价最低 0.01 元', '//*[text()="单价最低 0.01 元"]')

        # 价格改为1，提示
        self.type(text_path, "1")
        self.click('//span[contains(text(),"提 交")]')
        sleep(1)
        self.assert_element('//*[text()="提交编辑成功"]')








