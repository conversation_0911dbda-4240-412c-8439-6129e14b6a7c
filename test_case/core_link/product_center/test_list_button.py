import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from unittest import skip
from selenium.webdriver.common.keys import Keys

class TestSytCustomer(BaseTestCase):

    @skip("******** 托管商品功能下线")
    def test_managed_link(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        sleep(10)
        self.click('//span[contains(text(),"托管商品")]')
        sleep(10)
        self.assert_no_404_errors()

    @skip("******** 加入托管功能下线")
    def test_join_managed_link(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        sleep(10)
        self.click('//span[contains(text(),"加入托管")]')
        self.click('//span[contains(text(),"立即参与")]')
        sleep(10)
        self.assert_no_404_errors()

    @pytest.mark.p2
    def test_more_flow_link(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        # sleep(10)
        # self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]

        # 功能下线
        # sleep(3)
        # self.click('//span[contains(text(),"更多流量工具")]')
        # sleep(5)
        # self.assert_element("//span[contains(text(),'商城活动')]")
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[@class="anticon anticon-system-close-medium-line goods-list-v1-modal-close-icon"]')
        self.assert_element("//span[contains(text(),'查看重复品')]")

    @pytest.mark.p2
    def test_official_activity_link(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        # sleep(10)
        # self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]

        # 商城活动更换入口，到诊断，待适配
        # sleep(3)
        # self.click('//span[contains(text(),"商城活动")]')
        # sleep(1)
        # self.driver.find_elements(By.XPATH, '//span[contains(text(),"立即报名")]')[-1].click()
        # sleep(3)
        # handles = self.driver.window_handles
        # self.driver.switch_to.window(handles[-1])
        # self.assert_no_404_errors()
        # self.assert_text("大促活动", '//span[contains(text(),"大促活动")]')

    @pytest.mark.p1
    def test_export_list(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        # sleep(10)
        # self.driver.find_elements(By.XPATH,'//span[contains(text(),"在售")]')[1]
        sleep(3)
        self.execute_script(
            'document.getElementById("kpro-tool-box-tools-containner").style="bottom: 95px; right: 4px; z-index: 99; transform: translate(0px, -101px);"')
        sleep(3)

        self.click('//span[contains(text(),"导出记录")]')
        sleep(5)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.assert_text("已导出的商品记录", '//div[contains(text(),"已导出的商品记录")]')










