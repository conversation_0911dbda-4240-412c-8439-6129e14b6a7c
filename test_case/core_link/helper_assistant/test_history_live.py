"""
# Author     ：author yuanpan
# Description：
"""
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

@ddt
class TestHelperAssistant(BaseTestCase):

    # 直播-登录到我的直播页面
    @pytest.mark.skip
    def test_checkout_my_live_list(self, selector=None):
        self.login("LIVE_ASSISTANT_DOMAIN", "helper_assistant")
        # 通过我的直播页面进入复盘诊断页面
        self.goto_live_diagnose_from_mylive_list()
        # 通过我的直播页面进入历史跟播助手页面
        self.go_helper_assistant_history()
        sleep(2)
        # 刷新页面
        self.refresh_page()
        # 跳过新手引导
        self.skip_guider()
        # 跳转直播大屏
        self.goto_live_broadcast()
        # 打开复盘诊断
        self.goto_live_diagnose()
        # 跳转商祥
        self.goto_item_details()

    # 通过我的直播进入历史跟播助手页面
    def go_helper_assistant_history(self):
        sleep(1)
        self.click('div.ant-table-content>table>tbody>tr:nth-child(1)>td:nth-child(4)>button')
        # self.assert_text('历史售卖商品','//*[@id="root"]/section/section/main/div/div/div[3]/div/div/div/div/div')
        sleep(1)

    #通过我的直播进入直播诊断页面
    def goto_live_diagnose_from_mylive_list(self):
        sleep(1)
        self.click('div.ant-table-content>table>tbody>tr:nth-child(1)>td:nth-child(4)>button:nth-child(2)')
        sleep(2)
        self.switch_to_window(1)
        sleep(1)
        live_broadcase_url = self.get_current_url()
        # 回到我的直播页面
        self.switch_to_window(0)
        sleep(2)

    # 跳过新手引导
    def skip_guider(self):
        # 第一步
        sleep(1)
        self.click('div.guide-container--1mm6k>div:nth-child(2)>button:nth-child(3)')
        # 第二步
        sleep(1)
        self.click('div.guide-container--1mm6k>div:nth-child(2)>button:nth-child(3)')
        # 第三步
        sleep(1)
        self.click('div.guide-container--1mm6k>div:nth-child(2)>button:nth-child(3)')
        # 第四步
        sleep(1)
        self.click('div.guide-container--1mm6k>div:nth-child(2)>button:nth-child(3)')
        # 第五步
        sleep(1)
        self.click('div.guide-container--1mm6k>div:nth-child(2)>button:nth-child(3)')
        # 开始体验
        sleep(1)
        self.click('div.guide-container--1mm6k>div:nth-child(2)>button:nth-child(3)')

    # 跳转到直播大屏,并切回到跟播助手页面
    def goto_live_broadcast(self):
        sleep(1)
        self.click('//*[@id="root"]/section/section/main/div/div/div[4]/div/div/div/div[2]/div/div/div')
        sleep(2)
        self.switch_to_window(1)
        sleep(1)
        # self.assert_text('快手小店直播期间累计成交金额','//*[@id="root"]/section/div/div/div/div/div/div[8]/div/div/div/div/div/span[2]')
        live_broadcase_url = self.get_current_url()
        # 回到我的直播页面
        self.switch_to_window(0)
        sleep(2)

    # 跳转到复盘诊断，并切回到跟播助手页面
    def goto_live_diagnose(self):
        sleep(1)
        self.click('//*[@id="root"]/section/section/main/div/div/div/div[2]/button')
        self.switch_to_window(1)
        sleep(2)
        # 回到我的直播页面
        self.switch_to_window(0)
        sleep(2)

    # 点击商品主图跳转商祥
    def goto_item_details(self):
        sleep(1)
        self.click('//*[@id="root"]/section/section/main/div/div/div[3]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div[2]/div/div')
        self.switch_to_window(1)
        sleep(2)
        # 回到我的直播页面
        self.switch_to_window(0)
        sleep(2)
