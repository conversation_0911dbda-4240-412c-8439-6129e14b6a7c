from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):


    def login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.sleep(0.5)
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(0.5)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.sleep(0.5)
        self.click("//*[@id='root']/div/div[2]/div/div/div/div[4]/form/div[3]/button")