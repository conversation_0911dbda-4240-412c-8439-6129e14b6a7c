import pytest
from time import sleep
from unittest import skip

import time
from datetime import (datetime, timedelta)

from test_case.core_link.supply_chain.facesheet_case.Handwork_case import *
from utils.distribution_untils import *


@ddt
class TestHandwork(Handwork):

    def orderurl(self):
        self.handwork()

    @pytest.mark.p0
    def test_order_dyi(self):
        self.orderurl()
        self.sleep(0.5)
        self.assert_text('手工打单配置指导', '//*[@id="root"]/div/div/div[1]/div/div[2]/h2')

        # 时间筛选
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(0.5)
        # self.click('/html/body/div[4]/div/div/div/div[2]/div[2]/ul/li[1]/span')

        # 异常单号
        self.type('//*[@id="externalOrderCode"]', '@')
        self.sleep(0.5)
        self.assert_text('订单编号为纯数字，最多19字符',
                         '//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div[2]/div')

        # 重置
        self.click("//*[contains(text(),'重 置')]")
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[5]/div/div/div[1]/button/span')
        sleep(1)

        # 展开更多
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[2]')
        # 手机号
        self.type('#receiverMobile', '1')
        self.sleep(0.5)

        # 查询
        self.click("//*[contains(text(),'查 询')]")
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[5]/div/div/div[2]/button/span')
        self.sleep(0.5)
        self.assert_text('暂无数据',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]')

        # 非法手机号
        self.type('#receiverMobile', '@')
        self.assert_text('请输入正确的收货手机号',
                         '//*[@id="pro-form-wrapper"]/div[3]/div[2]/div/div[2]/div[2]/div')

    # 面板固定组件
    @pytest.mark.p0
    def test_pranel(self):
        self.orderurl()
        self.sleep(0.5)
        self.maximize_window()
        self.assert_text('手工单号',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[2]')
        self.assert_text('订单编号',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[3]')
        self.assert_text('商品信息',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[4]')
        self.assert_text('收货信息',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[5]')
        self.assert_text('商品数量',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[6]')
        self.assert_text('商家备注',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[7]')
        self.assert_text('创建时间',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[8]')
        self.assert_text('操作',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/div[1]/table/thead/tr/th[9]')

    # 批量录入
    @pytest.mark.p0
    def test_batch_entry(self):
        self.orderurl()
        self.sleep(0.5)
        self.maximize_window()
        self.assert_text('批量录入', '//*[@id="root"]/div/div/div[2]/div[2]/div/div[1]/div[1]/div[1]/div[1]/button')
        self.click('//*[@id="root"]/div/div/div[2]/div[2]/div/div[1]/div[1]/div[1]/div[1]/button')
        self.assert_text('标准手工订单(商家自建订单)',
                         '//*[@id="root"]/div/div/div[2]/div[2]/div/div[1]/div[1]/div[1]/div[1]/div/div/div/ul/li[1]')
        self.click('//*[@id="root"]/div/div/div[2]/div[2]/div/div[1]/div[1]/div[1]/div[1]/div/div/div/ul/li[1]')
        self.assert_text('下载模板',
                         "//button[contains(@class,'kwaishop-agent-pc-micro-btn kpro-importfile-item-btn')]")
        self.click("//button[contains(@class,'kwaishop-agent-pc-micro-btn kpro-importfile-item-btn')]")
        self.assert_text('上传文件',
                         "//button[contains(@class,'kwaishop-agent-pc-micro-btn kwaishop-agent-pc-micro-btn-primary kpro-importfile-item-btn kpro-importfile-item-btn-primary')]")
        self.click("//button[@aria-label='Close']")
        self.assert_text('打印面单', '//*[@id="root"]/div/div/div[3]/button[1]')
        self.assert_text('批量修改', '//*[@id="root"]/div/div/div[3]/button[2]')

    # 已打印-取号时间选择
    @pytest.mark.p0
    def test_printed(self):
        self.orderurl()
        self.sleep(0.5)
        self.maximize_window()
        self.click("//*[contains(text(),'已打印')]")
        # 取号时间选择
        self.click("(//input[@placeholder='结束时间'])[2]")
        self.sleep(1)
        # 近30天
        self.click("//span[contains(text(),'近30天')]")
        now_time = datetime.now()
        one_month_ago = now_time - timedelta(days=30)
        end_formatted_time = now_time.strftime("%Y-%m-%d 23:59:59")
        begin_formatted_time = one_month_ago.strftime("%Y-%m-%d 00:00:00")
        self.assert_text(begin_formatted_time, "(//input[@placeholder='开始时间'])[2]")
        self.assert_text(end_formatted_time, "(//input[@placeholder='结束时间'])[2]")
        # 提交按钮
        self.click("(//button[@type='submit'])[2]")
        # 重置按钮
        self.click("(//button[@type='button'])[8]")

    # 已打印-面板
    @pytest.mark.p0
    def test_printed(self):
        self.orderurl()
        self.sleep(0.5)
        self.maximize_window()
        self.click("//*[contains(text(),'已打印')]")
        self.assert_text('订单编号', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[12]")
        self.assert_text('商品名称', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[13]")
        self.assert_text('商品ID', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[14]")
        self.assert_text('商品规格', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[15]")
        self.assert_text('手工单号', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[16]")
        self.click(
            "/html/body/div[1]/section/section/main/div/div/div/div/div/div/div[2]/div[1]/div/div/div[2]/div/div[2]/div/form/div[2]/div[4]/div/div[2]")
        self.assert_text('物流公司', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[17]")
        self.click("(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[17]")
        self.click("(//div[@class='kwaishop-agent-pc-micro-select-selector'])[3]")
        self.assert_text('物流运单', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[18]")
        self.assert_text('目的区域', "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[19]")
        self.click("(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[19]")
        self.assert_text('收货手机号',
                         "(//div[@class='kwaishop-agent-pc-micro-col kwaishop-agent-pc-micro-col-8'])[20]")
        # 提交按钮
        self.click("(//button[@type='submit'])[2]")
        # 重置按钮
        self.click("(//button[@type='button'])[8]")

        # 已打印-页面

    @pytest.mark.p0
    def test_printed(self):
        self.orderurl()
        self.maximize_window()
        self.click("//*[contains(text(),'已打印')]")
        self.assert_text('导出明细', "(//button[@type='button'])[9]")
        self.assert_text('补打面单', "(//span[contains(text(),'补打面单')])[1]")
        self.assert_text('回收运单', "(//button[@type='button'])[11]")

    @pytest.mark.p0
    def test_order_xer(self):
        self.orderurl()
        self.sleep(0.5)

        # 手动录入订单
        self.click('//*[@id="root"]/div/div/div[2]/div[2]/div/div[1]/div[1]/div[1]/div[2]/button/span')
        self.sleep(0.5)
        self.click('//form[1]/div[1]/button/span')  # 空单号
        self.sleep(0.5)
        # self.assert_text('请输入正确的订单编号哦~', '/html/body/div[3]/div/div/div/div/div/span[2]')
        # sleep(1)
        self.type('//form[1]/div[1]/div/div[2]/div/div/input', '123321')
        self.sleep(0.5)
        self.click('//form[1]/div[1]/button/span')
        self.sleep(0.5)
        # self.assert_text('90328:当前订单为非快手订单！ [90328]', '/html/body/div[3]/div/div/div/div/div/span[2]')

        # 提交
        self.click('//div[2]/div/div/div[3]/div/button[1]')
        self.sleep(0.5)
        self.assert_text('请输入商品名称，最多输入50字符', '//div[2]/form[1]/div[3]/div[2]/div[2]/div')

        # 取消
        self.click("//span[contains(text(),'取 消')]")

    @pytest.mark.p0
    def test_tab_with(self):
        self.orderurl()
        self.sleep(0.5)
        self.click("//*[contains(text(),'待打印')]")
        # self.click('//*[@id="rc-tabs-0-tab-2"]')
        self.sleep(0.5)
        self.click("//*[contains(text(),'已打印')]")
        # self.click('//*[@id="rc-tabs-0-tab-1"]')
        self.sleep(0.5)
