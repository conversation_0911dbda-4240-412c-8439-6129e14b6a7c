import pytest

from test_case.core_link.supply_chain.facesheet_case.Handwork_case import *

@ddt
class TestFaceSheet(Handwork):

    def face_sheet(self):
        self.sheet()

    # 待打单搜索框
    def test_facesheet_dadan(self):
        self.face_sheet()
        self.sleep(0.5)
        self.assert_text('待打单', '//*[@id="root"]/div/div[2]/div/div/div/div[1]/div[1]/div/div[1]')
        self.click("//span[@aria-label='system-close-small-line']")
        # 时间筛选
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(0.5)
        self.click("//span[contains(text(),'今日')]")  # 今天
        self.sleep(0.5)

        # 订单错误
        self.type('//*[@id="tradeOrderCode"]', '12345678901234567890')
        self.sleep(0.5)
        self.assert_text('订单编号为纯数字，最多19字符', '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div[2]/div')

        # 重置
        self.click("//*[contains(text(),'重 置')]")
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[5]/div/div[1]/div[1]/button')
        sleep(1)

        # 输入框查询
        self.type('//*[@id="itemName"]', '火龙果')
        self.sleep(0.5)

        # 下拉单选
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div[2]/div/div/div/div')
        # self.sleep(0.5)
        # self.click("//div[contains(@title,'无售后')]//div[1]")
        # self.sleep(0.5)
        # self.assert_text('无售后', "//div[contains(@title,'无售后')]//div[1]")

        # 展开全部
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[2]')
        self.sleep(0.5)

        # 目的省市--二级地址选择
        # self.click('#pro-form-wrapper > div:nth-child(3) > div:nth-child(1) > div > div.kwaishop-agent-pc-micro-col.kwaishop-agent-pc-micro-form-item-control > div > div > div > div > div')
        # self.sleep(0.5)
        # # self.click("body > div:nth-child(3) > div > div > div > div > ul > li:nth-child(1)")
        # # self.click("body > div:nth-child(3) > div > div > div > div > ul:nth-child(1) > li.kwaishop-agent-pc-micro-cascader-menu-item.kwaishop-agent-pc-micro-cascader-menu-item-expand.kwaishop-agent-pc-micro-cascader-menu-item-active > span")
        # self.click("body > div:nth-child(4) > div > div > div > div > ul > li:nth-child(1) > span > span")
        # self.sleep(0.5)

        # 更多筛选
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[5]/div/div[2]')
        # self.sleep(0.5)
        # self.click('#pro-form-wrapper > div:nth-child(4) > div.kwaishop-agent-pc-micro-col.kwaishop-agent-pc-micro-col-8.kwaishop-agent-pc-micro-pro-form-layout-button-group-col > div > div.kwaishop-agent-pc-micro-pro-form-layout-collapse')
        # self.sleep(0.5)

        # 查询
        self.click("//*[contains(text(),'查 询')]")
        # self.click('//*[@id="pro-form-wrapper"]/div[4]/div[2]/div/div[1]/div[2]/button')
        self.sleep(0.5)
        self.assert_text('暂无数据', '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]')

    # 待打单功能按钮
    @pytest.mark.p0
    def test_facesheet_pushbutton(self):
        self.face_sheet()
        self.sleep(0.5)
        self.click("//span[@aria-label='system-close-small-line']")
        # 批量打标备注
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[1]/div/button')
        # self.sleep(0.5)
        # self.assert_element_present('//*[@id="rc-tabs-1-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[1]/div/div/div/div/div/div[2]')

        # 合并打印
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[3]/button')
        self.sleep(0.5)
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[3]/button')
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]')
        self.sleep(0.5)

        # 买家卖家留言
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/label[1]')
        self.sleep(0.5)
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/label[2]')
        self.sleep(0.5)

        # 支付时间排序
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]/div')
        self.sleep(0.5)
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]/div[2]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        self.sleep(0.5)
        self.assert_text('按支付时间升序', '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]/div[1]/span[2]')

    @pytest.mark.p0
    def test_facesheet_module(self):
        self.face_sheet()
        self.sleep(0.5)

        # 已打单待发货tab
        self.click('//*[@id="rc-tabs-0-tab-WAIT_SHIP"]')
        self.sleep(0.5)

        # 发货
        self.assert_element('//*[@id="rc-tabs-0-panel-WAIT_SHIP"]/div/div[3]/button[1]')
        self.click('//*[@id="rc-tabs-0-panel-WAIT_SHIP"]/div/div[3]/button[1]')
        self.sleep(0.5)
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)
        # self.assert_text('请先勾选表格中的订单，再点击按钮', '//*[@id="rc-tabs-1-panel-WAIT_SHIP"]/div/div[3]/div/div/div/div/div[2]')

        # 补打面单
        self.click('//*[@id="rc-tabs-0-panel-WAIT_SHIP"]/div/div[3]/button[2]')
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)
        # self.assert_text('请先勾选表格中的订单，再点击按钮', '//*[@id="rc-tabs-1-panel-WAIT_SHIP"]/div/div[3]/div[2]/div/div/div/div[2]')

        # 回收面单
        self.click('//*[@id="rc-tabs-0-panel-WAIT_SHIP"]/div/div[3]/button[3]')
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)
        # self.assert_text('请先勾选表格中的订单，再点击按钮', '//*[@id="rc-tabs-1-panel-WAIT_SHIP"]/div/div[3]/div[3]/div/div/div/div[2]')
        # self.assert_text('请先勾选表格中的订单，再点击按钮', '/html/body/div[1]/section/section/main/div/div[2]/div/div/div/div/div[4]/div[2]/div/div/div/div[2]')

        # 已发货底tab
        self.click('//*[@id="rc-tabs-0-tab-ALREADY_SHIP"]')
        self.sleep(0.5)

        # 补打面单
        self.assert_element('//*[@id="rc-tabs-0-panel-ALREADY_SHIP"]/div/div[3]/button')
        self.click('//*[@id="rc-tabs-0-panel-ALREADY_SHIP"]/div/div[3]/button')
        self.sleep(0.5)
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)
        # self.assert_text('请先勾选表格中的订单，再点击按钮', '//*[@id="rc-tabs-1-panel-ALREADY_SHIP"]/div/div[3]/div/div/div/div/div[2]')

        # 仅打单tab
        self.click('//*[@id="rc-tabs-0-tab-WAIT_PRINT"]')
        self.sleep(0.5)

        # 仅打单
        self.assert_element('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[3]/button[1]')
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[3]/button[1]')
        self.sleep(0.5)
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)
        # self.assert_text('请先勾选表格中的订单，再点击按钮', '/html/body/div[1]/section/section/main/div/div[2]/div/div/div/div/div[4]/div[1]/div/div/div/div[2]')

        # 打单+发货
        self.assert_element('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[3]/button[2]')
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[3]/button[2]')
        self.sleep(0.5)
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)
        # self.assert_element('//*[@id="root"]/div/div/div[4]/div[2]/div/div/div/div[2]')

        # 拆单
        self.assert_element('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[3]/button[3]')
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[3]/button[3]')
        self.sleep(0.5)
        self.assert_equal(self.is_text_visible('请先勾选表格中的订单，再点击按钮'), True)

     #  待打单--翻页再搜索订单编号
    def test_facesheet_check_order(self):
        self.face_sheet()
        self.sleep(0.5)
        self.click("//span[@aria-label='system-close-small-line']")
        self.assert_text('待打单', '//*[@id="rc-tabs-0-tab-WAIT_PRINT"]')
        self.assert_text('支付时间', "//label[contains(text(),'支付时间')]")
        # 支付时间选择
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[3]/input')
        self.sleep(1)
        # 近30天
        self.click("//span[contains(text(),'近30天')]")
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[1]/div[2]/button')  # 查询
        element = self.find_element(
            '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div')  # 订单编号
        order_number = element.text
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[3]/div/ul/li[5]')  # 跳转翻页
        self.type("//input[@id='tradeOrderCode']", order_number)  # 填入订单编号
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[1]/div[2]/button')  # 翻页后搜索订单编号
        self.sleep(2)
        self.assert_text(order_number,'//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div')
        # 元素确认当前页面有该订单信息

     #  待打单--翻页 搜索框功能
    def test_facesheet_check_search_box(self):
        self.face_sheet()
        self.sleep(0.5)
        self.click("//span[@aria-label='system-close-small-line']")
        self.assert_text('待打单', '//*[@id="rc-tabs-0-tab-WAIT_PRINT"]')
        self.assert_text('支付时间', "//label[contains(text(),'支付时间')]")
        # 支付时间选择
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[3]/input')
        self.sleep(1)
        # 近30天
        self.click("//span[contains(text(),'近30天')]")
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[1]/div[2]/button')  # 查询
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[3]/div/ul/li[5]')  # 跳转翻页
        self.sleep(2)
        # 时间筛选
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[1]/input')
        self.sleep(0.5)
        self.click("//span[contains(text(),'今日')]")  # 今天
        self.sleep(0.5)

        # 订单错误
        self.type('//*[@id="tradeOrderCode"]', '12345678901234567890')
        self.sleep(0.5)
        self.assert_text('订单编号为纯数字，最多19字符', '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div[2]/div')

        # 重置
        self.click("//*[contains(text(),'重 置')]")
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[5]/div/div[1]/div[1]/button')
        sleep(1)

        # 输入框查询
        self.type('//*[@id="itemName"]', '火龙果')
        self.sleep(0.5)

        # 下拉单选
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div[2]/div/div/div/div')
        # self.sleep(0.5)
        # self.click('/html/body/div[2]/div/div/div/div[2]/div[1]/div/div/div[1]/div')
        # self.sleep(0.5)
        # self.assert_text('无售后', '//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div[2]/div/div/div/div/span[2]')

        # 展开全部
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[2]')
        self.sleep(0.5)

        # 目的省市--二级地址选择
        # self.click('#pro-form-wrapper > div:nth-child(3) > div:nth-child(1) > div > div.kwaishop-agent-pc-micro-col.kwaishop-agent-pc-micro-form-item-control > div > div > div > div > div')
        # self.sleep(0.5)
        # # self.click("body > div:nth-child(3) > div > div > div > div > ul > li:nth-child(1)")
        # # self.click("body > div:nth-child(3) > div > div > div > div > ul:nth-child(1) > li.kwaishop-agent-pc-micro-cascader-menu-item.kwaishop-agent-pc-micro-cascader-menu-item-expand.kwaishop-agent-pc-micro-cascader-menu-item-active > span")
        # self.click("body > div:nth-child(4) > div > div > div > div > ul > li:nth-child(1) > span > span")
        # self.sleep(0.5)

        # 更多筛选
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[5]/div/div[2]')
        # self.sleep(0.5)
        # self.click('#pro-form-wrapper > div:nth-child(4) > div.kwaishop-agent-pc-micro-col.kwaishop-agent-pc-micro-col-8.kwaishop-agent-pc-micro-pro-form-layout-button-group-col > div > div.kwaishop-agent-pc-micro-pro-form-layout-collapse')
        # self.sleep(0.5)

        # 查询
        self.click("//*[contains(text(),'查 询')]")
        # self.click('//*[@id="pro-form-wrapper"]/div[4]/div[2]/div/div[1]/div[2]/button')
        self.sleep(0.5)
        self.assert_text('暂无数据', '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]')

     #  待打单--翻页 页面功能正常使用
    def test_facesheet_page_function(self):
        self.face_sheet()
        self.sleep(0.5)
        self.click("//span[@aria-label='system-close-small-line']")
        self.assert_text('待打单', '//*[@id="rc-tabs-0-tab-WAIT_PRINT"]')
        self.assert_text('支付时间', "//label[contains(text(),'支付时间')]")
        # 支付时间选择
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[3]/input')
        self.sleep(1)
        # 近30天
        self.click("//span[contains(text(),'近30天')]")
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[1]/div[2]/button')  # 查询
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[3]/div/ul/li[5]')  # 跳转翻页
        self.sleep(2)
        # 批量打标备注
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[1]/div/button')

        # 合并打印
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[3]/button')
        self.sleep(0.5)
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/div[3]/button')
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]')
        self.sleep(0.5)

        # 买家卖家留言
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/label[1]')
        self.sleep(0.5)
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[1]/label[2]')
        self.sleep(0.5)

        # 支付时间排序
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]/div')
        self.sleep(0.5)
        self.click(
            '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]/div[2]/div/div/div/div[2]/div[1]/div/div/div[2]/div')
        self.sleep(0.5)
        self.assert_text('按支付时间升序', '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[1]/div[2]/div[1]/span[2]')


        #  待打单--翻页 页面列表正常展示
    def test_facesheet_check_pagelist(self):
        self.face_sheet()
        self.sleep(0.5)
        self.click("//span[@aria-label='system-close-small-line']")
        self.assert_text('待打单', '//*[@id="rc-tabs-0-tab-WAIT_PRINT"]')
        self.assert_text('支付时间', "//label[contains(text(),'支付时间')]")
        # 支付时间选择
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/div/div[3]/input')
        self.sleep(1)
        # 近30天
        self.click("//span[contains(text(),'近30天')]")
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div[1]/div[2]/button')  # 查询
        self.click('//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[3]/div/ul/li[5]')  # 跳转翻页
        self.sleep(2)
        self.assert_text('收货信息',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[2]')
        self.assert_text('订单编号',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[3]')
        self.assert_text('订单/售后状态',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[4]')
        self.assert_text('商品信息',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[5]')
        self.assert_text('待打单数量',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[6]')
        self.assert_text('买家备注',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[7]')
        self.assert_text('操作',
                         '//*[@id="rc-tabs-0-panel-WAIT_PRINT"]/div[2]/div[2]/div/div/div/div/div[1]/table/thead/tr/th[10]')