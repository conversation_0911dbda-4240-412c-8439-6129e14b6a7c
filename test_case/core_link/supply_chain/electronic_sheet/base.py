import time

from seleniumbase import BaseCase

from constant.account import get_account_info
from constant.domain import get_domain

class BaseTestCase(BaseCase):

    def login(self, domain):

        # 快手id **********
        account_data = get_account_info("supply_account")

        host = get_domain(domain)
        self.open(host)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.sleep(0.5)
        self.assert_text("扫码登录", "//*[text()='扫码登录']")
        self.assert_text("手机号登录", "//*[text()='手机号登录']")
        self.click("//*[text()='手机号登录']")
        self.sleep(0.5)
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.sleep(0.5)
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.sleep(0.5)
        self.click("button[type='button']")
        self.sleep(0.5)

