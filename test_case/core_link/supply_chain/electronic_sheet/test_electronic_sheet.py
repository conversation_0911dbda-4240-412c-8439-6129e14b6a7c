from unittest import skip

import pytest
from ddt import ddt

from test_case.core_link.supply_chain.electronic_sheet.base import BaseTestCase

@ddt
class test_electronic_sheet(BaseTestCase):

    @pytest.mark.p0
    def test_electronic_sheet_login(self):
        # 检查电子面单是否能正常登录
        self.login('ELECTRONIC_SHEET_DOMAIN')
        self.sleep(0.5)
        # 校验地址
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/dadan/electronic-sheet/open'), True)
        self.maximize_window()

    @pytest.mark.p0
    def test_open_manual(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)

        # 点击使用说明书
        self.click("//header[@class='dadan-main-layout-header iP0Z7DwRcKlzJLvnAbUJ']//div[3]//div[1]")
        self.sleep(0.5)

        # 校验是否成功跳转
        self.switch_to_window(1)

        c_url = self.get_current_url()
        self.assert_equal(c_url, 'https://university.kwaixiaodian.com/kwaishop/newKnowledge/588443663844786272/581287785622441994')

    @pytest.mark.p0
    def test_open_download_manual(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)

        # 点击下载打印组件
        self.click('//*[@id="main_root"]/section/header/div[2]/div[1]')
        self.sleep(0.5)

        # 校验是否成功跳转
        self.switch_to_window(1)
        c_url = self.get_current_url()
        self.assert_title('快手打印组件安装教程及常见FAQ - 轻雀文档')

    @pytest.mark.p0
    @pytest.mark.skip
    def test_open_electronic_sheet(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)

        # 校验链接
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/express/electronic-sheet/open'), True)

        # 查看记录
        self.click('//*[@id="root"]/section/main/div/div/div[2]/section[1]/div[2]/div[2]/button')
        self.sleep(0.5)
        # self.assert_equal(self.is_element_visible('/html/body/div[2]/div/div[2]/div/div'), True)

        # 选择物流公司—韵达
        self.click('/html/body/div[2]/div/div[2]/div/div/div[2]/form/div/div[1]/div/div[2]/div/span/div/div/div')
        self.sleep(0.5)
        self.click('/html/body/div[3]/div/div/div/ul/li[1]')
        self.sleep(0.5)

        # 选择申请状态—已开通
        self.click('/html/body/div[2]/div/div[2]/div/div/div[2]/form/div/div[2]/div/div[2]/div/span/div/div/div/div')
        self.sleep(0.5)
        self.click('/html/body/div[4]/div/div/div/ul/li[2]')
        self.sleep(0.5)

        # 查询
        self.click('/html/body/div[2]/div/div[2]/div/div/div[2]/form/div/div[3]/div/div/div/span/button[1]')
        self.sleep(1)

        # 校验
        self.assert_equal(self.is_text_visible('韵保价'), True)
        self.assert_equal(self.is_text_visible('智橙网'), True)

        # 点击服务开通
        self.click('//tbody/tr[1]/td[4]/div[1]')
        self.sleep(0.5)
        self.assert_equal(self.is_element_visible('/html/body/div[5]/div/div[2]/div/div'), True)

        # 点击申请开通
        self.click('/html/body/div[5]/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div')
        self.sleep(0.5)
        self.assert_equal(self.is_element_visible('/html/body/div[6]/div/div/div/div[2]/div/div'), True)
        self.click('/html/body/div[6]/div/div/div/div[2]/div/div/div[2]/button[1]')
        self.sleep(0.5)

        # 检验增值服务管理的问号
        self.hover_on_element('/html/body/div[5]/div/div[2]/div/div/div[1]/div/div/span')
        self.sleep(0.5)
        self.assert_equal(self.is_element_visible('/html/body/div[7]/div/div/div/div[2]/div/div'), True)

        # 回到上一页
        self.click('/html/body/div[5]/div/div[2]/div/div/div[1]/button')
        self.sleep(0.5)

        # 检验合作列表的问号
        self.hover_on_element('/html/body/div[2]/div/div[2]/div/div/div[1]/div/span')
        self.sleep(0.5)
        self.assert_equal(self.is_element_visible('/html/body/div[5]/div/div/div/div[2]/div/div'), True)

        # 校验展开
        self.click('/html/body/div[2]/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div')
        self.sleep(0.5)

        # 校验重置
        self.click('/html/body/div[2]/div/div[2]/div/div/div[1]/button')
        self.sleep(0.5)

        # 新增合作
        self.click('//*[@id="root"]/section/main/div/div/div[2]/section[1]/div[2]/div[3]/button')
        self.sleep(0.5)
        # self.assert_equal(self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]'), True)

        # 保存，校验报错
        self.click("(//button[@class='ant-btn ant-btn-primary'])[1]")
        self.sleep(0.5)
        self.assert_text('请选择发货地址', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[2]/div[1]/div/div[2]/div/div')
        self.assert_text('请选择网点', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[4]/div/div/div')
        self.assert_text('请输入联系人', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[5]/div[2]/div/div')
        self.assert_text('请输入联系电话 请输入正确的联系电话','/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[6]/div[2]/div/div')
        self.assert_text('请阅读并同意快手电子面单服务协议', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[7]/div/div/div')

        # 取消
        # self.click("//*[contains(text(),'取 消')]")
        self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button[1]')
        self.sleep(0.5)

    @pytest.mark.p0
    def test_sheet_account_detail(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)
        # 点击面单账户详情并校验url
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[2]/div')
        self.sleep(0.5)
        self.click('//html/body/div[1]/section/section/aside/div/div[1]/ul/li[2]/ul/li[1]/span')
        # self.click("//*[contains(text(),'面单账户详情')]") 如果上面一行fail就尝试用这个
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/dadan/electronic-sheet/account-detail'), True)


        # 查询物流公司
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/div/div[1]')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div/div[1]/div/div[2]/div/div/div/span')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[1]/button')
        self.sleep(0.5)

        # self.assert_text('12', '//*[@id="pro-form-wrapper"]/section/main/div/div/div/div/div[1]/p/span/span')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        self.sleep(0.5)

        # 网点名称查询
        self.type('//*[@id="pro-form-wrapper"]/div/div[2]/div/div[2]/div/div/span/input', '1')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        self.sleep(0.5)
        # self.assert_text('网点名称过长 [1000]', '/html/body/div[3]/div/span/div/div')
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[1]/button')
        self.sleep(0.5)

        # 网点编码查询
        self.type('//*[@id="pro-form-wrapper"]/div/div[3]/div/div[2]/div/div/span/input', '311017000')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div/div[4]/div/div/div[2]/button')
        self.sleep(0.5)
        # self.assert_text('1', '//*[@id="pro-form-wrapper"]/div/div[3]/div/div[2]/div/div/span')

        # 余额记录查询
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[11]/button')
        self.sleep(0.5)
        # self.assert_equal(self.is_element_visible('/html/div/div/div[2]/div/div/div[1]/div/div'), True)
        # self.click('/html/div/div/div[2]/div/div/div[2]/button')
        # self.sleep(0.5)
        # self.click('/html/body/div[3]/div/div[2]/div/div/div[1]/button')
        # self.sleep(0.5)

        # 校验导出是否可见
        self.assert_equal(self.is_element_visible('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div[2]/div/div[2]/button'), True)

    @pytest.mark.p0
    def test_sheet_usage_detail(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)

        # 点击面单使用详情并校验url
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[2]/div')
        self.sleep(0.5)
        self.click('//html/body/div[1]/section/section/aside/div/div[1]/ul/li[2]/ul/li[2]/span')
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/dadan/electronic-sheet/usage-detail'), True)
        self.assert_text('您的账号当前仅允许查看近三月的订单', "//div[@class='kwaishop-supply-dadan-pc-alert-content']")
        self.assert_text('导 出', "//button[@class='kwaishop-supply-dadan-pc-btn kwaishop-supply-dadan-pc-btn-primary']")
        self.hover_on_element("//button[@class='kwaishop-supply-dadan-pc-btn kwaishop-supply-dadan-pc-btn-primary']")
        self.sleep(0.5)
        self.assert_equal(self.is_text_visible('导出最大条数5000条'), True)

        # 查询
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div/div[2]/button')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div/div[1]/button')
        self.sleep(0.5)
        self.type('//*[@id="pro-form-wrapper"]/div[1]/div[2]/div/div[2]/div/div/span/input', 'test')  # 无输入校验
        self.sleep(0.5)
        self.type('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div/div/span/input', 'test')  # 无输入校验
        self.sleep(0.5)
        self.type('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div[2]/div/div/span/input', 'test')  # 无输入校验
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[1]/div/div[2]/div/div/div[1]')
        self.sleep(0.5)
        # self.click('/html/body/div[4]/div/div/div/ul/li[1]')
        self.click('//*[@id="pro-form-wrapper"]/div[1]/div[3]/div/div[2]/div/div/div')  # 备用项
        self.sleep(0.5)
        # self.click('//*[@id="pro-form-wrapper"]/section/main/div/div/div/form/div[2]/div[3]/div/div[2]/div/span/div/div/div')
        # self.sleep(0.5)
        # self.click('/html/body/div[5]/div/div/div/ul/li[1]')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[3]/div/div[2]/div/div/div/div[1]/span[2]')  # 备用项
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div/div[1]/button')
        self.sleep(0.5)
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div/div[2]/button')
        self.sleep(0.5)

        # 校验导出是否可见
        self.assert_equal(self.is_element_visible("//*[contains(text(),'导 出')]"), True)

    @pytest.mark.p0
    def test_address_manage(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)

        # 点击地址管理
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[3]/span')
        self.sleep(0.5)

        # 校验地址
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supplychain/address'), True)

        # 校验新增发货地址按钮
        self.assert_text('新增发货地址', '//*[@id="rc-tabs-0-panel-1"]/div/button/span[2]')
        self.assert_text('默认发货', '//*[@id="rc-tabs-0-panel-1"]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div')
        self.assert_text('发', '//*[@id="rc-tabs-0-panel-1"]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/span')
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/button')
        self.sleep(0.5)
        self.click("//span[contains(text(),'确 定')]")
        self.sleep(0.5)
        # self.assert_text('必填', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[2]/div[2]/div[2]/div')
        # self.assert_text('必填', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[4]/div[2]/div[2]/div')
        # self.assert_text('必填', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[5]/div[2]/div[2]/div')
        # self.assert_text('必填', '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/form/div[6]/div[2]/div[2]/div')
        self.click("//span[contains(text(),'取 消')]")
        self.sleep(0.5)

        # 校验退货地址
        # self.click('//*[@id="rc-tabs-0-tab-2"]')
        # self.sleep(2)
        # if self.is_element_visible("//span[contains(text(),'取 消')]"):
        #     # self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[1]')
        #     self.click("//span[contains(text(),'取 消')]")
        #     self.sleep(0.5)
        # self.assert_text('新增退货地址', '//*[@id="rc-tabs-0-panel-2"]/div/button/span[2]')
        # self.assert_text('默认退货', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div')
        # self.assert_text('退', '//*[@id="rc-tabs-0-panel-2"]/div/div/div/div/div/div/div/table/tbody/tr[1]/td['
        #                        '5]/div/span')
        # self.click('//*[@id="rc-tabs-0-panel-2"]/div/button')
        # self.sleep(0.5)
        # self.click("//span[contains(text(),'确 定')]")
        # self.sleep(0.5)
        # self.assert_text('必填', '/html/body/div[4]/div/div[2]/div/div[2]/div[2]/form/div[2]')
        # self.assert_text('必填', '/html/body/div[4]/div/div[2]/div/div[2]/div[2]/form/div[4]')
        # self.assert_text('必填', '/html/body/div[4]/div/div[2]/div/div[2]/div[2]/form/div[5]')
        # self.assert_text('必填', '/html/body/div[4]/div/div[2]/div/div[2]/div[2]/form/div[6]')
        # self.click("//span[contains(text(),'取消')]")

    @pytest.mark.p0
    def test_open_cloud_print_editor(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(1)

        # 点击云打印编辑器
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[4]/span')
        self.sleep(0.5)

        # 校验跳转是否成功
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.startswith('https://cloudprint.kwaixiaodian.com'), True)

    @pytest.mark.skip
    def test_cloud_print_editor(self):
        # 打开云打印
        self.test_open_cloud_print_editor()

        # self.click('//*[@id="identityType"]/label[2]/span[2]')
        # self.sleep(0.5)
        self.click('//*[@id="identityType"]/label[2]/span[1]')
        self.sleep(0.5)
        self.click('/html/body/div[8]/div/div[2]/div/div[2]/div[2]/div/div/form/div[2]/div[2]/div/div/div/span')
        self.click('/html/body/div[10]/div/div[2]/div/div[2]/div[2]/div/div/form/div[2]/div[2]/div/div/div/div[2]/div/div/div/div[2]/div[1]/div/div/div[4]')
        self.sleep(0.5)
        self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]')
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/main/welcome'), True)

        self.click('//*[@id="node_menu_for_intersectionObserver_1"]/ul/li/span/span')
        self.sleep(0.5)
        self.click('/html/body/div/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                   '1]/div/div/div/section/div/form/div/div[1]/div/div[2]/div/div/div/div/div/span[1]')
        self.sleep(0.5)
        self.click("//*[contains(text(),'韵达快递')]")
        self.sleep(0.5)
        self.click('/html/body/div/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                   '1]/div/div/div/section/div/form/div/div[2]/div/div[2]/div/div/div/div/div/span[1]')
        self.sleep(0.5)
        self.click("//*[contains(text(),'快递三联面单')]")
        self.sleep(0.5)
        self.type('/html/body/div[1]/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                  '1]/div/div/div/section/div/form/div/div[3]/div/div[2]/div/div/div/input', '123456789')
        self.sleep(0.5)
        self.click('/html/body/div[1]/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                   '1]/div/div/div/section/div/form/div/div[4]/div/div/div/div/div/div/div/div/div[1]/button')
        self.sleep(0.5)
        self.click('/html/body/div[1]/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                   '1]/div/div/div/section/div/form/div/div[4]/div/div/div/div/div/div/div/div/div[2]/button/span')
        self.sleep(0.5)
        self.click('/html/body/div[1]/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                   '2]/div/div/div/section/div/div/div/div/section/div/div/div/div[1]/div[1]/div/div[2]/div/span')
        self.sleep(0.5)

        # self.click("//*[contains(text(),'更多选项')]")
        # self.click('/html/body/div/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div[2]/div/div/div/section/div/div/div/div/section/div/div/div/div[2]/div/div[2]/div/section/div[1]/div/div/div/section/div[1]/div/div/section/div/section/div/div/div[3]/div/div[1]/button/span')
        # self.sleep(0.5)
        self.click('/html/body/div/div/div/div/div/div/div/div/div[2]/div[2]/div/div/div/div/div['
                   '2]/div/div/div/section/div/div/div/div/section/div/div/div/div[1]/div['
                   '3]/section/div/div/div/button/span')
        self.sleep(0.5)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/fangzhou/customTemplateAdd'), True)

    @pytest.mark.p0
    def test_express_template(self):
        # 打开电子面单
        self.test_electronic_sheet_login()
        self.sleep(0.5)

        # 点击快递模板设置
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[5]/span')
        self.sleep(0.5)

        # 校验地址
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/template-setting'), True)

        # 新增模板的错误提示 涉及到写操作，不创建新模板
        self.click('//*[@id="root"]/div/div/div/div/div[2]/div[3]/button')
        self.sleep(0.5)
        self.click("//button[@class='kwaishop-supply-dadan-pc-btn kwaishop-supply-dadan-pc-btn-primary']")
        self.sleep(0.5)
        # self.assert_text('模板名称必填', '/html/body/div[10]/div/div[2]/div/div[2]/div[2]/form/div[2]/div[2]/div[2]/div')
        # self.assert_text('模板样式必选', '/html/body/div[10]/div/div[2]/div/div[2]/div[2]/form/div[3]/div[2]/div[2]/div')
        self.click("//span[@class='kwaishop-supply-dadan-pc-modal-close-x']")
        self.sleep(0.5)

        # 展开第一个物流的详情 校验元素
        # self.click('#root > section > main > div > div > div > div > div:nth-child(2) > div.leftWrapper--81wtH > a > '
        #            'svg')
        self.click("//div[2]//div[2]//a[1]//span[1]//*[name()='svg']")
        self.sleep(0.5)
        self.assert_text('默认模板', '//*[@id="root"]/div/div/div/div/div[3]/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/div')
        self.assert_text('默认', '//*[@id="root"]/div/div/div/div/div[3]/div/div/div/div/div/table/tbody/tr[1]/td[4]/div/span')
        self.assert_text('编辑模板', '//*[@id="root"]/div/div/div/div/div[3]/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/button[1]')
        self.assert_text('删除', '//*[@id="root"]/div/div/div/div/div[3]/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/button[2]')

    @pytest.mark.p0
    def test_modify_express_template(self):
        self.test_express_template()
        self.click('//*[@id="root"]/div/div/div/div/div[3]/div/div/div/div/div/table/tbody/tr[1]/td[5]/div/button[1]')
        self.sleep(0.5)
        self.switch_to_window(1)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[1]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[2]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[3]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[4]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[5]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[6]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[7]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[8]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[9]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[10]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[7]/div/label/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[1]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[2]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[3]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[4]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[5]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[6]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[7]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[8]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[9]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[5]/label[10]/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[7]/div/label/span[1]')
        self.sleep(0.5)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/form/div[7]/button/span[2]')
        self.assert_equal(self.is_text_visible('自定义文字2'), True)
        self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/div/div/div[2]/button')
        self.sleep(0.5)
        self.click("//div[@class='kwaishop-supply-editor-micro-modal-root']//button[1]")
        # self.sleep(0.5)
        # self.click('//*[@id="root"]/div/div/div/div[2]/div/div/div/div/div/div[1]/button')
        # self.sleep(5)
        #
        # # 校验地址
        # self.switch_to_window(-1)
        # c_url = self.get_current_url()
        # self.assert_equal(c_url.endswith('/template-setting'), True)