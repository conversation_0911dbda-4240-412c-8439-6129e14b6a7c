import time
from datetime import datetime, timedelta

import pytest
from ddt import ddt
from test_case.core_link.supply_chain.base import BaseTestCase


@ddt
class TestSytCustomer(BaseTestCase):

    # 包裹中心
    @pytest.mark.p0
    def test_package_center(self):
        self.sub_account_login('MERCHANT_DOMAIN', 'supply_account_child')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击包裹中心
        self.sleep(0.5)
        self.click('//*[@id="menu_item_k7aSWoGZRoY"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/package'), True)
        # 校验元素
        self.assert_text('发货包裹', '#rc-tabs-0-tab-1')
        self.assert_text('退货包裹', '#rc-tabs-0-tab-2')

        self.check_document('//*[@id="root"]/div/div[2]/div[1]/div[3]/button/span', '新包裹中心使用手册 - 轻雀文档')

    # 包裹中心excel导出
    @pytest.mark.p0
    def test_package_center_data_export(self):
        self.sub_account_login('MERCHANT_DOMAIN', 'supply_account_child')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('#menu_item_k7aSWoGZRoY')
        self.sleep(0.5)
        self.click('//*[@id="rc-tabs-0-panel-1"]/div[3]/div[3]/div/div[1]/div/div/div/div/div[3]/div/div[2]/button')
        time.sleep(1)
        current_time = datetime.now().replace(microsecond=0)
        end_time = current_time + timedelta(seconds=5)
        # 点击生成报表
        self.click("//span[contains(text(),'生成报表')]")  # 如果需要调试注意时间，导出时间有5分钟间隔
        self.sleep(0.5)
        try:
            self.switch_to_window(1)

            self.sleep(2)
            time_str = self.find_element(
                '#root > div > div > div.kwaishop-supply-express-micro-pro-table > div > div > div > div > div > div > div > div > div > table > tbody > tr:nth-child(1) > td:nth-child(1)').text
            given_time = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            assert current_time <= given_time and given_time <= end_time, "导出时间不符合预期"
            # 刷新一下页面
            self.refresh()
            self.sleep(1)
            button = self.find_element(
                '//*[@id="root"]/div/div/div[2]/div/div/div/div/div/div/div/div/div/table/tbody/tr/td[5]/button/span')
            self.assert_equal(button.is_enabled(), True)
        except Exception as e:
            assert str(e) == "Window {1} was not present after 7 seconds!", "其他错误" + str(e)
            self.click("//button[@class='kwaishop-supply-express-micro-btn kwaishop-supply-express-micro-btn-primary']")
            self.click("//span[contains(text(),'生成报表')]")
            time.sleep(0.5)
            self.assert_text('为了保证您的查询性能，两次导出的时间至少需要间隔5分钟。',
                             "//span[contains(text(),'为了保证您的查询性能，两次导出的时间至少需要间隔5分钟。')]")

    # 地址管理
    @pytest.mark.p0
    def test_supply_address(self):
        self.sub_account_login('MERCHANT_DOMAIN', 'supply_account_child')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击地址管理
        self.sleep(1)
        self.click('//*[@id="menu_item_fSzMd3Rslg0"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/address'), True)
        # 校验元素
        # self.sleep(100)
        self.assert_text('发货地址', '//*[@id="rc-tabs-1-tab-1"]')
        self.assert_text('退货地址', '//*[@id="rc-tabs-1-tab-2"]')
        self.assert_text('多退货地址策略', '//*[@id="rc-tabs-1-tab-3"]/div/div[1]')

    # 真宝仓质检
    @pytest.mark.p0
    def test_supply_treasure_waybill_code_mode(self):
        self.sub_account_login('MERCHANT_DOMAIN', 'supply_account_child')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击真宝仓质检
        self.sleep(0.5)
        self.click('//*[@id="menu_item_9qpNk9-oGqA"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/treasure'), True)

        self.assert_text("订单码", '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div/div/div/table/thead/tr/th[3]')

    # 电子面单
    @pytest.mark.p0
    def test_supply_ebill(self):
        self.sub_account_login('MERCHANT_DOMAIN', 'supply_account_child')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击电子面单
        self.click('//*[@id="menu_item_f8GaV-4KdD4"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/dadan/electronic-sheet'), True)
        # 校验元素
        title_root = '//*[@id="root"]/div/div/div/div[1]/div[1]/div/'
        self.assert_text('开通电子面单', title_root + 'div[1]')
        self.assert_text('面单账户详情', title_root + 'div[2]')
        self.assert_text('面单使用详情', title_root + 'div[3]')
        self.assert_text('快递模板设置', title_root + 'div[4]')
        self.check_document('//*[@id="root"]/div/div/div/div[1]/div[3]/a[1]',
                            '快手打印组件安装教程及常见FAQ - 轻雀文档')
        self.switch_to_window(0)
        self.click('//*[@id="root"]/div/div/div/div[1]/div[3]/a[2]')
        self.switch_to_window(2)
        self.assert_text("【电子面单】使用教程", '//*[@id="root"]/div/div/div/div[2]/div[1]/div[1]/div[1]')

    # 运费模板
    @pytest.mark.p0
    def test_express_template(self):
        self.sub_account_login('MERCHANT_DOMAIN', 'supply_account_child')
        # 去除小店弹窗
        self.click_xiaodian_popup_window()
        self.sleep(0.5)
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击运费模板
        self.click('//*[@id="menu_item_VLqIFWSy8Gs"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/address/freight-template/list'), True)
        # 校验元素
        self.assert_text('新增运费模板', "//span[contains(text(),'新增运费模板')]")
        self.check_document("//span[contains(text(),'操作手册')]", '新版 运费模板 操作手册 - 轻雀文档')
