from time import sleep
from ddt import ddt
from test_case.core_link.supply_chain.printing_sheet.base import BaseTestCase

@ddt
class Handwork(BaseTestCase):

    # 官方打单--打单发货
    def sheet(self):
        self.login("DADAN_DOMAIN", "supply_account")
        # self.click('//*[@id="root"]/div/div[2]/div/div/div/div[4]/form/div[3]/button')
        self.assert_element_present('//*[@id="main_root"]/section/header/div[1]/a[3]')
        self.maximize_window()

    # 官方打单--手工打单
    def handwork(self):
        self.login("DADAN_DOMAIN", "supply_account")
        self.click('//*[@id="main_root"]/section/section/aside/div/div[1]/ul/li[2]/span')
        self.sleep(0.5)
        self.maximize_window()
