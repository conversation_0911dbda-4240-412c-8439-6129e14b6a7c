import pytest
from ddt import ddt

from test_case.core_link.supply_chain.base import BaseTestCase


@ddt
class TestSytCustomer(BaseTestCase):

    # 打单发货
    @pytest.mark.p0
    def test_official_deliver(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')

        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)

        # 点击打单发货
        self.click('//*[@id="menu_item_a-oQ1Q74vlU"]')

        self.switch_to_window(1)

        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/dadan/print-waybill'), True)

    # 代发管理
    @pytest.mark.p0
    def test_drop_shipping(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')

        # 处理小店弹窗
        self.click_xiaodian_popup_window()

        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)

        # 点击代发管理
        self.sleep(1)
        self.click('//*[@id="menu_item_BSPblLtl6rc"]')

        self.switch_to_window(1)

        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/electronic-sheet/drop-shipping-manage'), True)

    # 在线寄件
    @pytest.mark.p0
    def test_online_deliver(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')

        # 处理小店弹窗
        self.click_xiaodian_popup_window()

        #切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)

        # # 点击在线寄件
        # self.click('//*[@id="menu_item_rSP6rkCNeAg"]')
        #
        # # 获取并校验url
        # c_url = self.get_current_url()
        # self.assert_equal(c_url.endswith('/zone/supply/package/online-parcel-sending'), True)
        # # 校验元素
        # self.assert_text('下单发货', '//*[@id="order-tab"]')
        # self.assert_text('支付运费', '//*[@id="pay-tab"]')
        # self.assert_text('历史发货记录', '//*[@id="history-tab"]')
        # button = self.find_element('#place-order-button')
        # # 下单按钮是否可点击
        # self.assert_equal(button.is_enabled(), True)
        #
        # self.check_document('//*[@id="tutorials"]/button[1]/span', '中小商家专属功能——快手在线寄件操作手册 - 轻雀文档')
        #
        # self.switch_to_window(0)
        #
        # self.check_document('//*[@id="tutorials"]/button[2]/span', '在线寄件报价单-单量阶梯价，下单越多 价格越优惠 - 轻雀文档', 2)

    # 包裹中心
    @pytest.mark.p0
    def test_package_center(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')
        self.maximize_window()
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)

        # 点击包裹中心
        self.click('//*[@id="menu_item_k7aSWoGZRoY"]')

        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/package'), True)

        # 校验元素
        self.assert_text('发货包裹', '#rc-tabs-0-tab-1')
        self.assert_text('退货包裹', '#rc-tabs-0-tab-2')

        self.check_document("(//span[contains(text(),'使用手册')])[1]", '新包裹中心使用手册 - 轻雀文档')

        self.switch_to_window(0)
        # self.click('//*[@id="rc-tabs-0-panel-1"]/div[4]/div[3]/div/div[1]/div/div/div/div/div[4]/div/div[1]/button')
        # 获取并校验url
        # c_url = self.get_current_url()
        # self.assert_equal(c_url.endswith('/zone/supplychain/package-export-table'), True)

    # 地址管理
    @pytest.mark.p0
    def test_package_address(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')

        # 处理小店弹窗
        self.click_xiaodian_popup_window()

        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)

        # 点击地址管理
        self.click('//*[@id="menu_item_fSzMd3Rslg0"]')

        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/address'), True)

        # 校验元素
        # self.sleep(100)
        self.assert_text('发货地址', '//*[@id="rc-tabs-1-tab-1"]')
        self.assert_text('退货地址', '//*[@id="rc-tabs-1-tab-2"]')
        self.assert_text('多退货地址策略', '//*[@id="rc-tabs-1-tab-3"]/div/div[1]')

    # 运费模板
    @pytest.mark.p0
    def test_express_template(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')
        # 去除小店弹窗
        self.click_xiaodian_popup_window()
        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击运费模板
        self.click('#menu_item_VLqIFWSy8Gs')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/address/freight-template/list'), True)
        # 校验元素
        self.assert_text('新增运费模板', "//span[contains(text(),'新增运费模板')]")
        self.check_document("//span[contains(text(),'操作手册')]", '新版 运费模板 操作手册 - 轻雀文档')

        ## 下面的单独一个case
        # # 跳转新增运费模板
        # self.click('div.sc-ciZhAO.bDEnKY > a > button > span')
        # self.switch_to_window(1)
        # self.assert_text('基本信息', 'div.sc-ksZaOG.hcFElF')
        # self.switch_to_window(0)
        # # 跳转模板详情
        # self.click('div.ant-table-body>table>tbody>tr>td:nth-child(1)>a')
        # self.switch_to_window(2)
        # self.assert_text('模板名称',
        #                  '#root > section > main > div > div > div:nth-child(1) > '
        #                  'div > div > table > tbody > tr:nth-child(1) > td > '
        #                  'span.ant-descriptions-item-label.ant-descriptions-item-colon')
        # self.switch_to_window(0)
        # # 跳转编辑
        # self.click('div.ant-table-body>table>tbody>tr>td:nth-child(5)>a')
        # self.switch_to_window(3)
        # self.assert_text('基本信息', 'div.sc-ksZaOG.hcFElF')
        # self.switch_to_window(0)
        # # 跳转使用
        # self.click('div.ant-table-body>table>tbody>tr>td:nth-child(5)>a:nth-child(4)')
        # self.switch_to_window(4)
        # self.assert_text('使用商品', '#root > section > main > div > div.sc-ksZaOG.hcFElF')

    # 物流服务
    @pytest.mark.p0
    def test_express_service(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')

        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击物流服务
        self.click('//*[@id="menu_item_6VEf81IZfeA"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/express-service'), True)


    # 真宝仓质检
    # @pytest.mark.p0
    # def test_package_treasure_waybill_code_mode(self):
    #     self.login('INSPECTION_DOMAIN', 'supply_account')
    #
    #     # 处理小店弹窗
    #     # self.click_xiaodian_popup_window()
    #     #
    #     # # 点击真宝仓质检
    #     # self.click('//*[@id="menu_item_GJgqJSATOIg"]')
    #     # 获取并校验url
    #     c_url = self.get_current_url()
    #     self.assert_equal(c_url.endswith('/zone/supplychain/treasure'), True)
        # self.assert_text("批量录入快递单号", '//*[@id="root"]/section/main/div/div/div[2]/div[1]/div[2]/button[2]')

    # 真宝仓管理
    @pytest.mark.p0
    def test_package_treasure_order_code_mode(self):
        self.login('MERCHANT_DOMAIN', 'member_account2')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 点击真宝仓质检
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        try:
            self.click('//*[@id="menu_item_9qpNk9-oGqA"]')
            # 获取并校验url
            c_url = self.get_current_url()
            self.assert_equal(c_url.endswith('zone/supplychain/treasure'), True)
        except Exception as e:
            self.click('#menu_item_GJgqJSATOIg')
            # 获取并校验url
            c_url = self.get_current_url()
            self.assert_equal(c_url.endswith('/zone/supply/express/inspection'), True)
            self.assert_text("快递单号",
                             '#root > section > main > div > div > div.sc-bZkfAO.egeqoJ > div.ant-table-wrapper > div > div > div > div > div.ant-table-scroll > div.ant-table-header.ant-table-hide-scrollbar > table > thead > tr > th:nth-child(3) > span > div > span.ant-table-column-title')

    # 货主平台
    @pytest.mark.p0
    def test_supplier(self):
        self.login('MERCHANT_DOMAIN', 'member_account2')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 点击货主平台
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        self.click('//*[@id="menu_item_fGvqbU7rahw"]')
        # 获取并校验url
        self.switch_to_window(1)
        self.sleep(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url, 'https://supplier.kwaixiaodian.com/zone/fangzhou/home')

    # 电子面单
    @pytest.mark.p0
    def test_supply_ebill(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击电子面单
        self.click("//span[@id='menu_item_f8GaV-4KdD4']")
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('zone/supply/dadan/electronic-sheet'), True)
        # 校验元素
        title_root = '//*[@id="root"]/div/div/div/div[1]/div[1]/div/'
        self.assert_text('开通电子面单', title_root + 'div[1]')
        self.assert_text('面单账户详情', title_root + 'div[2]')
        self.assert_text('面单使用详情', title_root + 'div[3]')
        self.assert_text('快递模板设置', title_root + 'div[4]')

        self.check_document('//*[@id="root"]/div/div/div/div[1]/div[3]/a[1]',
                            '快手打印组件安装教程及常见FAQ - 轻雀文档')
        self.switch_to_window(0)

        self.click('//*[@id="root"]/div/div/div/div[1]/div[3]/a[2]')
        self.switch_to_window(2)
        self.assert_text("【电子面单】使用教程", '//*[@id="root"]/div/div/div/div[2]/div[1]/div[1]/div[1]')

    # 物流工具
    @pytest.mark.p0
    def test_tool(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击物流工具
        self.click('//*[@id="menu_item_0TvWDFe0-Ws"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supplychain/tool'), True)
        # 校验元素
        title_root = '//*[@id="rc-tabs-0-tab-1"]'
        self.assert_text('快递不配送区域查询', '//*[@id="rc-tabs-1-tab-1"]')
        self.assert_text('取号失败推荐快递', '//*[@id="rc-tabs-1-tab-2"]')
        self.assert_text('网点履约能力查询', '//*[@id="rc-tabs-1-tab-3"]')

    # 物流诊断
    @pytest.mark.skip
    def test_supply_data(self):
        self.login('MERCHANT_DOMAIN', 'supply_account')
        # 处理小店弹窗
        self.click_xiaodian_popup_window()
        # 切回单列
        self.sleep(5)
        if self.is_element_visible("(//button[contains(text(),'返回旧版')])[1]"):
            self.click("(//button[contains(text(),'返回旧版')])[1]")
            self.sleep(2)
            self.click("//body//div//div[@class='seller-main-form-item-control-input-content']//div//div[1]")
            self.sleep(2)
            self.click("(//span[contains(text(),'确定返回旧版')])[1]")
        self.sleep(3)
        # 点击物流数据
        self.click('//*[@id="menu_item_iogKi7dLgP8"]')
        # 获取并校验url
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/supply/data'), True)
        # 校验元素
        self.assert_text('我的物流体验分', '//*[@id="root"]/section/main/div/div[1]/div/div/div/div[1]/div[1]/span[1]')
        self.assert_text('物流整体概况', '//*[@id="root"]/section/main/div/div[2]/div/div/div/div[1]/div[1]/span[1]')
        self.assert_text('已合作物流公司', '//*[@id="root"]/section/main/div/div[3]/div/div/div/div[1]/div/span[1]')
        self.assert_text('物流商指数排名', '//*[@id="root"]/section/main/div/div[4]/div[1]/div/div[1]/div/span[1]')
        self.assert_text('物流工具', '//*[@id="root"]/section/main/div/div[4]/div[2]/div/div[1]/div/span')

        self.click('//*[@id="root"]/section/main/div/div[1]/div/div/div/div[1]/div[2]/a')
        self.switch_to_window(1)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/zone/store-score/shop/home'), True)
