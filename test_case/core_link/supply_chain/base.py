import time

from selenium.common import WebDriverException
from seleniumbase.config.settings import SMALL_TIMEOUT
from seleniumbase.fixtures import page_actions

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    def sub_account_login(self, domain, account):
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            # 选择我是员工
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[1]/div[2]/span[2]')
            # 选择密码登陆
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.type("input[placeholder='请输入密码']", account_data['password'])
            # 登陆
            self.click(
                '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button')
            time.sleep(1)
        else:
            self.click("(//span[contains(text(),'我是员工')])[1]")
            self.sleep(1)
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.type("(//input[@placeholder='请输入手机号'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentB svelte-am7f5d'])[1]")
            self.sleep(2)

    def click_if_exist(self, selector, by="css selector", timeout=SMALL_TIMEOUT, delay=0, scroll=True):
        try:
            page_actions.wait_for_element_visible(
                self.driver,
                selector,
                by,
                timeout=timeout)
            self.click(selector, by, timeout, delay, scroll)
        except WebDriverException:
            print("not exist element, ingore")

    def click_switch_to_single(self):
        self.sleep(1)
        self.click_if_exist(
            '//div[contains(@id, "main-pc-page-layout")]//button[contains(text(), "返回旧版")]',
            by="xpath",
            timeout=15  # 延长超时时间至15秒
        )
        self.click_if_exist(
            "//div[@class='seller-main-modal-root']//div[5]",
            by="xpath",
            timeout=15
        )

        self.click_if_exist(
            "//span[contains(text(),'确定返回旧版')]",
            by="xpath",
            timeout=15  # 延长超时时间至15秒
        )
        self.sleep(3)

    def click_xiaodian_popup_window(self):
        time.sleep(2)
        count = 0
        while count < 3:
            if self.is_element_visible('#driver-popover-item'):
                self.click('#driver-page-overlay')  # 点击空白处
            count += 1
            time.sleep(1)

    def check_document(self, selector, doc_name, tab_no=1):
        # 点击连接
        self.click(selector)
        # 切换窗口
        self.switch_to_window(tab_no)
        self.sleep(3)
        # 校验title
        self.assert_title(doc_name)
