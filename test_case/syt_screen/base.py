from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")

    def map_user_id(self, user_id):
        self.click('//*[@id="DropDownContainer"]/img')
        self.click('//*[@id="DropDownContainer"]/div/div/div/ul/div[2]/div[1]/span[2]')
        self.type('#rc_select_0.syt-main-select-selection-search-input', user_id, retry=1)
        self.sleep(2)
        self.send_keys('#rc_select_0.syt-main-select-selection-search-input', '\ue007')
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/button[2]')

    def map_user_id_admin(self, user_id):
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.click('//*[@id="DropDownContainer"]/img')
        self.click('//*[@id="DropDownContainer"]/div/div/div/ul/div[2]/div[1]/span[2]')
        self.type('#rc_select_0.syt-main-select-selection-search-input', user_id, retry=1)
        self.sleep(2)
        self.send_keys('#rc_select_0.syt-main-select-selection-search-input', '\ue007')
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/button[2]')
