import random
from unittest import skip

from ddt import ddt
from .base import BaseTestCase

@ddt
class TestSytScreen(BaseTestCase):


    #登录直播大屏
    def live_screen(self):
        self.login("SYT_DOMAIN", "live_screen_account")

        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.maximize_window()
        #username = self.get_text('#DropDownContainer > span')
        #if username == '言植优选':
        #    pass
        #else:
        #    self.map_user_id(*********)

        self.click('//*[@id="main_root"]/div/div/section/section/aside/div/div/div[4]/div[2]/div[2]/span')
        #self.click('//*[@id="content-menu"]/ul/li[2]')
        self.click('//*[@id="module-content"]/div/div/div[2]/div/div/div/div/div/div/div/div/table/tbody/tr['+ str(random.randint(2, 5)) +']/td[6]/button[2]')
        self.switch_to_window(1)
        self.assert_title('快手数据大屏')
        self.maximize_window()
        self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[7]/div[1]/div')
        self.click('/html/body/div[3]/div/div/div/div/div/span[2]/img')

    @skip
    def test_homepage(self):
        self.live_screen()

        self.assert_text("快手小店直播期间累计成交金额",'//*[@id="root"]/section/div/div/div/div/div/div[8]/div[1]/div/div/div/div[1]')
        self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[1]/div/div/div/div[2]')

        platform_text = self.get_text('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[3]')
        self.assert_in("累计新增粉丝", platform_text)
        self.assert_in("累计成交订单数", platform_text)
        self.assert_in("累计成交人数", platform_text)
        self.assert_in("成交转化率", platform_text)
        self.assert_in("千次观看成交", platform_text)
        self.assert_in("成交粉丝占比", platform_text)
        self.assert_in("粉丝复购率", platform_text)
        self.assert_in("人均观看时长", platform_text)
        self.assert_in("实时观看人数", platform_text)
        self.assert_in("累计观看人数", platform_text)

    @skip
    def test_livereport(self):
        self.live_screen()

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[7]/div[4]/div/div/div/div[1]/span')
        self.assert_element('/html/body/div[4]/div/div[2]/div/div[2]/div')

    @skip
    #大屏趋势图
    def test_watchingnum(self):
        self.live_screen()

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[2]/div/div/div/div/div[1]/div[1]/div[1]/div/div[1]')
        self.click('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[2]/div/div/div/div/div[2]/div/label[' + str(
            random.randint(1, 2)) + ']')
        self.assert_element('//*[@id="rc-tabs-1-panel-1"]/div/div')
        self.click(
            '//*[@id="root"]/section/div/div/div/div/div/div[8]/div[2]/div/div/div/div/div[1]/div[1]/div[1]/div/div[2]')
        self.click('//*[@id="root"]/section/div/div/div/div/div/div[8]/div[2]/div/div/div/div/div[2]/div/label[' + str(
            random.randint(1, 2)) + ']')
        self.assert_element('//*[@id="rc-tabs-1-panel-2"]/div/div')

    @skip
    #大屏画像分析
    def test_analysis(self):
        self.live_screen()

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[2]/div/ul/li[1]')
        self.click('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[1]/div/div/div[1]/div/div[3]/div[2]/div/ul/li['+ str(
            random.randint(1, 2)) +']')
        self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[1]/div/div/div[1]/div/div[3]/div[1]/div/div/div[1]/div/div[1]')
        self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[1]/div/div/div[1]/div/div[3]/div[1]/div/div/div[1]/div/div[2]')

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[2]/div/ul/li[2]')
        self.click('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[1]/div/div/div[2]/div/div[3]/div[2]/div/ul/li['+ str(
            random.randint(1, 2)) +']')
        self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[1]/div/div/div[2]/div/div[3]/div[1]/div/div/div[1]/div/div[1]')
        self.assert_element('//*[@id="root"]/section/div/div/div/div/div/div[3]/div[1]/div/div/div[2]/div/div[3]/div[1]/div/div/div[1]/div/div[2]')

    @skip
    def test_customerdistribution(self):
        self.live_screen()

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[6]/div[4]')
        self.assert_element('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[1]/div/img')
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/button/span')

    @skip
    def test_goodslist(self):
        self.live_screen()

        self.click('//*[@id="root"]/section/div/div/div/div/div/div[2]/div[5]')
        self.assert_element('/html/body/div[3]/div/div/div')
        self.assert_text('商品数据配置选项', '/html/body/div[3]/div/div/div/div[2]/div/div/div[1]/span')
        self.click('/html/body/div[3]/div/div/div/div[2]/button/span')








