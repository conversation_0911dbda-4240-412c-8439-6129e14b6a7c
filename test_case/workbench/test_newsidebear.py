from time import sleep
from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip

from constant.account import get_account_info
from constant.domain import get_domain

# 获取账号信息
account_data = get_account_info("wb_account")
account_data1 = get_account_info("gaozhongshu")
account_data2 = get_account_info("zhangchenghong")


@ddt
class NewSidebear(BaseCase):
    # 登陆
    def land(self):
        self.open("https://s.kwaixiaodian.com/")
        self.driver.maximize_window()
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data2['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data2['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data2['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data2['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        # 新手引导点击
        sleep(8)
        for i in range(1, 6):
            sleep(3)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
                sleep(3)

    # 校验学规减分
    def test_home(self):
        self.land()
        sleep(3)
        self.click('//*[@id="menu_item_W5bfiXQszVg"]/span/div/span')
        sleep(3)
        self.assert_url_contains('https://s.kwaixiaodian.com')

    # 校验订单查询页面
    @skip
    def test_order_query(self):
        self.open("https://s.kwaixiaodian.com/")
        self.driver.maximize_window()
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", account_data2['account'])
        self.type("input[placeholder='请输入密码']", account_data2['password'])
        self.click("button[type='button']")
        sleep(5)
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click('//*[@id="driver-page-overlay"]')
        sleep(3)
        self.click('//*[@id="first_menu_for_intersectionObserver_R553YgpLnhQ"]/span/span')
        sleep(3)
        self.click('//*[@id="menu_item_zAoD7EEcix0"]')
        sleep(3)
        self.assert_text('待发货', '//*[@id="orderPC"]/section/section/main/div/div/div[2]/div/div/div/h1')

    # 校验评价管理页面
    @skip
    def test_evaluation_management(self):
        self.land()
        self.click('//*[@id="menu_item_R553YgpLnhQ"]/span/div/span')
        sleep(3)
        self.click('//*[@id="menu_item_v4vvDi_1eOQ"]')
        sleep(3)
        self.assert_url_contains('https://s.kwaixiaodian.com')

    # 校验快速打单页面
    @skip
    def test_quick_order_printing(self):
        self.land()
        self.click('//*[@id="menu_item_R553YgpLnhQ"]/span/div/span')
        sleep(3)
        self.click('//*[@id="menu_item_JdQhwmS_XOk"]')
        sleep(3)
        self.assert_text('快速打单', '//*[@id="root"]/section/main/div/div[1]/div[1]')

    # 校验跟单助手页面
    @skip
    def test_documentary_assistant(self):
        self.land()
        self.click('//*[@id="menu_item_R553YgpLnhQ"]/span/div/span')
        sleep(3)
        self.click('//*[@id="menu_item_lPLrueBenLk"]')
        sleep(3)
        self.assert_url_contains('https://s.kwaixiaodian.com')

    # 校验回头客说页面
    @skip
    def test_repeat_customer(self):
        self.land()
        self.click('//*[@id="first_menu_for_intersectionObserver_R553YgpLnhQ"]/span/span')
        sleep(3)
        self.click('//*[@id="menu_item_lPLrueBenLk"]')
        sleep(3)
        self.assert_text('差评(0)', '//*[@id="rc-tabs-3-tab-2"]')

    # 校验改地址服务页面
    def test_change_address(self):
        self.land()
        self.click('//*[@id="menu_item_R553YgpLnhQ"]/span/div/span')
        sleep(3)
        self.click('//*[@id="menu_item_wT7GT1ygRUI"]')
        sleep(3)
        self.assert_url_contains('https://s.kwaixiaodian.com')

    # 校验交易设置页面
    def test_transaction_settings(self):
        self.land()
        self.click('//*[@id="menu_item_R553YgpLnhQ"]/span/div/span')
        sleep(3)
        # self.click('//*[@id="menu_item_jWiEoyO0908"]')
        # sleep(3)
        self.assert_url_contains('https://s.kwaixiaodian.com')
        sleep(3)

    # 点击一级菜单'订单'
    def test_Common(self):
        self.land()
        self.click('//*[@id="menu_item_R553YgpLnhQ"]/span/div/span')
        self.assert_url_contains('https://s.kwaixiaodian.com')

    # 一级菜单商品
    @skip
    def test_commodity(self):
        self.land()
        self.click('//*[@id="main-pc-page-layout"]/div[1]/div[1]/div/div/div[4]/div[1]/div')
        self.assert_url_contains('https://s.kwaixiaodian.com')

    # 快手小店->数据tab，跳转到pc端生意通
    def test_data(self):
        self.land()
        sleep(3)
        self.click('//*[@id="menu_item_AXnm3GURXeU"]/span/div/span')
        sleep(3)
        self.assert_url_contains('https://syt.kwaixiaodian.com/')














