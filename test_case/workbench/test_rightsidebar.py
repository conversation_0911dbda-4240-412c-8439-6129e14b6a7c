import random
from time import sleep

from ddt import ddt
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info
from .base import BaseTestCase
from seleniumbase import BaseCase


@ddt
class MyTestClass(BaseTestCase):

    @skip
   # 工作台右侧边栏校验
    def test_platformdynamic(self):
        self.login("WB_DOMAIN", "wb_account")


        self.assert_title("快手小店")

        self.maximize_window()
        self.assert_text("平台动态", '//*[@id="root"]/div/div/div[2]/div[1]/div/div/div/div[1]/div')
        platform_text = self.get_text('div.Mdx5EOEwva582rVkNL32 > div:nth-child(2) > div:nth-child(1)')
        self.assert_in("平台动态", platform_text)
        self.assert_in("最新资讯", platform_text)
        self.assert_in("活动报名", platform_text)
        self.click('div.ant-tabs-nav-list > div:nth-child(1)') #点击最新资讯
        self.assert_element('//*[@id="root"]/div/div/div[2]/div[1]/div/div/div/div[2]/div[2]')
        self.click('//*[@id="rc-tabs-1-panel-0"]/div/div[' + str(random.randint(1,5))+']')
        self.switch_to_window(1)
        self.assert_element('//*[@id="app"]/section/div[1]/header/div/div[1]/div[1]')
        self.sleep(4)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div/div/div/div[2]/div[1]/div[1]/div/div[2]')  # 点击活动报名
        self.click('//*[@id="rc-tabs-1-panel-1"]/div/div[' + str(random.randint(1,3))+']/div/a')
        self.switch_to_window(2)
        self.assert_title("快手小店")
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div/div/div/div[1]/a')
        self.switch_to_window(3)
        self.assert_title("快手小店")
        self.assert_text("全部活动",'//*[@id="root"]/section/section/main/div/div/div[1]/div[2]/div')

    @skip
    def test_learningbase(self):
        self.login("WB_DOMAIN", "wb_account")
        self.click('//*[@id="driver-page-overlay"]')

        self.assert_title("快手小店")

        
        self.maximize_window()
        self.assert_text("学习基地", '//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[1]/div')
        learning_text = self.get_text('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[2]/div[1]/div[1]/div')
        self.assert_in("账号运营", learning_text)
        self.assert_in("流量投放", learning_text)
        self.assert_in("直播带货", learning_text)
        self.click('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div[1]') #点击账号运营
        self.click('//*[@id="rc-tabs-2-panel-0"]/div/div[' + str(random.randint(1,3))+']/div/a')
        self.switch_to_window(1)
        self.assert_title("快手电商学习基地")
        self.assert_element('//*[@id="app"]/section/header/div/div[1]/a')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div[2]') #点击流量投放
        self.click('//*[@id="rc-tabs-2-panel-1"]/div/div[' + str(random.randint(1,3))+']/div/a')
        self.switch_to_window(2)
        self.assert_title("快手电商学习基地")
        self.assert_element('//*[@id="app"]/section/header/div/div[1]/a')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div[3]') #点击直播运营
        self.click('//*[@id="rc-tabs-2-panel-2"]/div/div[' + str(random.randint(1,3))+']/div/a')
        self.switch_to_window(3)
        self.assert_title("快手电商学习基地")
        self.assert_element('//*[@id="app"]/section/header/div/div[1]/a')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[1]/a')
        self.switch_to_window(4)
        self.assert_title("快手电商学习基地")
        self.assert_element('//*[@id="app"]/section/header/div/div[1]/a')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)

    @skip
    def test_servicemarket(self):
        self.login("WB_DOMAIN", "wb_account")
        self.click('//*[@id="driver-page-overlay"]')

        self.assert_title("快手小店")

        self.maximize_window()
        self.click('//*[@id="main-root"]')
        
        self.assert_text("服务市场", '#root > div > div > div:nth-child(2) > div:nth-child(4) > div > div > div > div.BBkwQeqemIVsMea3HY8R > div')
        service_text = self.get_text('#root > div > div > div:nth-child(2) > div:nth-child(4) > div > div > div '
                                     '> div.ant-tabs.ant-tabs-top.SReAfzX4PSJwoAyPevVF > div.ant-tabs-nav > div.ant-tabs-nav-wrap > div')
        self.assert_in("推荐服务", service_text)
        self.assert_in("新品榜单", service_text)
        self.assert_in("我的服务", service_text)
        self.click('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div[1]') #点击推荐服务
        self.click('//*[@id="rc-tabs-3-panel-0"]/div/a[' + str(random.randint(1,4))+']')
        self.switch_to_window(1)
        self.assert_title("服务详情-快手服务市场-快手电商官网")
        self.assert_element('//*[@id="app"]/section/header/div[2]/div/div[1]/div[1]')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div[4]/div/div/div/div[2]/div[1]/div[1]/div/div[2]') #点击新品榜单
        self.click('//*[@id="rc-tabs-3-panel-1"]/div/a[' + str(random.randint(1,4))+']')
        self.switch_to_window(2)
        self.assert_title("服务详情-快手服务市场-快手电商官网")
        self.assert_element('//*[@id="app"]/section/header/div[2]/div/div[1]/div[1]')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div[4]/div/div/div/div[2]/div[1]/div[1]/div/div[3]') #点击我的服务
        #self.assert_text("暂无订购的服务\n 海量工具服务，全面助力经营", '//*[@id="rc-tabs-3-panel-2"]/div/div[1]/div')

        self.click('//*[@id="rc-tabs-3-panel-2"]/div/div[2]/a')
        self.switch_to_window(3)
        self.assert_title("工具服务-快手服务市场-快手电商官网")
        self.assert_element('//*[@id="app"]/section/header/div[2]/div/div[1]/div[1]')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)
        self.click('//*[@id="root"]/div/div/div[2]/div[3]/div/div/div/div[1]/a')
        self.switch_to_window(4)
        self.assert_title("快手电商学习基地")
        self.assert_element('//*[@id="app"]/section/header/div/div[1]/a')
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.sleep(2)

