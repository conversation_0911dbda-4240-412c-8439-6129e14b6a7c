import random
from time import sleep

from ddt import ddt
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info
from .base import BaseTestCase
from seleniumbase import BaseCase


@ddt
class MyTestClass(BaseTestCase):

    @skip
    # 直播日历
    def test_calendar(self):
        self.login("WB_DOMAIN", "wb_account")

        #self.wait_for_element_visible("button.driver-close-btn.driver-close-only-btn")
        #if self.is_element_visible("button.driver-close-btn.driver-close-only-btn"):
        #    self.click("button.driver-close-btn.driver-close-only-btn")  # 运费模块知道了
            
        self.click('//*[@id="driver-page-overlay"]')
        tabs_text = self.get_text('//*[@id="1024"]/div/div/div/div/div[2]/div[1]/div[2]')
        tab_list = ["上个月", "下个月", "回到今天"]
        for item in tab_list:
            self.assert_in(item, tabs_text)
        self.click('//*[@id="1024"]/div/div/div/div/div[2]/div[2]/div[2]/button') #点击建直播计划
        self.assert_text("创建直播计划", "div.ant-drawer-title")
        self.sleep(2)
        self.click('/html/body/div[4]/div/div/div[2]/div/div/div[3]/div/button[1]')
        self.sleep(1)
        '''
        self.click('#root > div > div > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div > div > div > div > div.es-cal-header > div:nth-child(2) > button')
        self.sleep(2)
        self.switch_to_window(1)
        self.assert_title("直播助手")
        live_text = self.get_text('#main-container > header')
        self.assert_in("快手小店直播助手", live_text)
        self.assert_in("直播日历", live_text)
        self.assert_in("直播计划", live_text)
        self.assert_in("跟播助手", live_text)
        self.assert_in("我的直播", live_text)
        self.assert_text("我的直播日历", '//*[@id="main-container"]/section/main/div/div[2]/div/div/div/div[1]/div[1]/div/div')'''

       
        







        
