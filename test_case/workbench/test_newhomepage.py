from time import sleep

from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip
import pytest

from constant.account import get_account_info
from constant.domain import get_domain

# 获取账号信息
account_data = get_account_info("wb_account")
account_data1 = get_account_info("gaozhongshu")
account_data2 = get_account_info("zhangchenghong")
account_data3 = get_account_info("huoyangyang")


@ddt
class HomePage(BaseCase):
    # 登陆
    def land(self):
        host = get_domain("WORKEBENCH_HOME_PAGE")
        self.open(host)
        self.driver.maximize_window()
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data2['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data2['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        sleep(8)
        # 新手引导点击
        for i in range(1, 7):
            sleep(1)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                sleep(1)
                self.click('//*[@id="driver-page-overlay"]')
                sleep(3)
        # 经营助手弹窗检测点击
        # for i in range(1, 3):
        #     sleep(1)
        #     if self.is_element_visible('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]'):
        #         sleep(2)
        #         self.click('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img[2]')
        #         sleep(1)
        # 检测是否在老版工作台，不在的话切换老版工作台
        if self.is_text_visible('返回旧版', '//*[@id="NoviceStageReturnPc#C1"]/div/div/div'):
            self.click('//*[@id="NoviceStageReturnPc#C1"]/div/div/div')
            sleep(2)
            self.click('//div[@class=\'ant-modal-root\']//div[5]')
            sleep(2)
            self.click('//body[1]/div[4]/div[1]/div[2]/div[1]/div[2]/div[3]/button[1]')
            sleep(8)


    # 直播大卡
    def test_Live_broadcast_card(self):
        self.land()
        self.assert_element('//*[@id="live-card-id"]/div')

    # 代办
    def test_todo_itemas(self):
        self.land()
        self.assert_element('//*[@id="TodoListPc#C1"]/div/div/div')
    # 生意通
    def test_businessman(self):
        self.land()
        self.assert_element('//*[@id="seller-home-syt"]')

    # 分销
    @pytest.mark.skip
    def test_distribution(self):
        self.land()
        sleep(10)
        self.assert_element('//*[@id="CpsPc#CpsGoodsSlider"]')

    # 小店信息
    @skip
    def test_store_information(self):
        self.land()
        self.click("//*[@id='first_menu_for_intersectionObserver_W5bfiXQszVg']/span/span")
        self.assert_text('豌豆射手暴打草莓味奥利给', '//*[@class="EUfga_9EZVJOKHyPkwFN"]')

    # 最新资讯
    def test_latest_information(self):
        self.land()
        self.assert_element('//*[@id="LatestNewsPc#C1"]/div/div')

    # 服务市场
    def test_service_market(self):
        self.land()
        self.assert_element('//*[@id="ServiceMarketPc#V1"]/div/div')

    # 学习基地
    def test_university(self):
        self.land()
        self.assert_element('//*[@id="EducationStudyBasePc#UniversityCardConfig"]/div/div/div')

    # 点击学习基地查看更多
    def test_test_university_see_more(self):
        self.land()
        self.click('//*[@id="EducationStudyBasePc#UniversityCardConfig"]/div/div/div/div[1]/a')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(10)
        self.assert_url('https://university.kwaixiaodian.com/kwaishop/index')

    # 点击生意通查看更多
    def test_businessConnect_see_more(self):
        self.land()
        self.click('//*[@id="seller-home-syt"]/div[1]/div[2]')
        self.assert_url_contains('https://syt.kwaixiaodian.com/zones/home')

        # 点击信息模块儿购物体验分
    @skip
    def test_shopping_experience_score(self):
        self.land()
        self.click_xpath('//*[@src="https://ali2.a.kwimgs.com/udata/pkg/se-cdn/youxiu_light.png"]')
        self.assert_text('购物体验分数据概览', '//*[@id="root"]/section/main/div/div/article/div/span')

    # 点击最新资讯查看更多
    def text_information_see_more(self):
        self.land()

    # 点击活动报查看更多
    def text_event_registration_see_more(self):
        self.land()
        self.click_xpath(
            '//*[@href="https://s.kwaixiaodian.com/zone/business-invitation/business/list?source=xingongzoutai"]')
        self.assert_element('//*[@id="main-root"]/div/div/div[1]/div[2]/div[2]/div')

    # 点击服务市场查看更多
    def test_service_market_see_more(self):
        self.land()
        self.click('//*[@id="ServiceMarketPc#V1"]/div/div/div[1]/a')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://fuwu.kwaixiaodian.com/?source=kwaixiaodianhome')

    # 点击电商大学查看更多
    def test_university_see_more(self):
        self.land()
        self.click('//*[@id="EducationStudyBasePc#UniversityCardConfig"]/div/div/div/div[1]/a/span')
        self.assert_url_contains('https://university.kwaixiaodian.com/kwaishop/index')

    # 顶部导航栏鼠标悬浮'快分销'点击商家
    @skip
    def test_fastdistribution_business(self):
        self.open("https://s.kwaixiaodian.com/")
        self.driver.maximize_window()
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", account_data2['account'])
        self.type("input[placeholder='请输入密码']", account_data2['password'])
        self.click("button[type='button']")
        sleep(20)
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/span/button[2]'):
            self.click('//*[@id="driver-popover-item"]/div[4]/span/button[2]')
        if self.is_element_visible('//*[@id="driver-popover-item"]/div[4]/button'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
        if self.is_element_visible('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span'):
            self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span')
        self.hover_and_click('//*[@id="nav-menu-K0Js91FRnRw"]',
                             '//*[@id="main-root"]/div/div/div[1]/div[1]/div/div/div[2]/div[1]/div[1]/div[1]/div/div/div/div[2]/div/div/div[1]/div[1]/div')
        self.assert_element('//*[@id="main_root"]/section/section/main')

    # 店铺信息页重定向跳转
    def test_redirect(self):
        self.open("https://s.kwaixiaodian.com/shop/info/index")
        self.driver.maximize_window()
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data2['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data2['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data2['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data2['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        sleep(10)
        self.assert_url_contains('/zone/shop/info/index')

    # 快手小店（旧）->首页->商城经营，跳转到pc端生意通
    def test_Mall_management(self):
        self.land()
        self.click('//*[@id="MallManage"]/div[2]/div[1]/span')
        self.assert_url_contains('https://syt.kwaixiaodian.com/zones/')

    # 进入搜索页进行搜索#######################################################
    def Search_page_longin(self):
        self.land()
        self.click('//*[@id="seller-main-pc-header"]/div/div/div[1]/div[1]/span/input')
        self.type('//*[@id="seller-main-pc-header"]/div/div/div[1]/div[1]/span/input', '服务')
        # 模拟按下回车键
        self.input_element = self.driver.find_element(By.XPATH, element)  # 初始化 input_element
        self.input_element.send_keys(Keys.RETURN)  # 模拟按下回车键
        #self.click('//*[@id="seller-main-pc-header"]/div/div/div[2]/div[1]/span/span/span')
        #self.assert_element('//*[@id="main-pc-page-layout"]/div[2]/div/div/div/div/div/div/div[1]/div[1]/div')

    # 搜索框-搜索输入服务
    @skip
    def test_Search_page(self):
        self.Search_page_longin()
        self.assert_element('//*[@id="main-pc-page-layout"]/div[2]/div/div/div/div/div/div/div[1]/div[1]/div')

    # 搜索页-点击改应用-地址服务
    @skip
    def test_Search_page_apply(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-panel-All"]/div/div/div/div[1]/div/section/a[1]/span')
        self.assert_url_contains('https://s.kwaixiaodian.com/zone/trade/order/address/approval')

    # 搜索页-点击服务市场tab
    @skip
    def test_Search_page_serve(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-tab-ServicesMarketList"]')
        self.assert_element('//*[@id="rc-tabs-0-panel-ServicesMarketList"]')

    # 搜索页-点击服务市场tab-进入第一服务
    @skip
    def test_Search_page_serve_page(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-tab-ServicesMarketList"]')
        self.click('//*[@id="rc-tabs-0-panel-ServicesMarketList"]/div/div/div/div/div/section/section/section[1]')
        self.assert_url_contains('https://fuwu.kwaixiaodian.com/new/detail')

    # 搜索页-点击大学课程tab
    @skip
    def test_Search_page_university(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-tab-UniversityList"]')
        self.assert_element('//*[@id="rc-tabs-0-panel-UniversityList"]')

    # 搜索页-点击大学课程tab-进入第一个课程
    @skip
    def test_Search_page_university_page(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-tab-UniversityList"]')
        self.click('//*[@id="rc-tabs-0-panel-UniversityList"]/div/div/div/div/div/section/section/section[1]/div[2]/a')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://university.kwaixiaodian.com/')

    # 搜索页-点击规则中心tab
    @skip
    def test_Search_page_rule(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-tab-RulesCenterList"]')
        self.assert_element('//*[@id="rc-tabs-0-panel-RulesCenterList"]')

    # 搜索页-点击规则中心tab-进入第一个规则
    @skip
    def test_Search_page_rule_page(self):
        self.Search_page_longin()
        self.click('//*[@id="rc-tabs-0-tab-RulesCenterList"]')
        self.click('//*[@id="rc-tabs-0-panel-RulesCenterList"]/div/div/div/div/div/section/section/section[1]')
        self.assert_url_contains('https://edu.kwaixiaodian.com/')

    # 点击成长中心查看更多
    # def test_growth_Center_see_more(self):
    #     self.land()
    #     self.click('//*[@id="seller-growth-center"]/div[1]/div[2]')
    #     sleep(10)
    #     self.assert_url('https://s.kwaixiaodian.com/zone/cultivation/growthCenter/growth')

    # 校验营销卡片查看更多
    def test_Marketing_tool(self):
        self.land()
        self.assert_element('//*[@id="MarketingToolPc#C1"]/div/div')

    # 校验营销卡片查看更多
    def test_Marketing_tool_view_more(self):
        self.land()
        self.click('//*[@id="MarketingToolPc#C1"]/div/div/div[1]/div[2]/div')
        self.assert_url_contains('zone/marketing/tools/v2')
























