from time import sleep
from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip

from constant.account import get_account_info
from constant.domain import get_domain

# 获取账号信息
account_data = get_account_info("wb_account")
account_data1 = get_account_info("gaozhongshu")


@ddt
class MyTestClass(BaseCase):
    #工作台首页侧边栏订单管理校验
    @skip
    def test_sidebar_ordermanagement(self):
        self.open("https://prt-eshop-s.test.gifshow.com/")
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        sleep(2)
        self.click('//*[@id="main-root"]')
        self.assert_text("首页", '//*[@id="node_menu_for_intersectionObserver_17"]/div/span[2]')
        self.sleep(1)
        self.click('//*[@id="menu_item_1"]/span/span')
        self.sleep(1)
        self.click('//*[@id="menu_item_2"]/span/span')
        sidebar_text = self.get_text("div[id= 'kwaishop-seller-main-pc-menu']")  # 获取快分销等模块元素对应的text
        sidebar_list = ["首页", "订单管理", "商品管理"]
        for item in sidebar_list:
             self.assert_in(item, sidebar_text)

        self.sleep(3)

        # self.click('//*[@id="node_menu_for_intersectionObserver_1"]/div') # 点击订单管理
        # self.click('//*[@id="node_menu_for_intersectionObserver_1"]/div')
        # order_text = self.get_text('//*[@id="node_menu_for_intersectionObserver_1"]/ul')  # 获取快分销等模块元素对应的text
        # order_list = ["订单查询", "评价管理", "快速打单"]
        # for item in order_list:
        #     self.assert_in(item, order_text)
        # self.click('//*[@id="menu_item_1"]') # 点击订单查询
        # sleep(2)
        # self.assert_text("近6个月订单","li[class = 'ant-menu-item ant-menu-item-selected']")
        # self.assert_text("6个月前订单", "li[class= 'ant-menu-item']")
        # tabs_text = self.get_text("div.order-list-tab")  # 获取快分销等模块元素对应的text
        # tab_list = ["全部", "待付款", "待发货", "已发货", "已收货", "交易成功", "订单关闭"]
        # for item in tab_list:
        #     self.assert_in(item, tabs_text)
        # self.click('//*[@id="menu_item_2"]') # 评价管理
        # sleep(2)
        # self.assert_text("店铺动态评分", '//*[@id="root"]/section/section/main/div/div/div[1]')
        # self.assert_text("来自买家的评价", '//*[@id="root"]/section/section/main/div/div/div[3]')
        # self.click('//*[@id="menu_item_67"]')
        # sleep(2)
        # self.assert_text("快速打单", "div.quick-header")
        # self.click("button[type= 'button']") # 进入服务市场
        # sleep(2)
        # self.switch_to_window(1)
        # self.assert_title("服务列表-快手服务市场-快手电商官网")
        # self.sleep(2)
        # self.switch_to_window(0)
        # self.assert_title("快手小店")
        # sleep(2)
    @skip
    def test_tongzhizhongxin(self):
        self.open("https://prt-eshop-s.test.gifshow.com/")
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", account_data1['account'])
        self.type("input[placeholder='请输入密码']", account_data1['password'])
        self.click("button[type='button']")
        sleep(2)
        self.click('//*[@id="driver-popover-item"]/div[4]/button')
        sleep(1)
        #self.click('//*[@id="driver-popover-item"]/div[4]/button')
        sleep(1)

        self.assert_element('//*[@id="nav-notice-center"]')
        self.click('//*[@id="nav-notice-center"]')
        sleep(2)
        self.assert_text("重要", '//*[@id="rc-tabs-0-tab-0"]/span/span')
        self.assert_text("店铺", '//*[@id="rc-tabs-0-tab-1"]/span/span')
        self.assert_text("交易", '//*[@id="rc-tabs-0-tab-2"]/span/span')
        self.assert_text("官方", '//*[@id="rc-tabs-0-tab-3"]/span/span')
        sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-1"]/span/span')
        sleep(2)
        self.assert_text("消息订阅设置", '//*[@id="kpro-notice-box-warp"]/div/div/div[2]/div/div/div[2]/div[1]/div[1]/div[3]/div/div[2]/span[2]')
        self.click('//*[@id="kpro-notice-box-warp"]/div/div/div[2]/div/div/div[2]/div[1]/div[1]/div[3]/div/div[2]/span[2]')
        sleep(2)
        self.assert_text("违规信息", '//*[@id="kpro-notice-config-warp"]/div/div/div/div/div/div[2]/div/div/div[2]/div/div/div/li[1]/div/div[2]/h4')

    #新版工作台侧边栏
    @skip
    def test_sidebar_new(self):
        self.open("https://eshop-s.prt.kwaixiaodian.com/")
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", account_data1['account'])
        self.type("input[placeholder='请输入密码']", account_data1['password'])
        self.click("button[type='button']")
        sleep(2)
        self.click('//*[@id="driver-popover-item"]/div[4]/button')
        sleep(1)
        for i in range(1, 6):
            sleep(3)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
                sleep(3)
        #self.click('//*[@id="driver-popover-item"]/div[4]/button')
        sleep(1)

        #self.click('/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]/span')


        self.assert_text("首页", '//*[@id="first_menu_for_intersectionObserver_W5bfiXQszVg"]/span/span')
        self.assert_text("订单", '//*[@id="first_menu_for_intersectionObserver_R553YgpLnhQ"]/span/span')
        self.assert_text("商品", '//*[@id="first_menu_for_intersectionObserver_hoM4hVWletc"]/span/span')
        self.assert_text("店铺", '//*[@id="first_menu_for_intersectionObserver_nB1uqX31G8Q"]/span/span')
        sleep(1)
        self.assert_text("快分销", '//*[@id="main-root"]/div/div/div/div[1]/div/div/div[2]/div[1]/div[1]/span[1]/span')
        self.assert_text("服务市场", '//*[@id="main-root"]/div/div/div/div[1]/div/div/div[2]/div[1]/div[1]/span[2]/span')
        self.assert_text("推广中心", '//*[@id="main-root"]/div/div/div/div[1]/div/div/div[2]/div[1]/div[1]/span[3]/span')
        self.assert_text("学习中心", '//*[@id="main-root"]/div/div/div/div[1]/div/div/div[2]/div[1]/div[1]/span[4]/span')
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/div/div/div[2]/div[2]/span/img')
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/div/div/div[2]/div[2]/img')

        sleep(1)
        self.assert_text("常用", '//*[@id="main-root"]/div/div/div/div[1]/div/div/div[1]/div[1]')
        sleep(1)
        #校验
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/div/div/div[1]/div[2]/span')
        sleep(1)
        self.open("https://eshop-s.prt.kwaixiaodian.com/zone/shop/info/index")
        sleep(10)
        self.assert_element('//*[@id="root"]/section/main/div/div[1]/div/div/div/div/div[1]/div[1]')






    @skip
    # 工作台首页侧边栏商品管理校验
    def test_sibebar_goodsmanagement(self):
        self.login("WB_DOMAIN", "wb_account")
        self.click('//*[@id="main-root"]')
        self.click('//*[@id="node_menu_for_intersectionObserver_2"]/div')
        self.sleep(1)
        self.click('//*[@id="node_menu_for_intersectionObserver_2"]/div')
        goods_text = self.get_text('//*[@id="node_menu_for_intersectionObserver_2"]')  # 获取
        goods_list = ["商品列表", "新增商品", "商品数据", "商品导入", "商品诊断"]
        for item in goods_list:
            self.assert_in(item, goods_text)
        self.click('//*[@id="menu_item_91"]') #点击商品列表
        sleep(2)
        tabs_text = self.get_text('//*[@id="root"]/section/main/div')
        tab_list = ["商品ID", "商品标题", "商品价格", "类目名称", "商品状态", "SKU编码", "全部", "在售", "已下架", "审核记录", "已封禁"]
        for item in tab_list:
            self.assert_in(item, tabs_text)
        self.click('//*[@id="menu_item_94"]') #点击新增商品
        sleep(2)
        self.switch_to_window(1)
        self.assert_title("快手小店")
        self.sleep(2)
        self.assert_element('//*[@id="root"]/section/main/div/section/section/main/div[1]')
        self.assert_element('//*[@id="root"]/section/main/div/section/section/main/div[2]')
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.click('//*[@id="menu_item_95"]')  # 点击商品诊断
        sleep(2)
        self.assert_element('//*[@id="main-root"]/div/div/div/div[2]/div[2]/div')
        self.click('//*[@id="menu_item_5"]') #点击商品数据
        sleep(2)
        self.assert_element("div.sc-gsnTZi.fmyhjW.sc-gKXOVf.iGdKXC")
        self.click('//*[@id="menu_item_68"]')  # 点击商品导入
        sleep(2)
        self.assert_element("div.quick-header")
        self.click("button[type= 'button']")  # 进入服务市场
        sleep(3)
        self.switch_to_window(2)
        self.assert_title("服务列表-快手服务市场-快手电商官网")
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        sleep(2)



    @skip
    # 工作台首页侧边栏物流管理校验
    def test_homepage_logisticsmanagement(self):
        self.login("WB_DOMAIN", "wb_account")
        self.click('//*[@id="main-root"]')
        self.click("div.seller-main-spin-container > li:nth-child(5)")
        self.click('//*[@id="menu_item_16"]/span') #点击小额打款
        self.assert_text("小额打款功能介绍", '//*[@id="root"]/section/section/main/div/div/div/div/div[1]')
        self.click('//*[@id="root"]/section/section/main/div/div/div/div/button')
        self.sleep(2)
        tabs_text = self.get_text('//*[@id="root"]/section/section/main/div/div[1]')
        tab_list = ["发起打款", "打款记录", "待处理打款申请", "打款配额"]
        for item in tab_list:
            self.assert_in(item, tabs_text)
