import time
from time import sleep
from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip

from constant.account import get_account_info
from constant.domain import get_domain
import pytest

#  获取账号信息
account_data1 = get_account_info("zhangchenghong")
account_data2 = get_account_info("gaozhongshu1")


class NewNewHomePage(BaseCase):
    def log_in(self):
        host = get_domain("WORKEBENCH_HOME_PAGE")
        self.open(host)
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data1['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data1['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data1['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data1['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        self.driver.switch_to.window(self.driver.window_handles[-1])
        time.sleep(8)
        # 新手引导点击
        for i in range(1, 10):
            time.sleep(1)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                time.sleep(1)
                self.click('//*[@id="driver-page-overlay"]')
                time.sleep(1)
        self.refresh()
        time.sleep(8)
        # 经营助手弹窗检测点击
        # for i in range(1, 3):
        #     sleep(1)
        #     if self.is_element_visible('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]'):
        #         sleep(2)
        #         self.click('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img[2]')
        #         sleep(1)
        # 检测是否在新版工作台，不在的话切换新版工作台
        if self.is_text_visible('体验新版', '//*[@id="NoviceStageReturnPc#C1"]/div/div/div/span'):
            self.click('//*[@id="NoviceStageReturnPc#C1"]/div/div/div')
            # 新手引导点击
            for i in range(1, 10):
                time.sleep(1)
                if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                    time.sleep(1)
                    self.click('//*[@id="driver-page-overlay"]')
                    time.sleep(1)
        self.refresh()
        time.sleep(8)

        # 待办模块儿下发
    def test_todu_distribute(self):
        self.log_in()
        sleep(3)
        self.assert_text('待办事项', '//*[@id="TodoListPc#C2"]/div/div/div/div[1]/div/span')

    # 待付款点击跳转校验
    def test_todu_obligation(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[2]/div[1]/div[1]/div[1]')
        for i in range(1, 3):
            sleep(2)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
                sleep(2)
        self.assert_text('近6个月订单', '//*[@id="rc-tabs-0-tab-1"]')

    # 待付款发货跳转校验
    @skip
    def test_todu_to_be_shipped(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[1]/div[2]/div[2]/span[1]')
        for i in range(1, 3):
            sleep(2)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
                sleep(2)
        self.assert_text('近6个月订单', '//*[@id="rc-tabs-0-tab-1"]')

    # 已发货未揽收点击校验
    def test_todu_shipped_but_not_collected(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[2]/div[1]/div[3]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_text('发货包裹', '//*[@id="rc-tabs-0-tab-1"]')

    # 揽收后未发出点击校验
    def test_todu_not_sent_out_after_collection(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[2]/div[1]/div[3]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_text('发货包裹', '//*[@id="rc-tabs-0-tab-1"]')

    # 售后待处理点击校验
    def test_todu_after_sales_order_inquiry(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[2]/div[1]/div[5]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(5)
        for i in range(1, 4):
            sleep(1)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
        sleep(5)
        self.assert_url_contains("https://s.kwaixiaodian.com/zone/refund/refund-workbench/index")

    # 24小时内逾期点击校验
    @skip
    def test_todu_overdue_within_24hours(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[3]/div[2]/div[2]/span[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(5)
        for i in range(1, 4):
            sleep(1)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                self.click('//*[@id="driver-page-overlay"]')
        sleep(5)
        self.assert_url_contains("https://s.kwaixiaodian.com/zone/refund/refund-workbench/index")

    # 经营预警中心点击校验
    def test_todu_Business_Warning_Center(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[2]/div[1]/div[7]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url('https://s.kwaixiaodian.com/zone/remind/home')

    # 待处理违规点击校验
    @skip
    def test_todu_Pending_violations(self):
        self.log_in()
        self.click('//*[@id="TodoListPc#C2"]/div/div/div/div[4]/div[2]/div[2]/span[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        #self.assert_text('达人', '//*[@id="rc-tabs-0-tab-1"]')

    # 核心数据模块下发校验
    def test_core_data(self):
        self.log_in()
        self.assert_text('核心数据', '//*[@id="BusinessDataPc#DataOverviewV2"]/div/div/div/div[1]/div[1]/span[1]')

    # 校验直播经营模块儿是否下发
    @skip
    def test_Live_streaming_operation(self):
        self.log_in()
        self.assert_text('直播经营', '//*[@id="LiveOperatePc#C1"]/div/div/div[1]/div[1]/span')

    # 校验直播经营模块儿数据概况是否下发
    @skip
    def test_Live_streaming_operation_Data_Profiling(self):
        self.log_in()
        sleep(5)
        self.click('//*[@id="LiveOperatePc#C1"]/div/div/div[2]/div/div[1]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(3)
        self.assert_text('累计成交金额', '//*[@id="kpro-datacard-new-livePayAmt-title"]/span[1]')

    # 点击直播管理模块切换校验
    @skip
    def test_Live_streaming_operation_Live_streaming_management(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="LiveOperatePc#C1"]/div/div/div[2]/div/div[1]/div[2]')
        self.assert_element('//*[@id="rc-tabs-0-panel-2"]/div[2]')

    # 短视频经营模块儿是否展示校验
    @skip
    def test_Short_video_operation(self):
        self.log_in()
        sleep(10)
        self.assert_text('短视频经营', '//*[@id="ShortVideoPc#C1"]/div/div/div[1]/div[1]/span')

    # 短视频成交下游是否挂掉校验
    @skip
    def test_Short_video_transaction(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="ShortVideoPc#C1"]/div/div/div[2]/div/div/div[1]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_text('累计成交金额', '//*[@id="kpro-datacard-new-photoPayAmt-title"]/span[1]')

    # 短视频引流下游是否挂掉
    @skip
    def test_Short_video_streaming(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="ShortVideoPc#C1"]/div/div/div[2]/div/div/div[1]/div[2]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(5)
        self.assert_text('引流成交金额', '//*[@id="kpro-datacard-new-livePayOrderAmt-title"]/span[1]')

    # 商城经营是否下发校验
    @skip
    def test_Mall_operation(self):
        self.log_in()
        self.click('//*[@id="MallGuidancePC#C2"]/div/div/div[2]/div[1]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_text('累计成交金额', '//*[@id="kpro-datacard-new-mallOperation-title"]/span[1]')

    # 分销卖货模块是否下发
    def test_Distribution_sales(self):
        self.log_in()
        self.assert_element('//*[@id="DistributeSellerOperationCyclePc#C1"]/div')

    # 分销卖货右上角查看更多点击校验
    def test_Distribution_sales_see_more(self):
        self.log_in()
        self.click('//*[@id="DistributeSellerOperationCyclePc#C1"]/div/div/div[1]/div[2]/div')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('/zone/cps/base/verification')

    # 店铺服务模块是否下发校验
    def test_Store_Services(self):
        self.log_in()
        self.assert_element('//*[@id="ShopOperationPc#C1"]/div/div')

    # 订单物流模块儿是否下发校验
    def test_Order_Logistics(self):
        self.log_in()
        self.assert_text('订单物流', '//*[@id="OrderLogisticsPc#C1"]/div/div/div[1]/div[1]/span')

    # 客服管理模块下发校验
    def test_The_customer_service_managing(self):
        self.log_in()
        self.assert_text('客服管理', '//*[@id="CustomerServicePc#C1"]/div/div/div[1]/div[1]/span')

    # 售后管理模块下发校验
    def test_After_sales_management(self):
        self.log_in()
        self.assert_text('售后管理', '//*[@id="AfterSalesPc#C1"]/div/div/div[1]/div[1]/span')


    # 点击返回旧版弹出弹窗校验
    def test_Return_to_previous_version(self):
        self.log_in()
        self.click('//*[@id="NoviceStageReturnPc#C1"]/div/div/div/span')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_text('返回旧版', '//*[@id="rcDialogTitle0"]')

    # 流量风向标模块下发校验
    def test_Flow_Weather_vane(self):
        self.log_in()
        sleep(3)
        self.assert_element('//*[@id="FlowWeatherVanePc#C1"]/div/div/div')

    # 流量风向标查看更多点击校验
    def test_Flow_Weather_vane_See_more(self):
        self.log_in()
        self.click('//*[@id="FlowWeatherVanePc#C1"]/div/div/div/div/div[1]/div[2]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(6)
        # 新版页面
        self.assert_url_contains('https://s.kwaixiaodian.com/zone/governance-score/era/store-score-v4-pc')

    # 最新资讯模块下发校验
    def test_Information(self):
        self.log_in()
        self.assert_text('最新资讯', '//*[@id="LatestNewsPc#C2"]/div/div/div[1]/div[1]/span')

    # 最新资讯查看更多点击校验。
    def test_Information_See_more(self):
        self.log_in()
        #self.click('//*[@id="LatestNewsPc#C2"]/div/div/div[1]/div[2]/div/span')
        sleep(5)
        #self.assert_url_contains('https://edu.kwaixiaodian.com/bbs/web/home/<USER>')

    # 营销活动模块下发校验
    def test_Marketing_activities(self):
        self.log_in()
        self.assert_text('营销活动', '//*[@id="ActivityApplyPc#C2"]/div/div/div[1]/div[1]/span')

    # 营销活动查看更多点击校验
    def test_Marketing_activities_See_more(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="ActivityApplyPc#C2"]/div/div/div[1]/div[2]/div/span')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(3)
        self.assert_url_contains('https://s.kwaixiaodian.com/zone/business-invitation/business/list')

    # 已订购服务模块下发校验
    def test_Service_Ordered(self):
        self.log_in()
        self.assert_text('推荐服务', '//*[@id="ServiceMarketPc#V2"]/div/div/div[1]/div[1]/span')

    # 已订购服务去服务市场选购校验
    def test_Service_Ordered_See_more(self):
        self.log_in()
        self.click('//*[@id="ServiceMarketPc#V2"]/div/div/div[2]/div/div[3]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(10)
        # todo
        # self.assert_text('首页', '//*[@id="main_root"]/div/div[2]/div/div[3]/span')

    # 学习基地模块下发校验
    def test_Learning_Base(self):
        self.log_in()
        self.assert_element('//*[@id="EducationStudyBasePc#UniversityCardConfig"]/div/div/div')

    # 学习基地查看更多点击校验
    def test_Learning_Base_See_more(self):
        self.log_in()
        self.click('//*[@id="EducationStudyBasePc#UniversityCardConfig"]/div/div/div/div[1]/a/span')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        sleep(3)
        self.assert_url_contains('https://university.kwaixiaodian.com/kwaishop/index')

    # 学习基地tab切换校验
    @skip
    def test_Learning_Base_tab(self):
        self.log_in()
        self.click('//*[@id="rc-tabs-3-tab-recoCourse"]')
        self.click('//*[@id="rc-tabs-3-tab-hotCourse"]')
        self.click('//*[@id="rc-tabs-3-tab-question"]')
        self.assert_element('//*[@id="StudyBasePc#UniversityCardConfig"]/div/div/div')

    # 顶部导航快分销验证
    def test_The_Top_Navigation_Distribution(self):
        self.log_in()
        self.assert_text('快分销', '//*[@id="menu_item_K0Js91FRnRw"]')

    # 顶部导航服务市场验证
    def test_The_Top_Navigation_Market(self):
        self.log_in()
        self.assert_text('服务市场', '//*[@id="menu_item_3pdNxdyYy80"]')

    # 顶部导航磁力金牛验证
    def test_The_Top_Navigation_Taurus(self):
        self.log_in()
        self.assert_text('磁力金牛', '//*[@id="menu_item_u50Zu-rWcFI"]')

    # 顶部导航学习中心验证
    def test_The_Top_Navigation_University(self):
        self.log_in()
        self.assert_text('学习中心', '//*[@id="menu_item_He6OcPr-Wgo"]')

    # 顶部导航搜索验证
    def test_The_Top_Navigation_Search(self):
        self.log_in()
        self.assert_element('//*[@id="seller-main-pc-header"]/div/div/div[2]/div[1]')

    # 顶部导航通知中心验证
    def test_The_Top_Navigation_Notice(self):
        self.log_in()
        self.assert_element('//*[@id="seller-main-pc-header"]/div/div/div[2]/div/div[1]/div[2]/div/span/div/span')

    # 顶部客户端下载验证
    def test_The_Top_Navigation_Client_download(self):
        self.log_in()
        self.assert_element('//*[@id="seller-main-pc-header"]/div/div/div[2]/div/div[1]/div[4]/div[1]/div')

    # 点击核心数据查看更多跳转生意通
    def test_Core_Data_View_For_More(self):
        self.log_in()
        time.sleep(1)
        self.click('//*[@id="BusinessDataPc#DataOverviewV2"]/div/div/div/div[1]/div[2]/span')
        time.sleep(1)
        self.driver.switch_to.window(self.driver.window_handles[-1])
        time.sleep(1)
        self.assert_url_contains('https://syt.kwaixiaodian.com/zones/home')

    # 点击直播经营累计成交金额
    @skip
    def test_The_Cumulative_transaction_amount_of_live_broadcast_operation(self):
        self.log_in()
        sleep(2)
        self.click('//*[@id="LiveOperatePc#C1"]/div/div/div[2]/div/div[1]/div[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.click('//*[@id="kpro-datacard-new-livePayAmt-title"]/span[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://syt.kwaixiaodian.com/zones')

    # 快手小店（新）->首页->直播经营->数据概览/扶摇计划，跳转到pc端生意通
    @skip
    def test_Shake_the_plan(self):
        self.log_in()
        self.click('//*[@id="LiveOperatePc#C1"]/div/div/div[2]/div/div[1]/div[3]')
        self.click('//*[@id="rc-tabs-0-panel-3"]/div/div[3]/div')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://syt.kwaixiaodian.com/')

    # 快手小店（新）->首页->商城经营，跳转到pc端生意通
    @skip
    def test_Mall_management(self):
        self.log_in()
        self.click('//*[@id="MallGuidancePC#C2"]/div/div/div[2]/div[1]/div[1]')
        self.click('//*[@id="kpro-datacard-new-mallOperation-title"]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://syt.kwaixiaodian.com/')

    # 快手小店（新）->首页->客服管理，跳转到pc端生意通
    @skip
    def test_Customer_service_management(self):
        self.log_in()
        self.click('//*[@id="MallGuidancePC#C2"]/div/div/div[2]/div[1]/div[1]')
        self.click('//*[@id="kpro-datacard-new-mallOperation-title"]/span[1]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://syt.kwaixiaodian.com/')

    # 快手小店（新）->首页->售后管理，跳转到pc端生意通
    def test_customer_service(self):
        self.log_in()
        self.click('//*[@id="kpro-datacard-new-退款率-title"]')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('https://syt.kwaixiaodian.com/')

    # 校验营销卡片是否存在
    @skip
    def test_Marketing_tool(self):
        self.log_in()
        self.assert_element('//*[@id="MarketingToolPc#C1"]/div/div')

    # 校验营销卡片查看更多
    @skip
    def test_Marketing_tool_view_more(self):
        self.log_in()
        self.click('//*[@id="MarketingToolPc#C1"]/div/div/div[1]/div[2]/div')
        self.driver.switch_to.window(self.driver.window_handles[-1])
        self.assert_url_contains('zone/marketing/tools/v2')










































