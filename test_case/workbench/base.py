from time import sleep

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain


class BaseTestCase(BaseCase):

    def login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click("(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

        # self.wait_for_element_visible("button.driver-close-btn.driver-close-only-btn")
        # if self.is_element_visible("button.driver-close-btn.driver-close-only-btn"):
        #     self.click("button.driver-close-btn.driver-close-only-btn")  # 运费模块知道了

    def child_login(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)

        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        #self.assert_text("账号登录", "div.choseTab--1_6F7 > div:nth-child(1)")  # div标签中的第一个元素
        #self.assert_text("验证码登录", "div.choseTab--1_6F7 > div:nth-child(2)")
        #self.click("div.choseTab--1_6F7 > div:nth-child(1)")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        sleep(2)
        self.wait_for_element_visible("button.driver-close-btn.driver-close-only-btn")
        if self.is_element_visible("button.driver-close-btn.driver-close-only-btn"):
            self.click("button.driver-close-btn.driver-close-only-btn")  # 运费模块知道了

    def map_user_id(self, user_id):
        self.click("img[class='head']")
        self.click("div.menu-blue")
        self.type("input[id='rc_select_13']", user_id)
        self.sleep(2)
        self.send_keys("input[id='rc_select_13']", '\ue007')
        self.click("div.ant-modal-footer button.ant-btn.ant-btn-primary")
