import random
from time import sleep

from ddt import ddt
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info
from .base import BaseTestCase
from seleniumbase import BaseCase


@ddt
class MyTestClass(BaseTestCase):

    @skip
   # 工作台首页顶栏校验
    def test_homepage_overview(self):
        self.login("WB_DOMAIN", "wb_account")

        self.assert_title("快手小店")
        self.click('//*[@id="main-root"]')
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/header/img')  # 判断快手小店模块元素是否存在
        tabs_text = self.get_text("div.seller-main-space.seller-main-space-horizontal.seller-main-space-align-center")  # 获取快分销等模块元素对应的text
        tab_list = ["快分销", "服务市场", "学习基地", "商友圈", "商家端APP", "通知中心"]
        for item in tab_list:
            self.assert_in(item, tabs_text)
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/header/div[1]')
        self.type('//*[@id="main-root"]/div/div/div/div[1]/header/div[1]/input', "7616购物节")  # 搜索框输入
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[1]/button')  # 点击搜索按钮

    @skip
    def test_homepage_notificationcenter(self):
        self.login("WB_DOMAIN", "wb_account")
        sleep(2)
        self.click('//*[@id="main-root"]')
        if self.is_element_visible("div[id= 'driver-popover-item']"):
            self.click("button.driver-close-btn.driver-close-only-btn")  # 运费模块知道了
            sleep(2)
        elif self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(2)
        else:
            self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/header/img')

        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[3]/div[6]')  #点击通知中心
        sleep(2)
        self.switch_to_window(1)
        self.assert_title("通知中心")
        self.assert_text("通知中心", '//*[@id="app"]/section/section/main/div/section/header')
        notification_text = self.get_text("aside[class= 'el-aside']")
        self.assert_in("全部消息", notification_text)
        self.assert_in("重要通知", notification_text)
        self.assert_in("交易信息", notification_text)
        self.assert_in("违规信息", notification_text)
        self.assert_in("平台动态", notification_text)
        self.assert_in("店铺动态", notification_text)
        self.click('#app > section > section > aside > div > ul > li:nth-child(5)') #点击店铺动态
        sleep(2)
        self.click('//*[@id="app"]/section/section/main/div/section/div/div[2]/div[1]/div[3]/table/tbody/tr[' + str(random.randint(1,10))+']/td[2]/div/div/div[2]/div[1]')
        sleep(2)
        self.assert_text("重要通知", '//*[@id="app"]/section/section/main/div/section/header/span[2]')
        self.switch_to_window(0)
        self.assert_title("快手小店")
        sleep(2)
        if self.is_element_visible("div[id= 'driver-popover-item']"):
            self.click("button.driver-close-btn.driver-close-only-btn")  # 运费模块知道了
            sleep(2)
        elif self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.click('//*[@id="driver-popover-item"]/div[4]/button')
            self.sleep(2)
        else:
            self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/header/img')
            
        self.maximize_window()
        self.sleep(2)
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[5]') #点击账号信息
        self.sleep(2)
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/header/div[6]/div/div')
        account_text = self.get_text('//*[@id="main-root"]/div/div/div/div[1]/header/div[6]/div/div')
        self.assert_in("小店星级", account_text)
        self.assert_in("小店分数", account_text)
        self.assert_in("商品质量", account_text)
        self.assert_in("客服服务", account_text)
        self.assert_in("物流速度", account_text)
        self.assert_in("售后体验", account_text)
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[6]/div/div/div/div[3]/a') #点击店铺设置
        self.sleep(2)
        self.switch_to_window(2)
        self.assert_title("快手小店")
        self.click('//*[@id="main-root"]')
        self.sleep(2)
        #self.click("button.driver-close-btn.driver-close-only-btn")
        #self.click('#main-root > div > div > div > div.src-Components-Layout-index-module__header--ds2c9 > header > div:nth-child(7) > '
        #           'div > div > div > div.src-Components-Layout-Header-components-Avatar-components-MainUserInfo-index-module__actions--tAOsN > a > span')
        #sleep(2)
        tabs_text = self.get_text("div.ant-tabs-nav-wrap")
        tab_list = ["基础信息", "主体信息", "品牌资质", "店铺经营许可"]
        for item in tab_list:
            self.assert_in(item, tabs_text)
        self.sleep(2)
        self.switch_to_window(0)
        self.assert_title("快手小店")
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[5]')  # 点击账号信息
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[6]/div/div/div/div[3]/button') #点击退出
        self.assert_element('/html/body/div[5]/div/div[2]/div/div[2]')
        self.click('/html/body/div[5]/div/div[2]/div/div[2]/div/div/div[2]/button[1]') #点击取消
        self.assert_element('//*[@id="main-root"]/div/div/div/div[1]/header/img')
        self.sleep(2)
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[5]')  # 点击账号信息
        self.sleep(2)
        self.click('//*[@id="main-root"]/div/div/div/div[1]/header/div[6]/div/div/div/div[3]/button')  # 点击退出
        self.assert_element('/html/body/div[5]/div/div[2]/div/div[2]')
        self.click('/html/body/div[5]/div/div[2]/div/div[2]/div/div/div[2]/button[2]')  # 点击退出
        self.sleep(2)
        self.assert_element('//*[@id="root"]/div/div[1]')







