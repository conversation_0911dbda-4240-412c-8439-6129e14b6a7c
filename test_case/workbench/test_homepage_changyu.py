import time
from time import sleep
from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip

from constant.account import get_account_info
from constant.domain import get_domain
import pytest

#  获取账号信息
account_data1 = get_account_info("zhangchenghong")
account_data2 = get_account_info("gaozhongshu1")


class NewNewHomePage(BaseCase):
    def log_in(self):
        host = get_domain("WORKEBENCH_HOME_PAGE")
        self.open(host)
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data1['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data1['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data1['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data1['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        self.driver.switch_to.window(self.driver.window_handles[-1])
        time.sleep(8)
        # 新手引导点击
        for i in range(1, 10):
            time.sleep(1)
            if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                time.sleep(1)
                self.click('//*[@id="driver-page-overlay"]')
                time.sleep(1)
        self.refresh()
        time.sleep(8)
        # 经营助手弹窗检测点击
        # for i in range(1, 3):
        #     sleep(1)
        #     if self.is_element_visible('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]'):
        #         sleep(2)
        #         self.click('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img[2]')
        #         sleep(1)
        # 检测是否在新版工作台，不在的话切换新版工作台
        if self.is_text_visible('体验新版', '//*[@id="NoviceStageReturnPc#C1"]/div/div/div/span'):
            self.click('//*[@id="NoviceStageReturnPc#C1"]/div/div/div')
            # 新手引导点击
            for i in range(1, 10):
                time.sleep(1)
                if self.is_element_visible('//*[@id="driver-page-overlay"]'):
                    time.sleep(1)
                    self.click('//*[@id="driver-page-overlay"]')
                    time.sleep(1)
        self.refresh()
        time.sleep(8)

        # 场域tab下发
    def test_changyu_page(self):
        self.log_in()
        sleep(3)
        self.assert_text('直播卖货', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[1]/span')
        self.assert_text('短视频卖货', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[2]/span')
        self.assert_text('货架售卖', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[3]/span')
        self.assert_text('达人带货', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[4]/span')

    def test_changyu_duanshipin(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[2]/span')
        sleep(1)
        self.assert_text('发布短视频', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/section/section[1]/div[1]/div[1]/button[1]/span')
        self.assert_text('直播切片管理', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/section/section[1]/div[1]/div[1]/button[2]/span')
        self.assert_text('短视频数据', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/section/section[2]/header/div/span[1]')


    def test_changyu_huojiashoumai(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[3]/span')
        sleep(1)
        self.assert_text('上架即热销', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/div/div[1]/div[1]/span')
        self.assert_text('平台活动', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/div/div[2]/div[1]/span')
        self.assert_text('超级链接竞价', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/div/div[3]/div[1]/span')


    def test_changyu_fenxiao(self):
        self.log_in()
        sleep(3)
        self.click('//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[1]/div/div[4]/span')
        sleep(1)
        self.assert_text('暂无分销权限', '//*[@id="XiaoWeiDomainPc#C1"]/div/div/div[2]/div/div/div/div[1]/div')