from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip

from constant.account import get_account_info
from constant.domain import get_domain



# 获取账号信息
account_data = get_account_info("child_account")
# 用户名 account_data['account']
# 密码 account_data['password']
host = get_domain("WB_DOMAIN")


@ddt
class MyTestClass(BaseCase):
    def login_child_page(self):
        self.open(host)

        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.assert_text("账号登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')  # div标签中的第一个元素
        self.assert_text("验证码登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')

    #子账号账号+密码登录
    @skip
    def test_child_login_success(self):
        self.login_child_page()
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        self.sleep(1)
        self.assert_title("快手小店")

    #子账号账号+验证码登录
    @skip
    def test_child_login_success_by_yanzhengma(self):
        self.login_child_page()
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.type("input[placeholder='请输入手机号']", "***********")
        self.type("input[placeholder='请输入短信验证码']", "255697")
        self.click("button[type='button']")
        self.sleep(1)
        self.assert_title("快手小店")



    @skip
    @data((account_data['account']))
    def test_child_login_fail2(self, account):
        self.login_child_page()
        self.type("input[placeholder='请输入手机号']", account)
        self.type("input[placeholder='请输入密码']", "123")
        self.click("button[type='button']")
        self.assert_text("请输入至少 8 位的密码", '//*[@id="root"]/div/div[2]/div/div/div/div[4]/form/div[2]/div/div/div')

    @skip
    def test_child_login_fail3(self):
        self.login_child_page()
        self.type("input[placeholder='请输入手机号']", "123456")
        self.type("input[placeholder='请输入密码']", "123456")
        self.click("button[type='button']")
        self.assert_text("请输入11位手机号", '//*[@id="root"]/div/div[2]/div/div/div/div[4]/form/div[1]/div/div/div')  # 请输入11位电话号码
        self.assert_text("请输入至少 8 位的密码", '//*[@id="root"]/div/div[2]/div/div/div/div[4]/form/div[2]/div/div/div')

    @skip
    @data((account_data['account']))
    def test_child_login_fail4(self, account):
        self.login_child_page()
        self.type("input[placeholder='请输入手机号']", account)
        self.type("input[placeholder='请输入密码']", "*********")
        self.click("button[type='button']")
        self.sleep(0.5)
        ant_msg = self.get_text('/html/body/div[3]/div/div')
        self.assert_in("帐号不存在或密码错误", ant_msg)
