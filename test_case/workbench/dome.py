from time import sleep
from ddt import ddt, data, unpack
from seleniumbase import BaseCase
from unittest import skip

from constant.account import get_account_info
from constant.domain import get_domain

#  获取账号信息

account_data = get_account_info("gaozhongshu1")


class NewNewHomePage(BaseCase):
    def land(self):
        self.open("https://s.kwaixiaodian.com/")
        self.driver.maximize_window()
        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        # if self.is_element_visible('//*[@id="driver-popover-item"]/div[2]'):
        sleep(3)
        self.click('//*[@id="driver-page-overlay"]')

    # 已订购服务去服务市场选购校验
    def test_Service_Ordered_See_more(self):
        self.land()
        self.click('//*[@id="ServiceMarketPc#C2"]/div/div/div[2]/div/div[3]')
        sleep(3)
        self.assert_text('同行在看:', '//*[@id="app"]/div/section/header/div[2]/div/div[2]/ul/li[1]')


















