import random
from time import sleep
from ddt import ddt
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info
from .base import BaseTestCase
from seleniumbase import BaseCase


@ddt
@skip
class MySubAccountd(BaseTestCase):

    # 子账号工作台首页侧边栏订单管理校验-评价管理
    @skip
    def test_sub_account_evaluation_management(self):
        # 登陆子账号
        self.child_login("WB_DOMAIN", "child_account")
        # 断言是否登陆成功
        self.assert_text("詹乐子账号", "//*[@id='main-root']/div/div/div/div[1]/header/div[5]/div[2]/div/div[1]")
        # 点击订单管理
        sleep(3)
        self.click("//*[@id='menu_item_2']/span/span")
        # 断言是否进入订单管理页面
        sleep(3)
        self.assert_text("店铺动态评分", "//*[@id='root']/section/section/main/div/div/div[1]/span[1]")
        self.assert_text("来自买家的评价", '//*[@id="root"]/section/section/main/div/div/div[3]')

    # 子账号工作台首页侧边栏订单管理校验-新增商品
    @skip
    def test_sub_account_new_product(self):
        # 登陆詹乐的子账号
        self.child_login("WB_DOMAIN", "child_account")
        # 断言是否登陆成功
        self.assert_text("詹乐子账号", "//*[@id='main-root']/div/div/div/div[1]/header/div[5]/div[2]/div/div[1]")
        # 点击新增商品
        self.click("//*[@id='menu_item_94']/span/span")
        # 断言进入新增商品页面
        self.switch_to_window(1)
        self.assert_text("填写商品类目", '//*[@id="root"]/section/main/div/section/section/main/div[1]/div/div[1]')
        self.sleep(2)

    # 子账号工作台 侧边栏订单管理校验-小额打款
    @skip
    def test_sub_account_small_amount_payment(self):
        # 登陆詹乐的子账号
        self.child_login("WB_DOMAIN", "child_account")
        # 断言是否登陆成功
        self.assert_text("詹乐子账号", "//*[@id='main-root']/div/div/div/div[1]/header/div[5]/div[2]/div/div[1]")
        # 点击小额打款
        sleep(3)
        self.click("//*[@id='menu_item_16']/span/span")
        # 断言是否进入小额打款页面
        sleep(3)
        self.assert_text("小额打款功能介绍", "//*[@id='root']/section/section/main/div/div/div/div/div[1]")
        self.click('//*[@id="root"]/section/section/main/div/div/div/div/button')
        self.sleep(2)
        tabs_text = self.get_text('//*[@id="root"]/section/section/main/div/div[1]')
        tab_list = ["发起打款", "打款记录", "待处理打款申请", "打款配额"]
        for item in tab_list:
            self.assert_in(item, tabs_text)