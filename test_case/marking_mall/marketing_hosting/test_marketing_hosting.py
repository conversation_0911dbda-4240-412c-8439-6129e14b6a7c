from time import sleep

from selenium.webdriver.common.by import By
from ..base import BaseTestCase


# 登陆托管总览
class TestMarketingHosting(BaseTestCase):

    # 登陆托管总览
    def test_enter_marketing_hosting(self):
        self.login(domain="MARKETING_HOSTING_DOMAIN", account="bjfeng")

    # 整体商城数据
    def test_total_mall_data(self):
        self.test_enter_marketing_hosting()
        self.assert_text("整体商城数据", "//div[contains(text(),'整体商城数据')]")
        self.assert_text("在架商品数", "//span[contains(text(),'在架商品数')]")
        self.assert_text("动销商品数", "//span[contains(text(),'动销商品数')]")
        self.assert_text("商城成交金额", "//span[contains(text(),'商城成交金额')]")
        self.assert_text("商城订单量", "//span[contains(text(),'商城订单量')]")

        expected_url = "https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation"
        self.check_jump_link("//a[contains(text(), '查看更多')]", "查看更多", expected_url,
                             self.test_enter_marketing_hosting, need_login=False, has_href=True, need_click=True)

    # 商城托管收益
    def test_mall_hosting_income(self):
        self.test_enter_marketing_hosting()
        self.assert_text("商城托管收益", "//span[contains(text(),'商城托管收益')]")
        # 流量扶持
        self.assert_text("流量扶持", "//div[contains(text(),'流量扶持')]")
        # self.assert_text("已获流量扶持商品数", "//div[contains(text(),'已获流量扶持商品数')]")
        # 活动代报
        self.assert_text("活动代报", "//div[contains(text(),'活动代报')]")
        # 素材优化
        self.assert_text("素材优化", "//div[contains(text(),'素材优化')]")
        self.assert_text("累计为你优化商品数", "//div[contains(text(),'累计为你优化商品数')]")
        # 爆款链接保护
        self.assert_text("爆款链接保护", "//div[contains(text(),'爆款链接保护')]")
        self.assert_text("累计为你保护商品数", "//div[contains(text(),'累计为你保护商品数')]")

        assert len(self.find_elements("//div[contains(text(),'累计成交额')]")) == 3
        assert len(self.find_elements("//div[contains(text(),'累计订单量')]")) == 3

    # 商品托管明细

    def test_mall_hosting_detail(self):
        self.test_enter_marketing_hosting()
        self.assert_text("商品托管明细", "//div[contains(text(),'商品托管明细')]")
        self.assert_text("活动代报说明：若涉及价格采集，系统会重点标记采集的价格、提示风险，若无价格展示则代表不涉及价格问题",
                         "//span[contains(text(),'活动代报说明')]")

        # 筛选项
        self.assert_text("类目名称", "//label[contains(text(),'类目名称')]")
        self.assert_text("商品名称", "//label[contains(text(),'商品名称')]")
        self.assert_text("商品ID", "//label[contains(text(),'商品ID')]")
        self.assert_text("商品状态", "//label[contains(text(),'商品状态')]")
        self.assert_text("是否获得权益", "//label[contains(text(),'是否获得权益')]")
        self.assert_text("权益是否涉及价格采集", "//label[contains(text(),'权益是否涉及价格采集')]")
        self.assert_text("是否新品", "//label[contains(text(),'是否新品')]")

        # 表格列名称
        self.assert_text("商品信息", "//span[contains(text(),'商品信息')]")
        self.assert_text("商品在售状态", "//span[contains(text(),'商品在售状态')]")
        self.assert_text("商城推荐准入", "//span[contains(text(),'商城推荐准入')]")
        self.assert_text("爆款链接保护", "//span[contains(text(),'爆款链接保护')]")
        self.assert_text("流量扶持权益", "//span[contains(text(),'流量扶持权益')]")
        self.assert_text("活动代报", "//span[contains(text(),'活动代报')]")
        self.assert_text("商品素材优化", "//span[contains(text(),'商品素材优化')]")
        # self.assert_text("商城曝光次数", "//span[contains(text(),'商城曝光次数')]")
        # self.assert_text("商城曝光点击率", "//span[contains(text(),'商城曝光点击率')]")
        # self.assert_text("商城订单量", "//span[contains(text(),'商城订单量')]")
        # self.assert_text("商城成交额", "//span[contains(text(),'商城成交额')]")
        # self.assert_text("操作", "//span[contains(text(),'操作')]")



