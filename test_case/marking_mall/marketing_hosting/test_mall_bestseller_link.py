import time

import pytest
from _pytest.skipping import Skip

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class TestMallBestSellerLink(BaseTestCase):

    bestseller_link_domain = "BESTSELLER_LINK_DOMAIN"
    account_login = "xiaohs"

    def test_mall_bestseller_link_init(self):
        self.maximize_window()
        self.login(self.bestseller_link_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)

    def test_mall_bestseller_link_ele(self):
        self.test_mall_bestseller_link_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        # 验证头部文字
        self.wait_for_element_visible('//*[@id="dilu_micro_root"]/div/div/div', "css xpath", 4)
        self.assert_text("快手商城「爆品链接运营」重磅上线！洞察爆品机会，高效运营，让好货卖爆更轻松！", '//*[@id="dilu_micro_root"]/div/div/div/div[1]/span')
        self.assert_text("即将打爆品->爆品->爆品流失各阶段经营诊断提醒，官方多重流量加持，延长爆品生命周期，引爆销量！", '//*[@id="dilu_micro_root"]/div/div/div/div[1]/div/span')
        self.assert_text("爆品链接系统说明", '//*[@id="dilu_micro_root"]/div/div/div/div[1]/div/div/span')

        # 验证整体数据tab
        total_data_tab_xpath= '//*[@id="dilu_micro_root"]/div/div/div/div[2]/div/div/div/div[1]'
        self.assert_text("在架爆品", total_data_tab_xpath)
        self.assert_text("即将出爆", total_data_tab_xpath)
        self.assert_text("爆品下架与流失", total_data_tab_xpath)

        # 验证整体数据指标卡
        total_card_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div[2]/div/div/div/div[2]/div/div/div'
        self.assert_text("在架爆品数", total_card_xpath)
        self.assert_text("常挂爆品数", total_card_xpath)
        self.assert_text("爆品成交金额", total_card_xpath)
        self.assert_text("爆品订单量", total_card_xpath)
        self.assert_text("整体", total_card_xpath)
        self.assert_text("自营商品卡", total_card_xpath)

        # 验证爆品商品明细列表信息
        list_title_xpath = '//*[@id="hot-table"]/div[1]/div/div[1]/div/div/div[1]/div/div'
        self.assert_text("全量爆品商品明细", list_title_xpath)
        self.assert_text("曝光次数~订单量、及对应经营问题列,统计时间", list_title_xpath)
        list_search_xpath = '//*[@id="hot-table"]/div[1]/div/div[1]/div/div/div[2]'
        self.assert_text("商品ID", list_search_xpath)
        self.assert_text("商品名称", list_search_xpath)
        self.assert_text("爆品阶段", list_search_xpath)
        self.assert_text("商品在售状态", list_search_xpath)
        self.assert_text("品类", list_search_xpath)
        self.assert_text("是否常挂", list_search_xpath)
        list_table_title_xpath = '//*[@id="tableId"]/div/div[1]'
        self.assert_text("商品信息", list_table_title_xpath)
        self.assert_text("商品在售状态", list_table_title_xpath)
        self.assert_text("爆品阶段", list_table_title_xpath)
        self.assert_text("爆品异动预警", list_table_title_xpath)
        self.assert_text("曝光次数", list_table_title_xpath)
        self.assert_text("点击次数", list_table_title_xpath)
        self.assert_text("曝光点击率(CTR)", list_table_title_xpath)
        self.assert_text("成交金额", list_table_title_xpath)
        # self.assert_text("订单量", list_table_title_xpath)
        # self.assert_text("商城权益及活动情况", list_table_title_xpath)
        # self.assert_text("链接常挂", list_table_title_xpath)
        # self.assert_text("经营问题分析", list_table_title_xpath)
        # self.assert_text("经营建议", list_table_title_xpath)
        self.assert_text("操作", list_table_title_xpath)



