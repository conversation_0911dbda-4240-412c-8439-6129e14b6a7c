import time

import pytest
from _pytest.skipping import Skip

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class TestMallBestSellerLink(BaseTestCase):

    search_zone_domain = "SEARCH_OPERATIONS_DOMAIN"
    account_login = "xiaoh<PERSON><PERSON><PERSON>"

    def test_mall_search_zone_init(self):
        self.maximize_window()
        self.login(self.search_zone_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)

    def test_mall_search_zone_ele(self):
        self.test_mall_search_zone_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        # 验证整体数据title
        total_data_title_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div/div/div/div[1]'
        self.assert_text("搜索数据", total_data_title_xpath)
        self.assert_text("搜索运营指南", total_data_title_xpath)
        self.assert_text("对比周期", total_data_title_xpath)

        # 验证整体数据指标卡
        total_card_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div/div/div/div[2]/div[1]'
        self.assert_text("", total_card_xpath)
        self.assert_text("全店搜索成交金额", total_card_xpath)
        self.assert_text("商品卡搜索成交金额", total_card_xpath)
        self.assert_text("直播间搜索成交金额", total_card_xpath)
        self.assert_text("短视频搜索成交金额", total_card_xpath)
        self.assert_text("其他搜索成交金额", total_card_xpath)

        # 验证整体数据漏斗
        total_card_pic_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div/div/div/div[2]/div[2]'
        self.assert_text("搜索曝光次数", total_card_pic_xpath)
        self.assert_text("全店搜索流量漏斗", total_card_pic_xpath)
        self.assert_text("搜索点击次数", total_card_pic_xpath)
        self.assert_text("搜索成交订单数", total_card_pic_xpath)

        # 验证行业搜索词
        search_word_title_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]'
        self.assert_text("行业搜索词", search_word_title_xpath)
        search_word_search_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[2]/div/div/div/div[2]/div[1]/div/div[1]/div/div/div/div/div'
        self.assert_text("热度高", search_word_search_xpath)
        self.assert_text("增速快", search_word_search_xpath)
        self.assert_text("竞争小", search_word_search_xpath)
        self.assert_text("搜索意图", search_word_search_xpath)
        self.assert_text("行业类目", search_word_search_xpath)
        # 验证行业搜索词列表
        list_search_head_xpath = '//*[@id="tableId"]/div/div[1]/table/thead'
        self.assert_text("排名", list_search_head_xpath)
        self.assert_text("搜索词", list_search_head_xpath)
        self.assert_text("搜索次数", list_search_head_xpath)
        self.assert_text("搜索商品曝光次数", list_search_head_xpath)
        self.assert_text("词下热门商品", list_search_head_xpath)
        self.assert_text("本店曝光商品", list_search_head_xpath)
        self.assert_text("操作", list_search_head_xpath)

        # 验证商品优化
        product_title_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[3]/div/div/div/div[1]/div/div[1]/div/div/div[1]/div/div/div/div[1]'
        self.assert_text("商品优化", product_title_xpath)
        product_search_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[3]/div/div/div/div[1]/div/div[1]/div/div/div[2]'
        self.assert_text("商品ID", product_search_xpath)
        self.assert_text("商品名称", product_search_xpath)
        product_thead_xpath = '/html/body/div[1]/div/div/div/div[2]/div[2]/div/div/div/div/div/div/div/div/div[3]/div/div/div/div[1]/div/div[2]/div/div/div/div/div/div/div[1]/table/thead'
        self.assert_text("配置商品", product_thead_xpath)
        self.assert_text("搜索曝光Top3搜索词", product_thead_xpath)
        self.assert_text("商品成交金额（元）", product_thead_xpath)
        # self.assert_text("商品成交订单数	", product_thead_xpath)
        self.assert_text("操作", product_thead_xpath)

        # 验证投广
        ad_title_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[4]/div/div/div/div[1]/div'
        self.assert_text("搜索投广", ad_title_xpath)
        self.assert_text("快速提升商品搜索曝光和转化", ad_title_xpath)
        ad_text_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[4]/div/div/div/div[2]/div[2]'
        self.assert_text("撬动搜索流量 一键加码曝光", ad_text_xpath)
        self.assert_text("参考同行数据，进行搜索投广后，ROI增长预计可达 15%", ad_text_xpath)
        self.assert_text("投放广告", ad_text_xpath)
        self.assert_text("具体投放效果以实际为准", ad_text_xpath)
        self.assert_text("搜索直通车一键加投广告，获得商业化曝光", ad_text_xpath)




