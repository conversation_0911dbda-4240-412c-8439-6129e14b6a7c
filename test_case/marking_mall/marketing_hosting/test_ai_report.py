import time

import pytest
import requests

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time


def contains_braces_multiline(text):
    pattern = r'\{[^{}]*\}'
    return bool(re.search(pattern, text, re.DOTALL))


def contains_chinese_digits(text):
    digits = ["一.", "二.", "三.", "四."]
    return any(digit in text for digit in digits)


def should_contain_last_week_dates(text):
    """
    判断文本中是否包含正确的日期范围
    """
    now = datetime.datetime.now()

    # 1. 判断当前时间是否在当前周周二12:00前
    # 获取本周周一日期（作为参考点）
    current_week_monday = now - datetime.timedelta(days=now.weekday())

    # 计算本周周二12:00的时间点
    tuesday_12pm = datetime.datetime.combine(
        current_week_monday + datetime.timedelta(days=1),  # 本周周二
        datetime.time(12, 0)
    )

    # 2. 根据当前时间确定要检查的日期范围
    if now < tuesday_12pm:
        # 周二12:00前 - 需要上上周的日期范围
        target_monday = current_week_monday - datetime.timedelta(days=14)
    else:
        # 周二12:00后 - 需要上周的日期范围
        target_monday = current_week_monday - datetime.timedelta(days=7)

    target_sunday = target_monday + datetime.timedelta(days=6)

    # 3. 格式化日期为"YYYY年MM月DD日"格式
    def format_date(date):
        return date.strftime("%Y年%m月%d日")

    date_range_str = f"取数周期：{format_date(target_monday)}至{format_date(target_sunday)}"

    # 4. 检查文本中是否包含正确的日期范围字符串
    return date_range_str in text


def contains_markdown_headings(text):
    """
    检测文本中是否包含 Markdown 标题字符（如 ##, ###, #### 等）
    """
    # 正则表达式匹配 2-6 个连续的井号（## 到 ######）
    pattern = r'#{2,6}'
    return re.search(pattern, text) is not None


def validate_task_section(text):
    """
    验证文本中是否存在"4.完成平台任务获取流量扶持"
    如果存在，则校验其下方是否包含完整的推荐任务、奖励、执行动作标题
    以及至少一行包含三块内容的数据行
    """
    # 检查主标题是否存在
    if "完成平台任务获取流量扶持" not in text:
        return True  # 不存在则直接通过

    # 分割文本为行
    lines = text.split('\n')

    # 查找主标题位置
    start_index = -1
    for i, line in enumerate(lines):
        if "完成平台任务获取流量扶持" in line:
            start_index = i
            break

    if start_index == -1:
        return True  # 不存在则通过

    # 在后续行中查找标题行
    has_titles = False
    for i in range(start_index + 1, min(start_index + 10, len(lines))):
        line = lines[i].strip()
        if "推荐任务" in line and "奖励" in line and "执行动作" in line:
            has_titles = True
            break

    if not has_titles:
        return False  # 存在主标题但缺少三个字段标题

    # 在标题行后续查找数据行
    has_valid_data = False
    # 从标题行后开始搜索数据行
    for i in range(i + 1, min(i + 20, len(lines))):  # i是找到标题行的位置
        line = lines[i].strip()

        # 跳过空行
        if not line:
            continue

        # 检查是否为数据行（包含分隔符或有三部分内容）
        # 分割行内容（支持空格、竖线、制表符等多种分隔符）
        parts = re.split(r'\s{1,}|\t', line)  # 多个空格、竖线或制表符分隔
        parts = [p.strip() for p in parts if p.strip()]  # 清理空白部分

        # 验证是否包含三块内容
        if len(parts) >= 3:
            # 检查每块内容是否非空
            if all(parts[:3]):  # 前三部分都有内容
                has_valid_data = True
                break

    return has_valid_data

upload_path = 'https://kim-robot.kwaitalk.com/api/robot/upload?key=%s'

send_path = 'https://kim-robot.kwaitalk.com/api/robot/send?key=%s'

hook_key = '1495d7e8-48ab-4d1e-abb1-f8ea9fafac83'


def send_text(robot_key, text):
    send_json = {
        'msgtype': 'text',
        'text': {
            'content': text
        }
    }
    robot_send_path = send_path % robot_key
    result = requests.post(robot_send_path, json=send_json)
    print("result=", result)


class TestMallNewTrafficOperationHighScore(BaseTestCase):

    @pytest.mark.skip
    def test_mall_ai_report(self):
        self.maximize_window()
        # all uid
        # url_uid = [11318492, 15933695, 44086744, 52768680, 78145269, 85026481, 94751498, 110787112, 116735759,
        #            124986501, 131377350, 149531668, 154206387, 194351858, 198472786, 203140805, 208232709, 233772711,
        #            237460137, 243063895, 274271132, 288271097, 302140191, 336195521, 336234737, 346706507, 349614346,
        #            358537307, 359910978, 365473101, 386161811, 400981194, 463676590, 471578085, 475114243, 523113253,
        #            566078630, 566765154, 573248957, 586297787, 590772488, 608193550, 628888682, 634222705, 635714346,
        #            638718336, 642388857, 656873420, 660310674, 664263672, 682627387, 708653481, 721307979, 731825592,
        #            734861821, 736944493, 743018108, 753270890, 754170778, 759823842, 760039411, 773364813, 775633240,
        #            784235653, 785322940, 800086528, 822087195, 823695463, 854251564, 876279939, 883786328, 885921808,
        #            894945983, 900299080, 902715592, 916215286, 922778902, 932774539, 939816528, 945515728, 970277635,
        #            975801040, 986433789, 1049255080, 1104235271, 1142896669, 1149992221, 1158541102, 1177023206,
        #            1208743635, 1217923425, 1220224418, 1221677769, 1236368082, 1238629057, 1265219997, 1268404913,
        #            1268528226, 1301631680, 1307442010, 1320046066, 1325871294, 1328042861, 1341180876, 1343655957,
        #            1362959928, 1363928225, 1365533474, 1386348259, 1393948476, 1398120093, 1417847935, 1422309691,
        #            1422607619, 1424602120, 1459838614, 1463201780, 1475587339, 1480834066, 1523473253, 1538773396,
        #            1539675075, 1551759342, 1578530328, 1582228889, 1582927890, 1618385532, 1655421464, 1656119776,
        #            1660188794, 1708491253, 1713748016, 1747342291, 1749612398, 1760390285, 1773516770, 1773972613,
        #            1783566903, 1787377463, 1795250160, 1804441825, 1815060544, 1818346229, 1822436213, 1829640279,
        #            1841676408, 1846347606, 1854264638, 1883461130, 1901183147, 1912373791, 1922926908, 1943908525,
        #            1959118622, 1961394263, 1966888728, 1977312909, 1986687488, 1991850613, 1997473425, 1998422763,
        #            2011654862, 2033443185, 2038794146, 2039015513, 2087056359, 2087266808, 2099553209, 2101460996,
        #            2143331544, 2146034720, 2146560815, 2166115897, 2167958983, 2186258506, 2204721436, 2266057671,
        #            2266996337, 2310601111, 2317918387, 2325128965, 2329796481, 2340493143, 2352588151, 2359460034,
        #            2378665538, 2382048994, 2383440332, 2390853395, 2401792937, 2404562542, 2418025508, 2422600555,
        #            2447345396, 2461824390, 2536328164, 2558070812, 2560278104, 2573537737, 2596709700, 2597866406,
        #            2605223511, 2613985660, 2622349952, 2627900218, 2650206088, 2682850347, 2691200581, 2700535356,
        #            2700885245, 2707965447, 2724383753, 2727409017, 2762029308, 2813813551, 2822217442, 2829295396,
        #            2846058294, 2847977614, 2852230948, 2861094476, 2864231786, 2865009208, 2867989803, 2894880235,
        #            2911053414, 2920347708, 2977617351, 2981499384, 2995854507, 2997352765, 3023937008, 3057816956,
        #            3063203440, 3072164806, 3081579777, 3121798363, 3160668799, 3188348123, 3189652323, 3214192408,
        #            3217285250, 3246118205, 3258276282, 3274959042, 3277298905, 3278723969, 3295493955, 3324691923,
        #            3341335905, 3345934534, 3358436787, 3371934531, 3441180219, 3452924473, 3480289464, 3505302464,
        #            3515875224, 3581850006, 3586499323, 3654754150, 3685784085, 3686089456, 3693675297, 3700918020,
        #            3739687640, 3755634979, 3764090454, 3777918749, 3790878964, 3791805926, 3795399066, 3799617780,
        #            3818890108, 3838582273, 3843774972, 3846722496, 3853474083, 3854658268, 3858898334, 3859288661,
        #            3894867078, 3938014314, 3987538020, 3990364148, 3993749935, 3997340604, 4049846048, 4055904706,
        #            4064713592, 4070128415, 4074447738, 4081106242, 4105563874, 4110715883, 4115699934, 4135908588,
        #            4138383899, 4143194659, 4158779537, 4192109196, 4219097440, 4229889450, 4235013289, 4238271017,
        #            4248069045, 4248973150, 4258804220, 4259255931, 4270961488, 4273844489, 4292935108, 4312262020,
        #            4326166016, 4348709727, 4350642064, 4361937919, 4384731161, 4396140560, 4458513700, 4479523115,
        #            4487137963, 4536708332, 4537414242, 4544986926, 4554915799, 4567647444, 4567942824, 4626505753,
        #            4639440061, 4649622943, 4672348530, 4674386700, 4677250705, 4684984355, 4695391120, 4698008445,
        #            4700883548, 4704550507, 4705980826, 4706452716, 4709096573, 4710321891, 4711436216, 4711457460,
        #            4711785565, 4713188258, 4713278317, 4713582494, 4717176253, 4721957379, 4722461031, 4722735670,
        #            4723549650, 4724710835, 4726211725, 4726954219, 4727264487, 4728562055, 4728757398, 4728937785,
        #            4729042194, 4729042925, 4729070259, 4729300999, 4729508242, 4730004406, 4730440393, 4730560301,
        #            4730700201, 4731897650, 4732255174, 4732280135, 4732510397, 4732606504, 4732914162, 4733283131,
        #            4733350046, 4734002114, 4734103124, 4734223610, 4734991269, 4735341980, 4735746178, 4735772853,
        #            4735799426, 4736204820, 4736680114, 4736704023, 4737171189, 4737188068, 4737313297, 4737393831,
        #            4738060843, 4738496386, 4738551958, 4738790282, 4739035599, 4739116371, 4739165482, 4739704593,
        #            4740556150, 4740707357, 4740715857, 4743510674, 4743976985, 4744564049, 4744896244, 4745021258,
        #            4746027096, 4746201001, 4746361958, 4746373517, 4746476490, 4746497380, 4746575929, 4747220806,
        #            4747732816, 4747924587, 4748237581, 4749225476, 4749369077, 4749553939, 4749731795, 4750194110,
        #            4750630796, 4750762771, 4750781760, 4750798556, 4751053963, 4751123237, 4751486144, 4751534918,
        #            4751558430, 4751783942, 4752775458, 4752803231, 4752995515, 4753014729, 4753043966, 4753078074,
        #            4753516746, 4753744417, 4754594113, 4754763558, 4755078946, 4755085513, 4755859003, 4756072163,
        #            4756107731, 4756169655, 4756304127, 4756657483, 4756673704, 4757617378, 4757697581, 4757884987,
        #            4758184148, 4758866038, 4758875703, 4758971232, 4759400704, 4761698482, 4763961149, 4763992611,
        #            4764094454, 4764676238, 4764998045, 4766012766, 4766707439, 4768308535, 4768816234, 4769808377,
        #            4771294448, 4772896254, 4773264299, 4774008725, 4774063427, 4774622326, 4775167306, 4775652375,
        #            4775736095, 4775769583, 4776082496, 4776358581, 4776373885, 4776992125, 4777310684, 4777541907,
        #            4778199722, 4784977199, 4785468042, 4791844318, 4798001297, 4812637640, 4812859822, 4825976333,
        #            4826503242, 4826615882, 4827116195, 4828165556, 4828241087, 4828718758, 4828820547, 4829026895,
        #            4829051487, 4829188541, 4829193847, 4829270693, 4829478773, 4829578825, 4830096452, 4830169780,
        #            4830181036, 4830242516, 4830266927, 4830509538, 4830587515, 4830606753, 4830689890, 4830690108,
        #            4830694098, 4830757663, 4830765721, 4830919054, 4830968730, 1214105716, 3587090571, 1165827806]
        url_uid = [11318492, 15933695, 44086744, 52768680, 78145269, 85026481, 94751498, 110787112, 116735759,
                   124986501, 131377350, 149531668, 154206387, 194351858, 198472786, 203140805, 208232709, 233772711,
                   237460137, 243063895, 274271132, 288271097, 302140191, 336195521, 336234737, 346706507, 349614346]
        # url_uid = ['52768680', '78145269', '94751498', '233772711', '336195521', '346706507']
        error_list = {}
        count = 0
        for uid in url_uid:
            count += 1
            ratio = count / len(url_uid) * 100
            print(str(ratio) + "%")
            url = "https://app.kwaixiaodian.com/page/tianhe/AIOperation/operational_report?layoutType=4&sellerId=" + str(
                uid)
            self.open(url)
            time.sleep(7)

            report_data_time_xpath = '//*[@id="root"]/div/div[1]/div/div/div[1]/div[2]/div[2]'
            date_text = self.get_text(report_data_time_xpath)

            report_content_xpath = '//*[@id="root"]/div/div[1]/div/div/div[2]'
            text = self.get_text(report_content_xpath)

            err_msg = ""
            if text is None or date_text is None:
                err_msg += '报告无数据  '
            if contains_braces_multiline(text) == True:
                err_msg += '含大括号  '
            if contains_chinese_digits(text) == False:
                err_msg += '大标题缺失  '
            if should_contain_last_week_dates(date_text) == False :
                err_msg += '取数周期异常  '
            if contains_markdown_headings(text) == True:
                err_msg += 'markdown格式异常  '
            if validate_task_section(text) == False:
                err_msg += f'任务表格格式异常  '
            print(validate_task_section(text))
            if err_msg != "":
                error_list[uid] = err_msg

        if len(error_list) > 0:
            print(error_list)
            text_msg = f'ai 报告异常：{error_list}'
            send_text(hook_key, text_msg)
