import time

import pytest
from _pytest.skipping import Skip

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException


@pytest.mark.skip
class TestMallTrafficOperation(BaseTestCase):

    traffic_operation_domain = "MALL_TRAFFIC_OPERATION_DOMAIN"
    account_login = "xiaohs"

    # 登录，进降价得流量
    def test_mall_traffic_operation_init(self):
        self.maximize_window()
        self.login(self.traffic_operation_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)

    def test_mall_traffic_operation_ele(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        # 验证头部文字
        self.wait_for_element_visible('//*[@id="root"]/div/div/div[1]/div/div[1]', "css xpath", 4)
        self.assert_text("拿商城首页推荐流量", '//*[@id="root"]/div/div/div[1]/div/div[1]/div[1]/span[1]')
        self.assert_text("商品入池，累计获得曝光", '//*[@id="root"]/div/div/div[1]/div/div[1]/div[2]/span/div/span[3]')
        # 验证注意事项
        self.assert_text("使用注意事项", '//*[@id="root"]/div/div/div[1]/div/div[2]/div/div[1]/div[1]/span')
        self.assert_text("1. 降价前，请您确认好列表第二列的【热销规格】库存大于10件，且仅列表展示的热销规格降价后才能得扶持。请您一定确保库存充足（至少大于10件），避免优惠失效！", '//*[@id="root"]/div/div/div[1]/div/div[2]/div/div[2]/div[1]/div')
        self.assert_text("2. 列表第四列的【当前价格】不是用户到手价，改价后请您务必确认好商品的到手价，避免给您造成损失！", '//*[@id="root"]/div/div/div[1]/div/div[2]/div/div[2]/div[2]/div/span')
        self.assert_text("3. 两种方式可取消降价退出扶持。方式一：在「营销工具-优惠券-管理优惠券」页面，结束商品相关的优惠券；方式二：在「商品管理-商品列表-修改价格」页面，调整有「降价得流量」标签的SKU价格", '//*[@id="root"]/div/div/div[1]/div/div[2]/div/div[2]/div[3]/div/span')
        self.assert_text("4. 降价成功后，数据更新可能会延迟，期间请不要反复修改商品价格，避免扶持失效。可耐心等待10秒钟后",
                         '//*[@id="root"]/div/div/div[1]/div/div[2]/div/div[2]/div[4]/div/span')

        if self.is_exact_text_visible("同行案例",'//*[@id="root"]/div/div/div[1]/div/div[3]/div[1]/div[1]/span'):
            self.assert_text("同行案例", '//*[@id="root"]/div/div/div[1]/div/div[3]/div[1]/div[1]/span')

        # 我的收益项
        profit_name = ["我的收益", "已扶持商品数", "累计获得单日曝光", "待扶持商品数"]
        profit_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]'
        for i in range(4):
            self.assert_text(profit_name[i], profit_xpath)
        # 商品数据明细
        self.assert_text("查看数据详情", '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/div[2]/span[1]')
        # 验证筛选项
        search_name = ["商品ID", "商品名称", "扶持状态", "是否符合条件"]
        search_xpath = '//*[@id="root"]/div/div/div[1]/div/div[4]/div/div[1]/div/div/div[2]/div'
        for i in range(len(search_name)):
            self.assert_text(search_name[i], search_xpath)
        # 表头
        thead_xpath = '//*[@id="tableId"]/div/div[1]/table/thead/tr'
        thead_name = ["商品信息", "热销规格", "当前价格", "建议价格", "扶持状态", "预计可得收益", "操作"]
        for i in range(len(thead_name)):
            self.assert_text(thead_name[i], thead_xpath)

    # 收益时间-默认昨天更新
    def test_mall_profit_time_1d(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        time_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/span[2]'
        time_str = self.get_text(time_xpath)

        # 当前日期前一天
        expect_day_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_day_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_day_3 = str(datetime.date.today())
        # 判断数据是否更新
        if time_str == expect_day_1:
            assert time_str == expect_day_1
        elif time_str == expect_day_2:
            assert expect_day_2 == time_str
        else:
            assert expect_day_3 == time_str

     # 收益时间-切换近7天更新
    def test_mall_profit_time_7d(self):

        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        buttom_7d_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/div[1]/label[2]/span[2]'
        buttom_7d_name = '近七天'
        self.assert_text(buttom_7d_name,buttom_7d_xpath)

        self.click(buttom_7d_xpath)
        text_7d_xpath ='//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/span[2]'
        text_7d_start = str(self.get_text(text_7d_xpath)).split(" ~ ")[0]
        text_7d_end = str(self.get_text(text_7d_xpath)).split(" ~ ")[1]
        print(text_7d_start, text_7d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=7))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=8))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=6))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_7d_start == expect_start_1:
            assert text_7d_start == expect_start_1
        elif text_7d_start == expect_start_2:
            assert expect_start_2 == text_7d_start
        else:
            assert expect_start_3 == text_7d_start

        if text_7d_end == expect_end_1:
            assert text_7d_end == expect_end_1
        elif text_7d_end == expect_end_2:
            assert expect_end_2 == text_7d_end
        else:
            assert expect_end_3 == text_7d_end

     # 收益时间-切换近30天更新
    def test_mall_profit_time_30d(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        buttom_30d_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/div[1]/label[4]/span[2]'
        buttom_30d_name = "近30天"
        self.assert_text(buttom_30d_name, buttom_30d_xpath)

        self.click(buttom_30d_xpath)
        text_30d_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/span[2]'
        text_30d_start = str(self.get_text(text_30d_xpath)).split(" ~ ")[0]
        text_30d_end = str(self.get_text(text_30d_xpath)).split(" ~ ")[1]
        print(text_30d_start, text_30d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=30))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=31))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=29))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_30d_start == expect_start_1:
            assert text_30d_start == expect_start_1
        elif text_30d_start == expect_start_2:
            assert expect_start_2 == text_30d_start
        else:
            assert expect_start_3 == text_30d_start

        if text_30d_end == expect_end_1:
            assert text_30d_end == expect_end_1
        elif text_30d_end == expect_end_2:
            assert expect_end_2 == text_30d_end
        else:
            assert expect_end_3 == text_30d_end

    # 查看数据详情-页面跳转
    def test_mall_data_detail_link(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        # 准备跳转链接元素的xpath、url：查看数据详情
        data_detail_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[1]/div[2]/div[2]/span[1]'
        data_detail_url = 'https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation?activeChannel=reco'
        try:
            self.wait_for_element_visible(data_detail_xpath)
            if self.is_exact_text_visible('查看数据详情', data_detail_xpath):
                self.click(data_detail_xpath)
                self.wait_for_ready_state_complete()

                print(f"页面点击跳转:{self.get_current_url()}")
                self.assert_url_contains(data_detail_url)
                time.sleep(2)
            else:
                print(f"页面点击跳转:{self.get_current_url()} ---与预期不一致")
                assert False
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            assert False

    # 降价指导手册-页面跳转
    def test_mall_traffic_operation_rule_link(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        # 准备跳转链接元素的xpath、url：更多经营数据
        rule_xpath = '//*[@id="root"]/div/div/div[1]/div/div[4]/div/div[1]/div/div/div[1]/div/div/div[2]/div/div/span'
        rule_url = 'https://docs.qingque.cn/d/home/<USER>'
        try:
            self.wait_for_element_visible(rule_xpath)
            if self.is_exact_text_visible('降价指导手册', rule_xpath):
                self.click(rule_xpath)
                self.wait_for_ready_state_complete()

                print(f"页面点击跳转:{self.get_current_url()}")
                self.assert_url_contains(rule_url)
                time.sleep(2)
            else:
                print(f"页面点击跳转:{self.get_current_url()} ---与预期不一致")
                assert False
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            assert False

    # 注意事项-收起
    def test_mall_fold_up_button(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(2)

        button_xpath = '//*[@id="root"]/div/div/div[1]/div/div[2]/div/div[1]/div[2]/div'
        exp_fold_up_name = "收起"
        exp_open_name = "展开"

        fold_up_name = self.get_text(button_xpath)
        assert fold_up_name == exp_fold_up_name

        self.click(button_xpath)
        time.sleep(2)

        open_name = self.get_text(button_xpath).split('\n')[0]
        assert open_name == exp_open_name

    # 按钮校验--待扶持
    @pytest.mark.skip
    def test_mall_button_not_eff(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)

        count_xpath = '//*[@id="root"]/div/div/div[1]/div/div[3]/div[2]/div[2]/div[3]/span'
        count = self.get_text(count_xpath)
        statu_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[5]/div/div/div/span[2]'
        statu = self.get_text(statu_xpath)

        suggest_price_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[4]/div/div/span'
        suggest_price = self.get_text(suggest_price_xpath)
        print(suggest_price)
        if count != 0 and statu == '待扶持':
            if suggest_price != '-':
                product_button_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[7]/div/div/button[1]/span'
                product_button = self.get_text(product_button_xpath)
                assert product_button == '商品一键降价'
                sku_button_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[7]/div/div/button[2]/span'
                sku_button = self.get_text(sku_button_xpath)
                assert sku_button == '仅热销规格降价'

    # 按钮校验 -- 不满足条件
    def test_mall_button_not_match(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(2)

        count_xpath = '//*[@id="root"]/div/div/div[1]/div/div[4]/div/div[2]/div/div/div/div/ul/li[1]'
        count = int(str(self.get_text(count_xpath)).split(' ')[1])
        if count >= 10:
            count = 10

        for i in range(2, 12):
            statu_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[5]/div/div/div/span[2]'
            statu = self.get_text(statu_xpath)
            if statu == '不符合条件':
                case_content_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[6]/div/div/span/div/span[2]'
                case_content = self.get_text(case_content_xpath)
                exp_case = '1.确保无价格异常'
                if exp_case in case_content:
                    adjust_price_xapth= f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]/div/div/button/span'
                    adjust_price = self.get_text(adjust_price_xapth)
                    assert adjust_price == '调整价格'