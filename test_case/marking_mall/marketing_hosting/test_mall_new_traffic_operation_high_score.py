import time

import pytest

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException


@pytest.mark.skip
class TestMallNewTrafficOperationHighScore(BaseTestCase):
    traffic_operation_domain = "MALL_TRAFFIC_OPERATION_DOMAIN"
    account_login = "b_mall_traffic_operation"

    # 登录，好价免费拿量
    def test_mall_traffic_operation_init(self):
        self.maximize_window()
        self.login(self.traffic_operation_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)
        self.click_popup_window()

    def test_mall_traffic_operation_ele(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        # 验证头部文字
        header_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div'
        self.wait_for_element_visible(header_xpath, "css xpath", 4)
        self.assert_text("商品好价格·拿商城亿级流量", header_xpath)
        self.assert_text("个商品入池，累计免费获得额外曝光", header_xpath)
        self.assert_text("首页推荐流量", header_xpath)
        self.assert_text("得10亿+曝光", header_xpath)
        self.assert_text("商城搜索流量", header_xpath)
        self.assert_text("获取精准流量", header_xpath)
        self.assert_text("专属卖点标签", header_xpath)
        self.assert_text("提升商品转化", header_xpath)
        self.assert_text("猜喜固定坑位", header_xpath)
        self.assert_text("抢夺销售机会", header_xpath)

        # 验证注意事项
        self.assert_text("注意事项", header_xpath)
        self.assert_text("保障库存充足", header_xpath)
        self.assert_text("降价前，请您确认列表中的【热销规格】库存大于10件，且仅列表展示的【热销规格】降价后才能得扶持",
                         header_xpath)
        self.assert_text("确认好到手价", header_xpath)
        self.assert_text("列表中的【当前价格】不是用户到手价，改价后请您务必确认好商品的到手价，避免给您造成损失！",
                         header_xpath)
        self.assert_text("退出活动方法", header_xpath)
        self.assert_text(
            "两种方式可退出活动。方式一：在「营销工具-优惠券-管理优惠券」页面，结束商品相关的优惠券；方式二：在「商品管理-商品列表-修改价格」页面，调整热销规格的原价",
            header_xpath)
        self.assert_text("注意数据更新", header_xpath)
        self.assert_text(
            "降价成功后，数据更新可能会延迟，期间请不要反复修改商品价格，避免扶持失效。可耐心等待10秒钟后 刷新",
            header_xpath)

        # 我的收益项
        profit_name = ["我的收益", "获得扶持次数", "商品曝光次数", "商品点击次数", "成交订单数"]
        profit_xpath = '//*[@id="root"]/div/div/div[1]/div[2]'
        for i in range(4):
            self.assert_text(profit_name[i], profit_xpath)
        # 商品数据明细
        self.assert_text("查看更多", '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/span[1]')

        # tab
        tab_xpath = '//*[@id="list"]/div[1]/div'
        tab_text = self.get_text(tab_xpath)
        tab_name = ["可扶持商品", "扶持中商品", "扶持停止商品", "历史扶持商品"]
        for i in range(4):
            assert tab_name[i] in tab_text

        # 验证筛选项
        search_name = ["商品ID", "商品名称", "是否符合条件"]
        search_xpath = '//*[@id="pro-form-wrapper"]/div'
        for i in range(len(search_name)):
            self.assert_text(search_name[i], search_xpath)
        # 表头
        thead_xpath = '//*[@id="tableId"]/div/div[1]/table/thead/tr'
        thead_name = ["商品信息", "热销规格", "当前价格", "建议价格", "扶持状态", "预计可得收益", "操作"]
        for i in range(len(thead_name)):
            self.assert_text(thead_name[i], thead_xpath)

    # 收益时间-默认近7天更新
    def test_mall_profit_time_7d(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        text_7d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        text_7d_start = str(self.get_text(text_7d_xpath)).split(" ~ ")[0]
        text_7d_end = str(self.get_text(text_7d_xpath)).split(" ~ ")[1]
        print(text_7d_start, text_7d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=7))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=8))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=6))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_7d_start == expect_start_1:
            assert text_7d_start == expect_start_1
        elif text_7d_start == expect_start_2:
            assert expect_start_2 == text_7d_start
        else:
            assert expect_start_3 == text_7d_start

        if text_7d_end == expect_end_1:
            assert text_7d_end == expect_end_1
        elif text_7d_end == expect_end_2:
            assert expect_end_2 == text_7d_end
        else:
            assert expect_end_3 == text_7d_end

    # 收益时间-昨天
    def test_mall_profit_time_1d(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        button_1d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/label[1]/span[2]'
        button_1d_name = "昨天"
        self.assert_text(button_1d_name, button_1d_xpath)

        self.click(button_1d_xpath)
        time_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        time_str = self.get_text(time_xpath).split(" ~ ")[1]

        # 当前日期前一天
        expect_day_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_day_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_day_3 = str(datetime.date.today())
        # 判断数据是否更新
        if time_str == expect_day_1:
            assert time_str == expect_day_1
        elif time_str == expect_day_2:
            assert expect_day_2 == time_str
        else:
            assert expect_day_3 == time_str

    # 收益时间-切换近14天更新
    def test_mall_profit_time_14d(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        button_14d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/label[3]/span[2]'
        button_14d_name = "近14天"
        self.assert_text(button_14d_name, button_14d_xpath)

        self.click(button_14d_xpath)
        text_14d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        text_14d_start = str(self.get_text(text_14d_xpath)).split(" ~ ")[0]
        text_14d_end = str(self.get_text(text_14d_xpath)).split(" ~ ")[1]
        # print(text_30d_start, text_30d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=14))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=15))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=13))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_14d_start == expect_start_1:
            assert text_14d_start == expect_start_1
        elif text_14d_start == expect_start_2:
            assert expect_start_2 == text_14d_start
        else:
            assert expect_start_3 == text_14d_start

        if text_14d_end == expect_end_1:
            assert text_14d_end == expect_end_1
        elif text_14d_end == expect_end_2:
            assert expect_end_2 == text_14d_end
        else:
            assert expect_end_3 == text_14d_end

    # 收益时间-切换近30天更新
    def test_mall_profit_time_30d(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        button_30d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/label[4]/span[2]'
        button_30d_name = "近30天"
        self.assert_text(button_30d_name, button_30d_xpath)

        self.click(button_30d_xpath)
        text_30d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        text_30d_start = str(self.get_text(text_30d_xpath)).split(" ~ ")[0]
        text_30d_end = str(self.get_text(text_30d_xpath)).split(" ~ ")[1]
        print(text_30d_start, text_30d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=30))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=31))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=29))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_30d_start == expect_start_1:
            assert text_30d_start == expect_start_1
        elif text_30d_start == expect_start_2:
            assert expect_start_2 == text_30d_start
        else:
            assert expect_start_3 == text_30d_start

        if text_30d_end == expect_end_1:
            assert text_30d_end == expect_end_1
        elif text_30d_end == expect_end_2:
            assert expect_end_2 == text_30d_end
        else:
            assert expect_end_3 == text_30d_end

    # 我的收益-查看更多-页面跳转
    def test_profit_data_detail_link(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        # 准备跳转链接元素的xpath、url：查看数据详情
        data_detail_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/span[1]'
        data_detail_url = 'https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation'
        try:
            self.wait_for_element_visible(data_detail_xpath)
            if self.is_exact_text_visible('查看更多', data_detail_xpath):
                self.click(data_detail_xpath)
                self.wait_for_ready_state_complete()

                print(f"页面点击跳转:{self.get_current_url()}")
                self.assert_url_contains(data_detail_url)
                time.sleep(2)
            else:
                print(f"页面点击跳转:{self.get_current_url()} ---与预期不一致")
                assert False
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            assert False

    # 好价免费拿量操作手册-页面跳转
    def test_mall_new_traffic_operation_rule_link(self):
        self.test_mall_traffic_operation_init()
        if self.is_element_visible("//span[contains(text(), '展开')]"):
            self.click("//span[contains(text(), '展开')]")
        # 准备跳转链接元素的xpath、url：更多经营数据
        rule_xpath = "//a[contains(text(), '查看详情')]"
        expected_url = 'https://docs.qingque.cn/d/home/<USER>'
        self.check_jump_link(rule_xpath, '查看详情', expected_url, self.test_mall_traffic_operation_init,
                             need_login=False, has_href=False, need_click=True)

    # 注意事项-收起
    def test_mall_fold_up_button(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(2)

        button_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div/div/div[1]/div[1]/span'
        exp_fold_up_name = "收起"
        exp_open_name = "展开"

        fold_up_name = self.get_text(button_xpath)
        assert fold_up_name == exp_fold_up_name

        self.click(button_xpath)
        time.sleep(2)

        open_name = self.get_text(button_xpath).split('\n')[0]
        assert open_name == exp_open_name

    # 按钮校验--待扶持
    def test_mall_button_not_eff(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)
        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'
        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])

            if count != 0:
                if count >= 10:
                    count = 10
                for i in range(2, count+2):
                    statu_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[6]/div'
                    statu = self.get_text(statu_xpath)
                    exp_statu = '待扶持'
                    assert exp_statu in statu
                    suggest_price_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[4]/div'
                    suggest_price = self.get_text(suggest_price_xpath)
                    not_match_head = '不满足扶持条件，具体原因：'
                    not_match_res_1 = '商品库存数应超过0件'
                    not_match_res_2 = '确保无价格异常'
                    if suggest_price != '-' and suggest_price != '计算中...' and not_match_head not in statu:
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                        product_button = self.get_text(product_button_xpath)
                        assert '商品一键降价' in product_button
                        assert '仅热销规格降价' in product_button
                    elif suggest_price == '计算中...' and statu == '计算中...':
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                        product_button = self.get_text(product_button_xpath)
                        assert '刷新' in product_button
                    elif suggest_price == '-' and not_match_head in statu:
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                        product_button = self.get_text(product_button_xpath)
                        assert product_button == ""
                        if not_match_res_1 in statu:
                            exp_button = '调整库存'
                            assert exp_button in statu
                        if not_match_res_2 in statu:
                            exp_button = '调整价格'
                            assert exp_button in statu

    # 按钮校验--扶持中
    def test_mall_button_in_eff(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)
        in_eff_xpath = '//*[@id="list"]/div[1]/div/div/div/div[2]'
        self.click(in_eff_xpath)
        time.sleep(3)

        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'
        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])

            if count != 0:
                if count >= 10:
                    count = 10
                for i in range(2, count+2):
                    statu_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[6]'
                    statu = self.get_text(statu_xpath)
                    exp_statu = '扶持中'
                    assert exp_statu in statu
                    continue_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[5]'
                    continue_income = self.get_text(continue_xpath)

                    if continue_income == '暂无新的调价建议':
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                        product_button = self.get_text(product_button_xpath)
                        assert product_button == ""
                    elif '降价至' in continue_income:
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                        product_button = self.get_text(product_button_xpath)
                        assert '继续降价' in product_button
                    elif continue_income == '计算中...':
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                        product_button = self.get_text(product_button_xpath)
                        assert '刷新' in product_button

    # 按钮校验--扶持停止
    def test_mall_button_eff_end(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)

        # 切换扶持停止商品tab
        eff_end_xpath = '//*[@id="list"]/div[1]/div/div/div/div[3]'
        self.click(eff_end_xpath)
        time.sleep(3)

        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'
        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])

            if count != 0:
                if count >= 10:
                    count = 10
                for i in range(2, count+2):
                    statu_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[5]'
                    statu = self.get_text(statu_xpath)
                    exp_statu = '扶持停止'
                    assert exp_statu in statu
                    suggest_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[4]'
                    suggest_price = self.get_text(suggest_xpath)

                    if suggest_price == '-':
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[6]'
                        product_button = self.get_text(product_button_xpath)
                        assert product_button == ""
                    elif '自定义' in suggest_price:
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[6]'
                        product_button = self.get_text(product_button_xpath)
                        assert '商品一键降价' in product_button
                        assert '仅热销规格降价' in product_button
                    elif suggest_price == '计算中...':
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[6]'
                        product_button = self.get_text(product_button_xpath)
                        assert '刷新' in product_button

                    if "不满足扶持条件，具体原因：" in statu:
                        not_match_res_1 = '商品库存数应超过0件'
                        not_match_res_2 = '确保无价格异常'
                        if not_match_res_1 in statu:
                            exp_button = '调整库存'
                            assert exp_button in statu
                        if not_match_res_2 in statu:
                            exp_button = '调整价格'
                            assert exp_button in statu

    # 按钮校验--历史扶持
    def test_mall_button_history_eff(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)
        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'

        # 切换历史扶持商品tab
        history_eff_xpath = '//*[@id="list"]/div[1]/div/div/div/div[4]'
        self.click(history_eff_xpath)
        time.sleep(3)

        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])

            if count != 0:
                if count >= 10:
                    count = 10
                for i in range(2, count+2):
                    statu_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[8]'
                    statu = self.get_text(statu_xpath)
                    exp_statu = '已完成扶持'
                    assert exp_statu in statu

                    income_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[5]'
                    income_text = self.get_text(income_xpath)
                    if '改进以下问题，提升扶持收益' in income_text:
                        if '优化' in income_text:
                            assert '商品诊断' in income_text
                        if '报名' in income_text:
                            assert '商城活动' in income_text

                    continue_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[7]'
                    continue_text = self.get_text(continue_xpath)
                    if "暂无新的调价建议" in continue_text:
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[9]'
                        product_button = self.get_text(product_button_xpath)
                        assert product_button == ''
                    elif "降价至" in continue_text:
                        product_button_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[9]'
                        product_button = self.get_text(product_button_xpath)
                        assert product_button == '继续降价'

    # 筛选结果--待扶持
    @pytest.mark.skip
    def test_mall_search_not_eff(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)
        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'
        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])

            if count != 0:
                same_trade_button_xpath = '//*[@id="list"]/div[3]/div/div[1]/div/div/div/div/div[2]/div/div/div/div/label/span[2]'
                self.click(same_trade_button_xpath)
                time.sleep(2)
                if '暂无数据' in self.get_text(no_data_xpath):
                    assert True
                else:
                    count = int(self.get_text(count_xpath).split(' ')[1])
                    if count != 0:
                        if count >= 10:
                            count = 10
                        for i in range(2, count+2):
                            item_info_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[1]'
                            item_info = self.get_text(item_info_xpath)
                            exp_statu = '可抢同行权益'
                            assert exp_statu in item_info

    # 筛选结果--扶持中
    def test_mall_search_in_eff(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)

        # 切换扶持中商品tab
        in_eff_xpath = '//*[@id="list"]/div[1]/div/div/div/div[2]'
        self.click(in_eff_xpath)
        time.sleep(3)

        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'
        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])
            if count != 0:
                same_trade_button_xpath = '//*[@id="list"]/div[3]/div/div[1]/div/div/div/div/div[2]/div/div/div/div/label/span[2]'
                self.click(same_trade_button_xpath)
                time.sleep(2)
                if '暂无数据' in self.get_text(no_data_xpath):
                    assert True
                else:
                    count = int(self.get_text(count_xpath).split(' ')[1])
                    if count != 0:
                        if count >= 10:
                            count = 10
                        for i in range(2, count+2):
                            item_info_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[1]'
                            item_info = self.get_text(item_info_xpath)
                            exp_statu = '权益被抢夺'
                            assert exp_statu in item_info

    # 筛选结果--扶持停止
    def test_mall_search_eff_end(self):
        self.test_mall_traffic_operation_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)

        # 切换扶持停止商品tab
        eff_end_xpath = '//*[@id="list"]/div[1]/div/div/div/div[3]'
        self.click(eff_end_xpath)
        time.sleep(3)

        no_data_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td/div/div/div[2]'
        if '暂无数据' in self.get_text(no_data_xpath):
            assert True
        else:
            count_xpath = '//*[@id="list"]/div[3]/div/div[2]/div/div/div/div/ul/li[1]'
            count = int(self.get_text(count_xpath).split(' ')[1])
            if count != 0:
                same_trade_button_xpath = '//*[@id="list"]/div[3]/div/div[1]/div/div/div/div/div[2]/div/div/div/div/label/span[2]'
                self.click(same_trade_button_xpath)
                time.sleep(2)
                if '暂无数据' in self.get_text(no_data_xpath):
                    assert True
                else:
                    count = int(self.get_text(count_xpath).split(' ')[1])
                    if count != 0:
                        if count >= 10:
                            count = 10
                        for i in range(2, count+2):
                            item_info_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[{i}]/td[1]'
                            item_info = self.get_text(item_info_xpath)
                            exp_statu = '权益被抢夺'
                            assert exp_statu in item_info