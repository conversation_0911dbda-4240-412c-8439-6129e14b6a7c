import time

import pytest

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException


@pytest.mark.skip
class TestMallNewTrafficOperationNoScore(BaseTestCase):
    traffic_operation_domain = "MALL_TRAFFIC_OPERATION_DOMAIN"
    account_login = "xiaohuishuang"

    # 登录，好价免费拿量
    def test_mall_traffic_operation_online_init(self):
        self.maximize_window()
        self.login(self.traffic_operation_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)
        self.click_popup_window()

    def test_mall_traffic_operation_ele_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        # 验证头部文字
        header_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div'
        self.wait_for_element_visible(header_xpath, "css xpath", 4)
        self.assert_text("商品好价格·拿商城亿级流量", header_xpath)
        self.assert_text("个商品入池，累计免费获得额外曝光", header_xpath)
        self.assert_text("首页推荐流量", header_xpath)
        self.assert_text("得10亿+曝光", header_xpath)
        self.assert_text("商城搜索流量", header_xpath)
        self.assert_text("获取精准流量", header_xpath)
        self.assert_text("专属卖点标签", header_xpath)
        self.assert_text("提升商品转化", header_xpath)
        self.assert_text("猜喜固定坑位", header_xpath)
        self.assert_text("抢夺销售机会", header_xpath)

        # 验证注意事项
        self.assert_text("注意事项", header_xpath)
        self.assert_text("保障库存充足", header_xpath)
        self.assert_text("降价前，请您确认列表中的【热销规格】库存大于10件，且仅列表展示的【热销规格】降价后才能得扶持",
                         header_xpath)
        self.assert_text("确认好到手价", header_xpath)
        self.assert_text("列表中的【当前价格】不是用户到手价，改价后请您务必确认好商品的到手价，避免给您造成损失！",
                         header_xpath)
        self.assert_text("退出活动方法", header_xpath)
        self.assert_text(
            "两种方式可退出活动。方式一：在「营销工具-优惠券-管理优惠券」页面，结束商品相关的优惠券；方式二：在「商品管理-商品列表-修改价格」页面，调整热销规格的原价",
            header_xpath)
        self.assert_text("注意数据更新", header_xpath)
        self.assert_text(
            "降价成功后，数据更新可能会延迟，期间请不要反复修改商品价格，避免扶持失效。可耐心等待10秒钟后 刷新",
            header_xpath)

        # 我的收益项
        profit_name = ["我的收益", "获得扶持次数", "商品曝光次数", "商品点击次数", "成交订单数"]
        profit_xpath = '//*[@id="root"]/div/div/div[1]/div[2]'
        for i in range(4):
            self.assert_text(profit_name[i], profit_xpath)
        # 商品数据明细
        self.assert_text("查看更多", '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/span[1]')

        # tab
        tab_xpath = '//*[@id="list"]/div[1]/div'
        tab_text = self.get_text(tab_xpath)
        tab_name = ["可扶持商品", "扶持中商品", "扶持停止商品", "历史扶持商品"]
        for i in range(4):
            assert tab_name[i] in tab_text

        # 验证筛选项
        search_name = ["商品ID", "商品名称", "是否符合条件"]
        search_xpath = '//*[@id="pro-form-wrapper"]/div'
        for i in range(len(search_name)):
            self.assert_text(search_name[i], search_xpath)
        # 店铺分不满足
        not_match_score_xpath = '//*[@id="list"]/div[3]/div[2]/span[1]'
        not_match_score = self.get_text(not_match_score_xpath)
        assert '当前店铺暂不满足降价得流量要求，请您先改善经营。' in not_match_score

    # 收益时间-默认近7天更新
    def test_mall_profit_time_7d_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        text_7d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        text_7d_start = str(self.get_text(text_7d_xpath)).split(" ~ ")[0]
        text_7d_end = str(self.get_text(text_7d_xpath)).split(" ~ ")[1]
        print(text_7d_start, text_7d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=7))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=8))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=6))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_7d_start == expect_start_1:
            assert text_7d_start == expect_start_1
        elif text_7d_start == expect_start_2:
            assert expect_start_2 == text_7d_start
        else:
            assert expect_start_3 == text_7d_start

        if text_7d_end == expect_end_1:
            assert text_7d_end == expect_end_1
        elif text_7d_end == expect_end_2:
            assert expect_end_2 == text_7d_end
        else:
            assert expect_end_3 == text_7d_end

    # 收益时间-昨天
    def test_mall_profit_time_1d_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        button_1d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/label[1]/span[2]'
        button_1d_name = "昨天"
        self.assert_text(button_1d_name, button_1d_xpath)

        self.click(button_1d_xpath)
        time_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        time_str = self.get_text(time_xpath).split(" ~ ")[1]

        # 当前日期前一天
        expect_day_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_day_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_day_3 = str(datetime.date.today())
        # 判断数据是否更新
        if time_str == expect_day_1:
            assert time_str == expect_day_1
        elif time_str == expect_day_2:
            assert expect_day_2 == time_str
        else:
            assert expect_day_3 == time_str

    # 收益时间-切换近14天更新
    def test_mall_profit_time_14d_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        button_14d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/label[3]/span[2]'
        button_14d_name = "近14天"
        self.assert_text(button_14d_name, button_14d_xpath)

        self.click(button_14d_xpath)
        text_14d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        text_14d_start = str(self.get_text(text_14d_xpath)).split(" ~ ")[0]
        text_14d_end = str(self.get_text(text_14d_xpath)).split(" ~ ")[1]
        # print(text_30d_start, text_30d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=14))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=15))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=13))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_14d_start == expect_start_1:
            assert text_14d_start == expect_start_1
        elif text_14d_start == expect_start_2:
            assert expect_start_2 == text_14d_start
        else:
            assert expect_start_3 == text_14d_start

        if text_14d_end == expect_end_1:
            assert text_14d_end == expect_end_1
        elif text_14d_end == expect_end_2:
            assert expect_end_2 == text_14d_end
        else:
            assert expect_end_3 == text_14d_end

    # 收益时间-切换近30天更新
    def test_mall_profit_time_30d_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        button_30d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/label[4]/span[2]'
        button_30d_name = "近30天"
        self.assert_text(button_30d_name, button_30d_xpath)

        self.click(button_30d_xpath)
        text_30d_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/span'
        text_30d_start = str(self.get_text(text_30d_xpath)).split(" ~ ")[0]
        text_30d_end = str(self.get_text(text_30d_xpath)).split(" ~ ")[1]
        print(text_30d_start, text_30d_end)

        # 预期开始时间
        expect_start_1 = str(datetime.date.today() - datetime.timedelta(days=30))
        expect_start_2 = str(datetime.date.today() - datetime.timedelta(days=31))
        expect_start_3 = str(datetime.date.today() - datetime.timedelta(days=29))

        # 当前日期前一天
        expect_end_1 = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_end_2 = str(datetime.date.today() - datetime.timedelta(days=2))
        expect_end_3 = str(datetime.date.today())

        if text_30d_start == expect_start_1:
            assert text_30d_start == expect_start_1
        elif text_30d_start == expect_start_2:
            assert expect_start_2 == text_30d_start
        else:
            assert expect_start_3 == text_30d_start

        if text_30d_end == expect_end_1:
            assert text_30d_end == expect_end_1
        elif text_30d_end == expect_end_2:
            assert expect_end_2 == text_30d_end
        else:
            assert expect_end_3 == text_30d_end

    # 我的收益-查看更多-页面跳转
    def test_profit_data_detail_link_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        # 准备跳转链接元素的xpath、url：查看数据详情
        data_detail_xpath = '//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/span[1]'
        data_detail_url = 'https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation'
        try:
            self.wait_for_element_visible(data_detail_xpath)
            if self.is_exact_text_visible('查看更多', data_detail_xpath):
                self.click(data_detail_xpath)
                self.wait_for_ready_state_complete()

                print(f"页面点击跳转:{self.get_current_url()}")
                self.assert_url_contains(data_detail_url)
                time.sleep(2)
            else:
                print(f"页面点击跳转:{self.get_current_url()} ---与预期不一致")
                assert False
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            assert False

    # 好价免费拿量操作手册-页面跳转
    def test_mall_new_traffic_operation_rule_link_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        # 准备跳转链接元素的xpath、url：更多经营数据
        rule_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div/div/div[2]/div/div/div[3]/header/a'
        rule_url = 'https://docs.qingque.cn/d/home/<USER>'
        try:
            self.wait_for_element_visible(rule_xpath)
            if self.is_exact_text_visible('查看详情', rule_xpath):
                self.click(rule_xpath)
                self.wait_for_ready_state_complete()

                print(f"页面点击跳转:{self.get_current_url()}")
                self.assert_url_contains(rule_url)
                time.sleep(2)
            else:
                print(f"页面点击跳转:{self.get_current_url()} ---与预期不一致")
                assert False
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            assert False

    # 注意事项-收起
    def test_mall_fold_up_button_online(self):
        self.test_mall_traffic_operation_online_init()
        self.wait_for_ready_state_complete()
        time.sleep(2)

        button_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div/div/div[1]/div[1]/span'
        exp_fold_up_name = "收起"
        exp_open_name = "展开"

        fold_up_name = self.get_text(button_xpath)
        assert fold_up_name == exp_fold_up_name

        self.click(button_xpath)
        time.sleep(2)

        open_name = self.get_text(button_xpath).split('\n')[0]
        assert open_name == exp_open_name