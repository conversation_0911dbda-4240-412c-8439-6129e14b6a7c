import time

import pytest
from _pytest.skipping import Skip

from ..base import BaseTestCase
import datetime
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class TestMallBestSellerLink(BaseTestCase):

    chuanliu_plan_domain = "CHUANLIU_PLAN_DOMAIN"
    account_login = "xiaoh<PERSON>shuang"

    def test_mall_chuanliu_plan_init(self):
        self.maximize_window()
        self.login(self.chuanliu_plan_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)

    def test_mall_chuanliu_plan_ele(self):
        self.test_mall_chuanliu_plan_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        # 验证整体数据
        total_data_title_xpath = '//*[@id="dilu_micro_root"]/div/div/div[2]/div[1]/div[1]'
        self.assert_text("商品卡川流数据", total_data_title_xpath)

        total_data_core_xpath = '//*[@id="dilu_micro_root"]/div/div/div[2]/div[1]/div[2]'
        self.assert_text("核心影响因子", total_data_core_xpath)
        self.assert_text("近14天被分销订单数", total_data_core_xpath)

        # 验证整体数据指标卡
        total_card_xpath = '//*[@id="dilu_micro_root"]/div/div/div[2]/div[1]/div[4]'
        self.assert_text("商品卡总曝光", total_card_xpath)
        self.assert_text("商品卡总点击次数", total_card_xpath)
        self.assert_text("商品卡总订单量", total_card_xpath)
        self.assert_text("商品卡总支付金额", total_card_xpath)
        improvement_xpath = '//*[@id="dilu_micro_root"]/div/div/div[2]/div[2]'
        self.assert_text("提升手段", improvement_xpath)
        self.assert_text("方式1: ", improvement_xpath)
        self.assert_text("方式2: 发布新品，丰富货盘增加商城推荐机会", improvement_xpath)

        # 验证商品列表信息
        list_title_xpath = '//*[@id="dilu_micro_root"]/div/div/div[2]/div[3]/div[1]'
        self.assert_text("我的商品卡川流流量榜单", list_title_xpath)
        self.assert_text("对比周期", list_title_xpath)
        list_table_title_xpath = '//*[@id="tableId"]/div/div/table/thead'
        self.assert_text("商品信息", list_table_title_xpath)
        self.assert_text("商品卡曝光量", list_table_title_xpath)
        self.assert_text("商品卡川流曝光量", list_table_title_xpath)
        self.assert_text("操作", list_table_title_xpath)



