from time import sleep

from ..base import BaseTestCase
from unittest import skip

from selenium.webdriver.common.by import By


class TestMallGrowthTraffic(BaseTestCase):

    # 门槛拦截账号
    def test_enter_mall_growth_traffic_init(self):
        self.login(domain="COLD_START_DOMAIN", account="returnnull")

    # 扶持页面账号
    def test_enter_mall_growth_traffic(self):
        self.login(domain="COLD_START_DOMAIN", account="gaohuzhen")

    # 门槛页 tab文案
    def test_ele_text(self):
        self.test_enter_mall_growth_traffic_init()

        # 新品免费起量
        self.assert_text("新品免费起量", "//span[contains(text(), '新品免费起量')]")
        self.assert_text("一亿+曝光扶持", "//span[contains(text(), '一亿+曝光扶持')]")
        self.assert_text("符合条件的新品将免费获得平台流量扶持，不发短视频不直播也能轻松起量！",
                         "//span[contains(text(),'符合条件的新品将免费获得平台流量扶持')]")
        self.assert_text("14天内新发布的商品，需报名参与营销托管，并符合商城推荐准入标准及其他新品起量门槛要求的商品",
                         "//span[contains(text(),'14天内新发布的商品')]")

        # 商品助推打爆
        self.assert_text("商品助推打爆", "//span[contains(text(), '商品助推打爆')]")
        self.assert_text("3亿+曝光扶持", "//span[contains(text(), '3亿+曝光扶持')]")
        self.assert_text("通过平台助推打爆两个阶段流量扶持，打造货架爆款，曝光率最高猛增100%！",
                         "//span[contains(text(),'通过平台助推打爆两个阶段流量扶持')]")
        self.assert_text("30天内新发布的商品，需报名参与营销托管，并符合商城推荐准入标准及其他助推打爆门槛要求的商品",
                         "//span[contains(text(),'30天内新发布的商品')]")

        # 新品免费起量 跳链
        element = self.find_elements("//span[contains(text(), '详细规则')]")[0]
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        self.check_jump_link_by_element(element, "详细规则", expected_url, has_href=False)

        # 商品助推打爆 跳链
        element = self.find_elements("//span[contains(text(), '详细规则')]")[1]
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        self.check_jump_link_by_element(element, "详细规则", expected_url, has_href=False)

    # 门槛页 列表
    def test_threshold_page(self):
        self.test_enter_mall_growth_traffic_init()

        # 新品免费起量
        th_text = ["商家要求", "商品要求", "具体要求说明", "是否符合", "不符合要求原因"]
        for text in th_text:
            self.assert_text(text, f"//th[contains(text(), '{text}')]")

        td_text = ["经营状态正常", "店铺类型", "商品销量", "报名营销托管", "商家商品体验分",
                   "发品状态正常", "店铺未售假", "有14日内店铺新品", "无重复铺货商品"]
        for text in td_text:
            self.assert_text(text, f"//td[contains(text(), '{text}')]")

        span_text = ["店铺经营状态正常，无明显违规、负向管控及其他触犯平台规则行为",
                     "旗舰店，专卖店，专营店，卖场旗舰店，普通企业店，个体工商户，跨境店铺等非个人店铺",
                     "近30日内商城商品卡销量>=10单",
                     "需要报名营销托管",
                     "个体工商户商家商品体验分>=4.6分，旗舰店、专卖店、专营店、卖场旗舰店、普通企业店、跨境店铺等商家商品体验分>=4.4分（非默认分）",
                     "店铺发布商品状态正常",
                     "店铺及关联店铺无因出售假冒商品而被触发的历史记录",
                     "商家14日内有发布并上架的新品",
                     "14日内店铺新品中无重复铺货的商品"]
        for text in span_text:
            self.assert_text(text, f"//span[contains(text(), '{text}')]")

        # 商品助推打爆
        self.click("//span[contains(text(), '商品助推打爆')]")
        th_text = ["商家要求", "具体要求说明", "是否符合", "不符合要求原因"]
        for text in th_text:
            self.assert_text(text, f"//th[contains(text(), '{text}')]")

        td_text = ["经营状态正常", "30日内店铺新品", "流量扶持范围", "店铺未售假", "商家商品体验分"]
        for text in td_text:
            self.assert_text(text, f"//td[contains(text(), '{text}')]")

        span_text = ["店铺经营状态正常，无明显违规、负向管控及其他触犯平台规则行为",
                     "30日内有首次上架新品",
                     "当前未报名营销托管",
                     "店铺及关联店铺无因出售假冒商品而被触发的历史记录",
                     "个体工商户、旗舰店、专卖店、专营店、卖场旗舰店、普通企业店、跨境店铺等商家商品体验分>=4.4分（非默认分）"]
        for text in span_text:
            self.assert_text(text, f"//span[contains(text(), '{text}')]")

    # 进入扶持页面 新品免费起量
    def test_new_cold_start_support_page(self):
        self.test_enter_mall_growth_traffic()

        assert "可扶持新品" in self.find_element("//span[contains(text(), '可扶持新品')]").text
        assert "扶持中" in self.find_element("//span[contains(text(), '扶持中')]").text
        assert "已完成" in self.find_element("//span[contains(text(), '已完成')]").text

        label_text = ["商品ID", "商品标题", "类目名称"]
        for text in label_text:
            self.assert_text(text, f"//label[contains(text(), '{text}')]")

        self.assert_text("孵化状态", "//div[contains(text(), '孵化状态')]")

        span_text = ["商品信息", "首次上架时间", "上新天数", "扶持状态", "成长建议", "近30天商城推荐商品数据"]
        for text in span_text:
            self.assert_text(text, f"//span[contains(text(), '{text}')]")

        url = "//a[contains(text(), '查看详细规则')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        self.check_jump_link(url, "查看详细规则", expected_url, login_func=self.test_enter_mall_growth_traffic,
                             need_login=False, has_href=True, need_click=True)
