from datetime import datetime
import time
import re

import pytest
from _pytest.skipping import Skip
from selenium.webdriver import ActionChains

from ..base import BaseTestCase
from unittest import skip


@skip
class TestMallBusinessListGoodsList(BaseTestCase):
    business_list_domain = "BUSINESS_LIST_DOMAIN"
    account_login = "bjfeng"

    # 登录，进市场榜单-商品榜
    def test_mall_business_list_init(self):
        self.maximize_window()
        self.login(self.business_list_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)
        self.click_popup_window()

    # 商品榜 goodsList
    # 页面元素校验
    def test_mall_goods_list_ele(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)

        # 验证标题、tab、数据筛选
        self.wait_for_element_visible('//*[@id="root"]/div/div/div/div[1]/span', "css xpath", 4)
        self.assert_text("市场榜单", '//*[@id="root"]/div/div/div/div[1]/span')
        self.assert_text("商品榜", '//*[@id="rc-tabs-0-tab-mallItemRank"]/span')
        self.assert_text("商家榜", '//*[@id="rc-tabs-0-tab-mallSellerRank"]/span')
        self.assert_text("数据筛选", '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]')

        #验证筛选项
        search_name = ["行业类目", "价格范围", "是否品牌", "销售渠道", "排序方式"]
        search_xpath='//*[@id="pro-tag-form-wrapper"]/div['
        for i in range(5):
            self.assert_text(search_name[i], search_xpath + f'{i + 1}'+']/div[1]/label')
        #验证筛选项下各选项
        search_name_sfpp = ["不限品牌", "知名品牌", "非知名品牌"]
        search_name_xsqd = ["不限方式", "店铺页", "推荐", "搜索"]
        search_name_pxfs = ["成交金额降序", "点击次数降序", "转化率降序"]
        search_xpath_sfpp = '//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div'
        search_xpath_xsqd = '//*[@id="pro-tag-form-wrapper"]/div[4]/div[2]/div/div/div/div/div'
        search_xpath_pxfs = '//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div'
        for i in range(3):
            self.assert_text(search_name_sfpp[i], search_xpath_sfpp+f'[{i+1}]')
            self.assert_text(search_name_pxfs[i], search_xpath_pxfs + f'[{i + 1}]')
        for i in range(4):
            self.assert_text(search_name_xsqd[i], search_xpath_xsqd+f'[{i+1}]')
        #验证表头字段
        th_name = ["排名","商品信息","所属店铺","成交金额","点击次数","成交增速","转化率","近30天成交趋势"]
        th_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th'
        for i in range(8):
            self.assert_text(th_name[i], th_xpath+f'[{i+1}]')

    # 数据筛选项-输入粗略校验
    @pytest.mark.skip
    def test_goods_list_filters_ele_input(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        # 行业类目 多级下拉选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[1]/div[2]/div/div/div/div/div/div/div/div/span[2]')
        self.click('//*[@id="pro-tag-form-wrapper"]/div[1]/div[2]/div/div/div/div/div/div/div/div/span[2]')
        # self.assert_text("女装亲子行业 / 内衣/家居服/袜子",'//*[@id="pro-tag-form-wrapper"]/div[1]/div[2]/div/div/div/div/div/div/div/div/span[2]')

        # 价格范围手动选择
        self.type('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[1]/div/div[1]/div/input', "1")
        self.type('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[2]/div/div[1]/div/input', "10")
        self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[1]/label')
        self.assert_text("1", '//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[1]/div/div[1]/div/input')
        self.assert_text("10", '//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[2]/div/div[1]/div/input')
        time.sleep(2)
        # 价格范围点击选择
        self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[3]/div[2]/div')
        self.assert_text("20",'//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[1]/div/div[1]/div/input')
        self.assert_text("50", '//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[2]/div/div[1]/div/input')
        time.sleep(2)

        # 是否品牌取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[1]')
        # 销售渠道取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[4]/div[2]/div/div/div/div/div[1]')
        # 排序方式取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div[1]')
        # 收起全部筛选校验选中数量
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        self.assert_text("已选2个", '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]/span')

    # 收起全部筛选，校验选中数量
    @pytest.mark.skip
    def test_goods_list_collapse_all_filters(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        # 未填价格
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        self.assert_text("已选4个", '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]/span')
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        time.sleep(2)
        # 填价格
        self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[3]/div[2]/div')
        self.assert_text("20", '//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[1]/div/div[1]/div/input')
        self.assert_text("50", '//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div[2]/div/div[1]/div/input')
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        self.assert_text("已选5个", '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]/span')
        time.sleep(2)


    #分页
    def test_goods_list_paging(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        if self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]') == '暂无数据':
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            text1 = self.get_text('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span')
            # 下一页
            self.click('//*[@title="下一页"]')
            time.sleep(2)
            text2 = self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span')

            assert text1 != text2

    #下载文件
    def test_goods_list_download(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()

        self.assert_text("下载数据",'//*[@id="root"]/div/div/div/div[2]/div[3]/div[1]/button/span')
        self.click('//*[@id="root"]/div/div/div/div[2]/div[3]/div[1]/button/span')
        time.sleep(2)

    # 数据筛选项-是否品牌切换校验
    def test_filters_ele_brand_switch(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        have_data_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
        if self.get_text(have_data_xpath) == '暂无数据':
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            # 是否品牌-知名品牌
            self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[2]')
            time.sleep(5)

            if self.get_text(have_data_xpath) == '暂无数据':
                assert True
            else:
                text1 = self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div/div[1]/div[1]').text
                # 是否品牌-非知名品牌
                self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[3]')
                time.sleep(5)
                if self.get_text(have_data_xpath) == '暂无数据':
                    assert True
                else:
                    text2 = self.find_element(
                        '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div/div[1]/div[1]').text
                    assert text1 != text2

    # 数据筛选项-排序方式 - 成交金额降序
    def test_goods_list_transaction_amount_desc(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        have_data_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
        if self.get_text(have_data_xpath) == '暂无数据':
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            # 成交金额降序
            # self.click('//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div[1]')
            time.sleep(2)
            transaction_amount1 = self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div').text
            amount_1 = transaction_amount1.split('-')[-1]
            if amount_1[-1] == "万":
                amount_1_desc = float(amount_1.split("万")[0]) * 10000
            elif amount_1[-1] == "亿":
                amount_1_desc = float(amount_1.split("亿")[0]) * 100000000
            else:
                amount_1_desc = float(amount_1)
            print(amount_1_desc)

            #下一页
            self.click('//*[@title="下一页"]')
            time.sleep(2)
            transaction_amount2 =  self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div').text
            amount_2 = transaction_amount2.split('-')[-1]
            if amount_2[-1] == "万":
                amount_2_desc = float(amount_2.split("万")[0]) * 10000
            elif amount_2[-1] == "亿":
                amount_2_desc = float(amount_2.split("亿")[0]) * 100000000
            else:
                amount_2_desc = float(amount_2)
            print(amount_2_desc)

            assert amount_1_desc >= amount_2_desc


    # 数据筛选项-排序方式 - 点击次数降序
    def test_goods_list_click_count_desc(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        have_data_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
        if self.get_text(have_data_xpath) == '暂无数据':
            assert True
        elif int(self.get_text('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            # 点击次数降序
            self.click('//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div[2]')
            time.sleep(2)
            click_count1 = self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/div').text
            count_1 = click_count1.split('-')[-1]
            if count_1[-1] == "万":
                count_1_desc = float(count_1.split("万")[0]) * 10000
            elif count_1[-1] == "亿":
                count_1_desc = float(count_1.split("亿")[0]) * 100000000
            else:
                count_1_desc = float(count_1)
            print(count_1_desc)
            #下一页
            self.click('//*[@title="下一页"]')
            time.sleep(2)
            click_count2 =  self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/div').text
            count_2 = click_count2.split('-')[-1]
            if count_2[-1] == "万":
                count_2_desc = float(count_2.split("万")[0]) * 10000
            elif count_2[-1] == "亿":
                count_2_desc = float(count_2.split("亿")[0]) * 100000000
            else:
                count_2_desc = float(count_2)
            print(count_2_desc)

            assert count_1_desc >= count_2_desc


    # 数据筛选项-排序方式 - 转化率降序
    def test_goods_list_conversion_rate_desc(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        have_data_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
        if self.get_text(have_data_xpath) == '暂无数据':
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            # 转化率降序
            self.click('//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div[3]')
            time.sleep(2)
            conversion_rate1 = self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[7]/div').text
            match2 = re.search(r'\d+', conversion_rate1)
            if match2:
                rate1 = int(match2.group())
            #下一页
            self.click('//*[@title="下一页"]')
            time.sleep(2)
            conversion_rate2 =  self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[7]/div').text
            match = re.search(r'\d+', conversion_rate2)
            if match:
                rate2 = int(match.group())

            assert rate1 >= rate2


    #时间选择-昨日/近7天切换
    def test_good_list_time_selected(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)

        # 提取昨日日期
        self.click('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[2]/div[1]')
        time.sleep(1)
        time1 = self.get_text('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[1]/span[2]/span')
        # print(time1)
        # 提取近7日开始日期
        self.click('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[2]/div[2]')
        time.sleep(1)
        time2 = self.get_text('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[1]/span[2]/span[1]')
        # print(time2)
        # 将时间字符串转换成datetime对象
        datetime1 = datetime.strptime(time1, '%Y-%m-%d')
        datetime2 = datetime.strptime(time2, '%Y-%m-%d')

        # 转换成时间戳（单位：秒）
        timestamp1 = datetime1.timestamp()
        timestamp2 = datetime2.timestamp()

        # 计算时间差（单位：秒）
        time_difference = timestamp1 - timestamp2

        # 将时间差转换成天数
        days_difference = time_difference // (24 * 60 * 60) + 1

        print(f"两个时间的差为：{days_difference}天")

        # 对比日期差
        assert days_difference == 7

    # 点商品名称出现商品弹框
    def test_good_list_good_detail(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)

        good_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div[1]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span'
        detail_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div[2]/div[2]'
        detail_title_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div[2]/div[1]/div'
        if self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]') == '暂无数据':
            assert True
        else:
            self.click(good_xpath)
            time.sleep(2)
            if self.is_element_visible(detail_xpath):
                good_title = self.get_text(good_xpath)
                detail_title = self.get_text(detail_title_xpath)
                assert detail_title == good_title

    # 点近30天成交趋势出现弹框
    def test_good_list_30d_trend(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)

        good_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div[1]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span'
        c30d_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div/div/div/div/div/canvas'
        c30d_detail_xpath = '//*[@id="main-root"]/div[2]/div/div[2]/div/div[2]/div/div/div/div'
        detail_good_title_xpath = '//*[@id="main-root"]/div[2]/div/div[2]/div/div[2]/div/div/div/div/div[2]/div[1]/div/div/div[1]/div[1]'
        if self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]') == '暂无数据':
            assert True
        else:
            self.click(c30d_xpath)
            time.sleep(2)
            if self.is_element_visible(c30d_detail_xpath):
                good_title = self.get_text(good_xpath)
                c30d_good_detail_title = self.get_text(detail_good_title_xpath)
                assert c30d_good_detail_title == good_title