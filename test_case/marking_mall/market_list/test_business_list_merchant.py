import time
import re

import pytest
from _pytest.skipping import Skip

from ..base import BaseTestCase
from datetime import datetime
from unittest import skip


@skip
class TestMallBusinessListMerchantList(BaseTestCase):
    business_list_domain = "BUSINESS_LIST_DOMAIN"
    account_login = "bjfeng"

    # 登录，进市场榜单
    def test_mall_business_list_init(self):
        self.maximize_window()
        self.login(self.business_list_domain, self.account_login)
        self.assert_no_404_errors()
        # 关闭弹窗
        while self.is_element_visible('//*[@id="driver-popover-item"]'):
            self.refresh()
            time.sleep(1)
        time.sleep(2)

    # 商家榜tab切换
    def test_merchant_list_switch(self):
        self.test_mall_business_list_init()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        self.click('//*[@id="rc-tabs-0-tab-mallSellerRank"]/span')
        time.sleep(2)
        self.assert_text('店铺名称','//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th[2]')

    # 商品榜 goodsList
    # 页面元素校验
    def test_mall_goods_list_ele(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)

        # 验证标题、tab、数据筛选
        self.wait_for_element_visible('//*[@id="root"]/div/div/div/div[1]/span', "css xpath", 4)
        self.assert_text("市场榜单", '//*[@id="root"]/div/div/div/div[1]/span')
        self.assert_text("商品榜", '//*[@id="rc-tabs-0-tab-mallItemRank"]/span')
        self.assert_text("商家榜", '//*[@id="rc-tabs-0-tab-mallSellerRank"]/span')
        self.assert_text("数据筛选", '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]')

        #验证筛选项
        search_name = ["行业类目", "是否品牌","售卖方式", "售卖渠道", "销售渠道"]
        search_xpath='//*[@id="pro-tag-form-wrapper"]/div['
        for i in range(5):
            self.assert_text(search_name[i], search_xpath + f'{i + 1}'+']/div[1]/label')
        #验证筛选项下各选项
        search_name_sfpp = ["不限品牌", "知名品牌", "非知名品牌"]
        search_name_smfs = ["不限方式", "达人合作"]
        search_name_smqd = ["不限渠道", "直播间", "短视频","商品卡"]
        search_name_xsqd = ["不限方式", "店铺页", "推荐", "搜索"]
        search_xpath_sfpp = '//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div/div'
        search_xpath_smfs = '//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div'
        search_xpath_smqd = '//*[@id="pro-tag-form-wrapper"]/div[4]/div[2]/div/div/div/div/div'
        search_xpath_xsqd = '//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div'
        for i in range(2):
            self.assert_text(search_name_smfs[i], search_xpath_smfs+f'[{i+1}]')
        for i in range(3):
            self.assert_text(search_name_sfpp[i], search_xpath_sfpp+f'[{i+1}]')
        for i in range(4):
            self.assert_text(search_name_smqd[i], search_xpath_smqd+f'[{i+1}]')
            self.assert_text(search_name_xsqd[i], search_xpath_xsqd + f'[{i + 1}]')
        #验证表头字段
        th_name = ["排名","店铺名称","成交Top商品","成交金额","成交增速","在售商品数","店铺分","经营占比"]
        th_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/thead/tr/th'
        for i in range(8):
            self.assert_text(th_name[i], th_xpath+f'[{i+1}]')

    # 数据筛选项-输入粗略校验
    @pytest.mark.skip
    def test_merchant_list_filters_ele_input(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()
        time.sleep(2)
        # 是否品牌取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div/div[1]')
        # 售卖方式取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[1]')
        # 售卖渠道取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[4]/div[2]/div/div/div/div/div[1]')
        # 销售渠道取消默认选中
        self.click('//*[@id="pro-tag-form-wrapper"]/div[5]/div[2]/div/div/div/div/div[1]')
        # 收起全部筛选校验选中数量
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        count_xpath = '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]/span/span'
        count = int(self.find_element(count_xpath).text)
        print(count)
        if not self.assert_text("1", count_xpath):
            assert count == 3
        else:
            assert count == 1

    # 收起全部筛选，校验选中数量
    @pytest.mark.skip
    def test_goods_list_collapse_all_filters(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        time.sleep(2)
        count_xpath = '//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[1]/span/span'
        count = int(self.find_element(count_xpath).text)
        print(count)
        if not self.assert_text("5", count_xpath):
            assert count == 3
        else:
            assert count == 5
        self.click('//*[@id="root"]/div/div/div/div[2]/div[2]/div/div/div/div/span[2]')
        time.sleep(2)


    # 数据筛选项-是否品牌切换校验
    def test_filters_ele_brand_switch(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()
        time.sleep(2)
        have_data_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
        if self.get_text(have_data_xpath) == '暂无数据':
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            # 是否品牌-知名品牌
            self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div/div[2]')
            time.sleep(5)
            if self.get_text(have_data_xpath) == '暂无数据':
                assert True
            else:
                text1 = self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div[1]/div[1]').text
                # 是否品牌-非知名品牌
                self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div/div[3]')
                time.sleep(5)
                if self.get_text(have_data_xpath) == '暂无数据':
                    assert True
                else:
                    text2 = self.find_element('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div[1]/div[1]').text
                    assert text1 != text2

    # 分页
    def test_merchant_list_paging(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)
        have_data_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]'
        if self.get_text(have_data_xpath) == '暂无数据':
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) <= 10:
            assert True
        elif int(self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[1]/div/div/div/div/div/span')) > 10:
            text1 = self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div[1]/div[1]')
            # 下一页
            self.click('//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/ul/li[10]')
            time.sleep(2)
            text2 = self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div[1]/div[1]')
            assert text1 != text2

   #下载文件
    def test_merchant_list_download(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()

        self.assert_text("下载数据", '//*[@id="root"]/div/div/div/div[2]/div[3]/div[1]/button/span')
        self.click('//*[@id="root"]/div/div/div/div[2]/div[3]/div[1]/button/span')
        time.sleep(2)

    # 时间选择-昨日/近7天切换
    def test_merchant_list_time_selected(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()

        # 提取昨日日期
        self.click('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[2]/div[1]')
        time.sleep(1)
        time1 = self.get_text('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[1]/span[2]/span')
        # print(time1)
        # 提取近7日开始日期
        self.click('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[2]/div[2]')
        time.sleep(1)
        time2 = self.get_text('//*[@id="root"]/div/div/div/div[2]/div[1]/div/div[2]/div/div[1]/span[2]/span[1]')
        # print(time2)
        # 将时间字符串转换成datetime对象
        datetime1 = datetime.strptime(time1, '%Y-%m-%d')
        datetime2 = datetime.strptime(time2, '%Y-%m-%d')

        # 转换成时间戳（单位：秒）
        timestamp1 = datetime1.timestamp()
        timestamp2 = datetime2.timestamp()

        # 计算时间差（单位：秒）
        time_difference = timestamp1 - timestamp2

        # 将时间差转换成天数
        days_difference = time_difference // (24 * 60 * 60) + 1

        print(f"两个时间的差为：{days_difference}天")

        # 对比日期差
        assert days_difference == 7

    # 点查看top10商品名称出现商品列表
    def test_merchant_list_good_list(self):
        self.test_merchant_list_switch()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)

        merchant_name_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div[1]/div[1]'
        top10_xpath = '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[2]'
        top10_list_xpath = '//*[@id="main-root"]/div[3]/div/div[2]/div/div'
        top10_list_title_xpath = '//*[@id="main-root"]/div[3]/div/div[2]/div/div/div[1]/div[1]/div'
        top10_merchant_name_xpath = '//*[@id="rank-list"]/div[1]/div/div/div[1]/div[1]'

        if self.get_text(
                '//*[@id="root"]/div/div/div/div[2]/div[3]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]') == '暂无数据':
            assert True
        elif self.is_element_visible(top10_list_xpath):
            self.click(top10_xpath)
            time.sleep(3)
            top10_list_title = self.get_text(top10_list_title_xpath)
            expect_top10_list_title = "店铺成交TOP 10"
            assert top10_list_title == expect_top10_list_title
            merchant_name = self.get_text(merchant_name_xpath)
            top10_merchant_name = self.get_text(top10_merchant_name_xpath)
            assert top10_merchant_name == merchant_name