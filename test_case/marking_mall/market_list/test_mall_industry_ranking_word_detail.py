import datetime
import time
import re

import pytest

from ..base import BaseTestCase


class TestMallIndustryRankingWordDetail(BaseTestCase):
    industry_ranking_domain = "INDUSTRY_RANKING_DOMAIN"
    account_login = "gaohuzhen"

    # 登录，进行业趋势
    def test_mall_industry_ranking_hot(self):
        self.maximize_window()
        self.login(self.industry_ranking_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        time.sleep(5)

    # 进行业趋势，跳转搜索词详情页
    def test_industry_ranking_word_detail(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()
        # 定位搜索词、跳转链接
        word = self.get_text(
            '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div')
        link_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[10]/div/button/span'
        # 定位数据更新时间
        yesterday = str(self.get_text('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[2]/div/div[1]/span[4]/span[3]'))

        # 当前日期前一天
        expect_day = str(datetime.date.today() - datetime.timedelta(days=1))
        # 判断数据是否更新
        if yesterday == expect_day:
            #拼接跳转链接
            currentEndDay = str(datetime.date.today()-datetime.timedelta(days=1))
            compareEndDay = str(datetime.date.today() - datetime.timedelta(days=8))
            link_url = "https://s.kwaixiaodian.com/zone/industryRanking/IndustryDetail?backName=行业趋势&keywords="+word+"&timeRange=SEVEN_DAY&currentEndDay="+currentEndDay+"&compareEndDay="+compareEndDay
            #点击搜索词详情
            self.click(link_xpath)
            time.sleep(2)
            cur_link = self.get_current_url()
            assert cur_link == link_url
            # 校验跳转页面搜索词与预期是否一致
            self.wait_for_ready_state_complete()
            word2 = self.get_text('//*[@id="root"]/div/div/div[2]/span')
            assert word == word2
        else:
            # 拼接跳转链接
            currentEndDay = str(datetime.date.today() - datetime.timedelta(days=2))
            compareEndDay = str(datetime.date.today() - datetime.timedelta(days=9))
            link_url = "https://s.kwaixiaodian.com/zone/industryRanking/IndustryDetail?backName=行业趋势&keywords=" + word + "&timeRange=SEVEN_DAY&currentEndDay=" + currentEndDay + "&compareEndDay=" + compareEndDay
            # 点击搜索词详情
            self.click(link_xpath)
            time.sleep(2)
            cur_link = self.get_current_url()
            assert cur_link == link_url
            # 校验跳转页面搜索词与预期是否一致
            self.wait_for_ready_state_complete()
            word2 = self.get_text('//*[@id="root"]/div/div/div[2]/span')
            assert word == word2

    # 验证页面元素
    def test_word_detail_ele(self):
        self.test_industry_ranking_word_detail()
        self.wait_for_ready_state_complete()

        self.assert_text("趋势分析",'//*[@id="root"]/div/div/div[3]/div[1]/span')
        # 指标、xpath
        index_xpath = '//*[@id="root"]/div/div/div[3]/div[2]'
        index_content = ["搜索热度指数", "关联内容曝光指数", "关联商品曝光指数", "点击热度指数", "点击率", "点击-成交转化率", "成交指数"]
        # 验证指标是否存在
        for i in range(7):
            self.assert_text(index_content[i], index_xpath)
        # 验证趋势图是否存在
        self.is_element_visible('//*[@id="root"]/div/div/div[3]/div[3]/div/div/div/div/div/canvas')
        # 验证相关商品列表
        self.assert_text("相关商品列表",'//*[@id="root"]/div/div/div[4]/div/div/div[1]/span')
        self.assert_text("行业分布", '//*[@id="pro-form-wrapper"]/div/div[1]/div/div[1]/label')
        self.assert_text("价格带",'//*[@id="pro-form-wrapper"]/div/div[2]/div/div[1]/label')
        self.assert_text("排序方式",'//*[@id="pro-form-wrapper"]/div/div[3]')
        self.assert_text("成交金额降序", '//*[@id="pro-form-wrapper"]/div/div[3]')
        self.assert_text("商品曝光降序", '//*[@id="pro-form-wrapper"]/div/div[3]')
        self.assert_text("商品点击降序", '//*[@id="pro-form-wrapper"]/div/div[3]')
        # 验证表头
        thead_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/thead/tr'
        thead_content = ["排名","商品信息","价格带","店铺名称","成交金额指数","商品曝光指数","商品点击指数"]
        for i in range(7):
            self.assert_text(thead_content[i], thead_xpath)
        # 面包屑验证
        bread_xpath = '//*[@id="root"]/div/div/div[1]'
        self.assert_text("行业趋势", bread_xpath)
        self.assert_text("搜索词详情", bread_xpath)

    # 趋势对比按钮
    def test_tendency_contrast(self):
        self.test_industry_ranking_word_detail()
        self.wait_for_ready_state_complete()

        # 验证趋势图是否存在
        trend_chart = '//*[@id="root"]/div/div/div[3]/div[3]/div/div/div/div/div/canvas'
        self.is_element_visible(trend_chart)

        # 趋势对比按钮定位
        button_xpath = '//*[@id="root"]/div/div/div[3]/div[1]/div/button'
        if self.is_element_visible(button_xpath):
            self.click(button_xpath)
            time.sleep(2)
            assert self.is_element_visible(trend_chart) != True

    # 相关商品列表排序方式切换
    def test_order_method_switch(self):
        self.test_industry_ranking_word_detail()
        self.wait_for_ready_state_complete()

        # 确认列表是否有数据
        total_count_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]'
        print(self.is_element_visible(total_count_xpath))
        if self.get_text('//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr/td/div/div[2]') == '暂无数据':
            assert True
        elif self.is_element_visible(total_count_xpath):
            # 确认是否有1条以上数据
            total_count_word = self.get_text(total_count_xpath)
            all_counts = re.findall(r'\d+', total_count_word)
            if all_counts:
                total_count = int(all_counts[-1])
            if total_count <= 1:
                assert True
            if total_count > 1:
                # xpath准备 商品曝光、商品点击
                spbg_xpath = '//*[@id="pro-form-wrapper"]/div/div[3]/div/div[2]/div/div/div/div/div[2]'
                spdj_xpath = '//*[@id="pro-form-wrapper"]/div/div[3]/div/div[2]/div/div/div/div/div[3]'

                # 默认成交金额排序
                cjje_res1_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div'
                cjje_res2_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/div'

                cjje_res1 = self.get_text(cjje_res1_xpath)
                cjje_res2 = self.get_text(cjje_res2_xpath)
                print(cjje_res1, cjje_res2)

                if cjje_res1[-1] == "万":
                    cjje_desc_1 = float(cjje_res1.split("万")[0]) * 10000
                else:
                    cjje_desc_1 = float(cjje_res1)
                if cjje_res2[-1] == "万":
                    cjje_desc_2 = float(cjje_res2.split("万")[0]) * 10000
                else:
                    cjje_desc_2 = float(cjje_res2)
                print(cjje_desc_1, cjje_desc_2)
                assert cjje_desc_1 >= cjje_desc_2

                # 切换商品曝光降序
                self.click(spbg_xpath)
                time.sleep(2)

                spbg_res1_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[6]/div'
                spbg_res2_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[6]/div'
                spbg_res1 = self.get_text(spbg_res1_xpath)
                spbg_res2 = self.get_text(spbg_res2_xpath)
                print(spbg_res1, spbg_res2)
                assert float(spbg_res1) >= float(spbg_res2)

                # 切换商品点击降序
                self.click(spdj_xpath)
                time.sleep(2)

                spdj_res1_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[7]/div'
                spdj_res2_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[7]/div'
                spdj_res1 = self.get_text(spdj_res1_xpath)
                spdj_res2 = self.get_text(spdj_res2_xpath)
                print(spdj_res1, spdj_res2)
                assert float(spdj_res1) >= float(spdj_res2)

    # 相关商品列表分页
    @pytest.mark.skip
    def test_about_goods_list_paging(self):
        self.test_industry_ranking_word_detail()
        self.wait_for_ready_state_complete()
        time.sleep(2)

        total_count_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]'
        next_page_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[4]/a'

        # 判断列表是否为空
        if self.get_text(
                '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr/td/div/div[2]') == '暂无数据':
            assert True
        elif self.is_element_visible(total_count_xpath):
            # 判断是否有下一页
            total_count_word = self.get_text(total_count_xpath)
            all_counts = re.findall(r'\d+', total_count_word)
            if all_counts:
                total_count = int(all_counts[-1])
            if total_count <= 10:
                assert True
            if total_count > 10:
                # 商品信息定位
                good_title_xpath = '//*[@id="root"]/div/div/div[4]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div[2]/div/div/span'
                good_title1 = self.get_text(good_title_xpath)

                # 点下一页
                self.click(next_page_xpath)
                time.sleep(2)
                good_title2 = self.get_text(good_title_xpath)

                assert good_title1 != good_title2


    # 面包屑点击返回上一页
    def test_go_back(self):
        self.test_industry_ranking_word_detail()
        self.wait_for_ready_state_complete()
        time.sleep(2)

        # 上一页面包屑定位、url
        go_back_xpath = '//*[@id="root"]/div/div/div[1]/span[1]'
        go_back_url ='https://s.kwaixiaodian.com/zone/industryRanking/trendsPage'
        self.click(go_back_xpath)
        time.sleep(2)
        cur_link = self.get_current_url()
        assert cur_link == go_back_url
