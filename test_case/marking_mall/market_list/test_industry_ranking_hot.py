import datetime
import time
import re

from _pytest.skipping import Skip

from ..base import BaseTestCase
from unittest import skip


# @skip
class TestMallIndustryRankingHotList(BaseTestCase):
    industry_ranking_domain = "INDUSTRY_RANKING_DOMAIN"
    account_login = "gaohuzhen"

    # 登录，进行业趋势-热搜榜
    def test_mall_industry_ranking_hot(self):
        self.maximize_window()
        self.login(self.industry_ranking_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        time.sleep(5)

    # 页面元素校验
    def test_industry_ranking_hot_ele(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()

        # 校验title
        self.wait_for_element_visible('//*[@id="root"]/div/div/div[1]/span')
        self.assert_text("行业趋势",'//*[@id="root"]/div/div/div[1]/span')
        # 校验tab
        tab_xpath = '//*[@id="root"]/div/div/div[2]/div/div[1]/div'
        ele = self.get_text(tab_xpath)
        self.assert_text("热搜榜",tab_xpath)
        self.assert_text("飙升榜", tab_xpath)
        # 校验筛选项
        search_xpath = '//*[@id="root"]/div/div/div[2]/div/div[2]/form'
        ele = self.get_text(tab_xpath)
        self.assert_text("类目选择", search_xpath)
        self.assert_text("搜索意图", search_xpath)
        self.assert_text("搜商品", search_xpath)
        self.assert_text("搜主播/店铺", search_xpath)
        self.assert_text("其他", search_xpath)
        # 校验表头
        thead_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead'
        thead = ["排名", "搜索词", "搜索热度指数", "关联商品曝光指数", "关联内容曝光指数", "点击热度指数", "曝光-点击转化率（次数）", "点击-成交转化率（次数）", "成交指数", "操作"]
        for i in range(len(thead)):
            self.assert_text(thead[i], thead_xpath)

    # 搜索词详情、跳转链接
    def test_word_detail_jump_link(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()

        # 定位搜索词、跳转链接
        word = self.get_text('//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div')
        link_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[10]/div/button/span'

        # 定位数据更新时间
        yesterday = str(self.get_text('//*[@id="root"]/div/div/div[2]/div/div[1]/div/div[2]/div/div[1]/span[4]/span[3]'))
        print(yesterday)
        # 当前日期前一天
        expect_day = str(datetime.date.today() - datetime.timedelta(days=1))
        # 判断数据是否更新
        if yesterday == expect_day:
            # 拼接跳转链接
            currentEndDay = str(datetime.date.today()-datetime.timedelta(days=1))
            compareEndDay = str(datetime.date.today() - datetime.timedelta(days=8))
            link_url = "https://s.kwaixiaodian.com/zone/industryRanking/IndustryDetail?backName=行业趋势&keywords="+word+"&timeRange=SEVEN_DAY&currentEndDay="+currentEndDay+"&compareEndDay="+compareEndDay
            # 点击搜索词详情
            self.click(link_xpath)
            time.sleep(2)
            cur_link = self.get_current_url()
            assert cur_link == link_url
            # 校验跳转页面搜索词与预期是否一致
            self.wait_for_ready_state_complete()
            word2 = self.get_text('//*[@id="root"]/div/div/div[2]/span')
            assert word == word2
        else:
            # 拼接跳转链接
            currentEndDay = str(datetime.date.today() - datetime.timedelta(days=2))
            compareEndDay = str(datetime.date.today() - datetime.timedelta(days=9))
            link_url = "https://s.kwaixiaodian.com/zone/industryRanking/IndustryDetail?backName=行业趋势&keywords=" + word + "&timeRange=SEVEN_DAY&currentEndDay=" + currentEndDay + "&compareEndDay=" + compareEndDay
            # 点击搜索词详情
            self.click(link_xpath)
            time.sleep(2)
            cur_link = self.get_current_url()
            assert cur_link == link_url
            # 校验跳转页面搜索词与预期是否一致
            self.wait_for_ready_state_complete()
            word2 = self.get_text('//*[@id="root"]/div/div/div[2]/span')
            assert word == word2

    # 下载数据
    def test_hot_words_download(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()
        self.assert_text("下载数据", '//*[@id="root"]/div/div/div[2]/div/div[2]/div/div[2]/div[2]/button/span')
        self.click('//*[@id="root"]/div/div/div[2]/div/div[2]/div/div[2]/div[2]/button/span')
        time.sleep(2)

    # 筛选项切换-搜索意图
    def test_hot_filters_switch(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()

        #搜索意图-三个选项
        search_good_xpath = '//*[@id="root"]/div/div/div[2]/div/div[2]/form/div[2]/div[2]/div/div/div/div/div[1]'
        search_shop_xpath = '//*[@id="root"]/div/div/div[2]/div/div[2]/form/div[2]/div[2]/div/div/div/div/div[2]'
        search_other_xpath = '//*[@id="root"]/div/div/div[2]/div/div[2]/form/div[2]/div[2]/div/div/div/div/div[3]'
        search_word_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div'
        #校验搜索词
        self.click(search_shop_xpath)
        time.sleep(2)
        shop_word = self.get_text(search_word_xpath)
        self.click(search_other_xpath)
        time.sleep(2)
        other_word = self.get_text(search_word_xpath)
        self.click(search_good_xpath)
        time.sleep(2)
        good_word = self.get_text(search_word_xpath)
        assert shop_word != other_word != good_word

    # 筛选项切换-搜索词输入
    def test_hot_filters_input(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()

        search_word_xpath = '//*[@id="root"]/div/div/div[2]/div/div[2]/div/div[1]/span/input'
        result_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div'
        search_word = self.get_text(result_xpath)
        self.click(search_word_xpath)
        self.input(search_word_xpath, search_word)
        time.sleep(2)
        res_word = self.get_text(result_xpath)
        assert res_word.find(search_word) != -1

    # 热度指数排序
    def test_order_by_heat_index(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()

        # 定位搜索热度指数xpath，点一次升序，点第二次降序
        heat_index_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[3]/div[1]/span[1]'
        heat_index_value_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div'
        self.click(heat_index_xpath)
        time.sleep(2)
        heat_index_asc = self.get_text(heat_index_value_xpath)
        if heat_index_asc[-1] == "万":
            index_asc = float(heat_index_asc.split("万")[0]) * 10000
        else:
            index_asc = float(heat_index_asc)
        self.click(heat_index_xpath)
        time.sleep(2)
        heat_index_desc = self.get_text(heat_index_value_xpath)
        if heat_index_desc[-1] == "万":
            index_desc = float(heat_index_desc.split("万")[0])*10000
        else:
            index_desc = float(heat_index_desc)
        assert index_asc <= index_desc

    # 分页
    def test_industry_ranking_hot_paging(self):
        self.test_mall_industry_ranking_hot()
        self.wait_for_ready_state_complete()
        # 确认是否有下一页
        total_count_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/ul/li[1]'
        total_count_word = self.get_text(total_count_xpath)
        all_counts = re.findall(r'\d+', total_count_word)
        if all_counts:
            total_count = int(all_counts[-1])
        if total_count > 10:
            # 搜索词定位
            search_word_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div'
            word1 = self.get_text(search_word_xpath)
            # 第一页 1-10 共 100 条 取 1
            total_count_word_1 = self.get_text(total_count_xpath)
            all_counts_1 = re.findall(r'\d+', total_count_word_1)
            if all_counts_1:
                start_count_1 = int(all_counts_1[0])

            # 定位下一页按钮
            next_page_button_xpath = '//*[@id="root"]/div/div/div[2]/div/div[3]/div/div/div/div/div/div/div/div/div/ul/li[10]/button'
            self.click(next_page_button_xpath)
            time.sleep(2)
            word2 = self.get_text(search_word_xpath)
            # 第二页 11-20 共 100 条 取 11
            total_count_word_2 = self.get_text(total_count_xpath)
            all_counts_2 = re.findall(r'\d+', total_count_word_2)
            if all_counts_2:
                start_count_2 = int(all_counts_2[0])

            assert word1 != word2
            assert start_count_1 < start_count_2