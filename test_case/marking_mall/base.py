import logging

from time import sleep

from selenium.webdriver.common.by import By
from seleniumbase import BaseCase
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.kwaixiaodianUtils import KwaiXiaoDianToolTest

account_user = get_account_info("sanzhimao")
host = get_domain("MARKING_MALL_DOMAIN")

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 创建控制台处理器并设置级别
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
# 将处理器添加到日志记录器
logger.addHandler(console_handler)


class BaseTestCase(KwaiXiaoDianToolTest):

    def login(self, domain, account, lane_id="PRT.test"):
        """
            前置登陆函数
        """
        # 若运行环境为prt时，指定泳道lane_id
        if self.var1 and self.var1 == 'prt':
            self.driver.header_overrides = {"trace-context": f'{{"laneId":"{lane_id}"}}'}
            host = get_domain_by_env(domain, self.var1)
            logger.info(f"current env prt with {lane_id}")
        # 运行线上环境
        else:
            host = get_domain_by_env(domain, 'online')
            logger.info("current env:online")

        self.kwaixiaodian_login(account)
        self.open(host)
        self.wait_for_ready_state_complete()
        self.assert_no_404_errors()
        self.click_popup_window()

    def check_jump_link(self, xpath, expected_text, expected_url, login_func,
                        need_login=True, has_href=True, need_click=True):
        """
            校验跳链
        """
        if need_login:
            login_func()
        if self.is_element_present(xpath):
            # 检查文案
            self.assert_text(expected_text, xpath)
            logger.info(f"文案: {self.get_text(xpath)}")

            if has_href:
                link_element = self.find_element(xpath)
                link_url = link_element.get_attribute("href")
                assert expected_url in link_url
            if need_click:
                self.click(xpath)
                self.switch_to_newest_window()
                self.wait_for_ready_state_complete()
                self.sleep(1)
                self.assert_url_contains(expected_url)
                logger.info(f"链接: {self.get_current_url()}")
                self.switch_to_window(0)
        assert f'跳链元素不存在'

    def check_jump_link_by_element(self, element, expected_text, expected_url, has_href=True):
        """
            校验跳链(元素入参)
        """
        # 检查文案
        assert element.text == expected_text
        logger.info(f"文案: {element.text}")

        if has_href:
            link_url = element.get_attribute("href")
            self.assert_equal(link_url, expected_url)
        element.click()
        self.switch_to_newest_window()
        self.wait_for_ready_state_complete()
        self.sleep(1)
        self.assert_url_contains(expected_url)
        logger.info(f"链接: {self.get_current_url()}")
        self.switch_to_window(0)

    def click_popup_window(self):
        """
            处理弹窗
        """
        sleep(3)
        # 处理流量保护弹窗
        if self.is_element_visible("//span[contains(text(),'避免店内爆品')]"):
            self.click("/html/body/div[2]/div/div[2]/div/div[2]/div/div/button")
            sleep(1)

        # 处理经营建议弹窗
        while self.is_element_visible('#driver-popover-item'):
            self.click('#driver-page-overlay')
            sleep(1)

        # 处理冷启弹窗
        coldstart_popup_xpath = '//*[@class="kwaishop-store-coldStartl-modal-pc-modal-content"]'
        if self.is_element_visible(coldstart_popup_xpath):
            coldstart_popup = self.find_element(coldstart_popup_xpath)
            button = coldstart_popup.find_element(By.XPATH, './button')
            button.click()
            sleep(1)

        # 处理降价得流量弹窗
        depreciate_popup_xpath = '//*[@class="kwaishop-store-depreciate-modal-pc-modal-content"]'
        if self.is_element_visible(depreciate_popup_xpath):
            depreciate_popup = self.find_element(depreciate_popup_xpath)
            button = depreciate_popup.find_element(By.XPATH, './div/div/button')
            button.click()
            sleep(1)

        # 处理经营建议弹窗
        seller_main_popover_xpath = '//*[@class="seller-main-popover-inner-content"]'
        seller_helper_box_xpath = '//*[@id="kpro-tool-box--sellerHelperBox"]'
        if self.is_element_visible(seller_main_popover_xpath):
            seller_main_popover = self.find_element(seller_main_popover_xpath)
            button_1 = seller_main_popover.find_element(By.XPATH, './div/div[3]/div')
            button_1.click()
            sleep(1)

            seller_helper_box = self.find_element(seller_helper_box_xpath)
            button_2 = seller_helper_box.find_element(By.XPATH, './div/img[2]')
            button_2.click()
            sleep(1)

        # 处理爆款竞价弹窗
        # boost_xpath = '//*[@id="root"]/div/div/div/div/div/div[2]/div/div[2]/button'
        # if self.is_element_visible(boost_xpath):
        #     boost_button = self.find_element(boost_xpath)
        #     boost_button.click()
        #     sleep(1)

        # 处理爆品弹窗
        hot_selling_product_xpath ='/html/body/div[2]/div/div[2]/div/div[2]/div'
        if self.is_element_visible(hot_selling_product_xpath):
            hot_selling_button = self.find_element(hot_selling_product_xpath)
            sleep(4)
            hot_selling_button.click()
            sleep(1)

        # 处理发货通知
        seller_main_modal_xpath = '//*[@class="seller-main-modal-content"]'
        if self.is_element_visible(seller_main_modal_xpath):
            seller_main_modal = self.find_element(seller_main_modal_xpath)
            sleep(5)
            button = seller_main_modal.find_element(By.XPATH, './div[3]/div/button[1]')
            button.click()
            sleep(1)

        # 处理营销托管弹窗
        marketing_hosting_xpath = '//*[@id="dilu_micro_root"]/div/div/div/div/div[2]/div/div[2]/button/span'
        if self.is_element_visible(marketing_hosting_xpath):
            marketing_hosting_button = self.find_element(marketing_hosting_xpath)
            marketing_hosting_button.click()
            sleep(1)

        # 处理商城推荐准入弹窗
        mall_recommend_xpath = '//*[@class="kwaishop-store-admittance-qualification-pc-modal-body"]'
        if self.is_element_visible(mall_recommend_xpath):
            mall_recommend_popover = self.find_element(mall_recommend_xpath)
            recommend_button = mall_recommend_popover.find_element(By.XPATH, './div/div/button[1]')
            recommend_button.click()
            sleep(1)

        # 处理为您推荐
        recommend_xpath = '//*[@class="zqkOZIyCpVY51qdNuCM1"]'
        if self.is_element_visible(recommend_xpath):
            zoom_button = self.find_element(recommend_xpath)
            zoom_button.click()
            sleep(1)

        # 处理为你推荐
        recommend_close_icon_xpath = '//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img'
        if self.is_element_visible(recommend_close_icon_xpath):
            close_icon = self.find_element(recommend_close_icon_xpath)
            close_icon.click()
            sleep(1)

        # 处理竞价
        # boost_xpath = '//*[@id="root"]/div/div/div/div/div/div[2]/div/div[2]/button'
        # if self.is_element_visible(boost_xpath):
        #     boost_button = self.find_element(boost_xpath)
        #     boost_button.click()
        #     sleep(1)

        sleep(3)
