import time
import datetime
from unittest import skip

import pytest
from ..base import BaseTestCase


@skip
class TestProductCardOverview(BaseTestCase):
    mall_operation_domain = "MALL_OPERATION_DOMAIN"
    account_login = "xiaoh<PERSON>shuang"

    # 登录跳转
    def test_mall_operation_homepage_init(self):
        self.login(domain="MALL_OPERATION_DOMAIN", account="xiaohuishuang")

    # 元素校验
    def test_mall_operation_homepage_ele(self):
        self.test_mall_operation_homepage_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)

        # 顶部文案
        self.assert_text("快手商城新升级 亿级流量机遇来了！", "//div[contains(text(),'快手商城新升级 亿级流量机遇来了！')]")
        self.assert_text("20亿+商品卡曝光", "//div[contains(text(),'20亿+商品卡曝光')]")
        self.assert_text("年同比增长50%+", "//div[contains(text(),'年同比增长50%+')]")
        self.assert_text("不直播/不发短视频也能卖货", "//div[contains(text(),'不直播/不发短视频也能卖货')]")

        # tab文案
        self.assert_text("我的商城经营数据", "//div[contains(text(),'我的商城经营数据')]")
        assert self.find_elements("//div[contains(text(),'营销托管')]")[0].text == '营销托管'
        assert self.find_elements("//div[contains(text(),'商城活动')]")[0].text == '商城活动'
        assert self.find_elements("//div[contains(text(),'超级链接竞价')]")[1].text == '超级链接竞价'
        assert self.find_elements("//div[contains(text(),'商品托管')]")[0].text == '商品托管'
        self.assert_text("热门营销工具", "//div[contains(text(),'热门营销工具')]")

        # 商城经营数据
        self.assert_text("商城经营数据", "//div[contains(text(),'商城经营数据')]")
        self.assert_text("商城整体成交金额", "//span[contains(text(),'商城整体成交金额')]")
        self.assert_text("商城推荐成交金额", "//span[contains(text(),'商城推荐成交金额')]")
        self.assert_text("搜索成交金额", "//span[contains(text(),'搜索成交金额')]")
        self.assert_text("店铺成交金额", "//span[contains(text(),'店铺成交金额')]")
        self.assert_text("其他总计成交金额", "//span[contains(text(),'其他总计成交金额')]")

        self.assert_text("实时", "//div[contains(text(),'实时')]")
        self.assert_text("近1日", "//div[contains(text(),'近1日')]")
        self.assert_text("近7日", "//div[contains(text(),'近7日')]")
        self.assert_text("近30日", "//div[contains(text(),'近30日')]")
        self.assert_text("大促", "//div[contains(text(),'大促')]")

        # 营销托管
        assert self.find_elements("//div[contains(text(),'营销托管')]")[1].text == '营销托管'
        self.assert_text("商城托管收益", "//span[contains(text(),'商城托管收益')]")
        self.assert_text("流量扶持", "//div[contains(text(),'流量扶持')]")
        self.assert_text("活动代报", "//div[contains(text(),'活动代报')]")
        self.assert_text("素材优化", "//div[contains(text(),'素材优化')]")
        self.assert_text("爆款链接保护", "//div[contains(text(),'爆款链接保护')]")
        time_text_1 = self.find_elements("//span[contains(text(),'统计时间')]")[0].text
        time_text_2 = self.find_elements("//span[contains(text(),'对比时间')]")[0].text
        assert "统计时间" in time_text_1
        assert "对比时间" in time_text_2
        self.assert_text("更新:预计每日12点左右,频次/天", "//span[contains(text(),'更新:预计每日12点左右,频次/天')]")

        # 商城活动
        assert self.find_elements("//div[contains(text(),'商城活动')]")[1].text == '商城活动'
        self.assert_text("我参与的活动数据", "//span[contains(text(),'我参与的活动数据')]")
        time_text_1 = self.find_elements("//span[contains(text(),'统计时间')]")[1].text
        time_text_2 = self.find_elements("//span[contains(text(),'对比时间')]")[1].text
        assert "统计时间" in time_text_1
        assert "对比时间" in time_text_2
        self.assert_text("近7天累计交易额", "//span[contains(text(),'近7天累计交易额')]")
        self.assert_text("近7天累计订单量", "//span[contains(text(),'近7天累计订单量')]")
        self.assert_text("近7天活动商品总数", "//span[contains(text(),'近7天活动商品总数')]")
        self.assert_text("近7天活动商品销量", "//span[contains(text(),'近7天活动商品销量')]")
        self.assert_text("大牌大补活动", "//div[contains(text(),'大牌大补活动')]")
        self.assert_text("低价特卖活动", "//div[contains(text(),'低价特卖活动')]")
        self.assert_text("限时秒杀活动", "//div[contains(text(),'限时秒杀活动')]")
        self.assert_text("暂无可参与的活动，推荐您关注以下商机", "//div[contains(text(),'暂无可参与的活动')]")

        # 超级链接竞价
        assert self.find_elements("//div[contains(text(),'超级链接竞价')]")[1].text == '超级链接竞价'
        self.assert_text("什么是超级链接竞价", "//div[contains(text(),'什么是超级链接竞价')]")
        self.assert_text("建议竞价的商品", "//span[contains(text(),'建议竞价的商品')]")

        # 商品托管
        assert self.find_elements("//div[contains(text(),'商品托管')]")[1].text == '商品托管'
        self.assert_text("什么是商品托管", "//div[contains(text(),'什么是商品托管')]")

        # 热门营销工具
        self.assert_text("热门营销工具", "//span[contains(text(),'热门营销工具')]")
        self.assert_text("好物秒杀", "//span[contains(text(),'好物秒杀')]")
        self.assert_text("限时促销，爆款提单必备，提升店铺成交金额", "//span[contains(text(),'限时促销')]")
        self.assert_text("沉浸式抢购氛围", "//span[contains(text(),'沉浸式抢购氛围')]")
        self.assert_text("商品详情页专属", "//span[contains(text(),'商品详情页专属')]")
        self.assert_text("商品销量快速破零", "//span[contains(text(),'商品销量快速破零')]")

    # 我的商城经营数据-数据统计时间段验证-实时
    def test_mall_operation_data_stat_time_real_time(self):
        self.test_mall_operation_homepage_init()
        self.wait_for_ready_state_complete()

        self.click("//div[contains(text(),'实时')]")
        time.sleep(2)
        real_time_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[1]/span[2]'
        real_time = str(self.get_text(real_time_xpath))

        # 当前日期
        expect_day = str(datetime.date.today())
        print(expect_day)
        assert real_time == expect_day

    # 我的商城经营数据-数据统计时间段验证-近1日（默认）
    def test_mall_operation_data_stat_time_1d(self):
        self.test_mall_operation_homepage_init()
        self.wait_for_ready_state_complete()
        compare_time_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[1]'
        time_1d_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[2]/span[2]'

        compare_time_str = str(self.get_text(compare_time_xpath)).split('-')
        compare_time = compare_time_str[-3] + "-" + compare_time_str[-2] + "-" + compare_time_str[-1]

        time_1d = str(self.get_text(time_1d_xpath))
        # 当前日期前一天
        expect_day = str(datetime.date.today() - datetime.timedelta(days=1))
        expect_compare_day = str(datetime.date.today() - datetime.timedelta(days=2))
        # 判断数据是否更新
        if time_1d == expect_day:
            assert time_1d == expect_day
            assert compare_time == expect_compare_day
        else:
            # 预期统计时间
            expect_day = str(datetime.date.today() - datetime.timedelta(days=2))
            expect_compare_day = str(datetime.date.today() - datetime.timedelta(days=3))
            assert time_1d == expect_day
            assert compare_time == expect_compare_day

    # 我的商城经营数据-数据统计时间段切换近7日
    def test_mall_operation_data_stat_time_7d(self):
        self.test_mall_operation_homepage_init()
        self.wait_for_ready_state_complete()

        self.click("//div[contains(text(),'近7日')]")
        time.sleep(2)

        compare_time_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[1]'
        time_7d_start_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[2]/span[2]'
        time_7d_end_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[2]/span[4]'

        time_7d_start = str(self.get_text(time_7d_start_xpath))
        time_7d_end = str(self.get_text(time_7d_end_xpath))

        compare_time_str = str(self.get_text(compare_time_xpath)).split('-')
        compare_time_start_year = compare_time_str[0].split(' ')[1]
        compare_time_start = compare_time_start_year + "-" + compare_time_str[-5] + "-" + compare_time_str[-4]
        compare_time_end = compare_time_str[-3] + "-" + compare_time_str[-2] + "-" + compare_time_str[-1]
        print(compare_time_start,compare_time_end,time_7d_start,time_7d_end)

        # 当前日期前一天
        expect_day = str(datetime.date.today() - datetime.timedelta(days=1))
        # 判断数据是否更新
        if time_7d_end == expect_day:
            expect_stat_start_date = str(datetime.date.today() - datetime.timedelta(days=7))
            expect_stat_end_date = str(datetime.date.today() - datetime.timedelta(days=1))
            expect_compare_start_date = str(datetime.date.today() - datetime.timedelta(days=14))
            expect_compare_end_date = str(datetime.date.today() - datetime.timedelta(days=8))
            assert expect_stat_start_date == time_7d_start
            assert expect_stat_end_date == time_7d_end
            assert expect_compare_start_date == compare_time_start
            assert expect_compare_end_date == compare_time_end
        else:
            # 预期统计时间
            expect_stat_start_date = str(datetime.date.today() - datetime.timedelta(days=8))
            expect_stat_end_date = str(datetime.date.today() - datetime.timedelta(days=2))
            expect_compare_start_date = str(datetime.date.today() - datetime.timedelta(days=15))
            expect_compare_end_date = str(datetime.date.today() - datetime.timedelta(days=9))
            assert expect_stat_start_date == time_7d_start
            assert expect_stat_end_date == time_7d_end
            assert expect_compare_start_date == compare_time_start
            assert expect_compare_end_date == compare_time_end

    # 我的商城经营数据-数据统计时间段切换近30日
    def test_mall_operation_data_stat_time_30d(self):
        self.test_mall_operation_homepage_init()
        self.wait_for_ready_state_complete()

        self.click("//div[contains(text(),'近30日')]")
        time.sleep(2)

        compare_time_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[1]'
        time_30d_start_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[2]/span[2]'
        time_30d_end_xpath = '//*[@id="mall-operation"]/div[1]/div/div/div/div/div[2]/span[4]'

        time_30d_start = str(self.get_text(time_30d_start_xpath))
        time_30d_end = str(self.get_text(time_30d_end_xpath))

        compare_time_str = str(self.get_text(compare_time_xpath)).split('-')
        compare_time_start_year = compare_time_str[0].split(' ')[1]
        compare_time_start = compare_time_start_year + "-" + compare_time_str[-5] + "-" + compare_time_str[-4]
        compare_time_end = compare_time_str[-3] + "-" + compare_time_str[-2] + "-" + compare_time_str[-1]

        # 当前日期前一天
        expect_day = str(datetime.date.today() - datetime.timedelta(days=1))
        # 判断数据是否更新
        if time_30d_end == expect_day:
            expect_stat_start_date = str(datetime.date.today() - datetime.timedelta(days=30))
            expect_stat_end_date = str(datetime.date.today() - datetime.timedelta(days=1))
            expect_compare_start_date = str(datetime.date.today() - datetime.timedelta(days=60))
            expect_compare_end_date = str(datetime.date.today() - datetime.timedelta(days=31))
            assert expect_stat_start_date == time_30d_start
            assert expect_stat_end_date == time_30d_end
            assert expect_compare_start_date == compare_time_start
            assert expect_compare_end_date == compare_time_end
        else:
            # 预期统计时间
            expect_stat_start_date = str(datetime.date.today() - datetime.timedelta(days=31))
            expect_stat_end_date = str(datetime.date.today() - datetime.timedelta(days=2))
            expect_compare_start_date = str(datetime.date.today() - datetime.timedelta(days=61))
            expect_compare_end_date = str(datetime.date.today() - datetime.timedelta(days=32))
            assert expect_stat_start_date == time_30d_start
            assert expect_stat_end_date == time_30d_end
            assert expect_compare_start_date == compare_time_start
            assert expect_compare_end_date == compare_time_end

    # 我的商城经营数据-查看更多跳链
    def test_mall_operation_data_jump_link(self):
        xpath = "//div[contains(text(),'查看更多')]"
        expected_url = 'https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation'
        self.check_jump_link(xpath, '查看更多', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)

    # 营销托管-链接跳转
    def test_mall_marketing_hosting_jump_link(self):
        xpath = "//div[contains(text(),'查看托管详情')]"
        expected_url = 'https://s.kwaixiaodian.com/zone/market-hosting/homepage'
        self.check_jump_link(xpath, '查看托管详情', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)

    # 商城活动-链接跳转
    def test_more_opportunity_jump_link(self):
        self.test_mall_operation_homepage_init()
        self.wait_for_ready_state_complete()
        time.sleep(3)
        if self.is_element_visible("//div[contains(text(),'暂无可参与的活动, 推荐您关注以下商机')]"):
            xpath = "//a[contains(text(),'更多商机商品')]"
            expected_url = 'https://s.kwaixiaodian.com/zone/business/center/tianhe-chance-goods'
            self.check_jump_link(xpath, '更多商机商品', expected_url, self.test_mall_operation_homepage_init,
                                 need_login=False, has_href=False, need_click=True)

    # 超级链接竞价-链接跳转
    def test_link_bidding_detailed_rules_jump_link(self):
        xpath = "//div[contains(text(),'什么是超级链接竞价')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        self.check_jump_link(xpath, '什么是超级链接竞价', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)

    # 超级链接竞价-链接跳转
    def test_more_bidding_goods_jump_link(self):
        xpath = "//span[contains(text(),'更多竞价商品')]"
        expected_url = "https://s.kwaixiaodian.com/zone/business-invitation/hyperlinkBidding/list"
        self.check_jump_link(xpath, '更多竞价商品', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)

    # 商品托管-链接跳转
    def test_product_hosting_detailed_rules_jump_link(self):
        xpath = "//div[contains(text(),'什么是商品托管')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        self.check_jump_link(xpath, '什么是商品托管', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)

    # 商品托管-链接跳转
    def test_product_hosting_activate_button(self):
        xpath = "//span[contains(text(),'立即开通')]"
        expected_url = "https://s.kwaixiaodian.com/zone/business-activity/productHost/entry"
        self.check_jump_link(xpath, '立即开通', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)

    # 热门营销工具-链接跳转
    def test_mall_marketing_hot_tools_jump_link(self):
        xpath = "//span[contains(text(),'更多营销工具')]"
        expected_url = "https://s.kwaixiaodian.com/zone/marketing/tools/all-tools"
        self.check_jump_link(xpath, '更多营销工具', expected_url, self.test_mall_operation_homepage_init,
                             need_login=True, has_href=False, need_click=True)
