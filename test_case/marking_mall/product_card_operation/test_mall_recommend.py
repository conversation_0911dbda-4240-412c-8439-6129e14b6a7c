import re

from random import randint
from test_case.marking_mall.base import BaseTestCase
from selenium.webdriver.common.by import By
from unittest import skip, skipIf


# @skip
class TestMallRecommend(BaseTestCase):

    def test_enter_recommend(self):
        self.login(domain="MALL_RECOMMEND_DOMAIN", account="bjfeng")

    # tab元素 文字
    def test_check_tab_ele(self):
        self.test_enter_recommend()
        self.assert_text("商城推荐·商品池", "//div[contains(normalize-space(text()), '商城推荐·商品池')]")
        self.assert_text("基本商家门槛", "//span[contains(text(),'基本商家门槛')]")
        self.assert_text("推荐池内商品情况", "//span[contains(text(),'推荐池内商品情况')]")

    # 基本商家门槛
    def test_basic_merchant_threshold(self):
        self.test_enter_recommend()
        self.assert_text("基本商家门槛", "//span[contains(text(),'基本商家门槛')]")
        self.assert_text("正常开店", "//div[contains(text(),'正常开店')]")
        self.assert_text("非售假商家", "//div[contains(text(),'非售假商家')]")
        self.assert_text("店铺商品体验分满足", "//div[contains(text(),'店铺商品体验分满足')]")

    # 指标说明 基本商家门槛
    def test_indicator_description_1(self):
        xpath = "(//span[contains(text(), '指标说明')])[1]"
        self.test_enter_recommend()
        self.is_element_visible(xpath)
        self.assert_text("指标说明", xpath)
        self.click(xpath)
        self.assert_text("指标说明", "//*[@id='rcDialogTitle0']")
        self.is_element_present("//*[@class='ant-modal-content']")

    # 指标说明 推荐池内商品情况
    def test_indicator_description_2(self):
        xpath = "(//span[contains(text(), '指标说明')])[2]"
        self.test_enter_recommend()
        self.is_element_visible(xpath)
        self.assert_text("指标说明", xpath)
        self.click(xpath)
        self.assert_text("指标说明", "//*[@id='rcDialogTitle0']")
        self.is_element_present("//*[@class='ant-modal-content']")

    # 更多数据 跳链
    def test_more_data_jump_link(self):
        xpath = "//span[contains(text(),'更多数据')]"
        expected_url = "https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation"
        expected_text = "更多数据"
        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_recommend,
                             need_login=True, has_href=False, need_click=True)

    # 查看攻略 跳链
    def test_view_guide_jump_link(self):
        xpath = "//div[contains(text(),'查看攻略')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        expected_text = "查看攻略"
        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_recommend,
                             need_login=True, has_href=False, need_click=True)

    # 了解详情 跳链
    def test_learn_more_jump_link(self):
        xpath = "//div[contains(text(), '了解详情')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        expected_text = "了解详情"
        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_recommend,
                             need_login=True, has_href=False, need_click=True)

    # 未进推荐池商品
    def test_not_in_reco_goods_tab(self):
        pagination_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/ul"
        thead_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/div/div/div/table/thead/tr"
        tbody_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/div/div/div/table/tbody/tr"
        self.test_enter_recommend()

        # 点击「当前未进推荐池商品数」
        self.click("//span[contains(text(),'当前未进推荐池')]")
        self.assert_text("当前未进推荐池商品数", "//span[contains(text(),'当前未进推荐池')]")
        goods_list_elements = self.find_elements(tbody_xpath)
        if len(goods_list_elements) == 1:
            print("未进推荐池商品数为0")
            return

        goods_num_str = self.get_text("(//*[@id='not_in_reco_goods']/div/div[2]/div[1]/div/div/div/div/span/span)[1]")
        goods_num = int(goods_num_str.replace(',', ''))
        print(f"当前未进推荐池商品数: {goods_num}")

        # 表头校验
        self.assert_text("商品信息 商品未进池原因 优化方案", thead_xpath)

        # 点击最后一页
        pagination_element = self.find_element(pagination_xpath)
        pagination_li_elements = pagination_element.find_elements(By.TAG_NAME, "li")
        last_page = pagination_li_elements[-3]
        page_nums = int(last_page.text)
        last_page.click()
        self.sleep(2)

        # 获取最后一页商品数
        goods_list_elements = self.find_elements(tbody_xpath)
        last_page_nums = len(goods_list_elements)
        tab_goods_num = (page_nums - 1) * 10 + last_page_nums
        print(f"列表展示商品数: {tab_goods_num}")
        assert goods_num == tab_goods_num, f"页面展示商品数不一致"

    # 已进推荐池商品
    def test_in_reco_goods_tab(self):
        pagination_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/ul"
        thead_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/div/div/div/table/thead/tr"
        goods_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/div/div/div/table/tbody/tr"
        self.test_enter_recommend()

        # 点击「当前已进推荐池商品数」
        self.click("//span[contains(text(),'当前已进推荐池商品数')]")
        self.assert_text("当前已进推荐池商品数", "//span[contains(text(),'当前已进推荐池商品数')]")
        goods_num_str = self.get_text("(//*[@id='not_in_reco_goods']/div/div[2]/div[1]/div/div/div/div/span/span)[2]")
        goods_num = int(goods_num_str.replace(',', ''))
        print(f"当前已进推荐池商品数: {goods_num}")

        # 表头校验
        self.assert_text("商品信息 曝光人数 点击人数 成交人数", thead_xpath)

        # 点击最后一页
        pagination_element = self.find_element(pagination_xpath)
        pagination_li_elements = pagination_element.find_elements(By.TAG_NAME, "li")
        last_page = pagination_li_elements[-3]
        page_nums = int(last_page.text)
        last_page.click()
        self.sleep(2)

        # 获取最后一页商品数
        goods_list_elements = self.find_elements(goods_xpath)
        last_page_nums = len(goods_list_elements)
        tab_goods_num = (page_nums - 1) * 10 + last_page_nums
        print(f"列表展示商品数: {tab_goods_num}")
        assert goods_num == tab_goods_num, f"页面展示商品数不一致"

    # 查询按钮
    def test_search_button(self):
        tbody_xpath = "//*[@id='commodity-operation-good-reco']/div/div[2]/div[3]/div/div/div/div/div/table/tbody/tr"
        self.test_enter_recommend()

        # 点击未进入推荐池tab
        self.click("//span[contains(text(),'当前未进推荐池')]")
        goods_list_elements = self.find_elements(tbody_xpath)
        if len(goods_list_elements) == 1:
            print("未进推荐池商品数为0")
            return

        # 随机选择一个商品id 点击查询按钮
        random_idx = randint(0, len(goods_list_elements) - 1)
        good_text = goods_list_elements[random_idx].text
        match = re.search(r'商品id:\s*(\d+)', good_text)
        good_id = match.group(1)
        self.type("input[placeholder='请输入商品名称/ID']", good_id)
        self.click("//div[contains(text(),'查询')]")
        print(f"选择第{random_idx + 1}个商品 id: {good_id} 进行查询")
        self.sleep(3)

        goods_list_elements = self.find_elements(tbody_xpath)
        assert len(goods_list_elements) == 1, f"查询结果数量不为1"
        search_good_text = goods_list_elements[0].text
        assert search_good_text == good_text, f"查询结果不一致"
        print(f"查询成功: {search_good_text}")

    # 未进池原因筛选项
    def test_not_in_pool_reason_filter(self):
        expected_filter_texts = ["低价高邮", "当前商品价格不合理", "商品无销量", "商品品退率不达标", "商品差评率不达标"]
        self.test_enter_recommend()

        # 点击未进入推荐池tab
        self.click("//span[contains(text(),'当前未进推荐池')]")
        self.assert_text("未进池原因", "//label[text()='未进池原因']")

        # 点击未入池原因
        self.click("//*[@class='ant-select-selection-item']")
        filter_elements = self.find_elements("//*[@class='ant-select-item-option-content']")
        self.click("//*[@class='ant-select-selection-item']")
        filter_texts = [ele.text for ele in filter_elements]
        print(f"未入池原因: {filter_texts}")
        assert filter_texts == expected_filter_texts, f"筛选项文字校验不一致"
