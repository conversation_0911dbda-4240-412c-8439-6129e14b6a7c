import time
import datetime
from unittest import skip

import pytest
from ..base import BaseTestCase


@skip
class TestProductCardMaterial(BaseTestCase):

    # 登录跳转
    def test_mall_operation_homepage_init(self):
        self.login(domain="GOODS_VIDEO_MANAGE_DOMAIN", account="bjfeng")

    # 元素名称校验
    def test_ele(self):
        self.test_mall_operation_homepage_init()
        self.assert_text("有优选视频商品", "//div[text()='有优选视频商品']")
        self.assert_text("无优选视频商品", "//div[text()='无优选视频商品']")
        self.assert_text("商品ID", "//label[text()='商品ID']")
        self.assert_text("商品标题", "//label[text()='商品标题']")
        self.assert_text("重 置", "//span[text()='重 置']")
        self.assert_text("查 询", "//span[text()='查 询']")
        self.assert_text("商品信息", "//div[text()='商品信息']")
        self.assert_text("商城优选视频", "//div[text()='商城优选视频']")
        self.assert_text("累计收益", "//div[text()='累计收益']")
        self.assert_text("操作", "//div[text()='操作']")

    # 数量校验
    def test_count(self):
        self.test_mall_operation_homepage_init()

        xpath_has_chosen = '//*[@id="dilu_micro_root"]/div/div/div/div[1]/div[1]/div[2]'
        xpath_no_chosen = '//*[@id="dilu_micro_root"]/div/div/div/div[1]/div[2]/div[2]'
        xpath_1 = '//*[@id="dilu_micro_root"]/div/div/div/div[2]/div[2]/div/div[1]/div/div/div/div/div[1]/span/span'
        xpath_2 = '//*[@id="dilu_micro_root"]/div/div/div/div[2]/div[2]/div/div[2]/div/div/div/div/ul/li[1]'

        def check_count(xpath):
            count_1 = int(self.find_element(xpath).text[:-1])
            count_2 = int(self.find_element(xpath_1).text[1:-1])
            assert count_1 == count_2

            if count_1 > 0:
                count_3 = int(self.find_element(xpath_2).text[1:-1])
                assert count_1 == count_3

        check_count(xpath_has_chosen)
        self.click("//div[text()='无优选视频商品']")
        self.sleep(2)
        check_count(xpath_no_chosen)
