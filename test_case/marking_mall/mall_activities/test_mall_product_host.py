from datetime import datetime
import time

from selenium.common import TimeoutException, NoSuchElementException

from ..base import BaseTestCase


class TestMallProductHost(BaseTestCase):
    product_host_domain = "PRODUCT_HOST_DOMAIN"
    account_login = "gaohuzhen"

    def test_mall_product_host(self):
        self.maximize_window()
        self.login(self.product_host_domain, self.account_login)
        self.assert_no_404_errors()
        self.refresh()
        time.sleep(2)

    def test_mall_product_host_ele(self):
        self.test_mall_product_host()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        left_img_text_xpath = '//*[@id="root"]/section/section/aside/div/div'
        left_img_text_text = self.get_text(left_img_text_xpath)
        print(left_img_text_text)
        self.assert_in("四大流量池助力托管商品曝光", left_img_text_text)
        self.assert_in("全域赋能商家生意爆发", left_img_text_text)
        self.assert_in("平台托管产品介绍", left_img_text_text)
        right_text_xpath = '//*[@id="root"]/section/section/main'
        right_text_text = self.get_text(right_text_xpath)
        self.assert_in("平台托管计划", right_text_text)
        self.assert_in("佣金托管", right_text_text)
        self.assert_in("商家与平台约定托管佣金率，由平台进行佣金率调配，撮合各渠道进行商品分发。同时，为商品提供流量、补贴等资源支持，助力商品成交。", right_text_text)
        self.assert_in("定价托管", right_text_text)
        self.assert_in("商家提供商品结算价，由平台进行托管运营，撮合各渠道进行商品分发。同时，为商品提供流量、补贴等资源支持，助力商品成交。", right_text_text)
        self.assert_in("立即开通", right_text_text)

    def test_mall_product_host_detail_link(self):
        self.test_mall_product_host()
        self.wait_for_ready_state_complete()
        # self.driver.set_window_size(600, 990)
        time.sleep(2)

        link_xpath ='//*[@id="root"]/section/section/aside/div/div/div[3]/a/text()'
        expect_url = "https://docs.qingque.cn/d/home/<USER>"
        print( self.is_element_visible("平台托管产品介绍", link_xpath))
        if self.is_exact_text_visible("平台托管产品介绍", '//*[@id="root"]/section/section/aside/div/div'):
            try:
                self.wait_for_element_visible(link_xpath)
                if self.is_exact_text_visible('平台托管产品介绍', link_xpath):
                    self.click(link_xpath)
                    time.sleep(2)
                    self.wait_for_ready_state_complete()

                    print(f"页面点击跳转:{self.get_current_url()}")
                    self.assert_url_contains(expect_url)
                    time.sleep(2)
                else:
                    print(f"页面点击跳转:{self.get_current_url()} ---与预期不一致")
                    assert False
            except TimeoutException:
                print("Timeout: Element did not become visible within the specified time.")
                assert False
            except NoSuchElementException:
                print("No such element: The element does not exist in the DOM.")
                assert False
            except Exception as e:
                print("An unexpected error occurred:", str(e))
                assert False
