import random
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_advertisement_page import *
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytAdvertisementUserProfile(BaseTestCase):

    def checkout_advertisement_profile(self):
        """广告-人群画像"""
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(3)
        self.click(HomePage.advertisement_tab)
        self.sleep(3)
        self.click(AdvertisementPage.user_profile)
        self.sleep(3)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(8)


    def random_time_selection(self):
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(AdvertisementPage.time_frame.format(time_frame=date_text))
        self.sleep(3)

    def test_advertisement_user_profile_magnetic_distribution(self):
        """测试用户画像 - 磁力金牛 - 人群分布"""
        self.checkout_advertisement_profile()
        #点击磁力金牛
        self.click(AdvertisementPage.magnetic_advertisement)
        self.sleep(2)
        self.random_time_selection()
        #随机点击 封面曝光 封面点击 素材曝光 等
        top_selection_list = [
            AdvertisementPage.cover_exposure_cnt,
            AdvertisementPage.cover_click_cnt,
            AdvertisementPage.material_exposure_cnt,
            AdvertisementPage.action_cnt,
            AdvertisementPage.new_fans_cnt,
            AdvertisementPage.stream_view_cnt,
            AdvertisementPage.goods_click_cnt
        ]
        selected_btn = random.choice(top_selection_list)
        self.click(selected_btn)
        self.sleep(2)
        self.assert_element(AdvertisementPage.gender_distribution)
        self.assert_element(AdvertisementPage.age_distribution)
        self.assert_element(AdvertisementPage.region_distribution)


    def test_adertisement_user_profile_magnetic_audience(self):
        """测试用户画像 - 磁力金牛 - 受众明细"""
        self.checkout_advertisement_profile()
        # 点击磁力金牛
        self.click(AdvertisementPage.magnetic_advertisement)
        self.sleep(2)
        self.random_time_selection()
        # 随机点击性别 年龄 省份
        top_selection_list = [
            AdvertisementPage.gender_btn,
            AdvertisementPage.age_btn,
            AdvertisementPage.region_btn
        ]
        selected_btn = random.choice(top_selection_list)
        self.click(selected_btn)
        self.sleep(2)
        self.assert_element(AdvertisementPage.audience_detail)

    def test_advertisement_user_profile_fentiao_distribution(self):
        """"测试用户画像 - 快手粉条 - 人群分布"""
        self.checkout_advertisement_profile()
        # 点击磁力金牛
        self.click(AdvertisementPage.fentiao_advertisement)
        self.sleep(2)
        self.random_time_selection()
        # 随机点击 点击，点赞 评论 分享 关注
        top_selection_list = [
           AdvertisementPage.click_cnt,
           AdvertisementPage.like_cnt,
           AdvertisementPage.comment_cnt,
           AdvertisementPage.share_cnt,
           AdvertisementPage.follow_cnt
        ]
        selected_btn = random.choice(top_selection_list)
        self.click(selected_btn)
        self.sleep(2)
        self.assert_element(AdvertisementPage.gender_distribution)
        self.assert_element(AdvertisementPage.age_distribution)
        self.assert_element(AdvertisementPage.region_distribution)



    def test_advertisement_user_profile_fentiao_audience(self):
        """测试用户画像 - 快手粉条 - 受众明细"""
        self.checkout_advertisement_profile()
        # 点击磁力金牛
        self.click(AdvertisementPage.fentiao_advertisement)
        self.sleep(2)
        self.random_time_selection()
        # 随机点击性别 年龄 省份
        top_selection_list = [
            AdvertisementPage.gender_btn,
            AdvertisementPage.age_btn,
            AdvertisementPage.region_btn,
            AdvertisementPage.system_btn,
            AdvertisementPage.city_btn
        ]
        selected_btn = random.choice(top_selection_list)
        self.click(selected_btn)
        self.sleep(2)
        self.assert_element(AdvertisementPage.audience_detail)