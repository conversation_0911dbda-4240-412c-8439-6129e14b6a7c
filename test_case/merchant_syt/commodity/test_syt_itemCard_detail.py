# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/12 4:34 PM
@Auth ： zhanle
@File ：test_syt_itemCard_detail.py
@IDE ：PyCharm
"""
import random
from unittest import skip

from ddt import ddt

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import time
from datetime import datetime, timedelta
import re

account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytShelf(BaseTestCase):

    # 商品卡
    def checkout_cardChannel_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        self.sleep(3)
        # self.refresh()
        # self.sleep(3)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)

        self.click("(//span[contains(text(),'商品卡')])[1]")
        self.sleep(2)
        self.click("//span[@class='linkMenu___DEWk7'][contains(text(),'流量来源明细')]")
        self.sleep(2)

    def checkout_cardChannel_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.open("https://syt.kwaixiaodian.com/zones/goods/cardChannel")
        self.sleep(5)


    # 查看商品卡说明
    @pytest.mark.p0
    def test_cardChannel_jump(self):
        self.checkout_cardChannel_module_old()
        self.click("//span[contains(text(),'查看商品卡说明')]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("(//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'【生意通·商家版】-商品卡流量渠道来源升级！')])[1]")


    # 今日tab切换
    @pytest.mark.p1
    def test_timedata_todayday_tab_change_cardChannel(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.checkout_cardChannel_module()
        time.sleep(1)
        # 点击今日的tab
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_yes = self.find_element('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[1]/span[2]/span').text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")


        # 获取今天的日期
        today = datetime.now().date()

        # 获取今天的日期
        assert today == element_datetime.date()

    #商城推荐
    #核心数据
    def test_cardChannel_shelf_overview(self):
        self.checkout_cardChannel_module()
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    #核心数据-趋势图校验
    def test_cardChannel_shelf_trend(self):
        self.checkout_cardChannel_module()
        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]/div/div/div')

    #流量明细-指标
    @pytest.mark.p0
    def test_cardChannel_shelf_goods_detail(self):
        self.checkout_cardChannel_module()
        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)


    @pytest.mark.p0
    def test_cardChannel_shelf_list_amt_desc(self):
        self.checkout_cardChannel_module()
        # 成交金额降序
        self.click(
            "(//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[1]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = "0"
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_shelf(self):
        self.checkout_cardChannel_module()
        title1 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
                "//li[@title='下一页']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    #列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_2_shelf(self):
        self.checkout_cardChannel_module()
        title1 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
                "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    #流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_shelf(self):
        self.checkout_cardChannel_module()
        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置","//div[@class='group-filter-modal-header-title']")

   #搜索
    #核心数据
    def test_cardChannel_search_overview(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    #核心数据-趋势图校验
    def test_cardChannel_search_trend(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]/div/div/div')

    #流量明细-指标
    @pytest.mark.p0
    def test_cardChannel_search_goods_detail(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)


    @pytest.mark.p0
    def test_cardChannel_search_list_amt_desc(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        # 成交金额降序
        self.click(
            "(//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[1]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[5]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_search(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
                "//li[@title='下一页']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:20]
        substr2 = title2[:20]
        assert substr1 != substr2

    #列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_2_search(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
                "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:20]
        substr2 = title2[:20]
        assert substr1 != substr2

    #流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_search(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-搜索']")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置","//div[@class='group-filter-modal-header-title']")

    # 店铺页
    # 核心数据
    def test_cardChannel_shop_overview(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    # 核心数据-趋势图校验
    def test_cardChannel_shop_trend(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]/div/div/div')

    # 流量明细-指标
    @pytest.mark.p0
    def test_cardChannel_shop_goods_detail(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)

    @pytest.mark.p0
    def test_cardChannel_shop_list_amt_desc(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        # 成交金额降序
        self.click(
            "//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_shop(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
            "//li[@title='下一页']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_2_shop(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
            "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_shop(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-店铺页']")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置", "//div[@class='group-filter-modal-header-title']")

    # 频道活动页
    # 核心数据
    def test_cardChannel_pindao_overview(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    # 核心数据-趋势图校验
    def test_cardChannel_pindao_trend(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]/div/div/div')

    # 流量明细-指标
    @pytest.mark.p0
    def test_cardChannel_pindao_goods_detail(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)

    @pytest.mark.p0
    def test_cardChannel_pindao_list_show_desc(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        # 成交金额降序
        self.click(
            "//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡曝光次数')]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_pindao(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
            "//li[@title='下一页']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_2_pindao(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
            "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_pindao(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-频道']")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置", "//div[@class='group-filter-modal-header-title']")

    # 我的足迹
    # 核心数据
    def test_cardChannel_zuji_overview(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-我的足迹']")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    # 核心数据-趋势图校验
    def test_cardChannel_zuji_trend(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-我的足迹']")
        self.sleep(3)

        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]/div/div/div')

    # 流量明细-指标
    @pytest.mark.p0
    def test_cardChannel_zuji_goods_detail(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-我的足迹']")
        self.sleep(3)

        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)

    @pytest.mark.p0
    def test_cardChannel_zuji_list_show_desc(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-我的足迹']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[4]')  #近30天
        self.sleep(3)
        if self.is_element_visible('//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]'):

            # 成交金额降序
            self.click(
                "//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡曝光次数')]")
            self.sleep(1)

            self.sleep(2)
            meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(
                '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 >= mey_number2
        else:
            return

    # 流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_zuji(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-我的足迹']")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置", "//div[@class='group-filter-modal-header-title']")

    # 收藏列表
    # 核心数据
    def test_cardChannel_bookmark_overview(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    # 核心数据-趋势图校验
    def test_cardChannel_bookmark_trend(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)

        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]')

    # 列表-指标
    @pytest.mark.p0
    def test_cardChannel_bookmark_goods_detail(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)

        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)

    @pytest.mark.p0
    def test_cardChannel_bookmark_list_show_desc(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[3]')  #近7天
        self.sleep(3)

        # 成交金额降序
        self.click(
            "//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡曝光次数')]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_bookmark(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[3]') #近7天
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]').text

        self.click(
            "//li[@title='下一页']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_2_bookmark(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[3]')  #近7天
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]').text

        self.click(
            "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_bookmark(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-收藏列表']")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置", "//div[@class='group-filter-modal-header-title']")

    # 其他
    # 核心数据
    def test_cardChannel_other_overview(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)


    # 核心数据-趋势图校验
    def test_cardChannel_other_trend(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)

        self.assert_element('//*[@id="root"]/div/div[3]/div[2]/div/div/div/div/div[2]/div/div/div')

    # 列表-指标
    @pytest.mark.p0
    def test_cardChannel_other_goods_detail(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)

        real_info1 = self.get_text("//thead[@class='kwaishop-data-syt-micro-goods-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)

    @pytest.mark.skip
    @pytest.mark.p0
    def test_cardChannel_other_list_show_desc(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[3]')  #近7天
        self.sleep(3)

        # 成交金额降序
        self.click(
            "//span[@class='kwaishop-data-syt-micro-goods-pc-table-column-title-no-align'][contains(text(),'商品卡曝光次数')]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_other(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[3]') #近7天
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
            "//li[@title='下一页']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 列表-翻页
    @pytest.mark.p0
    def test_cardChannel_list_page_reverse_2_other(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div[2]/div/div/div[2]/div[3]')  #近7天
        self.sleep(3)

        title1 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text

        self.click(
            "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div[4]/div/div[2]/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 流量明细-指标配置出现
    @pytest.mark.p0
    def test_cardChannel_configuration_other(self):
        self.checkout_cardChannel_module()
        self.click("//div[@id='rc-tabs-0-tab-其他']")
        self.sleep(3)

        self.click("//span[contains(text(),'指标配置')]")
        self.assert_text("指标配置", "//div[@class='group-filter-modal-header-title']")

