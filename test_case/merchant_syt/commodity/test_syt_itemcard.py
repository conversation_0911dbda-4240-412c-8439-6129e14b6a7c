# -*- coding: utf-8 -*-
"""
@Time ： 2024/5/22 11:13 AM
@Auth ： zhanle
@File ：test_syt_itemcard.py
@IDE ：PyCharm
"""

import random
from unittest import skip

from ddt import ddt

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import time
from datetime import datetime, timedelta
import re

account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytShelf(BaseTestCase):

    # 商品卡
    def checkout_itemCard_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        self.sleep(1)

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)

        self.click("(//span[contains(text(),'商品卡')])[1]")
        self.sleep(3)

    def checkout_itemCard_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.open("https://syt.kwaixiaodian.com/zones/data-goods-card-manage/overview")
        self.sleep(5)

    # 查看商品卡说明
    @pytest.mark.p0
    def test_itemCard_jump(self):
        self.checkout_itemCard_module_old()
        self.click("//span[contains(text(),'查看商品卡说明')]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("(//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'【生意通·商家版】-商品卡流量渠道来源升级！')])[1]")

    # 今日tab切换
    @pytest.mark.p1
    def test_timedata_todayday_tab_change_itemCard(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.checkout_itemCard_module()
        time.sleep(1)
        # 点击今日的tab
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_yes = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div[2]/div/div/div[1]/span[2]/span').text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")


        # 获取今天的日期
        today = datetime.now().date()

        # 获取今天的日期
        assert today == element_datetime.date()

    #核心数据
    def test_itemCard_overview(self):
        self.checkout_itemCard_module()
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡退款金额", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    #核心数据-趋势图校验
    def test_itemCard_trend(self):
        self.checkout_itemCard_module()
        self.assert_element('//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div[2]/div[2]/div/div/div/canvas')

    #核心数据-下载校验
    def test_itemCard_overview_download(self):
        self.checkout_itemCard_module()
        self.click("(//span[contains(text(),'下载数据')])[1]")


    #流量明细-指标
    @pytest.mark.p0
    def test_itemCard_source_detail(self):
        self.checkout_itemCard_module()
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-right']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("流量来源", real_info1)
        self.assert_in("曝光趋势", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("操作", real_info1)

    #流量明细-指标降序
    @pytest.mark.p0
    def test_itemCard_source_rank_amt(self):
        self.checkout_itemCard_module()
        # 成交金额降序
        self.click("(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[1]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//tbody[contains(@class,"kwaishop-tianhe-data-goods-card")]/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.click("//li[@title='下一页']//button[@type='button']")
        self.sleep(1)
        meynumber2 = self.find_element(
            '//tbody[contains(@class,"kwaishop-tianhe-data-goods-card")]/tr[4]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2


    #流量明细-店铺页详情
    @pytest.mark.p0
    def test_itemCard_source_shop_detail(self):
        self.checkout_itemCard_module()
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  #查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.assert_text("达人数据","//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-title']")

    #流量明细-店铺页详情-指标
    @pytest.mark.p0
    def test_itemCard_source_shop_detail_overview(self):
        self.checkout_itemCard_module()
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  # 查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.sleep(3)
            self.assert_text("达人数据", "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-title']")
            real_info1 = self.get_text(
                "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-content']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
            self.assert_in("店铺信息", real_info1)
            self.assert_in("商品卡成交金额", real_info1)
            self.assert_in("商品卡成交订单数", real_info1)
            self.assert_in("商品卡曝光次数", real_info1)
            self.assert_in("商品卡点击次数", real_info1)
            self.assert_in("曝光点击率（次数）", real_info1)
            self.assert_in("点击成交率（次数）", real_info1)
            self.assert_in("曝光成交率（次数）", real_info1)

        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-店铺页详情-指标排序
    @pytest.mark.p0
    def test_itemCard_source_shop_detail_amt(self):
        self.checkout_itemCard_module()
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  # 查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.assert_text("达人数据", "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-title']")
            # 成交金额降序
            self.click("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-content']//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交金额')]")
            self.sleep(1)
            self.click("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-content']//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交金额')]")
            self.sleep(2)
            meynumber1 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[3]/div/div[1]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2

        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-店铺页详情-翻页
    @pytest.mark.p0
    def test_source_shop_detail_page_reverse(self):
        self.checkout_itemCard_module()
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  # 查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.sleep(3)

            title1 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div/div[1]/div').text
            self.sleep(3)
            self.click("(//a[@rel='nofollow'][normalize-space()='2'])[2]")

            self.sleep(3)
            title2 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div/div[1]/div').text
            self.sleep(2)
            substr1 = title1[:10]
            substr2 = title2[:10]
            assert substr1 != substr2

    #流量明细-操作
    @pytest.mark.p0
    def test_source_shop_detail_action(self):
        self.checkout_itemCard_module()
        self.click("(//span[contains(text(),'渠道明细')])[1]")
        self.sleep(5)
        self.assert_element("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab-active']")

    #流量明细-指标配置出现
    @pytest.mark.p0
    def test_source_shop_detail_configuration(self):
        self.checkout_itemCard_module()
        self.click("(//span[contains(text(),'指标配置')])[1]")
        self.assert_text("指标配置","//div[@id='rcDialogTitle0']")


    #流量来源-条数
    def test_source_shop_detail_count(self):
        self.checkout_itemCard_module()
        self.assert_element("//span[@customstyle='[object Object]'][contains(text(),'流量来源')]")
        elements = self.find_elements(
            "//span[contains(text(),'渠道明细')]")
        self.assert_true(len(elements) == 8)
        self.assert_true(all(item.text != "" for item in elements) == True)

    #商品列表-指标
    @pytest.mark.p0
    def test_itemCard_list_detail(self):
        self.checkout_itemCard_module()
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)


    @pytest.mark.p0
    def test_itemCard_list_amt_desc(self):
        self.checkout_itemCard_module()
        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[2]")
        self.sleep(1)
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse(self):
        self.checkout_itemCard_module()
        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
                "//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_2(self):
        self.checkout_itemCard_module()
        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
                "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 今日tab切换
    @pytest.mark.p1
    def test_timedata_todayday_tab_change_self(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.checkout_itemCard_module()

        self.click("//div[contains(text(),'自营商品卡')]")
        time.sleep(3)
        # 点击今日的tab
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_yes = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div[2]/div/div/div[1]/span[2]/span').text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")


        # 获取今天的日期
        today = datetime.now().date()

        # 获取今天的日期
        assert today == element_datetime.date()

    #核心数据
    def test_itemCard_overview_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡退款金额", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    #核心数据-趋势图校验
    def test_itemCard_trend_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)
        self.assert_element('//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div[2]/div[2]/div/div/div/canvas')

    #核心数据-下载校验
    def test_itemCard_overview_download_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'下载数据')])[1]")


    #流量明细-指标
    @pytest.mark.p0
    def test_itemCard_source_detail_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-right']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("流量来源", real_info1)
        self.assert_in("曝光趋势", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("操作", real_info1)

    #流量明细-指标降序
    @pytest.mark.p0
    def test_itemCard_source_rank_amt_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)
        # 成交订单数降序
        self.click("(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[1]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[3]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[3]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[4]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    #流量明细-操作
    @pytest.mark.p0
    def test_source_shop_detail_action_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'渠道明细')])[1]")
        self.sleep(5)
        self.assert_text("商城推荐","(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[1]")

    #流量明细-指标配置出现
    @pytest.mark.p0
    def test_source_shop_detail_configuration_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'指标配置')])[1]")
        self.assert_text("指标配置","//div[@id='rcDialogTitle0']")


    #流量来源-条数
    def test_source_shop_detail_count_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        self.assert_element("//span[@customstyle='[object Object]'][contains(text(),'流量来源')]")
        elements = self.find_elements(
            "//span[contains(text(),'渠道明细')]")
        self.assert_true(len(elements) == 8)
        self.assert_true(all(item.text != "" for item in elements) == True)

    #商品列表-指标
    @pytest.mark.p0
    def test_itemCard_list_detail_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)


    @pytest.mark.p0
    def test_itemCard_list_amt_desc_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        # 成交金额降序
        self.click(
            "(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[2]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
                "//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_2_self(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'自营商品卡')]")
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
                "//li[@title='2']")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    # 今日tab切换
    @pytest.mark.p1
    def test_timedata_todayday_tab_change_cooperation(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.checkout_itemCard_module()

        self.click("//div[contains(text(),'合作商品卡')]")
        time.sleep(3)
        # 点击今日的tab
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_yes = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div[2]/div/div/div[1]/span[2]/span').text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")


        # 获取今天的日期
        today = datetime.now().date()

        # 获取今天的日期
        assert today == element_datetime.date()

    #核心数据
    def test_itemCard_overview_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡退款金额", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)
        self.assert_in("点击成交率（次数）", real_info1)
        self.assert_in("曝光成交率（次数）", real_info1)

    #核心数据-趋势图校验
    def test_itemCard_trend_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.assert_element('//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div[2]/div[2]/div/div/div/canvas')

    #核心数据-下载校验
    def test_itemCard_overview_download_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'下载数据')])[1]")


    #流量明细-指标
    @pytest.mark.p0
    def test_itemCard_source_detail_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-right']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("店铺信息", real_info1)
        self.assert_in("曝光趋势", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("操作", real_info1)

    #流量明细-指标降序
    @pytest.mark.skip
    @pytest.mark.p0
    def test_itemCard_source_rank_amt_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        # 曝光次数降序
        self.click("(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡曝光次数')])[1]")
        self.sleep(1)

        self.sleep(2)
        meynumber1 = self.find_element(
            "//tbody[contains(@class,'kwaishop-tianhe-data-goods-card-manage-pc-table-tbody')]/tr[2]/td[5]/div/div[1]").text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            "//tbody[contains(@class,'kwaishop-tianhe-data-goods-card-manage-pc-table-tbody')]/tr[3]/td[5]/div/div[1]").text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    #流量明细-操作
    @pytest.mark.p0
    def test_source_shop_detail_action_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)
        self.assert_text("商品明细","(//span[contains(text(),'商品明细')])[1]")


    # 流量明细-店铺信息-商品明细
    @pytest.mark.p0
    def test_itemCard_source_shop_detail_overview_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)

        real_info1 = self.get_text(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)


    # 流量明细-商品明细详情-指标排序
    @pytest.mark.p0
    @skip
    def test_itemCard_source_shop_detail_amt_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)

        # 成交金额降序
        self.click(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交金额')]")
        self.sleep(1)
        self.click(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(
                    '//*[@id="root"]/div[1]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
                    '//*[@id="root"]/div[1]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

            # account_proxy_app.account_proxy_remove(**********, *********)

    # 流量明细-店铺页详情-翻页
    @pytest.mark.p0
    def test_source_shop_detail_page_reverse_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)

        if self.is_element_clickable(
                    '//*[@id="dilu_micro_root"]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/ul/li[4]/a'):
            title1 = self.find_element(
                    '//*[@id="dilu_micro_root"]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div').text

            self.click(
                    '//*[@id="dilu_micro_root"]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/ul/li[4]/a')

            self.sleep(2)
            title2 = self.find_element(
                    '//*[@id="dilu_micro_root"]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div').text
            self.sleep(2)
            substr1 = title1[:15]
            substr2 = title2[:15]
            assert substr1 != substr2
        else:
            return

    #流量明细-指标配置出现
    @pytest.mark.p0
    def test_source_shop_detail_configuration_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'指标配置')])[1]")
        self.assert_text("指标配置","//div[@id='rcDialogTitle0']")


    #流量来源-条数
    def test_source_shop_detail_count_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        self.assert_element("//span[@customstyle='[object Object]'][contains(text(),'流量来源')]")
        elements = self.find_elements(
            "//span[contains(text(),'商品明细')]")
        self.assert_true(len(elements) == 10)
        self.assert_true(all(item.text != "" for item in elements) == True)

    #商品列表-指标
    @pytest.mark.p0
    def test_itemCard_list_detail_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)


    @pytest.mark.p0
    def test_itemCard_list_amt_desc_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)

        # 成交金额降序
        self.click(
            "(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[2]")
        self.sleep(1)
        # //*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]
        # //*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text   #第三个品
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text  #第四个品
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_cooperation(self):
        self.checkout_itemCard_module()
        self.assert_text("商品卡整体","//div[contains(text(),'商品卡整体')]")
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click("(//a[@rel='nofollow'][text()='2'])")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_2_cooperation(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
                "(//a[@rel='nofollow'][normalize-space()='2'])[2]")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        assert title1 != title2



    #核心数据
    def test_itemCard_overview_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)


    #核心数据-趋势图校验
    def test_itemCard_trend_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)
        self.assert_element('//*[@id="dilu_micro_root"]/div/div[2]/div[3]/div[2]/div[2]/div/div/div/canvas')

    #核心数据-下载校验
    def test_itemCard_overview_download_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'下载数据')])[1]")


    #流量明细-指标
    @pytest.mark.p0
    def test_itemCard_source_detail_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-right']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("店铺信息", real_info1)
        self.assert_in("曝光趋势", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("操作", real_info1)

    #流量明细-指标降序
    @pytest.mark.p0
    def test_itemCard_source_rank_amt_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)
        # 曝光次数降序
        self.click("(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡曝光次数')])[1]")
        self.sleep(1)

    #//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div/div[1]
        self.sleep(2)
        meynumber1 = self.find_element(
            "//tbody[contains(@class,'kwaishop-tianhe-data-goods-card-manage-pc-table-tbody')]/tr[2]/td[5]/div/div[1]").text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            "//tbody[contains(@class,'kwaishop-tianhe-data-goods-card-manage-pc-table-tbody')]/tr[3]/td[5]/div/div[1]").text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2


    #流量明细-操作
    @pytest.mark.p0
    def test_source_shop_detail_action_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)
        self.assert_text("商品明细","(//span[contains(text(),'商品明细')])[1]")


    # 流量明细-店铺信息-商品明细
    @pytest.mark.p0
    def test_itemCard_source_shop_detail_overview_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)

        real_info1 = self.get_text(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)


    # 流量明细-商品明细详情-指标排序
    @pytest.mark.p0
    @skip
    def test_itemCard_source_shop_detail_amt_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)

        # 成交金额降序
        self.click(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交金额')]")
        self.sleep(1)
        self.click(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(
                    '//*[@id="root"]/div[1]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[2]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
                    '//*[@id="root"]/div[1]/div/div[1]/div/div/div[2]/div/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[2]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

            # account_proxy_app.account_proxy_remove(**********, *********)

    # 流量明细-店铺页详情-翻页
    @pytest.mark.p0
    def test_source_shop_detail_page_reverse_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'商品明细')])[1]")
        self.sleep(5)
        if self.is_element_clickable(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//li[@title='下一页']//button[@type='button']"):
            title1 = self.find_element(
                    '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text

            self.click(
                    "//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-drawer-body']//div//li[@title='下一页']//button[@type='button']")

            self.sleep(2)
            title2 = self.find_element(
                    '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text
            self.sleep(2)
            substr1 = title1[:15]
            substr2 = title2[:15]
            assert substr1 != substr2
        else:
            return

    #流量明细-指标配置出现
    @pytest.mark.p0
    def test_source_shop_detail_configuration_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        self.click("(//span[contains(text(),'指标配置')])[1]")
        self.assert_text("指标配置","//div[@id='rcDialogTitle0']")


    #流量来源-条数
    def test_source_shop_detail_count_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        self.assert_element("//span[@customstyle='[object Object]'][contains(text(),'流量来源')]")
        elements = self.find_elements(
            "//span[contains(text(),'商品明细')]")
        self.assert_true(len(elements) == 10)
        self.assert_true(all(item.text != "" for item in elements) == True)

    #商品列表-指标
    @pytest.mark.p0
    def test_itemCard_list_detail_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-table kwaishop-tianhe-data-goods-card-manage-pc-table-ping-right kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-header kwaishop-tianhe-data-goods-card-manage-pc-table-fixed-column kwaishop-tianhe-data-goods-card-manage-pc-table-scroll-horizontal kwaishop-tianhe-data-goods-card-manage-pc-table-has-fix-left']//thead[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品卡成交金额", real_info1)
        self.assert_in("商品卡成交订单数", real_info1)
        self.assert_in("商品卡曝光次数", real_info1)
        self.assert_in("商品卡点击次数", real_info1)
        self.assert_in("曝光点击率（次数）", real_info1)


    @pytest.mark.p0
    def test_itemCard_list_amt_desc_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-data-goods-card-manage-pc-table-column-title-no-align'][contains(text(),'商品卡成交订单数')])[2]")
        self.sleep(1)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)

        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click(
                "(//a[@rel='nofollow'][normalize-space()='2'])[2]")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        assert title1 != title2

    #列表-翻页
    @pytest.mark.p0
    def test_itemCard_list_page_reverse_2_platform(self):
        self.checkout_itemCard_module()
        self.click("//div[contains(text(),'合作商品卡')]")
        self.sleep(3)
        self.click("//span[contains(text(),'平台售卖 (超链竞价)')]")
        self.sleep(3)
        title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text

        self.click("(//a[@rel='nofollow'][normalize-space()='2'])[2]")

        self.sleep(2)
        title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[2]/div[5]/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[1]/div[1]/span').text
        self.sleep(2)
        assert title1 != title2

