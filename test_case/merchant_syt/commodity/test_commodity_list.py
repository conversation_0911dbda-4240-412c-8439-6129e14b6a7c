import re
import time
from datetime import datetime, timedelta
from unittest import skip

import pytest
from ddt import ddt
from utils.merchantDataUtils import *

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")


@ddt
@pytest.mark.skip
class TestCommodityList(BaseTestCase):

    #展示商品榜单
    def activity_commodity_list_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)


        self.click("//span[contains(text(),'市场')]")

        self.sleep(3)

    #展示商品榜单
    def activity_commodity_list(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/rankingList/productRanking")
        self.sleep(5)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)


    # 商品总榜 - tab切换
    @pytest.mark.p1
    @pytest.mark.skip
    def test_tab_change_commodity_main(self):
        self.activity_commodity_list()
        self.click("//span[contains(text(),'商品总榜')]")
        self.assert_element("//th[@class='kwaishop-tianhe-ranking-management-pc-table-cell kwaishop-tianhe-ranking-management-pc-table-cell-fix-left kwaishop-tianhe-ranking-management-pc-table-cell-fix-left-last']")
        self.assert_element("//tbody/tr[5]/td[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 商品卡商品榜 - tab切换
    @pytest.mark.p1
    def test_tab_change_commodity_card(self):
        self.activity_commodity_list_old()
        self.click("//div[contains(@class,'kwaishop-tianhe-ranking-management-pc-form-item-control-input-content')]//div//div[contains(@class,'')][contains(text(),'商品卡')]")
        time.sleep(1)
        self.assert_element("//th[contains(text(),'所属店铺')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 直播商品榜 - tab切换
    @pytest.mark.p1
    def test_tab_change_commodity_realtime_commodity(self):
        self.activity_commodity_list()
        self.click("//div[contains(@class,'container___KVcrU')]//div[2]//div[1]//div[2]//div[1]//div[1]//div[1]//div[3]")
        time.sleep(1)
        self.assert_element("//th[contains(text(),'关联直播间')]")
        self.assert_element("//tbody/tr[5]/td[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 短视频商品榜tab切换
    @pytest.mark.p1
    def test_tab_change_commodity_showtime_commodity(self):
        self.activity_commodity_list()
        self.click("//div[contains(@class,'kwaishop-tianhe-ranking-management-pc-form-item-control-input-content')]//div//div[contains(@class,'')][contains(text(),'短视频')]")
        time.sleep(1)
        self.assert_element("//th[contains(text(),'关联短视频')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 达人带货商品榜 - tab切换
    @skip("已经无达人带货商品榜")
    @pytest.mark.p1
    def test_tab_change_commodity_promoter_commodity(self):
        self.activity_commodity_list()
        self.click("//div[@id='rc-tabs-0-tab-sytPcProductItemDarenTopRankV2']")
        time.sleep(1)
        self.assert_element("//th[contains(text(),'关联达人')]")
        self.assert_element("//tbody/tr[5]/td[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)




    # 价格范围输入校验正确
    @pytest.mark.p1
    def test_data_screening_price_scope(self):
        self.activity_commodity_list()
        self.input("(//input[@placeholder='请输入'])[1]","10")
        self.input("(//input[@placeholder='请输入'])[2]","100")
        time.sleep(2)
        self.click("//span[contains(text(),'数据筛选')]")
        time.sleep(3)
        text = self.find_element("(//div[contains(text(),'成交价：')])[1]").text
        number = convert_to_float(text)
        assert 10 <= number <= 100
        #account_proxy_app.account_proxy_remove(**********, *********)



    # 是否品牌 - 知名品牌
    @pytest.mark.p1
    def test_data_screening_brand_widespread(self):
        self.activity_commodity_list()
        self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[2]')
        time.sleep(1)
        text1 = self.find_element("//tbody/tr[2]/td[2]/div[1]/div[2]/div[1]/div[1]").text
        self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[3]')
        time.sleep(5)
        text2 = self.find_element("//tbody/tr[2]/td[2]/div[1]/div[2]/div[1]/div[1]").text
        time.sleep(1)

        assert  text1 != text2
        #account_proxy_app.account_proxy_remove(**********, *********)


    # 是否品牌 - 非知名品牌
    @pytest.mark.p1
    def test_data_screening_brand_bad(self):
        self.activity_commodity_list()
        self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[2]')
        time.sleep(1)
        text1 = self.find_element("//tbody/tr[2]/td[2]/div[1]/div[2]/div[1]/div[1]").text
        self.click('//*[@id="pro-tag-form-wrapper"]/div[3]/div[2]/div/div/div/div/div[3]')
        time.sleep(5)
        text2 = self.find_element("//tbody/tr[2]/td[2]/div[1]/div[2]/div[1]/div[1]").text
        time.sleep(1)

        assert text1 != text2
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 排序方式 - 成交金额降序
    @pytest.mark.p1
    def test_data_screening_rank_money_desc(self):
        self.activity_commodity_list()
        # 成交金额降序
        self.click("(//span[contains(text(),'成交金额')])[1]")
        time.sleep(2)
        meynumber1 = self.find_element('//*[@id="root"]/div/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[11]/td[4]/div').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        meynumber2 = self.find_element('//*[@id="root"]/div/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2
        #account_proxy_app.account_proxy_remove(**********, *********)





    # 排序方式 - 点击次数降序
    @pytest.mark.p1
    def test_data_screening_rank_click_desc(self):
        self.activity_commodity_list()
        # 点击次数降序
        self.click("//span[contains(text(),'点击次数')]")
        time.sleep(1)
        clicknumber1 = self.find_element("//tbody/tr[2]/td[5]").text
        match = re.search(r'\d+', clicknumber1)
        if match:
            click_number1 = int(match.group())
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        clicknumber2 = self.find_element(
            "//tbody/tr[2]/td[5]").text
        match = re.search(r'\d+', clicknumber2)
        if match:
            click_number2 = int(match.group())

        assert click_number1 >= click_number2
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 排序方式 - 转化率降序
    @pytest.mark.p1
    def test_data_screening_rank_transfer_desc(self):
        self.activity_commodity_list()
        # 转化率降序
        self.click("//span[contains(text(),'转化率')]")
        time.sleep(2)
        trfnumber1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[3]/td[7]').text
        match2 = re.search(r'\d+', trfnumber1)
        if match2:
            trf_number1 = int(match2.group())
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        trfnumber2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[7]').text
        match = re.search(r'\d+', trfnumber2)
        if match:
            trf_number2 = int(match.group())

        assert trf_number1 >= trf_number2
        #account_proxy_app.account_proxy_remove(**********, *********)



        # 商品信息 - 商品点击弹窗出现
    @pytest.mark.p1
    @pytest.mark.skip
    def test_commodity_message_click_title(self):
        self.activity_commodity_list()
        self.click("//tbody/tr[2]/td[2]/div[1]/div[2]/div[1]/div[1]")
        time.sleep(1)
        self.assert_element('//*[@id="app"]/div[1]/div[1]/div[2]/div/div[1]/div[2]')
        self.assert_element('//*[@id="app"]/div[1]/div[2]/div[2]/div/div')
        #account_proxy_app.account_proxy_remove(**********, *********)



    # 商品信息 - 二维码点击出现
    @pytest.mark.p1
    def test_commodity_message_click_erweima(self):
        self.activity_commodity_list()
        self.click("//tbody/tr[2]/td[2]/div[1]/div[2]/div[1]/div[2]//*[name()='svg']")
        time.sleep(1)
        #account_proxy_app.account_proxy_remove(**********, *********)




    # 商品信息 - 近30天成交趋势点击出现弹窗
    @pytest.mark.p1
    def test_commodity_message_click_trend(self):
        self.activity_commodity_list()
        title1 = self.find_element('//*[@id="root"]/div[1]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span').text
        self.click("(//canvas)[1]")
        time.sleep(1)
        self.assert_element("//div[@class='kwaishop-tianhe-ranking-management-pc-modal-content']")
        time.sleep(1)
        title2 = self.find_element("//div[@class='UjNeI4UG5pm_QiXrOSzr']").text
        time.sleep(1)

        substr1 = title1[:5]
        substr2 = title2[:5]
        assert substr1 == substr2
        #account_proxy_app.account_proxy_remove(**********, *********)



    # 商品信息 - 切页功能正常
    @pytest.mark.p1
    def test_commodity_message_click(self):
        self.activity_commodity_list()
        title1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span').text

        self.click("//a[normalize-space()='3']")
        time.sleep(1)
        title2 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span').text
        time.sleep(1)
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(1)
        title3 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[1]/div[1]/span').text
        time.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        substr3 = title3[:15]
        assert substr1 != substr2 and substr3 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)



    # 今日tab切换
    @skip
    @pytest.mark.p1
    def test_timedata_today_tab_change(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.activity_commodity_list()
        time.sleep(1)
        # 点击昨天的tab
        self.click("//div[@id='796facda-d4b4-4398-a96d-5c559760ff94']")
        time_yes = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")

        # 获取今天的日期
        today = datetime.now().date()

        # 获取昨天的日期
        yesterday = today - timedelta(days=1)
        assert yesterday == element_datetime.date()
        self.assert_element("//tbody/tr[2]/td[1]")

        time.sleep(1)
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_today = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        element_datetime2 = datetime.strptime(time_today, "%Y-%m-%d").date()

        time.sleep(1)
        assert element_datetime2 == today
        self.assert_element("//tbody/tr[2]/td[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 昨日tab切换
    @skip
    @pytest.mark.p1
    def test_timedata_yesterday_tab_change(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.activity_commodity_list()
        time.sleep(1)
        # 点击昨天的tab
        self.click('//*[@id="root"]/div/div/div[2]/div[1]/div[2]/div/div/div[2]/div[2]')
        time_yes = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")

        # 获取今天的日期
        today =  datetime.now().date()

        # 获取昨天的日期
        yesterday = today - timedelta(days=1)
        assert yesterday == element_datetime.date()
        self.assert_element("//tbody/tr[2]/td[1]")

        time.sleep(1)
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_today = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        element_datetime2 = datetime.strptime(time_today, "%Y-%m-%d").date()

        time.sleep(1)
        assert element_datetime2 == today
        self.assert_element("//tbody/tr[2]/td[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)







        # 收起全部筛选功能正常
    @pytest.mark.p1
    def test_rollback_all_selector(self):
        self.activity_commodity_list()
        self.assert_element("//span[contains(text(),'是否品牌')]")
        self.click("//span[@class='IcRUXEy9rbSQtu6Rxaaf']")
        time.sleep(1)
        self.assert_element("//div[@class='kwaishop-tianhe-ranking-management-pc-table-content']//table")
        #account_proxy_app.account_proxy_remove(**********, *********)



    # 行业类目选择
    @pytest.mark.p1
    def test_category(self):
        self.activity_commodity_list()
        self.assert_element("//span[@title='家居生活 / 居家清洁 / 全部']")
        self.click("//span[@title='家居生活 / 居家清洁 / 全部']")
        self.assert_element("//li[contains(@title,'家居生活')]")
        time.sleep(5)
        #self.assert_element("//li[contains(@title,'美妆跨境行业')]")
        #account_proxy_app.account_proxy_remove(**********, *********)



