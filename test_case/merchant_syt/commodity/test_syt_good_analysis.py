# -*- coding: utf-8 -*-
"""
@Time ： 2023/10/13 4:57 PM
@Auth ： zhanle
@File ：test_syt_good_analysis.py
@IDE ：PyCharm
"""
from random import random
import random

from ddt import ddt
from unittest import skip, skipIf

from test_case.merchant_syt.base import BaseTestCase
from selenium.webdriver.common.keys import Keys
from utils.http_help import BaseHttpRequest
import pytest

account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
@skip
@pytest.mark.p1
class MyTestClass(BaseTestCase):

    def prepare(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        #代理账号
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)

        self.click("//span[contains(text(),'返回旧版')]")
        self.click("//span[@class='syt-main-modal-close-x']")
        self.sleep(1)


    # 生意通首页经营总览
    @pytest.mark.p1
    def test_goods_list(self):
        self.prepare()

        self.click("//span[contains(text(),'商品分析')]")
        self.assert_text("商品分析", "//*[@id='scroll-content']/div[2]")
        self.assert_text("商品列表","//*[@id='scroll-content']/div[3]/div[1]")
        #id = self.find_element('//tbody/tr[2]/td[1]/div[1]/div[2]/div[2]')
        #self.type('//*[@id="scroll-content"]/div[3]/div[2]/span', id)
        #self.click('//*[@id="scroll-content"]/div[3]/div[2]/span/span/span[2]')
        self.click("//span[contains(text(),'自建商品')]")
        self.assert_element("//div[@class='ant-table-content']")
        self.click("//span[contains(text(),'快分销商品')]")
        self.assert_element("//div[@class='ant-table-content']")
        self.click("//span[contains(text(),'三方商品')]")
        self.assert_element("//div[@class='ant-table-content']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    def test_goods_search(self):
        self.prepare()
        self.click("//span[contains(text(),'商品分析')]")

        self.click("//span[contains(text(),'自定义指标')]")
        self.click("//div[@class='filter-checked-all']") #全选
        self.click("//div[@class='filter-checked-all']") #取消全选
        self.click("//div[@class='ant-checkbox-group']//div[2]//label[1]") #成交金额
        self.click("//span[contains(text(),'确定')]")
        #account_proxy_app.account_proxy_remove(**********, *********)


    # def test_singlegood_analysis_sale(self):
    #     self.prepare()
    #     self.sleep(3)
    #     self.click("//span[contains(text(),'商品分析')]")
    #     #点击详情
    #     self.sleep(3)
    #     self.click("//tbody/tr[2]/td[7]/button[1]/span[1]")
    #
    #     self.assert_element("#module-content")
    #     self.click("(//span[contains(text(),'近30天')])[1]")  #近30天
    #     self.click("//div[@class='ant-select-selector']")  #对比指标
    #     self.click("(//div[contains(@class,'ant-col ant-col-8')])[1]")
    #     self.assert_element("//div[@class='mutiple-y-chart']") #趋势图
    #
    #     self.click("//button[@class='ant-btn auto-filter-btn middle']") #自定义指标
    #     self.click("//div[@class='filter-modle-title']") #全选
    #     self.click("//div[@class='filter-checked-all']") #取消全选
    #     self.click("(//span[contains(text(),'支付金额')])[7]")
    #     self.click("//button[contains(@class,'ant-btn ant-btn-primary')]") #确定
    #     self.assert_element("(//div[contains(@class,'ant-table-wrapper')])[1]")
    #
    #     self.click("//button[@class='ant-btn download-btn-middle ']") #下载
    #     self.assert_downloaded_file("safe.xls")
    #     self.delete_downloaded_file("safe.xls")
    #     account_proxy_app.account_proxy_remove(**********, *********)


    # def test_singlegood_analysis_quality(self):
    #     max_retries = 3
    #     retries = 0
    #     while retries < max_retries:
    #         try:  # 在这里执行测试步骤
    #             self.prepare()
    #             self.maximize_window()
    #             self.click("//span[contains(text(),'商品分析')]")
    #             self.sleep(3)
    #             self.click("//tbody/tr[2]/td[7]/button[1]/span[1]")
    #             self.sleep(6)
    #
    #             # 点击品质分析
    #             self.click("//div[@id='rc-tabs-0-tab-3']")  # 品质分析
    #             self.sleep(3)
    #             self.click("//div[contains(@class,'kpro-data-date-filter-btn-group small')]//div[1]")  # 选择近7天
    #             self.sleep(3)
    #             self.assert_element("(//section[contains(@class,'cv8rNEVr_XgD1DsjWBdg')])[1]")
    #
    #             self.click("//span[contains(text(),'自销')]")
    #             self.sleep(3)
    #             self.assert_element("//div[contains(@class,'CUrpMA78vtCksDW1E5p1')]")
    #             account_proxy_app.account_proxy_remove(**********, *********)
    #             break  # 如果测试通过，跳出循环
    #         except BaseException as e:
    #             print(f"Assertion error occurred: {e}")
    #             retries += 1
    #             if retries == max_retries:
    #                 raise  # 如果达到最大重试次数，抛出异常
    #             else:
    #                 print("Retrying...")
    #                 self.click("//span[@class='syt-main-avatar syt-main-avatar-circle syt-main-avatar-image user-header']//img")
    #                 self.click("/html/body/div[1]/div[1]/div/section/header/div/div[3]/div/div/div/div/ul/div[2]")


    @pytest.mark.p1
    @pytest.mark.skip
    def test_singlegood_analysis_user(self):
        self.prepare()
        self.sleep(3)
        self.click("//span[contains(text(),'商品分析')]")
        self.sleep(3)
        self.click("//tbody/tr[2]/td[7]/button[1]/span[1]")
        self.sleep(3)
        #点击用户画像
        self.click("//div[@class='ant-tabs-tab ant-tabs-tab-active']")
        self.sleep(3)
        self.click("//span[contains(text(),'自建商品被分销')]")
        account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    def test_goodOverview(self):
        self.prepare()

        self.click("//span[contains(text(),'商品总览')]")
        self.assert_element("//div[@class='module-left module-title']")
        self.assert_no_404_errors()
        self.click("(//span[contains(text(),'自建商品')])[1]")
        self.assert_element("//div[@class='irF62wTNGJ7doizg1UpP']")
        self.click("//span[contains(text(),'快分销商品')]")
        self.assert_element("//div[@class='irF62wTNGJ7doizg1UpP']")
        self.click("//span[contains(text(),'三方商品')]")
        self.assert_element("//div[@class='irF62wTNGJ7doizg1UpP']")
        self.click("//div[@class='ant-tabs-tab ant-tabs-tab-active']")
        self.assert_element("//div[@class='ant-table-container']")
        self.click("//div[@class='ant-tabs-tab']")
        self.assert_element("//div[@class='ant-table-container']")
        account_proxy_app.account_proxy_remove(**********, *********)


