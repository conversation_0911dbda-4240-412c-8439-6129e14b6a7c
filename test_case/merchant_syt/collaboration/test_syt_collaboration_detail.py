import random
from xml.etree.ElementPath import xpath_tokenizer

from more_itertools.more import first

from utils.merchantDataUtils import *
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_collaboration_page import *
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytCollaborationDetail(BaseTestCase):
    # 合作 - 合作内容明细
    def checkout_collaboration_detail(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(3)
        # 点击交易tab
        self.click(HomePage.collaboration_tab)
        self.sleep(3)
        # 点击合作内容明细
        self.click(CollaborationPage.collaboration_detail)
        self.sleep(3)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(8)

    def random_time_selection(self):
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(CollaborationPage.time_frame.format(time_frame=date_text))
        self.sleep(3)

    def test_collaboration_funnel_overview(self):
        """合作内容明细-流量转化漏斗"""
        self.checkout_collaboration_detail()
        self.random_time_selection()
        #校验是否有流量转化漏斗
        self.assert_element_visible(CollaborationPage.flow_funnel)
        #从关注页，发现页，个人页复选框 随机去掉一个 曝光次数要小于等于原来的
        #获取之前的短视频曝光次数
        pre_short_video_exposure_count = self.get_text(CollaborationPage.short_video_exposure_count)
        #随机点击一个复选框
        idx = random.randint(1, 6)
        self.click(CollaborationPage.checked_box.format(idx = idx))
        self.sleep(2)
        curr_short_video_exposure_count = self.get_text(CollaborationPage.short_video_exposure_count)
        assert convert_to_float(curr_short_video_exposure_count) <= convert_to_float(pre_short_video_exposure_count)

        #校验是否有流量来源占比
        self.assert_element_visible(CollaborationPage.flow_source_proportion)

        #校验是否有合作视频分析
        self.assert_element_visible(CollaborationPage.collaboration_video_analysis)

    def test_collaboration_short_video_list_sortBy_publish_time(self):
        """合作内容明细-短视频列表-发布时间"""
        self.checkout_collaboration_detail()
        self.random_time_selection()
        #校验是否有根据发布时间排序
        #点击发布时间排序
        self.click(CollaborationPage.video_list_publish_time_sorter)
        self.sleep(2)
        #获取列表的总行数
        total_count = extract_total_count(self.get_text(CollaborationPage.video_list_count))
        if total_count >= 2:
            #获取第一行的发布时间
            first_publish_time = self.get_text(CollaborationPage.video_list_publish_time.format(idx = 1))
            #获取第二行的发布时间
            second_publish_time = self.get_text(CollaborationPage.video_list_publish_time.format(idx = 2))
            assert compare_dates(first_publish_time, second_publish_time) == 1


    def test_collaboration_short_video_list_sortBy_transaction_amt(self):
        """合作内容明细-短视频列表-交易金额"""
        self.checkout_collaboration_detail()
        self.random_time_selection()
        #校验是否有根据交易金额排序
        # 点击交易金额排序
        self.click(CollaborationPage.video_list_amt_sorter)
        self.sleep(2)
        # 获取列表的总行数
        total_count = extract_total_count(self.get_text(CollaborationPage.video_list_count))
        if total_count >= 2:
            # 获取第一行的发布时间
            first_amt = self.get_text(CollaborationPage.video_list_amt.format(idx=1))
            # 获取第二行的发布时间
            second_amt = self.get_text(CollaborationPage.video_list_amt.format(idx=2))
            assert first_amt >= second_amt

    def test_collaboration_short_video_list_pagination(self):
        """合作内容明细-短视频列表-分页"""
        self.checkout_collaboration_detail()
        self.random_time_selection()
        #获取列表的总行数
        total_count = extract_total_count(self.get_text(CollaborationPage.video_list_count))
        #如果总行数大于等于10，才能分页
        #随机选择要么发布时间，要么发布金额排序
        selected_btn = random.choice([CollaborationPage.video_list_publish_time_sorter, CollaborationPage.video_list_amt_sorter])
        self.click(selected_btn)
        self.sleep(2)
        if total_count > 10:
            #获取页面的第一个视频的发布时间(证明不是同一个视频即可)
            publish_time_before_pagination = self.get_text(CollaborationPage.video_list_publish_time.format(idx = 1))
            #点击下一页
            self.click(CollaborationPage.page_2)
            self.sleep(2)
            publish_time_after_pagination = self.get_text(CollaborationPage.video_list_publish_time.format(idx = 1))
            #校验是否能正常分页
            assert publish_time_before_pagination != publish_time_after_pagination