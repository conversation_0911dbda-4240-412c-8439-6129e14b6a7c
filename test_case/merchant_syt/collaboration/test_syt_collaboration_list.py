import random
from xml.etree.ElementPath import xpath_tokenizer
from utils.merchantDataUtils import *
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_collaboration_page import *
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytCollaborationList(BaseTestCase):
    # 合作-达人列表
    def checkout_collaboration_list(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(3)
        # 点击交易tab
        self.click(HomePage.collaboration_tab)
        self.sleep(3)
        #点击合作达人列表
        self.click(CollaborationPage.collaboration_list_tab)
        self.sleep(3)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(8)


    def test_collaboration_list_sorting(self):
        """测试达人合作列表的排序和搜索功能"""
        self.checkout_collaboration_list()
        # 随机点击一个tab 全部，直播间，短视频
        tab_list = [CollaborationPage.collaboration_list_all_btn,CollaborationPage.collaboration_list_live_btn,
                    CollaborationPage.collaboration_list_short_video_btn,CollaborationPage.collaboration_list_goods_card_btn,
                    CollaborationPage.collaboration_list_other_btn]
        select_tab = random.choice(tab_list)
        self.click(select_tab)
        self.sleep(2)
        #获取当前的列表的条数
        list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
        if list_count >= 2:
            # 测试排序
            #点击第一次 是升序
            self.click(CollaborationPage.collaboration_list_amt_sorter)
            self.sleep(2)
            # 获取成交金额
            first_row_amount = convert_to_float(
                self.get_text(CollaborationPage.collaboration_list_amt_value.format(idx = 2)))
            second_row_amount = convert_to_float(
                self.get_text(CollaborationPage.collaboration_list_amt_value.format(idx = 3)))
            assert  first_row_amount <= second_row_amount
            #点击第二次 是降序
            self.click(CollaborationPage.collaboration_list_amt_sorter)
            self.sleep(2)
            first_row_amount = convert_to_float(
                self.get_text(CollaborationPage.collaboration_list_amt_value.format(idx=2)))
            second_row_amount = convert_to_float(
                self.get_text(CollaborationPage.collaboration_list_amt_value.format(idx=3)))
            assert first_row_amount >= second_row_amount


    def test_collaboration_list_searching(self):
        """测试达人合作列表的搜索功能"""
        self.checkout_collaboration_list()
        # 随机点击一个tab 全部，直播间，短视频
        tab_list = [CollaborationPage.collaboration_list_all_btn, CollaborationPage.collaboration_list_live_btn,
                    CollaborationPage.collaboration_list_short_video_btn,
                    CollaborationPage.collaboration_list_goods_card_btn,
                    CollaborationPage.collaboration_list_other_btn]
        select_tab = random.choice(tab_list)
        self.click(select_tab)
        self.sleep(2)
        # 获取当前的列表的条数
        list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
        print(list_count)
        if list_count > 0:
            # 测试搜索
            # 获取一个快手ID
            kuaishou_id = self.get_text(CollaborationPage.kuaishou_id)
            # 输入快手ID
            self.type(CollaborationPage.input_btn, kuaishou_id)
            # 点击搜索
            self.click(CollaborationPage.search_action_btn)
            self.sleep(2)
            # 获取搜索后的列表的条数
            search_list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
            assert search_list_count == 1


    def test_collaboration_list_pagination(self):
        """测试达人合作列表的分页功能"""
        self.checkout_collaboration_list()
        # 随机点击一个tab 全部，直播间，短视频
        tab_list = [CollaborationPage.collaboration_list_all_btn, CollaborationPage.collaboration_list_live_btn,
                    CollaborationPage.collaboration_list_short_video_btn,
                    CollaborationPage.collaboration_list_goods_card_btn,
                    CollaborationPage.collaboration_list_other_btn]
        select_tab = random.choice(tab_list)
        self.click(select_tab)
        self.sleep(2)
        # 获取当前的列表的条数
        list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
        if list_count > 10:
            # 获取分页前的title
            title_before_pagination = self.get_text(CollaborationPage.collaboration_list_title.format(idx = 2))
            print("分页前的title:", title_before_pagination)
            # 第二页
            self.click(CollaborationPage.page_2)
            self.sleep(2)
            # 获取分页后的title
            title_after_pagination = self.get_text(CollaborationPage.collaboration_list_title.format(idx = 2))
            print("分页后的title:", title_after_pagination)
            assert title_before_pagination != title_after_pagination



    def test_collaboration_goods_list_sorting(self):
        """测试达人合作商品列表的排序和搜索功能"""
        self.checkout_collaboration_list()
        # 随机点击一个tab 全部，直播间，短视频
        tab_list = [CollaborationPage.collaboration_list_all_btn, CollaborationPage.collaboration_list_live_btn,
                    CollaborationPage.collaboration_list_short_video_btn,
                    CollaborationPage.collaboration_list_goods_card_btn,
                    CollaborationPage.collaboration_list_other_btn]
        select_tab = random.choice(tab_list)
        self.click(select_tab)
        self.sleep(2)
        # 获取当前的列表的条数
        list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
        if list_count > 0:
            select_id = random.randint(1,10 if list_count >= 10 else list_count)
            #点击 随意一个查看商品的按钮
            self.click(CollaborationPage.collaboration_influencer_list.format(idx = select_id))
            self.sleep(2)
            collaboration_goods_list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
            if collaboration_goods_list_count >= 2:
                # 测试排序
                self.click(CollaborationPage.collaboration_list_amt_sorter)
                self.sleep(2)
                self.click(CollaborationPage.collaboration_list_amt_sorter)
                self.sleep(2)
                #两次点击 之后降序
                first_row_amount = convert_to_float(
                    self.get_text(CollaborationPage.collaboration_goods_list_amt.format(idx = 2)))
                second_row_amount = convert_to_float(
                    self.get_text(CollaborationPage.collaboration_goods_list_amt.format(idx = 3)))
                assert first_row_amount >= second_row_amount



    def test_collaboration_goods_list_pagination(self):
        """测试达人合作商品列表的分页功能"""
        self.checkout_collaboration_list()
        # 随机点击一个tab 全部，直播间，短视频
        tab_list = [CollaborationPage.collaboration_list_all_btn, CollaborationPage.collaboration_list_live_btn,
                    CollaborationPage.collaboration_list_short_video_btn,
                    CollaborationPage.collaboration_list_goods_card_btn,
                    CollaborationPage.collaboration_list_other_btn]
        select_tab = random.choice(tab_list)
        self.click(select_tab)
        self.sleep(2)
        # 获取当前的列表的条数
        list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
        if list_count > 0:
            select_id = random.randint(1,10 if list_count >= 10 else list_count)
            #点击 随意一个查看商品的按钮
            self.click(CollaborationPage.collaboration_influencer_list.format(idx = select_id))
            self.sleep(2)
            collaboration_goods_list_count = extract_total_count(self.get_text(CollaborationPage.collaboration_list_count))
            if collaboration_goods_list_count >= 10:
                # 测试分页
                # 获取分页前的title
                title_before_pagination = self.get_text(CollaborationPage.collaboration_goods_list_title.format(idx=2))
                print("分页前的title:", title_before_pagination)
                # 第二页
                self.click(CollaborationPage.page_2)
                self.sleep(2)
                # 获取分页后的title
                title_after_pagination = self.get_text(CollaborationPage.collaboration_goods_list_title.format(idx=2))
                print("分页后的title:", title_after_pagination)
                assert title_before_pagination != title_after_pagination



