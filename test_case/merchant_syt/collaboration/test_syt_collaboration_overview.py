import random
from xml.etree.ElementPath import xpath_tokenizer
from utils.merchantDataUtils import *
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_collaboration_page import *
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytCollaborationOverview(BaseTestCase):
    # 合作 - 合作总览
    def checkout_collaboration_overview(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(3)
        # 点击合作列表
        self.click(HomePage.collaboration_tab)
        self.sleep(3)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(8)


    def test_collaboration_data_overview(self):
        """达人数据总览 - 数据总览"""
        self.checkout_collaboration_overview()
        #数据总览核心数据存在
        category_list = [CollaborationPage.transaction_refund, CollaborationPage.collaboration_short_video]
        selected_cat = random.choice(category_list)
        self.click(selected_cat)

        data_info = self.get_text(CollaborationPage.indicator_all)
        if selected_cat == CollaborationPage.transaction_refund:
            #成交退款类
            self.assert_in('成交金额', data_info)
            self.assert_in('成交订单数', data_info)
            self.assert_in('成交退款金额', data_info)
        else:
            #短视频合作类
            self.assert_in('短视频曝光次数', data_info)
            self.assert_in('完播率', data_info)
            self.assert_in('短视频观看次数', data_info)


    def test_collaboration_data_data_division(self):
        """数据拆分的逻辑校验"""
        self.checkout_collaboration_overview()
        #点击成交退款
        self.click(CollaborationPage.transaction_refund)
        #随机点击一个指标
        indicator_id = random.randint(1, 4)
        self.click(CollaborationPage.indicator_value_btns.format(idx=indicator_id))
        self.sleep(2)
        #获取该指标的值
        indicator_value = self.get_text(CollaborationPage.indicator_value_btns.format(idx=indicator_id))
        print('indicator_value:', indicator_value)
        division_valves_list = [CollaborationPage.data_division_streaming,
                                CollaborationPage.data_division_short_video,
                                CollaborationPage.data_division_goods_card,
                                CollaborationPage.data_division_other]
        sum_of_value = 0
        for division_valves in division_valves_list:
            tmp = self.get_text(division_valves)
            value = convert_to_float(tmp)
            print('each_value:', value)
            sum_of_value += value
        tolerance = 10
        print('sum_of_value:', sum_of_value)
        assert abs(sum_of_value - convert_to_float(indicator_value)) <= tolerance

    def test_collaboration_data_overview_trend(self):
        """达人数据总览 - 数据趋势图"""
        self.checkout_collaboration_overview()
        #点击核心的指标 校验数据趋势图是否存在
        category_list = [CollaborationPage.transaction_refund, CollaborationPage.collaboration_short_video]
        selected_category = random.choice(category_list)
        indicator_id = random.randint(1, 5)
        self.click(CollaborationPage.indicator_value_btns.format(idx=indicator_id))
        self.click(selected_category)
        self.assert_element(CollaborationPage.trend_chart)







