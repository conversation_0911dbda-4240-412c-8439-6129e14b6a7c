from page_objects.merchant_data.syt_homepage import HomePage
from page_objects.merchant_data.syt_goods_page import GoodsPage
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import re
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytGoodsOverview(BaseTestCase):

    def checkout_goods_overview(self):
        # 进入到商品模块
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(5)
        #点击商品模块
        self.click(HomePage.goods_tab)
        self.sleep(2)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(10)


    def check_core_index(self):
        # 核心数据
        # 核心数据
        core_texts = [
            '商品曝光次数', '商品点击人数', '商品点击次数', '点击成交率(人数)',
            '成交金额', '成交订单数', '成交人数', '成交件数', '客单价', '件单价'
        ]
        for text in core_texts:
            self.assert_text(text, GoodsPage.core_index_class.format(text=text))


    def check_filed_table(self):
        self.assert_text('场域分布', GoodsPage.field_distribution_title)
        # 场域分布表头
        table_info = self.get_text(GoodsPage.field_distribution_table)
        self.assert_in('货架', table_info)
        self.assert_in('短视频', table_info)
        self.assert_in('直播间', table_info)

    def test_goods_overview_self(self):
        # 商品总览-自建商品
        self.checkout_goods_overview()
        # 点击自建
        self.click(GoodsPage.self_goods)
        # 点击近1日
        self.click(GoodsPage.time_1_day)
        self.sleep(5)
        self.check_core_index()
        self.check_filed_table()
        # 点击近7日
        self.click(GoodsPage.time_7_days)
        self.sleep(5)
        self.check_core_index()
        self.check_filed_table()
        # 点击近30日
        self.click(GoodsPage.time_30_days)
        self.sleep(5)
        # 校验核心数据是否存在
        self.check_core_index()
        self.check_filed_table()

    def test_goods_overview_others(self):
        # 商品总览-他人商品
        self.checkout_goods_overview()
        # 点击他人
        self.click(GoodsPage.other_goods)
        # 点击近1日
        self.click(GoodsPage.time_1_day)
        self.sleep(5)
        self.check_core_index()
        self.check_filed_table()
        # 点击近7日
        self.click(GoodsPage.time_7_days)
        self.sleep(5)
        self.check_core_index()
        self.check_filed_table()
        # 点击近30日
        self.click(GoodsPage.time_30_days)
        self.sleep(5)
        # 校验核心数据是否存在
        self.check_core_index()
        self.check_filed_table()

    def test_goods_overview_all(self):
        # 商品总览-全部商品
        self.checkout_goods_overview()
        # 默认就是全部
        self.check_core_index()
        self.check_filed_table()
        # 点击近1日
        self.click(GoodsPage.time_1_day)
        self.sleep(5)
        self.check_core_index()
        self.check_filed_table()
        # 点击近7日
        self.click(GoodsPage.time_7_days)
        self.sleep(5)
        self.check_core_index()
        self.check_filed_table()
        # 点击近30日
        self.click(GoodsPage.time_30_days)
        self.sleep(5)
        # 校验核心数据是否存在
        self.check_core_index()
        self.check_filed_table()









