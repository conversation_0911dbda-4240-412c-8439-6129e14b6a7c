import math
import random
from selenium.webdriver.common.keys import Keys
from page_objects.merchant_data.syt_homepage import HomePage
from page_objects.merchant_data.syt_goods_page import GoodsPage
from test_case.merchant_syt.base import BaseTestCase
from utils.merchantDataUtils import *
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytGoodsRanking(BaseTestCase):

    def checkout_goods_ranking(self):
        # 进入到商品模块
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(5)
        #点击商品模块
        self.click(HomePage.goods_tab)
        self.sleep(2)
        # 点击商品排行
        self.click(GoodsPage.goods_ranking)
        self.sleep(2)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(10)

    def random_select_date_frame(self):
        #随机选择日期
        time_frames = ['近1天','近7天','近30天']
        selected_date = random.choice(time_frames)
        selected_date_btn = GoodsPage.time_frame.format(time_frame=selected_date)
        self.click(selected_date_btn)


    def test_goods_ranking_video_playing(self):
        """测试短视频榜单的关联短视频播放"""
        self.checkout_goods_ranking()
        #点击成交载体 - 短视频
        self.click(GoodsPage.carrier_video)
        self.sleep(2)
        #点击一个视频让他去播放
        self.click(GoodsPage.video_play)
        self.sleep(2)
        #让他播放5s
        self.sleep(5)
        ele = self.get_element(GoodsPage.video_play_progress)
        self.driver.execute_script("arguments[0].style.visibility = 'visible'",ele)
        #校验进度不是 00:00
        progress = ele.text
        print(progress)
        assert progress != '00:00'


    def test_carrier_and_scope_switch(self):
        """测试短视频榜单的成交载体和商品范围的切换"""
        self.checkout_goods_ranking()
        self.random_select_date_frame()
        #随机选一个是否品牌
        selected_brand = random.choice([GoodsPage.brand_all, GoodsPage.brand_famous, GoodsPage.brand_unknown])
        self.click(selected_brand)
        self.sleep(2)
        #点击商品卡片
        self.click(GoodsPage.carrier_item_card)
        self.sleep(2)
        #判断列表应该存在
        self.assert_element(GoodsPage.ranking_list)
        #点击直播间
        self.click(GoodsPage.carrier_live)
        self.sleep(2)
        # 判断列表应该存在
        self.assert_element(GoodsPage.ranking_list)
        #关联直播间字段应该存在
        self.assert_element(GoodsPage.relative_live)
        #点击短视频
        self.click(GoodsPage.carrier_video)
        self.sleep(2)
        # 判断列表应该存在
        self.assert_element(GoodsPage.ranking_list)
        #关联短视频字段应该存在
        self.assert_element(GoodsPage.relative_video)


    def test_ranking_list_filtered_by_price(self):
        """测试短视频榜单根据价格的上下限过滤"""
        self.checkout_goods_ranking()
        #self.random_select_date_frame()
        #输入下界
        self.type(GoodsPage.price_scope_lower, 10)
        self.send_keys(GoodsPage.price_scope_lower, Keys.ENTER)
        self.sleep(2)
        #输入上界
        self.type(GoodsPage.price_scope_upper, 100)
        self.send_keys(GoodsPage.price_scope_upper, Keys.ENTER)
        self.sleep(2)

        #获取第一个成交价 要大于下界
        first_row_price = self.get_text(GoodsPage.deal_price.format(idx = 2))
        assert convert_to_float(first_row_price) >= 10
        # 获取当前页的数量
        current_page_num = extract_total_count(self.get_text(GoodsPage.list_pagination_total))
        #点击最后一页，获取的值也还是小于下界
        total_pages = math.ceil(current_page_num / 10)
        self.click(GoodsPage.page_num_btn.format(page_num=total_pages))
        self.sleep(2)
        idx = (current_page_num - 1) % 10 +1
        last_row_price = self.get_text(GoodsPage.deal_price.format(idx=idx+1))
        assert convert_to_float(last_row_price) <= 100


    def test_ranking_list_pagination(self):
        """测试短视频榜单的分页"""
        self.checkout_goods_ranking()
        self.random_select_date_frame()
        #随机点击成交载体
        selected_carrier = random.choice([GoodsPage.carrier_all,GoodsPage.carrier_item_card,
                                          GoodsPage.carrier_live,GoodsPage.carrier_video])
        self.click(selected_carrier)
        self.sleep(2)
        #随机点击品牌
        selected_brand = random.choice([GoodsPage.brand_all, GoodsPage.brand_famous, GoodsPage.brand_unknown])
        self.click(selected_brand)
        self.sleep(2)

        #获取当前页的数量
        current_page_num = extract_total_count(self.get_text(GoodsPage.list_pagination_total))
        if current_page_num > 10:
            #获取点击下一页之前的第一行的title
            pre_product_title = self.get_text(GoodsPage.product_title)
            pre_shop_name = self.get_text(GoodsPage.shop_title)
            combined_title = pre_product_title + pre_shop_name
            #点击下一页
            self.click(GoodsPage.next_page)
            self.sleep(2)
            #获取点击下一页之后的第一行的title
            new_product_title = self.get_text(GoodsPage.product_title)
            new_shop_name = self.get_text(GoodsPage.shop_title)
            new_combined_title = new_product_title + new_shop_name
            #判断点击下一页之后的title和之前的title不一样
            assert combined_title != new_combined_title










