from xml.etree.ElementPath import xpath_tokenizer
from page_objects.merchant_data import *
from page_objects.merchant_data.syt_goods_page import *
from page_objects.merchant_data.syt_homepage import *
from test_case.merchant_syt.base import BaseTestCase
from utils.merchantDataUtils import *
from utils.http_help import BaseHttpRequest
import pytest
import re
import random
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytGoodsList(BaseTestCase):
    def checkout_goods_list(self):
        # 进入到商品模块
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(5)
        # 点击商品模块
        self.click(HomePage.goods_tab)
        self.sleep(2)
        # 点击商品列表
        self.click(GoodsPage.goods_list)
        self.sleep(2)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(10)

    def random_time_selection(self):
        # 随机时间范围 近1日 近7日 近30日
        time_frames = [GoodsPage.time_1_day,GoodsPage.time_7_days,GoodsPage.time_30_days]
        selected = random.choice(time_frames)
        self.click(selected)
        self.sleep(3)

    def test_goods_list(self):
        """场域切换和商品范围切换"""
        self.checkout_goods_list()
        # 随机时间范围
        self.random_time_selection()
        # 随机选择一个场域
        filed_id = random.randint(1, 6)
        self.click(GoodsPage.field.format(idx = filed_id))
        self.sleep(3)
        # 随机选择一个商品范围 达人合作和平台售卖 不能选择他人商品
        if filed_id == 5 or filed_id == 6:
            scope_id = random.randint(1, 2)
        else:
            scope_id = random.randint(1, 3)
        self.click(GoodsPage.scope.format(idx = scope_id))
        self.sleep(3)
        if not self.is_element_visible(GoodsPage.no_data):
            self.assert_element(GoodsPage.product_id)




    def test_goods_list_order(self):
        """商品列表测试字段排序"""
        self.checkout_goods_list()
        #默认的排序是降序 点击之后变成生序,点击近30天 保证有数据
        self.click(GoodsPage.time_30_days)
        self.sleep(2)
        #点击商品曝光次数
        self.click(GoodsPage.sort_exposure_button)
        self.sleep(2)
        #表格的第一条要小于等于表格的第二条
        #曝光次数
        show_times1 = self.get_text(GoodsPage.first_row_exposure)
        print(show_times1)
        show_times2 = self.get_text(GoodsPage.second_row_exposure)
        print(show_times2)
        assert convert_to_float(show_times1) <= convert_to_float(show_times2)
        #点击商品曝光次数 升序
        self.click(GoodsPage.sort_exposure_button)
        self.sleep(2)
        show_times1 = self.get_text(GoodsPage.first_row_exposure)
        print(show_times1)
        show_times2 = self.get_text(GoodsPage.second_row_exposure)
        print(show_times2)
        assert convert_to_float(show_times1) >= convert_to_float(show_times2)





