import random
from xml.etree.ElementPath import xpath_tokenizer
from utils.merchantDataUtils import *
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_transaction_page import *
import pytest
import re
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytTransactionAnalysis(BaseTestCase):
    #交易 - 成交分析
    def checkout_transaction_refund_analysis(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(3)
        # 点击交易tab
        self.click(HomePage.transaction_tab)
        # 点击退款分析
        self.click(TransactionPage.refund_analysis_tab)
        self.sleep(3)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(8)



    def test_transaction_refund_data_trend(self):
        """趋势图"""
        self.checkout_transaction_refund_analysis()
        # #随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日','近7日','近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        # # 点击趋势图的筛选 按钮 ，全部，退货退款，仅退款，发货前，发货后 等等
        btn_id = random.randint(1,5)
        self.click(TransactionPage.data_overview_btn.format(idx=btn_id))
        self.sleep(2)
        self.assert_element(TransactionPage.trend_chart)


    def test_transaction_refund_data_overview(self):
        """测试数据总览"""
        self.checkout_transaction_refund_analysis()
        # #随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日', '近7日', '近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        # # 点击趋势图的筛选 按钮 ，全部，退货退款，仅退款，发货前，发货后 等等
        btn_id = random.randint(1, 5)
        self.click(TransactionPage.data_overview_btn.format(idx=btn_id))
        self.sleep(2)
        data_info = self.get_text(TransactionPage.data_overview)
        self.assert_in('成交退款人数', data_info)
        self.assert_in('成交退款率(金额)', data_info)
        self.assert_in('成交退款率(订单)', data_info)

    def test_transaction_refund_data_logistics(self):
        """测试数据逻辑校验"""
        self.checkout_transaction_refund_analysis()
        total_amt = 0
        for i in range(2, 7):
            amount_text = self.get_text(TransactionPage.transaction_amount_data.format(idx=i))
            if amount_text == '-':
                amount = 0
            else:
                amount = convert_to_float(amount_text)
            total_amt += amount

        total_amt_text = self.get_text(TransactionPage.refund_amount)
        # 获取左侧的总退款金额
        amt = convert_to_float(total_amt_text)

        # 定义容差值
        tolerance = 10
        assert abs(total_amt - amt) <= tolerance


    def test_transaction_refund_filed_composition_sort(self):
        self.checkout_transaction_refund_analysis()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日', '近7日', '近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        #点击成交金额 第一次是生序
        self.click(TransactionPage.transaction_amount_header)
        row_idx = random.randint(2, 5)
        amount_text_1st = self.get_text(TransactionPage.transaction_amount_data.format(idx=row_idx))
        amount_text_2nd = self.get_text(TransactionPage.transaction_amount_data.format(idx=row_idx+1))
        amt_1st = 0 if amount_text_1st == '-' else convert_to_float(amount_text_1st)
        amt_2nd = 0 if amount_text_2nd == '-' else convert_to_float(amount_text_2nd)
        assert amt_1st <= amt_2nd
        #点击成交金额 第二次是倒序
        self.click(TransactionPage.transaction_amount_header)
        amount_text_1st = self.get_text(TransactionPage.transaction_amount_data.format(idx=row_idx))
        amount_text_2nd = self.get_text(TransactionPage.transaction_amount_data.format(idx=row_idx+1))
        amt_1st = 0 if amount_text_1st == '-' else convert_to_float(amount_text_1st)
        amt_2nd = 0 if amount_text_2nd == '-' else convert_to_float(amount_text_2nd)
        assert amt_1st >= amt_2nd





    def test_transaction_refund_filed_composition_view_detail(self):
        self.checkout_transaction_refund_analysis()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日', '近7日', '近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        idx = random.randint(2, 6)
        self.click(TransactionPage.refund_detail_btn.format(idx=idx))
        self.sleep(3)
        #如果可以看到 对应的 1-3 这类的数据 说明列表有数据
        if self.is_element_visible(TransactionPage.refund_detail_list_count):
            row_cnt = extract_total_count(self.get_text(TransactionPage.refund_detail_list_count))
            assert row_cnt > 0
            if row_cnt >= 2:
                # 点击两次是降序
                self.click(TransactionPage.refund_detail_amount_header)
                self.click(TransactionPage.refund_detail_amount_header)
                amount_text_1st = self.get_text(TransactionPage.refund_detail_amount_data.format(idx=2))
                amount_text_2nd = self.get_text(TransactionPage.refund_detail_amount_data.format(idx=3))
                assert convert_to_float(amount_text_1st) >= convert_to_float(amount_text_2nd)


    def test_transaction_refund_account_composition_sort(self):
        self.checkout_transaction_refund_analysis()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日', '近7日', '近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        btns = [TransactionPage.seller_list,TransactionPage.provider_list]
        selected_btn = random.choice(btns)
        self.click(selected_btn)
        self.sleep(3)
        if self.is_element_visible(TransactionPage.account_list_count):
            row_cnt = extract_total_count(self.get_text(TransactionPage.account_list_count))
            #可以测试排序
            if row_cnt >= 2:
                # 点击成交金额 第一次是生序
                self.click(TransactionPage.account_list_amt_header)
                amount_text_1st = self.get_text(TransactionPage.account_list_amt_data.format(idx=2))
                amount_text_2nd = self.get_text(TransactionPage.account_list_amt_data.format(idx=3))
                assert convert_to_float(amount_text_1st) <= convert_to_float(amount_text_2nd)
                # 点击成交金额 第二次是倒序
                self.click(TransactionPage.account_list_amt_header)
                amount_text_1st = self.get_text(TransactionPage.account_list_amt_data.format(idx=2))
                amount_text_2nd = self.get_text(TransactionPage.account_list_amt_data.format(idx=3))
                assert convert_to_float(amount_text_1st) >= convert_to_float(amount_text_2nd)


    def test_transaction_refund_account_list_pagination(self):
        self.checkout_transaction_refund_analysis()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日', '近7日', '近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        #随机点击卖货 or 供货
        btns = [TransactionPage.seller_list,TransactionPage.provider_list]
        selected_btn = random.choice(btns)
        self.click(selected_btn)
        self.sleep(3)
        if self.is_element_visible(TransactionPage.account_list_count):
            row_cnt = extract_total_count(self.get_text(TransactionPage.account_list_count))
            if row_cnt > 10:
                # 获取当前页的快手id
                curr_kuaishou_id = self.get_text(TransactionPage.account_kuaishou_id)
                # 点击下一页
                self.click(TransactionPage.account_list_nxt_page)
                self.sleep(2)
                # 获取下一页的快手id
                next_kuaishou_id = self.get_text(TransactionPage.account_kuaishou_id)
                # 验证快手id不相等
                assert curr_kuaishou_id != next_kuaishou_id



    def test_transaction_refund_account_list_search(self):
        self.checkout_transaction_refund_analysis()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1日', '近7日', '近30日']
        date_text = random.choice(time_frame)
        self.click(TransactionPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        if self.is_element_visible(TransactionPage.account_list_count):
            row_cnt = extract_total_count(self.get_text(TransactionPage.account_list_count))
            assert row_cnt > 0
            # 获取其中一个快手id
            kuaishou_id = self.get_text(TransactionPage.account_kuaishou_id)
            # 根据快手id去搜索
            self.type(TransactionPage.account_search_input, kuaishou_id)
            self.click(TransactionPage.search_btn)
            self.sleep(5)
            # 验证搜索结果 即列表的行数变成1
            row_cnt_after_search = extract_total_count(self.get_text(TransactionPage.account_list_count))
            assert row_cnt_after_search == 1
            # 验证搜索结果的快手id 和 输入的快手id 一致
            kuaishou_id_after_search = self.get_text(TransactionPage.account_kuaishou_id)
            assert kuaishou_id_after_search == kuaishou_id