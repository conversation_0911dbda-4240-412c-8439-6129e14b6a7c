# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/17 7:34 PM
@Auth ： zhan<PERSON>
@File ：test_syt_live_new.py.py
@IDE ：PyCharm
"""

from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
#
#
account_proxy_app = BaseHttpRequest(user_id="B_**********")

@skip
class TestSytLiveNew(BaseTestCase):

    # 直播-切到直播模块
    def checkout_live_module_new_old(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'直播')])[1]")
        self.sleep(3)


    def checkout_live_module_new(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/live_list/live_overview")
        self.sleep(5)

    @pytest.mark.p0
    #直播总览
    def test_live_overView_new(self):
        self.checkout_live_module_new_old()
        self.click("//span[contains(text(),'直播总览')]")
        self.sleep(3)
        real_info1 = self.get_text(
            "//div[@class='kpro-data-tabsdatacard-tabBar']")
        self.assert_in("直播场次", real_info1)
        self.assert_in("直播间成交金额", real_info1)
        self.assert_in("直播间商品成交件数", real_info1)
        self.assert_in("动销商品数", real_info1)
        self.assert_in("次均观看时长", real_info1)
        self.assert_in("曝光点击率", real_info1)
        self.assert_in("点赞率", real_info1)
        self.assert_in("讲解商品数", real_info1)
        self.assert_in("商品讲解率", real_info1)
        self.assert_in("讲解卡片成交金额", real_info1)

        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    #直播总览-切换时间选择器
    def test_live_change_time_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播总览')]")
        self.sleep(3)

        self.sleep(2)
        self.click("//div[@class='btn-item selected']") #近7天
        self.sleep(2)
        self.click("//div[@class='btn-item']")          #近30天
        self.sleep(2)
        self.click("//div[@class='YcPR8nWAUe0mqCf0tc9E']//div[4]")  #日
        #self.click("//td[@title='2023-11-10']//div[@class='ant-picker-cell-inner'][normalize-space()='10']")
        self.sleep(2)
        self.click("//div[@class='kpro-data-date-filter-btn-group small']//div[5]") #周
        #self.click("//tr[@class='ant-picker-week-panel-row']//td[@title='2023-11-09']//div[@class='ant-picker-cell-inner'][normalize-space()='9']")
        self.sleep(2)
        self.click('//*[@id="scroll-content"]/div[2]/div/div[2]/div/div[2]/div[5]')
        #self.click("//td[@title='2023-08']")
        #account_proxy_app.account_proxy_remove(**********, *********)


    @pytest.mark.p1
    #直播列表-指标
    def test_live_list_overView_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        name_list = ["直播开始时间", "直播时长", "直播间成交订单数", "直播间成交金额", "直播间退款金额", "操作"]
        tabs_context2 = self.get_text_content("//th[contains(text(),'直播开始时间')]")
        self.assert_in(tabs_context2,name_list)
        tabs_context3 = self.get_text_content("//th[contains(text(),'直播时长')]")
        self.assert_in(tabs_context3, name_list)
        tabs_context4 = self.get_text_content("//th[contains(text(),'直播间成交订单数')]")
        self.assert_in(tabs_context4, name_list)
        tabs_context5 = self.get_text_content("//th[contains(text(),'直播间成交金额')]")
        self.assert_in(tabs_context5, name_list)
        tabs_context6 = self.get_text_content("//th[contains(text(),'直播间退款金额')]")
        self.assert_in(tabs_context6, name_list)
        tabs_context7 = self.get_text_content("//th[contains(text(),'操作')]")
        self.assert_in(tabs_context7, name_list)

        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-下载
    def test_live_list_download_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        self.click("//span[@aria-label='download']")
        self.assert_downloaded_file('历史直播概览.xls')
        self.delete_downloaded_file('历史直播概览.xls')

        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-场次对比
    def test_live_list_vs_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        self.click("//span[contains(text(),'场次对比')]")
        self.sleep(2)
        self.assert_text("选择直播场次","//span[contains(text(),'选择直播场次')]")
        self.click("//span[contains(text(),'确 认')]")
        self.assert_element("//span[contains(text(),'必须选择两场直播进行对比')]")
        self.click("//button[@aria-label='Close']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-大屏复盘
    def test_live_list_fupan_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'数据复盘')])[3]")
        self.switch_to_window(1)
        self.sleep(5)

        self.assert_element("(//span[contains(text(),'直播间成交金额')])[1]")
        self.assert_element("//div[@id='review_tab_container-tab-coreData']//div[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-实时数据-直播实时作战系统
    def test_live_list_realtime_board_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        if self.is_element_visible("//span[contains(text(),'直播实时作战系统')]"):
            self.click("//span[contains(text(),'直播实时作战系统')]")
            self.switch_to_window(1)
            self.sleep(3)
            if self.is_element_visible("//div[@class='ant-modal-content']"):
                self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
                self.sleep(3)
            self.assert_element("//span[contains(text(),'实时操盘系统')]")
            self.assert_element("//span[contains(text(),'直播间成交金额')]")
        #account_proxy_app.account_proxy_remove(**********, *********)
    #
    # @pytest.mark.p1
    # # 直播列表-实时数据-实时直播数据-选择跳转大屏
    # def test_live_list_realtime_data_1_new(self):
    #     self.checkout_live_module_new()
    #     self.click("//span[contains(text(),'直播列表')]")
    #     self.sleep(2)
    #     if self.is_element_visible("//span[contains(text(),'实时直播数据')]"):
    #         self.click("//span[contains(text(),'实时直播数据')]")
    #         self.click("//span[contains(text(),'前往直播作战系统')]")
    #         self.switch_to_window(2)
    #         self.sleep(3)
    #         if self.is_element_visible("//div[@class='ant-modal-content']"):
    #             self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
    #             self.sleep(3)
    #         self.assert_element("//span[contains(text(),'实时操盘系统')]")
    #         self.assert_element("//span[contains(text(),'直播间成交金额')]")
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 直播列表-实时数据-实时直播数据-关闭
    def test_live_list_realtime_data_2_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        if self.is_element_visible("//span[contains(text(),'实时直播数据')]"):
            self.click("//span[contains(text(),'实时直播数据')]")
            self.click("//span[contains(text(),'关 闭')]")
            self.assert_element("//span[@class='section-left-title'][contains(text(),'数据看板')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # @pytest.mark.p1
    # #直播列表-历史直播概览-详细数据-选择跳转大屏
    # def test_live_list_detail_data_1(self):
    #     self.checkout_live_module_new()
    #     self.click("//span[contains(text(),'直播列表')]")
    #     self.sleep(2)
    #     self.click("(//span[contains(text(),'详细数据')])[1]")
    #     self.click("//span[contains(text(),'前往直播作战系统')]")
    #     self.switch_to_window(1)
    #     self.sleep(3)
    #     if self.is_element_visible("//div[@class='ant-modal-content']"):
    #         self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
    #         self.sleep(3)
    #     self.assert_element("//span[contains(text(),'实时操盘系统')]")
    #     self.assert_element("//span[contains(text(),'直播间成交金额')]")
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    # @pytest.mark.p1
    # #直播列表-历史直播概览-详细数据-关闭
    # def test_live_list_detail_data_1(self):
    #     self.checkout_live_module()
    #     self.click("//span[contains(text(),'直播列表')]")
    #     self.sleep(2)
    #     self.click("(//span[contains(text(),'详细数据')])[1]")
    #     self.click("//span[contains(text(),'关 闭')]")
    #     self.assert_element("//span[@class='section-left-title'][contains(text(),'数据看板')]")
    #     #account_proxy_app.account_proxy_remove(**********, *********)
    #

    #直播列表-翻页
    @pytest.mark.p0
    def test_livelist_page_reverse_1_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        title1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div').text

        self.click("//li[@title='2']")
        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div').text
        self.sleep(2)

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)

    #直播列表-翻页
    @pytest.mark.p0
    def test_livelist_page_reverse_2_new(self):
        self.checkout_live_module_new()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        title1 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div').text

        self.click("//li[@title='下一页']")
        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="root"]/div/div/div[2]/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div').text
        self.sleep(2)
        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)


    @pytest.mark.p1
    def test_jubao_overview(self):
        self.checkout_live_module_new()

        #举报信息
        self.sleep(3)
        self.click("//span[@class='mfMkX6Qd7Q8IzuJwc0zJ'][contains(text(),'举报信息')]")
        self.sleep(3)
        self.assert_text('举报信息','//*[@id="scroll-content"]/div[3]/section[1]')
        #近30日
        self.click('//*[@id="scroll-content"]/div[2]/div/div[2]/div/div[2]/div[2]')
        self.click('//*[@id="scroll-content"]/div[2]/div/div[2]/div/div[2]/div[3]')
        self.assert_element('//*[@id="scroll-content"]/div[2]/div/div[2]/div/div[3]/div/div')

        #查看详情
        self.click('//*[@id="scroll-content"]/div[4]/section[2]/div[2]/div/div/div/button')
        self.switch_to_window(1)
        self.assert_text('关注','//*[@id="root"]/section/div[1]/header/div/div[1]/div[2]')
        self.switch_to_window(0)
        #account_proxy_app.account_proxy_remove(**********, *********)




