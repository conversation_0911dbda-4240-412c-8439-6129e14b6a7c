# -*- coding: utf-8 -*-
"""
@Time ： 2025/1/6 10:35 AM
@Auth ： zhanle
@File ：test_syt_fupan.py.py
@IDE ：PyCharm
"""

from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import re
#
#
account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")
#@pytest.mark.skip
class TestSytLiveNew(BaseTestCase):

    # 直播-切到直播模块
    def checkout_live_fupan_old(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'直播')])[1]")
        self.sleep(5)
        self.click("(//span[contains(text(),'复盘诊断')])")
        self.sleep(5)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.refresh()
        self.sleep(10)


    def checkout_live_fupan_new(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open('https://syt.kwaixiaodian.com/zones/live/live_list')
        self.sleep(6)
        # live_id = self.get_attribute('tr.kwaishop-tianhe-live_list-pc-table-row.kwaishop-tianhe-live_list-pc-table-row-level-0:nth-child(4)',"data-row-key")
        # print(live_id)
        #
        # self.open("https://syt.kwaixiaodian.com/zones/live/review?tab=coreData&liveId={}".format(live_id))
        # self.sleep(5)
        self.click("(//span[contains(text(),'复盘诊断')])")
        self.sleep(5)

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.refresh()
        self.sleep(10)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return



    @pytest.mark.p0
    # 数据复盘总览
    def test_live_fupan_overView(self):
        self.checkout_live_fupan_old()

        real_info1 = self.get_text(
            "(//div[@class='kpro-data-tabsdatacard-new'])[1]")
        self.assert_in("直播间成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("直播期间成交金额", real_info1)

    @pytest.mark.p0
    # 数据复盘-流量-核心数据
    def test_live_fupan_traffic_overView(self):
        self.checkout_live_fupan_new()

        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("直播曝光人数", real_info1)
        self.assert_in("观看-成交率(人数)", real_info1)
        self.assert_in("观看人数", real_info1)
        self.assert_in("次均观看时长(分钟)", real_info1)

    @pytest.mark.p1
    # 数据复盘-流量-转化效率-全站说明
    def test_live_fupan_traffic_jump(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        if self.is_element_visible("//span[contains(text(),'查看全站推广说明')]"):
             self.click("//span[contains(text(),'查看全站推广说明')]")
             self.switch_to_window(1)
             self.sleep(3)
             self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'磁力金牛「直播推广-全站推广·智投版」产品手册（对外版）')]")
        else:
             return

    @pytest.mark.p1
    # 数据复盘-流量-转化效率-流量来源说明
    def test_live_fupan_traffic_jump2(self):
        self.checkout_live_fupan_new()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("//span[contains(text(),'流量来源说明')]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'快手【生意通】')]")



    @pytest.mark.p0
    # 数据复盘-流量-转化效率
    def test_live_fupan_traffic_table(self):
        self.checkout_live_fupan_new()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        real_info1 = self.get_text("//thead[@class='kwaishop-tianhe-live-pc-table-thead']")
        self.assert_in("渠道名称", real_info1)
        self.assert_in("观看次数（占比）", real_info1)
        self.assert_in("成交金额（占比）", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("观看-成交率（次数）", real_info1)
        self.assert_in("单均价", real_info1)
        self.assert_in("操作", real_info1)


    @pytest.mark.p0
    # 数据复盘-流量-转化效率-条数
    def test_live_fupan_traffic_table_count(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.assert_element("//th[contains(text(),'渠道名称')]")
        elements = self.find_elements(
            'tr.kwaishop-tianhe-live-pc-table-row.kwaishop-tianhe-live-pc-table-row-level-0')
        self.assert_true(len(elements) == 9)  #
        self.assert_true(all(item.text != "" for item in elements) == True)

    @pytest.mark.p0
    # 数据复盘-流量-转化效率-直播推荐条数
    def test_live_fupan_traffic_table_count_live(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        self.assert_element("(//span[@class='kwaishop-tianhe-live-pc-tag ojYUVBJrdHIOCstbR7Nh'][contains(text(),'直播推荐')])[1]")
        elements = self.find_elements(
            'tr.kwaishop-tianhe-live-pc-table-row.kwaishop-tianhe-live-pc-table-row-level-1')
        self.assert_true(len(elements) == 5)  #
        self.assert_true(all(item.text != "" for item in elements) == True)

    @pytest.mark.p0
    # 数据复盘-流量-转化效率-操作
    def test_live_fupan_traffic_action(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        self.click("(//span[contains(text(),'查看漏斗')])[1]")
        self.sleep(3)
        self.assert_element("//span[contains(text(),'漏斗分析')]")
        real_info1 = self.get_text("//div[@class='kpro-data-funnel ']")
        self.assert_in("直播曝光次数", real_info1)
        self.assert_in("累计观看次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("创建订单数", real_info1)
        self.assert_in("成交订单数", real_info1)

        self.click("//span[contains(text(),'确 认')]")
        self.assert_element("(//span[contains(text(),'查看漏斗')])[1]")

    @pytest.mark.skip
    @pytest.mark.p0
    # 数据复盘-流量-趋势图-指标数
    def test_live_fupan_traffic_trend(self):
        self.checkout_live_fupan_new()
        elements = self.find_elements(
            'div.kwaishop-tianhe-live-pc-row.kwaishop-tianhe-live-pc-form-item.kwaishop-tianhe-live-pc-pro-form-item.kwaishop-tianhe-live-pc-pro-form-item__normal')
        self.assert_true(len(elements) >=2)  #
        self.assert_true(all(item.text != "" for item in elements) == True)

        elements = self.find_elements(
            'div.kwaishop-tianhe-live-pc-row.kwaishop-tianhe-live-pc-form-item.kwaishop-tianhe-live-pc-pro-form-item.kwaishop-tianhe-live-pc-pro-form-item__light')
        self.assert_true(len(elements) == 2)  #
        self.assert_true(all(item.text != "" for item in elements) == True)

    @pytest.mark.p1
    # 数据复盘-流量-趋势图
    def test_live_fupan_traffic_trend_detail(self):
        self.checkout_live_fupan_new()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.assert_element("//span[contains(text(),'商品讲解')]")
        self.assert_element("//span[contains(text(),'好运来')]")

    @pytest.mark.p0
    # 数据复盘-流量-引流短视频
    def test_live_fupan_traffic_yinliu(self):
        self.checkout_live_fupan_new()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[3]")
        self.assert_in("引流短视频数量", real_info1)
        self.assert_in("直播期间总引流人数", real_info1)
        self.assert_in("直播入口曝光人数", real_info1)
        self.assert_in("引导直播间成交金额（元）", real_info1)

    @pytest.mark.p0
    # 数据复盘-流量-引流短视频-金额降序
    def test_live_fupan_traffic_yinliu_amt_desc(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        # 成交金额降序
        meynumber1 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[3]/div[2]/span[2]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[3]/div[2]/div[2]/div[3]/div[3]/div[2]/span[2]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    @pytest.mark.p0
    # 数据复盘-流量-引流短视频-引流人数降序
    def test_live_fupan_traffic_yinliu_num_desc(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        # 引流人数降序
        self.click("//span[@title='按直播期间总引流人数降序']")
        self.click("//span[@title='按直播期间总引流人数降序']")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[1]/div[2]/div[2]/div[3]/div[3]/div[2]/span[2]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[3]/div[2]/div[2]/div[3]/div[3]/div[2]/span[2]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    @pytest.mark.p0
    # 数据复盘-流量-引流短视频-翻页
    def test_live_fupan_traffic_yinliu_page_reverse(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        title1 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[1]/div[2]/div[2]/div[1]/div/span[1]').text

        self.click("//a[normalize-space()='2']")
        self.sleep(1)
        title2 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[1]/div[2]/div[1]/div/span').text
        self.sleep(1)
        self.click("//li[contains(@title,'下一页')]//button[contains(@type,'button')]")
        self.sleep(1)
        title3 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div[2]/div/div/div/div/div/div[1]/div[2]/div[2]/div[1]/div/span[1]').text
        self.sleep(2)
        substr1 = title1[:20]
        substr2 = title2[:20]
        substr3 = title3[:20]
        assert substr1 != substr2 and substr3 != substr2

    @pytest.mark.p0
    # 数据复盘-流量-引流短视频-翻页-条数
    def test_live_fupan_traffic_yinliu_page_reverse_1(self):
        self.checkout_live_fupan_new()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("//span[contains(@title,'10 条/页')]")
        self.sleep(3)
        self.click("//div[normalize-space()='20']")
        self.sleep(1)
        elements = self.find_elements(
            'div.B8qEuuJb0OowYeJSG3ZO.Xyjbnir4_Mjd0zJ4wNFg.undefined')
        self.assert_true(len(elements) == 20)  #
        self.assert_true(all(item.text != "" for item in elements) == True)


    @pytest.mark.p0
    # 数据复盘-互动营销-互动数据
    def test_live_fupan_market(self):
        self.checkout_live_fupan_new()
        self.click('//*[@id="market"]/div/div/div[1]')  #互动营销
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("平均在线人数", real_info1)
        self.assert_in("最高在线人数", real_info1)
        self.assert_in("次均观看时长（分钟）", real_info1)
        self.assert_in("点赞次数", real_info1)
        self.assert_in("评论次数", real_info1)
        self.assert_in("分享次数", real_info1)
        self.assert_in("新增粉丝人数", real_info1)

        real_info2 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[3]")
        self.assert_in("营销成交金额", real_info2)
        self.assert_in("营销成交订单数", real_info2)

    @pytest.mark.p0
    # 数据复盘-互动营销-工具跳转-购物团
    def test_live_fupan_market_jump_1(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        self.click("//div[@class='dKziERP8pAOmcXN1hZs3 M093oHNpOuFa5qWTZZn3']")  #互动营销
        self.sleep(3)
        self.click("(//span[contains(text(),'立即创建')])[1]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//span[contains(text(),'免费开通')]")

    @pytest.mark.p0
    # 数据复盘-互动营销-工具跳转-秒杀活动
    def test_live_fupan_market_jump_2(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        self.click("//div[@class='dKziERP8pAOmcXN1hZs3 M093oHNpOuFa5qWTZZn3']")  #互动营销
        self.sleep(3)
        self.click("(//span[contains(text(),'立即创建')])[2]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//div[@class='sc-eDvSVe jQOoCR']")

    @pytest.mark.p0
    # 数据复盘-互动营销-工具跳转-店铺券
    def test_live_fupan_market_jump_3(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        self.click("//div[@class='dKziERP8pAOmcXN1hZs3 M093oHNpOuFa5qWTZZn3']")  #互动营销
        self.sleep(3)
        self.click("(//span[contains(text(),'立即创建')])[3]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//h3[contains(text(),'填写优惠信息')]")

    @pytest.mark.p0
    # 数据复盘-互动营销-工具跳转-商品满减券
    def test_live_fupan_market_jump_4(self):
        self.checkout_live_fupan_new()

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        self.click("//div[@class='dKziERP8pAOmcXN1hZs3 M093oHNpOuFa5qWTZZn3']")  #互动营销
        self.sleep(3)
        self.click("(//span[contains(text(),'立即创建')])[4]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//h3[contains(text(),'填写优惠信息')]")

    @pytest.mark.p0
    # 数据复盘-人群
    def test_live_fupan_people_overview(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='people']")  #人群
        self.sleep(3)
        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("粉丝复购率", real_info1)
        self.assert_in("粉丝-成交人数占比", real_info1)
        self.assert_in("粉丝-成交金额占比", real_info1)
        self.assert_in("新增粉丝数", real_info1)

    @pytest.mark.p1
    # 数据复盘-人群-购物团-跳转
    def test_live_fupan_people_jump(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='people']")  # 人群
        self.sleep(3)
        if self.is_element_visible("(//span[contains(text(),'立即创建')])[1]"):
            self.click("(//span[contains(text(),'立即创建')])[1]")
            self.switch_to_window(1)
            self.sleep(3)
            self.assert_element("//span[contains(text(),'免费开通')]")
        else:
            real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[3]")
            self.assert_in("新增购物团成员数", real_info1)
            self.assert_in("购物团成交金额", real_info1)
            self.assert_in("购物团成交订单数", real_info1)
            self.assert_in("购物团成交人数", real_info1)


    @pytest.mark.p1
    # 数据复盘-人群-用户画像
    def test_live_fupan_people_userprofile(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='people']")  # 人群
        self.sleep(3)
        self.assert_element("//span[contains(text(),'看播用户画像')]")
        self.assert_element("//div[contains(text(),'性别分布')]")
        self.assert_element("//div[contains(text(),'粉丝/非粉丝分布')]")

        self.click("//span[contains(text(),'成交用户画像')]")
        self.assert_element("//div[contains(text(),'年龄分布')]")
        self.assert_element("//div[contains(text(),'地域分布')]")

    @pytest.mark.p0
    # 数据复盘-商品
    def test_live_fupan_goods_overview(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("单均价", real_info1)
        self.assert_in("商品曝光-点击率(次数)", real_info1)
        self.assert_in("商品点击-成交率(人数)", real_info1)

    @pytest.mark.p1
    # 数据复盘-商品-趋势图
    def test_live_fupan_goods_trend(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.assert_element("(//canvas)[1]")

    @pytest.mark.p0
    # 数据复盘-商品-商品详情
    def test_live_fupan_goods_detail(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("//div[@id='rc-tabs-0-tab-goodsDetail']//div[1]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        real_info1 = self.get_text("//thead[@class='kwaishop-tianhe-live-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("首次上车时间", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交件数", real_info1)
        self.assert_in("操作", real_info1)

    @pytest.mark.p0
    # 数据复盘-商品-商品详情-查看详情
    def test_live_fupan_goods_action(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("//div[@id='rc-tabs-0-tab-goodsDetail']//div[1]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.assert_element("//span[contains(text(),'商品详情')]")

        #具体指标
        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[3]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交件数", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("退款订单数", real_info1)
        self.assert_in("商品点击次数", real_info1)

    @pytest.mark.p0
    # 数据复盘-商品-商品详情-查看详情-趋势图
    def test_live_fupan_goods_action_trend(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("(//div[@class='kwaishop-tianhe-live-pc-tabs-tab'])[2]")
        self.sleep(3)
        # if self.is_element_visible("//button[contains(text(),'关闭')]"):
        #     self.click("//button[contains(text(),'关闭')]")
        # else:
        #     return
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.assert_element("//span[contains(text(),'商品详情')]")
        self.assert_element("(//canvas)[1]")

        self.click("//span[contains(text(),'确 认')]")
        self.assert_element("(//span[contains(text(),'查看详情')])[1]")


    @pytest.mark.p0
    # 数据复盘-商品-商品详情-查看详情-sku销量
    def test_live_fupan_goods_action_sku(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click('//*[@id="rc-tabs-1-tab-goodsDetail"]/div')
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.assert_element("//span[contains(text(),'商品详情')]")

        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[3]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交件数", real_info1)
        self.assert_in("成交人数", real_info1)

    @pytest.mark.p0
    # 数据复盘-商品-商品详情-查看详情-sku销量-订单数降序
    def test_live_fupan_goods_action_sku_desc(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click('//*[@id="rc-tabs-1-tab-goodsDetail"]/div')
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.assert_element("//span[contains(text(),'商品详情')]")
        #点击sku的成交订单数
        self.click('/html/body/div[3]/div/div[2]/div/div/div[2]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[4]/div/span[1]')
        self.sleep(1)
        self.click(
            '/html/body/div[3]/div/div[2]/div/div/div[2]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead/tr/th[4]/div/span[1]')
        self.sleep(1)

        meynumber1 = self.find_element(
            '/html/body/div[3]/div/div[2]/div/div/div[2]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '/html/body/div[3]/div/div[2]/div/div/div[2]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div').text
        if self.is_element_visible(meynumber2):
          match = re.findall(r'\d+', meynumber2)
          mey_number2 = ""
          for num in match:
            mey_number2 += num
          mey_number2 = int(mey_number2)

          assert mey_number1 > mey_number2

    @pytest.mark.p0
    # 数据复盘-商品-商品详情-翻页
    def test_live_fupan_goods_page_reverse(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品s
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click('//*[@id="rc-tabs-1-tab-goodsDetail"]/div')
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return

        title1 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[2]/div').text

        self.click("//a[normalize-space()='2']")
        self.sleep(1)
        title2 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[2]/div').text
        self.sleep(1)
        self.click("//li[@class='kwaishop-tianhe-live-pc-pagination-next']//button[@type='button']")
        self.sleep(1)
        title3 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[2]/div[2]').text
        self.sleep(2)
        substr1 = title1[:15]
        substr2 = title2[:15]
        substr3 = title3[:15]
        assert substr1 != substr2 and substr3 != substr2

    @pytest.mark.p0
    # 数据复盘-商品-商品详情-指标配置
    def test_live_fupan_goods_detail_configuration(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click('//*[@id="rc-tabs-1-tab-goodsDetail"]/div')
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click("//span[contains(text(),'指标配置')]")

        real_info1 = self.get_text("//div[@class='group-filter-modal-content-left']")
        self.assert_in("基础指标", real_info1)
        self.assert_in("交易指标", real_info1)
        self.assert_in("流量&转化指标", real_info1)

    @pytest.mark.p0
    # 数据复盘-商品-商品详情-订单数降序
    def test_live_fupan_goods_detail_desc(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='item']")  # 商品
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return
        self.click('//*[@id="rc-tabs-1-tab-goodsDetail"]/div')
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        else:
            return


        self.click(
            "(//span[@class='kwaishop-tianhe-live-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-live-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(1)

        meynumber1 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[6]/div').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="syt-core-data-container"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[6]/div').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    @pytest.mark.p0
    # 数据复盘-售后
    def test_live_fupan_aftersale(self):
        self.checkout_live_fupan_new()

        self.click("//div[@id='afterSale']")  # 售后
        self.sleep(3)
        real_info1 = self.get_text("(//div[@class='kpro-data-tabsdatacard-new'])[2]")
        self.assert_in("退款金额", real_info1)
        self.assert_in("退款人数", real_info1)
        self.assert_in("退款订单数", real_info1)

    # def checkout_live_fupan(self):
    #
    #     self.login("SYT_DOMAIN", "supply_account")
    #     self.driver.maximize_window()
    #     account_proxy_app.account_proxy_confirm(**********, *********)
    #     self.open("https://syt.kwaixiaodian.com/zones/live_list/live_overview")
    #     live_id = self.get_attribute('tr.kwaishop-tianhe-live_list-pc-table-row.kwaishop-tianhe-live_list-pc-table-row-level-0:nth-child(4)', "data-row-key")
    #     #print(live_id)
    #     self.open("https://syt.kwaixiaodian.com/zones/live/review?tab=coreData&liveId={}".format(live_id))
    #     self.sleep(5)
    #
    # def test_live_fupan_market_data(self):
    #     self.checkout_live_fupan()









