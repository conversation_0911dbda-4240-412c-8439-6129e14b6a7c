# -*- coding:utf-8 -*-
"""
Python 3.10
author：zhangpinghui03
date：2023年11月28日
"""
from typing import re
from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
#
#
account_proxy_app = BaseHttpRequest(user_id="B_**********")

@skip

class TestSytLive(BaseTestCase):

    # 直播-切到直播模块
    def checkout_live_module_old(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'直播')])[1]")
        self.sleep(6)

    def checkout_live_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.open("https://syt.kwaixiaodian.com/zones/live/live_list")
        self.sleep(6)

    @pytest.mark.p0
    #直播总览
    def test_live_overView(self):
        self.checkout_live_module_old()
        real_info1 = self.get_text(
            "(//div[contains(@data-kael-component-style-scope,'comp_VeHh_sz_NVGYPqMVauRE-')])[1]")
        self.assert_in("直播场次", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("观看人数", real_info1)
        self.assert_in("新增粉丝数", real_info1)
        self.assert_in("粉丝-成交金额占比", real_info1)


    @pytest.mark.p0
    #直播总览-切换时间选择器
    def test_live_change_time(self):
        self.checkout_live_module()

        if self.is_element_visible("//span[contains(text(),'正在直播（1）')]"):
            self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[4]/div[2]/div[1]/div[1]") #实时
            self.sleep(2)
            self.assert_element("//span[contains(text(),'大屏复盘')]")
        else:
            return

        self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[4]/div[2]/div[1]/div[3]")          #近30天
        self.sleep(2)
        self.assert_element("(//span[contains(text(),'大屏复盘')])[1]")
        self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[4]/div[2]/div[1]/div[4]")  #自定义
        self.sleep(2)



    @pytest.mark.p1
    #直播列表-指标
    def test_live_list_overView(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'直播列表')]")
        self.sleep(2)
        name_list = ["直播间详情", "开播时间", "直播时长", "成交金额", "成交订单数"]
        tabs_context1 = self.get_text_content("//span[contains(text(),'直播间详情')]")
        self.assert_in(tabs_context1,name_list)
        tabs_context2 = self.get_text_content("//span[contains(text(),'开播时间')]")
        self.assert_in(tabs_context2,name_list)
        tabs_context3 = self.get_text_content("//span[contains(text(),'直播时长')]")
        self.assert_in(tabs_context3, name_list)
        tabs_context4 = self.get_text_content("(//span[contains(@customstyle,'[object Object]')][contains(text(),'成交金额')])[1]")
        self.assert_in(tabs_context4, name_list)
        tabs_context5 = self.get_text_content("(//span[contains(@customstyle,'[object Object]')][contains(text(),'成交订单数')])[1]")
        self.assert_in(tabs_context5, name_list)

    @pytest.mark.p1
    # 直播列表-下载
    def test_live_list_configuration(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'自定义展示数据')]")
        self.assert_element("(//span[contains(text(),'直播场次')])[2]")
        self.assert_element("//span[contains(text(),'观看-成交率（人数）')]")


    @pytest.mark.p1
    #直播列表-下载
    def test_live_list_jump(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'查看使用说明')]")
        self.switch_to_window(1)
        self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'直播「数据复盘」工具上线，助您全面提升直播效果')]")



    @pytest.mark.p1
    #直播列表-场次对比
    def test_live_list_vs(self):
        self.checkout_live_module()

        self.click("//span[contains(text(),'场次对比')]")
        self.sleep(2)
        self.assert_text("选择直播场次","//span[contains(text(),'选择直播场次')]")
        self.click("//span[contains(text(),'确 认')]")
        self.assert_element("//span[contains(text(),'必须选择两场直播进行对比')]")
        self.click("//button[@aria-label='Close']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-大屏复盘
    def test_live_list_fupan(self):
        self.checkout_live_module()

        self.click("(//span[contains(text(),'大屏复盘')])[3]")
        self.switch_to_window(1)
        self.sleep(3)
        if self.is_element_visible("//div[@class='ant-modal-content']"):
            self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
            self.sleep(3)
        self.assert_element("//span[contains(text(),'实时操盘系统')]")
        self.assert_element("//span[contains(text(),'直播间成交金额')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-实时数据-直播实时作战系统
    def test_live_list_realtime_board(self):
        self.checkout_live_module()

        if self.is_element_visible("//span[contains(text(),'正在直播（1）')]"):
            self.click("//span[contains(text(),'看大屏')]")
            self.switch_to_window(1)
            self.sleep(3)
            if self.is_element_visible("//div[@class='ant-modal-content']"):
                self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
                self.sleep(3)
            self.assert_element("//span[contains(text(),'实时操盘系统')]")
            self.assert_element("//span[contains(text(),'直播间成交金额')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 直播列表-实时数据-实时直播数据
    def test_live_list_realtime_data(self):
        self.checkout_live_module()
        if self.is_element_visible("//span[contains(text(),'正在直播（1）')]"):
            self.click("//span[contains(text(),'看大屏')]")
            self.assert_element("(//span[contains(@class,'fg8BniaTaZSZQ32HcK8z')][contains(text(),'成交金额')])[1]")


    @pytest.mark.p1
    #直播列表-历史直播概览-详细数据-选择跳转大屏
    def test_live_list_detail_data_jump(self):
        self.checkout_live_module()

        self.click("(//span[contains(text(),'详细数据')])[2]")
        self.click("//span[contains(text(),'前往直播作战系统')]")
        self.switch_to_window(2)
        self.sleep(3)
        if self.is_element_visible("//div[@class='ant-modal-content']"):
            self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
            self.sleep(3)
        self.assert_element("//span[contains(text(),'实时操盘系统')]")
        self.assert_element("//span[contains(text(),'直播间成交金额')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #直播列表-历史直播概览-详细数据-关闭
    def test_live_list_detail_data_1(self):
        self.checkout_live_module()

        self.click("(//span[contains(text(),'详细数据')])[2]")
        self.click("//span[contains(text(),'关 闭')]")
        self.assert_element("//span[@class='section-left-title'][contains(text(),'数据看板')]")
        #account_proxy_app.account_proxy_remove(**********, *********)


    #直播列表-翻页
    @pytest.mark.p0
    def test_livelist_page_reverse_1(self):
        self.checkout_live_module()

        text = self.find_element(
            '//*[@id="lits_live_3"]/div[4]/ul/li[1]').text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
            '//*[@id="lits_live_3"]/div[3]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/div[2]/div/span').text

            self.click("//a[normalize-space()='2']")
            self.sleep(2)
            title2 = self.find_element(
            '//*[@id="lits_live_3"]/div[3]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/div[2]/div/span').text
            self.sleep(2)

            substr1 = title1[:18]
            substr2 = title2[:18]
            assert substr1 != substr2
        else:
            return


    #直播列表-翻页
    @pytest.mark.p0
    def test_livelist_page_reverse_2(self):
        self.checkout_live_module()
        text = self.find_element(
            '//*[@id="lits_live_3"]/div[4]/ul/li[1]').text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
                '//*[@id="lits_live_3"]/div[3]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/div[2]/div/span').text

            self.click("//li[@class='kwaishop-tianhe-live-pc-pagination-next']")
            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="lits_live_3"]/div[3]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/div[2]/div/span').text
            self.sleep(2)

            substr1 = title1[:18]
            substr2 = title2[:18]
            assert substr1 != substr2
        else:
            return


    @pytest.mark.p1
    def test_zhenduan_overview(self):
        self.checkout_live_module()

        self.click("(//span[contains(text(),'复盘诊断')])[3]")
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("//div[@id='review_tab_container-tab-coreData']//div[1]")
        self.assert_element("//div[@id='review_tab_container-tab-diagnosis']//div[1]")





