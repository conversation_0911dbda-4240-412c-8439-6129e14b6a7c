# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/5 4:26 PM
@Auth ： zhanle
@File ：test_syt_yushou.py.py
@IDE ：PyCharm
"""
import re
from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
#
#
account_proxy_app = BaseHttpRequest(user_id="B_**********")


class TestSytYuShouV2(BaseTestCase):

    def checkout_yushou_module_old(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********,**********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'商品')])[2]")
        self.sleep(3)


    def checkout_yushou_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********,**********)
        self.open("https://syt.kwaixiaodian.com/zones/goods/presale")
        self.sleep(5)

    def test_yushou_overview(self):
        self.checkout_yushou_module_old()

        if self.is_element_visible("//div[@id='zones_goods_presale']"):
            self.click("//div[@id='zones_goods_presale']")
            self.sleep(5)

            real_info1 = self.get_text("//div[@class='LrYJtZV3Jmt3FJ4Goaaw']//div[@class='kwaishop-data-syt-micro-goods-pc-spin-nested-loading']")
            self.assert_in("预售商品数", real_info1)
            self.assert_in("定金支付买家数", real_info1)
            self.assert_in("定金支付订单数", real_info1)
            self.assert_in("实际总成交金额", real_info1)
        else:
            return

    def test_yushou_list(self):
        self.checkout_yushou_module()
        if self.is_element_visible("//div[@class='kwaishop-data-syt-micro-goods-pc-empty-description']"):
            return
        else:

            meynumber1 = self.find_element('//*[@id="root"]/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[4]').text
        # meynumber1 = '1,23'
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element('//*[@id="root"]/div/div[4]/div[2]/div/div/div/div/div/table/tbody/tr[3]/td[4]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2







