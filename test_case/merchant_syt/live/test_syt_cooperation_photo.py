# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/18 2:23 PM
@Auth ： zhan<PERSON>
@File ：test_syt_cooperation_photo.py.py
@IDE ：PyCharm
"""
from ddt import ddt
import pytest
from unittest import skip
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import re


account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")
class TestSytPhoto(BaseTestCase):


    # 合作短视频
    def checkout_cooperationVideo_module_old(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)

        self.sleep(3)
        self.click("//span[contains(text(),'短视频')]")
        self.sleep(5)
        self.click("//span[contains(text(),'合作短视频')]")
        self.sleep(5)

    def checkout_cooperationVideo_module(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/shortVideoManagement/cooperationVideo")
        self.sleep(5)

    def test_cooperalte_photo_overView(self):
        self.checkout_cooperationVideo_module_old()

        real_info1 = self.get_text(
            "//div[@class='kpro-data-funnel ']")
        self.assert_in("短视频曝光次数", real_info1)
        self.assert_in("短视频有效播放次数", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("商品下单数", real_info1)
        self.assert_in("商品成交数", real_info1)

    def test_cooperalte_photo_cvr(self):
        self.checkout_cooperationVideo_module()
        #//*[@id="1-left-txt"]/text[2]
        self.assert_element("(//*[name()='text'][contains(text(),'短视频曝光次数')])[1]")
        meynumber1 = self.find_element(
            '//*[@id="2-left-txt"]').text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.click("//span[contains(text(),'同城页')]")  # 同城页
        meynumber2 = self.find_element(
            '//*[@id="2-left-txt"]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2


    def test_cooperalte_photo_analysis(self):
        self.checkout_cooperationVideo_module()

        self.click("//span[contains(text(),'更多短视频')]")
        #表现优异短视频 xxx
        self.assert_element('//*[@id="dilu_micro_root"]/div/div[8]/div/div/div[2]/div/div/div[1]/div/div/span')


    def test_cooperalte_photo_analysis_jump(self):
        self.checkout_cooperationVideo_module()

        self.click("(//span[contains(@customstyle,'[object Object]')][contains(text(),'详情')])[1]")
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("//span[contains(text(),'基础信息')]")
        real_info1 = self.get_text(
            "//div[@class='m4GXwY3LoweKaGpfle9i']")
        self.assert_in("曝光次数", real_info1)
        self.assert_in("有效播放次数", real_info1)
        self.assert_in("点赞次数", real_info1)
        self.assert_in("涨粉人数", real_info1)

        #成交数据--订单数和漏斗比
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div[2]/section[2]/div/div/div[1]/div[2]/div/div[3]/div[2]/div[2]').text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.sleep(2)
        meynumber2 = self.find_element('//*[@id="6-left-txt"]/text[2]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 == mey_number2


    def test_cooperalte_photo_analysis_jump(self):
        self.checkout_cooperationVideo_module()

        self.click("(//span[contains(@customstyle,'[object Object]')][contains(text(),'详情')])[1]")
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("//span[contains(text(),'基础信息')]")

        #漏斗少一个选项
        meynumber1 = self.find_element(
            "//div[@class='kpro-data-funnel ']//*[name()='svg']//*[name()='g' and @id='1-left']//*[name()='g' and @id='1-left-txt']//*[name()='text']").text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.click("//span[contains(text(),'关注页')]")
        self.sleep(2)
        meynumber2 = self.find_element(
            "//div[@class='kpro-data-funnel ']//*[name()='svg']//*[name()='g' and @id='1-left']//*[name()='g' and @id='1-left-txt']//*[name()='text']").text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    #详情页-其他优秀短视频跳转
    def test_cooperalte_photo_analysis_jump(self):
        self.checkout_cooperationVideo_module()

        self.click("(//span[contains(@customstyle,'[object Object]')][contains(text(),'详情')])[1]")
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("//span[contains(text(),'基础信息')]")

        self.click('//*[@id="root"]/div/div[2]/section[2]/div/div/div[3]/img[1]')
        self.switch_to_window(2)
        self.sleep(5)
        self.assert_element("//span[contains(text(),'基础信息')]")

    def test_cooperalte_photo_list(self):
        self.checkout_cooperationVideo_module()
        #点击成交金额
        self.click('//*[@id="dilu_micro_root"]/div/div[9]/div[1]/div/label[2]/span[2]')
        self.sleep(5)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[2]/div[4]/span').text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)
        self.sleep(2)
        #//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[2]/li/div/div[2]/div[2]/div[4]/span
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[2]/li/div/div[2]/div[2]/div[4]/span').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    @pytest.mark.p1
    def test_cooperalte_photo_list_reverse(self):
        self.checkout_cooperationVideo_module()
        title1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[1]/div[1]/div[1]/span').text

        self.click("//li[@title='下一页']")
        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[1]/div[1]/div[1]/span').text
        self.sleep(2)

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        # account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_cooperalte_photo_list_revers(self):
        self.checkout_cooperationVideo_module()
        #//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[1]/div[1]/div[1]/span
        title1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[1]/div[1]/div[1]/span').text

        self.click("//a[normalize-space()='3']")
        self.sleep(2)
        title2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[1]/div[1]/div[1]/span').text
        self.sleep(2)

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2


    @pytest.mark.p1
    # 自营-全部-短视频列表-详情
    def test_cooperalte_photo_list_detail(self):
        self.checkout_cooperationVideo_module()
        self.click("(//span[contains(text(),'详情')])[22]")

        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("//span[contains(text(),'基础信息')]")

    @pytest.mark.p1
    # 合作-全部-短视频列表-视频
    def test_cooperalte_photo_list_photo_detail(self):
        self.checkout_cooperationVideo_module()
        # self.click('//*[@id="root"]/div/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[1]')
        self.assert_elements('//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[1]/div[1]/div/img')


    @pytest.mark.p1
    # 自营-全部-短视频列表-商品图片
    def test_cooperalte_photo_list_picturce(self):
        self.checkout_cooperationVideo_module()
        #self.click('//*[@id="root"]/div/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[2]/div')
        self.sleep(2)
        self.assert_elements('//*[@id="dilu_micro_root"]/div/div[9]/div[2]/div/div/div[1]/div[1]/div/ul/div[1]/li/div/div[2]/div[1]/div[2]/div/div/div/img')



