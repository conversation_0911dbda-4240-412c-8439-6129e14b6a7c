
import time
from datetime import datetime
from unittest import skip

import pytest
from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")
#。直播列表，复盘诊断
class TestLiveReview(BaseTestCase):
    @skip
    def test_live_review(self):
        self.login("SYT_DOMAIN", "supply_account")
        #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.find_element("(//span[contains(text(),'直播')])[1]",by="xpath").click()
        self.find_element("//span[@class='mfMkX6Qd7Q8IzuJwc0zJ']",by="xpath").click()
        time.sleep(1)
        self.find_element("(//span[contains(text(),'复盘诊断')])[6]",by="xpath").click()
        self.assert_element("(//span[contains(text(),'直播间成交金额')])[1]", by="xpath")
        self.assert_element("(//span[contains(text(),'退款金额')])[1]", by="xpath")
        self.find_element("(//button[@class='kwaishop-tianhe-live-pc-btn kwaishop-tianhe-live-pc-btn-primary'])[1]",by="xpath").click()
        self.switch_to_window(0)
        self.assert_element("(//span[contains(text(),'时长诊断')])[1]", by="xpath")

        #self.find_element("//span[contains(text(),'更多数据')]",by="xpath").click()
        #self.assert_element("//", by="xpath")
       # self.switch_to_window(0)