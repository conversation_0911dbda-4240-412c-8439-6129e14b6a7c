"""
# Time       ：2022/6/26 9:03 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
from ddt import ddt
import pytest
from unittest import skip
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import re


account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")

class TestSytPhoto(BaseTestCase):


    # 直播-切到短视频
    def checkout_photo_module_old(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        # 新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'短视频')])[1]")
        self.sleep(10)

    def checkout_photo_module(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/shortVideoManagement/selfoperatedshortvideos")
        self.sleep(10)

    @pytest.mark.p0
    def test_photo_overView(self):
        self.checkout_photo_module_old()
        #指标名称
        tabs_text = self.get_text("//div[@class='slick-slide slick-active slick-current']")
        name_list = ["新发布短视频数", "曝光次数", "退款金额（退款日）", "观看人数", "成交订单数", "成交金额"]
        for item in name_list:
             self.assert_in(item, tabs_text)
        self.assert_element("(//div[@customstyle='[object Object]'])[7]")
        #account_proxy_app.account_proxy_remove(**********, *********)


    @pytest.mark.p1
    #自营-全部-流量转化漏斗
    def test_photo_flow_cvr(self):
         self.checkout_photo_module()
         self.assert_text("全部页面转化漏斗","(//div[@customstyle='[object Object]'])[8]")
         # 漏斗图存在
         self.assert_element_present("//div[@class='kpro-data-funnel ']")
         self.assert_in("短视频曝光-观看转化率(人数)", self.get_text("//div[@class='kpro-data-funnel ']"))

    @pytest.mark.skip
    @pytest.mark.p1
    #自营-全部-流量转化漏斗-下载
    def test_photo_flow_cvr_download(self):
        self.checkout_photo_module()

        self.click("//span[contains(text(),'下载数据')]")
        self.assert_downloaded_file("短视频流量渠道明细列表.xlsx")
        self.sleep(3)
        self.delete_downloaded_file("短视频流量渠道明细列表.xlsx")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    #自营短视频-全部-流量页面来源明细-指标名
    def test_photo_flow_detail(self):
        self.checkout_photo_module()

        self.open("https://syt.kwaixiaodian.com/zones/collaboration/slimCollaborationDetail")

        real_info1 = self.get_text(
            "//div[@class='kpro-data-funnel ']")
        self.assert_in("短视频曝光次数", real_info1)
        self.assert_in("短视频有效播放次数", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # @pytest.mark.p1
    # # 自营短视频-全部-流量页面来源明细-条数
    # def test_photo_flow_detail_count(self):
    #     self.checkout_photo_module()
    #
    #     self.click("(//thead[@class='kwaishop-tianhe-shortVideoManagement-pc-table-thead'])[1]")  # 流量页面来源明细
    #     self.sleep(3)
    #     elements = self.find_elements('div.ant-tabs-tabpane.ant-tabs-tabpane-active div.ant-tabs-tabpane.ant-tabs-tabpane-active tr.ant-table-row.ant-table-row-level-0')
    #     self.assert_true(len(elements) == 6)
    #     self.assert_true(all(item.text != "" for item in elements) == True)
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.skip
    @pytest.mark.p1
    # 自营短视频-全部-流量页面来源明细-下载
    def test_photo_flow_detail_download(self):
        self.checkout_photo_module()

        self.click("//div[@id='rc-tabs-1-tab-source']")  # 流量页面来源明细
        self.sleep(3)
        self.click("//span[contains(text(),'下载数据')]")
        self.assert_downloaded_file("流量页面来源明细.xls")
        self.sleep(3)
        self.delete_downloaded_file("流量页面来源明细.xls")

    @pytest.mark.p1
    # 自营短视频-全部-流量页面来源明细-指标名
    def test_photo_flow_fufei(self):
        self.checkout_photo_module()
        # 指标名称
        self.click("//span[@class='slick-arrow slick-next']")
        self.sleep(1)
        tabs_text = self.get_text("//div[@class='slick-slide slick-active slick-current']")
        name_list = ["观看次数", "商品点击人数", "完播率", "引流直播间次数", "引流直播间成交金额", "短视频商品曝光次数"]
        for item in name_list:
            self.assert_in(item, tabs_text)
        self.assert_element("(//div[@customstyle='[object Object]'])[7]")
        # account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    @skip
    # 自营短视频-全部-流量页面来源明细-行数
    def test_photo_flow_fufei_count(self):
        self.checkout_photo_module()
        self.click("//div[@id='rc-tabs-1-tab-promote']//div[1]")  # 付费推广流量
        self.sleep(3)
        elements = self.find_elements(
            'div.ant-tabs-tabpane-active div.ant-tabs-tabpane-active div.ant-table.ant-table-bordered.ant-table-fixed-header.ant-table-has-fix-left tr.ant-table-row.ant-table-row-level-0')
        self.assert_true(len(elements) == 3)
        self.assert_true(all(item.text != "" for item in elements) == True)
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.skip
    @pytest.mark.p1
    # 自营短视频-全部-流量页面来源明细-下载
    def test_photo_flow_fufei_download(self):
        self.checkout_photo_module()
        self.click("//div[@id='rc-tabs-1-tab-promote']")  # 付费推广流量
        self.sleep(3)
        self.click("//span[contains(text(),'下载数据')]")
        self.sleep(1)
        self.assert_downloaded_file("付费推广流量.xls")
        self.sleep(3)
        self.delete_downloaded_file("付费推广流量.xls")
        #account_proxy_app.account_proxy_remove(**********, *********)


    @pytest.mark.p1
    #自营-全部-短视频列表-指标
    def test_photolist(self):
        self.checkout_photo_module()
        self.sleep(3)
        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(1)
        real_info1 = self.get_text(
            "//thead[@class='kwaishop-tianhe-shortVideos-pc-table-thead']")
        self.assert_in("短视频信息", real_info1)
        self.assert_in("商品信息", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("观看人数", real_info1)
        self.assert_in("曝光次数", real_info1)
        self.assert_in("商品点击-成交转化率（人数）", real_info1)
        self.assert_in("操作", real_info1)
        #account_proxy_app.account_proxy_remove(**********, *********)


    @pytest.mark.p1
    #自营-全部-短视频列表-跳转
    def test_photolist_jump(self):
        self.checkout_photo_module()
        self.sleep(3)

        self.click("//span[contains(text(),'短视频列表')]")  # 点击短视频详情
        self.sleep(10)

        self.assert_text("短视频列表", "//span[contains(text(),'短视频列表')]")
        self.assert_text("查看详情","//span[contains(text(),'查看详情')]")


    @pytest.mark.p1
    #自营-全部-短视频列表-跳转
    def test_photolist_jump_detail_c1(self):
        self.checkout_photo_module()
        self.sleep(3)

        self.click("//span[contains(text(),'短视频列表')]")  # 点击短视频详情
        self.sleep(2)

        self.click("//span[contains(text(),'指标配置')]")
        self.sleep(2)
        real_info1 = self.get_text('(//div[@class="group-filter-modal-content-left-opt-item"])[1]')
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("成交人数", real_info1)

        real_info2 = self.get_text('(//div[@class="group-filter-modal-content-left-opt-item"])[3]')
        self.assert_in("曝光次数", real_info2)
        self.assert_in("观看人数", real_info2)
        self.assert_in("完播率", real_info2)

    @pytest.mark.p1
    #自营-全部-短视频列表-跳转-流量页面转化漏斗
    def test_photolist_jump_detail_c2(self):
        self.checkout_photo_module()
        self.sleep(3)
        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(2)
        self.click("//div[contains(text(),'非带货短视频')]")  # 点击短视频详情

        self.assert_text("短视频列表", "//span[contains(text(),'短视频列表')]")
        self.assert_text("查看详情", "//span[contains(text(),'查看详情')]")

    @pytest.mark.skip
    @pytest.mark.p1
    # 自营-全部-短视频列表-跳转-成交金额对齐
    def test_photolist_jump_detail_c3(self):
        self.checkout_photo_module()
        self.sleep(3)

        self.click("(//div[@class='tabs-tab-btn__content'][contains(text(),'带货短视频')])[1]")

        self.click("(//span[contains(text(),'短视频详情')])[3]")  # 点击短视频详情
        self.sleep(1)
        meynumber1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div[2]/div/div[2]/div/div/div/div/div[contains(@class,"value")]')
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        if mey_number1 == "":
            mey_number1 = 0
        else:
            mey_number1 = int(mey_number1)
        self.sleep(1)
        meynumber2 = self.find_element('//*[@id="dilu_micro_root"]/div/div[6]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr/td[4]/div').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 == mey_number2

    @pytest.mark.p1
    #自营-全部-短视频列表-跳转-流量页面转化漏斗
    def test_photolist_jump_detail_c4(self):
        self.checkout_photo_module()
        self.sleep(3)

        self.assert_element("//div[@class='kpro-data-funnel ']")  #流量来源页面

        elements = self.find_elements("//div[@class='kpro-data-funnel ']/svg")
        #取前4个 个人页  其他 关注页 等
        self.assert_true(all(item.text != "" for item in elements[:4]) == True)


    @pytest.mark.p1
    def test_photolist_item_tanchuan(self):
        self.checkout_photo_module()

        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(2)
        self.click("//div[contains(text(),'非带货短视频')]")

        self.click("(//div[@class='Acr2u3Kk_zjVikasMHUh'])[1]")  # 点击播放
        self.sleep(2)
        self.assert_element("//div[@class='iZoaXjeOlL1x09kIYLb8 react-draggable']")

        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_photolist_page_reverse_1(self):
        self.checkout_photo_module()

        self.click("//span[contains(text(),'短视频列表')]")
        title1 = self.find_element("//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text

        self.click("(//button[contains(@class,'kwaishop-tianhe-shortVideos-pc-pagination-item-link')])[2]")
        self.sleep(2)
        title2 = self.find_element("//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text
        self.sleep(2)

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_photolist_page_reverse_2(self):
        self.checkout_photo_module()

        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(2)
        title1 = self.find_element("//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text

        self.click("//li[@title='3']")
        self.sleep(2)
        title2 = self.find_element("//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text


        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_photo_overView_sale(self):
        self.checkout_photo_module()
        # 切换非带货短视频
        self.click("//div[contains(text(),'非带货短视频')]")
        # 指标名称
        tabs_text = self.get_text("//div[@class='slick-slide slick-active slick-current']")
        name_list = ["新发布短视频数", "曝光次数", "涨粉人数", "观看人数", "完播率", "观看-曝光转化率（人数）"]
        for item in name_list:
            self.assert_in(item, tabs_text)
        self.assert_element("(//div[@customstyle='[object Object]'])[7]")

    @pytest.mark.p1
    # 自营-全部-短视频列表-指标
    def test_photolist_sale(self):
        self.checkout_photo_module()
        self.click("//div[contains(text(),'非带货短视频')]")
        self.sleep(3)
        self.click("//span[@class='slick-arrow slick-next']")
        self.sleep(1)
        real_info1 = self.get_text("//div[@class='slick-slide slick-active slick-current']")
        self.assert_in("有效播放次数", real_info1)
        self.assert_in("引流直播间次数", real_info1)
        self.assert_in("引流直播间成交金额", real_info1)
        self.assert_in("观看次数", real_info1)
        self.assert_in("直播预告短视频曝光人数", real_info1)
        self.assert_in("预告预约人数", real_info1)


    @pytest.mark.p1
    # 自营-全部-短视频列表-跳转
    def test_photolist_jump_sale(self):
        self.checkout_photo_module()
        self.click("//span[contains(text(),'视频榜单')]")
        self.sleep(3)
        self.assert_element("//div[contains(text(),'带货短视频榜单')]")

        real_info1 = self.get_text("//thead[@class='kwaishop-data-short-video-table-thead']")
        self.assert_in("短视频信息", real_info1)
        self.assert_in("关联商品", real_info1)
        self.assert_in("成交效果", real_info1)
        self.assert_in("流量效果", real_info1)
        self.assert_in("点赞次数", real_info1)
        self.assert_in("评论次数", real_info1)



    @pytest.mark.p1
    # 自营-全部-短视频列表-商详页
    def test_photolist_item_tanchuan_sale(self):
        self.checkout_photo_module()
        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(3)

        self.click("(//div[@class='Acr2u3Kk_zjVikasMHUh'])[1]")  # 点击播放
        self.sleep(2)
        self.assert_element("//div[@class='iZoaXjeOlL1x09kIYLb8 react-draggable']")

    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_photolist_page_reverse_1_sale(self):
        self.checkout_photo_module()
        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(2)
        self.click("//div[contains(text(),'非带货短视频')]")
        self.sleep(1)
        title1 = self.find_element(
            "//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text

        self.click("(//button[contains(@class,'kwaishop-tianhe-shortVideos-pc-pagination-item-link')])[2]")
        self.sleep(2)
        title2 = self.find_element(
            "//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text
        self.sleep(2)

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        # account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_photolist_page_reverse_2_sale(self):
        self.checkout_photo_module()
        self.click("//span[contains(text(),'短视频列表')]")
        self.sleep(2)
        self.click("//div[contains(text(),'非带货短视频')]")
        self.sleep(1)

        title1 = self.find_element(
            "//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text

        self.click("//li[@title='3']")
        self.sleep(2)
        title2 = self.find_element(
            "//tbody[contains(@class,'shortVideos-pc-table-tbody')]//div[contains(@class,'JhWVn6LEZXJOyli7NbnU')]").text

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2

    @pytest.mark.p0
    def test_photo_overView_nosale(self):
        self.checkout_photo_module()
        # 切到非带货短视频
        self.click("//div[contains(text(),'非带货短视频')]")
        self.sleep(1)
        self.click("//span[@class='slick-arrow slick-next']")
        self.sleep(1)
        # 指标名称
        tabs_text = self.get_text("//div[@class='slick-slide slick-active slick-current']")
        name_list = ["有效播放次数", "引流直播间次数", "引流直播间成交金额", "观看次数", "直播预告短视频曝光人数", "预告预约人数"]
        for item in name_list:
            self.assert_in(item, tabs_text)
        self.assert_element("(//div[@customstyle='[object Object]'])[7]")

    @pytest.mark.p1
    # 自营-非带货短视频列表-指标
    def test_photolist_nosale(self):
        self.checkout_photo_module()

        self.click("//span[contains(text(),'短视频列表')]")  # 点击短视频详情
        self.sleep(2)
        self.click("//div[contains(text(),'非带货短视频')]")
        self.sleep(2)
        self.click("//span[contains(text(),'指标配置')]")
        self.sleep(2)
        real_info1 = self.get_text('(//div[@class="group-filter-modal-content-left-opt-item"])[1]')
        self.assert_in("观看人数", real_info1)
        self.assert_in("曝光次数", real_info1)
        self.assert_in("有效播放次数", real_info1)
        self.assert_in("完播率", real_info1)

        real_info2 = self.get_text('(//div[@class="group-filter-modal-content-left-opt-item"])[3]')
        self.assert_in("引流直播间次数", real_info2)
        self.assert_in("引流直播间人数", real_info2)
        self.assert_in("引流直播间成交金额", real_info2)


    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_photolist_page_reverse_1_nosale(self):
        self.checkout_photo_module()
        self.click("//span[contains(text(),'视频榜单')]")
        self.sleep(2)
        self.click("//div[contains(text(),'带货短视频')]")
        title1 = self.find_element(
            "//tbody[contains(@class,'kwaishop-data-short-video-table-tbody')]//div[contains(@class,'aqgvOk6q6mWp4e4KH2dq')]").text

        self.click("//li[@title='3']")
        self.sleep(2)
        title2 = self.find_element(
            "//tbody[contains(@class,'kwaishop-data-short-video-table-tbody')]//div[contains(@class,'aqgvOk6q6mWp4e4KH2dq')]").text

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2
        # account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    # 自营-全部-短视频列表-翻页
    def test_photolist_page_reverse_2_nosale(self):
        self.checkout_photo_module()
        self.click("//span[contains(text(),'视频榜单')]")
        self.sleep(2)
        self.click("//div[contains(text(),'引流短视频')]")
        self.sleep(2)
        title1 = self.find_element(
            "//tbody[contains(@class,'kwaishop-data-short-video-table-tbody')]//div[contains(@class,'aqgvOk6q6mWp4e4KH2dq')]").text

        self.click("//li[@title='3']")
        self.sleep(2)
        title2 = self.find_element(
            "//tbody[contains(@class,'kwaishop-data-short-video-table-tbody')]//div[contains(@class,'aqgvOk6q6mWp4e4KH2dq')]").text

        substr1 = title1[:18]
        substr2 = title2[:18]
        assert substr1 != substr2




