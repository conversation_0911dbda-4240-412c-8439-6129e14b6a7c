"""
# Time       ：2022/6/26 9:03 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
import time

from ddt import ddt
from unittest import skip

from utils.http_help import BaseHttpRequest
from test_case.merchant_syt.base import BaseTestCase
import pytest


account_proxy_app = BaseHttpRequest(user_id="B_**********")

@ddt
@skip
class TestSytTrade(BaseTestCase):

    def checkout_trade_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(1)
        #新手引导
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)
        self.click("(//span[contains(text(),'交易')])[1]")
        self.sleep(1)
        if self.is_element_visible("//button[contains(text(),'知道了')]"):
            self.click("//button[contains(text(),'知道了')]")
        self.sleep(1)

    def checkout_trade_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.open("https://syt.kwaixiaodian.com/zones/trade/overview")
        self.sleep(5)
        if self.is_element_visible("//button[contains(text(),'知道了')]"):
            self.click("//button[contains(text(),'知道了')]")
        self.sleep(1)



    @pytest.mark.p0
    def test_trade_overView(self):
        self.checkout_trade_module_old()
        self.assert_text("商品浏览量","(//span[contains(text(),'商品浏览量')])[1]")
        self.assert_text("商品访客数","(//span[contains(text(),'商品访客数')])[1]")
        self.assert_text("累计创建订单人数","(//span[contains(text(),'累计创建订单人数')])[1]")
        self.assert_text("累计创建订单数","(//span[contains(text(),'累计创建订单数')])[1]")
        self.assert_text("累计创建订单金额","(//span[contains(text(),'累计创建订单金额')])[1]")
        self.assert_text("累计成交人数","(//span[contains(text(),'累计成交人数')])[1]")

        self.click("//span[contains(text(),'自建商品自卖')]")
        self.assert_element("//div[@class='LFcpjvf8hmXXqujhN9DN']")
        self.assert_element("//div[@class='MQ9xBmxMGwb_FYq8oIAv']")

        self.click("//span[contains(text(),'自建商品被分销')]")
        self.assert_element("//div[@class='LFcpjvf8hmXXqujhN9DN']")
        self.assert_element("//div[@class='MQ9xBmxMGwb_FYq8oIAv']")

        self.click("//span[contains(text(),'分销他人')]")
        self.assert_element("//div[@class='LFcpjvf8hmXXqujhN9DN']")
        self.assert_element("//div[@class='MQ9xBmxMGwb_FYq8oIAv']")

        self.click("//span[contains(text(),'三方商品')]")
        self.assert_element("//div[@class='LFcpjvf8hmXXqujhN9DN']")
        self.assert_element("//div[@class='MQ9xBmxMGwb_FYq8oIAv']")

        self.click("//button[@class='kwaishop-data-trade-btn']")
        self.assert_downloaded_file("safe.xls")
        self.delete_downloaded_file("safe.xls")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_trend(self):
        self.checkout_trade_module()
        if self.is_element_visible("(//div[@id='driver-popover-item'])[1]"):
            self.click("(//button[contains(text(),'知道了')])[1]")
            self.click("//span[@class='kpro-data-date-filter-new-daterange-title']")
        else:self.click("//span[@class='kpro-data-date-filter-new-daterange-title']")
        # 开始结束时间
        self.click('//*[@id="kwaishop-data-trade-overview-v2"]/div[2]/div[1]/div[2]/div/div[4]/div/div/div/div[2]/div')  #选择近7天
        self.assert_element("//div[@class='MQ9xBmxMGwb_FYq8oIAv']")  #趋势图
        #account_proxy_app.account_proxy_remove(**********, *********)

        # 下拉框
        # self.click('//*[@id="module-content"]/div/div[2]/div[2]/div[1]/div[1]/div[1]/span[2]')
        # self.input('//*[@id="rc_select_13"]',"下单金额")

    @pytest.mark.p0
    def test_trade_compose(self):
        self.checkout_trade_module()
        # 切换到 GMV构成
        self.click("(//div[@class='kpro-data-multilevel-card-first-card'])[1]")
        self.assert_text("自营成交金额","(//span[contains(text(),'自营成交金额')])[1]")

        self.assert_text("自营直播间","(//span[contains(text(),'自营直播间')])[1]")
        #self.assert_text("全店累计成交金额",'//*[@id="module-content"]/div/div/div/div[3]/div[1]/div/div[2]/div/div[1]/div[1]')
        self.assert_text("自营短视频","(//span[contains(text(),'自营短视频')])[1]")
        self.assert_text("自营商品卡","(//span[contains(text(),'自营商品卡')])[1]")
        self.assert_text("自营其他","(//span[contains(text(),'自营其他')])[1]")
        self.click("(//div[@class='kpro-data-multilevel-card-first'])[1]")
        self.sleep(3)
        self.assert_text("合作直播间","(//span[contains(text(),'合作直播间')])[1]")
        self.assert_text("合作短视频","(//span[contains(text(),'合作短视频')])[1]")
        self.assert_text("合作商品卡","(//span[contains(text(),'合作商品卡')])[1]")
        self.assert_text("合作其他","(//span[contains(text(),'合作其他')])[1]")

        self.assert_element_present("(//div[@class='kwaishop-data-trade-table-container'])[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    # def test_trade_compose_jump_live(self):
    #     self.checkout_trade_module()
    #     # 切换到 GMV构成
    #     self.click("(//div[@class='kpro-data-multilevel-card-first-card'])[1]")
    #
    #     self.click("//div[@class='kpro-data-multilevel-card-second kpro-data-multilevel-card-second-active']")
    #     self.click("//tbody/tr[2]/td[8]/button[1]/span[1]")  # 直播作战室
    #     self.switch_to_window(1)
    #     self.sleep(5)
    #     if self.is_element_visible("//div[@class='ant-modal-content']"):
    #         self.click("//button[@class='ant-btn ant-btn-text']")  #跳过
    #         self.sleep(3)
    #     self.assert_element("//span[contains(text(),'实时操盘系统')]")
    #     self.assert_element("//span[contains(text(),'直播间成交金额')]")
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_jump_photo(self):
        self.checkout_trade_module()
            # 切换到 GMV构成
        self.click("(//div[@class='kpro-data-multilevel-card-first-card'])[1]")

        self.sleep(1)
        self.click("(//span[contains(text(),'自营短视频')])[1]")
        self.sleep(1)
        self.click("//tbody/tr[2]/td[8]/button[1]/span[1]")  # 短视频详情
        self.switch_to_window(1)
        self.sleep(2)
        self.assert_element("//span[contains(text(),'基础信息')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_jump_good(self):
        self.checkout_trade_module()

        self.click("(//div[@class='kpro-data-multilevel-card-first-card'])[1]")

        self.sleep(1)
        self.click("(//span[contains(text(),'自营商品卡')])[1]")
        self.click("(//button[@type='button'])[4]")  #商品详情
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("(//div[@customstyle='[object Object]'])[2]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_jump_other(self):
        self.checkout_trade_module()

        self.click("(//div[@class='kpro-data-multilevel-card-first-card'])[1]")

        self.sleep(1)
        self.click("(//span[contains(text(),'自营其他')])[1]")
        self.click("(//button[@type='button'])[4]")  # 商品详情
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("(//div[@customstyle='[object Object]'])[2]")

        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_carried(self):
        self.checkout_trade_module()

        #按直播/短视频/商品卡/其他
        self.click("//span[contains(text(),'按直播/短视频/商品卡/其他')]")
        self.assert_text("直播间成交金额","//span[contains(text(),'直播间成交金额')]")
        self.assert_text("短视频成交金额", "//span[contains(text(),'短视频成交金额')]")
        self.assert_text("商品卡成交金额", "//span[contains(text(),'商品卡成交金额')]")
        self.assert_text("其他成交金额", "//span[contains(text(),'其他成交金额')]")
        #account_proxy_app.account_proxy_remove(**********, *********)


    @pytest.mark.p0
    # def test_trade_compose_carried_live(self):
    #     self.checkout_trade_module()
    #
    #     #按直播/短视频/商品卡/其他
    #     self.click("//span[contains(text(),'按直播/短视频/商品卡/其他')]")
    #
    #     self.click("//div[@class='kpro-data-multilevel-card-second kpro-data-multilevel-card-second-active']")
    #     self.click("//tbody/tr[2]/td[8]/button[1]/span[1]")  # 直播作战室
    #     self.switch_to_window(1)
    #     self.sleep(3)
    #     if self.is_element_visible("//div[@class='ant-modal-content']"):
    #         self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
    #         self.sleep(3)
    #     self.assert_element("//span[contains(text(),'实时操盘系统')]")
    #     self.assert_element("//span[contains(text(),'直播间成交金额')]")
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_carried_jump_photo(self):
        self.checkout_trade_module()

        # 按直播/短视频/商品卡/其他
        self.click("//span[contains(text(),'按直播/短视频/商品卡/其他')]")
        self.sleep(2)
        self.click("//span[contains(text(),'短视频成交金额')]")
        self.sleep(2)
        self.click("//tbody/tr[2]/td[8]/button[1]/span[1]")  # 短视频详情
        self.switch_to_window(1)
        self.sleep(1)
        self.assert_element("//span[contains(text(),'基础信息')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_carried_jump_good(self):
        self.checkout_trade_module()

        # 按直播/短视频/商品卡/其他
        self.click("//span[contains(text(),'按直播/短视频/商品卡/其他')]")
        self.sleep(2)
        self.click("//span[contains(text(),'商品卡成交金额')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'商品详情')])[1]")  #商品详情
        self.switch_to_window(1)
        self.sleep(5)
        self.assert_element("(//div[@customstyle='[object Object]'])[2]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_compose_jump_other_amt(self):
        self.checkout_trade_module()

        self.click("//span[contains(text(),'按直播/短视频/商品卡/其他')]")
        self.sleep(2)
        self.click("//span[contains(text(),'其他成交金额')]")
        self.sleep(2)
        self.click("(//span[contains(text(),'商品详情')])[1]")  # 商品详情
        self.switch_to_window(1)
        self.sleep(2)
        self.assert_element("(//div[@customstyle='[object Object]'])[2]")

        #account_proxy_app.account_proxy_remove(**********, *********)



    @pytest.mark.p0
    def test_trade_compose_account(self):
        self.checkout_trade_module()
        self.click("//span[contains(text(),'交易总览')]")

        self.click("//div[@class='ZIQ0vuJwRlvtxZ1mHJwH']//div//div[@class='kpro-data-multilevel-card-first kpro-data-multilevel-card-first-active']")
        self.assert_text("自营直播间","//span[contains(text(),'自营直播间')]")
        self.assert_text("自营短视频","//span[contains(text(),'自营短视频')]")
        self.assert_text("自营商品卡","//span[contains(text(),'自营商品卡')]")
        self.assert_text("自营其他","//span[contains(text(),'自营其他')]")
        self.click("//div[@class='ZIQ0vuJwRlvtxZ1mHJwH']//div//div[@class='kpro-data-multilevel-card-first']")
        self.assert_text("合作直播间","//span[contains(text(),'合作直播间')]")
        self.assert_text("合作短视频","//span[contains(text(),'合作短视频')]")
        self.assert_text("合作商品卡","//span[contains(text(),'合作商品卡')]")
        self.assert_text("合作其他","//span[contains(text(),'合作其他')]")

        self.assert_element("//div[@class='table-container']")
        self.assert_element("//div[@class='chart-container']")
        self.click("//button[@role='switch']")
        self.assert_element("(//div[@class='kwaishop-data-trade-spin-nested-loading'])[5]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_trade_contain(self):
        self.checkout_trade_module()
        self.click("//span[contains(text(),'交易构成')]")

        self.assert_element("//div[@class='kpro-data-module-wrap']")
        self.click("//span[@class='kpro-data-date-filter-new-daterange-title']") #近7天
        self.click("//span[@class='label-selected']") #近30天
        self.click("//div[@class='kpro-data-date-filter-new-daterange-overlay-right']")
        self.assert_element("//div[@class='kpro-data-module-wrap']")
        #account_proxy_app.account_proxy_remove(**********, *********)