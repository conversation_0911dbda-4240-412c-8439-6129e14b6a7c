"""
# Time       ：2022/6/27 9:49 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
from unittest import skip

from ddt import ddt

from utils.http_help import BaseHttpRequest
from test_case.merchant_syt.base import BaseTestCase
import pytest

account_proxy_app = BaseHttpRequest(user_id="B_**********")
# @ddt
class TestSytLogistics(BaseTestCase):

    # 直播-切到售后模块
    def checkout_logistics_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.refresh()
        self.sleep(3)
        # 新手引导
        self.click("//span[contains(text(),'服务')]")
        self.sleep(2)
        self.click("//span[contains(text(),'售后总览')]")
        self.sleep(2)
        # 点击服务 tab



    def checkout_logistics_module_nomock(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        # 新手引导
        self.tanchuan()

    @pytest.mark.p1
    def test_logistics_overview(self):
        self.checkout_logistics_module()

        # 点击服务 tab
        self.click("//span[contains(text(),'物流履约')]")
        self.sleep(3)
        real_info1 = self.get_text(
            "//div[contains(@class,'kwaishop-tianhe-serviceManagement-pc-spin-nested-loading')]")
        self.assert_in("揽收超时率", real_info1)
        self.assert_in("揽收后停滞率", real_info1)
        self.assert_in("揽收后停滞率", real_info1)
        self.assert_in("发签时长", real_info1)
        self.assert_in("超长单占比", real_info1)
        self.assert_in("发货及时率", real_info1)



    @skip
    def test_quality_overview(self):
        self.checkout_logistics_module()

        self.click("//span[contains(text(),'品质概览')]")
        self.sleep(3)
        self.assert_text("商家主营一级类目",'//*[@id="quality-data-page"]/div/div[4]/section[2]/div/div[1]/div/div/div/div[1]/span[1]')
        self.assert_element('//*[@id="quality-data-page"]/div/div[4]/section[2]/div/div[2]/div/div')

        self.click('//*[@id="quality-data-page"]/div/div[5]/section[2]/div/div[1]/div[2]')
        self.click('//*[@id="quality-data-page"]/div/div[5]/section[2]/div/div[3]/div/div/div/div/ul/li[1]/div[1]') #美妆
        self.click('//*[@id="quality-data-page"]/div/div[5]/section[2]/div/div[1]/button[2]') #查询

        self.assert_text('商品品质分析','//*[@id="quality-data-page"]/div/div[6]/section[1]/section[1]')
        self.click('//*[@id="quality-data-page"]/div/div[6]/section[2]/div[2]/div/div/div/div/div/table/tbody/tr[2]/td[7]/button')
        self.switch_to_window(1)
        self.assert_element('//*[@id="kwaishop-syt-layout-container-new"]/div/header/div')
        self.switch_to_window(0)
        self.click('//*[@id="quality-data-page"]/div/div[6]/section[2]/div[2]/div/div/ul/li[10]/div[1]')
        self.click('//*[@id="quality-data-page"]/div/div[6]/section[2]/div[2]/div/div/ul/li[10]/div[1]/div[2]/div/div/div/div[2]/div/div/div/div[2]/div')
        self.assert_element('//*[@id="quality-data-page"]/div/div[6]/section[2]/div[2]/div/div/div/div/div/table/tbody')

        self.click('//*[@id="quality-data-page"]/div/div[7]/section[2]/div[2]/div[2]/div/div/div/a') #查看详情
        self.switch_to_window(2)
        self.assert_element('//*[@id="root"]/section/header/div')
        self.switch_to_window(1)
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.skip
    def test_quality_overview_nomock(self):
        self.checkout_logistics_module_nomock()

        self.click("//span[contains(text(),'品质概览')]")
        self.sleep(3)
        self.assert_text("商家主营一级类目",
                     '//*[@id="quality-data-page"]/div/div[4]/section[2]/div/div[1]/div/div/div/div[1]/span[1]')


        self.assert_text('商品品质分析', '//*[@id="quality-data-page"]/div/div[6]/section[1]/section[1]')
        self.assert_element('//*[@id="quality-data-page"]/div/div[6]/section[2]/div[2]/div/div/div/div/div/table/tbody')

        self.click('//*[@id="quality-data-page"]/div/div[7]/section[2]/div[2]/div[2]/div/div/div/a')  # 查看详情
        self.switch_to_window(1)
        self.assert_element('//*[@id="root"]/section/header/div')
        self.switch_to_window(0)
