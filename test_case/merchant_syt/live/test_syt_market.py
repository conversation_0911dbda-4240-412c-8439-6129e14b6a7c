import re
from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
#
#
account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")


class TestSytMarketV2(BaseTestCase):

    def checkout_market_module_old(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'广告')])[1]")
        self.sleep(5)

    def checkout_market_module(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        self.driver.maximize_window()

        account_proxy_app.account_proxy_confirm(**********,*********)
        self.open("https://syt.kwaixiaodian.com/zone/marketing/overview")
        self.sleep(5)


    def test_market_overview(self):
        self.checkout_market_module_old()
        self.sleep(2)
        real_info1 = self.get_text("//div[contains(@class,'kpro-data-module-wrap undefined')]//div[contains(@class,'ordercard-container')]")
        self.assert_in("花费(非全站推广)", real_info1)
        self.assert_in("当日累计GMV(非全站推广)", real_info1)
        self.assert_in("当日累计ROI(非全站推广)", real_info1)

    def test_market_choose_key(self):
        self.checkout_market_module()

        self.click("//span[contains(text(),'自定义指标')]")

        real_info1 = self.get_text("//div[@class='ant-checkbox-group']")
        self.assert_in("花费", real_info1)
        self.assert_in("当日成交金额", real_info1)
        self.assert_in("当日ROI", real_info1)
        self.assert_in("涨粉数", real_info1)

    def test_market_list(self):
        self.checkout_market_module()
        self.click("(//button[@aria-label='展开行'])[1]")
        element_selectors = [
            "//td[contains(text(),'作品推广')]",  # 替换为你要检查的选择器
            "//td[contains(text(),'直播推广')]",  # 另一个选择器
            "//td[contains(text(),'短视频推广')]"  # 还有一个选择器
        ]

        element_found = False
        for selector in element_selectors:
            elements = self.find_elements(selector)
            if elements:  # 如果找到元素
               element_found = True
               break

        # 校验是否至少找到一个元素
        self.assertTrue(element_found, "二级类型不存在！")

    def test_market_overview_cljn(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-shop']")
        self.sleep(5)

        real_info1 = self.get_text('//*[@id="scroll-content"]/div[3]/div/div[1]/section[2]/div/div/div/section')
        self.assert_in("花费(非全站推广)", real_info1)
        self.assert_in("当日累计GMV(非全站推广)", real_info1)
        self.assert_in("当日累计ROI(非全站推广)", real_info1)
        self.assert_in("涨粉数", real_info1)

        self.click("//a[contains(text(),'前往磁力金牛查看更多广告推广数据')]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'立即登录')])[2]")


    def test_market_choose_key_cljn(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-shop']")
        self.sleep(5)

        self.click("//span[contains(text(),'自定义指标')]")
        self.assert_element("//div[@id='rcDialogTitle0']")
        real_info1 = self.get_text("//div[@class='ant-checkbox-group']")
        self.assert_in("花费", real_info1)
        self.assert_in("当日成交金额", real_info1)
        self.assert_in("当日ROI", real_info1)
        self.assert_in("涨粉数", real_info1)

    def test_market_account_detail_cljn(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-shop']")
        self.sleep(5)

        title1 = self.find_element(
            '//*[@id="scroll-content"]/div[3]/div/div[2]/section[2]/div/div/div/div/div/div/table/tbody/tr[2]/td[1]').text

        self.click("//li[contains(@title,'下一页')]")
        self.sleep(3)
        title2 = self.find_element(
            '//*[@id="scroll-content"]/div[3]/div/div[2]/section[2]/div/div/div/div/div/div/table/tbody/tr[3]/td[1]').text
        self.sleep(3)
        substr1 = title1[:8]
        substr2 = title2[:8]
        assert substr1 != substr2

    def test_market_account_detail_cljn(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-shop']")
        self.sleep(5)

        self.click(
            "//span[@class='ant-table-column-title-no-align'][contains(text(),'花费')]")
        self.sleep(1)
        self.click(
            "//span[@class='ant-table-column-title-no-align'][contains(text(),'花费')]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="scroll-content"]/div[3]/div/div[2]/section[2]/div/div/div/div/div/div/table/tbody/tr[2]/td[3]/div/div[1]').text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = '//*[@id="scroll-content"]/div[3]/div/div[2]/section[2]/div/div/div/div/div/div/table/tbody/tr[3]/td[3]/div/div[1]'
        if(self.is_element_visible(meynumber2)):
            meynumber2 = ('//*[@id="scroll-content"]/div[3]/div/div[2]/section[2]/div/div/div/div/div/div/table/tbody/tr[3]/td[3]/div/div[1]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 >= mey_number2

    def test_market_account_detail_jump_cljn(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-shop']")
        self.sleep(5)

        self.click("(//span[contains(text(),'去投放')])[1]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("(//span[contains(text(),'立即登录')])[2]")

    def _test_market_overview_fans(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-fans']")
        self.sleep(5)

        real_info1 = self.get_text("//section[@class='DOAReip9fweaTeU7E4IZ']")
        self.assert_in("花费", real_info1)
        self.assert_in("投放当日引导的累计GMV", real_info1)
        self.assert_in("投放当日累计ROI", real_info1)
        self.assert_in("涨粉数", real_info1)

    def _test_market_choose_key_fans(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-fans']")
        self.sleep(5)

        self.click("//span[contains(text(),'自定义指标')]")
        self.assert_element("//div[@id='rcDialogTitle0']")
        real_info1 = self.get_text("//div[@class='ant-checkbox-group']")
        self.assert_in("花费", real_info1)
        self.assert_in("当日成交金额", real_info1)
        self.assert_in("当日ROI", real_info1)
        self.assert_in("涨粉数", real_info1)
        self.assert_in("播放数", real_info1)

    def _test_market_account_detail_fans(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-0-tab-fans']")
        self.sleep(5)
        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("日期", real_info1)
        self.assert_in("推广类型", real_info1)

        self.click("//span[contains(text(),'直播推广')]")
        real_info2 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("日期", real_info2)
        self.assert_in("推广类型", real_info2)

        self.click("//span[contains(text(),'智能推广')]")
        real_info3 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("日期", real_info3)
        self.assert_in("推广类型", real_info3)

    def _test_market_overview_bomb(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-2-tab-flash-bomb']")
        self.sleep(5)

        real_info1 = self.get_text("//section[@class='DOAReip9fweaTeU7E4IZ']")
        self.assert_in("花费", real_info1)
        self.assert_in("投放当日引导的累计GMV", real_info1)
        self.assert_in("投放当日累计ROI", real_info1)
        self.assert_in("涨粉数", real_info1)
        self.assert_in("进人数", real_info1)

    def _test_market_overview_bomb(self):
        self.checkout_market_module()
        self.click("//div[@id='rc-tabs-2-tab-flash-bomb']")
        self.sleep(5)

        real_info1 = self.get_text("//thead[@class='ant-table-thead']")
        self.assert_in("花费", real_info1)
        self.assert_in("当日累计GMV", real_info1)
        self.assert_in("当日累计ROI", real_info1)
        self.assert_in("广告曝光量", real_info1)
        self.assert_in("进入观众数量", real_info1)

    def _test_market_people(self):
        self.checkout_market_module()

        self.click("//div[@id='zone_marketing_person']")
        self.sleep(3)
        self.assert_element("//h3[contains(text(),'性别分布')]")
        self.assert_element("//h3[contains(text(),'年龄分布')]")
        self.assert_element("//h3[contains(text(),'地域分布')]")





























