import random
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_short_video_page import *
from utils.merchantDataUtils import *
import re
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytGoodsList(BaseTestCase):
    def checkout_shortVideo_list(self):
        # 进入到短视频模块
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(5)
        # 点击短视频
        self.click(HomePage.short_video_tab)
        # 点击短视频列表
        self.click(ShortVideoPage.shortVideo_list)
        self.sleep(5)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(5)

    def random_time_selection(self):
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(ShortVideoPage.time_frame.format(time_frame=date_text))
        self.sleep(3)


    def test_shortVideo_list_with_item(self):
        """测试带货短视频"""
        #获取当前列表的条数
        self.checkout_shortVideo_list()
        #点击带货短视频
        self.click(ShortVideoPage.shortVideo_with_item_tab)
        self.random_time_selection()
        # 获取当前列表的条数
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 10:
            # 获取列表一个视频的id
            video_id = self.get_text(ShortVideoPage.shortVideo_list_first_id)
            # 点击下一页 再获取第一条的title
            self.click(ShortVideoPage.next_page)
            self.sleep(3)
            video_id_after_click = self.get_text(ShortVideoPage.shortVideo_list_first_id)
            # 判断两条不相等
            assert video_id != video_id_after_click

    def test_shortVideo_list_without_item(self):
        """测试非带货短视频"""
        self.checkout_shortVideo_list()
        #点击非带货短视频
        self.click(ShortVideoPage.shortVideo_without_item_tab)
        self.random_time_selection()
        # 获取当前列表的条数
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 10:
            # 获取列表一个视频的id
            video_id = self.get_text(ShortVideoPage.shortVideo_list_first_id)
            # 点击下一页 再获取第一条的title
            self.click(ShortVideoPage.next_page)
            self.sleep(3)
            video_id_after_click = self.get_text(ShortVideoPage.shortVideo_list_first_id)
            # 判断两条不相等
            assert video_id != video_id_after_click


    def test_shortVideo_list_sort_with_item(self):
        """带货短视频 列表排序"""
        self.checkout_shortVideo_list()
        # 点击带货短视频
        self.click(ShortVideoPage.shortVideo_with_item_tab)
        self.random_time_selection()
        # 没数据 没有排序的必要
        if self.is_element_visible(ShortVideoPage.no_data):
            return

        # 获取当前列表的条数
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 2:
            #点击 升序
            self.click(ShortVideoPage.short_video_exposure_header)
            self.sleep(3)
            val1 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx=2)))
            val2 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx=3)))
            assert val1 <= val2
            # 点击 降序
            self.click(ShortVideoPage.short_video_exposure_header)
            self.sleep(3)
            val3 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx=2)))
            val4 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx=3)))
            assert val3 >= val4




    def test_shortVideo_list_sort_without_item(self):
        """非带货短视频 列表排序"""
        self.checkout_shortVideo_list()
        # 点击非带货短视频
        self.click(ShortVideoPage.shortVideo_without_item_tab)
        self.random_time_selection()
        #没数据 没有排序的必要
        if self.is_element_visible(ShortVideoPage.no_data):
            return
        # 获取当前列表的条数
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 2:
            # 点击 升序
            self.click(ShortVideoPage.short_video_exposure_header)
            self.sleep(3)
            val1 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx = 2)))
            val2 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx = 3)))
            assert val1 <= val2
            # 点击 降序
            self.click(ShortVideoPage.short_video_exposure_header)
            self.sleep(3)
            val3 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx = 2)))
            val4 = convert_to_float(self.get_text(ShortVideoPage.short_video_exposure_value.format(idx = 3)))
            assert val3 >= val4





    def test_shortVideo_list_search(self):
        """测试短视频列表搜索"""
        self.checkout_shortVideo_list()
        self.random_time_selection()
        # 获取当前列表的条数
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 0:
            #获取一个商品id
            goods_id = self.get_text(ShortVideoPage.goods_info_id)
            # 输入搜索id
            self.type(ShortVideoPage.search_input_with_item, goods_id)
            self.sleep(2)
            # 获取搜索后的列表的条数
            count_after_search = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
            assert count_after_search >= 1;


    def test_shortVideo_list_check_detail(self):
        """测试短视频列表详情"""
        self.checkout_shortVideo_list()
        self.random_time_selection()
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 0:
            #获取跳转前的id
            id_before_click = self.get_text(ShortVideoPage.shortVideo_list_first_id)
            # 点击查看详情
            self.click(ShortVideoPage.shortVideo_detail)
            self.sleep(3)
            # 获取跳转后的id
            id_after_click = (re.search(r'\d+', self.get_text(ShortVideoPage.shortVideo_detail_id))
                              .group(0)) if re.search(r'\d+', self.get_text(ShortVideoPage.shortVideo_detail_id)) else None
            # 判断是否相等
            assert id_before_click == id_after_click
            #数据概览指标存在
            data_overview_list = self.get_text(ShortVideoPage.shortVideo_detail_data)
            self.assert_in("成交金额", data_overview_list)
            self.assert_in("成交订单数", data_overview_list)
            self.assert_in("成交人数", data_overview_list)

    def test_shortVideo_list_detail_traffic(self):
        """测试短视频列表详情流量分析"""
        self.checkout_shortVideo_list()
        self.random_time_selection()
        # 获取当前列表的条数
        total_count = extract_total_count(self.get_text(ShortVideoPage.shortVideo_list_bottom_count))
        if total_count > 0:
            # 点击查看详情
            self.click(ShortVideoPage.shortVideo_detail)
            self.sleep(3)
            self.switch_to_window(1)
            print(self.get_current_url())
            #观看页面要和漏斗页面的值相同
            # 随机选择一个渠道 获取其观看人数 判断其和漏斗的观看人数是否一致
            random_number = random.randint(2, 8)
            self.click(ShortVideoPage.detail_chanel_name.format(idx=random_number))
            self.sleep(3)
            # 获取渠道列表的观看人数
            val1 = self.get_text(ShortVideoPage.detail_chanel_view_count.format(idx=random_number))
            # 获取漏斗的观看人数
            val2 = self.get_text(ShortVideoPage.funnel_view_count)
            # 判断是否相等
            assert val1 == val2 or (val1 in ['0','-'] and val2 in ['0','-'])













