import random
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_short_video_page import *
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytGoodsOverview(BaseTestCase):

    def checkout_shortVideo_overview(self):
        # 进入到短视频模块
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(5)
        #点击短视频
        self.click(HomePage.short_video_tab)
        self.sleep(5)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(5)


    def check_data_overview_with_item(self):
        # 校验数据概览
        self.assert_text("新发布短视频数", ShortVideoPage.new_video_count_xpath)
        self.assert_text("成交金额", ShortVideoPage.transaction_amount_xpath)
        self.assert_text("成交订单数", ShortVideoPage.transaction_order_count_xpath)
        self.assert_text("退款金额（退款日）", ShortVideoPage.refund_amount_xpath)
        self.assert_text("观看人数", ShortVideoPage.view_count_xpath)
        self.assert_text("曝光次数", ShortVideoPage.exposure_count_xpath)
        #趋势图存在
        self.assert_element(ShortVideoPage.right_trend_chart)

    def check_data_overview_without_item(self):
        self.assert_text("新发布短视频数", ShortVideoPage.new_video_count_xpath)
        self.assert_text("观看人数", ShortVideoPage.view_count_xpath)
        self.assert_text("曝光次数", ShortVideoPage.exposure_count_xpath)
        self.assert_text("观看曝光转化率", ShortVideoPage.view_exposure_conversion_rate_xpath)
        self.assert_text("涨粉人数", ShortVideoPage.fan_count_xpath)
        self.assert_text("完播率", ShortVideoPage.view_completion_rate_xpath)
        # 趋势图存在
        self.assert_element(ShortVideoPage.right_trend_chart)

    def random_time_selection(self):
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(ShortVideoPage.time_frame.format(time_frame=date_text))
        self.sleep(3)

    def check_traffic_conversion(self):
        # 流量页面转化
        #随机选择一个渠道 获取其观看人数 判断其和漏斗的观看人数是否一致
        random_number = random.randint(3, 8)
        self.click(ShortVideoPage.chanel_name.format(idx = random_number))
        self.sleep(3)
        #获取渠道列表的观看人数
        val1 = self.get_text(ShortVideoPage.chanel_view_count.format(idx = random_number))
        #获取漏斗的观看人数
        val2 = self.get_text(ShortVideoPage.funnel_view_count.format())
        #判断是否相等
        assert val1 == val2

    def navigate_to_cooperation_shortVideo(self):
        # 跳转合作短视频页面
        self.click(ShortVideoPage.go_to_cooperation_shortVideo)
        self.sleep(3)
        self.assert_element(ShortVideoPage.cooperation_shortVideo_title)



    def test_shortVideo_overview_with_item(self):
        """带货短视频校验"""
        self.checkout_shortVideo_overview()
        #点击带货短视频
        self.click(ShortVideoPage.shortVideo_with_item_tab)
        # 时间选择
        self.random_time_selection()
        # 校验数据概览
        self.check_data_overview_with_item()
        # 校验流量页面转化
        self.check_traffic_conversion()
        # 跳转合作短视频页面
        self.navigate_to_cooperation_shortVideo()



    def test_shortVideo_overview_without_item(self):
        """非带货短视频校验"""
        self.checkout_shortVideo_overview()
        #点击非带货短视频
        self.click(ShortVideoPage.shortVideo_without_item_tab)
        # 时间选择
        self.random_time_selection()
        # 校验数据概览
        self.check_data_overview_without_item()
        # 校验流量页面转化
        self.check_traffic_conversion()
        # 跳转合作短视频页面
        self.navigate_to_cooperation_shortVideo()











