# -*- coding:utf-8 -*-
"""
Python 3.10
author：zhangpinghui03
date：2023年12月05日
"""
import pytest
from ddt import ddt
from unittest import skip, skipIf

from test_case.merchant_syt.base import BaseTestCase
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
#
#
account_proxy_app = BaseHttpRequest(user_id="B_**********")
@ddt
@pytest.mark.skip

class MyTestClass(BaseTestCase):

    def prepare(self):
        self.login("SYT_DOMAIN", "supply_account")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(3)
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        elif self.is_element_visible("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']"):
                self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        else: self.assert_element("//div[@class='AOSjK37cO3N6aGXAjqF4']")
        self.sleep(1)
        if self.is_element_visible("//span[contains(text(),'川流计划')]"):
            self.click("//span[contains(text(),'川流计划')]")
        else:
            self.sleep(3)
            self.click("//span[contains(text(),'川流计划')]")

    def test_chuanliu_plan(self):
        self.prepare()
        self.click("//div[@class='action']")
        if self.is_element_visible("//span[contains(text(),'数据效果')]"):
            self.click("//div[@class='action']")
            self.assert_element_present("(//th[@class='kwaishop-data-chuanliu-pc-table-cell kwaishop-data-chuanliu-pc-table-cell-fix-left'])[1]")
            self.click("//th[3]//div[1]//div[2]//span[1]//*[name()='svg']")
            self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")
            self.assert_element_visible("(//span[contains(text(),'提升手段-获得更多川流计划流量！')])[1]")
            self.click("//div[contains(text(),'更多达人')]//img")
            self.switch_to_window(0)
            self.click("//div[contains(text(),'设置直播计划')]")
            self.switch_to_window(0)
        #account_proxy_app.account_proxy_remove(**********, *********)



    def test_fuyao_plan(self):
        self.prepare()
        self.click("//span[contains(text(),'扶摇计划')]")
        self.assert_text("如何参与扶摇计划",'body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > div:nth-child(1) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1)')
        self.assert_element_visible("(//div[@class='vbo5uvfuboJi7EW38XqT'])[1]")
        #account_proxy_app.account_proxy_remove(**********, *********)