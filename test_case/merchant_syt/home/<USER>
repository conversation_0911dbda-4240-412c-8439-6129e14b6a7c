# -*- coding: utf-8 -*-
"""
@Time ： 2023/11/15 4:41 PM
@Auth ： zhanle
@File ：test_vip.py
@IDE ：PyCharm
"""
from unittest import skip

from ddt import ddt
from seleniumbase import BaseCase
from test_case.merchant_syt.base import BaseTestCase
import requests
from utils.http_help import BaseHttpRequest
from selenium.webdriver.common.keys import Keys
import pytest


account_proxy_app = BaseHttpRequest(user_id="B_**********")

@ddt
class TestSytUser(BaseTestCase):

    # 切到用户-会员数据
    def checkout_member_module(self):

        self.login("SYT_DOMAIN", "supply_account")
    #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(1)

        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)

        self.click("//span[contains(text(),'用户')]")
        self.sleep(1)
        self.click("//span[contains(text(),'会员数据')]")
        self.sleep(1)

    def checkout_no_member_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.maximize_window()
        self.refresh()
        self.sleep(1)
        #self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        self.sleep(1)
        self.click("//span[contains(text(),'用户')]")
        self.sleep(1)
        self.click("//span[contains(text(),'会员数据')]")
        self.sleep(1)

    @pytest.mark.p1
    def test_no_member_overView(self):
        self.checkout_no_member_module()
        self.click("//span[contains(text(),'会员数据')]")
        self.assert_element("//div[@id='kwaishop-data-user-container']")

    # 会员数据
    @skip
    @pytest.mark.p1
    def test_member_overView(self):
        self.checkout_member_module()
        self.click("//span[contains(text(),'会员数据')]")

        self.assert_no_404_errors()
        self.assert_element('//*[@id="root"]')
        self.assert_element("//span[contains(text(),'核心数据')]")
        self.click("//div[@class='Lh6l_MZnciYJplsFEv_e']")
        #self.click("//div[@class='btn-item selected']")
        #self.click("//div[@class='kpro-data-date-filter-new-daterange-overlay-right']")
        self.click("//span[@class='Fl0TyZUOx2NlCEeiwFZm']") #下载
        #self.assert_downloaded_file("工作台_会员数据看板_会员核心数据.xls")
        #self.delete_downloaded_file("工作台_会员数据看板_会员核心数据.xls")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 买家分析
    @pytest.mark.p1
    @pytest.mark.skip
    def test_seller_analysis_merchant(self):
        self.checkout_member_module()
        self.click("//span[contains(text(),'买家分析')]")


        self.click("//a[contains(text(),'提升商品点击转化 >')]")
        self.assert_element("//div[@class='zone-crm-spin-nested-loading gNpNvOWJfKZNYvA8e9Jv']")
        self.go_back()
        self.click("//a[contains(text(),'提升下单转化 >')]")
        self.assert_element("//div[@class='zone-crm-spin-nested-loading gNpNvOWJfKZNYvA8e9Jv']")
        self.go_back()
        self.click("//a[contains(text(),'提升成交转化 >')]")
        self.assert_element("//div[@class='zone-crm-spin-nested-loading gNpNvOWJfKZNYvA8e9Jv']")
        self.go_back()

        self.click("//label[@class='ant-radio-button-wrapper']//span[contains(text(),'短视频')]")  #切短视频按钮
        self.click("//a[contains(text(),'提升商品点击转化 >')]")
        self.assert_element("//div[@class='zone-crm-spin-nested-loading gNpNvOWJfKZNYvA8e9Jv']")
        self.go_back()
        self.click("//a[contains(text(),'提升下单转化 >')]")
        self.assert_element("//div[@class='zone-crm-spin-nested-loading gNpNvOWJfKZNYvA8e9Jv']")
        self.go_back()
        self.click("//a[contains(text(),'提升成交转化 >')]")
        self.assert_element("//div[@class='zone-crm-spin-nested-loading gNpNvOWJfKZNYvA8e9Jv']")
        self.go_back()

        self.click("//tbody/tr[2]/td[9]/button[1]")
        self.assert_text("商品分析","//a[contains(text(),'商品分析')]")
        self.click("//a[contains(text(),'商品分析')]")
        self.assert_element("//div[@class='kpro-data-page-header-module-header']")

        account_proxy_app.account_proxy_remove(**********, *********)

    # 粉丝分析
    @pytest.mark.p1
    def test_fans_analysis(self):
        self.checkout_member_module()

        self.click("//span[contains(text(),'粉丝分析')]")
        self.assert_no_404_errors()
        account_proxy_app.account_proxy_remove(**********, *********)
