# -*- coding: utf-8 -*-
"""
@Time ： 2023/11/28 9:58 PM
@Auth ： zhanle
@File ：test_syt_distributor.py
@IDE ：PyCharm
"""
from ddt import ddt
from unittest import skip
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest


account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytTrade(BaseTestCase):

    def checkout_distributor_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.sleep(1)
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        #新手引导

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)
        self.refresh()
        self.sleep(1)

        self.click("//span[contains(text(),'达人合作')]")
        self.sleep(3)
        if self.is_element_visible("//div[@class='VpIP1yFmUG94lwOfFD5b']//span"):
            self.click("//img[@class='zqkOZIyCpVY51qdNuCM1']")

    def go_distributor_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/distributionCollabo/distributionExpert")

    @pytest.mark.p1
    def test_distributor_overview(self):
        self.go_distributor_module()
        self.sleep(2)

        self.assert_text("成交退款","//span[contains(text(),'成交退款')]")
        self.assert_element("//span[contains(text(),'成交金额')]")
        self.assert_element("//canvas")  # 趋势图

        self.click("//div[contains(text(),'合作短视频')]")
        self.assert_element("//span[contains(text(),'短视频曝光次数')]")


        self.assert_element("//canvas") #趋势图
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    def test_flow_cvr(self):
        # self.checkout_distributor_module()
        import time
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        cur_min = int(time.strftime('%M', time.localtime(time.time())))
        if cur_hour < 11:
            return
        self.go_distributor_module()
        self.click("//span[contains(text(),'合作内容明细')]")
        self.sleep(2)
        self.assert_text("流量页面转换漏斗","//span[contains(text(),'流量页面转换漏斗')]")
        self.click("//*[contains(text(),'短视频有效播放次数')]")
        self.assert_element("//div[@class='kpro-data-funnel ']//*[name()='svg']") #漏斗图
        self.assert_element("//div[@class='kpro-data-doughnut-content']//div//div//canvas") #饼状图
        #account_proxy_app.account_proxy_remove(**********, *********)


    def test_trade_channel(self):
        self.go_distributor_module()
        self.click("//span[contains(text(),'指标配置')]")
        self.sleep(3)
        self.assert_element("//div[contains(text(),'可添加指标')]")
        self.sleep(3)
        self.click("//span[contains(text(),'确 定')]")  # 取消全选
        self.sleep(3)
        #self.assert_element("//div[@class='kwaishop-data-distribution-table-content']")
        if self.is_element_visible("(//span[contains(text(),'成交商品')])[2]"):
            self.click("(//span[contains(text(),'成交商品')])[2]")  # 成交商品
            self.sleep(3)
            self.assert_text("成交商品列表", "//span[contains(text(),'成交商品列表')]")
            self.assert_element("//div[@class='kwaishop-tianhe-distributionCollabo-pc-drawer-body']//div//div[@class='kwaishop-tianhe-distributionCollabo-pc-table-container']")
        else:
            return

        #account_proxy_app.account_proxy_remove(**********, *********)

    '''
    def test_cooperation(self):
        self.checkout_distributor_module()

        self.assert_element("//div[@class='kwaishop-data-distribution-table-content']")
        self.click("//span[contains(text(),'自定义指标')]")
        self.sleep(3)
        self.click("//div[@class='filter-checked-all']") #全选
        self.sleep(3)
        self.click("//div[@class='filter-checked-all']") #取消全选
        self.sleep(3)
        self.click("//div[@class='kwaishop-data-distribution-checkbox-group']//div[2]")  #点击成交订单数
        self.sleep(3)
        self.click("//button[@class='kwaishop-data-distribution-btn kwaishop-data-distribution-btn-primary']")
        self.sleep(3)
        self.assert_element("//div[@class='kwaishop-data-distribution-table-content']")

        self.click("(//span[contains(text(),'成交商品')])[1]")  #成交商品
        self.sleep(3)
        self.assert_text("成交商品列表","//div[contains(text(),'成交商品列表')]")
        self.assert_element("(//div[@class='kwaishop-data-distribution-spin-container'])[8]")
        account_proxy_app.account_proxy_remove(**********, *********)
    '''


