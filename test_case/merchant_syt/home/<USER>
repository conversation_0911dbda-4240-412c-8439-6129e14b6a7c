from os.path import exists
from random import random
import random

from ddt import ddt
from unittest import skip, skipIf
import pytest

from page_objects.merchant_data.syt_homepage import HomePage
from utils.http_help import BaseHttpRequest
from test_case.merchant_syt.base import BaseTestCase
account_proxy_app = BaseHttpRequest(user_id="B_**********")
from page_objects.merchant_data.syt_homepage import HomePage


# @ddt

class MyTestClass(BaseTestCase):

    def check_self_module(self):

        self.login("SYT_DOMAIN", "supply_account")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()

        #self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")

        self.sleep(3)


    # 生意通首页base校验
    @pytest.mark.p1
    def test_homepage(self):
        self.check_self_module()
        self.sleep(2)

        self.assert_title("快手小店生意通")
        tabs_text = self.get_text("//div[@class='resizeContain___rZzS0']")
        tab_list = ["首页",  "市场", "直播", "短视频", "商品卡", "合作", "商城", "搜索", "商品","交易","用户"]
        for item in tab_list:
            self.assert_in(item, tabs_text)
        #account_proxy_app.account_proxy_remove(**********, *********)



    @pytest.mark.p1
    def test_realtime(self):
        self.check_self_module()

        self.assert_text("经营概览", "//div[@class='normal-title']")
        real_info1 = self.get_text("//div[contains(@class,'overview-datacard-slim-swiper')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        real_info2 = self.get_text("//div[contains(@class,'channel-data-cards')]")
        self.assert_in("自营直播", real_info2)
        self.assert_in("自营短视频", real_info2)
        self.assert_in("达人合作", real_info2)
        self.assert_in("自营商品卡", real_info2)
        self.assert_element("//div[@class='trend-chart']")

    @pytest.mark.p0
    # 生意通新首页经营概览览-查看更多
    def test_homepage_todetail(self):
        self.check_self_module()
        self.sleep(10)
        self.click("//div[@class='to-detail']")  # 查看更多
        self.switch_to_window(1)
        self.assert_text('全店成交分析', "(//span[contains(text(),'全店成交分析')])[2]")

    @pytest.mark.p1
    # 生意通首页经营总览
    def test_homepage_overview(self):
        self.check_self_module()
        self.sleep(1)
        self.assert_text("经营概览", "//div[@class='normal-title']")
        self.click("//div[contains(text(),'近7天')]")
        real_info1 = self.get_text("//div[contains(@class,'overview-datacard-slim-swiper')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("退款金额(退款日)", real_info1)
        real_info2 = self.get_text("//div[contains(@class,'channel-data-cards')]")
        self.assert_in("自营直播",real_info2)
        self.assert_in("自营短视频",real_info2)
        self.assert_in("自营商品卡",real_info2)
        self.assert_in("达人合作", real_info2)
        self.assert_text("超级链接","(//div[@class='otherContain'])[1]")
        self.assert_text("其他", "(//div[@class='otherContain'])[2]")
        #account_proxy_app.account_proxy_remove(**********, *********)



    @pytest.mark.p0
    # 生意通新首页经营诊断
    def test_homepage_diagnosis(self):
        self.check_self_module()
        self.assert_text("经营诊断", "//span[contains(text(),'经营诊断')]")
        self.assert_element("//div[@class='tip-container']")  # 诊断内容
        # account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    # 生意通新首页经营诊断
    def test_homepage_diagnosis_suggestion(self):
        self.check_self_module()
        i = random.randint(1, 5)
        self.click('//*[@id="home-management-diagnosis"]/div/div/div[2]/div[1]/div[1]/div[1]/div/div[' + str(i) + ']')
        j = random.randint(1, 2)
        self.click('//*[@id="home-management-diagnosis"]/div/div/div[2]/div[2]/div[' + str(j) + ']')
        self.sleep(1)
        self.assert_element('//*[@id="Diagnosis_CardList_right"]')
        self.click('//*[@id="Diagnosis_CardList_left"]/div[1]/div[1]/div[2]/span')
        self.switch_to_window(1)
        self.assert_element('//*[@id="root"]')
        # account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p1
    # 生意通首页经营总览
    # def test_homepage_overview_jump_live(self):
    #     self.check_self_module()
    #     self.sleep(1)
    #
    #     #验证跳转
    #     self.click("//div[@class='kpro-data-multilevel-card-second kpro-data-multilevel-card-second-active']")
    #     self.click("(//span[contains(text(),'直播作战系统')])[1]") #直播作战室
    #     self.switch_to_window(1)
    #     self.sleep(3)
    #     if self.is_element_visible("//div[@class='ant-modal-content']"):
    #         self.click("//button[@class='ant-btn ant-btn-text']")  # 跳过
    #         self.sleep(3)
    #     self.assert_element("//span[contains(text(),'实时操盘系统')]")
    #     #account_proxy_app.account_proxy_remove(**********, *********)

    # @pytest.mark.p1
    # # 生意通首页经营总览
    # def test_homepage_overview_jump_photo(self):
    #     self.check_self_module()
    #     self.click("//div[@class='kpro-data-multilevel-card-second-card-title']//span[contains(text(),'自营短视频')]")
    #     self.sleep(2)
    #     self.click("(//span[contains(text(),'短视频详情')])[1]") #短视频详情
    #     self.switch_to_window(1)
    #     self.sleep(3)
    #     self.assert_element("//span[contains(text(),'基础信息')]")
    #     self.switch_to_window(0)
    #     #account_proxy_app.account_proxy_remove(**********, *********)


    # 历史数据流量数据
    @pytest.mark.p1
    @pytest.mark.skip(reason="已废弃")
    def test_homepage_history_live(self):
        self.check_self_module()

        self.click("//div[@id='home-history-data']//div[@class='syt-micro-home-tabs-tab syt-micro-home-tabs-tab-active']")
        history_test = self.get_text_content('//*[@id="rc-tabs-0-panel-flow"]/div/div/div/div/div[1]')
        self.assert_in("直播间曝光次数", history_test)
        self.assert_in("直播间标准观看次数", history_test)
        self.assert_in("短视频曝光次数", history_test)
        self.assert_in("短视频有效播放次数", history_test)
        self.assert_in("商品曝光次数", history_test)
        self.assert_in("商品点击次数", history_test)

        self.click('//*[@id="rc-tabs-0-panel-flow"]/div/div/div/div/div[1]/div['+ str(random.randint(1, 6)) +']')
        self.assert_element('//*[@id="rc-tabs-0-panel-flow"]/div/div/div/div/div[2]')

        # 详细入口
        self.click('//*[@id="home-history-data"]/div[2]')
        self.switch_to_window(1)
        menu_text = self.get_text_content("//span[@customstyle='[object Object]'][contains(text(),'直播间流量')]")
        self.assert_in("直播间流量", menu_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 历史数据营销数据
    @pytest.mark.skip(reason="已废弃")
    @pytest.mark.p1
    def test_homepage_history_mark(self):
        self.check_self_module()

        self.click("//div[@id='rc-tabs-0-tab-market']")
        self.assert_element("//div[@class='kpro-data-tabsdatacard-tabContent']")

        # 详细入口
        self.click('//*[@id="home-history-data"]/div[2]')
        menu_text = self.get_text_content("//div[@class='module-left module-title']")
        self.assert_in("营销概览", menu_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 历史数据商品数据
    @pytest.mark.skip(reason="已废弃")
    @pytest.mark.p1
    def test_homepage_history_goods(self):
        self.check_self_module()

        self.click('//*[@id="rc-tabs-0-tab-item"]')

        self.assert_text("在售商品数", '//*[@id="rc-tabs-0-panel-item"]/div/div/div/div/div[1]/div[1]')
        self.assert_text("点击商品数", '//*[@id="rc-tabs-0-panel-item"]/div/div/div/div/div[1]/div[2]')
        self.assert_text("动销商品数", '//*[@id="rc-tabs-0-panel-item"]/div/div/div/div/div[1]/div[3]')

        self.click('#rc-tabs-0-panel-item > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child('+ str(random.randint(1, 3)) +')')
        self.assert_element( '//*[@id="rc-tabs-0-panel-item"]/div/div/div/div/div[2]/div/div/div')

        # 详细入口
        self.click('//*[@id="home-history-data"]/div[2]')
        self.sleep(5)
        menu_text = self.get_text_content("//span[@customstyle='[object Object]'][contains(text(),'商品总览')]")
        self.assert_in("商品总览", menu_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 历史数据交易数据
    @pytest.mark.skip(reason="已废弃")
    @pytest.mark.p1
    def test_homepage_history_deal(self):
        self.check_self_module()

        self.click('//*[@id="rc-tabs-0-tab-trade"]')
        self.assert_text("累计创建订单人数", '#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child(1)')
        self.assert_text("累计成交人数", '#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child(2)')
        self.assert_text("累计创建订单数", '#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child(3)')
        self.assert_text("累计成交订单数", '#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child(4)')
        self.assert_text("累计创建订单金额", '#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child(5)')
        self.assert_text("累计成交金额", '#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child(6)')

        self.click('#rc-tabs-0-panel-trade > div > div > div > div > div.kpro-data-tabsdatacard-tabBar > div:nth-child('+ str(random.randint(1, 6)) +')')
        self.assert_element('//*[@id="rc-tabs-0-panel-trade"]/div/div/div/div/div[2]/div/div/div')

        # 详细入口
        self.click('//*[@id="home-history-data"]/div[2]')
        menu_text = self.get_text_content("(//span[contains(text(),'全店成交分析')])[2]")
        self.assert_equal(menu_text, "全店成交分析")
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 历史经营看板用户数据
    @pytest.mark.skip(reason="已废弃")
    @pytest.mark.p1
    def test_homepage_history_user(self):
        self.check_self_module()

        self.click('//*[@id="home-history-data"]/div[1]/div[1]/div[1]/div/div[5]')

        self.assert_text("成交人数", '//*[@id="rc-tabs-0-panel-user"]/div/div/div/div/div[1]/div[1]')
        self.assert_text("新增粉丝人数", '//*[@id="rc-tabs-0-panel-user"]/div/div/div/div/div[1]/div[2]')
        self.assert_text("直播间引导成交人数",'//*[@id="rc-tabs-0-panel-user"]/div/div/div/div/div[1]/div[3]')
        self.assert_text("带货短视频引导成交人数", '//*[@id="rc-tabs-0-panel-user"]/div/div/div/div/div[1]/div[4]')

        self.click('//*[@id="rc-tabs-0-panel-user"]/div/div/div/div/div[1]/div[' + str(random.randint(1, 4)) + ']')
        self.assert_element(
            '//*[@id="rc-tabs-0-panel-user"]/div/div/div/div/div[2]/div/div/div')

        # 详细入口
        self.click('//*[@id="home-history-data"]/div[2]')
        menu_text = self.get_text_content("//*[@id='scroll-content']/div[3]/div/div[1]")
        self.assert_in("买家分析", menu_text)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 历史经营看板售后数据
    @pytest.mark.skip(reason="已废弃")
    @pytest.mark.p1
    def test_homepage_history_sale(self):
        self.check_self_module()

        self.click('//*[@id="home-history-data"]/div[1]/div[1]/div[1]/div/div[6]')

        self.assert_text("退款金额", '//*[@id="rc-tabs-0-panel-afterSale"]/div/div/div/div/div[1]/div[1]')
        self.assert_text("退款订单数", '//*[@id="rc-tabs-0-panel-afterSale"]/div/div/div/div/div[1]/div[2]')
        self.assert_text("纠纷介入退款订单数", '//*[@id="rc-tabs-0-panel-afterSale"]/div/div/div/div/div[1]/div[3]')

        self.click('//*[@id="rc-tabs-0-panel-afterSale"]/div/div/div/div/div[1]/div[' + str(random.randint(1, 3)) + ']')
        self.assert_element(
            '//*[@id="rc-tabs-0-panel-afterSale"]/div/div/div/div/div[2]/div/div/div')

        # 详细入口
        self.click('//*[@id="home-history-data"]/div[2]')
        menu_text = self.get_text_content("//*[@id='scroll-content']/div/div[1]/div[1]")
        self.assert_in("售后总览", menu_text)
        #account_proxy_app.account_proxy_remove(**********, *********)




