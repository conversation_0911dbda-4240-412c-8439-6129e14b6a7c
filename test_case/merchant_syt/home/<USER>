# -*- coding: utf-8 -*-
"""
@Time ： 2023/11/17 3:00 PM
@Auth ： zhanle
@File ：syt_chuanliu.py
@IDE ：PyCharm
"""
from ddt import ddt
from seleniumbase import BaseCase
from test_case.merchant_syt.base import BaseTestCase
import requests
from unittest import skip
from utils.http_help import BaseHttpRequest
import pytest



account_proxy_app = BaseHttpRequest(user_id="B_**********")


class TestSytFlowPlan(BaseTestCase):

    # 切到用户-流量计划
    def checkout_flowplan_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #新手引导
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        self.sleep(1)

        self.click("//span[contains(text(),'政策')]")
        self.sleep(1)
        self.click("//span[@class='linkMenu___DEWk7'][contains(text(),'川流计划')]")



    def test_chuanliu(self):
        self.checkout_flowplan_module()
        self.assert_no_404_errors()
        self.assert_element("//div[@class='module-left module-title']")
        #account_proxy_app.account_proxy_remove(**********, *********)
        