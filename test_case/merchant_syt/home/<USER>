# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/11 11:04 AM
@Auth ： zhanle
@File ：test_kssyt_rukou.py
@IDE ：PyCharm
"""
from ddt import ddt

from page_objects.merchant_data.syt_homepage import HomePage
from test_case.merchant_syt.base import BaseCase
from constant.account import get_account_info
from constant.domain import get_domain
from test_case.merchant_syt.base import BaseTestCase

# 获取账号信息

# 用户名 account_data['account']
# 密码 account_data['password']
@ddt
class TestRuKou(BaseTestCase):

    def test_live_plan_rukou(self):
        self.login3("RESERVATION_DOMAIN","maliya")
        self.maximize_window()

        self.sleep(3)
        self.click(HomePage.my_live)  #我的直播
        self.sleep(3)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(3)
        self.click(HomePage.data_screen) #数据大屏
        self.switch_to_window(1)
        self.sleep(5)
        #self.click("//span[contains(text(),'跳过')]")
        if self.is_element_visible("//span[contains(text(),'实时操盘系统')]"):
            self.assert_element("//span[contains(text(),'直播间成交金额')]")
        else:
            self.assert_element("//span[contains(text(),'主作战室')]") #主作战室

    def test_live_plan_rukou_2(self):
        self.login3("RESERVATION_DOMAIN", "maliya")
        self.maximize_window()

        self.sleep(3)
        self.click(HomePage.my_live)  # 我的直播
        self.sleep(3)

        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(2)
        # self.click("//span[contains(text(),'回到旧版')]")
        # self.sleep(2)
        # self.click("//span[contains(text(),'继续切换')]")
        # self.sleep(2)
        #//*[@id="main-container"]/section/main/div/div/div[2]/div/div[1]/div[2]/div[2]
        self.click('//*[@id="main-container"]/section/main/div/div/div[2]/div/div[1]/div[2]/div[2]')
        self.switch_to_window(1)
        self.sleep(5)
        #self.click("//span[contains(text(),'跳过')]")
        if self.is_element_visible("//span[contains(text(),'实时操盘系统')]"):
            self.assert_element("//span[contains(text(),'直播间成交金额')]")
        else:
            self.assert_element("//span[contains(text(),'主作战室')]")

    def test_live_plan_rukou_3(self):
        self.login3("RESERVATION_DOMAIN", "maliya")
        self.sleep(2)
        self.maximize_window()
        self.click(HomePage.my_live)  # 我的直播

        self.sleep(2)
        self.click("(//span[contains(text(),'查看详情')])[1]")
        self.sleep(2)
        self.click('//*[@id="main-container"]/section/main/div/div/div[2]/div/div[1]/div[2]/div[2]')
        self.switch_to_window(1)
        self.sleep(5)

        if self.is_element_visible("//span[contains(text(),'实时操盘系统')]"):
            self.assert_element("//span[contains(text(),'直播间成交金额')]")
        else:
            self.assert_element("//span[contains(text(),'主作战室')]")


