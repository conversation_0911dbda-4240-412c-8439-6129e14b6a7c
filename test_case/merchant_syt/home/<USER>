# -*- coding: utf-8 -*-
"""
@Time ： 2024/9/19 4:18 PM
@Auth ： zhanle
@File ：test_syt_dpdb.py
@IDE ：PyCharm
"""
from ddt import ddt
from unittest import skip, skipIf
from seleniumbase import BaseCase
import pytest
from test_case.merchant_syt.base import BaseTestCase
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import re
#
#
account_proxy_app = BaseHttpRequest(user_id="sytAD_**********")

# @ddt
class MyTestClass(BaseTestCase):

    def prepare_old(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()

        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)

        self.click("(//span[contains(text(),'商城')])[1]")
        self.sleep(2)
        self.click("//div[@id='zones_goodsCardManagement_brandSubsidy']//a")  #大牌大补
        self.sleep(1)

    def prepare(self):

        self.login("SYT_DOMAIN", "syt_account_wj")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.open("https://syt.kwaixiaodian.com/zones/goodsCardManagement/brandSubsidy")
        self.sleep(5)

    #流量概览
    @pytest.mark.p0
    def test_cole_overview(self):
        self.prepare_old()
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("已报名商品数", real_info1)
        self.assert_in("曝光商品数", real_info1)
        self.assert_in("被访问商品数", real_info1)
        self.assert_in("动销商品数", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("退款订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("客单价", real_info1)
        self.assert_in("单均价", real_info1)
        self.assert_in("曝光人数", real_info1)
        self.assert_in("点击人数", real_info1)
        self.assert_in("曝光-点击率", real_info1)
        self.assert_in("点击-成交率", real_info1)
        #account_proxy_app.account_proxy_remove(**********, **********)

    #流量概览
    @pytest.mark.p0
    def test_trend(self):
        self.prepare()
        self.assert_element("(//canvas)[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    #商品明细-指标明细
    @pytest.mark.p0
    def test_list_detail(self):
        self.prepare()
        list_info = self.get_text("//thead[@class='kwaishop-tianhe-goodsCardManagement-pc-table-thead']")
        self.assert_in("商品信息", list_info)
        self.assert_in("成交金额", list_info)
        self.assert_in("退款金额", list_info)
        self.assert_in("成交订单数", list_info)
        self.assert_in("退款订单数", list_info)
        self.assert_in("成交人数", list_info)
        self.assert_in("客单价", list_info)
        self.assert_in("单均价", list_info)
        self.assert_in("曝光人数", list_info)
        self.assert_in("点击人数", list_info)
        self.assert_in("曝光-点击率", list_info)
        self.assert_in("点击-成交率", list_info)
        #account_proxy_app.account_proxy_remove(**********, **********)

    #商品明细-指标降序
    @pytest.mark.p0
    def test_rank_amt(self):
        self.prepare()
        #成交金额降序
        self.click("//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title'][contains(text(),'成交金额')]")
        self.sleep(1)
        self.click("//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title'][contains(text(),'成交金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div/div/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div').text
        #meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div/div/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[2]/div').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 商品信息 - 切页功能正常
    @pytest.mark.p1
    def test_click_title(self):
        self.prepare()
        title1 = self.find_element('//*[@id="root"]/div[1]/div/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div/div').text

        self.click("//li[@title='下一页']//button[@type='button']")
        self.sleep(3)
        title2 = self.find_element('//*[@id="root"]/div[1]/div/div/div[3]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div/div').text
        self.sleep(3)
        substr1 = title1[:15]
        substr2 = title2[:15]
        assert substr1 != substr2
        #account_proxy_app.account_proxy_remove(**********, **********)







