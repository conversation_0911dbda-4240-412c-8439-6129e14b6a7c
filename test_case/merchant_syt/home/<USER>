"""
# Time       ：2022/6/27 9:49 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
from unittest import skip

import pytest
from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest


account_proxy_app = BaseHttpRequest(user_id="B_**********")

# @ddt
class TestSytCustomer(BaseTestCase):

    # 直播-切到服务模块
    def checkout_cs_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
    #    username = self.get_text('//*[@id="DropDownContainer"]/span')
    #    if username == '言植优选':
    #        pass
    #    else:
    #        self.map_user_id(*********)
        # 代理账号
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)
        self.refresh()
        self.sleep(1)
        self.click("//span[contains(text(),'服务')]")
        self.sleep(1)
        self.click("//span[contains(text(),'客服概览')]")
        self.sleep(1)


    def checkout_cs_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        # 代理账号
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zone/service/overview")
        self.sleep(5)

    @pytest.mark.p1
    def test_cs_overview(self):
        self.checkout_cs_module_old()
        # 点击弹窗
        #self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # 客服数据
        self.assert_text("客服分析", "//span[contains(text(),'客服分析')]")
        self.sleep(3)
        real_info1 = self.get_text(
            "//thead[@class='kwaishop-tianhe-serviceManagement-pc-table-thead']")
        self.assert_in("人工会话量", real_info1)
        self.assert_in("3分钟回复率", real_info1)
        self.assert_in("平均响应时长", real_info1)
        self.assert_in("不服务率", real_info1)
        self.assert_in("im不满意率", real_info1)
        self.assert_in("询单转化金额", real_info1)

    @pytest.mark.p1
    def test_cs_history(self):
        self.checkout_cs_module()
        # 时间切换
        self.click("(//div[contains(text(),'近1天')])")
        self.click("(//div[contains(text(),'近7天')])")
        self.click("(//div[contains(text(),'近30天')])")
        # 下拉 客服
        # self.click('#module-content>div>div:nth-child(4)>div:nth-child(1)>div:nth-child(2)>div:nth-child(2)')
        # self.click('#module-content>div>div:nth-child(4)>div:nth-child(1)>div:nth-child(2)>div:nth-child(2)>div>span:nth-child(2)')
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p1
    def test_cs_trend(self):
        self.checkout_cs_module()
        # 数据总览
        self.assert_text("人工会话量", "(//span[contains(text(),'人工会话量')])")
        self.assert_text("3分钟回复率", "(//span[contains(text(),'3分钟回复率')])")
        self.assert_text("平均响应时长", "(//span[contains(text(),'平均响应时长')])")
        self.assert_text("不服务率", "(//span[contains(text(),'不服务率')])")
        self.sleep(2)
        # 时间选择
        self.click("(//div[contains(text(),'近1天')])")
        self.click("(//div[contains(text(),'近7天')])")
        self.click("(//div[contains(text(),'近30天')])")
        self.sleep(2)
        # 导出数据
        self.click("(//span[contains(text(),'下载数据')])")

        #account_proxy_app.account_proxy_remove(**********, *********)



