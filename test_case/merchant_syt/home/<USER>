# -*- coding:utf-8 -*-
"""
Python 3.10
author：zhangpinghui03
date：2023年12月05日
"""

from ddt import ddt
from unittest import skip, skipIf
from seleniumbase import BaseCase
import pytest
from test_case.merchant_syt.base import BaseTestCase
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
#
#
account_proxy_app = BaseHttpRequest(user_id="B_**********")

# @ddt
class MyTestClass(BaseTestCase):

    def prepare(self):

        self.login("SYT_DOMAIN", "supply_account")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()

        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(3)



        self.click("(//div[contains(text(),'实时')])")

    def prepare_old(self):

        self.login("SYT_DOMAIN", "supply_account")
        #self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()

        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.sleep(3)



        self.click("(//span[contains(text(),'实时')])[1]")


    # @pytest.mark.p0
    # def test_realtime_overview_old(self):
    #     self.prepare_old()
    #     #self.assert_text("实时概览","(//div[@class='card-container'])[1]")
    #     self.assert_text("今日成交金额","//span[contains(text(),'今日成交金额')]")
    #     self.assert_text("直播间成交金额","//span[contains(text(),'直播间成交金额')]")
    #     self.assert_text("短视频成交金额","//span[contains(text(),'短视频成交金额')]")
    #     self.assert_text("商品卡成交金额","//span[contains(text(),'商品卡成交金额')]")
    #     self.assert_element_visible("(//canvas)[1]")
    #     self.assert_text("对比日期","(//span[@class='margin-right-4'][contains(text(),'对比日期')])[1]")
    #     #self.assert_text("商品点击次数","//div[@class='slick-track']")
    #     self.assert_text("创建订单数","(//span[contains(text(),'创建订单数')])[1]")
    #     self.assert_text("成交订单数","(//span[contains(text(),'成交订单数')])[1]")
    #     self.assert_text("商品曝光次数","(//span[contains(text(),'商品曝光次数')])[1]")
    #     self.click("//span[@aria-label='system-arrow-large-right-line']//*[name()='svg']")
    #     self.assert_text("创建订单人数","(//span[contains(text(),'创建订单人数')])[1]")
    #     self.assert_text("退款订单数","(//span[contains(text(),'退款订单数')])[1]")
    #     # self.click("(//*[name()='svg'])[57]")
    #     # self.assert_text("投放涨粉","(//span[contains(text(),'投放涨粉')])[2]")
    #     self.click("(//span[contains(text(),'自营')])[2]")
    #     self.click("(//span[contains(text(),'达人合作')])[2]")
    #     #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    def test_realtime_overview(self):
        self.prepare()
        self.click("(//div[contains(text(),'营销推广')])")
        self.sleep(3)
        self.click("//span[contains(@class,'text-by-arrow___WeSTh')]")
        self.assert_text("花费(非全站推广)", "//div[contains(text(),'花费(非全站推广)')]")
        self.assert_text("当日累计GMV(非全站推广)", "//div[contains(text(),'当日累计GMV(非全站推广)')]")
        self.assert_text("当日累计ROI(非全站推广)", "//div[contains(text(),'当日累计ROI(非全站推广)')]")
        self.assert_text("涨粉数", "//div[contains(text(),'涨粉数')]")



    def test_realtime_detail(self):
        self.prepare()
        #self.assert_text("直播间明细","(//div[@id='rc-tabs-4-tab-livePayAmt'])[1]")
        self.assert_text("直播内容","(//div[contains(text(),'直播内容')])")
        self.assert_text("短视频内容","(//div[contains(text(),'短视频内容')])")
        self.assert_text("达人合作", "(//div[contains(text(),'达人合作')])")
        self.assert_text("营销推广", "(//div[contains(text(),'营销推广')])")
        self.assert_text("用户运营", "(//div[contains(text(),'用户运营')])")

        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    def test_realtime_marketing_effectiveness_overview(self):
        self.prepare()
        self.sleep(3)
        # self.assert_text("直播间明细","(//div[@id='rc-tabs-4-tab-livePayAmt'])[1]")
        self.assert_text("成交金额", "(//span[contains(text(),'成交金额')])")
        self.assert_text("成交人数", "(//span[contains(text(),'成交人数')])")
        self.assert_text("退款金额", "(//span[contains(text(),'退款金额')])")
        self.assert_text("自营直播", "(//span[contains(text(),'自营直播')])")
        self.assert_text("自营短视频", "(//span[contains(text(),'自营短视频')])")
        self.assert_text("达人合作", "(//span[contains(text(),'达人合作')])")
        self.assert_text("自营商品卡", "(//span[contains(text(),'自营商品卡')])")
        self.assert_text("超级链接", "(//span[contains(text(),'超级链接')])")
        self.assert_text("其他", "(//span[contains(text(),'其他')])")
        #account_proxy_app.account_proxy_remove(**********, **********)




