# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/18 3:31 PM
@Auth ： zhan<PERSON>
@File ：test_trade_refund.py.py
@IDE ：PyCharm
"""
import random
from unittest import skip

from ddt import ddt

from page_objects.merchant_data.syt_refund_page import RefundPage
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import re
account_proxy_app = BaseHttpRequest(user_id="B_**********")

#@pytest.mark.skip
class TestSytLiveNew(BaseTestCase):


    def checkout_refund_new(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        if self.var2 == "prt" :
          self.open("https://eshop-syt.prt.kwaixiaodian.com/zones/tradeManagement/refundAnalysis")
          self.sleep(10)
        else:
          self.open("https://syt.kwaixiaodian.com/zones/tradeManagement/refundAnalysis")

    #全部商品
    #核心指标
    @pytest.mark.p0
    def test_refund_core_overView(self):
        self.checkout_refund_new()
        self.sleep(10)

        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        real_info1 = self.get_text(
            "//div[@class='Fr4Cneutq5ayWCYHx9OA']")
        self.assert_in("金额退款率（支付日）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)




    @pytest.mark.p0
    def test_refund_core_overView_list(self):
        self.checkout_refund_new()
        self.sleep(10)

        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        real_info1 = self.get_text(
            "(//div[contains(@data-kael-component-style-scope,'comp_dORvPafeMJBubMLMvMZoD')])[1]")
        self.assert_in("退货退款", real_info1)
        self.assert_in("仅退款", real_info1)
        self.assert_in("发货前退款", real_info1)
        self.assert_in("发货后退款", real_info1)

   # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_live_desc(self):
        self.checkout_refund_new()

        self.click("(//span[contains(text(),'自营退款')])")

        self.click("(//span[contains(text(),'详情')])[1]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_live_reverse(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[1]")


        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

   # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_photo_desc(self):
        self.checkout_refund_new()

        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[2]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_photo_reverse(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

        # 载体构成-售卖方式拆分-载体二级页

    @pytest.mark.p0
    def test_refund_compose_goods_desc(self):
        self.checkout_refund_new()

        self.click("(//span[contains(text(),'达人合作退款')])")
        self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[3]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list_compare.format(idx=3)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_compare_second.format(idx=3)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_goods_reverse(self):
        self.checkout_refund_new()

        self.click("(//span[contains(text(),'达人合作退款')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[3]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:

            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_self_desc(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'直播间退款')])")
        self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[4]")
        # 成交订单数降序
        self.click(
            "(//span[contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[contains(text(),'成交订单数')])[1]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_self_reverse(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'直播间退款')])")
        self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[4]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_cooperate_desc(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'短视频退款')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[4]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 2:
            # //*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div/div[1]
            # 成交订单数的数量
            meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
            # meynumber1 = '1,23'
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_cooperate_reverse(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'短视频退款')])")

        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[4]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_account_compose_desc(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'商品卡退款')])")
        self.sleep(3)
        self.click("(//span[contains(text(),'详情')])[4]")
        # 成交订单数降序
        self.click("//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click("//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = match2[0]
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = match[0]
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    @pytest.mark.p0
    def test_refund_account_compose_reverse(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'商品卡退款')])")
        self.sleep(3)
        self.click("(//span[contains(text(),'详情')])[4]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

            self.click("//li[contains(@title,'下一页')]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
            self.sleep(3)
            assert title1 != title2
        else:
            return


    #全部商品-实时
    #核心指标
    @pytest.mark.p0
    def test_refund_core_overView_real(self):
        self.checkout_refund_new()
        self.sleep(5)

        idx = str(random.choice([num for num in range(1, 4)]))
        path = RefundPage.sale_style.format(idx=idx)
        self.click(path)
        self.sleep(2)

        idx1 = str(random.choice([num for num in range(1,4)]))
        path1 = RefundPage.sale_style.format(idx=idx1)
        self.click(path1)
        self.sleep(2)
        real_info1 = self.get_text(
            "//div[@class='Fr4Cneutq5ayWCYHx9OA']")
        self.assert_in("金额退款率（支付日）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)



   # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_live_desc_real(self):
        self.checkout_refund_new()


        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[1]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 1:
        # 成交订单数降序
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(1)
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
        # meynumber1 = '1,23'
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = ""
                for num in match2:
                    mey_number1 += num
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
                match = re.findall(r'\d+', meynumber2)
                mey_number2 = ""
                for num in match:
                    mey_number2 += num
                mey_number2 = int(mey_number2)

                assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_live_reverse_real(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[1]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        #print(num1)

        if num == 0:
            return
        elif num > 10:

                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

   # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_photo_desc_real(self):
        self.checkout_refund_new()
        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        # print(num1)

        if num == 0:
            return
        elif num > 2:
                # 成交订单数降序
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(1)
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
        # meynumber1 = '1,23'
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = ""
                for num in match2:
                    mey_number1 += num
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
                match = re.findall(r'\d+', meynumber2)
                mey_number2 = ""
                for num in match:
                    mey_number2 += num
                mey_number2 = int(mey_number2)

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_photo_reverse_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=2)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=4)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.click("(//span[contains(text(),'详情')])[4]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

        # 载体构成-售卖方式拆分-载体二级页

    @pytest.mark.p0
    def test_refund_compose_goods_desc_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=2)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=4)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.click("(//span[contains(text(),'详情')])[3]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 2:
                # 成交订单数降序
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(1)
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(2)

                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
                # meynumber1 = '1,23'
                match2 = re.search(r'(\d+)', meynumber1)
                mey_number1 = int(match2.group(1))

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
                match = re.search(r'(\d+)', meynumber2)
                mey_number2 = int(match.group(1))

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_goods_reverse_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=2)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=4)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.click("(//span[contains(text(),'详情')])[1]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_self_desc_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=2)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=3)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.click("(//span[contains(text(),'详情')])[1]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 2:
        # 成交订单数降序
                self.click("(//span[contains(text(),'成交订单数')])[1]")
                self.sleep(1)
                self.click("(//span[contains(text(),'成交订单数')])[1]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
                # meynumber1 = '1,23'
                match2 = re.search(r'(\d+)', meynumber1)
                mey_number1 = int(match2.group(1))

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
                match = re.search(r'(\d+)', meynumber2)
                mey_number2 = int(match.group(1))

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_self_reverse_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=2)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=2)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.click("(//span[contains(text(),'详情')])[1]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_cooperate_desc_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=1)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=4)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'达人合作退款')])")
        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 2:
                # 成交订单数降序
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(1)
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=3)).text
                # meynumber1 = '1,23'
                match2 = re.search(r'(\d+)', meynumber1)
                mey_number1 = int(match2.group(1))

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=3)).text
                match = re.search(r'(\d+)', meynumber2)
                mey_number2 = int(match.group(1))

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_cooperate_reverse_real(self):
        self.checkout_refund_new()
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=1)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=3)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'达人合作退款')])")
        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num == 0:
            return
        elif num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text

                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
                self.sleep(3)

                assert title1 != title2

    @pytest.mark.p0
    def test_refund_account_compose_desc_real(self):
        self.checkout_refund_new()

        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=1)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=2)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        # 成交订单数降序
        self.click("//span[contains(text(),'退款金额')]")
        self.sleep(1)
        self.click("//span[contains(text(),'退款金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2


    @pytest.mark.p0
    def test_refund_account_compose_reverse_real(self):
        self.checkout_refund_new()

        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=1)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=2)
        self.click(path2)
        self.sleep(3)
        self.click("(//span[contains(text(),'达人合作退款')])")
        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

            self.click("//li[contains(@title,'下一页')]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
            self.sleep(3)
            assert title1 != title2
        else:
            return

    # 自建商品
    # 核心指标
    @pytest.mark.p0
    def test_refund_core_overView_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        real_info1 = self.get_text(
            "//div[@class='Fr4Cneutq5ayWCYHx9OA']")
        self.assert_in("金额退款率（支付日）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)

    @pytest.mark.p0
    def test_refund_core_overView_self_list(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        real_info1 = self.get_text(
            "(//div[contains(@data-kael-component-style-scope,'comp_dORvPafeMJBubMLMvMZoD')])[1]")
        self.assert_in("退货退款", real_info1)
        self.assert_in("仅退款", real_info1)
        self.assert_in("发货前退款", real_info1)
        self.assert_in("发货后退款", real_info1)

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_live_desc_self(self):

        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)


        self.click("(//span[contains(text(),'详情')])[1]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_live_reverse_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[1]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text


            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_photo_desc_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[2]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_photo_reverse_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(3)
        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(1)
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            idx = 3
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=idx)).text
            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=idx)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

        # 载体构成-售卖方式拆分-载体二级页

    @pytest.mark.p0
    def test_refund_compose_goods_desc_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[3]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_goods_reverse_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[3]")

        title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

        if self.is_element_clickable("(//li[contains(@title,'下一页')])[2]"):
            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_self_desc_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[4]")
        # 成交订单数降序
        self.click(
            "(//span[contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[contains(text(),'成交订单数')])[1]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=4)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_self_reverse_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[4]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_cooperate_desc_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[5]")
        # 成交订单数降序
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(1)
        self.click(
            "//span[contains(text(),'成交订单数')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @skip
    @pytest.mark.p0
    def test_refund_compose_cooperate_reverse_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[5]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_account_compose_desc_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[5]")
        # 退款金额降序
        self.click("//span[contains(text(),'退款金额')]")
        self.sleep(1)
        self.click("//span[contains(text(),'退款金额')]")
        self.sleep(2)
        meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    @skip
    @pytest.mark.p0
    def test_refund_account_compose_reverse_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[5]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text

            self.click("//li[contains(@title,'下一页')]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=5)).text
            self.sleep(3)
            substr1 = title1[:8]
            substr2 = title2[:8]
            assert substr1 != substr2
        else:
            return

    # 自建商品-实时
    # 核心指标
    @pytest.mark.p0
    def test_refund_core_overView_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        real_info1 = self.get_text(
            "//div[@class='Fr4Cneutq5ayWCYHx9OA']")
        self.assert_in("金额退款率（支付日）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)

    # 载体构成-售卖方式拆分-载体二级页
    @skip
    @pytest.mark.p0
    def test_refund_compose_live_desc_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[1]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:

                # 成交订单数降序
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
                # meynumber1 = '1,23'
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = ""
                for num in match2:
                    mey_number1 += num
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=5)).text
                match = re.findall(r'\d+', meynumber2)
                mey_number2 = ""
                for num in match:
                    mey_number2 += num
                mey_number2 = int(mey_number2)

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_live_reverse_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[1]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=6)).text

                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list.format(idx=6)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_photo_desc_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        self.sleep(2)
        if num > 10:
                # 成交订单数降序
                self.click("(//span[contains(text(),'退款金额（支付日）')])[2]")
                self.sleep(1)
                self.click("(//span[contains(text(),'退款金额（支付日）')])[2]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list_compare.format(idx=5)).text
        # meynumber1 = '1,23'
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = match2[0]
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list_compare_second.format(idx=5)).text
                match = re.findall(r'\d+', meynumber2)
                mey_number2 = match[0]
                mey_number2 = int(mey_number2)

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_photo_reverse_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click("(//span[contains(text(),'自营退款')])")
        self.sleep(5)
        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
                idx = 6
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=idx)).text
                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list.format(idx=idx)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

        # 载体构成-售卖方式拆分-载体二级页

    @pytest.mark.p0
    def test_refund_compose_goods_desc_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[3]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            # 成交订单数降序
            self.click("(//*[contains(text(),'退款金额（支付日）')])[2]")
            self.sleep(1)
            self.click("(//*[contains(text(),'退款金额（支付日）')])[2]")
            self.sleep(2)
            meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = match2[0]
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=5)).text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = match[0]
            mey_number2 = int(mey_number2)

            assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_goods_reverse_real_self(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[3]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    @pytest.mark.p0
    def test_refund_compose_self_desc_self_real(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[1]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 2:

            # 成交订单数降序
            self.click("(//*[contains(text(),'退款金额（支付日）')])[2]")
            self.sleep(1)
            self.click("(//*[contains(text(),'退款金额（支付日）')])[2]")
            self.sleep(2)
            meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = match2[0]
            mey_number1 = int(mey_number1)


            self.sleep(1)
            meynumber2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=5)).text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = match[0]
            mey_number2 = int(mey_number2)

            assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_self_reverse_self_real(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[1]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text

                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list_second.format(idx=5)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    @skip
    @pytest.mark.p0
    def test_refund_compose_cooperate_desc_self_real(self):
        self.checkout_refund_new()
        self.click("//div[contains(text(),'实时')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 2:
        # 成交订单数降序
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(1)
                self.click("//span[contains(text(),'成交订单数')]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
                # meynumber1 = '1,23'
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = ""
                for num in match2:
                    mey_number1 += num
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=5)).text
                match = re.findall(r'\d+', meynumber2)
                mey_number2 = ""
                for num in match:
                    mey_number2 += num
                mey_number2 = int(mey_number2)

                assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_refund_compose_cooperate_reverse_self_real(self):
        self.checkout_refund_new()
        self.click("//div[contains(text(),'实时')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
                title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert len(substr1)>0 and len(substr2)>0
                # assert substr1 != substr2


    @pytest.mark.p0
    def test_refund_account_compose_desc_real_self(self):
        self.checkout_refund_new()
        self.click("//div[contains(text(),'实时')]")
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.click("(//span[contains(text(),'详情')])[3]")
        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 2:
                # 成交订单数降序
                self.click("//span[contains(text(),'退款金额')]")
                self.sleep(1)
                self.click("//span[contains(text(),'退款金额')]")
                self.sleep(2)
                meynumber1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = match2[0]
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
                match = re.findall(r'(\d+)', meynumber2)
                mey_number2 = match[0]
                mey_number2 = int(mey_number2)

                assert mey_number1 >= mey_number2

    @pytest.mark.p0
    def test_refund_account_compose_reverse_real_self(self):
        self.checkout_refund_new()
        self.click("//div[contains(text(),'实时')]")
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)

        self.click("(//span[contains(text(),'详情')])[4]")

        text = self.find_element(RefundPage.list_total_number).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))

        if num > 10:
            title1 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text

            self.click("//li[contains(@title,'下一页')]")
            self.sleep(3)
            title2 = self.find_element(RefundPage.refund_detail_list.format(idx=4)).text
            self.sleep(3)
            substr1 = title1[:8]
            substr2 = title2[:8]
            assert substr1 != substr2
        else:
            return

    # 他人商品
    # 核心指标

    @pytest.mark.p0
    def test_refund_core_overView_other(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'他人商品')]")
        self.sleep(5)

        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.sleep(2)
        real_info1 = self.get_text(
            "//div[@class='Fr4Cneutq5ayWCYHx9OA']")
        self.assert_in("金额退款率（支付日）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)

    # 他人商品-实时
    # 核心指标
    @pytest.mark.p0
    def test_refund_core_overView_real_other(self):
        self.checkout_refund_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'他人商品')]")
        self.click("//div[contains(text(),'实时')]")
        idx = str(random.choice([num for num in range(1, 4)]))
        xpathStr1 = RefundPage.sale_style
        path1 = xpathStr1.format(idx=idx)
        self.click(path1)
        xpathStr2 = RefundPage.sale_component
        path2 = xpathStr2.format(idx=idx)
        self.click(path2)
        self.sleep(2)
        real_info1 = self.get_text(
            "//div[@class='Fr4Cneutq5ayWCYHx9OA']")
        self.assert_in("金额退款率（支付日）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)