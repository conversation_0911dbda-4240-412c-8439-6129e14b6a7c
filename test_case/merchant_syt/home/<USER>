# -*- coding: utf-8 -*-
"""
@Time ： 2023/11/30 8:19 PM
@Auth ： zhanle
@File ：test_syt_shop.py
@IDE ：PyCharm
"""
import time

from ddt import ddt
from unittest import skip
import pytest
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
account_proxy_app = BaseHttpRequest(user_id="B_**********")



@ddt
@pytest.mark.skip
class TestSytTrade(BaseTestCase):


    def checkout_shop_module(self):
        self.login("SYT_DOMAIN", "supply_account")
    #    self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(3)
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        elif self.is_element_visible("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']"):
            self.sleep(3)
            self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")
        else:
            self.assert_element("//div[@class='AOSjK37cO3N6aGXAjqF4']")

    @pytest.mark.p0
    def test_shop_overview(self):
        self.checkout_shop_module()
        time.sleep(10)
        self.click('//span[text()="店铺经营"]')
        self.sleep(1)
        self.assert_text("店铺经营分析",'//*[@id="kwaishop-data-shop-management"]/div/div[2]/div[1]/div[1]')
        self.click("//span[contains(text(),'近30天')]")  #近30天
        self.sleep(1)
        self.assert_element("(//span[@class='kwaishop-data-shop-radio-button'])[1]")

        self.assert_text("进店渠道及转化","//span[contains(text(),'进店渠道及转化')]")
        self.assert_element("//div[@class='kwaishop-data-shop-table-content']//table")

        self.assert_text("店铺页面分析","//span[contains(text(),'店铺页面分析')]")
        self.sleep(2)
        self.click('//*[@id="kwaishop-data-shop-management"]/div/div[5]/section[2]/div[1]/div[1]/div[2]')
        self.sleep(2)
        self.assert_element("//div[@class='detail-container']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    @pytest.mark.p0
    def test_shop_good(self):
        self.checkout_shop_module()
        time.sleep(3)
        self.click('//span[text()="店铺货品"]')
        self.sleep(1)
        self.assert_text("店铺货品分析","//div[@class='module-left module-title']")
        self.click("//span[contains(text(),'近30天')]")
        self.sleep(1)
        self.assert_element("(//div[@class='PT1R5oYhWb0YQjgodAck'])[1]")

        #account_proxy_app.account_proxy_remove(**********, *********)
