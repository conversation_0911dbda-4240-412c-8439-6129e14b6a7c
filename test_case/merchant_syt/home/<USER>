import re
import time
from datetime import datetime, timedelta
from unittest import skip

import pytest
from ddt import ddt
from selenium.common import NoSuchElementException

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")


@ddt
class TestShangDaList(BaseTestCase):

    #展示商达榜单
    def activity_shangda_list_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()

        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)

        self.click("//span[contains(text(),'市场')]")
        self.sleep(1)

        self.click("//span[@class='linkMenu___DEWk7'][contains(text(),'商达榜单')]")
        self.sleep(2)

    def activity_shangda_list(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.open("https://syt.kwaixiaodian.com/zones/rankingList/shangdaRanking")
        self.sleep(5)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(5)




    # 商达总榜 - tab切换
    @pytest.mark.p1
    def test_tab_change_shangda_main(self):
        self.activity_shangda_list_old()
        time.sleep(1)
        self.click("//div[@class='kwaishop-tianhe-ranking-management-pc-tabs-tab']")
        time.sleep(2)
        self.assert_text("仅售卖他人","//div[3]//div[2]//div[1]//div[1]//div[1]//div[1]//div[2]")
        time.sleep(2)
        self.click("(//div[@class='kwaishop-tianhe-ranking-management-pc-tabs-tab'])[1]")
        time.sleep(2)
        self.assert_text("达人合作","//div[@class='kwaishop-tianhe-ranking-management-pc-pro-checkableTag kwaishop-tianhe-ranking-management-pc-pro-checkableTag__default kwaishop-tianhe-ranking-management-pc-pro-checkableTag__middle'][contains(text(),'达人合作')]")
    # 达人榜单 - tab切换
    @pytest.mark.p1
    def test_tab_change_shangda_promoter(self):
        self.activity_shangda_list()
        time.sleep(1)
        self.click("//div[@class='kwaishop-tianhe-ranking-management-pc-tabs-tab']")
        time.sleep(2)
        self.assert_text("仅售卖他人", "//div[3]//div[2]//div[1]//div[1]//div[1]//div[1]//div[2]")
        time.sleep(2)
        self.click("(//div[@class='kwaishop-tianhe-ranking-management-pc-tabs-tab'])[1]")
        time.sleep(2)
        self.assert_text("达人合作",
                         "//div[@class='kwaishop-tianhe-ranking-management-pc-pro-checkableTag kwaishop-tianhe-ranking-management-pc-pro-checkableTag__default kwaishop-tianhe-ranking-management-pc-pro-checkableTag__middle'][contains(text(),'达人合作')]")




    # # 是否品牌 - 知名品牌
    # @pytest.mark.p1
    # def test_shangda_data_screening_brand_widespread(self):
    #     self.activity_shangda_list()
    #     self.click("//div[contains(text(),'非知名品牌')]")
    #     time.sleep(1)
    #     text1 = self.find_element('//*[@id="root"]/div[1]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div/div[1]').text
    #     time.sleep(2)
    #     self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div[2]/div/div/div/div/div[3]')
    #     time.sleep(2)
    #     text2 = self.find_element('//*[@id="root"]/div[1]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div/div/div[1]').text
    #     time.sleep(1)
    #
    #     assert  text1 != text2

    # 是否品牌 - 非知名品牌
    @pytest.mark.p1
    def test_shangda_data_screening_brand_not_widespread(self):
        self.activity_shangda_list()
        #点击 知名品牌
        self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div/div[2]/div/div/div/div/div[2]')
        time.sleep(1)
        text1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        time.sleep(2)
        self.click('//*[@id="pro-tag-form-wrapper"]/div[2]/div/div[2]/div/div/div/div/div[3]')
        time.sleep(2)
        text2 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        time.sleep(1)
        # 知名品牌 应该和非知名品牌不一样
        assert text1 != text2

    # 售卖方式 - 达人合作
    @pytest.mark.p1
    def test_shangda_seller_method_promoter_cooperation(self):
        self.activity_shangda_list()
        self.click("//div[@class='kwaishop-tianhe-ranking-management-pc-pro-checkableTag kwaishop-tianhe-ranking-management-pc-pro-checkableTag__default kwaishop-tianhe-ranking-management-pc-pro-checkableTag__middle'][contains(text(),'达人合作')]")
        time.sleep(2)
        self.assert_element("//th[contains(text(),'合作达人数')]")
        #//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[3]/td[6]/div
        number1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[3]/td[6]/div').text
        #合作达人数
        num = (int)(number1)
        assert num > 0










    # 售卖渠道
    @pytest.mark.p1
    def test_shangda_seller_ways(self):
        self.activity_shangda_list()

        # 点击直播间tab
        self.click('//*[@id="pro-tag-form-wrapper"]/div[4]/div/div[2]/div/div/div/div/div[2]')
        time.sleep(6)
        title1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        assert title1 != '-'
        # 点击短视频tab
        self.click('//*[@id="pro-tag-form-wrapper"]/div[4]/div/div[2]/div/div/div/div/div[3]')
        time.sleep(6)
        title2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        assert title2 != '-'
        # 点击商品卡tab
        self.click('//*[@id="pro-tag-form-wrapper"]/div[4]/div/div[2]/div/div/div/div/div[4]')
        time.sleep(6)
        title3 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        assert title3 != '-'
        # substr1 = title1[:5]
        # substr2 = title2[:5]
        # substr3 = title3[:5]
        #
        # assert substr1 != substr2 and substr3 != substr2



    # 商品信息 - 二维码点击出现
    # @pytest.mark.p1
    # def test_shangda_message_click_erweima(self):
    #     self.activity_shangda_list()
    #     self.click("//tbody/tr[2]/td[2]/div[1]/div[1]/div[1]/div[2]//*[name()='svg']")
    #     time.sleep(1)

    # 商品信息
    @pytest.mark.p1
    def test_shangda_message_click_trend(self):
        self.activity_shangda_list()
       
        self.click("(//canvas)[1]")
        time.sleep(1)


    # 商品信息 - 切页功能正常
    @pytest.mark.p1
    def test_shangda_message_click_title(self):
        self.activity_shangda_list() # //*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]
        title1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text

        # 点击第3页
        self.click("//a[normalize-space()='3']")
        time.sleep(5)
        #第一列的商品名称
        title2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        time.sleep(1)
        self.click("//li[@title='下一页']//button[@type='button']")
        time.sleep(5)
        title3 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[2]/div/div/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div/div/div[1]/div[1]').text
        substr1 = title1[:8]
        substr2 = title2[:8]
        substr3 = title3[:8]
        assert substr1 != substr2 and substr3 != substr2


    # 今日tab切换
    @skip
    @pytest.mark.p1
    def test_shangda_timedata_today_tab_change(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.activity_shangda_list()
        time.sleep(1)
        # 点击昨天的tab
        self.click('//*[@id="f4d2d55e-9626-4409-a0bb-f77a9e5fb17c"]')
        time_yes = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")

        # 获取今天的日期
        today = datetime.now().date()

        # 获取昨天的日期
        yesterday = today - timedelta(days=1)
        assert yesterday == element_datetime.date()
        self.assert_element("//tbody/tr[2]/td[1]")

        time.sleep(1)
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_today = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        element_datetime2 = datetime.strptime(time_today, "%Y-%m-%d").date()

        time.sleep(1)
        assert element_datetime2 == today
        self.assert_element("//tbody/tr[2]/td[1]")

    # 昨日tab切换
    @skip
    @pytest.mark.p1
    def test_shangda_timedata_yesterday_tab_change(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.activity_shangda_list()
        time.sleep(1)
        # 点击昨天的tab
        self.click('//*[@id="f4d2d55e-9626-4409-a0bb-f77a9e5fb17c"]')
        time_yes = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")

        # 获取今天的日期
        today =  datetime.now().date()

        # 获取昨天的日期
        yesterday = today - timedelta(days=1)
        assert yesterday == element_datetime.date()
        self.assert_element("//tbody/tr[2]/td[1]")

        time.sleep(1)
        self.click("//div[@class='kpro-data-date-filter-new-daterange-btn-group']//div[1]")
        time_today = self.find_element("(//span[@class='kpro-data-date-filter-new-single-string'])[1]").text
        element_datetime2 = datetime.strptime(time_today, "%Y-%m-%d").date()

        time.sleep(1)
        assert element_datetime2 == today
        self.assert_element("//tbody/tr[2]/td[1]")

    # 收起全部筛选功能正常
    @pytest.mark.p1
    def test_shangda_rollback_all_selector(self):
        self.activity_shangda_list()
        self.assert_element("//label[contains(text(),'是否品牌')]")
        self.click("//span[@class='IcRUXEy9rbSQtu6Rxaaf']")
        time.sleep(1)
        self.assert_element("//div[@class='kwaishop-tianhe-ranking-management-pc-table-content']//table")







