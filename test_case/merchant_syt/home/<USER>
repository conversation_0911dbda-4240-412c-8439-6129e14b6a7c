import time

import pytest

from test_case.merchant_syt.base import BaseTestCase
from constant.domain import get_domain
from unittest import skip

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
#
#
account_proxy_app = BaseHttpRequest(user_id="B_**********")


@pytest.mark.skip
class TestSytTraffic(BaseTestCase):

    @pytest.mark.p0
    # 直播-切到直播间流量
    def checkout_live_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        #新手引导

        self.click("//button[@class='syt-main-btn USaUYuDSYVS6wzVynLTq']")

        self.sleep(1)
        if self.is_element_visible("//span[contains(text(),'直播间流量')]"):
            self.click("//span[contains(text(),'直播间流量')]")
        else :
            self.sleep(3)
            self.click("//span[contains(text(),'直播间流量')]")

  # 直播间流量 流量概览

    @pytest.mark.p0
    def checkout_live_module_nomock(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        # 新手引导
        self.tanchuan()

    @pytest.mark.p0
    # 直播间流量 流量概览
    def test_live_traffic_overView(self):
        self.checkout_live_module()
        name_list = ["直播间曝光次数", "直播间标准观看次数", "直播间涨粉数量", "直播间商品曝光次数", "直播间商品访问次数", "直播间商品下单量", "直播间支付订单量",
                     "直播间成交金额(元)", "同行同等级商家", "较对比日期", "超过"]
        tabs_context1 = self.get_text_content("//span[contains(text(),'直播间曝光次数')]")
        self.assert_in(tabs_context1, name_list)
        tabs_context2 = self.get_text_content("//span[contains(text(),'直播间标准观看次数')]")
        self.assert_in(tabs_context2, name_list)
        tabs_context3 = self.get_text_content("//span[contains(text(),'直播间涨粉数量')]")
        self.assert_in(tabs_context3, name_list)
        tabs_context4 = self.get_text_content("//span[contains(text(),'直播间商品曝光次数')]")
        self.assert_in(tabs_context4, name_list)
        tabs_context5 = self.get_text_content("//span[contains(text(),'直播间商品访问次数')]")
        self.assert_in(tabs_context5, name_list)
        tabs_context6 = self.get_text_content("//span[contains(text(),'直播间商品下单量')]")
        self.assert_in(tabs_context6, name_list)
        tabs_context7 = self.get_text_content("//span[contains(text(),'直播间支付订单量')]")
        self.assert_in(tabs_context7, name_list)
        tabs_context8 = self.get_text_content("(//span[contains(text(),'直播间成交金额(元)')])[1]")
        self.assert_in(tabs_context8, name_list)
        #for i in range(1, 9):
         #   self.click('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > div:nth-child(1) > div:nth-child(1) > div:nth-child(3) > section:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child('+str(i)+') > div:nth-child(1)')
        #    time.sleep(3)
        self.click("(//div[@class='kwaishop-data-flow-pc-dropdown-trigger kpro-data-date-filter-new-daterange-rangeitem'])[1]")
        self.click("(//div[@class='btn-item'])[1]")
        time.sleep(3)
        self.click("(//span[contains(text(),'自然日')])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'自然周')])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'自然月')])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'下载数据')])[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    # 直播间流量——流量渠道及其转化
    def test_live_traffic_flow_conversion_funnel(self):
        self.checkout_live_module()
        self.assert_text("流量渠道及转化","//span[contains(text(),'流量渠道及转化')]")
        self.assert_text("渠道名称","(//th[contains(text(),'渠道名称')])[1]")
        self.assert_text("曝光次数","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'曝光次数')])[1]")
        self.assert_text("标准观看次数","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'标准观看次数')])[1]")
        self.assert_text("直播间成交金额(元)","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'直播间成交金额(元)')])[1]")
        # self.assert_text("引导直播间外成交金额(元)","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'引导直播间外成交金额(元)')])[1]")
        # self.assert_text("千次观看成交金额","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'千次观看成交金额(元)')])[1]")
        # self.click("(//button[@aria-label='展开行'])[1]")
        self.assert_text("付费流量","//span[@class='JbZJTEaxEICOT9mJo2Nx'][contains(text(),'付费流量')]")

        self.click("//tr[@class='kwaishop-data-flow-pc-table-row kwaishop-data-flow-pc-table-row-level-0 sOEWiC1yHWghpc9mB1Ns']//button[@aria-label='展开行']")
        self.assert_text("磁力金牛PC端","//span[contains(text(),'磁力金牛PC端')]")
        self.assert_text("磁力金牛移动端","//span[contains(text(),'磁力金牛移动端')]")
        self.assert_text("粉条","//span[contains(text(),'粉条')]")

        self.assert_text("自然流量", "//span[contains(text(),'自然流量')]")
        self.click("//tr[@class='kwaishop-data-flow-pc-table-row kwaishop-data-flow-pc-table-row-level-0']//button[@aria-label='展开行']")
        self.assert_element("//span[contains(text(),'关注页')]")
        self.click("//div[@class='Ienk2a_1pOxEXsHUx7zg']//button[1]")
        self.sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        self.assert_text("曝光次数占比","(//span[contains(text(),'曝光次数占比')])[1]")
        self.assert_text("观看次数占比","(//span[contains(text(),'观看次数占比')])[1]")
        self.assert_text("成交订单量占比","(//span[contains(text(),'成交订单量占比')])[1]")
        self.assert_text("成交金额占比","(//span[contains(text(),'成交金额占比')])[1]")
        # self.assert_text("关注页流量转化率","(//div[@class='P1Dogg0NI0NR721bnqAe'])[2]")
        self.click("(//span[contains(text(),'下载数据')])[2]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 直播间流量——直播场次流量
    @pytest.mark.p0
    def test_live_traffic_single(self):
        self.checkout_live_module()
        self.assert_text("直播场次流量","(//span[contains(text(),'直播场次流量')])[1]")
        self.assert_text("直播信息","(//th[@class='kwaishop-data-flow-pc-table-cell kwaishop-data-flow-pc-table-cell-fix-left'])[1]")
        self.assert_text("流量来源","(//th[contains(text(),'流量来源')])[1]")
        self.assert_text("曝光次数","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'曝光次数')])[2]")
        self.assert_text("标准观看次数","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'标准观看次数')])[2]")
        self.assert_text("直播间成交金额(元)","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'直播间成交金额(元)')])[2]")
        #self.assert_text("引导直播间外成交金额(元)","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'引导直播间外成交金额(元)')])[2]")
        self.assert_text("千次观看成交金额(元)","(//span[@class='kwaishop-data-flow-pc-table-column-title-no-align'][contains(text(),'千次观看成交金额(元)')])[2]")
        self.click("(//span[contains(text(),'指标配置')])[2]") #指标配置
        self.click("(//span[contains(text(),'确 定')])[1]")
        self.click("(//span[contains(text(),'下载数据')])[3]")
        self.sleep(5)
        self.click("(//span[contains(text(),'直播详情')])[1]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//span[contains(text(),'生意通直播作战系统')]")
        self.switch_to_window(0)

        #self.assert_text("关注页","(//td[contains(text(),'关注页')])[1]")
        #self.assert_text("精选页","(//td[contains(text(),'精选页')])[1]")
        #self.assert_text("发现页","(//td[contains(text(),'发现页')])[1]")
        #self.assert_text("短视频引流","(//td[contains(text(),'短视频引流')])[1]")
        #self.assert_text("搜索","(//td[contains(text(),'搜索')])[1]")
        #self.assert_text("商城推荐","(//td[contains(text(),'商城推荐')])[1]")
        #self.assert_text("小店首页推荐","(//td[contains(text(),'小店首页推荐')])[1]")
        #self.assert_text("个人页","(//td[contains(text(),'个人页')])[1]")
        #self.assert_text("店铺页","(//td[contains(text(),'店铺页')])[1]")
        #self.assert_text("其他","(//td[contains(text(),'其他')])[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 短视频流量——流量概览
    @pytest.mark.p0
    def test_video_traffic_overviwe(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'短视频流量')]")
        self.assert_text("曝光次数","//div[@id='photoShowCnt']")
        self.click("//span[@aria-label='system-calendar-line']")
        self.click("//span[contains(text(),'近30天')]")
        self.click('//*[@id="root"]/div/div/div[1]/div/div/div[4]/div/div/div/div[2]/div')
        self.assert_element('//*[@id="root"]/div/div/div[2]/div[3]/div/div')
        self.click("(//span[contains(text(),'下载数据')])[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 短视频流量-流量转化漏斗
    @pytest.mark.p0
    @pytest.mark.skip
    def test_video_traffic_flow_conversion_funnel(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'短视频流量')]")
        self.sleep(1)
        self.assert_text("流量转化漏斗",'//*[@id="scroll-content"]/div[4]/section[1]/section[1]')
        tabs_name_list = ["关注页","发现页","个人页","同城页","买家首页","其他"]
        #for i in range(1,7):
        #    tabs_context = self.get_text_content('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(4) > section:nth-child(2) > div:nth-child(1) > section:nth-child(1) > div:nth-child(1) > label:nth-child(' + str(i) + ') > span:nth-child(2)')
        self.click("//span[contains(text(),'关注页')]")
        self.sleep(1)
        self.click("//span[contains(text(),'发现页')]")
        self.sleep(1)
        self.click("//span[contains(text(),'个人页')]")
        self.sleep(1)
        self.click("//span[contains(text(),'同城页')]")
        self.sleep(1)
        self.click("//span[contains(text(),'买家首页')]")
        self.sleep(1)
        # self.click("//span[contains(text(),'其他')]")
        self.sleep(1)
        #self.assert_in(tabs_context,tabs_name_list)
        # self.click("(//*[name()='path'])[28]")
        self.assert_element('//*[@id="scroll-content"]/div[4]/section[2]/div/section[2]/div[1]')
        self.assert_element('//*[@id="scroll-content"]/div[4]/section[2]/div/section[2]/div[2]/section/div')
        self.click("(//span[@class='oXbJhgXAF8LHkBXxLeXz'])[2]")
        self.assert_downloaded_file("流量转化漏斗.xls")
        self.delete_downloaded_file("流量转化漏斗.xls")
        #account_proxy_app.account_proxy_remove(**********, **********)


    # 短视频流量-流量来源明细
    @pytest.mark.p0
    def test_video_traffic_traffic_source_details(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'短视频流量')]")
        time.sleep(2)
        self.assert_text("流量来源明细","//span[@customstyle='[object Object]'][contains(text(),'流量来源明细')]")
        self.assert_text("流量页面来源","//th[contains(text(),'流量页面来源')]")
        self.assert_text("短视频曝光次数","//th[contains(text(),'短视频曝光次数')]")
        self.assert_text("短视频有效播放次数","//th[contains(text(),'短视频有效播放次数')]")
        self.assert_text("短视频涨粉人数","//th[contains(text(),'短视频涨粉人数')]")
        self.assert_text("短视频引导直播间次数","//th[contains(text(),'短视频引导直播间次数')]")
        #for i in range(2, 8):
        #    list_text = self.get_text_content('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(5) > section:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(2) > table:nth-child(1) > tbody:nth-child(2) > tr:nth-child('+str(i)+') > td:nth-child(1)')
        #    self.assert_in(list_text, tabs_context_list)
        #    time.sleep(5)

        self.click('//*[@id="root"]/div/div/div[4]/div[1]/div/button')
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 短视频流量-流量来源明细
    @pytest.mark.p0
    def test_video_traffic_paid_promotion(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'短视频流量')]")
        self.assert_text("磁力金牛-作品推广","//div[contains(text(),'磁力金牛-作品推广')]")
        self.assert_text("粉条-作品推广","//div[contains(text(),'粉条-作品推广')]")
        self.assert_text("粉条-智能推广","//div[contains(text(),'粉条-智能推广')]")
        self.assert_text("短视频观看次数","//th[contains(text(),'短视频观看次数')]")
        self.assert_text("短视频涨粉人数","(//th[@class='ant-table-cell'][contains(text(),'短视频涨粉人数')])[2]")
        self.assert_text("短视频商品成交订单数","(//th[@class='ant-table-cell'][contains(text(),'短视频商品成交订单数')])[2]")
        self.assert_text("短视频成交金额","(//th[@class='ant-table-cell'][contains(text(),'短视频成交金额')])[2]")
        self.click('//*[@id="root"]/div/div/div[5]/div[1]/div/button')
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    def test_short_video_traffic(self):
        self.checkout_live_module()
        self.click("//span[contains(text(),'短视频流量')]")
        self.assert_text("短视频作品流量","//span[contains(text(),'短视频作品流量')]")
        self.assert_text("短视频信息","//th[contains(text(),'短视频信息')]")
        self.assert_text("流量来源","//th[contains(text(),'流量来源')]")
        self.assert_text("短视频曝光次数","//span[contains(text(),'短视频曝光次数')]")
        self.assert_text("短视频有效播放次数","//span[contains(text(),'短视频有效播放次数')]")
        self.assert_text("短视频涨粉人数","//span[contains(text(),'短视频涨粉人数')]")
        self.assert_text("短视频引导直播间次数","(//span[@class='ant-table-column-title-no-align'][contains(text(),'短视频引导直播间次数')])[1]")
        if self.is_element_visible('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(7) > section:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > table:nth-child(1) > tbody:nth-child(3) > tr:nth-child(2) > td:nth-child(1) > button:nth-child(2)'):
            self.click('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(7) > section:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > table:nth-child(1) > tbody:nth-child(3) > tr:nth-child(2) > td:nth-child(1) > button:nth-child(2)')
            self.assert_text("关注页","(//td[@class='ant-table-cell ant-table-cell-fix-left ant-table-cell-fix-left-last'][contains(text(),'关注页')])[2]")
            self.assert_text("发现页","//tr[@class='ant-table-row ant-table-row-level-1']//td[@class='ant-table-cell ant-table-cell-fix-left ant-table-cell-fix-left-last'][contains(text(),'发现页')]")
            self.assert_text("个人页","(//td[@class='ant-table-cell ant-table-cell-fix-left ant-table-cell-fix-left-last'][contains(text(),'个人页')])[2]")
            self.assert_text("同城页","(//td[@class='ant-table-cell ant-table-cell-fix-left ant-table-cell-fix-left-last'][contains(text(),'同城页')])[2]")
            self.assert_text("买家首页","(//td[@class='ant-table-cell ant-table-cell-fix-left ant-table-cell-fix-left-last'][contains(text(),'买家首页')])[2]")
            self.assert_text("其他","(//td[@class='ant-table-cell ant-table-cell-fix-left ant-table-cell-fix-left-last'][contains(text(),'其他')])[2]")
            self.click("//button[@aria-label='关闭行']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 商品流量-流量概览
    @pytest.mark.p0
    def test_goods_flow_flow_overview(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'商品流量')]")
        self.assert_text("流量概览","//span[contains(text(),'流量概览')]")
        tabs_list = ["商品曝光次数","商品点击次数","商品下单数","商品成交订单数","商品成交金额"]
        self.assert_text("商品曝光次数",'//*[@id="scroll-content"]/div[3]/section[2]/div/div/div/section/div[1]/div/div[1]')
        self.assert_text("超过","(//span[contains(text(),'超过')])[1]")
        self.assert_text("同行同等级商家","(//span[contains(text(),'同行同等级商家')])[1]")
        self.assert_text("较对比日期",'//*[@id="scroll-content"]/div[3]/section[2]/div/div/div/section/div[1]/div/div[4]')
        #for i in range(1,6):
        #    self.click('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(3) > section:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > div:nth-child('+str(i)+')')
        self.click("(//div[@class='btn-item selected'])[1]")
        self.click("(//div[@class='btn-item'])[1]")
        self.click("//div[@class='ant-dropdown-trigger btn-item'][contains(text(),'日')]")
        # self.click("//div[normalize-space()='16']")
        self.click("//div[@class='ant-dropdown-trigger btn-item'][contains(text(),'周')]")
        # self.click("//tr[@class='ant-picker-week-panel-row']//td[@title='2023-11-17']")
        self.click("//div[@class='ant-dropdown-trigger btn-item'][contains(text(),'月')]")
        # self.click("//div[contains(text(),'11月')]")
        self.click("(//span[@class='oXbJhgXAF8LHkBXxLeXz'])[1]")
        self.assert_element_visible("//div[@class='ant-spin-container']//div//div//canvas")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 商品流量-流量渠道来源
    @pytest.mark.p0
    def test_goods_flow_source_of_traffic_channels(self):
        self.checkout_live_module()
        self.sleep(3)
        self.click("//span[@class='syt-main-badge'][contains(text(),'商品流量')]")
        self.sleep(3)
        self.assert_element_visible('//*[@id="root"]/div/div/div[5]/div[2]')
        self.click('//*[@id="root"]/div/div/div[4]/div[1]/div/button')
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 商品流量-流量渠道来源明细
    @pytest.mark.p0
    def test_goods_flow_details_of_traffic_channel_sources(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'商品流量')]")
        self.assert_text("流量渠道来源明细","//span[contains(text(),'流量渠道来源明细')]")
        self.assert_text("商品流量渠道来源","//th[contains(text(),'商品流量渠道来源')]")
        self.assert_text("商品曝光次数","//th[contains(text(),'商品曝光次数')]")
        self.assert_text("商品点击次数","//th[contains(text(),'商品点击次数')]")
        self.assert_text("商品下单数","//th[contains(text(),'商品下单数')]")
        self.assert_text("商品成交订单数","//th[contains(text(),'商品成交订单数')]")
        self.assert_text("商品成交金额","//th[contains(text(),'商品成交金额')]")
        self.click("//th[contains(text(),'商品曝光次数')]//span[@aria-label='question-circle']//*[name()='svg']")
        self.click('//*[@id="scroll-content"]/div[3]/section[1]/section[2]/span')
        #account_proxy_app.account_proxy_remove(**********, **********)


    # 商品流量-商品流量明细
    @pytest.mark.p0
    @skip
    def test_goods_flow_product_traffic_details(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'商品流量')]")
        self.assert_text("商品名称","//th[contains(text(),'商品名称')]")
        self.assert_text("商品类型","//th[contains(text(),'商品类型')]")
        self.assert_text("流量来源","//th[contains(text(),'流量来源')]")
        self.assert_text("商品曝光次数","//span[contains(text(),'商品曝光次数')]")
        self.assert_text("商品点击次数","//span[contains(text(),'商品点击次数')]")
        self.assert_text("商品下单数","//span[contains(text(),'商品下单数')]")
        self.assert_text("商品成交订单数","//span[contains(text(),'商品成交订单数')]")
        self.assert_text("商品成交金额","(//th[@class='ant-table-cell'][contains(text(),'商品成交金额')])[2]")
        if self.is_element_visible("(//button[@aria-label='展开行'])[1]"):
            self.click("(//button[@aria-label='展开行'])[1]")
            self.sleep(3)
            self.assert_text("短视频","//td[contains(text(),'短视频')]")
            self.assert_text("个人页","//td[contains(text(),'个人页')]")
            self.assert_text("其他","//td[contains(text(),'其他')]")
            self.click("//button[@aria-label='关闭行']")
            self.sleep(3)
            self.click("//tbody/tr[2]/td[10]/div[1]/a[1]")
            self.sleep(3)
            self.switch_to_window(0)
            self.click("(//span[contains(text(),'人群分析')])[1]")
            self.is_element_visible("(//canvas)[5]")
            self.is_element_visible("(//canvas)[6]")
            self.is_element_visible("(//canvas)[7]")
            self.is_element_visible("(//div[@class='v1cDsHK6zYbMGNHgyTyi'])[2]")
            self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")
        self.click("//span[contains(text(),'自建商品')]")
        self.sleep(5)
        self.assert_element_visible("(//a[@target='_blank'][contains(text(),'商品详情')])[1]")
        self.sleep(2)
        self.click("(//span[contains(text(),'分销商品')])[1]")
        self.sleep(3)
        self.assert_element_visible("(//a[@target='_blank'][contains(text(),'商品详情')])[1]")
        self.click("//span[contains(text(),'三方商品')]")
        self.assert_element_visible("//span[contains(text(),'商品曝光次数')]//span[@aria-label='question-circle']//*[name()='svg']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    @pytest.mark.p0
    def test_goods_flow_product_traffic_detail(self):
        self.checkout_live_module_nomock()
        self.click("//span[@class='syt-main-badge'][contains(text(),'商品流量')]")
        self.assert_text("商品名称", "//th[contains(text(),'商品名称')]")
        self.assert_text("流量渠道来源",'//*[@id="scroll-content"]/div[4]/section[1]/section[1]')
        self.assert_text("流量渠道来源明细",'//*[@id="scroll-content"]/div[5]/section[1]/section[1]')

    # 奖励流量-直播间流量-流量概览
    @pytest.mark.p0
    def test_award_flow_live_flow_live_overview(self):
        self.checkout_live_module()
        # 点击弹窗
        if self.is_element_visible("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]"):
            self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        self.click("//span[@class='syt-main-badge'][contains(text(),'奖励流量')]")
        self.click("(//div[@id='rc-tabs-0-tab-studio'])[1]")
        self.assert_text("流量概览","//span[contains(text(),'流量概览')]")
        name_list = ["直播间曝光次数","直播间观看次数","直播间涨粉数量","直播间商品支付量","直播间成交支付金额(元)"]
        self.assert_text("直播间曝光次数",'//*[@id="scroll-content"]/div[3]/section[2]/div/div/div/section/div[1]')
        #self.assert_text("较对比日期",'body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(3) > section:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div:nth-child(3) > span:nth-child(1)')
        self.click("//div[3]//section[1]//section[2]//span[1]//span[1]//*[name()='svg']")
        self.click("(//div[@class='btn-item selected'])[1]")
        self.click("(//div[@class='btn-item'])[1]")
        # self.click("//div[@class='ant-dropdown-trigger btn-item'][contains(text(),'日')]")
        # self.click("//div[normalize-space()='16']")
        self.click("//div[@class='kpro-data-date-filter-btn-group small']//div[3]")
        # self.click("//div[normalize-space()='16']")
        self.click("(//div[@class='ant-dropdown-trigger btn-item'][contains(text(),'月')])[1]")
        # self.click("//div[contains(text(),'11月')]")
        self.assert_element_visible("//div[@class='ant-spin-container']//div//div//canvas")
        #account_proxy_app.account_proxy_remove(**********, **********)
    @skip
    # 奖励流量-直播间流量-奖励流量分类
    @pytest.mark.p0
    def test_award_flow_live_flow_live_category(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'奖励流量')]")
        self.assert_text("奖励流量分类","//span[contains(text(),'奖励流量分类')]")
        self.assert_text("奖励流量类型","//th[contains(text(),'奖励流量类型')]")
        self.assert_text("流量占比","//span[contains(text(),'流量占比')]")
        self.assert_text("直播间曝光次数","//span[contains(text(),'直播间曝光次数')]")
        self.assert_text("直播间观看次数","//span[contains(text(),'直播间观看次数')]")
        self.assert_text("直播间涨粉人数","//span[contains(text(),'直播间涨粉人数')]")
        self.assert_text("直播间商品成交订单数","//span[contains(text(),'直播间商品成交订单数')]")
        self.sleep(1)
        self.assert_text("直播间成交金额","//span[contains(text(),'直播间成交金额')]")
        self.click("//span[contains(text(),'奖励流量能为我们带来什么？如何获得奖励流量？点击获取攻略')]")
        self.click("//div[4]//section[1]//section[2]//span[1]//span[1]//*[name()='svg']")
        self.assert_text("奖励流量诊断","(//div[@class='F40fyJVORuTCWqDsooav'])[1]")
        self.assert_element_visible("//div[@class='cgzMCxHUpawwinmomEID']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 奖励流量-直播间流量-直播场次奖励明细
    @pytest.mark.p0
    def test_award_flow_live_flow_live_streaming_sessions_reward_details(self):
        self.checkout_live_module()
        # 点击弹窗
        #self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        self.click("//span[@class='syt-main-badge'][contains(text(),'奖励流量')]")
        self.click("(//div[@id='rc-tabs-0-tab-studio'])[1]")
        self.assert_text("直播场次奖励明细","//span[contains(text(),'直播场次奖励明细')]")
        self.assert_text("直播信息","//th[contains(text(),'直播信息')]")
        self.assert_text("奖励流量分类","//th[contains(text(),'奖励流量分类')]")
        if self.is_element_visible("(//button[@aria-label='展开行'])[1]"):
            self.click("(//button[@aria-label='展开行'])[1]")
            self.click("//button[@aria-label='关闭行']")
            self.click("(//span[contains(text(),'直播详情')])[1]")
            self.switch_to_window(1)
            self.sleep(2)
            self.assert_text("生意通直播作战系统","//div[@class='I1MfuZFEZr33y1gMmLxU']")
            self.switch_to_window(0)
            self.click("(//span[contains(text(),'流量诊断')])[1]")
            self.assert_element_visible("(//div[@class='i_Gqhubi7zKE9fnGvGp5'])[4]")
            self.assert_element_visible("(//div[@class='i_Gqhubi7zKE9fnGvGp5'])[5]")
            self.assert_element_visible("(//div[@class='i_Gqhubi7zKE9fnGvGp5'])[6]")
            self.click("//span[@aria-label='system-close-medium-line']//*[name()='svg']")
        # self.click("//div[5]//section[1]//section[2]//span[1]//span[1]//*[name()='svg']")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 奖励流量-短视频流量-流量概览
    @pytest.mark.p0
    def test_award_flow_short_flow_live_overview(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'奖励流量')]")
        self.click('//*[@id="scroll-content"]/div[2]/div[2]/div[1]/div/div[1]/div[1]/div/div[2]')
        self.assert_text("流量概览","//span[contains(text(),'流量概览')]")
        self.assert_text("短视频曝光次数","(//div[@id='kpro-data-datacard-showCnt-title'])[1]")
        self.click("//div[@class='btn-item selected']")
        self.click("//div[@class='btn-item']")
        self.click("//div[@class='kpro-data-date-filter-btn-group small']//div[1]")
        account_proxy_app.account_proxy_remove(**********, **********)
        # self.click("//div[normalize-space()='16']")
        # self.click('body > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > section:nth-child(1) > section:nth-child(2) > main:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(9) > section:nth-child(1) > section:nth-child(1) > main:nth-child(1) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div:nth-child(4)')
        # self.click("(//div[@class='ant-picker-cell-inner'][normalize-space()='16'])[2]")
        # self.click("//div[@class='ant-dropdown-trigger btn-item'][contains(text(),'月')]")
        # self.click("//td[@title='2023-11']//div[1]")

    # 奖励流量-短视频流量-奖励流量分类
    @pytest.mark.p0
    def test_award_flow_short_flow_live_category(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'奖励流量')]")
        self.click('//*[@id="scroll-content"]/div[2]/div[2]/div[1]/div/div[1]/div[1]/div/div[2]')
        #self.click('//*[@id="scroll-content"]/div[4]/section[1]/section[2]/span')
        #self.assert_downloaded_file("奖励流量分类.xls")
        #self.delete_downloaded_file("奖励流量分类.xls")
        self.assert_text("奖励流量分类",'//*[@id="scroll-content"]/div[4]/section[1]/section[1]')
        self.click('//*[@id="scroll-content"]/div[4]/section[1]/section[2]/div')
        self.switch_to_window(0)
        self.assert_text("奖励流量类型","//th[contains(text(),'奖励流量类型')]")
        #account_proxy_app.account_proxy_remove(**********, **********)

    # 奖励流量-短视频流量-短视频奖励明细
    @pytest.mark.p0
    def test_award_flow_short_flow_live_short_award_details(self):
        self.checkout_live_module()
        self.click("//span[@class='syt-main-badge'][contains(text(),'奖励流量')]")
        self.click('//*[@id="scroll-content"]/div[2]/div[2]/div[1]/div/div[1]/div[1]/div/div[2]')

        self.assert_text("短视频奖励明细","//span[contains(text(),'短视频奖励明细')]")
        self.assert_text("短视频信息","(//th[contains(text(),'短视频信息')])[1]")
        #account_proxy_app.account_proxy_remove(**********, **********)



