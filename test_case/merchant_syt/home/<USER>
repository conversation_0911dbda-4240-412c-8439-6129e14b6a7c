"""
# Time       ：2022/6/27 9:49 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
from unittest import skip
import pytest
from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase

@ddt
class TestSytAfterSale(BaseTestCase):

    # 直播-切到售后模块
    def checkout_as_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
        # 点击服务 tab
        self.driver.maximize_window()
        self.refresh()
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()

        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)
        self.refresh()
        self.click("//span[contains(text(),'服务')]")

        self.sleep(2)
        self.click("//span[contains(text(),'售后总览')]")
        self.sleep(2)

    def checkout_as_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        # 点击服务 tab
        self.driver.maximize_window()
        self.open("https://syt.kwaixiaodian.com/zone/service/afterSales")
        self.sleep(5)

    @pytest.mark.p1
    def test_aftersale_overview(self):
        self.checkout_as_module_old()
        # 点击弹窗
        #self.click("(//img[@class='zqkOZIyCpVY51qdNuCM1'])[1]")
        # 时间选择
        self.click("//div[contains(text(),'近1天')]")
        self.assert_text("近14日售后差评率", "//span[contains(text(),'近14日售后差评率')]")
        self.assert_text("退款金额(退款日)", "//span[contains(text(),'退款金额(退款日)')]")
        self.assert_text("退款订单数(退款日)", "//span[contains(text(),'退款订单数(退款日)')]")
        self.assert_text("仅退款平均完结时长", "//span[contains(text(),'仅退款平均完结时长')]")
        self.assert_text("退货退款平均完结时长", "//span[contains(text(),'退货退款平均完结时长')]")
        self.assert_text("纠纷介入率", "//span[contains(text(),'纠纷介入率')]")

    @pytest.mark.p1
    def test_aftersale_index(self):
        self.checkout_as_module()
        # 售后考核指标
        # tabs_text = self.get_text_content(
        #     '#module-content>div>div>div:nth-child(2)>div.ant-row')
        # name_list = ["纠纷介入单量", "纠纷介入率", "仅退款自主完结时长", "退货退款自主完结时长", "商责纠纷率"]
        #
        # for item in name_list:
        #     self.assert_in(item, tabs_text)
        self.click("//span[contains(text(),'客服概览')]")
        self.click("//div[contains(text(),'近1天')]")

        content_text = self.get_text('//div[@class="slick-track"]')
        name_list = ["人工会话量", "3分钟回复率",  "平均响应时长(秒)","不服务率","im不满意率","询单转化金额"]
        for item in name_list:
            self.assert_in(item, content_text)

    @pytest.mark.skip
    @pytest.mark.p1
    def test_aftersale_comment(self):
        self.checkout_as_module()
        self.click("//span[contains(text(),'售后评价')]")
        self.sleep(2)
        self.assert_element("(//span[contains(text(),'售后评价量')])[1]",by="xpath")
        self.assert_element("(//span[contains(text(),'售后满意度')])[1]")
        self.find_element("(//input[@placeholder='请选择日期'])[3]",by="xpath").click()
        self.sleep(1)
        self.assert_element("//a[contains(text(),'今天')]", by="xpath")

    @pytest.mark.p1
    def test_aftersale_retriveTop10(self):
        self.checkout_as_module()
        self.sleep(2)
        self.click("//span[contains(text(),'仅退款')]")
        self.assert_element("//span[contains(text(),'退款金额(退款日)')]")

        self.click("//span[contains(text(),'退货退款')]")
        self.assert_element("//span[contains(text(),'退款人数(退款日)')]")

        self.click("//span[contains(text(),'发货前退款')]")
        self.assert_element("//span[contains(text(),'退款订单数(退款日)')]")

        self.click("//span[contains(text(),'发货后退款')]")
        self.assert_element("//span[contains(text(),'退款原因')]")












