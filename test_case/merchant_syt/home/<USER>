"""
# Time       ：2022/6/26 9:03 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
用例不完整 先给你注销了
"""
import random
from unittest import skip

from ddt import ddt

from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
import pytest
import time
from datetime import datetime, timedelta
import re


account_proxy_app = BaseHttpRequest(user_id="B_**********")


# @ddt
class TestSytShelf(BaseTestCase):

    # 商城
    def checkout_shelf_module_old(self):
        self.login("SYT_DOMAIN", "supply_account")
    #   self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.refresh()
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)

        self.click("(//span[contains(text(),'商城')])[1]")
        self.sleep(5)

    def checkout_shelf_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()

    # 代理账号
        account_proxy_app.account_proxy_confirm(**********,*********)
        self.open("https://syt.kwaixiaodian.com/zones/goodsCardManagement/mallOperation")
        self.sleep(5)

    # 昨日tab切换
    @pytest.mark.p1
    def test_timedata_yesterday_tab_change(self):
        cur_hour = int(time.strftime('%H', time.localtime(time.time())))
        if cur_hour < 12:
            return
        self.checkout_shelf_module()
        time.sleep(1)
        # 点击昨天的tab
        self.click("//div[contains(text(),'近1日')]")
        time_yes = self.find_element('//*[@id="root"]/div/div/div[2]/div[1]/div/div[1]/span[4]').text
        # 将文本日期转换为 datetime 对象
        element_datetime = datetime.strptime(time_yes, "%Y-%m-%d")

        # 获取今天的日期
        today = datetime.now().date()

        # 获取昨天的日期
        yesterday = today - timedelta(days=1)
        assert yesterday == element_datetime.date()
        self.assert_element("//div[@class='Xdz1SKZMNl0Eh9dzfjMi']")

        #account_proxy_app.account_proxy_remove(**********, *********)

    #核心数据-分渠道
    def test_shelf_channel_overview(self):
        self.checkout_shelf_module()
        real_info1 = self.get_text("//div[@class='Xdz1SKZMNl0Eh9dzfjMi']")
        self.assert_in("商城整体", real_info1)
        self.assert_in("商城推荐", real_info1)
        self.assert_in("搜索", real_info1)
        self.assert_in("店铺页", real_info1)
        self.assert_in("频道活动页", real_info1)
        self.assert_in("购后页面", real_info1)
        self.assert_in("浏览记录", real_info1)
        self.assert_in("收藏列表", real_info1)
        self.assert_in("其他", real_info1)
        #account_proxy_app.account_proxy_remove(**********, *********)

    #核心数据
    def test_shelf_overview(self):
        self.checkout_shelf_module()
        real_info1 = self.get_text("//div[@class='kpro-data-tabsdatacard-new']")
        self.assert_in("成交金额", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("曝光点击率", real_info1)
        self.assert_in("点击成交率", real_info1)
        self.assert_in("退款金额", real_info1)
        #account_proxy_app.account_proxy_remove(**********, *********)

    #核心数据-趋势图校验
    def test_shelf_trend(self):
        self.checkout_shelf_module()
        self.assert_element("//div[@customstyle='[object Object]']//div//div//div//canvas")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量来源-售卖方式切换
    def test_tradetype_change(self):
        self.checkout_shelf_module()
        self.click("//span[contains(text(),'自营')]")
        self.assert_element("//th[contains(text(),'渠道名称')]")
        self.sleep(1)
        self.click("(//span[contains(text(),'达人合作')])")
        self.sleep(1)
        self.assert_element("//th[contains(text(),'达人信息')]")
        #account_proxy_app.account_proxy_remove(**********, *********)


    #流量来源-售卖载体切换
    def test_carriertype_change_item(self):
        self.checkout_shelf_module()
        self.click("(//span[contains(text(),'商品卡')])[2]")
        self.sleep(2)
        self.assert_element("//th[contains(text(),'渠道名称')]")
        elements = self.find_elements(
            'td.kwaishop-tianhe-goodsCardManagement-pc-table-cell.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left-last.kwaishop-tianhe-goodsCardManagement-pc-table-cell-with-append')
        self.assert_true(len(elements) == 8)
        self.assert_true(all(item.text != "" for item in elements) == True)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 流量来源-售卖载体切换
    def test_carriertype_change_live(self):
        self.checkout_shelf_module()

        self.click("(//span[contains(text(),'直播间')])[3]")
        self.sleep(2)
        self.assert_element("//th[contains(text(),'渠道名称')]")
        elements = self.find_elements('td.kwaishop-tianhe-goodsCardManagement-pc-table-cell.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left-last.kwaishop-tianhe-goodsCardManagement-pc-table-cell-with-append')
        self.assert_true(len(elements) == 7)
        self.assert_true(all(item.text != "" for item in elements) == True)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 流量来源-售卖载体切换
    def test_carriertype_change_photo(self):
        self.checkout_shelf_module()

        self.click("(//span[contains(text(),'短视频')])[3]")
        self.sleep(2)
        self.assert_element("//th[contains(text(),'渠道名称')]")
        elements = self.find_elements(
            'td.kwaishop-tianhe-goodsCardManagement-pc-table-cell.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left-last.kwaishop-tianhe-goodsCardManagement-pc-table-cell-with-append')
        self.assert_true(len(elements) == 7)
        self.assert_true(all(item.text != "" for item in elements) == True)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 流量来源-售卖载体切换
    @pytest.mark.skip
    def test_carriertype_change_other(self):
        self.checkout_shelf_module()

        self.click("(//span[contains(text(),'其他')])")
        self.sleep(2)
        self.assert_element("//th[contains(text(),'渠道名称')]")
        self.sleep(2)
        elements = self.find_elements("//tbody[contains(@class,'kwaishop-tianhe-goodsCardManagement-pc-table-tbody')]")
        self.assert_true(len(elements) == 7)
        self.assert_true(all(item.text != "" for item in elements) == True)
        #account_proxy_app.account_proxy_remove(**********, *********)

    # 流量来源-售卖载体切换
    def test_carriertype_change_all(self):
        self.checkout_shelf_module()

        self.click("(//span[contains(text(),'全部')])[2]")
        self.sleep(2)
        self.assert_element("//th[contains(text(),'渠道名称')]")
        elements = self.find_elements(
            'td.kwaishop-tianhe-goodsCardManagement-pc-table-cell.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left.kwaishop-tianhe-goodsCardManagement-pc-table-cell-fix-left-last.kwaishop-tianhe-goodsCardManagement-pc-table-cell-with-append')
        self.assert_true(len(elements) == 8)  #
        self.assert_true(all(item.text != "" for item in elements) == True)
        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-指标
    @pytest.mark.p0
    def test_source_detail(self):
        self.checkout_shelf_module()
        self.sleep(3)
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-goodsCardManagement-pc-table-header']//thead[@class='kwaishop-tianhe-goodsCardManagement-pc-table-thead']")
        self.assert_in("渠道名称", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("商品曝光点击率", real_info1)
        #self.assert_text("商品成交点击率", "//span[contains(text(),'商品成交点击率')]")
        #self.assert_text("退款金额", "//span[contains(text(),'退款金额')]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-指标降序
    @pytest.mark.p0
    def test_source_rank_amt(self):
        self.checkout_shelf_module()
        # 成交金额降序
        self.click("(//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")

        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[4]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[4]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2
        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-店铺页详情
    @pytest.mark.p0
    def test_source_shop_detail(self):
        self.checkout_shelf_module()
        self.click("//div[contains(text(),'店铺页')]")
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  #查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.assert_text("达人店铺页数据","//div[@class='kwaishop-tianhe-goodsCardManagement-pc-drawer-title']")

        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-店铺页详情-指标
    @pytest.mark.p0
    def test_source_shop_detail_overview(self):
        self.checkout_shelf_module()
        self.click("//div[contains(text(),'店铺页')]")
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  #查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.sleep(3)
            self.assert_text("达人店铺页数据","//div[@class='kwaishop-tianhe-goodsCardManagement-pc-drawer-title']")
            real_info1 = self.get_text(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead')
            self.assert_in("达人信息", real_info1)
            self.assert_in("成交金额", real_info1)
            self.assert_in("成交订单数", real_info1)
            self.assert_in("商品曝光次数", real_info1)
            self.assert_in("商品点击次数", real_info1)
            self.assert_in("商品曝光点击率", real_info1)
            self.assert_in("商品点击成交率", real_info1)
            self.assert_in("退款金额", real_info1)

        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-店铺页详情-指标排序
    @pytest.mark.p0
    def test_source_shop_detail_amt(self):
        self.checkout_shelf_module()
        self.click("//div[contains(text(),'店铺页')]")
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  #查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.sleep(3)
            self.assert_text("达人店铺页数据","//div[@class='kwaishop-tianhe-goodsCardManagement-pc-drawer-title']")
            # 成交金额降序
            self.click("//div[@id='rank-list']//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title-no-align'][contains(text(),'成交金额')]")
            self.sleep(1)
            self.click("//div[@id='rank-list']//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title-no-align'][contains(text(),'成交金额')]")
            self.sleep(2)
            meynumber1 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div/div[1]').text
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div/div[1]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2

        #account_proxy_app.account_proxy_remove(**********, *********)

    #流量明细-店铺页详情-翻页
    @pytest.mark.p0
    def test_source_shop_detail_pagereverse(self):
        self.checkout_shelf_module()
        self.click("//div[contains(text(),'店铺页')]")
        if self.is_element_visible("//div[@class='Qa2dNtjeY7RAO_Enqw7y']"):  #查看更多
            self.click("//div[@class='Qa2dNtjeY7RAO_Enqw7y']")
            self.sleep(3)
            self.assert_text("达人店铺页数据","//div[@class='kwaishop-tianhe-goodsCardManagement-pc-drawer-title']")
            title1 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[1]/div[2]').text
            self.sleep(3)
            self.click(
                "//ul[@class='kwaishop-tianhe-goodsCardManagement-pc-pagination']//li[@title='下一页']//button[@type='button']")

            self.sleep(2)
            title2 = self.find_element(
                '//*[@id="rank-list"]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[1]/div[2]').text
            self.sleep(2)
            substr1 = title1[:5]
            substr2 = title2[:5]
            assert substr1 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)


    #商品列表-tab切换
    @pytest.mark.p0
    def test_itemlist_source_detail_tabchange(self):
        self.checkout_shelf_module()
        self.click('//*[@id="root"]/div[1]/div/div[5]/div[2]/div/div/div/div['+ str(random.randint(2,9)) +']/div/span[1]')
        self.sleep(3)
        self.assert_text("商品信息","//th[contains(text(),'商品信息')]")
        self.assert_element("//tbody")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品列表-指标降序
    @pytest.mark.p0
    def test_itemlist_rank_amt(self):
        self.checkout_shelf_module()
        # 成交金额降序
        self.click("//div[@class='kwaishop-tianhe-goodsCardManagement-pc-table-content']//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title-no-align'][contains(text(),'成交金额')]")
        self.sleep(1)
        self.click(
            "//div[@class='kwaishop-tianhe-goodsCardManagement-pc-table-content']//span[@class='kwaishop-tianhe-goodsCardManagement-pc-table-column-title-no-align'][contains(text(),'成交金额')]")

        self.sleep(2)
        meynumber1 = self.find_element(
            "//tbody[contains(@class,'kwaishop-tianhe-goodsCardManagement-pc-table-tbody')]/tr[2]/td[2]/div/div[1]").text
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            "//tbody[contains(@class,'kwaishop-tianhe-goodsCardManagement-pc-table-tbody')]/tr[3]/td[2]/div/div[1]").text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品列表-指标
    @pytest.mark.p0
    def test_itemlist_detail(self):
        self.checkout_shelf_module()
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-goodsCardManagement-pc-table-content']//thead[@class='kwaishop-tianhe-goodsCardManagement-pc-table-thead']")
        self.assert_in("商品信息", real_info1)
        self.assert_in("成交金额", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("商品曝光次数", real_info1)
        self.assert_in("商品点击次数", real_info1)
        self.assert_in("商品曝光点击率", real_info1)
        self.assert_in("商品点击成交率", real_info1)
        self.assert_in("退款金额", real_info1)
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品列表-翻页
    @pytest.mark.p0
    def test_itemlist_page_reverse(self):
        self.checkout_shelf_module()
        title1 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[5]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div/div/span[1]').text

        self.click("//a[normalize-space()='2']")
        self.sleep(1)
        title2 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[5]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div/div/span[1]').text
        self.sleep(1)
        self.click("//span[@aria-label='system-arrow-large-right-line']")
        self.sleep(1)
        title3 = self.find_element(
            '//*[@id="root"]/div[1]/div/div[5]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div/div/span[1]').text
        self.sleep(2)
        substr1 = title1[:20]
        substr2 = title2[:20]
        substr3 = title3[:20]
        assert substr1 != substr2 and substr3 != substr2
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品列表-点击出现商品详情页
    @pytest.mark.p0
    def test_itemlist_shop_detail(self):
        self.checkout_shelf_module()
        self.click('//*[@id="root"]/div/div/div[5]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div[1]')
        self.assert_element("//div[@class='EUnoMK92PHNn9yfhCBuE react-draggable']")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #商品列表-下载
    @pytest.mark.p0
    def test_itemlist_download(self):
        self.checkout_shelf_module()
        self.click("(//span[contains(text(),'下载数据')])[2]")
        #account_proxy_app.account_proxy_remove(**********, *********)

    #查看商城运营攻略
    @pytest.mark.p0
    def test_shelf_jump(self):
        self.checkout_shelf_module()
        self.click("//span[contains(text(),'查看商城运营攻略')]")
        self.switch_to_window(1)
        self.sleep(3)
        self.assert_element("//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'【生意通】商城经营入门宝典')]")
        #account_proxy_app.account_proxy_remove(**********, *********)




