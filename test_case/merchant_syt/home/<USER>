
import time
from datetime import datetime
from unittest import skip

import pytest
from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")

@skip
class TestSytLogisticAccount(BaseTestCase):
    def test_logistic_account(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        self.click("//span[@class='syt-main-dropdown-trigger w20sdnf3cIh6Yo7rYRwE']")
        self.sleep(2)
        self.click("//div[@class='vYykATY5stpT6CFOOYDF drop_common_item_special']//span[contains(text(),'物流履约')]")
        self.sleep(2)
        self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[4]/section[2]/div/div[1]/div[3]/span')
        #self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[4]/section[2]/div/div[4]/div/div/div[1]/div[1]/button/span/svg')
        self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[4]/section[2]/div/div[1]/div[7]/span')
        self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[4]/section[2]/div/div[1]/div[11]/span')

    def test_logistic_account1(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        self.refresh()
        self.click("//span[@class='syt-main-dropdown-trigger w20sdnf3cIh6Yo7rYRwE']")
        self.sleep(2)
        self.click("//div[@class='vYykATY5stpT6CFOOYDF drop_common_item_special']//span[contains(text(),'物流履约')]")
        self.sleep(2)
        self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[5]/section[1]/section[1]/span')
        self.assert_element('//*[@id="kpro-datacard-new-fake_send_rate-title"]/div/span')
        self.assert_element('//*[@id="kpro-datacard-new-send_err_recv_address_diff_cnt-title"]/div/span')
        #self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[5]/section[2]/div/div[4]/div/div/div[1]/div[1]/span/text()[1]')
        self.assert_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[5]/section[2]/div/div[1]/div/div/div/button/span')
        self.find_element('//*[@id="kwaishop-data-micro-service-container"]/div/div[5]/section[2]/div/div[1]/div/div/div/button/span').click()


        #self.find_element("(//span[@class='aoCXTxbbFdEV8wFggG23'])[1]",by="xpath").click()
       # self.assert_element("//span[contains(text(),'发货环节')]",by="xpath")

