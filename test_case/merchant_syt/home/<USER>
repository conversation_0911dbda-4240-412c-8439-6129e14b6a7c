
import time
from datetime import datetime
from unittest import skip

import pytest
from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest

account_proxy_app = BaseHttpRequest(user_id="B_**********")
"""
服务-品质概览-供货
服务-品质概览-带货

"""
# 服务-品质概览-供货
@skip
class TestSytServiceTakeGood(BaseTestCase):
    def test_service_take_good(self):
        self.login("SYT_DOMAIN", "supply_account")
        # self.click('/html/body/div[3]/div/div[2]/div/div[2]/div[3]/div/div/div[2]/button[1]')
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        # 大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)
        self.sleep(1)
        self.click("//span[@class='syt-main-dropdown-trigger w20sdnf3cIh6Yo7rYRwE']")  #更多
        self.sleep(1)
        self.click("(//span[contains(text(),'品质概览')])[2]")
        self.sleep(1)
        self.assert_element("(//span[contains(text(),'商品品质分析')])[1]",by="xpath")
        self.assert_element("//span[contains(text(),'商品类目品质指标')]",by="xpath")
        #self.assert_element("(//span[contains(text(),'品质指标')])[1]",by="xpath")
        self.assert_element("(//span[contains(text(),'快分销入驻指标')])[1]",by="xpath")
        self.assert_element("(//span[contains(text(),'带货视角')])[1]",by="xpath")
        self.find_element("(//a[normalize-space()='2'])[1]",by="xpath").click()
        self.assert_element("(//span[contains(text(),'详情')])[1]",by="xpath")


        self.find_element("//span[@title='全部']",by="xpath").click()
        #self.assert_element("//span[@title='他销']",by="xpath")
        #self.assert_element("//span[contains(text(),'详情')])[1]",by="xpath")
        self.find_element("(//span[contains(text(),'带货视角')])[1]",by="xpath").click()
        self.assert_element("(//span[contains(text(),'商品品质分析')])[1]",by="xpath")
        self.assert_element("(//th[contains(text(),'商家名称')])[1]",by="xpath")
        self.assert_element("(//span[contains(text(),'类目品质分析')])[1]",by="xpath")

        # //span[@title='他销']
        self.sleep(1)
