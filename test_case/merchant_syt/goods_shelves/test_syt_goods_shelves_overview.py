import random
from xml.etree.ElementPath import xpath_tokenizer
from utils.merchantDataUtils import *
from test_case.merchant_syt.base import BaseTestCase
from utils.http_help import BaseHttpRequest
from page_objects.merchant_data.syt_homepage import *
from page_objects.merchant_data.syt_goods_shelves_page import *
account_proxy_app = BaseHttpRequest(user_id="B_**********")
class TestSytGoodsShelvesOverview(BaseTestCase):
    # 交易 - 成交分析
    def checkout_goods_shelves(self):
        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        self.sleep(3)
        # 点击交易tab
        self.click(HomePage.goods_shelves_tab)
        self.sleep(3)
        # 首页弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(3)
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(8)



    def test_goods_shelves_core_data(self):
        """核心总览"""
        self.checkout_goods_shelves()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(GoodsShelvesPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        #随机选择自建or他人or全部
        btn = random.choice([GoodsShelvesPage.all_goods, GoodsShelvesPage.self_goods, GoodsShelvesPage.others_goods])
        self.click(btn)
        self.sleep(2)
        #查看核心指标数据是否存在
        info_list = self.get_text(GoodsShelvesPage.goods_indicator_data)
        self.assert_in("成交金额", info_list)
        self.assert_in("成交人数", info_list)
        self.assert_in("成交订单数", info_list)
        self.assert_in("退款金额(退款日)", info_list)


    def test_goods_selves_core_data_trend(self):
        """核心总览趋势图"""
        self.checkout_goods_shelves()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(GoodsShelvesPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        #随机选择自建or他人or全部
        btn = random.choice([GoodsShelvesPage.all_goods, GoodsShelvesPage.self_goods, GoodsShelvesPage.others_goods])
        self.click(btn)
        #随机点击一个指标
        indicator_list = ['成交金额','成交人数','成交订单数']
        indicator_title = random.choice(indicator_list)
        self.click(GoodsShelvesPage.indicator_btn.format(title=indicator_title))
        self.sleep(3)
        #查看趋势图是否存在
        self.assert_element(GoodsShelvesPage.trend_chart)



    def test_goods_shelves_chanel_data_sort(self):
        """渠道数据明细排序"""
        self.checkout_goods_shelves()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(GoodsShelvesPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        #随机选择自建or他人or全部
        btn = random.choice([GoodsShelvesPage.chanel_self_goods, GoodsShelvesPage.chanel_other_goods, GoodsShelvesPage.chanel_all_goods])
        self.click(btn)
        self.sleep(2)
        #点击成交人数 进行升序排序
        self.click(GoodsShelvesPage.transaction_customer_header)
        self.sleep(2)
        row1_data = convert_to_float(self.get_text(GoodsShelvesPage.transaction_customer_data.format(idx=2)))
        row2_data = convert_to_float(self.get_text(GoodsShelvesPage.transaction_customer_data.format(idx=3)))
        assert row1_data <= row2_data
        #点击成交人数 进行降序排序
        self.click(GoodsShelvesPage.transaction_customer_header)
        self.sleep(2)
        row1_data = convert_to_float(self.get_text(GoodsShelvesPage.transaction_customer_data.format(idx=2)))
        row2_data = convert_to_float(self.get_text(GoodsShelvesPage.transaction_customer_data.format(idx=3)))
        assert row1_data >= row2_data



    def test_goods_shelves_chanel_data_detail(self):
        """渠道数据明细"""
        self.checkout_goods_shelves()
        # 随机时间范围 近1日 近7日 近30日
        time_frame = ['近1天', '近7天', '近30天']
        date_text = random.choice(time_frame)
        self.click(GoodsShelvesPage.time_frame.format(time_frame=date_text))
        self.sleep(3)
        #随机选择自建or他人or全部
        btn = random.choice([GoodsShelvesPage.chanel_self_goods, GoodsShelvesPage.chanel_other_goods, GoodsShelvesPage.chanel_all_goods])
        self.click(btn)
        # 随机获取1到9之间的数
        idx = random.randint(1, 9)
        #点击该渠道商品表现
        self.click(GoodsShelvesPage.chanel_goods_performance.format(idx=idx))
        self.sleep(2)
        #获取某一行的成交金额 和后一行的成交金额 前一行要大于后一行
        row1_data = convert_to_float(
            self.get_text(GoodsShelvesPage.chanel_goods_performance_amt_data.format(idx=idx))
            .replace('¥', '')
        )
        row2_data = convert_to_float(
            self.get_text(GoodsShelvesPage.chanel_goods_performance_amt_data.format(idx=idx+1))
            .replace('¥', '')
        )
        assert row1_data >= row2_data

        #任意点击一个去看详情 跳转到商品详情页
        self.click(GoodsShelvesPage.best_selling_go_to_detail.format(idx=idx))
        self.sleep(2)
        self.assert_element(GoodsShelvesPage.goods_info)

    def test_goods_shelves_best_selling_rank(self):
        """热卖商品top10"""
        self.checkout_goods_shelves()
        # 随机获取1到9之间的数
        idx = random.randint(1, 9)
        #获取某一行的成交金额 和后一行的成交金额 前一行要大于后一行
        row1_data = convert_to_float(
            self.get_text(GoodsShelvesPage.best_selling_amount.format(idx=idx))
            .replace('¥', '')
        )
        row2_data = convert_to_float(
            self.get_text(GoodsShelvesPage.best_selling_amount.format(idx=idx + 1))
            .replace('¥', '')
        )
        assert row1_data >= row2_data
        #点击去看详情
        self.click(GoodsShelvesPage.best_selling_go_to_detail.format(idx=idx))
        self.sleep(3)
        #查看商品详情页是否存在
        self.assert_element(GoodsShelvesPage.goods_info)






