"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from sbase.steps import open_url
from selenium.webdriver.common.by import By

from utils.account_help import get_account_detail
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

aigc_url1 = 'https://s.kwaixiaodian.com/zone/shortVideo/aigc?itemId=**************&sliceId=************'
aigc_url = 'https://eshop-s.prt.kwaixiaodian.com/zone/aigc/ai_creation?itemId=**************&sliceId=************'
aigc_url_no_item = 'https://eshop-s.prt.kwaixiaodian.com/zone/aigc/ai_creation'

@ddt
class LoadShortVideoAigc(BaseTestCase):

    # 浏览器最大化，进入aigc
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_BP_DOMAIN", "short_video")
        self.open_url(aigc_url)
        self.skip_guider()

    # 跳过新手引导
    def skip_guider(self):
        # 刷新当前页面
        # self.refresh_page()
        # 【多退货地址策略】功能指引
        sleep(2)
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # 规则管理功能指引
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # sleep(3)
        # # 点击自动发布提示气泡 知道了
        # self.click('//*[@id="root"]/div[2]/div/div/div/div[2]/div[2]/div/button/span')

    # 校验aigc页面 查看教程
    def check_aigc_guide(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()


        self.assert_text('查看教程', "(//span[contains(text(),'查看教程')])[1]")
        self.click("(//span[contains(text(),'查看教程')])[1]")

        url = self.get_current_url()
        assert url == "https://docs.qingque.cn/d/home/<USER>"

    # 校验aigc页面 视频信息
    def check_aigc_video(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()

        self.assert_text('视频素材', "//span[contains(text(),'视频素材')]")
        self.assert_text("批量发短视频")
        self.assert_text('商品讲解')
        self.hover_on_element("(//*[name()='svg'][@clip-rule='evenodd'])[5]")
        self.assert_text("本地上传","(//div[contains(text(),'本地上传')])[1]")
        self.assert_text("素材中心导入", "(//div[contains(text(),'素材中心导入')])[1]")

        #老版页面
        # self.assert_text('上传视频', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div[1]/label/span')
        # self.assert_text('直播切片', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div[2]/div/div/div/div/div[1]/div/span')
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div[2]/div/div/div/div/div[1]/div/span')
        # time.sleep(1)
        # self.assert_text('预览视频', '//*[@id="rcDialogTitle0"]')

    # 校验aigc页面 未关联商品
    def check_aigc_no_ass_item(self):
        self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()


        self.click("(//span[contains(text(),'添加关联商品')])[1]")

        self.assert_text("商品信息","(//th[contains(text(),'商品信息')])[1]")
        self.assert_text("库存","(//th[contains(text(),'库存')])[1]")
        self.assert_text("价格（元）", "(//th[contains(text(),'价格（元）')])[1]")
        self.assert_("(//span[@class='kwaishop-tianhe-aigc-pc-typography kwaishop-tianhe-aigc-pc-typography-ellipsis kwaishop-tianhe-aigc-pc-typography-single-line kwaishop-tianhe-aigc-pc-typography-ellipsis-single-line WERcYj1IWf5d3lZsnYzY kwaishop-tianhe-aigc-pc-tooltip-open'])[1]")
        #老页面
        # self.assert_text('添加关联商品', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div[2]/div/div/div/div/button/span')
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div[2]/div/div/div/div/button/span')
        # self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')
        # self.assert_text('重 置', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div/div[1]/button/span')
        # self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div/div[2]/button/span')
        # # time.sleep(100)
        # self.assert_text('取 消')
        # self.assert_text('确 定')
        # sleep(5)
        # self.click("//span[contains(text(),'确 定')]")
        # # self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
        # time.sleep(1)
        # self.assert_text('请选择关联商品', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div[2]/div[2]/div')

    # 校验aigc页面 已关联商品
    def check_aigc_ass_item(self):
        # self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        # time.sleep(100)
        self.assert_text("关联商品","(//span[contains(text(),'关联商品')])[1]")
        self.assert_text("素言测试哈哈哈商品勿拍不发货18")
        self.assert_text("ID：**************")
        self.assert_text("总库存：300")
        self.assert_text("商品原价")


        # self.assert_text("素言测试哈哈哈商品勿拍不发货18", "(//div[@class='OARaJvB7rfe3Xj8meUvG'])[1]")

    # 校验aigc页面 智能推荐卖点
    def check_aigc_reco_seller_points(self):
        # self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()

        self.assert_text("商品卖点","(//span[contains(text(),'商品卖点')])[1]")
        self.assert_text("素言","(//span[contains(text(),'素言')])[1]")
        self.assert_text("18元发货","(//span[contains(text(),'18元发货')])[1]")
        self.assert_text("拍纸箱发货","(//span[contains(text(),'拍纸箱发货')])[1]")

        self.click("(//span[contains(text(),'18元发货')])[1]")
        self.assert_text("18元发货","(//div)[143]")




        #老页面
        # self.assert_text('18元发货', "(//div[@title='18元发货'])[1]")
        # self.assert_text('换一批', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[1]/div[2]/div/div[4]/div')
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[1]/div[2]/div/div[4]/div')

    # 校验aigc页面 手动输入卖点
    def check_aigc_seller_points_input(self):
        # self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.input("(//textarea[@id='itemHotKeyword'])[1]","UI自动化测试输入")
        self.assert_text("UI自动化测试输入")

        #老页面
        # self.input('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[2]/textarea', 'UI自动化测试')

    def check_aigc_video_ersonalization(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_text("个性化设置","(//span[contains(text(),'个性化设置')])[1]")
        self.assert_text("增加个性化设置，提升视频质量","(//span[contains(text(),'增加个性化设置，提升视频质量')])[1]")
        self.assert_text("音乐选择","(//span[contains(text(),'音乐选择')])[1]")
        self.assert_text("默认使用视频原声，可添加背景音乐，增加作品吸引力")
        self.assert_text("添加音乐","(//span[contains(text(),'添加音乐')])[1]")
        self.assert_text("字幕样式","(//span[contains(text(),'字幕样式')])[1]")
        self.assert_text("智能推荐","(//span[@title='智能推荐'])[1]")
        self.assert_text("视频配音","(//span[contains(text(),'视频配音')])[1]")
        self.assert_text("璀璨女声","(//span[@title='璀璨女声'])[1]")

    def check_aigc_video_music_list(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.click("(//span[contains(text(),'添加音乐')])[1]")
        self.assert_text("音乐选择")
        self.assert_("(//input[@placeholder='搜索音乐'])[1]")
        self.assert_("(//img[@class='kwaishop-tianhe-aigc-pc-image-img kpro-common-ai-creation-audio-list-list-item-header-left-music-image'])[1]")
        self.hover_on_element("(//img[@class='kwaishop-tianhe-aigc-pc-image-img kpro-common-ai-creation-audio-list-list-item-header-left-music-image'])[1]")
        self.assert_text("添加")

    def check_aigc_video_subtitle_stylet(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.click("(//span[@title='智能推荐'])[1]")
        self.assert_text("智能推荐")
        self.assert_text("无字幕")
        self.assert_text("微软雅黑")
        self.assert_text("阿里巴巴灰体")
        self.assert_text("猫啃珠圆体")

    def check_aigc_video_dubbin(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.click("(//span[@title='璀璨女声'])[1]")
        self.assert_text("璀璨女声")
        self.assert_text("温柔学姐")
        self.assert_text("活力学妹")
        self.assert_text("清朗男声")
        self.assert_text("成熟男声")
        self.assert_text("阳光少年")
        self.assert_("(//*[name()='svg'][@clip-rule='evenodd'])[9]")

    def check_aigc_video_modify_cover(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_text("封面预览","(//div[contains(text(),'封面预览')])[1]")
        self.click("(//div[contains(text(),'封面预览')])[1]")
        self.assert_text("修改封面","(//span[contains(text(),'修改封面')])[1]")
        self.click("(//span[contains(text(),'修改封面')])[1]")
        self.assert_text("推荐封面")
        self.assert_text("截取封面")
        self.assert_text("添加文本")
        self.assert_text("效果预览")
        self.assert_("(//span[contains(text(),'取 消')])[1]")
        self.assert_("(//span[contains(text(),'确 定')])[1]")
        #点击气泡
        self.click("(//img[@class='sl-thumbnail'])[3]")

        self.assert_text("思源黑体-粗体",timeout=60)
        #断言是否添加气泡
        self.assert_("(//div[@class='kwaishop-tianhe-aigc-pc-modal-wrap'])[1]")
        #断言视频截取
        self.click("//div[@class='kwaishop-tianhe-aigc-pc-tabs-tab']//div[@id='rc-tabs-1-tab-2']")
        sleep(10)
        self.assert_("(//div[@class='sl-background-mask'])[1]")
        self.assert_text("添加文本")
        self.assert_text("效果预览")
        self.assert_( "(//span[contains(text(),'取 消')])[1]")
        self.assert_( "(//span[contains(text(),'确 定')])[1]")























    # 校验aigc页面 生成视频
    def check_aigc_video_create(self):
        # self.open_url(aigc_url_no_item)
        # time.sleep(1)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()

        self.assert_text("暂无视频","(//span[contains(text(),'暂无视频')])[1]")
        self.assert_text("填写信息后智能生成","(//span[contains(text(),'填写信息后智能生成')])[1]")
        self.assert_text("生成视频","//span[contains(text(),'生成视频')]")
        self.click("(//span[contains(text(),'18元发货')])[1]")
        self.click("//span[contains(text(),'生成视频')]")
        self.assert_text("视频生成需要一些时间，您可以在本页等待查看预览，也可以离开本页，生成后会自动同步到待发布列表。","(//div[@class='kwaishop-tianhe-aigc-pc-modal-confirm-content'])[1]")
        self.assert_text("知道了","(//span[contains(text(),'知道了')])[1]")
        self.click("(//span[contains(text(),'知道了')])[1]")
        self.assert_text("视频生成中，请耐心等待...","//span[contains(text(),'视频生成中，请耐心等待...')]")
        self.assert_text("视频生成成功！","(//div[@class='kwaishop-tianhe-aigc-pc-modal-confirm-content'])[1]",timeout=300)
        self.assert_text("知道了","(//span[contains(text(),'知道了')])[1]")
        self.click("(//span[contains(text(),'知道了')])[1]")
        self.assert_("(//video)[1]")
        self.assert_text("重新生成","(//span[contains(text(),'重新生成')])[1]")

        #老页面
        # self.assert_text('暂无视频\n填写信息后智能生成', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div/div/div/div/div/div[1]/span')
        # self.input('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[2]/textarea', 'UI自动化测试')
        # time.sleep(0.5)
        # self.assert_text('生成视频', '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/button[2]/span')
        # # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/button[2]/span')
        # time.sleep(1)
        # # 切片发布次数每日有上限，触发上限时直接返回
        # if self.is_element_present("span.kwaishop-seller-short-video-b-pc-modal-confirm-title"):
        #     return
        # else:
        #     self.assert_text('知道了', '/html/body/div[2]/div/div[2]/div/div[2]/div/div/div[2]/button/span')
        #     self.click('/html/body/div[2]/div/div[2]/div/div[2]/div/div/div[2]/button/span')
        #     time.sleep(0.5)
        #     self.assert_text('视频生成中，请耐心等待\n生成后会自动同步到待发布列表',  '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div/div/div/div/div/div[1]/span')
        #     time.sleep(0.5)
        #     self.assert_text('根据商品和视频内容生成的文案，生成后可自行修改', "//div[@class='Bhj6DttyCnTSo5bhIV0w']")
        #     self.assert_text('一键发布', '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/button/span')



    def check_aigc_video_regenerat(self):
        self.click("(//span[contains(text(),'重新生成')])[1]")
        self.assert_text("视频生成需要一些时间，您可以在本页等待查看预览，也可以离开本页，生成后会自动同步到待发布列表。",timeout=10)
        self.assert_text("知道了")
        self.assert_text("视频生成中，请耐心等待...", "//span[contains(text(),'视频生成中，请耐心等待...')]")
        self.assert_text("视频生成成功！",timeout=300)
        self.assert_text("知道了", "(//span[contains(text(),'知道了')])[1]")
        self.click("(//span[contains(text(),'知道了')])[1]")
        self.assert_("(//video)[1]")
        self.assert_text("重新生成", "(//span[contains(text(),'重新生成')])[1]")


    def check_check_aigc_video_oneclick_publish(self):
        self.click("(//span[contains(text(),'一键发布')])[1]")
        self.assert_text("发布中，约30秒后可在直播切片页面查看","(//span[contains(text(),'发布中，约30秒后可在直播切片页面查看')])[1]")
        sleep(300)
        self.click("(//span[contains(text(),'查看已生成作品')])[1]")
        sleep(4)
        self.click("(//div[contains(text(),'已发布')])[1]")
        self.assert_text("商品ID:**************","(// span[contains(text(), '商品ID:**************')])[1]")






    def judge_text(self, text):
        time.sleep(2)
        if text in self.get_page_source():
            return True
        return False

    # 判断选品弹窗的商品有没有被选中
    def isCheckBox(self, xPath):
        isCheckBox = self.get_element(xPath)
        # 如果复选框没有被选中，就点击复选框
        if isCheckBox != 'ant-checkbox ant-checkbox-checked':
            self.click(xPath)
