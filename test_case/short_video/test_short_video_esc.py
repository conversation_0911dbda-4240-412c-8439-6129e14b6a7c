"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from .load_helper_assistant_preLive import LoadHelperAssistantPreLive

# m_load_pre_live_v2: LoadHelperAssistantPreLive = LoadHelperAssistantPreLive()
from .load_short_video_esc import LoadShortVideoEsc


@ddt
class TestShortVideoEsc(LoadShortVideoEsc):

    # 进入短视频页面
    @pytest.mark.skip
    def test_checkout_new_guide(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()


    # 进入短视频切片页面
    @pytest.mark.skip
    def test_checkout_esc_title(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_title()

    # 校验已发布列表去推广按钮
    @pytest.mark.skip
    def test_checkout_esc_promote(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_promote()

    # 校验智能托管
    @pytest.mark.skip
    def test_auto_publish(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()
        sleep(2)
        self.refresh_page()
        self.check_auto_publish()

   # 校验精细剪辑
    @pytest.mark.skip
    def test_esc_cut(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_cut()

    # 校验查看教程
    @pytest.mark.skip
    def test_esc_guide(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_guide()

    # 校验切片数据
    # def test_esc_data(self):
    #     # 浏览器最大化
    #     self.maximize_window_and_login()
    #     sleep(1)
    #     # 跳过新手引导
    #     self.skip_guider()
    #
    #     self.check_esc_data()

    # 校验切片列表
    @pytest.mark.skip
    def test_esc_list(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_list()

    # 校验封面修改弹窗
    #@pytest.mark.skip
    @pytest.mark.skip
    def test_esc_cover_popup(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_cover_popup()

     # 校验修改商品抽屉
    @pytest.mark.skip
    def test_esc_ass_item(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_ass_item()

     # 校验切片剪辑
    @pytest.mark.skip
    def test_esc_self_cut(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_self_cut()

     # 校验视频描述
    @pytest.mark.skip
    def test_esc_describe(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_esc_describe()

    # 校验aigc跳转
    # @pytest.mark.skip
    # def test_esc_aigc_goto(self):
    #     # 浏览器最大化
    #     self.maximize_window_and_login()
    #     sleep(1)
    #     # 跳过新手引导
    #     self.skip_guider()
    #     self.check_aigc_goto()

    # def test_lm(self):
    #     # 浏览器最大化
    #     self.maximize_window_and_login()
    #     # sleep(1)
    #     # # 跳过新手引导
    #     # self.skip_guider()
    #     self.update_text("(//input[@placeholder='开始日期'])[1]","哈哈哈")
    #     sleep(10)






