"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from utils.account_help import get_account_detail
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

@ddt
class LoadShortVideoVisual(BaseTestCase):

    # 浏览器最大化，进入图文列表页
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_VISUAL_DOMAIN", "short_video")

    # 跳过新手引导
    def skip_guider(self):
        # 刷新当前页面
        # self.refresh_page()
        # # 刷新当前页面
        # sleep(2)
        # self.refresh_page()
        # 【多退货地址策略】功能指引
        # sleep(2)
        # # 点击素材中心我知道了
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # #规则管理功能指引
        # # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        sleep(2)
        # # 点击自动发布提示气泡 知道了
        # self.click('//*[@id="live-explanation-slice-container"]/div/div[1]/div[2]/div[2]/div[3]/div/div/div/div[2]/div/div[2]/button/span')

    # 校验页面标题
    def check_visual_title(self):
        sleep(3)
        self.assert_text('图文带货', '//*[@id="root"]/div/div[1]/div/div/div[1]/div/div[1]')

    # 校验页面标题
    def check_visual_detail(self):
        sleep(5)
        self.assert_text('去创作', '//*[@id="root"]/div/div[1]/div/div/div[3]/div[2]/div[1]/div/button/span')
        sleep(1)
        self.click('//*[@id="root"]/div/div[1]/div/div/div[3]/div[2]/div[1]/div/button/span')
        sleep(2)
        self.assert_text('创作图文带货', '//*[@id="root"]/div/div/div/div[1]')
        sleep(2)
        self.assert_text('取 消', '//*[@id="root"]/div/form/div[4]/div/div/div/div/div[1]/button/span')
        sleep(2)
        self.assert_text('保存到列表', '//*[@id="root"]/div/form/div[4]/div/div/div/div/div[2]/button/span')
        sleep(2)
        self.assert_text('一键发布', '//*[@id="root"]/div/form/div[4]/div/div/div/div/div[3]/button/span')

    # 校验页面标题
    def check_visual_ass_item(self):
        sleep(1)
        self.assert_text('查看更多商品', '//*[@id="root"]/div/div[1]/div/div/div[3]/div[1]/button/span')
        sleep(1)
        # 点击查看更多商品
        self.click('//*[@id="root"]/div/div[1]/div/div/div[3]/div[1]/button/span')
        sleep(2)
        self.assert_text('商品列表', '/html/div/div/div[2]/div/div/div[1]/div/div')
        sleep(2)
        self.assert_text('去创作', '/html/div/div/div[2]/div/div/div[2]/div/div[1]/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/button/span')
        sleep(2)
        self.click('/html/div/div/div[2]/div/div/div[2]/div/div[1]/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/button/span')
        sleep(5)
        self.assert_text('创作图文带货', '//*[@id="root"]/div/div/div/div[1]')

