"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from utils.account_help import get_account_detail
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()
aigc_url1 = 'https://s.kwaixiaodian.com/zone/shortVideo/aigc?itemId=**************&sliceId=************'
aigc_url = 'https://eshop-s.prt.kwaixiaodian.com/zone/aigc/ai_creation?itemId=**************&sliceId=************'
aigc_url_no_item = 'https://s.kwaixiaodian.com/zone/shortVideo/aigc?sliceId=************'
@ddt
class LoadShortVideoEsc(BaseTestCase):

    # 浏览器最大化，进入直播切片页
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_ESC_DOMAIN", "short_video")

    def maximize_window_and_login1(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_BP_DOMAIN", "short_video")
        self.open_url("https://s.kwaixiaodian.com/zone/les/esc")

    # 跳过新手引导
    def skip_guider(self):
        self.refresh_page()
        sleep(2)
        # self.refresh_page()
        # sleep(2)
        # self.refresh_page()
        # sleep(2)
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        #规则管理功能指引
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # sleep(2)
        # 点击自动发布提示气泡 知道了
        # self.click('//*[@id="live-explanation-slice-container"]/div/div[1]/div[2]/div[2]/div[3]/div/div/div/div[2]/div/div[2]/button/span')
        # sleep(2)
        self.refresh_page()
        # sleep(2)
        # self.refresh_page()


    # 校验页面标题
    def check_esc_title(self):
        sleep(2)
        self.assert_text('直播切片发短视频',"(//div[@class='kwaishop-tianhe-shortVideoB-pc-pro-title-title'])[1]" )
        # time.sleep(120)
        self.assert_text('待发布', "(//div[contains(text(),'待发布')])[1]")
        self.assert_text('已发布', "(//div[contains(text(),'已发布')])[1]")
        self.assert_text('发布失败', "(//div[contains(text(),'发布失败')])[1]")

    # 校验已发布页面去推广按钮
    def check_esc_promote(self):
        sleep(1)
        self.assert_text('直播切片发短视频', "(//div[@class='kwaishop-tianhe-shortVideoB-pc-pro-title-title'])[1]")
        # time.sleep(120)
        self.assert_text('已发布',"(//div[contains(text(),'已发布')])[1]")
        self.click("(//div[contains(text(),'已发布')])[1]")
        time.sleep(1)
        self.assert_text('一键推广', "(//span[contains(text(),'一键推广')])[1]")
        self.click("(//span[contains(text(),'一键推广')])[1]")
        sleep(1)
        self.assert_text('流量推广',"(//span[contains(text(),'流量推广')])[1]")



    # 校验托管设置弹窗
    def check_auto_publish(self):
        sleep(2)
        try:
            self.click("(//span[contains(text(),'修改设置')])[1]")
            self.assert_text("全自动发布权限","(//div[contains(text(),'全自动发布权限')])[1]")
        except:
            self.assert_text('去设置', "(//span[contains(text(),'去设置')])[1]")
            sleep(1)
            self.click("(//span[contains(text(),'去设置')])[1]")
            # 点击一键托管
            self.assert_text('一键托管', "(//span[contains(text(),'一键托管')])[2]")
            self.click("(//span[contains(text(),'一键托管')])[2]")

            self.assert_text('智能关联商品', "(//div[@class='AOyhXjtSl0mivyQoN4aU'][contains(text(),'智能关联商品')])[1]")
            # 点击关闭所有开关
            self.click("(//button[@role='switch'])[1]")
            # sleep(200)

    # 校验精细剪辑
    def check_esc_cut(self):
        sleep(1)
        self.assert_text('精细剪辑')
        self.click("(//span[@customstyle='[object Object]'])[1]")
        url = self.get_current_url()
        assert url == 'https://onvideo.kuaishou.com/vangogh?source=Kwaishop_video_center'

    def check_esc_guide(self):
        sleep(2)
        self.assert_text('教程', "(//span[contains(text(),'教程')])[1]")
        self.click("(//span[contains(text(),'教程')])[1]")
        url = self.get_current_url()
        assert url == 'https://docs.qingque.cn/d/home/<USER>'

    # 校验切片数据
    def check_esc_data(self):
        sleep(2)
        self.assert_text('收益数据')
        self.assert_text('整体数据')
        self.assert_text('挂车数据')
        self.assert_text('切片挂车视频曝光数')
        # self.assert_text('1999999')
        self.assert_text('切片挂车GMV')
        # self.assert_text('876.78')
        self.assert_text('引流数据')

    # 校验切片列表
    def check_esc_list(self):
        self.assert_text('批量发布', "//button[@class='kwaishop-tianhe-shortVideoB-pc-btn kwaishop-tianhe-shortVideoB-pc-btn-primary kwaishop-tianhe-shortVideoB-pc-btn-lg']")
        # time.sleep(300)
        self.assert_text('视频内容', "(//th[@class='kwaishop-tianhe-shortVideoB-pc-table-cell kwaishop-tianhe-shortVideoB-pc-table-cell-fix-left kwaishop-tianhe-shortVideoB-pc-table-cell-fix-left-last kwaishop-tianhe-shortVideoB-pc-table-cell-fix-sticky'])[1]")
        self.assert_text('视频类型', "(//span[@class=' kwaishop-tianhe-shortVideoB-pc-table-column-title-no-align'])[1]")
        # 选第4行的切片进行判断
        self.assert_text('一键发布', "(//span[contains(text(),'一键发布')])[1]")
        self.assert_text('替换商品', "(// span[contains(text(), '替换商品')])[1]")
        self.assert_text("AI创作","(//span[contains(text(),'AI创作')])[1]")
        self.assert_text('...', "(//span[contains(text(),'...')])[1]")
        self.hover_on_element("(//span[contains(text(),'...')])[1]")
        sleep(1)
        self.assert_('修改封面')
        self.assert_('手动剪辑')
        # self.assert_('替换商品')
        self.assert_('下载视频')

    # 校验视频描述
    def check_esc_desc(self):
        self.assert_text('批量发布', '//*[@id="live-explanation-slice-container"]/div/div[5]/button/span')
        self.assert_text('视频内容', '//*[@id="live-explanation-slice-container"]/div/div[6]/div/div/div/div/div/table/thead/tr/th[2]/span/div/span[1]')
        self.assert_text('直播标题', '//*[@id="live-explanation-slice-container"]/div/div[6]/div/div/div/div/div/table/thead/tr/th[3]/span/div/span[1]')
        # todo 选第4行的切片进行判断 case有问题，需要调整
        sleep(2)
        # self.click('')


    # 校验修改封面弹窗
    def check_esc_cover_popup(self):
        self.click("(//span[contains(text(),'近30天')])[2]")
        sleep(2)
        # self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[6]/div[1]/div[1]/ul[1]/li[8]/a[1]")
        # sleep(2)
        self.assert_text('...', "(//span[contains(text(),'...')])[1]")
        self.hover_on_element("(//span[contains(text(),'...')])[1]")
        sleep(1)
        self.click("//span[contains(text(),'修改封面')]")
        sleep(2)
        self.assert_text('推荐封面', "(//div[@id='rc-tabs-1-tab-1'])[1]")
        self.assert_text('截取封面', "(//div[@id='rc-tabs-1-tab-2'])[1]")
        # self.assert_text('取 消', '/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[1]/span')
        # self.assert_text('确 定', '/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
        # self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')

    # 校验关联商品弹窗
    def check_esc_ass_item(self):
        self.refresh_page()
        self.click("(//span[contains(text(),'近30天')])[2]")
        sleep(3)
        # self.click("//a[normalize-space()='2']")
        #self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[6]/div[1]/div[1]/ul[1]/li[8]/a[1]")
        # sleep(2)
        self.assert_text('...',"(//span[normalize-space()='...'])[1]")
        self.hover_on_element("(//span[normalize-space()='...'])[1]")
        sleep(1)
        self.assert_text('替换商品', "//span[contains(text(),'替换商品')]")
        self.click("//span[contains(text(),'替换商品')]")
        # sleep(2000)
        sleep(2)
        self.assert_text('货架商品')
        self.assert_text('授权推广商品')
        # # self.click('input[type="radio"]')
        self.assert_text('取 消', "(//span[contains(text(),'取 消')])[1]")
        self.assert_text('确 认', "(//span[contains(text(),'确 认')])[1]")

    # 校验关联商品弹窗
    def check_esc_self_cut(self):
        sleep(2)
        self.assert_text('...', "(//span[normalize-space()='...'])[1]")
        self.hover_on_element("(//span[normalize-space()='...'])[1]")
        sleep(1)
        self.assert_text('手动剪辑', "//span[contains(text(),'手动剪辑')]")
        self.click("//span[contains(text(),'手动剪辑')]")
        # sleep(2000)
        sleep(2)
        url = self.get_current_url()
        # assert 'source=Kwaishop_video_center' in url

    # 校验视频描述
    def check_esc_describe(self):
        # sleep(200)
        self.click("(//div[@class='H3Tn5C514RPNLMXutdFw'])[1]")
        sleep(1)
        # self.type('//*[@id="live-explanation-slice-container"]/div/div[5]/div/div/div/div/div/table/tbody/tr[4]/td[7]/div/div/textarea', '自动化测试新增视频描述修改')
        # sleep(1)

    def check_aigc_goto(self):
        self.refresh_page()
        sleep(5)

        self.assert_text('...', "(//span[normalize-space()='...'])[1]")
        self.hover_on_element("(//span[normalize-space()='...'])[1]")
        # self.assert_text('...', "(//span[contains(text(),'...')])[1]")
        # self.hover_on_element("(//span[contains(text(),'...')])[1]")
        # sleep(1)


        self.click("(//*[name()='svg'])[12]")
        sleep(2)
        self.click("(//span[@class='kwaishop-tianhe-shortVideoB-pc-dropdown-menu-title-content'])[3]")
        self.click("(//span[contains(text(),'确 定')])[1]")


        self.assert_text("AI创作","(//span[contains(text(),'AI创作')])[1]")
        self.click("(//span[contains(text(),'AI创作')])[1]")
        sleep(5)
        # self.assert_text("(//span[contains(text(),'AI创作')])[2]")

        url = self.get_current_url()
        assert 'https://s.kwaixiaodian.com/zone/aigc' in url


    def judge_text(self, text):
        time.sleep(2)
        if text in self.get_page_source():
            return True
        return False

    # 判断选品弹窗的商品有没有被选中
    def isCheckBox(self, xPath):
        isCheckBox = self.get_element(xPath)
        # 如果复选框没有被选中，就点击复选框
        if isCheckBox != 'ant-checkbox ant-checkbox-checked':
            self.click(xPath)
