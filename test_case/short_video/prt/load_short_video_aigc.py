"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from utils.account_help import get_account_detail
from ..base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

aigc_url = 'https://s.kwaixiaodian.com/zone/shortVideo/aigc?itemId=**************&sliceId=************'
aigc_url_no_item = 'https://s.kwaixiaodian.com/zone/shortVideo/aigc?sliceId=************'

@ddt
class LoadShortVideoAigc(BaseTestCase):

    # 浏览器最大化，进入aigc
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_BP_DOMAIN", "short_video")
        self.open_url(aigc_url)

    # 跳过新手引导
    def skip_guider(self):
        # 刷新当前页面
        # self.refresh_page()
        # 【多退货地址策略】功能指引
        sleep(2)
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # 规则管理功能指引
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # sleep(3)
        # # 点击自动发布提示气泡 知道了
        # self.click('//*[@id="root"]/div[2]/div/div/div/div[2]/div[2]/div/button/span')

    # 校验aigc页面 查看教程
    def check_aigc_guide(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_text('查看教程', '//*[@id="root"]/div/div/div[1]/a/span')
        self.click('//*[@id="root"]/div/div/div[1]/a/span')
        url = self.get_current_url()
        assert url == 'https://docs.qingque.cn/d/home/<USER>'

    # 校验aigc页面 视频信息
    def check_aigc_video(self):
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_text('上传视频', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div[1]/label/span')
        self.assert_text('直播切片', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div[2]/div/div/div/div/div[1]/div/span')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div[2]/div/div/div/div/div[1]/div/span')
        time.sleep(1)
        self.assert_text('预览视频', '//*[@id="rcDialogTitle0"]')

    # 校验aigc页面 未关联商品
    def check_aigc_no_ass_item(self):
        self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()

        self.assert_text('添加关联商品', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div[2]/div/div/div/div/button/span')

        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div[2]/div/div/div/div/button/span')

        self.assert_text('添加商品', '//*[@id="rcDialogTitle0"]')
        self.assert_text('重 置', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div/div[1]/button/span')
        self.assert_text('查 询', '//*[@id="pro-form-wrapper"]/div/div[2]/div/div/div[2]/button/span')
        # time.sleep(100)
        self.assert_text('取 消')
        self.assert_text('确 定')
        sleep(5)
        # self.click('/html/body/div[2]/div/div[2]/div/div[2]/div[3]/button[2]/span')
        self.click("//span[contains(text(),'确 定')]")
        time.sleep(1)
        self.assert_text('请选择关联商品', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[2]/div[2]/div[2]/div')

    # 校验aigc页面 已关联商品
    def check_aigc_ass_item(self):
        # self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        # time.sleep(100)
        self.assert_text("素言测试哈哈哈商品勿拍不发货18", "(//div[@class='OARaJvB7rfe3Xj8meUvG'])[1]")

    # 校验aigc页面 智能推荐卖点
    def check_aigc_reco_seller_points(self):
        # self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_text('18元发货', "(//div[@title='18元发货'])[1]")
        self.assert_text('换一批', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[1]/div[2]/div/div[4]/div')
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[1]/div[2]/div/div[4]/div')

    # 校验aigc页面 手动输入卖点
    def check_aigc_seller_points_input(self):
        # self.open_url(aigc_url_no_item)
        time.sleep(1)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.input('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[2]/textarea', 'UI自动化测试')

    # 校验aigc页面 手动输入卖点
    def check_aigc_video_create(self):
        # self.open_url(aigc_url_no_item)
        # time.sleep(1)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_text('暂无视频\n填写信息后智能生成', '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div/div/div/div/div/div[1]/span')
        self.input('//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[3]/div[2]/div/div/div/div[2]/textarea', 'UI自动化测试')
        time.sleep(0.5)
        self.assert_text('生成视频', '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/button[2]/span')
        # self.click('//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/div/button[2]/span')
        # time.sleep(1)
        # # 切片发布次数每日有上限，触发上限时直接返回
        # if self.is_element_present("span.kwaishop-seller-short-video-b-pc-modal-confirm-title"):
        #     return
        # else:
        #     self.assert_text('知道了', '/html/body/div[2]/div/div[2]/div/div[2]/div/div/div[2]/button/span')
        #     self.click('/html/body/div[2]/div/div[2]/div/div[2]/div/div/div[2]/button/span')
        #     time.sleep(0.5)
        #     self.assert_text('视频生成中，请耐心等待\n生成后会自动同步到待发布列表',  '//*[@id="pro-form-wrapper"]/div[2]/div[1]/div/div[1]/div/div/div/div/div/div[1]/span')
        #     time.sleep(0.5)
        #     self.assert_text('根据商品和视频内容生成的文案，生成后可自行修改', "//div[@class='Bhj6DttyCnTSo5bhIV0w']")
        #     self.assert_text('一键发布', '//*[@id="pro-form-wrapper"]/div[2]/div[2]/div/button/span')




    def judge_text(self, text):
        time.sleep(2)
        if text in self.get_page_source():
            return True
        return False

    # 判断选品弹窗的商品有没有被选中
    def isCheckBox(self, xPath):
        isCheckBox = self.get_element(xPath)
        # 如果复选框没有被选中，就点击复选框
        if isCheckBox != 'ant-checkbox ant-checkbox-checked':
            self.click(xPath)
