"""
# Author     ：author <PERSON><PERSON><PERSON><PERSON>
# Description：
"""
from logging import fatal
from time import sleep
from ddt import ddt
from test_case.short_video.load_short_video_esc import LoadShortVideoEsc
# from .load_short_video_esc import LoadShortVideoEsc
import pytest as pytest
import subprocess
import platform
import os
# from .base import BaseTestCase

@ddt
class TestShortVideoDirectSlice(LoadShortVideoEsc):
    def maxwin_login(self):
        self.maximize_window()
        self.login("SWITCH_PAGE", "sanbanfu")
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

    #@pytest.mark.skip
    def test_open_switch(self,account1="sanbanfu"):
        self.maximize_window()
        self.login("SWITCH_PAGE", account=account1)
        sleep(1)
        # 跳过新手引导
        self.skip_guider()
        try:
            self.click("(//span[contains(text(),'修改设置')])[1]")
        except:
            self.click("(//span[contains(text(),'去设置')])[1]")
        sleep(5)
        self.click("(//button[@role='switch'])[1]")
        try:
            kaiguankai = '您已成功开启智能生产权限'
            a = self.find_text("您已成功开启智能生产权限", "(//span[contains(text(),'您已成功开启智能生产权限')])[1]")
            kaiqi = str(a.text)
            if kaiguankai == kaiqi:
                pass
        except:
            self.click("(//button[@role='switch'])[1]")
        self.find_text("您已成功开启智能生产权限", "(//span[contains(text(),'您已成功开启智能生产权限')])[1]")

    #@pytest.mark.skip
    def test_close_switch(self,account1="sanbanfu"):
        self.maximize_window()
        self.login("SWITCH_PAGE", account=account1)
        sleep(1)
        # 跳过新手引导
        self.skip_guider()
        try:
            self.click("(//span[contains(text(),'修改设置')])[1]")
        except:
            self.click("(//span[contains(text(),'去设置')])[1]")
        sleep(5)
        self.click("(//button[@role='switch'])[1]")
        try:
            kaiguankai = '您已成功取消智能生产权限'
            a = self.find_text("您已成功取消智能生产权限", "(//span[contains(text(),'您已成功取消智能生产权限')])[1]")
            kaiqi = str(a.text)
            if kaiguankai == kaiqi:
                pass
        except:
            self.click("(//button[@role='switch'])[1]")
        self.find_text("您已成功取消智能生产权限", "(//span[contains(text(),'您已成功取消智能生产权限')])[1]")

    def kaiguankz(self,switch=0):
        # 0打开开关 1关闭开关
        if switch == 0:
            self.test_open_switch()
        elif switch == 1:
            self.test_close_switch()


    # 引流短视频-发布直播切片
    #@pytest.mark.skip
    def test_publish_live_slice(self):
        self.test_open_switch(account1="short_video")
        self.open("https://zs.kwaixiaodian.com/realtime/operating/zones/dataScreen/main?liveId=KQHJIZOJJWC&entrance=7&trendRange=0")
        self.skip_guider()
        self.click("//div[@id='rc-tabs-0-tab-shortModule']//div[1]")
        self.assert_text("一键发布直播切片","(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'一键发布直播切片')])[3]")

    # 直播切片-发布直播切片
    #@pytest.mark.skip
    def test_live_publish_live_slice(self):
        self.test_publish_live_slice()
        self.click("(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'一键发布')])[4]")


    # 已授权-短视频切片-视频播放
    #@pytest.mark.skip
    def test_video_playback(self):
        self.test_publish_live_slice()
        sleep(5)
        self.click("(//img[@class='zIVhEUWgoFBl2bXkmxQQ'])[1]")
        self.assert_("//div[@class='h6jLB_IO99rPQSLe7OmT']//video")


    # 已授权 - 短视频切片 - 编辑视频描述
    #@pytest.mark.skip
    def test_edit_video_description(self):
        self.test_publish_live_slice()
        self.click("//tbody/tr[2]/td[3]/div[1]/div[1]/div[1]/div[1]")
        self.add_text("//textarea[@placeholder='添加合适的视频描述，作品能获得更多推荐']","自动化测试")
        self.click("(//th[contains(text(),'视频描述')])[1]")
        self.find_text("自动化测试")


    # 已授权-短视频切片-关联商品
    #@pytest.mark.skip
    def test_associated_products(self):
        self.test_publish_live_slice()
        self.assert_("//tbody/tr[2]/td[4]/div[1]/div[1]")

    # 已授权-短视频切片-一键发布
    #@pytest.mark.skip
    def test_click_publishing(self):
        self.test_publish_live_slice()
        self.assert_text("一键发布")
        self.click("//tbody/tr[2]/td[5]/button[1]/span[1]")


    # 授权抽屉-取消or授权
    def test_cancel_or_authorization(self):
        self.kaiguankz(1)
        self.open(
            "https://zs.kwaixiaodian.com/realtime/operating/zones/dataScreen/main?liveId=KQJ%40CPIJJPL&entrance=7&trendRange=0")
        self.skip_guider()
        try:
            self.click("(//span[contains(text(),'取 消')])[1]")
        except:
            pass
        self.click("//div[@id='rc-tabs-0-tab-shortModule']//div[1]")
        self.assert_text("一键发布直播切片", "(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'一键发布直播切片')])[3]")
        self.assert_text("取消","(//span[contains(text(),'取消')])[1]")
        self.assert_text("去授权", "(//span[contains(text(),'去授权')])[1]")
    # 未授权-直播切片托管-全自动发布权限
    #@pytest.mark.skip
    def test_fully_automatic_publishing_permission(self):
        self.kaiguankz(1)
        self.open(
            "https://zs.kwaixiaodian.com/realtime/operating/zones/dataScreen/main?liveId=KQJ%40CPIJJPL&entrance=7&trendRange=0")
        self.skip_guider()
        try:
            self.click("(//span[contains(text(),'取 消')])[1]")
        except:
            pass
        self.click("//div[@id='rc-tabs-0-tab-shortModule']//div[1]")
        self.assert_text("一键发布直播切片", "(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'去授权')])[1]")


        self.click("(//div[contains(text(),'直播中商品详解切片权限托管')])[1]")
        sleep(2)
        self.click("(//div[contains(text(),'直播爆品切片返场权限托管')])[1]")
        sleep(2)
        self.click("(//div[contains(text(),'直播引流片段发布权限托管')])[1]")
        sleep(2)
        self.click("(//div[contains(text(),'切片个人主页展示位置')])[1]")


        self.assert_text("已托管","(//button[@class='kwaishop-seller-shortVideo-promote-pc-btn kwaishop-seller-shortVideo-promote-pc-btn-primary'])[1]")


    # 未授权-直播切片托管-智能生产权限
    #@pytest.mark.skip
    def test_smart_production_rights(self):
        self.kaiguankz(1)
        self.open(
            "https://zs.kwaixiaodian.com/realtime/operating/zones/dataScreen/main?liveId=KQJ%40CPIJJPL&entrance=7&trendRange=0")
        self.skip_guider()
        try:
            self.click("(//span[contains(text(),'取 消')])[1]")
        except:
            pass
        self.click("//div[@id='rc-tabs-0-tab-shortModule']//div[1]")
        self.assert_text("一键发布直播切片", "(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'一键发布直播切片')])[3]")
        self.click("(//span[contains(text(),'去授权')])[1]")
        self.click("(//span[contains(text(),'一键托管')])[1]")
        self.assert_text("已托管",
                         "(//button[@class='kwaishop-seller-shortVideo-promote-pc-btn kwaishop-seller-shortVideo-promote-pc-btn-primary'])[1]")























































