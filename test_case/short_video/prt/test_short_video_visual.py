"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from .load_helper_assistant_preLive import LoadHelperAssistantPreLive

# m_load_pre_live_v2: LoadHelperAssistantPreLive = LoadHelperAssistantPreLive()
from test_case.short_video.load_short_video_visual import LoadShortVideoVisual


@ddt
class TestShortVideoVisual(LoadShortVideoVisual):

    # 进入短视频页面
    # @pytest.mark.skip
    def test_checkout_new_guide(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()


    # 进入短视频图文页面
    # @pytest.mark.skip
    def test_checkout_visual_title(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_visual_title()

    # 进入短视频图文作品详情页面
    # @pytest.mark.skip
    def test_checkout_visual_item_detail(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_visual_detail()

    # 进入短视频图文作品详情页面
    def test_checkout_visual_ass_item(self):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_visual_ass_item()
