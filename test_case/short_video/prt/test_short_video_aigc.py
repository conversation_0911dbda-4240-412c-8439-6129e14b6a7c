"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env

from test_case.short_video.prt.load_short_video_aigc import LoadShortVideoAigc


@ddt
class TestShortVideoAigc(LoadShortVideoAigc):

    # 进入aigc页面,校验新手引导
    @pytest.mark.skip
    def test_checkout_new_guide_aigc(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_guide()

    # 进入aigc页面,校验新手引导
    @pytest.mark.skip
    def test_checkout_aigc_video(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_video()


    @pytest.mark.skip
    def test_checkout_aigc_no_ass_item(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_no_ass_item()


    @pytest.mark.skip
    def test_checkout_aigc_ass_item(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_ass_item()


    @pytest.mark.skip
    def test_checkout_aigc_reco_seller_points(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_reco_seller_points()

    @pytest.mark.skip
    def test_checkout_aigc_seller_points_input(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        # sleep(1000)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_seller_points_input()

    @pytest.mark.skip
    def test_checkout_aigc_video_create(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        sleep(0.5)
        # 跳过新手引导
        self.skip_guider()

        self.check_aigc_video_create()






