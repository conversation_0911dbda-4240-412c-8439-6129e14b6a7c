"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from utils.account_help import get_account_detail
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

@ddt
class LoadShortVideoBp(BaseTestCase):

    # 浏览器最大化，进入批量发短视频页
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("SHORT_VIDEO_BP_DOMAIN", "short_video")

    # 跳过新手引导
    def skip_guider(self):
        # 刷新当前页面
        # self.refresh_page()
        # 【多退货地址策略】功能指引
        sleep(2)
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # 规则管理功能指引
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')
        # sleep(3)
        # # 点击自动发布提示气泡 知道了
        # self.click('//*[@id="root"]/div[2]/div/div/div/div[2]/div[2]/div/button/span')

    # 校验页面标题
    def check_bp_title(self):
        sleep(2)
        self.assert_text('批量发带货短视频', '//*[@id="root"]/div[1]/div[1]/div[1]/span[1]')
        # time.sleep(120)
        self.assert_text('未发布', '//*[@id="rc-tabs-0-tab-0"]')
        self.assert_text('已发布', '//*[@id="rc-tabs-0-tab-3"]')
        self.assert_text('发布失败', '//*[@id="rc-tabs-0-tab-4"]')



    def judge_text(self, text):
        time.sleep(2)
        if text in self.get_page_source():
            return True
        return False

    # 判断选品弹窗的商品有没有被选中
    def isCheckBox(self, xPath):
        isCheckBox = self.get_element(xPath)
        # 如果复选框没有被选中，就点击复选框
        if isCheckBox != 'ant-checkbox ant-checkbox-checked':
            self.click(xPath)
