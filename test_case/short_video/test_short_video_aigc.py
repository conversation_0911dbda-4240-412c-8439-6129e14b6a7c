"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt

from utils.account_help import get_account_detail
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env

from .load_short_video_aigc import LoadShortVideoAigc


@ddt
class TestShortVideoAigc(LoadShortVideoAigc):

    # 进入aigc页面,校验新手引导
    @pytest.mark.skip
    def test_checkout_new_guide_aigc(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_guide()

    # 校验aigc页面 视频信息
    @pytest.mark.skip
    def test_checkout_aigc_video(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_video()

    #校验aigc页面 未关联商品
    # @pytest.mark.skip
    def test_checkout_aigc_no_ass_item(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_no_ass_item()

    # 校验aigc页面 已关联商品
    @pytest.mark.skip
    def test_checkout_aigc_ass_item(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_ass_item()

    # 校验aigc页面 智能推荐卖点
    @pytest.mark.skip
    def test_checkout_aigc_reco_seller_points(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_reco_seller_points()

    # 校验aigc页面 手动输入卖点
    # @pytest.mark.skip
    def test_checkout_aigc_seller_points_input(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_seller_points_input()

    # 校验aigc页面 个性化设置
    @pytest.mark.skip
    def test_checkout_aigc_video_ersonalization(self):
        self.maximize_window_and_login()
        self.check_aigc_video_ersonalization()

    # 校验aigc页面 音乐列表
    @pytest.mark.skip
    def test_checkout_aigc_video_music_list(self):
        self.maximize_window_and_login()
        self.check_aigc_video_music_list()

    # 校验aigc页面 字幕样式
    @pytest.mark.skip
    def test_checkout_aigc_video_subtitle_stylet(self):
        self.maximize_window_and_login()
        self.check_aigc_video_subtitle_stylet()

    # 校验aigc页面  视频配音
    @pytest.mark.skip
    def test_checkout_aigc_video_dubbing(self):
        self.maximize_window_and_login()
        self.check_aigc_video_dubbin()

    # 校验aigc页面  修改封面
    @pytest.mark.skip
    def test_checkout_aigc_modify_cover(self):
        self.test_checkout_aigc_video_create()
        self.check_aigc_video_modify_cover()




    #校验aigc页面 生成视频
    # @pytest.mark.skip
    def test_checkout_aigc_video_create(self, selector=None):
        # 浏览器最大化
        self.maximize_window_and_login()
        self.check_aigc_video_create()

    # 校验aigc页面 重新生成
    @pytest.mark.skip
    def test_checkout_aigc_video_regenerate(self):
        self.maximize_window_and_login()
        self.check_aigc_video_create()
        self.check_aigc_video_regenerat()

    # 校验aigc页面 一键发布
    # def test_checkout_aigc_video_oneclick_publish(self):
    #     self.maximize_window_and_login()
    #     self.check_aigc_video_create()
    #     self.check_check_aigc_video_oneclick_publish()










