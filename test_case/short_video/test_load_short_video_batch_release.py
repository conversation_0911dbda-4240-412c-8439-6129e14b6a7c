"""
# Author     ：author ch<PERSON><PERSON><PERSON>
# Description：
"""
from contextlib import nullcontext
from time import sleep
from ddt import ddt
from pyautogui import click
# from scipy.stats import permutation_test

from .load_short_video_esc import LoadShortVideoEsc
from datetime import datetime
import os


@ddt
class TestShortVideoBatchRelease(LoadShortVideoEsc):
    def maxwin_login(self,zhanghao = "short_video" ):
        self.maximize_window()
        self.login("SHORT_VIDEO_BP_DOMAIN", zhanghao)
        sleep(1)
        # 跳过新手引导
        self.skip_guider()

    def push_video(self,delvideo=1,foundvideo=1,updatavideo=1):
        # foundvideo 是否创建视频 updatavideo上传视频数量 delvideo 1全部删除
        #默认删除已有视频，创建一个视频
        if delvideo == 1:
            try:
                self.click("//th[@class='kwaishop-seller-micro-live-explanation-licp-table-cell kwaishop-seller-micro-live-explanation-licp-table-selection-column kwaishop-seller-micro-live-explanation-licp-table-cell-fix-left']")
                self.click("//span[contains(text(),'删 除')]")
                self.click("//span[contains(text(),'确 认')]")
                self.assert_text("点击或拖拽文件到此区域上传","//span[@class='cloud-upload-text']")
            except:
                pass
        else:
            pass
        if foundvideo == 1:
            frequency = int(updatavideo)
            for i in range(1, frequency+1):
                # self.choose_file("// input[ @ type = 'file']", "short_video/testvideo.mp4")
                # path = os.path.abspath("..")
                # self.choose_file('input[type="file"]',
                #                  path + '/test_data/img/video_test.mp4')
                cur_path = os.path.abspath(os.path.dirname(__file__))
                img_path = cur_path + '/testvideo.mp4'
                self.choose_file('input[type="file"]', img_path)
                sleep(5)
                i +=1
        else:
            pass
        sleep(5)
        self.refresh()
        self.assvideo()


        # 上传视频按钮上传文件
        # self.choose_file("// input[ @ type = 'file']", "short_video/testvideo.mp4")
        # 拖拽区域上传文件
        # self.choose_file("//div[@class='live-explanation-licp-empty-container']//div//div//input[@type='file']","short_video/testvideo.mp4")


    # 未发布-上传视频
    def test_checkout_esc_promote(self):
        # 浏览器最大化
        self.maxwin_login()
        sleep(3)
        self.assert_text('上传视频',"//div[@class='kwaishop-seller-micro-live-explanation-licp-upload-drag-container']")

    # 切换视频来源
    def test_switch_video_sources(self):
        # 浏览器最大化

        self.maxwin_login()
        sleep(3)
        self.refresh_page()
        self.click("//span[@title='User_1607497332902']")
        self.click("/html[1]/body[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]")
        self.assert_text("小伙狠有样")
        # except:
        #     self.assert_text("小伙狠有样", "(//span[@title='小伙狠有样'])[1]")


    # 未发布-一键发布
    def test_switch_video_publishing(self):
        # 浏览器最大化
        self.maxwin_login()
        sleep(3)
        self.push_video()
        self.assvideo()
        self.click("(//span[contains(text(),'一键发布')])[1]")

        try:
            self.assert_text("发布中","//span[@class='kpro-storybook-text-container']")
        except:
            sleep(30)
            self.refresh()
            self.assert_text("发布中", "//span[@class='kpro-storybook-text-container']")

    #未发布-手动剪辑
    def test_switch_video_montage(self):
        # 浏览器最大化
        self.maxwin_login()
        self.push_video()
        self.assvideo()
        self.refresh()
        self.assert_text("手动剪辑","//div[@id='bp-clip-button']")
        self.click("//div[@id='bp-clip-button']")

    #未发布-修改封面
    #@pytest.mark.skip
    def test_switch_video_fm(self):
        # 浏览器最大化
        self.maxwin_login()
        self.push_video()
        self.assvideo()
        self.hover_on_element("//span[normalize-space()='...']")
        self.click("//body//div[@id='main-root']//div[@class='kwaishop-seller-micro-live-explanation-licp-popover-inner-content']//div//div//div[1]")
        cur_path = os.path.abspath(os.path.dirname(__file__))
        img_path = cur_path + '/pic.png'
        self.choose_file("//input[@accept='.jpg, .jpeg, .png']", img_path)
        sleep(3)
        self.assert_text("确 定","//span[contains(text(),'确 定')]")
        self.click("//span[contains(text(),'确 定')]")
        self.assert_text("视频封面替换成功","//span[contains(text(),'视频封面替换成功')]")

    #未发布 - 添加商品
    #@pytest.mark.skip
    def test_switch_add_goods(self):
        self.maxwin_login()
        self.push_video()
        self.assvideo()
        self.hover_on_element("(//span[normalize-space()='...'])[1]")
        self.assert_text("添加商品","//span[contains(text(),'添加商品')]")
        self.click("//span[contains(text(),'添加商品')]")
        try:
            self.click("(//input[@placeholder='仅支持按商品id搜索'])[1]")
            self.add_text("(//input[@placeholder='仅支持按商品id搜索'])[1]", "22315346242853")
        except:
            self.click("(//input[@placeholder='请输入商品关键字或商品ID'])[1]")
            self.add_text("(//input[@placeholder='请输入商品关键字或商品ID'])[1]", "22315346242853")
        self.click("(//button[@class='kwaishop-seller-micro-live-explanation-licp-btn kwaishop-seller-micro-live-explanation-licp-btn-icon-only kwaishop-seller-micro-live-explanation-licp-input-search-button'])[1]")
        self.click("(//label[@class='kwaishop-seller-micro-live-explanation-licp-radio-wrapper'])[1]")
        self.click("(//span[contains(text(),'确 认')])[1]")
        self.assert_text("商品ID:22315346242853","(//span[contains(text(),'商品ID:22315346242853')])[1]")
    #未发布-删除
    def test_switch_del_video(self):
        self.maxwin_login()
        self.push_video(updatavideo=5)
        # sleep(5)
        # self.click("(//span[@class='kwaishop-seller-micro-live-explanation-licp-checkbox'])[2]")
        # self.assert_text("删 除","(//span[contains(text(),'删 除')])[1]")
        # self.click("(//span[contains(text(),'删 除')])[1]")
        # self.click("//span[contains(text(),'确 认')]")
        # sleep(5)
        # try:
        #     self.assert_text("点击或拖拽文件到此区域上传", "//span[@class='cloud-upload-text']")
        # except:
        #     self.assert_text("删 除", "(//span[contains(text(),'删 除')])[1]")
        #     self.click("(//span[contains(text(),'删 除')])[1]")
        #     self.click("//span[contains(text(),'确 认')]")
        # self.assert_text("点击或拖拽文件到此区域上传", "//span[@class='cloud-upload-text']")

    #未发布-批量删除
    #@pytest.mark.skip
    # def test_switch_batch_deletion(self):
    #     self.maximize_window()
    #     self.login("SHORT_VIDEO_BP_DOMAIN", "sanbanfu")
    #     sleep(1)
    #     # 跳过新手引导
    #     self.skip_guider()
    #     self.push_video(updatavideo=2)
    #     sleep(30)
    #     self.refresh()
    #     sleep(2)
    #     self.click("//th[@class='kwaishop-seller-micro-live-explanation-licp-table-cell kwaishop-seller-micro-live-explanation-licp-table-selection-column kwaishop-seller-micro-live-explanation-licp-table-cell-fix-left']")
    #     self.click("//span[contains(text(),'删 除')]")
    #     self.click("//span[contains(text(),'确 认')]")
    #     try:
    #         self.assert_text("点击或拖拽文件到此区域上传", "//span[@class='cloud-upload-text']")
    #     except:
    #         self.click(
    #             "//th[@class='kwaishop-seller-micro-live-explanation-licp-table-cell kwaishop-seller-micro-live-explanation-licp-table-selection-column kwaishop-seller-micro-live-explanation-licp-table-cell-fix-left']")
    #         self.click("//span[contains(text(),'删 除')]")
    #         self.click("//span[contains(text(),'确 认')]")
    #         self.assert_text("点击或拖拽文件到此区域上传", "//span[@class='cloud-upload-text']")

    #未发布-Tab切换
    def test_switch_tab_toggle(self):
        self.maxwin_login()
        self.assert_text("已发布")
        self.click("(//div[@class='kwaishop-seller-micro-live-explanation-licp-tabs-tab'])[1]")

    #未发布 - 关联商品
    # @pytest.mark.skip
    def test_switch_related_goods(self):
        self.maxwin_login()
        self.push_video()
        self.assvideo()
        sleep(10)
        self.refresh()
        self.add_text("(//input[@id='rc_select_0'])[1]","22315346242853")
        self.click("//tbody//tr[contains(@class,'kwaishop-seller-micro-live-explanation-licp-table-row kwaishop-seller-micro-live-explanation-licp-table-row-level-0')]//div[contains(@class,'kwaishop-seller-micro-live-explanation-licp-select kpro-seller-custom-good-card-search kwaishop-seller-micro-live-explanation-licp-select-focused kwaishop-seller-micro-live-explanation-licp-select-single kwaishop-seller-micro-live-explanation-licp-select-open kwaishop-seller-micro-live-explanation-licp-select-show-search')]//div//div//span[2]")
        self.assert_text("商品ID:22315346242853", "(//span[contains(text(),'商品ID:22315346242853')])[1]")
    # 未发布-编辑视频描述
    def test_switch_video_description(self):
        self.maxwin_login()
        self.push_video()
        text = "自动化测试"
        self.refresh()
        try:
            self.click("(//span[contains(text(),'添加合适的视频描述，作品能获得更多推荐')])[1]")
            self.add_text("//textarea[@placeholder='添加合适的视频描述，作品能获得更多推荐']",text)
            self.click("(//th[contains(text(),'视频描述')])[1]")
        except:
            self.push_video()
            sleep(30)
            self.refresh()
            self.click("(//span[contains(text(),'添加合适的视频描述，作品能获得更多推荐')])[1]")
            self.add_text(
                "textarea[placeholder='添加合适的视频描述，作品能获得更多推荐']']",
                text)
        self.assert_text(text,"(//span[@title='自动化测试'])[1]")

    # 未发布-视频播放
    def test_switch_video(self):
        self.maxwin_login()
        self.push_video(updatavideo=5)
        self.assvideo()
        try:
            self.click("(//div[@class='kpro-video-play-play-image-contain'])[1]")
            sleep(2)
            self.click("//div[@class='kpro-video-play-video-container']//video")
        except:
            self.push_video()
            sleep(10)
            self.refresh()
            self.click("(//div[@class='kpro-video-play-play-image-contain'])[1]")
            sleep(2)
            self.click("//div[@class='kpro-video-play-video-container']//video")

        self.assert_("//div[@class='kpro-video-play-video-container']//video")
    # 已发布-去推广
    def test_switch_promote(self):
        self.maxwin_login()
        self.push_video()
        self.assvideo()
        self.click("(//span[contains(text(),'一键发布')])[1]")
        sleep(30)
        self.refresh()
        self.click("(//div[@class='kwaishop-seller-micro-live-explanation-licp-tabs-tab'])[1]")
        sleep(3)
        self.find_text("一键推广")
        self.click("(//div[@class='kwaishop-seller-micro-live-explanation-licp-tabs-tab'])[1]")
    # 已发布-查看数据
    # @pytest.mark.skip
    def test_switch_view_data(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        # self.click("//div[@id='rc-tabs-0-panel-3']//a[@rel='nofollow'][normalize-space()='5']")
        
        self.assert_("查看数据")

    def assvideo(self):
        while True:
            try:
                self.assert_text("58秒","(//td[contains(text(),'58秒')])[1]")
                break
            except:
                self.push_video()
                sleep(10)
                self.refresh()
    #未发布-上传多个视频
    def test_upload_multiple_videos(self):
        self.maxwin_login()
        self.push_video(updatavideo=10)

    #未发布-查看教程
    def test_viewthetutorial(self):
        self.maxwin_login()
        self.click("(//div[@class='kpro-storybook-header-tutorialUrl'])[1]")
        self.assert_text("快手小店电脑端-批量发布带货短视频","(//span[@class='vodka-wordhtmlgenerator-word-node'][contains(text(),'快手小店电脑端-批量发布带货短视频')])[1]")

    #未发布 - 翻页
    # def test_turnthepage(self):
    #     self.maxwin_login()
    #     self.push_video(updatavideo=11)
    #     self.click("(//a[normalize-space()='2'])[1]")
    #     self.assert_text("手动剪辑","(//div[@id='bp-clip-button'])[1]")

    #未发布-切换页面展示条数
    def test_togglethenumberofdisplaysonthepage(self):
        self.maxwin_login()
        self.push_video()
        self.click("(//span[@title='10 条/页'])[1]")
        self.assert_text("10 条/页","//div[@class='kwaishop-seller-micro-live-explanation-licp-select-item-option-content']")

    # 未发布-上传时间
    # def test_uploadtime(self):
    #     self.maxwin_login()
    #     self.push_video()
    #     # now = datetime.now()
    #     # formatted_time = now.strftime('%m-%d %H:%M')
    #     self.find_text("(//td[@class='kwaishop-seller-micro-live-explanation-licp-table-cell kwaishop-seller-micro-live-explanation-licp-table-column-sort kwaishop-seller-micro-live-explanation-licp-table-cell-row-hover'])[1]")

    #未发布 - 片段时常校验
    def test_fragmentsarealwayschecked(self):
        self.maxwin_login()
        self.push_video()
        self.assert_text("58秒", "(//td[contains(text(),'58秒')])[1]")

    # 未发布-状态 待发布
    def test_status_tobereleased(self):
        self.maxwin_login()
        self.push_video()
        self.assert_text("待发布","(//span[@class='kpro-storybook-text-container'])[1]")

    # 未发布-状态 发布中
    # def test_status_inrelease(self):
    #     self.maxwin_login()
    #     self.refresh()
    #     self.click("(//span[contains(text(),'一键发布')])[1]")
    #     self.assert_text("发布中","(//span[@class='kpro-storybook-text-container'])[1]")

    def test_pageswipes(self):
        self.maxwin_login()
        # self.push_video(updatavideo=10)
        self.execute_script("window.scrollBy(0, 10000);")
        sleep(100)

    # 已发布-切换页面展示条数
    def test_published_togglethenumberofdisplaysonthepage(self):
        self.maxwin_login()
        # self.push_video(updatavideo=11)
        # for i in range(1,11):
        #     self.click("(//span[contains(text(),'一键发布')])["+str(i)+"]")
        # self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//span[@title='10 条/页'])[1]")
        self.assert_text("10 条/页",
                         "//div[@class='kwaishop-seller-micro-live-explanation-licp-select-item-option-content']")

    #已发布 - 上传时间
    def test_published_uploadtime(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_("/html[1]/body[1]/div[3]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[2]")

    # 已发布-片段时常校验
    def test_published_fragmentsarealwayschecked(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_text("58秒")

    # 已发布-状态  发布成功
    def test_published_status(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_text("发布成功")


    #已发布-关联商品
    # def test_published_relatedproducts(self):
    #     self.test_switch_add_goods()
    #     self.click("(//span[contains(text(),'一键发布')])[1]")
    #     sleep(30)
    #     self.refresh()
    #     self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
    #     self.assert_text("商品ID:22315346242853")

    #已发布-视频描述
    def  test_published_videodescription(self):
        # self.test_switch_video_description()
        # self.click("(//span[contains(text(),'一键发布')])[1]")
        # sleep(30)
        self.maxwin_login()
        self.refresh()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_("自动化测试","//td[@class='kwaishop-seller-micro-live-explanation-licp-table-cell kwaishop-seller-micro-live-explanation-licp-table-cell-row-hover']//span[@title='自动化测试'][contains(text(),'自动化测试')]")

    #已发布-视频展示
    def test_published_videopresentation(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.assert_("(//div[@class='kpro-video-play-play-image-contain'])[11]")

    #已发布-打开视频
    def test_published_openthevideo(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//div[@class='kpro-video-play-play-image-contain'])[11]")
        self.assert_("(//video)[1]")

    #已发布-翻页
    def test_published_turnthepage(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//a[@rel='nofollow'][normalize-space()='2'])[2]")
        self.click("(//span[contains(text(),'一键推广')])[11]")

    #日期组件展示
    def test_date_componentpresentation(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//div[@class='ant-picker ant-picker-range live-explanation-licp-time-select'])[2]")
        self.assert_text("2025","(//button[contains(text(),'2025年')])[1]")

    # 日期组件切换年份
    def test_date_componentswitchesn(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//div[@class='ant-picker ant-picker-range live-explanation-licp-time-select'])[2]")
        self.click("(//button[@class='ant-picker-header-super-next-btn'])[1]")
        self.assert_text("10", "(//div[@class='ant-picker-cell-inner'][normalize-space()='10'])[1]")

    # 日期组件切换月份
    def test_date_componentswitchmonth(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//div[@class='ant-picker ant-picker-range live-explanation-licp-time-select'])[2]")
        self.click("(//span[@class='ant-picker-next-icon'])[1]")
        self.assert_text("10", "(//div[@class='ant-picker-cell-inner'][normalize-space()='10'])[1]")

    # 日期组件切换时间
    def test_date_component_switch_time(self):
        self.maxwin_login()
        self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
        self.click("(//div[@class='ant-picker ant-picker-range live-explanation-licp-time-select'])[2]")
        self.click("(//div[@class='ant-picker-time-panel-cell-inner'][normalize-space()='22'])[1]")
        self.click("(//span[contains(text(),'确 定')])[1]")
        self.click("(//div[@class='ant-picker-time-panel-cell-inner'][normalize-space()='22'])[1]")
        self.click("(//span[contains(text(),'确 定')])[1]")

    #日期筛选
    # def test_date_filtering(self):
    #     self.maxwin_login()
    #     self.click("(//div[@id='rc-tabs-0-tab-3'])[1]")
    #     self.click("(//div[@class='ant-picker ant-picker-range live-explanation-licp-time-select'])[2]")
    #     #获取当前月份 填入控件中
    #     now = datetime.now()
    #     formatted_time = now.strftime('%m')
    #     formatted_time1 = formatted_time.replace('0', '')
    #     formatted_time = "'"+formatted_time1 + "月"+"'"
    #     a = self.find_element("(//button[contains(text()," + formatted_time + ")])").text
    #     a1 = a.replace('月', '')
    #     lena1 = int(a1)
    #     #目标月份
    #     mubiaodata = 2
    #     #
    #     # while lena1 != mubiaodata:
    #     #     self.click("(//button[@class='ant-picker-header-prev-btn'])[1]")
    #     #     lena1 -= 1
    #     if lena1 > mubiaodata:
    #         for i in range(0,lena1-mubiaodata):
    #             self.click("(//button[@class='ant-picker-header-prev-btn'])[1]")
    #     elif lena1 == mubiaodata:
    #         pass
    #     elif lena1 < mubiaodata:
    #         self.click("(//button[@class='ant-picker-header-next-btn'])[1]")
    #     self.click("(//div[@class='ant-picker-cell-inner'][normalize-space()='1'])[1]")
    #     self.click("(//div[@class='ant-picker-time-panel-cell-inner'][normalize-space()='23'])[1]")
    #     self.click("(//span[contains(text(),'确 定')])[1]")
    #     if lena1 > mubiaodata:
    #         for i in range(0, lena1 - mubiaodata):
    #             self.click("(//button[@class='ant-picker-header-prev-btn'])[1]")
    #     elif lena1 == mubiaodata:
    #         pass
    #     elif lena1 < mubiaodata:
    #         self.click("(//button[@class='ant-picker-header-next-btn'])[1]")
    #     self.click("(//div[@class='ant-picker-cell-inner'][normalize-space()='28'])[1]")
    #     self.click("(//div[@class='ant-picker-time-panel-cell-inner'][normalize-space()='23'])[1]")
    #     self.click("(//span[contains(text(),'确 定')])[1]")
    #     self.assert_("02-28 23:17")
















































































































