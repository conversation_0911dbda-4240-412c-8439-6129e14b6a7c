import random
import time
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestIntelligence(BaseTestCase):

    @pytest.mark.p1
    def test_intelligence_reception_customer_service_assistant(self):
        """
        客服助手—右下角测试窗口
        """
        def checkout_knowledge_base_answer(question="你好", answer_text="你好", answer_class="", answer_id=""):
            self.update_text('//input[contains(@placeholder, "输入买家问题")]', question)
            sleep(2)
            self.click('//div[text()="发送"]')
            sleep(3)
            self.is_element_visible('//div[contains(text(), "'+answer_text+'")]')
            self.is_element_visible('//div[contains(text(), "'+answer_class+'")]')
            self.is_element_visible('//div[contains(text(), "'+answer_id+'")]')

        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='测试']")
        sleep(4)

        # 校验预设问题卡片展示内容
        assert_that(self.is_element_visible('//div[text()="快手官方旗舰店"]'))
        checkout_knowledge_base_answer(question="你好", answer_text="您好，很高兴为您服务", answer_class="闲聊客套", answer_id="52488")
        checkout_knowledge_base_answer(question="你好", answer_text="亲亲，可以描述一下您的问题", answer_class="闲聊客套", answer_id="52488")
        checkout_knowledge_base_answer(question="商品如何保存", answer_text="您可以查看商品详情页", answer_class="商品使用", answer_id="27488")
        checkout_knowledge_base_answer(question="真宝仓订单物流轨迹出现报错怎么办", answer_text="亲亲，对应系统正在升级中", answer_class="物流信息", answer_id="605029")
        checkout_knowledge_base_answer(question="商品价格", answer_text="您可以点击商品详情查看商品的价格", answer_class="商品咨询", answer_id="2295")
        checkout_knowledge_base_answer(question="一直未收到货怎么办", answer_text="普通地区发货后普通地区需要3-5天", answer_class="到货问题", answer_id="60163")

    @pytest.mark.p2
    def test_intelligence_reception_data_display(self):
        """
        数据概览-基础数据-数据展示
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        # "//button[contains(@class, 'ant-switch') and contains(@class, 'ant-switch-checked')]"
        # return          # todo: 编辑按钮被挡住
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='智能客服数据']")
        sleep(4)
        # 校验tab名称
        assert_that(self.is_element_visible('//span[text()="基础数据"]'))
        assert_that(self.is_element_visible('//span[text()="催付（下单未付款）"]'))
        assert_that(self.is_element_visible('//span[text()="催拍（咨询未下单）"]'))
        # 校验具体数值
        assert_that(int(self.get_text('//span[text()="机器人接待人数"]/..//following::div[1]/span[1]')) >= 0)
        assert_that(int(self.get_text('//span[text()="转人工人数"]/..//following::div[1]/span[1]')) >= 0)
        self.click('//span[text()="催付（下单未付款）"]')
        sleep(2)
        assert_that(int(self.get_text('//span[text()="催付次数"]/..//following::div[1]/span[1]')) >= 0)
        self.click('//span[text()="催拍（咨询未下单）"]')
        sleep(2)
        assert_that(int(self.get_text('//span[text()="催拍次数"]/..//following::div[1]/span[1]')) >= 0)

    @pytest.mark.p2
    def test_intelligence_reception_advisory(self):
        """
        问题咨询概览-柱状图
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='智能客服数据']")
        sleep(4)
        # 校验数据名称
        assert_that(self.is_element_visible('//span[text()="问题咨询概况"]'))
        assert_that(self.is_element_visible('//span[text()="咨询问题总排行"]'))
        self.click('//span[text()="未添加到知识库"]')
        self.click('//span[text()="未配置回复内容"]')
        self.click('//span[text()="已配置回复内容"]')
        self.click('//span[text()="咨询问题总排行"]')
        sleep(2)
        if not self.is_element_visible('//div[text()="暂无数据"]'):
            print(self.get_text('//tr[@class="ant-table-row ant-table-row-level-0"]'))
            self.click('//span[text()="去配置"]')
            sleep(2)
            assert_that(self.is_element_visible('//div[text()="新增答案"]'))

    @pytest.mark.p2
    def test_intelligence_knowlegde_list_data_profile(self):
        """
        知识库配置概况-数据展示
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='智能客服数据']")
        sleep(4)
        return              # todo: 缩进问题待前端排查

        assert_that(self.is_element_visible('//span[text()="知识库配置情况"]'))
        all_num = int(self.get_text('//span[text()="问题总数"]/following-sibling::span'))
        exist_num = int(self.get_text('//span[text()="生效中"]/following-sibling::span'))
        empty_num = int(self.get_text('//span[text()="未配置答案数"]/following-sibling::span'))
        assert_that(0 <= exist_num <= all_num and empty_num >= 0)
        # 点击去配置，跳转到知识库问答页面，校验页面元素是否存在
        self.click('//span[text()="去配置"]')
        sleep(7)
        assert_that(self.is_element_visible('//div[text()="通用高频问题"]'))
        assert_that(not self.is_element_visible('//div[text()="新增自定义问题"]'))

    @pytest.mark.p1
    def test_intelligence_reception_keyword_group_management(self):
        """
        关键词组管理-编辑关键词组
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='问答管理']")
        sleep(4)
        self.click("//div[text()='关键词组管理']")
        sleep(3)
        # 校验词组列表表头
        assert_that(self.is_element_visible('//div[text()="关键词组管理"]'))
        assert_that(self.is_element_visible('//th[text()="关键词组"]'))
        assert_that(self.is_element_visible('//th[text()="机器人回答"]'))
        self.click("//span[text()='编辑']")
        sleep(3)
        self.update_text('//input[contains(@placeholder, "请输入关键词")]', 'web自动化关键词')
        sleep(3)
        self.click("//span[text()='确 定']")
        sleep(3)
        assert_that(self.is_element_visible('//span[text()="web自动化关键词"]'))

    @pytest.mark.p1
    def test_intelligence_reception_keyword_phrase_list(self):
        """
        关键词组管理-列表展示
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        # "//button[contains(@class, 'ant-switch') and contains(@class, 'ant-switch-checked')]"
        # return          # todo: 编辑按钮被挡住
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='问答管理']")
        sleep(4)
        self.click("//div[text()='关键词组管理']")
        sleep(3)
        # 校验表头表格数据
        assert_that(self.is_element_visible('//div[text()="关键词组管理"]'))
        assert_that(self.is_element_visible('//th[text()="关键词组"]'))
        assert_that(self.is_element_visible('//th[text()="机器人回答"]'))
        assert_that(self.is_element_visible('//span[text()="web自动化关键词"]'))

    @pytest.mark.p2
    def test_intelligence_reception_product_attribute_management(self):
        """
        商品属性管理-列表展示
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        # "//button[contains(@class, 'ant-switch') and contains(@class, 'ant-switch-checked')]"
        # return          # todo: 编辑按钮被挡住
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='问答管理']")
        sleep(4)
        self.click("//div[text()='商品属性管理']")
        sleep(3)
        # 校验表头表格
        assert_that(self.is_element_visible('//div[text()="商品属性管理"]'))
        assert_that(self.is_element_visible('//th[text()="商品信息"]'))
        assert_that(self.is_element_visible('//th[text()="所属类目"]'))

    @pytest.mark.p2
    def test_intelligence_reception_product_attribute_management_editor(self):
        """
        商品属性管理-编辑
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        # "//button[contains(@class, 'ant-switch') and contains(@class, 'ant-switch-checked')]"
        # return          # todo: 编辑按钮被挡住
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='问答管理']")
        sleep(4)
        self.click("//div[text()='商品属性管理']")
        sleep(3)

        # 输入关键词，查找商品
        self.input('//input[@placeholder="请输入问题关键词"]', "远波测试商品请不要拍")
        sleep(3)
        # 校验表头
        assert_that(self.is_element_visible('//div[text()="商品属性管理"]'))
        assert_that(self.is_element_visible('//th[text()="商品信息"]'))
        assert_that(self.is_element_visible('//th[text()="所属类目"]'))
        sleep(1)
        self.click("//span[text()='编辑商品属性']")
        sleep(4)
        self.click("//span[text()='编辑']")
        sleep(2)
        self.update_text('//textarea[(@placeholder="请输入属性描述")]','web自动化属性值')
        sleep(3)
        self.click("//span[text()='保存']")
        sleep(2)
        self.click("//div[text()='自定义商品属性']")
        sleep(3)
        self.click("//span[text()='编辑']")
        sleep(3)
        self.click("//span[text()='取消']")
        sleep(3)
        self.click("//span[text()='编辑']")
        sleep(2)
        self.update_text('//textarea[(@placeholder="请输入属性描述")]','web自动化属性值')
        sleep(3)
        self.update_text('//textarea[@placeholder="请输入属性名称"]',"web自动化属性值")
        sleep(2)
        self.click("//span[text()='保存']")
        sleep(1)
        assert_that(self.is_element_visible('//span[text()="保存成功"]'))
        assert_that(self.is_element_visible('//div[text()="web自动化属性值"]'))
        assert_that(self.is_element_visible('//div[text()="自定义商品属性"]'))

    @pytest.mark.p1
    def test_intelligence_main_intelligent_shooting(self):
        """
        智能催拍
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(3)
        self.click('//div[text()="智能跟单助手"]')
        sleep(3)


        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[0]

        # 如果催拍总开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            ele.click()
            self.click_if_visible('//span[text()="保存设置"]')
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "true")

        #如果催拍总开关开启状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            ele.click()
            #开启状态点不了确定不知道为什么
            sleep(3)
            self.click_if_visible("//span[text()='确 定']")
            sleep(3)
            self.click_if_visible('//span[text()="保存设置"]')
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "false")

    @pytest.mark.p1
    def test_intelligence_main_smart_reminder(self):
        """
        智能催付款
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(3)
        self.click('//div[text()="智能跟单助手"]')
        sleep(5)
        self.click('//span[text()="催付（下单未付款）"]')
        sleep(5)

        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[0]
        sleep(2)
        # 如果催拍总开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            ele.click()
            self.click_if_visible('//span[text()="保存设置"]')
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "true")

        # 如果催拍总开关开启状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            ele.click()
            sleep(3)
            self.click_if_visible("//span[text()='确 定']")
            sleep(3)
            self.click_if_visible('//span[text()="保存设置"]')
            sleep(2)


    @pytest.mark.p1
    def test_intelligence_reception_service_hours(self):
        """
        接待配置-预设问题-服务时间
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        return          # todo: 编辑按钮被挡住
        self.click("//div[text()='智能客服']")
        sleep(8)
        self.click('//div[text()="接待配置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="接待配置"]')
        sleep(8)

        self.input('//textarea', '您有什么问题需要咨询')
        self.update_text('//input[contains(@placeholder, "开始时间")]', '01:00')
        # self.update_text('//textarea', 'web自动化答案-'+str(num))
        self.click('//span[text()="确 定"]')
        self.click('//span[text()="确 定"]')
        self.click('//span[text()="提 交"]')
        sleep(5)

        # 校验预设问题卡片展示内容
        assert_that(self.is_element_visible('//img[contains(@src, "eshop/chrome-plugin-upload")]'))
        assert_that(self.is_element_visible('//div[text()="客服助手"]'))
        assert_that(self.is_element_visible('//div[contains(text(), "您有什么问题需要咨询")]'))

    @pytest.mark.p1
    def test_intelligence_reception_size_assistant_switch(self):
        """
        直播间知识库-直播间尺码助手-开关
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="直播尺码助手"]')
        sleep(6)
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[0]
        sleep(2)
        # 如果尺码助手总开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            ele.click()
            sleep(3)
            self.click_if_visible("//span[text()='确 定']")
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "true")

        # 如果尺码助手总开关开启状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            ele.click()
            sleep(3)
            self.click_if_visible("//span[text()='确 定']")
            sleep(3)

    @pytest.mark.p2
    def test_intelligence_reception_size_chart(self):
        """
        直播间知识库-尺码表列表
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="直播尺码助手"]')
        sleep(3)
        assert_that(self.is_element_visible('//th[text()="尺码名称"]'))
        assert_that(self.is_element_visible('//th[contains(text(), "尺码图")]'))

    @pytest.mark.p2
    def test_intelligence_commodity_recommend_switch(self):
        """
        智能商品推荐：开关
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='商品推荐']")
        sleep(6)
        self.close_common_guide_toast2()

        # 开关文案校验
        assert_that(self.is_element_visible('//span[contains(text(), "搭配商品并发送")]'))

        # 开关开启/关闭操作
        status = self.get_attribute('//button[contains(@class, "kwaishop-tianhe-im-fe-intelligent-pc-switch")]', 'aria-checked')
        if status or status == 'true':
            self.click('//button[contains(@class, "kwaishop-tianhe-im-fe-intelligent-pc-switch")]')
            sleep(5)
        status = self.get_attribute('//button[contains(@class, "kwaishop-tianhe-im-fe-intelligent-pc-switch")]', 'aria-checked')
        if not status or status == 'false':
            self.click('//button[contains(@class, "kwaishop-tianhe-im-fe-intelligent-pc-switch")]')
            sleep(2)
            status2 = self.get_attribute('//button[contains(@class, "kwaishop-tianhe-im-fe-intelligent-pc-switch")]', 'aria-checked')
            assert_that(status2 or status == 'true')

    @pytest.mark.p2
    def test_intelligence_commodity_recommend_task_oper(self):
        """
        智能商品推荐：展示任务列表、启动/暂停
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='商品推荐']")
        sleep(6)
        self.close_common_guide_toast2()

        # 获取任务列表明细
        text = self.get_text('//tr[2]')
        assert_that("默认智能关联商品推荐" in text)
        assert_that("全部商品" in text)
        assert_that("智能搭配3个" in text)
        self.click('//span[text()="停止"]')
        sleep(2)
        self.click('//span[text()="确 认"]')
        sleep(2)
        self.click('//span[text()="启动"]')
        sleep(2)
        self.click('//span[text()="确 认"]')
        sleep(2)
        text = self.get_text('//tr[2]')
        assert_that("进行中" in text)

    @pytest.mark.p2
    def test_intelligence_commodity_recommend_task_edit_frame(self):
        """
        智能商品推荐：编辑弹窗 + 类目展示
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='商品推荐']")
        sleep(6)
        self.close_common_guide_toast2()

        self.click('//span[text()="编辑"]')
        sleep(5)
        assert_that(self.is_element_visible('//span[text()="【编辑】关联商品推荐任务"]'))
        assert_that(self.is_element_visible('//span[text()="进行中"]'))
        assert_that(self.is_element_visible('//input[@value="默认智能关联商品推荐"]'))
        # 校验文本框的文本是否出现在买家效果预览卡片
        assert_that(self.is_element_visible('//div[text()="为您推荐这些宝贝"]'))
        text = self.get_text('//textarea')
        assert_that(self.is_element_visible('//span[text()="'+text+'"]'))

        # 校验指定类目弹窗
        self.click('//span[text()="指定类目"]/../../../preceding-sibling::*[1]')
        sleep(1)
        self.click('//div[@class="kwaishop-tianhe-im-fe-intelligent-pc-select-selection-overflow"]')
        self.click('//div[text()="家居生活"]')
        self.click('//div[text()="厨具/杯具"]')
        self.click('//div[text()="厨房收纳"]')
        self.click('//div[text()="调味瓶罐"]')
        self.click('//span[text()="指定类目"]/../../../preceding-sibling::*[1]')
        sleep(1)
        assert_that(self.is_element_visible('//span[text()="调味瓶罐"]'))
        assert_that(self.is_element_visible('//span[text()="已选择1个类目"]'))

    @pytest.mark.p2
    def test_intelligence_commodity_recommend_task_edit_commodity(self):
        """
        智能商品推荐：编辑弹窗输入+输出商品选择
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click("//div[text()='商品推荐']")
        sleep(6)
        self.close_common_guide_toast2()

        self.click('//span[text()="编辑"]')
        sleep(5)
        assert_that(self.is_element_visible('//span[text()="【编辑】关联商品推荐任务"]'))

        # 校验输入商品弹窗
        self.click('//span[text()="指定商品"]/../../../preceding-sibling::*[1]')
        sleep(2)
        self.click('//span[text()="添加商品"]')
        sleep(5)
        # 是否已选商品
        self.click('//label[@class="kwaishop-tianhe-im-fe-intelligent-pc-checkbox-wrapper"]')
        sleep(2)
        assert_that(int(self.get_text('//span[contains(text(), " / ")]').split(' / ')[0]) > 0)
        assert_that(int(self.get_text('//span[contains(text(), " / ")]').split(' / ')[1]) == 1000)
        # 校验商品
        text = self.get_text('//div[@class="kwaishop-tianhe-im-fe-intelligent-pc-modal"]//child::tr[2]')
        assert_that('测试' in text)
        assert_that('已下架' in text)
        text2 = self.get_text('//div[@class="kwaishop-tianhe-im-fe-intelligent-pc-modal"]//child::div[contains(@class, "kwaishop-tianhe-im-fe-intelligent-pc-space-vertical")]')
        assert_that('测试' in text2)
        assert_that('已下架' in text2)
        assert_that(text in text2)
        self.click('//span[text()="确 定"]')
        sleep(2)
        # 选择完商品后，已选择商品数 > 0
        assert_that(int(self.get_text('//span[contains(text(), "已选择")]')[3:-3]) > 0)

        # 校验输出商品弹窗
        self.click('//span[text()="指定固定商品"]/../../../preceding-sibling::*[1]')
        sleep(2)
        self.click('//span[text()="添加商品"]')
        sleep(5)
        self.click('//label[@class="kwaishop-tianhe-im-fe-intelligent-pc-checkbox-wrapper"]')
        sleep(2)
        assert_that(int(self.get_text('//span[contains(text(), " / ")]').split(' / ')[0]) > 0)
        assert_that(int(self.get_text('//span[contains(text(), " / ")]').split(' / ')[1]) == 3)
        self.click('//span[text()="确 定"]')
        sleep(2)
        assert_that(self.is_element_visible('//div[text()="指定固定商品"]'))


















