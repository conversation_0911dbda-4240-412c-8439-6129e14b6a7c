import random
import time
from encodings.punycode import selective_len
from time import sleep
from unittest import skip

import pytest
from ddt import *
from pyautogui import click
from selenium.webdriver import ActionChains
from selenium.webdriver.common.keys import Keys
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestIntelligence(BaseTestCase):

    @pytest.mark.p0
    def test_intelligence_main_switch_oper(self):
        """
        智能客服(总)开关操作
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="基础配置"]')
        sleep(2)

        # 获取智能客服开关
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[0]

        # 如果智能客服总开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            ele.click()
            sleep(1)
            self.click_if_visible('//span[text()="确 定"]')
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "true")

        # 如果智能客服总开关关闭状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            ele.click()
            sleep(1)
            self.click_if_visible('//span[text()="关闭智能"]')
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "false")

    @pytest.mark.p1
    def test_intelligence_auto_reception_switch_oper(self):
        """
        智能辅助接待开关操作
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="基础配置"]')
        sleep(2)

        # 获取智能客服辅助接待开关
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[1]

        # 如果智能客服总开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            sleep(2)
            ele.click()
            sleep(2)
            self.click_if_visible('//span[text()="确 定"]')
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "true")

        # 如果智能客服总开关关闭状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            sleep(2)
            ele.click()
            sleep(2)
            self.click_if_visible('//span[text()="关闭智能"]')
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "false")

        # 点击“去设置”，跳转到智能辅助接待页面   todo: check智能辅助接待功能
        if len(switch_ls) < 2:
            self.click('//a[text()="去设置"]')
            sleep(2)
            assert_that(self.is_element_visible('//div[text()="智能辅助接待"]'))

    @pytest.mark.p2
    def test_intelligence_third_part_switch_oper(self):
        """
        三方客服展示，以及开关操作
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="基础配置"]')
        sleep(2)

        # 获取智能客服辅助接待开关
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        # 如果存在三方客服
        if len(switch_ls) >= 2:
            # 输出三方客服名称
            if self.is_element_visible('//span[@class="status-switch-tip"][last()]'):
                print('[三方客服名称]:', self.get_text('//span[@class="status-switch-tip"][last()]'))

    @pytest.mark.p1
    def test_intelligence_reception_question_update(self):
        """
        接待配置：设置预设问题
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(12)
        self.click('//div[text()="接待配置"]')
        sleep(2)
        self.click('//li[@role="menuitem"]/span/div[text()="接待配置"]')
        sleep(2)
        # return

        # 校验预设问题卡片展示内容
        assert_that(self.is_element_visible('//img[contains(@src, "nlav12333/live-assets")]'))
        assert_that(self.is_element_visible('//div[text()="客服助手"]'))
        assert_that(self.is_element_visible('//div[contains(text(), "您有什么问题需要咨询")]'))
        if self.is_element_visible("//span[contains(text(),'为你推荐')]"):
            self.click("//span[contains(text(),'为你推荐')]/following-sibling::img")

        # 预设卡片内容-编辑
        # return      # todo: JavascriptException https://ui-test-report.staging.kuaishou.com/ui/merchant/report4296.html
        self.execute_script('document.getElementsByClassName("cs-micro-container")[0].scrollTo(0, 10000)')
        self.click('//tr[@data-row-key="1"]//child::a[text()="编辑"]')
        num = random.randint(1000, 9999)
        self.update_text('//input[contains(@placeholder, "问题内容")]', 'web自动化问题-'+str(num))
        self.update_text('//textarea', 'web自动化答案-'+str(num))
        self.click('//span[text()="确 定"]')
        sleep(2)

        # 校验编辑后的问题/答案是否有出现
        assert_that(self.is_element_present('//span[contains(text(), "'+str(num)+'")]'))

    @pytest.mark.p1
    def test_intelligence_reception_valid_time_update(self):
        """
        时效管理：设置智能客服问答生效时效
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        self.click('//div[text()="时效管理"]')
        sleep(2)

        # 时效展示：默认展示永久生效
        assert_that(self.is_element_visible('//td[text()="永久生效"]'))
        assert_that(self.is_element_visible('//td[text()="永久时效"]'))
        assert_that(self.is_element_visible('//td[text()="每日00:00:00 至 每日23:59:59"]'))

        # 对web自动化时效进行编辑
        num = random.randint(1000, 9999)
        if self.is_element_visible('//td[contains(text(), "02:00:00 至 02:59:00")]/following-sibling::*[1]'):
            self.click('//td[contains(text(), "02:00:00 至 02:59:00")]/following-sibling::*[1]/div/button[1]')
            # 修改时效名称，保存
            self.update_text('//input[@placeholder="请输入时效名称"]', 'web自动化时效测试'+str(num))
            self.click('//span[text()="提 交"]')
            sleep(2)
            # 校验新时效名称是否出现
            self.is_element_visible('//td[contains(text(), "'+str(num)+'")]')

    @pytest.mark.p1
    def test_intelligence_knowledge_update(self):
        """
        时效管理：智能客服问答知识管理
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="问答知识管理"]')
        sleep(2)

        # 按关键词搜索知识点
        self.update_text('//input[@placeholder="请输入问题关键词"]', ' web自动化知识测试')
        self.click('//span[@aria-label="system-search-line"]')
        sleep(2)

        # 获取知识点开启/关闭状态，并将状态更新到关闭
        status = self.get_attribute('//button[contains(@class, "ant-switch")]', 'aria-checked')
        print('[当前知识点开关是否开启]:', status)
        if status == 'false':
            self.click('//button[contains(@class, "ant-switch")]')
            sleep(2)
            status = self.get_attribute('//button[contains(@class, "ant-switch")]', 'aria-checked')
            assert_that(status == 'true')
            sleep(2)
        if status == 'true':
            self.click('//button[contains(@class, "ant-switch")]')
            self.click('//span[text()="确 定"]')
            sleep(2)
            status = self.get_attribute('//button[contains(@class, "ant-switch")]', 'aria-checked')
            assert_that(status == 'false')
            sleep(2)

        # 点击更新按钮
        self.click('//span[@aria-label="system-edit-line"]')
        # 编辑答案
        num = random.randint(1000, 9999)
        self.update_text('//div[@data-placeholder="请输入问法内容"]/p', '')
        sleep(2)
        self.update_text('//div[@data-placeholder="请输入问法内容"]/p', 'web自动化知识答案-'+str(num))
        self.click('//span[text()="保 存"]')
        sleep(2)

        # 校验编辑后的答案，是否最新展示在列表里面
        self.is_element_present('//p[contains(text(), "'+str(num)+'")]')
        # [未生效|机器人/智能辅助接待通用] tab展示
        self.is_element_present('//span[contains(text(), "未生效")]')
        self.is_element_present('//span[contains(text(), "机器人")]')

    @pytest.mark.p1
    def test_Seat_configuration(self):
        """
        接待配置-坐席辅助配置
        """
        return
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click('//div[text()="接待配置"]')
        sleep(2)
        self.click('//div[text()="坐席辅助配置"]')
        sleep(4)

        # 获取智能辅助接待开关
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[0]

        # 如果智能辅助接待总开关  打开状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "false")

        # 如果智能辅助接待总开关 关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "true")

        # 智能辅助配置
        # 切换 全/半自动 模式
        self.click('//span[text()="半自动模式"]')
        time.sleep(1)
        self.click('//span[text()="全自动模式"]')
        time.sleep(1)
        # self.click('//span[text()="保存并生效"]')
        # time.sleep(1)
        # assert_that(self.is_element_present('//span[text()="保存智能辅助配置成功"]'))

        # 客服授权
        self.click('//div[text()="客服授权"]')
        time.sleep(1)
        # 第一次
        switch_ls1 = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls1[1]
        if ele.get_attribute("aria-checked") == "1":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "0")
        if ele.get_attribute("aria-checked") == "0":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "1")
            sleep(1)
        # 第二次
        switch_ls1 = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls1[2]
        if ele.get_attribute("aria-checked") == "1":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "0")
        if ele.get_attribute("aria-checked") == "0":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "1")
            sleep(1)
        # 第三次
        switch_ls1 = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls1[3]
        if ele.get_attribute("aria-checked") == "1":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "0")
        if ele.get_attribute("aria-checked") == "0":
            ele.click()
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "1")
            sleep(1)
        # 查询
        self.type("input[placeholder='请输入客服昵称']", 'wny')
        self.click('//span[text()="查 询"]')
        sleep(1)
        # 第一次
        switch_ls1 = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls1[1]
        if ele.get_attribute("aria-checked") == "1":
            ele.click()
            sleep(2)
            self.click('//span[text()="查 询"]')
            sleep(2)
            assert_that(ele.get_attribute("aria-checked") == "0")
            sleep(1)
            self.click('//span[text()="查 询"]')
        if ele.get_attribute("aria-checked") == "0":
            ele.click()
            sleep(2)
            self.click('//span[text()="查 询"]')
            sleep(2)
            assert_that(ele.get_attribute("aria-checked") == "1")
            sleep(1)
        # 返回保存
        self.click('//div[text()="智能辅助配置"]')
        time.sleep(1)
        self.click('//span[text()="保存并生效"]')
        time.sleep(1)
        assert_that(self.is_element_present('//span[text()="保存智能辅助配置成功"]'))
        time.sleep(1)
        print("结束")

    @pytest.mark.p2
    def test_Scenario_Solution(self):
        """
        问答管理-场景解决方案
        """
        return
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(4)
        self.click('//div[text()="问答管理"]')
        sleep(1)
        self.click('//div[text()="场景解决方案"]')
        sleep(2)
        # 关闭弹窗
        self.Close_the_scene_solution_popup_window()
        # 物流场景
        self.click('//span[text()="物流场景"]')
        sleep(1)
        self.click('//span[text()="物流进度"]')
        sleep(1)
        assert_that(self.is_element_present('//div[text()="场景名称："]'))
        # 断言文本内容

        # 问答断言3
        self.hover_on_element("//span[text()='催促物流更新']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="商家的发货单号快递公司根本查不到"]'))
        assert_that(self.is_element_present('//span[text()="我的快递查询了下发现没有这个快递单号"]'))
        assert_that(self.is_element_present('//span[text()="我的快递查询了下发现没有这个快递单号"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        sleep(1)
        # 问答断言2
        self.hover_on_element("//span[text()='快递到哪里了']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="没事，我就看看快递到哪了"]'))
        assert_that(self.is_element_present('//span[text()="我的快递到哪里了？"]'))
        assert_that(self.is_element_present('//span[text()="亲你把那个补发的单号发给我一下"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        sleep(1)
        # 问答断言1
        self.hover_on_element("//span[text()='一直未收到货怎么办']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="几天了怎么还没收到货品"]'))
        assert_that(self.is_element_present('//span[text()="我的货怎么还没到"]'))
        assert_that(self.is_element_present('//span[text()="但是我没收到商品"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        # 状态断言
        assert_that(self.is_element_present('//div[text()="未付款"]'))
        assert_that(self.is_element_present('//div[text()="未发货"]'))
        assert_that(self.is_element_present('//div[text()="已发货"]'))

        # 回复内容
        phone_text1 = self.get_text_content("(//div[@class='JnaVK5wFt9w0PNSgTB1Q'])[1]")
        text1 = phone_text1[:15]  # 切片：前10个字的内容
        self.hover_on_element("(//div[text()='[回复卡片预览]'])[1]")
        sleep(3)
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(2)

        # 回复内容2
        phone_text1 = self.get_text_content("(//div[@class='JnaVK5wFt9w0PNSgTB1Q'])[2]")
        text1 = phone_text1[:15]  # 切片：前10个字的内容
        self.hover_on_element("(//div[text()='[回复卡片预览]'])[2]")
        sleep(3)
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(2)

        # 编辑内容
        self.click('//span[@aria-label="system-edit-line"][1]')
        sleep(3)
        phone_text1 = self.get_text_content("(//div[@class='ql-editor'])")
        text1 = phone_text1[:15]
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(1)
        self.click("//span[text()='取 消']")
        sleep(1)
        self.click("//span[text()='确 定']")
        sleep(2)

        # 售后场景
        self.click('//span[text()="售后场景"]')
        sleep(1)
        # 售后场景-催收后
        self.click('//span[text()="催售后"]')
        sleep(1)
        assert_that(self.is_element_present('//div[text()="场景名称："]'))

        # 问答断言3
        self.hover_on_element("//span[text()='主观不想要能否申请换货']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="这个货我不想要了想换下货"]'))
        assert_that(self.is_element_present('//span[text()="你好我不想要这个格子了想换货怎么办"]'))
        assert_that(self.is_element_present('//span[text()="我不想要我想换一双别的样子"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        sleep(1)
        # 问答断言2
        self.hover_on_element("//span[text()='如何申请换货']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="我怎么换先退款退货重拍吗"]'))
        assert_that(self.is_element_present('//span[text()="如何换货"]'))
        assert_that(self.is_element_present('//span[text()="好的我要换货"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        sleep(1)
        # 问答断言1
        self.hover_on_element("//span[text()='退货退款处理时效']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="东西没收到我已经申请退款了什么时候能给我退款"]'))
        assert_that(self.is_element_present('//span[text()="我两件都申请退款了什么时候能到账亲"]'))
        assert_that(self.is_element_present('//span[text()="我已申请退款什么时候收到钱"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        # 状态断言
        assert_that(self.is_element_present('//div[text()="未申请售后"]'))
        assert_that(self.is_element_present('//div[text()="已申请售后"]'))

        # 回复内容
        phone_text1 = self.get_text_content("(//div[@class='JnaVK5wFt9w0PNSgTB1Q'])[1]")
        text1 = phone_text1[:15]  # 切片：前10个字的内容
        self.hover_on_element("(//div[text()='[回复卡片预览]'])[1]")
        sleep(3)
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(2)

        # 回复内容2
        phone_text1 = self.get_text_content("(//div[@class='JnaVK5wFt9w0PNSgTB1Q'])[2]")
        text1 = phone_text1[:15]  # 切片：前10个字的内容
        self.hover_on_element("(//div[text()='[回复卡片预览]'])[2]")
        sleep(3)
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(2)

        # 编辑内容
        self.click('//span[@aria-label="system-edit-line"][1]')
        sleep(3)
        phone_text1 = self.get_text_content("(//div[@class='ql-editor'])")
        text1 = phone_text1[:15]
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(1)
        self.click("//span[text()='取 消']")
        sleep(1)
        self.click("//span[text()='确 定']")
        sleep(2)


        # 售后场景-申请售后
        self.click('//span[text()="申请售后"]')
        sleep(1)
        assert_that(self.is_element_present('//div[text()="场景名称："]'))

        # 问答断言3
        self.hover_on_element("//span[text()='商品不合适如何申请退货退款']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="买的包太小了能退款吗"]'))
        assert_that(self.is_element_present('//span[text()="衣服不合适可以退款吗"]'))
        assert_that(self.is_element_present('//span[text()="不合适能退货不"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        sleep(1)
        # 问答断言2
        self.hover_on_element("//span[text()='买多了申请退款']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="我商品多拍了想退款"]'))
        assert_that(self.is_element_present('//span[text()="我多拍了申请退款"]'))
        assert_that(self.is_element_present('//span[text()="我买的床上用品我看不上多拍了我现在要退款"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        sleep(1)
        # 问答断言1
        self.hover_on_element("//span[text()='质量问题如何申请退款']")
        sleep(2)
        assert_that(self.is_element_present('//div[text()="更多问法"]'))
        assert_that(self.is_element_present('//span[text()="已收货发现质量问题申请退款"]'))
        assert_that(self.is_element_present('//span[text()="质量问题申请退款"]'))
        assert_that(self.is_element_present('//span[text()="怀疑质量问题申请退款"]'))
        self.hover_on_element('//div[text()="场景解决方案"]')
        # 状态断言
        assert_that(self.is_element_present('//div[text()="已申请售后"]'))
        assert_that(self.is_element_present('//div[text()="未申请售后"]'))

        # 回复内容
        phone_text1 = self.get_text_content("(//div[@class='JnaVK5wFt9w0PNSgTB1Q'])[1]")
        text1 = phone_text1[:15]  # 切片：前10个字的内容
        print(text1)
        sleep(1)
        self.hover_on_element("(//div[text()='[回复卡片预览]'])[1]")
        sleep(3)
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-MaterialText'])")
        text2 = phone_text2[:15]
        print(text2)
        assert str(text1) == str(text2)
        sleep(2)

        # 回复内容2
        phone_text1 = self.get_text_content("(//div[@class='JnaVK5wFt9w0PNSgTB1Q'])[2]")
        text1 = phone_text1[:15]  # 切片：前10个字的内容
        self.hover_on_element("(//div[text()='[回复卡片预览]'])[2]")
        sleep(3)
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-MaterialText'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(2)

        # 编辑内容
        self.click('//span[@aria-label="system-edit-line"][1]')
        sleep(3)
        phone_text1 = self.get_text_content("(//div[@class='ql-editor'])")
        text1 = phone_text1[:15]
        phone_text2 = self.get_text_content("(//div[@class='kwaishop-cs-MaterialText'])")
        text2 = phone_text2[:15]
        assert str(text1) == str(text2)
        sleep(1)
        return

    @pytest.mark.p2
    def test_Customer_service_data(self):
        """
        智能客服数据
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(8)
        self.click('//div[text()="智能客服数据"]')
        sleep(5)

        # 数据概览
        self.click('//span[text()="催拍（咨询未下单）"]')
        sleep(1)
        self.click('//span[text()="前天"]')
        sleep(1)
        self.click('//span[text()="近7日"]')
        sleep(1)
        self.click('//span[text()="近30天"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="催拍次数"]'))
        assert_that(self.is_element_present('//span[text()="催拍卡片触达人数"]'))
        assert_that(self.is_element_present('//span[text()="催拍成功人数"]'))
        assert_that(self.is_element_present('//span[text()="催拍成功率"]'))
        sleep(1)

        self.click('//span[text()="催付（下单未付款）"]')
        sleep(1)
        self.click('//div[text()="昨日"]')
        sleep(1)
        self.click('//div[text()="近7日"]')
        sleep(1)
        self.click('//div[text()="近30天"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="催付次数"]'))
        assert_that(self.is_element_present('//span[text()="催付卡片触达次数"]'))
        assert_that(self.is_element_present('//span[text()="催付成功次数"]'))
        assert_that(self.is_element_present('//span[text()="催付成功率"]'))
        sleep(1)

        self.click('//span[text()="基础数据"]')
        sleep(1)
        self.click('//div[text()="昨日"]')
        sleep(1)
        self.click('//div[text()="近7日"]')
        sleep(1)
        self.click('//div[text()="近30天"]')
        sleep(1)
        # assert_that(self.is_element_present('//span[text()="总咨询人数"]'))
        assert_that(self.is_element_present('//span[text()="机器人接待人数"]'))
        assert_that(self.is_element_present('//span[text()="转人工人数"]'))
        assert_that(self.is_element_present('//span[text()="机器人接待占比"]'))
        sleep(1)

        # 知识库配置情况    todo: 待前端排查
        # self.click('//span[@aria-label="system-arrow-small-right-line"][1]')
        # sleep(1)
        # assert_that(self.is_element_present('//div[text()="通用高频问题"]'))
        # sleep(3)
        # self.click('//div[text()="智能客服数据"]')
        # sleep(1)

        # 问题咨询概况
        self.click('(//div[text()="昨日"])[2]')
        sleep(1)
        self.click('//span[text()="咨询问题总排行"]')
        sleep(1)
        self.click('//span[text()="未添加到知识库"]')
        sleep(1)
        self.click('//span[text()="未配置回复内容"]')
        sleep(1)
        self.click('//span[text()="已配置回复内容"]')
        sleep(1)
        self.click('(//div[text()="近7日"])[2]')
        sleep(1)
        self.click('//span[text()="咨询问题总排行"]')
        sleep(1)
        self.click('//span[text()="未添加到知识库"]')
        sleep(1)
        self.click('//span[text()="未配置回复内容"]')
        sleep(1)
        self.click('//span[text()="已配置回复内容"]')
        sleep(1)
        self.click('(//div[text()="近30天"])[2]')
        sleep(1)
        self.click('//span[text()="咨询问题总排行"]')
        sleep(1)
        self.click('//span[text()="未添加到知识库"]')
        sleep(1)
        self.click('//span[text()="未配置回复内容"]')
        sleep(1)
        self.click('//span[text()="已配置回复内容"]')
        sleep(1)


        # 问题咨询概况 - 去配置
        if not self.is_element_present('//div[text()="暂无数据"]'):
            self.click('(//span[@class="JTwxwXbj3318xa_U5MYd"])[1]')
            sleep(2)
            self.click('//span[text()="关联"]')
            sleep(1)
            self.click('//span[text()="下单未支付"]')
            self.click('//span[text()="付款未发货"]')
            self.click('//span[text()="发货未签收"]')
            self.click('//span[text()="发货已签收"]')
            self.click('//span[text()="交易已成功"]')
            self.click('//span[text()="订单已关闭"]')
            sleep(1)
            self.click('//span[text()="不关联"]')
            sleep(1)
            self.click('//span[text()="添加第二次命中回复"]')
            sleep(1)
            self.type("div[data-placeholder='请输入问法内容']", '************')
            sleep(1)
            self.click('(//span[text()="取 消"])')
            sleep(1)
            self.click('(//span[text()="确 定"])')
            sleep(1)

    @pytest.mark.p2
    @pytest.mark.skip
    def test_relationship(self):
        # 直播尺码助手，关联商品
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="智能客服"]')
        sleep(2)
        self.click('//div[text()="直播尺码助手"]')
        sleep(2)
        self.click('//a[text()="关联商品"]')
        sleep(1)

        # 提取到第一个商品信息的勾选框
        switch_ls = self.driver.find_elements(by='class name', value='ant-checkbox-wrapper')
        ele = switch_ls[1]
        sleep(3)
        # 如果智能辅助接待总开关  打开状态、将其关闭
        if ele.get_attribute("class") == "ant-checkbox-wrapper":
            ele.click()
            sleep(2)
            assert_that(ele.get_attribute("class") == "ant-checkbox-wrapper ant-checkbox-wrapper-checked")
            self.click('//span[text()="确 定"]')
            sleep(1)
            assert_that(self.is_element_present('//span[text()="已成功关联1个商品"]'))

        sleep(2)

        self.click('//a[text()="关联商品"]')
        sleep(3)
        # 如果智能辅助接待总开关 关闭状态、将其打开
        # if ele.get_attribute("class") == "ant-checkbox-wrapper ant-checkbox-wrapper-checked":
        if not ele.get_attribute("class") == "ant-checkbox-wrapper":
            ele.click()
            sleep(2)
            assert_that(ele.get_attribute("class") == "ant-checkbox-wrapper")
            self.click('//span[text()="确 定"]')
            sleep(1)
            assert_that(self.is_element_present('//span[text()="已成功取消1个商品关联"]'))

        sleep(2)

    @pytest.mark.p2
    def test_assistant_copywriter_order(self):
        # 智能跟单助手，下方左右文案一致性   ---催拍
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        if not self.is_element_visible('//div[text()="智能客服"]'):
            return

        self.click('//div[text()="智能客服"]')
        sleep(2)
        self.click('//div[text()="智能跟单助手"]')
        sleep(2)
        self.type("textarea[class='ant-input']", '您关注的商品马上就要售罄啦，尽快下单!测试')
        sleep(2)
        assert_that(self.is_element_present('//div[text()="您关注的商品马上就要售罄啦，尽快下单!测试"]'))
        sleep(1)
        self.click('//span[text()="保存设置"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="保存成功！"]'))
        sleep(1)
        self.type("textarea[class='ant-input']", '您关注的商品马上就要售罄啦，尽快下单!12')
        sleep(2)
        assert_that(self.is_element_present('//div[text()="您关注的商品马上就要售罄啦，尽快下单!12"]'))
        sleep(1)
        self.click('//span[text()="保存设置"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="保存成功！"]'))
        sleep(1)

    @pytest.mark.p2
    def test_assistant_copywriter_payment(self):
        # 智能跟单对手，下方左右文案一致性   ---催付
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        if not self.is_element_visible('//div[text()="智能客服"]'):
            return

        self.click('//div[text()="智能客服"]')
        sleep(2)
        self.click('//div[text()="智能跟单助手"]')
        sleep(2)
        self.click('//span[text()="催付（下单未付款）"]')
        sleep(2)
        self.type("textarea[class='ant-input']", '您有一个订单仍未付款，早付款早发货哦~测试')
        sleep(2)
        assert_that(self.is_element_present('//span[text()="您有一个订单仍未付款，早付款早发货哦~测试"]'))
        sleep(1)
        self.click('//span[text()="保存设置"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="保存成功！"]'))
        sleep(1)
        self.type("textarea[class='ant-input']", '您有一个订单仍未付款，早付款早发货哦~')
        sleep(2)
        assert_that(self.is_element_present('//span[text()="您有一个订单仍未付款，早付款早发货哦~"]'))
        sleep(1)
        self.click('//span[text()="保存设置"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="保存成功！"]'))
        sleep(1)

    @pytest.mark.p2
    def test_intelligence_display_newcomer_guidance(self):
        """
        智能客服新人引导展示
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="基础配置"]')
        sleep(2)
        self.hide_seller_helper_box()

        task_str = self.get_text('//*[contains(text(), "已完成")]')
        pattern = re.compile(r"\d+")  # 查找一个或多个数字
        task_num = pattern.findall(task_str)

        # 标题校验
        self.is_element_visible('//div[contains(text(), "机器人配置要点")]')
        assert_that(self.is_element_visible('//div[text()="买家进店"]'))
        assert_that(self.is_element_visible('//div[text()="买家咨询"]'))
        assert_that(self.is_element_visible('//div[text()="买家转人工"]'))
        assert_that(self.is_element_visible('//div[text()="数据分析"]'))

        # 存在未完成任务
        if int(task_num[1]) > 0 and int(task_num[0]) < int(task_num[1]):
            assert_that(self.is_element_visible('//div[text()="去学习"]'))

        # 存在已完成任务
        if int(task_num[0]) > 0:
            assert_that(self.is_element_visible('//div[text()="去查看"]'))
            element = self.get_element('//div[text()="去查看"]/..')
            background_color = element.value_of_css_property('background-color')
            assert_that(background_color == 'rgba(255, 249, 236, 1)')

    @pytest.mark.p2
    def test_intelligence_size_recommend(self):
        """
        智能尺码推荐
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="智能尺码推荐"]')
        sleep(2)

        assert_that(self.is_element_visible('//div[contains(text(), "智能尺码推荐适用于服饰")]'))
        assert_that(self.is_element_visible('//img[contains(@src, "lxnqdkad9o")]'))
        status = self.get_attribute('//button[contains(@class, "ant-switch")]', 'aria-checked')
        if status or status == 'true':
            self.click('//button[contains(@class, "ant-switch")]')
            sleep(5)
            self.click('//span[text()="确认关闭"]')
            sleep(2)
            self.click('//button[contains(@class, "ant-switch")]')
            sleep(2)
            status2 = self.get_attribute('//button[contains(@class, "ant-switch")]', 'aria-checked')
            assert_that(status2 or status == 'true')

    @pytest.mark.p2
    # @pytest.mark.skip("需求更新，待修改ui自动化case")
    def test_intelligence_properties_auto_random_answer_switch(self):
        """
        智能生成设置--开关设置断言不上
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="智能生成设置"]')
        sleep(5)

        # 校验预览图元素
        assert_that(self.is_element_present('//div[text()="效果展示"]'))
        assert_that(self.is_element_present('//img[contains(@src, "trade-assets")]'))

        # 打开客服机器人开关
        assert_that(self.is_element_present('//div[text()="商品问题生成式答案设置"]'))
        assert_that(self.is_element_present('//div[contains(text(), "不会回答配置的固定答案")]'))
        assert_that(self.is_element_present('//div[contains(text(), "场景解决方案>生成式答案>知识点")]'))
        self.click('//span[text()="智能机器人"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="智能机器人已关闭"]') or self.is_element_present('//span[text()="智能机器人已开启"]'))

        self.click('//span[text()="智能辅助全自动模式"]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="智能辅助全自动模式已关闭"]') or
                    self.is_element_present('//span[text()="智能辅助全自动模式已开启"]'))


    @pytest.mark.p2
    def test_intelligence_generative(self):
        """
        生成式答案
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        if self.is_element_present('//div[text()="辅助中"]'):
            assert_that(self.is_element_present('//div[text()="辅助中"]'))
        else:
            assert_that(self.is_element_present('//div[text()="待开启辅助"]'))

        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="基础配置"]')
        sleep(2)

        # 获取智能客服开关
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[1]

        # 如果智能客服总开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "false":
            ele.click()
            sleep(3)
            assert_that(ele.get_attribute("aria-checked") == "true")
            sleep(2)
            self.click('//div[text()="会话"]')
            sleep(5)
            assert_that(self.is_element_present('//div[text()="辅助中"]'))
            sleep(2)
            self.hover_on_element("//div[text()='辅助中']")
            sleep(2)
            assert_that(self.is_element_present('//div[text()="根据辅助设置，会为你推荐话术或自动回复部分问题"]'))
            sleep(2)
            self.click("//div[text()='智能客服']")
            sleep(2)
            self.click('//div[text()="基础配置"]')
            sleep(5)

        # 获取智能客服开关
        switch_ls = self.driver.find_elements(by='class name', value='ant-switch')
        ele = switch_ls[1]
        # 如果智能客服总开关关闭状态、将其关闭
        if ele.get_attribute("aria-checked") == "true":
            sleep(2)
            ele.click()
            sleep(3)
            self.click('//span[text()="关闭智能"]')
            sleep(1)
            assert_that(ele.get_attribute("aria-checked") == "false")
            self.click('//div[text()="会话"]')
            sleep(5)
            assert_that(self.is_element_present('//div[text()="待开启辅助"]'))
            sleep(2)
            self.hover_on_element("//div[text()='待开启辅助']")
            sleep(2)
            assert_that(self.is_element_present('//div[text()="智能辅助可为您推荐话术或自动回复部分问题。请联系管理员在「智能客服-接待配置-坐席配置」中开启"]'))

    @pytest.mark.skip("需要远程调试下")
    @pytest.mark.p2
    def test_intelligence_generative_show(self):
        """
        生成式答案-帮助跳转
        """
        return
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return
        if self.is_element_present('//div[text()="辅助中"]'):
            assert_that(self.is_element_present('//div[text()="辅助中"]'))
        else:
            assert_that(self.is_element_present('//div[text()="待开启辅助"]'))

        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="智能生成设置"]')
        sleep(6)
        self.hover_on_element('//img[@class="icon___ch4nU"]')
        sleep(3)
        assert_that(self.is_element_present('//div[text()="需先开通智能机器人或智能辅助全自动模式，才会在对应环节，系统直接回复答案"]'))
        self.click('//a[text()="点击去开通智能机器人"]')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="接待模式"]'))
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="智能生成设置"]')
        sleep(6)
        self.hover_on_element('//img[@class="icon___ch4nU"]')
        sleep(3)
        self.click('//a[text()="点击去开通智能辅助全自动模式"]')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="智能辅助接待"]'))
        sleep(2)
        self.click('//a[text()="学习如何配置"]')
        sleep(5)
        assert_that(self.is_element_present('//span["快语智能客服「商品属性问题生成式答案」功能介绍"]'))

    @pytest.mark.p2
    def test_intelligence_generative_configuration(self):
        """
        生成式答案-设置跳转
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return

        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="接待配置"]')
        sleep(2)
        self.click('//div[text()="坐席辅助配置"]')
        sleep(2)
        self.hover_on_element('//span[@aria-label="system-questionmark-circle-line"]')
        sleep(1)
        assert_that(self.is_element_present('//div[text()="开启智能辅助全自动模式，并在“生成式答案设置”中勾选相应场景， 则会在触发场景问题时，直接回复生成式答案"]'))
        self.click('//span[text()="生成式答案设置"]')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="商品问题生成式答案设置"]'))


    @pytest.mark.p2
    def test_intelligence_generative_authorize(self):
        """
        生成式答案-客服授权
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='智能客服']"):
            return

        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="接待配置"]')
        sleep(2)
        self.click('//div[text()="坐席辅助配置"]')
        sleep(2)
        self.click('//div[text()="客服授权"]')
        sleep(2)
        # 获取开关状态
        ele1 = self.driver.find_elements(by='class name',value='ant-switch')[0]
        ele = self.driver.find_elements(by='class name',value='ant-switch')[1]

        # 如果开关关闭状态、将其打开
        if ele.get_attribute("aria-checked") == "1" and ele1.get_attribute("aria-checked") == "true":
            self.click('//div[text()="会话"]')
            sleep(5)
            assert_that(self.is_element_present('//div[text()="辅助中"]'))
        else:
            self.click('//div[text()="会话"]')
            sleep(5)
            assert_that(self.is_element_present('//div[text()="待开启辅助"]'))
    @pytest.mark.p2
    def test_intelligence_update_knowledge_reply(self):
        """
        智能客服-更改标问回复
        """
        return          # todo: 账号更换，暂时注释
        self.go_workbrench(self,user="wanqing")
        if not self.is_element_visible("//div[text()='智能客服']"):
            return

        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="问答知识管理"]')
        sleep(2)
        self.type("input[class='ant-input']", '退货商品可以一起邮寄吗')
        sleep(2)
        self.click('//span[@class="anticon anticon-system-search-line"]')
        sleep(2)
        self.click('//span[@class="anticon anticon-system-edit-line icon___oE56b icon-active___A9Xhq"]')
        sleep(2)
        self.click('//span[text()="保 存"]')
        sleep(2)

        self.go_workbrench(self, user="chunxiao")
        if not self.is_element_visible("//div[text()='智能客服']"):
            return

        self.click("//div[text()='智能客服']")
        sleep(2)
        self.click('//div[text()="问答管理"]')
        sleep(2)
        self.click('//div[text()="问答知识管理"]')
        sleep(2)
        self.type("input[class='ant-input']", '退货商品可以一起邮寄吗')
        sleep(2)
        self.click('//span[@class="anticon anticon-system-search-line"]')
        sleep(2)
        assert not self.is_element_present('//div[text()="待开启辅助"]')
        assert_that(not self.is_element_present('//p[contains(text(), "123")]'))        # 加了123保存，到另外一个问题未修改