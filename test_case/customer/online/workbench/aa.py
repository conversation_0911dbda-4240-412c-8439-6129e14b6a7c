import subprocess
import os
import time
import pandas as pd
import requests
import openpyxl


def request(cks, data, url):
    # data: json格式，url：域名+前缀链接
    # data = {"buyerId": "1275802949", "limit": 10, "offset": 0, "searchKeyword": "", "viewTab": 2}
    # url = 'https://eshop-s.prt.kwaixiaodian.com/gateway/business/cs/list/item/info'
    if data != "" and url != "":
        cookies = cks
        headers = {
            'Content-Type': 'application/json',
            "Cookie": cookies
        }
        res = requests.request(url=url, method="POST", json=data, headers=headers).json()
        #print("【请求结果】：")
        #print(res)
        return res
    return {}

if __name__ == '__main__':

    """
    # 压测脚本
    TIMES = 50000
    # 使用 os 模块执行 cURL GET 请求
    curl = r'curl -H "Host: eshop-app.prt.kwaixiaodian.com" -H "Cookie: ehid=6500cwRKJZ8ZZE1fykcd2q8dvkh6BBUqbjYg8; yoda_wkjs_sessionOnly=test; yoda_wktest_rewrite=js; X-KTrace-Id-Enabled=0; __NSWJ=; appver=12.9.21.2400075; browseType=4; c=BETA; cdidTag=7; cdid_tag=7; cl=0; client_key=56c3713c; countryCode=cn; country_code=cn; darkMode=false; deviceBit=0; did=52BDB730-059B-41FD-A1F5-405EA46790EA; didTag=0; did_tag=0; egid=DFP6CFCE439F4E0FCE87ACD81E21874E60BC53037AE1A924801FC367FC175994; foreign=0; ftt=; gid=; global_id=DFP6CFCE439F4E0FCE87ACD81E21874E60BC53037AE1A924801FC367FC175994; grant_browse_type=AUTHORIZED; isp=CTCC; kfx-pre=0; kpf=IPHONE; kpn=KUAISHOU; kuaishou.api_st=Cg9rdWFpc2hvdS5hcGkuc3QSoAEPJjAylN_1gVFFr0ekTDAtsClBcBcr5ibagj8SLsl-r-a4hzXGkbCCa1Mw5H2sUnSk4De31aMJ7ritrGFENQHuVw_H6N0YrCZWk2d75dlcwAmlIOjG_BovXNGLt47tKoN9TZxwh1iW3xLArw3bdvAJmD5l1IPldFgcx93A-HP81hEhsWDfB49yKSNEAKjMv77wFx0kfYhYenwSfX1u--8aGhLgtQndTL9KcIMccEKwrUv06E4iIHI6RRU_GzIK5Qtip2494O62xpODXg06J1j_Yaec_Sj7KAUwAQ; kuaishou.h5_st=Cg5rdWFpc2hvdS5oNS5zdBKgAZcd3pnalzbOkLFJizT3HNtS-nP6xoJRniMtj0U20J0LyJe8daJxCPzhajmjYNPn6Kssv0Ue9LPNaHI_g5I8JGU_poXKHFgrugrpOcUWhLv2XDooa9Jd_TsRcTtZrcbDaLQrnVFUQx-g29gy499eB_kG_O7BlMnlkzLGXccV_k8V6_TUiZ9gcG_RWXvB4qW67howmm1vLxYqKdbje-kITFUaEg0_YXPVwk9IXs622rYg9Se0USIgbTWXkG4ZfQb3YSzbziHGlh99HGMSZCcbt3ccIvHdk68oBTAB; kuaishou.sixin.login_st=ChdrdWFpc2hvdS5zaXhpbi5sb2dpbi5zdBKgAdXNlQli16Mig08zLzJ4UxJLLxEC2YoeMcsFnCXo62XiRMe7RQLaxwc5ufug3e7I3qEqwMCn-vLeAwwjRbGzOUdy3qXDxfxqD5-uWxZccDyYgAm5lNbhyvPEwxeksQoK9-hGmsAhgDqZatMLTJIQBOF6s2WZO98Qm25YIVp54qVWCc3SIdeplVawMCVT9VgUJatEhMCsHvVd7w0mfXZsXHsaEjXWRved0Emhgzgb-UyLy3wtbiIgFNIij7gra8SLRlydmbYaAAxxnn7Qp75FjQQMTqkwrxgoBTAB; language=zh-Hans-CN%3Bq%3D1; lat=; lkvr=RjI4REExOUMtRUYw15prKDXrj3AXR2CNEgxw8JoRfMjC_uut42g7SB0f1-7NnFIxbGH5CA; ll=; ll_client_time=1730816049482.762; lon=; mcc=46011; mod=iPhone14%2C5; net=WIFI; oDid=52BDB730-059B-41FD-A1F5-405EA46790EA; odid=52BDB730-059B-41FD-A1F5-405EA46790EA; os=15.2.1; pUid=qg0J2a4G8NT8HS3tLzpaJcUpRrvb4_Qd091dee48f6e0239; power_mode=0; randomDid=52BDB730-059B-41FD-A1F5-405EA46790EA; rdid=52BDB730-059B-41FD-A1F5-405EA46790EA; session_id=2080FB71-1ABD-4A24-AC6A-B6CA9C3A3411; sh=2532; sid=2080FB71-1ABD-4A24-AC6A-B6CA9C3A3411; sw=1170; sys=iOS_15.2.1; thermal=10000; token=2bdc9469c6ee4e3ebcdab5fd681aa2c2-1691062442; trace-context=%7B%22laneId%22%3A%22PRT.yctest%22%7D; uQaTag=32834%2333333333339999999999%23ecLd%3A9-%23swRs%3A0-%23cmPs%3A11; ud=1691062442; urlencode_mod=iPhone14%252C5; userId=1691062442; userRecoBit=2; ver=12.9; videoModelCrowdTag=1_74; weblogger_switch=; yoda_wktest_httponly=test; yoda_wktest_sessiononly=test; kuaishou.shop.im_st=ChNrdWFpc2hvdS5zaG9wLmltLnN0EqAB_qtcJvW5nIOmj6iEbSfN3wWOTw07KN3I8n9CeJMaELljIYoio7Lgi9JSZrGjEQR6_0ulRGgoMWgeDNZBAIwtPDty6GY7w42rY7LvtJphjC4NsPuPcX9GLvZ6EXUJselBgoWbnZ3LMQkZ6wuaApRb6cso465FXY7azMFWYU_Ai51L8PK134NkyOTHVSYI8Lsba84wTrkqGyBDS9AjJA7f1RoSfddnxmSDjGMfKE2M2d7wX5hBIiB6cauZBCYaMD_v3OAeWesSyLFyU7pWaX5EZWmuTkSV9SgFMAE; yoda_nshttp_httponly=test; yoda_nshttp_long=test; hdige2wqwoino=2nZpmQDQpCRp64ZphSh2YeehT2xHzQek91c3c80a; accessproxy_session=887bb635-672b-48ff-a57d-1948e838466d; apdid=852aef24-59af-4433-a6cd-467f0ba6e0171c8e284ba9d4d7bf538740f943a1c938:1730807082:1" -H "content-type: application/json" -H "accept: application/json, text/plain, */*" -H "ks-csrf-token: " -H "kpf: IPHONE_H5" -H "kpn: " -H "accept-language: zh-CN,zh-Hans;q=0.9" -H "origin: https://eshop-im.prt.kwaixiaodian.com" -H "user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Kwai/12.9.21.2400075 ISLP/0 StatusHT/47 KDT/PHONE ISDM/0 TitleHT/44 NetType/WIFI ICFO/0 locale/zh-Hans CT/0 Yoda/******* ISLB/0 CoIS/2 ISLM/0 WebViewType/WK BHT/102 AZPREFIX/az1" -H "referer: https://eshop-im.prt.kwaixiaodian.com/" -H "ktrace-str: 1|****************************************************************|****************************************************************|0|eshop-frontend|plateco|true|src-Js" -H "trace-context: {\"laneId\":\"PRT.yctest\"}" --data-binary "{\"sid\":\"kuaishou.shop.im\"}" --compressed "https://eshop-app.prt.kwaixiaodian.com/rest/app/merchant/ks/shop/getShowOwner"'
    for i in range(TIMES):
        os.system(curl)
        time.sleep(1)
    """

    # # 获取数据工厂数据
    # data = {
    #     "account": "",
    #     "dataType": 1,
    #     "distributorPermission": 2,
    #     "leaderPermission": 2,
    #     "merchantPermission": 2,
    #     "pageNo": 2,
    #     "pageSize": 100,
    #     "promoterPermission": 2,
    #     "userId": ""
    # }
    # url = 'https://kwaishop-risk.test.gifshow.com/gateway/qa/risk/account/queryPageUserAccount'
    # Cookie = '_did=web_82392456248A1461; did=web_9d51t30ypwbep9j7urkc2azg7rs4nbvy; hdige2wqwoino=CCPxwyKcCpBi66DXdkNcm7cAbQwRrzaQ6a61cd06; sid=kuaishou.shop.b; apdid=f2a71991-b5e6-44ba-aa6a-bdda4cc863af81eb26263c630e24fe25c7e3fe088ea6:**********:1; userName=shimengtao; accessproxy_session=37794a25-9b54-40ae-8b4f-b467ccfda524; pa-gateway-token=649f3a25-0238-42e2-a582-a43d9e0633f7; username=shimengtao; userName=shimengtao; ehid=35fIYtkZ_d_4QZ-jjCx8BzB7oGAqS8jQ3jkeC'
    #
    # ls = []
    # for idx in range(1, 14):
    #     data["pageNo"] = idx
    #     res = request(Cookie, data, url)
    #     ls.extend(res['data']['details'])
    #     time.sleep(1)
    #
    # df = pd.json_normalize(ls)
    # df.to_excel("output.xlsx", index=False, engine='openpyxl')




