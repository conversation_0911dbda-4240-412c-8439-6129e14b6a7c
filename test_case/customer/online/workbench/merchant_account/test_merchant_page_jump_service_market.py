import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase, judge_in_seconds
from utils.time_help import *
from hamcrest import *


@ddt
class TestMerchantAccountPageJumpServiceMarket(BaseTestCase):
    # 客服管理-》客服机器人-〉服务市场
    #
    @skip("从客服跳转过去的服务市场PRT链接有问题，暂时skip")
    @pytest.mark.p1
    def test_customer_management_robot_service_market(self):
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", "customer_merchant")

        self.assert_text("客服管理", "li[id= 'node_menu_for_intersectionObserver_6']")
        # print("current_url1:", self.get_current_url())
        self.sleep(0.5)
        # 点击客服管理
        self.click('#node_menu_for_intersectionObserver_6 > div')
        sleep(0.2)
        # 再次点击客服管理
        self.click('#node_menu_for_intersectionObserver_6 > div')
        sleep(0.5)
        try:
            # 点击知道了
            self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        except Exception as e:
            print()
        self.maximize_window()
        sleep(1)
        self.maximize_window()
        sleep(1)
        # 点击客服机器人设置#menu_item_92 > span > span
        self.click('#node_menu_for_intersectionObserver_6 > ul > li:nth-child(6)')  # 点击进入客服机器人设置
        sleep(1)
        # 点击服务市场
        self.click('#root > section > main > div > div.sc-dkQkSb.gbobMG > div.sc-jivCuG.hcnbXe > a')
        sleep(1)
        # 切换到新页面
        self.switch_to_window(1)
        self.check_if_page_404()
        self.assert_title(' 服务列表-快手服务市场-快手电商官网 ')
        context = self.get_text('#app > section > main > div')
        assert_that(context, all_of(contains_string("晓多智能客服机器人"), contains_string("首页"), contains_string("客服工具")))
