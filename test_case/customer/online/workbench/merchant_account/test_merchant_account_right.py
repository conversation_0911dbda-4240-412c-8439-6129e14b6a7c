import random
import time
import datetime
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory
from selenium.webdriver import Keys, ActionChains

from test_case.customer.online.tools.order_tools import OrderTools


@ddt
class TestMerchantAccountLeft(BaseTestCase):

    # 快捷回复
    @pytest.mark.p0
    def test_fast_replay(self):
        """
        个人快捷回复
        """
        self.go_workbrench(self)
        # 快捷回复tab
        FAST_REPLY_TAB = '//div[text()="快捷回复"]'
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        # 点击快捷回复tab
        self.click(FAST_REPLY_TAB)
        sleep(1)
        # 点击快捷回复分组“问候语”
        self.click('//div[contains(text(),"问候")]')
        sleep(2)
        # 点击带“效劳”的快捷回复
        self.click('//div[contains(text(),"效劳")]/parent::div/div[2]')
        sleep(2)
        # 展开后缩回
        self.click('//div[contains(text(),"问候")]')
        sleep(2)
        if self.is_element_present(CHAT_EDIT_TEXT):
            # 校验会话区输入框，出现消息内容“宝宝”
            res = self.get_text(CHAT_EDIT_TEXT)
            assert_that(res, contains_string('宝宝'))
            # 点击发送按钮
            self.click(CHAT_SEND_BTN)
            # 校验发送后，消息框为空
            res = self.get_text(CHAT_EDIT_TEXT)
            sleep(1)
            assert_that(res == "", "")

    @pytest.mark.p0
    def test_fast_replay_bottom_button(self):
        """
        个人快捷回复：底部tab按钮操作
        """
        self.go_workbrench(self)
        # 快捷回复tab
        FAST_REPLY_TAB = '//div[text()="快捷回复"]'
        # 点击快捷回复tab
        self.click(FAST_REPLY_TAB)
        sleep(3)
        # 判断底部按钮是否出现
        assert_that(self.is_element_present('//div[@class="bottomButton"]'))
        assert_that(self.is_element_present('//span[@aria-label="system-add-line"]'))
        assert_that(self.is_element_present('//span[@aria-label="system-upload-line"]'))
        assert_that(self.is_element_present('//span[@aria-label="share-export-line"]'))
        # 上传快捷回复-弹窗校验
        self.click('//span[@aria-label="system-upload-line"]')
        time.sleep(2)
        assert_that(self.is_element_present('//div[text()="导入快捷回复"]'))
        assert_that(self.is_element_present('//a[text()="下载模版"]'))
        assert_that(self.is_element_present('//span[text()="立即导入"]'))
        self.click('//button[@class="ant-modal-close"]')
        time.sleep(3)
        # 新增快捷回复-弹窗校验
        self.click('//span[@aria-label="system-add-line"]')
        time.sleep(2)
        assert_that(self.is_element_present('//div[text()="新增快捷回复"]'))
        self.click('//div[@class="ant-form-item-control-input-content"]//div[1]')
        self.click('//div[text()="主动提醒"]')
        self.add_text('//input[@placeholder="请输入快捷编码"]', 'web_test')
        self.add_text('//textarea[@placeholder="请输入回复内容"]', 'web-ui自动化-测试快捷回复')
        # self.click('//button[@class="ant-modal-close"]')

    @pytest.mark.p0
    def test_group_fast_replay(self):
        """
        团队快捷回复
        """
        self.go_workbrench(self)
        # 快捷回复tab
        FAST_REPLY_TAB = '//div[text()="快捷回复"]'
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        # 点击快捷回复tab
        self.click(FAST_REPLY_TAB)
        sleep(1)
        # 点击团队列表
        self.click('//div[contains(text(),"团队列表")]')
        sleep(2)
        # 点击快捷回复分组“问候语”
        self.click('//div[contains(text(),"问候")]')
        sleep(2)
        # 点击带“效劳”的快捷回复-后一条
        self.click('//div[contains(text(),"效劳")]/parent::div/div[2]')
        sleep(2)
        # 展开后缩回
        self.click('//div[contains(text(),"问候")]')
        sleep(2)
        if self.is_element_present(CHAT_EDIT_TEXT):
            # 校验会话区输入框，出现消息内容“宝宝”
            res = self.get_text(CHAT_EDIT_TEXT)
            assert_that(res, contains_string('宝宝'))
        # 右侧快捷回复底部区域，无按钮
        assert_that(not self.is_element_present('//div[@class="bottomButton"]'))

    @pytest.mark.p0
    def test_buyer_info(self):
        """
        买家个人信息查看
        """
        self.go_workbrench(self, user='member_account2', id="**********")
        sleep(1)
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 获取买家头像、昵称
        buyer_name = self.get_text("//div[@class='buyerName']")
        buyer_avatar = self.get_element("//div[@class='avatar']//img").get_attribute("src")
        print("[买家昵称]: ", buyer_name)
        print("[买家头像]: ", buyer_avatar)
        assert_that("User" in buyer_name)

        # 获取买家下单数据
        orders = self.get_text('//span[contains(text(),"下单")]')
        avg = self.get_text('//span[contains(text(),"件单价")]')
        goods = self.get_text('//span[contains(text(),"好评")]')
        refund = self.get_text('//span[contains(text(),"退货")]')
        print("[下单信息]: ", [orders, avg, goods, refund])

        # 获取用户备注
        notice = self.get_text('//div[@class="note"]')
        print("[用户备注]: ", notice)

        # 缩回后（判断下单信息不存在），再重新展开
        self.click('//div[@class="append"]')
        sleep(2)
        assert_that(not self.is_element_visible('//span[contains(text(),"退货率")]'))
        sleep(1)
        self.click('//div[@class="append"]')
        sleep(2)
        assert_that(self.is_element_visible('//span[contains(text(),"退货率")]'))

        # 获取买家会员等级/入会时间
        buyer_vip_level = self.get_text('//span[@class="level"]')
        self.hover_on_element('//span[@class="level"]')
        buyer_vip_level_toast = self.get_text('//div[@class="popTitle"][1]')
        assert_that(buyer_vip_level in buyer_vip_level_toast)
        # buyer_vip_join_time = self.get_text('//span[@class="itemValue"]')

    @pytest.mark.p1
    def test_buyer_remark_modify(self):
        """
        买家个人备注修改
        """
        self.go_workbrench(self, id="2817449844")
        sleep(1)
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 非会员，不展示会员等级
        assert_that(not self.is_element_visible('//span[@class="level"]'))

        # 点击编辑按钮，修改买家备注
        self.click('//div[@class="editIcon"]')
        remark_content = 'web-ui自动化测试-买家备注-' + str(random.randint(1, 10000))
        self.update_text('//textarea[@placeholder="此处可添加或者修改买家备注信息，内容对全店客服可见"]', remark_content)
        # 点击确认按钮，保存
        self.click('//div[@class="editAction"]//button[2]')
        self.click('//div[@class="ant-popover-buttons"]//button[2]')
        sleep(5)
        # 保存后的买家备注存在
        assert_that(self.is_element_visible('//div[text()="'+remark_content+'"]'))

    @pytest.mark.p0
    def test_send_item_card(self):
        """
        商品推荐页面发送热销商品
        """
        self.go_workbrench(self, user='member_account2', id="**********")

        # 点击商品推荐
        if self.is_element_visible('//div[text()="商品推荐"]'):
            self.click('//div[text()="商品推荐"]')
            sleep(2)
            self.click('//div[text()="热销商品"]')
            sleep(3)

            # 发送商品
            if not self.is_element_visible('//div[@class="goodsName"][1]'):
                return
            goods_title = self.get_text('//div[@class="goodsName"][1]')
            self.click('(//span[text()="发送商品"])[1]')
            self.sleep(3)
            CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
            if self.is_element_present(CHAT_EDIT_TEXT):
                # title = self.get_text('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_msgBody"])[last()]/div[1]/div[1]')
                title = self.get_text('(//div[@class="kwaishop-cs-BizGoodsCard_title"])[last()]')
                # carsh = self.get_text('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_msgBody"])[last()]/div[1]/div[2]')
                carsh = self.get_text('(//span[@class="kwaishop-cs-BizGoodsCard_price"])[last()]')
                print("卡片发送", title, carsh)

            # 热销商品点击下一页
            cn = 2
            for i in range(cn):
                self.click('//li[@title="下一页"]/button/span')
                self.sleep(3)
                goods_title_next_page = self.get_text('//div[@class="goodsName"][1]')
                # 下一页商品标题，不能等于第一页的
                assert_that(goods_title_next_page != goods_title)

            # 点击商品标题，跳转至商详页
            self.click('//*[@class="kwaishop-cs-BizGoodsCard_img"][1]')
            try:
                # 点击知道了
                self.click('//button[text="知道了"]')
            except Exception as e:
                return
            self.sleep(3)
            assert_that(self.is_element_present('//span[contains(text(), "'+goods_title+'")]'))

    @pytest.mark.p1
    def test_send_history_card(self):
        """
        浏览足迹查看商品
        """
        self.go_workbrench(self, user='member_account2', id="**********")

        # 点击商品推荐
        if self.is_element_visible('//div[text()="商品推荐"]'):
            self.click('//div[text()="商品推荐"]')
            sleep(2)
            self.click('//div[text()="浏览足迹"]')
            sleep(12)
            # 获取浏览足迹-第一页-商品名称（未出现"当前用户未查询到商品"）
            if not self.is_element_present('//div[@class="empty-text"]'):
                goods_title = self.get_text('//div[@class="kope-view"][1]//span')
                goods_pages = int(self.get_text('//li[@class="ant-pagination-simple-pager"]')[1:])
                if goods_pages >= 2:
                    # 热销商品点击下一页
                    cn = min(2, goods_pages-1)
                    for i in range(cn):
                        self.click('//li[@title="下一页"]/button/span')
                        self.sleep(3)
                        goods_title_next_page = self.get_text('//div[@class="kope-view"][1]//span')
                        # 下一页商品标题，不能等于第一页的
                        assert_that(goods_title_next_page != goods_title)

    @pytest.mark.p1
    def test_send_item(self):
        """
        发送订单
        """
        self.go_workbrench(self, user='member_account2', id="**********")
        sleep(6)
        # return          # 修改发送卡片的订单编号

        # 点击订单
        PERSONAL_ORDER_TAB = '//div[text()="个人订单"]'
        self.click(PERSONAL_ORDER_TAB)
        sleep(2)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')
        # 清空输入框
        self.update_text('//input[@class="ant-input"]', '')
        sleep(3)
        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '****************'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        sleep(3)
        # 发送订单
        self.click('//span[text()="发送订单"][1]')
        sleep(6)

        # 校验订单
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        if self.is_element_present(CHAT_EDIT_TEXT):
            # 校验订单号、商品名称
            ele_title = self.driver.find_elements(by='class name', value='kwaishop-cs-MaterialTitle')[-1]
            ele_item = self.driver.find_elements(by='class name', value='kwaishop-cs-MaterialGoods_title')[-1]
            # 修改订单编号，同订单列表第一条订单编号保持一致
            pname = self.get_text('//div[@class="itemTitle"]')[:6]
            info = self.driver.find_elements(by='class name', value='kwaishop-cs-MaterialGoods_info')[-1].text
            print("【订单编号/商品信息】:", ele_title, pname, info)
            assert_that(ORDER_ID in ele_title.get_attribute('innerText'))
            assert_that('主品' in pname)
            assert_that(info == '共1件商品，实付¥0.1')

            # 链接跳出校验
            ele_title.click()
            # 如果是分销、大链接订单，做一层拦截，检查是否出现类似toast提示
            if self.is_element_visible('//*[contains(text(), "商家直发订单"]'):
                return
            try:
                # 点击知道了
                self.click('//button[text="知道了"]')
            except Exception as e:
                return
            self.sleep(15)
            assert_that(self.is_element_present('//span[contains(text(), "' + ORDER_ID + '")]'))


    @pytest.mark.p1
    def test_send_order_next(self):
        """
        根据商品名称搜索订单
        """
        self.go_workbrench(self, user='customer-z', id="2941560170")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 点击搜索
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//child::div[@class="right"]')
        self.update_text('//input[@class="ant-input"]', '测试')
        sleep(3)

        # 获取第一条订单编号
        order_id_page_1 = self.get_text('//div[@class="oid"][1]')
        print("[首页第一条订单编号]: ", order_id_page_1)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='95px')

        # 滑动最底部进行翻页，重试cnt次
        cnt = 3
        for i in range(cnt):
            # 滑动到最底部（执行js语句滚动）
            self.execute_script('document.getElementsByClassName("scrollListWrap")[0].scrollTop = 10000')
            # 页面翻页
            self.click('//div[@class="btn right  "]')
            sleep(3)
            # 获取订单编号
            order_id_page_i = self.get_text('//div[@class="oid"][1]')
            print("[当前页第一条订单编号]: ", order_id_page_i)
            assert_that(order_id_page_1 != order_id_page_i)

        # 点击取消，恢复"个人订单"tab展示
        self.click('//div[@class="cancel"]')
        sleep(1)
        assert_that(self.is_element_present('//div[contains(text(),"待付款")]'))


    @pytest.mark.p0
    def test_search_order_by_id(self):
        """
        根据订单编号搜索订单
        """
        self.go_workbrench(self, user='customer-z', id="2941560170")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入不存在的订单编号，提示"当前用户没有相关历史订单"
        self.add_text('//input[@class="ant-input"]', '12306')
        self.sleep(3)
        assert_that(self.is_element_present('//div[@class="noResult"]'))

        # 清空输入框
        self.update_text('//input[@class="ant-input"]', '')
        self.sleep(3)

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2329000026514170'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        self.sleep(3)
        if self.is_element_visible('//div[@class="oid"]'):
            assert_that(self.get_text('//div[@class="oid"]') == ORDER_ID)
            # 点击订单号，跳转到订单详情页，判断订单id是否被加载到页面中
            self.click('//div[@class="oid"]')
            try:
                # 点击知道了
                self.click('//button[text="知道了"]')
            except Exception as e:
                return
            self.sleep(3)
            assert_that(self.is_element_present('//span[text()="'+ORDER_ID+'"]'))

    @pytest.mark.p1
    def test_place_new_order(self):
        """
        下一条新订单后，校验订单信息是否存在：
        1. 校验当前订单状态标签 [未发货]
        2. 支持订单号复制
        """
        if datetime.datetime.now().hour % 6 == 7:
            # 调用接口，生成一条订单
            order_tool = OrderTools()
            order_data_res = order_tool.create_order_union(
                account="customer-ning",
                seller_id=**********,
                item_id=**************,
                sku_id=***************,
                payment_fee=1,
                address_id=************
            )
            oid = order_data_res["data"]["createOrderRes"]["tid"]
            print("下单结果为{}".format(order_data_res))
            sleep(3)
            pay_res = order_tool.pay_order_without_passwd_test(account="customer-ning", oid=oid)
            print("支付结果为{}".format(pay_res))
            sleep(3)

        self.go_workbrench(self, id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 点击待发货tab
        self.click('//div[text()="待发货"]')
        sleep(3)
        # 出现订单号，则对当前订单进行操作
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 校验当前订单状态标签：待发货
            assert_that(self.get_text('//div[contains(@class, "YELLOW-hasBack-only")]') == "待发货")

            # 复制订单号
            order_id = self.get_text('//div[@class="oid"]')
            self.click('//span[@aria-label="normal-copy-line"]')
            sleep(2)

            # 粘贴
            self.click('//div[contains(@class, "ql-editor")]')
            # ActionChains(self.driver).send_keys(Keys.PAGE_DOWN, 'v').perform()
            ele = self.driver.find_element("class name", "ql-editor")
            ele.send_keys(Keys.COMMAND, 'v')
            sleep(3)

            # 校验粘贴后的订单号
            paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
            if 'v' not in paste_text:
                assert_that(paste_text == order_id)

    @pytest.mark.p1
    def test_order_check_aftersale_info(self):
        """
        校验订单售后信息
        """
        self.go_workbrench(self, user='customer-z', id="3609491300")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='855px')

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        sleep(1)
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2324100054549300'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        sleep(3)
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 校验订单当前状态、售后状态
            assert_that(self.get_text('//div[contains(@class, "GRAY-hasBack")]') == "订单关闭")
            assert_that(self.get_text('//div[contains(@class, "order-tag-last")]') == "退款成功")
            # 点击展开按钮，展开售后信息
            # self.click('//span[@class="anticon anticon-system-arrow-large-down-line"]')
            # return      # 修改折叠按钮元素
            self.click('//div[@class="after-sale-section-container"]//child::span[@aria-label="system-arrow-large-right-line"]')
            sleep(3)
            # 获取售后单号
            aftersale_type = self.get_text('//div[text()="售后类型"]//following-sibling::div[1]')
            aftersale_id = self.get_text('//div[text()="售后单号"]//following-sibling::div[1]')
            print("【售后类型/单号】：", aftersale_type, '/', aftersale_id)

            # 复制售后单号
            self.click('//div[text()="售后单号"]//following-sibling::div[1]//child::span[2]')
            sleep(2)

            # 粘贴
            self.click('//div[contains(@class, "ql-editor")]')
            # ActionChains(self.driver).send_keys(Keys.PAGE_DOWN, 'v').perform()
            ele = self.driver.find_element("class name", "ql-editor")
            ele.send_keys(Keys.COMMAND, 'v')
            sleep(6)

            # 校验粘贴后的订单号
            # assert_that(self.get_text('//div[contains(@class, "ql-editor")]') == aftersale_id)

            # # 点击“查看历史售后”，弹出半屏提示“退款关闭”
            self.click('//span[text()="查看历史售后"]')
            sleep(2)
            self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
            sleep(5)
            assert_that(self.is_element_visible('//*[text()="退款成功"]'))
            self.click('//div[text()="查看历史售后"]//following-sibling::button[1]')

    @pytest.mark.p1
    def test_order_info_withdraw_expand(self):
        """
        订单收起与展开
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)
        # 点击待发货tab
        self.click('//div[text()="待发货"]')
        sleep(3)
        # 出现订单号，则对当前订单进行操作
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 点击收起、展开
            self.click('//div[contains(@class, "expand-up")]')
            sleep(5)
            self.click('//div[contains(@class, "expand-down")]')
            sleep(5)

    @pytest.mark.p1
    def test_order_flag_info_setting(self):
        """
        设置订单插旗备注
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单Œ
        self.click('//div[text()="个人订单"]')
        sleep(3)
        # 点击待发货tab
        self.click('//div[text()="待发货"]')
        sleep(3)
        # 出现订单号，则对当前订单进行操作
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 1-[插旗备注编辑/新增]
            # 点击订单号右侧flag
            self.click('//div[@class="noteFlag"][1]')
            sleep(2)
            # 选择黄色flag
            self.driver.execute_script("document.getElementsByClassName('ant-radio-input')[2].click()")
            # 输入备注内容，提交
            note_text = "web-ui自动化测试——待发货订单flag备注——"+str(random.randint(1,10000))
            self.input('//textarea[@class="ant-input"]', note_text)
            self.click('//span[text()="提 交"]')
            sleep(3)
            # 2-[插旗备注展示]
            # 判断备注内容是否展示在订单号下方
            # assert_that(note_text in self.get_text('//div[@class="note-text"][1]'))
            self.click('//div[@class="descContain"]//div[contains(@class,"expand")]')
            sleep(2)
            user = self.get_text('//div[@class="user"]')
            time = self.get_text('//div[@class="time"]')
            print('[备注编辑人/时间]：', user, "，", time)
            self.click('//div[@class="descContain"]//div[contains(@class,"expand")]')

    @pytest.mark.p1
    def test_order_display_paytime(self):
        """
        获取订单支付/下单时间
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单Œ
        self.click('//div[text()="个人订单"]')
        sleep(3)
        # 点击待发货tab
        self.click('//div[text()="待发货"]')
        sleep(3)
        # 出现订单号，则对当前订单进行操作
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            pay_tag = self.get_text('//div[@class="pay-tag"][1]')
            self.hover_on_element('//div[@class="pay-tag"][1]')
            order_tag = self.get_text('//div[contains(text(), "下单")]')
            print("[支付/下单时间展示]：\n", pay_tag, '\n', order_tag)

    @pytest.mark.p1
    def test_search_recommend_product(self):
        """
        商品推荐列表中，根据关键词搜索商品，并查看商品信息
        """
        self.go_workbrench(self, user="member_account2")

        # 点击商品推荐
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.click('//div[text()="全部商品"]')
        sleep(3)

        # 输入关键词搜索商品
        PRODUCT_NAME = "测试多仓库存"
        self.add_text('//input[@placeholder="请输入想要搜索的关键词"]', PRODUCT_NAME[:-2])
        sleep(3)
        if self.is_element_visible('//span[text()="'+PRODUCT_NAME+'"]'):
            # 获取商品信息
            print('[商品信息]:')
            product_sell_info = self.get_text('//div[@class="kpro-cs-cards-atom-components-CommonTagContain kpro-cs-cards-atom-components-needFlex"]/..').split('\n')
            print(product_sell_info)

            # 点击预览按钮，唤起商详页半屏
            self.click('//span[contains(text(), "预览")]')
            sleep(2)
            self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
            sleep(3)
            self.click('//button[@class="ant-drawer-close"]')
            sleep(2)

            # 点击价格，唤起规格/库存半屏
            self.click('//div[@class="price"]')
            sleep(2)
            self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
            sleep(3)
            ls = self.find_elements('//tr[contains(@class, "ant-table-row")]')
            print('[规格/库存半屏]: 商品规格/价格列表包括')
            for i in range(len(ls)):
                print(ls[i].get_attribute("innerText").split('\t'))
            self.click('//button[@class="ant-drawer-close"]')
            sleep(2)

            # 点击发送优惠券，唤起优惠券半屏
            self.click('//span[text()="发送优惠券"]')
            sleep(2)
            self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
            sleep(3)
            assert_that(self.is_element_present('//div[text()="商品券"]'))
            assert_that(self.is_element_present('//div[text()="店铺券"]'))
            if self.is_element_present('//div[text()="暂无优惠券"]'):
                print('[优惠券半屏]: 暂无优惠券')
            self.click('//button[@class="ant-drawer-close"]')
            sleep(2)

            # 点击商品图片，跳转到商详页
            self.click('//div[contains(@style,"image-kwaishop-product")]')
            try:
                # 点击知道了
                self.click('//button[text="知道了"]')
            except Exception as e:
                return
            self.sleep(3)
            assert_that(self.is_element_present('//span[text()="'+PRODUCT_NAME+'"]'))

    @pytest.mark.p0
    def test_order_info_jump_commodity_page(self):
        """
        点击订单中的商品图片/标题，跳转商详页
        """
        self.go_workbrench(self, user='customer-z', id="3609491300")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2324100054549300'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        self.sleep(3)
        if self.is_element_visible('//div[@class="oid"]'):
            commodity_title = self.get_text('//div[@class="itemTitle"]')
            # 点击商品名称，跳转到商品详情页
            self.click('//div[@class="itemTitle"]')
            try:
                # 点击知道了
                self.click('//button[text="知道了"]')
            except Exception as e:
                return
            self.sleep(3)
            assert_that(self.is_element_present('//span[text()="' + commodity_title + '"]'))

    @pytest.mark.p0
    def test_order_info_jump_aftersale_page(self):
        """
        点击订单中的售后单号，跳转售后详情页
        """
        # return          # todo: 暂时注释掉报错case
        self.go_workbrench(self, user='customer-z', id="3609491300")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='855px')
        sleep(2)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//child::div[@class="right"]')
        sleep(1)
        self.click('//span[@class="ant-select-selection-item"]')
        sleep(1)
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2324100054549300'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        self.sleep(3)
        # return              # 下拉条元素定位方式修改
        if self.is_element_visible('//div[@class="oid"]'):
            # 点击下拉条
            self.click('//div[@class="after-sale-section-container"]//child::span[@aria-label="system-arrow-large-right-line"]')
            # 访问父级节点，前一个兄弟元素
            aftersale_order_id = self.get_text('//span[@aria-label="normal-copy-line"]/../preceding-sibling::span[1]')
            # 点击售后单号，跳转售后详情页
            self.click('//span[@aria-label="normal-copy-line"]/../preceding-sibling::span[1]')
            try:
                # 点击知道了
                self.click('//button[text="知道了"]')
            except Exception as e:
                return
            self.sleep(3)
            assert_that(self.is_element_present('//*[text()="' + aftersale_order_id + '"]'))

    @pytest.mark.p1
    def test_order_display_commodity_info(self):
        """
        查看订单对应的商品信息
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2405000026392229'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        sleep(3)
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 校验商品名称
            assert_that("测试容器商品" in self.get_text('//div[@class="itemTitle"]'))
            # 校验商品图片url不为空
            assert_that(len(self.get_attribute('//div[@class="goodsCard"]//img', 'src')) > 0)
            # 获取规格、原价
            sku = self.get_text('//div[@class="skuInfo"]//span[@class="skuContain"]')
            item_price = self.get_text('//div[@class="itemPrice"]')
            print("[商品规格/原价]: ", sku, ",", item_price)
            # 获取实付、实收价格
            real_pay_price = self.get_text('//span[@class="realPayPrice"]')
            net_receipts_price = self.get_text('//span[@class="netReceiptsPrice"]')
            print("[商品实付/实收价]: ", real_pay_price, ",", net_receipts_price)

    def test_order_display_logistics_info(self):
        """
        查看订单对应的物流信息
        """
        self.go_workbrench(self, id="**********")
        # return      # todo: 复制按钮图标改文字，元素定位.
        # 修改

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2405000034905229'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        sleep(3)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='295px')

        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # [1] 如果物流区呈收起状态，需要将其展开
            if not self.is_element_present('//div[text()="收货信息"]'):
                self.click('//div[@class="logisticInfoTitle"]/img')
                sleep(5)
            # [2] 加密状态下收货信息复制
            self.click('//div[@class="receiver-value-wrapper"]//span[@class="copy"]')
            # 粘贴
            self.click('//div[contains(@class, "ql-editor")]')
            ele = self.driver.find_element("class name", "ql-editor")
            ele.send_keys(Keys.COMMAND, 'v')
            sleep(6)
            if "v" in self.get_text('//div[contains(@class, "ql-editor")]'):
                return
            if "*" in self.get_text('//div[contains(@class, "ql-editor")]'):
                self.update_text('//div[contains(@class, "ql-editor")]', '')
                # 复制的是加密内容，需要将其解密后再次复制
                self.click('//span[@class="kpro-info-decryption-icon"]')
                sleep(2)
                # 解密失败
                if self.is_element_visible('//*[text()="解密失败"]'):
                    self.click('//*[text()="知道了"]')
                # 解密成功
                else:
                    self.click('//span[@class="receiver-value-wrapper"]//span[@class="copy"]')
                    # 粘贴
                    self.click('//div[contains(@class, "ql-editor")]')
                    ele = self.driver.find_element("class name", "ql-editor")
                    ele.send_keys(Keys.COMMAND, 'v')
                    # 解密内容未带星号
                    assert_that("*" not in self.get_text('//div[contains(@class, "ql-editor")]'))
            # [3] 查看发货时间
            delivery_time = self.get_text('//div[@class="consigneeHoverValue"]')
            print('[发货时间]：', delivery_time)
            # [4] 复制物流状态
            self.click('//div[@class="consigneePackageList"]//span[contains(@class, "anticon")]')
            # 粘贴
            self.click('//div[contains(@class, "ql-editor")]')
            ele = self.driver.find_element("class name", "ql-editor")
            ele.send_keys(Keys.COMMAND, 'v')
            # 校验物流运单号是否被粘贴上
            assert_that("spsmt123fd1234ds" in self.get_text('//div[contains(@class, "ql-editor")]'))
            # [5] 点击“全部物流”，出现物流弹窗
            self.click('//span[text()="全部物流信息"]')
            sleep(3)
            assert_that(self.is_element_present('//div[text()="查看物流"]'))

    @pytest.mark.p1
    def test_order_click_button_aftersale(self):
        """
        订单详情：点击代申售后按钮
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)
        # 点击待发货tab
        self.click('//div[text()="待发货"]')
        sleep(3)
        # 出现订单号，则对当前订单进行操作
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 点击代申售后按钮
            self.click('//span[text()="代申售后"][1]')
            sleep(2)
            # “仅退款”：选择退款原因
            assert_that(self.is_element_visible('//div[text()="仅退款"]'))
            self.click('//div[@class="ant-drawer-body"]//span[@class="ant-select-selection-search"]//input')
            self.click('//div[text()="我不想要了"]')
            sleep(3)
            # self.click('//input[@id="rc_select_4"]')
            # self.click('//div[text()="不喜欢"]')

    @pytest.mark.p1
    def test_order_click_button_small_repayment(self):
        """
        订单详情：点击小额打款按钮
        """
        return      # 账号密码修改，暂时注释掉case
        self.go_workbrench(self, user='funds_account_01', id="*********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 点击待发货tab【假设小额打款订单必出现】
        self.click('//div[text()="待发货"]')
        sleep(3)

        # 点击小额打款按钮
        self.click('//span[text()="小额打款"][1]')
        # 未出现小额打款toast报错
        for i in range(3):
            assert_that(not self.is_element_visible('//*[contains(@class, "ant-message-error")]'))
            sleep(1)

        # 挪动半屏位置
        if self.is_element_visible('//div[contains(@class, "ant-drawer-open")]'):
            self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
            sleep(5)

        # 选择小额打款原因
        self.click('//span[@class="kwaishop-seller-fund-launch-small-payment-dialog-pc-radio"][1]')
        self.input('//input[@class="kwaishop-seller-fund-launch-small-payment-dialog-pc-input-number-input"]', "1")
        # 点击确认: 提示请选择出款账户必填项提示
        if self.is_element_visible('//*[text()="确 认"]'):
            self.click('//*[text()="确 认"]')
        else:
            self.click('//button[@class="kwaishop-seller-fund-launch-small-payment-dialog-pc-btn kwaishop-seller-fund-launch-small-payment-dialog-pc-btn-primary"]')
        sleep(2)
        #todo：保证金阈值修改，恢复后再去除注释
        # assert_that(self.is_element_visible('//*[contains(text(), "请选择出款账户")]'))
        # assert_that(not self.is_element_visible('//*[contains(text(), "请选择打款类型")]'))
        # assert_that(not self.is_element_visible('//*[contains(text(), "请选择打款金额")]'))
        # 点击取消
        self.click_if_visible('//*[text()="取 消"]')

    @pytest.mark.p1
    def test_order_click_button_change_address(self):
        """
        订单详情：点击改地址按钮
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)
        # 点击待发货tab
        self.click('//div[text()="待发货"]')
        sleep(3)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='295px')

        # 出现订单号，则对当前订单进行操作
        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 点击改地址按钮
            if not self.is_element_visible('//span[text()="改地址"][1]'):
                return
            self.click('//span[text()="改地址"][1]')
            sleep(3)
            # 出现弹窗
            assert_that(self.is_element_present('//div[text()="修改地址"]'))

    @pytest.mark.p1
    def test_order_click_button_change_sku(self):
        """
        订单详情：点击改规格按钮
        """
        self.go_workbrench(self, id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2405000026392229'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        sleep(3)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='295px')

        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            # 点击改规格按钮
            if not self.is_element_visible('//span[text()="改规格"][1]'):
                return
            self.click('//span[text()="改规格"][1]')
            sleep(2)
            assert_that(self.is_element_present('//div[text()="修改订单规格"]'))
            # 获取当前规格[中号]：价格
            old_price = self.get_text('//span[@class="price-style"]')
            self.click('//span[text()="大号"]')
            sleep(2)
            # 获取当前规格[大号]：价格
            new_price = self.get_text('//span[@class="price-style"]')
            if new_price != old_price:
                assert_that(self.is_element_visible('//div[contains(text(), "订单商品价格不一致")]'))
            else:
                assert_that(not self.is_element_visible('//div[contains(text(), "订单商品价格不一致")]'))

    @pytest.mark.p1
    def test_hide_secondary_bar(self):
        """
        右上角隐藏二级标签，例如“客服应用”
        """
        self.go_workbrench(self, user='member_account2')

        # 点击右上角二级标签按钮（刷新按钮左边）
        self.click('//div[@class="edit-button-text"]')
        # 隐藏按钮
        self.driver.execute_script('document.getElementsByClassName("ant-checkbox")[3].click()')

        # 点击右上角二级标签按钮（刷新按钮左边）
        self.click('//div[@class="edit-button-text"]')
        # 显示按钮
        self.driver.execute_script('document.getElementsByClassName("ant-checkbox")[3].click()')

    @pytest.mark.p1
    def test_open_aftersale_negotiation_frame(self):
        """
        打开售后协商弹窗
        """
        self.go_workbrench(self, user='huwen', id="**********")

        # 点击个人订单
        self.click('//div[text()="个人订单"]')
        sleep(3)

        # 将筛选条件修改为：订单编号
        self.click('//*[@id="OrderListDom"]//div[@class="FilterBar"]//div[@class="right"]')
        self.click('//span[@class="ant-select-selection-item"]')
        self.click('//div[@title="订单编号"]')

        # 输入已存在的订单编号，返回查询结果
        ORDER_ID = '2422200105801477'
        self.update_text('//input[@class="ant-input"]', ORDER_ID)
        sleep(3)

        # 将经营助手挪下位置
        self.move_seller_helper_box(bottom='395px')

        if self.is_element_present('//div[@class="topLeft"]//div[@class="oid"]'):
            self.click('//span[text()="待卖家处理"]')
            sleep(4)
            self.click('//span[text()="修改协商方案"]')
            sleep(6)
            assert_that(self.is_element_visible('//div[text()="协商方案设置"]'))
            # assert_that(self.is_element_visible('//div[contains(text(), "若接受您的协商方案，将自动同意售后")]'))
            assert_that(self.is_element_visible('//span[text()="仅退款"]'))
            assert_that(self.is_element_visible('//span[text()="买家已收到货"]'))
            assert_that(self.is_element_visible('//span[text()="买卖双方协商一致退款"]'))
            assert_that(self.is_element_visible('//span[contains(text(), "0.01")]'))


























