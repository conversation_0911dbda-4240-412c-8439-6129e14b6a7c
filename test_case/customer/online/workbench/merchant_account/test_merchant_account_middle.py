import random
import time
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory
from selenium.webdriver import Keys, ActionChains

@ddt
class TestMerchantAccountMiddle(BaseTestCase):

    # 发送文本消息
    @pytest.mark.p0
    def test_text_message(self):
        self.go_workbrench(self)
        self.sleep(2)
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        if self.is_element_present(CHAT_EDIT_TEXT):
            # 聊天区输入消息内容
            self.add_text(CHAT_EDIT_TEXT, "测试发送")
            # 消息发送
            self.click(CHAT_SEND_BTN)
            assert_that(self.is_element_present('//*[text()="测试发送"]'))
            assert_that(self.is_element_present('//*[text()="已读"]') or self.is_element_present('//*[text()="未读"]'))

    @pytest.mark.p1
    @pytest.mark.skip
    def test_view_text_send(self):
        self.go_workbrench(self, user='syt_account_admin', id="**********")
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        # 输入
        if self.is_element_present(CHAT_EDIT_TEXT):
            self.add_text(CHAT_EDIT_TEXT, "老铁")
            sleep(1)
            # 下滑
            # self.scroll_down_key(2)
            self.add_text(CHAT_EDIT_TEXT, "测试发送")
            # 消息发送
            self.click(CHAT_SEND_BTN)
            res = self.get_text(CHAT_EDIT_TEXT)
            assert_that(res == "", "")

    @pytest.mark.p1
    def test_view_text_withdraw(self):
        """
        消息撤回
        """
        self.go_workbrench(self, id="**********")         # **********
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        # 输入
        if self.is_element_present(CHAT_EDIT_TEXT):
            # aa：消息发送，2min内撤回
            self.add_text(CHAT_EDIT_TEXT, "web自动化测试_任意消息_aa")
            sleep(1)
            # 消息发送
            self.click(CHAT_SEND_BTN)
            sleep(3)
            # 如果消息能成功发送，无错误按钮
            if not self.is_element_present('//div[@class="kwaishop-cs-messageState_error"]'):
                if self.ENV == "prt":
                    # 右键，选择消息撤回（index=1）
                    ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
                    ActionChains(self.driver).context_click(ele).perform()
                    sleep(3)
                    self.click("//span[contains(text(), '撤回')]")
                    sleep(2)
                    assert_that(self.is_element_present("//div[text()='你撤回了一条消息']"))

    @pytest.mark.p1
    def test_view_text_delete(self):
        """
        消息删除
        """
        self.go_workbrench(self, id="1601434312")
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        # 输入
        if self.is_element_present(CHAT_EDIT_TEXT):
            # bb：消息发送，2min内撤回
            self.add_text(CHAT_EDIT_TEXT, "web自动化测试_任意消息_bb")
            sleep(1)
            # 消息发送
            self.click(CHAT_SEND_BTN)
            sleep(2)
            if self.ENV == "prt":
                # 右键，选择消息撤回（index=1）
                ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
                ActionChains(self.driver).context_click(ele).perform()
                sleep(3)
                self.click("//span[contains(text(), '删除')]")
                sleep(2)
                self.click('//span[text()="删 除"]')

    @pytest.mark.p1
    def test_check_realdata_queue_customer_count(self):
        """
        当前排队人数：展示+hover
        """
        self.go_workbrench(self, user='customer-z', id="**********")

        # 排队人数 > 0
        res = self.get_text('//span[contains(text(),"去处理")]/parent::div/parent::div/div')
        assert_that(res >= "0", "")

        # 排队人数icon：hover
        #   (class: DataCard cs-queue-num -> top -> icon)
        self.hover_on_element('//div[@class="DataCard cs-queue-num"][1]//div[1]//div[2]')
        #   关键词 "排队人数表示" 出现在toast弹窗中
        assert_that(self.is_element_present('//div[contains(text(), "排队人数表示")]'))

    @pytest.mark.p1
    def test_check_realdata_3min_reply_rate(self):
        """
        人工3分钟回复率：展示+hover，小屏需要展开后缩回
        """
        self.go_workbrench(self, user='customer-z', id="**********")

        # 展开实时数据概览
        self.click('//div[@class="rightBottom"]//img')
        # 获取实时数据文本
        elmts = self.find_elements('//div[@class="value"]')
        # 元素值校验判断
        if len(elmts) >= 3:
            assert_that('人' in elmts[0].text and int(elmts[0].text[:-1]) >= 0)       # 排队人数
            assert_that(0 <= float(elmts[1].text.replace('%', '')) <= 100)              # 3min回复率
            assert_that(int(elmts[2].text) >= 0)                                      # 未回复人数
            print(elmts[3].text)                                                      # 平均回复时长
        elif len(elmts) == 1:
            assert_that('人' in elmts[0].text and int(elmts[0].text[:-1]) >= 0)  # 排队人数
        # 缩回实时数据概览
        self.click('//div[@class="rightBottom"]//img')

    @pytest.mark.p1
    def test_check_order(self):
        """
        核单卡片展示
        todo: 核单卡片等特殊类型卡片补充在ui自动化用例名称里面
        """
        self.go_workbrench(self, id="1469353229")
        if self.is_element_visible('(//div[@class="kwaishop-cs-BizOrderCard defaultBorderCard"])[last()]/div'):
            res = self.get_text('(//div[@class="kwaishop-cs-BizOrderCard defaultBorderCard"])[last()]/div')
            print(res)
            # 确保最后一条消息是核单卡片消息
            if res and len(res) > 0:
                assert_that("订单" in res and "老铁" in res)
                # assert_that(res == "老铁，请您核对订单信息", "")
                assert_that(self.is_element_visible('//div[contains(text(), "共1件商品")]'))
                self.click('//div[contains(text(), "共1件商品")]')
                sleep(2)
                current_url = self.driver.execute_script("return document.URL")
                assert_that("order/detail?id=" in current_url)


    @pytest.mark.p0
    def test_batch_transfer(self):
        """
        批量转移会话列表中的买家
        """
        self.go_workbrench(self)
        return

        # 批量转移选择买家：优先转移已超时买家
        self.click('//span[text()="批量转移"]')
        count_over_time_reply = int(self.get_text('//div[contains(text(), "已超时")]//following::div[1]')[1:-1])
        if count_over_time_reply > 0:
            # [箭头图标]：缩回3min回复分组列表
            if 'down' in self.get_attribute('//div[text()="请在3分钟内回复"]/preceding::span[1]', 'aria-label'):
                self.click('//div[text()="请在3分钟内回复"]/preceding::span[1]')
                sleep(1)
                assert_that('right' in self.get_attribute('//div[text()="请在3分钟内回复"]/preceding::span[1]', 'aria-label'))
            # [箭头图标]：展开已超时分组列表
            if 'right' in self.get_attribute('//div[text()="已超时"]/preceding::span[1]', 'aria-label'):
                self.click('//div[text()="已超时"]/preceding::span[1]')
                assert_that('down' in self.get_attribute('//div[text()="请在3分钟内回复"]/preceding::span[1]', 'aria-label'))
            # 点击已超时底下的第一个买家
            self.driver.execute_script('document.getElementsByClassName("ant-checkbox")[3].click()')
            self.click('//span[text()="转移"]')
            sleep(8)
            # 打开客服转移弹窗
            self.input('//input[@placeholder="请输入客服昵称"]', 'hkk子账号')
            sleep(8)
            self.click('//td//child::span[text()="转移"]')
            sleep(1)
            self.click('//span[text()="发送"]')       # 默认选择第一原因：催发货
            sleep(5)
            if not self.is_element_present('//*[text()="转移成功"]'):
                assert_that(not self.is_element_visible('//div[text()="转移会话"]'))


    @pytest.mark.p1
    def test_display_buyer_avatar(self):
        """
        展示买家昵称、头像、标签
        """
        self.go_workbrench(self, user='customer-z', id="2234661335")

        BUYER_NAME = "sha"       # "shakalaka"
        # 买家昵称、头像校验
        assert_that("." in self.get_attribute('//div[@class="LoginUserInfo"]//span[1]//img[1]', 'src'))
        # assert_that(BUYER_NAME in self.get_text('//div[@class="LoginUserInfo-mainTitle-text"]'))
        # 获取买家标签
        elmts = self.find_elements('//span[contains(@class, "ant-tag-blue")]')
        flags = ""
        if len(elmts) > 0:
            for i in range(len(elmts)):
                flags += (elmts[i].text + ",")
        # assert_that("店铺高净值用户" in flags)
        print("[用户标签]:", flags)

    @pytest.mark.p1
    def test_buyer_session_transfer(self):
        """
        买家点击转移会话按钮，唤起弹窗
        """
        self.go_workbrench(self, user='customer-z', id="**********")

        # 打开弹窗
        self.click('//img[contains(@src, "session-transfer")]')
        sleep(2)
        # 输入“11”搜索分组
        self.click('//div[text()="转接到组"]')
        self.input('//input[@placeholder="请输入分组名称进行搜索"]', '11')
        sleep(2)
        # 弹出分组结果，具有转移按钮
        self.is_element_present('//span[text()="转移"]')

    @pytest.mark.p1
    def test_report_buyer(self):
        """
        举报买家
        """
        self.go_workbrench(self, user='customer-z', id='1275816963')
        sleep(2)

        # 点击举报图标，唤起弹窗
        self.click('//img[contains(@src, "seller_report")]')
        sleep(4)
        # return
        # 弹窗内选择举报原因，并选择证据
        self.click('//div[@class="ant-modal-content"]//child::div[@class="ant-select-selector"]')
        sleep(1)
        self.click('//*[text()="发送无意义刷屏信息"]')
        self.input('//textarea[@placeholder="请输入举报描述"]', 'web自动化测试123456')
        self.click('//span[contains(text(), "客服当前忙碌中")]/preceding-sibling::*[1]')
        self.click('//span[text()="选择证据"]')
        sleep(8)
        # 选择聊天记录证据，关闭弹窗
        # self.click('//*[@id="0_1275816963_1275816963_17101430879650070-contain"]/label/span')
        self.click('//span[contains(text(), "取消举报")]')
        sleep(5)
        # assert_that(not self.is_element_visible('//*[@id="0_1275816963_1275816963_17101430879650070-contain"]/label/span'))

    @pytest.mark.p1
    def test_send_basic_emo(self):
        """
        发送文字表情、动态表情
        """
        self.go_workbrench(self, id='1601262451')
        sleep(2)

        # 发送文字表情
        BASIC_EMO = '//div[@class="emoji-item"]/img[contains(@src, "s1296748052.png")]'
        BASIC_EMO_SEND = '//img[contains(@src, "s1296748052.png")]'
        self.click('//img[contains(@src, "sendbar-emoji.svg")]')
        sleep(2)
        if self.is_element_present(BASIC_EMO):
            self.click(BASIC_EMO)
            self.click('//span[text()="发送 Enter"]')
            sleep(3)
            assert_that(self.is_element_present(BASIC_EMO_SEND))
        self.click('//div[@class="SendBox-Editor"]')

        # 发送动态表情
        DYNAMIC_EMO = '//div[@class="preview-container"]/img[contains(@src, "1748497233407third_party_s2357909510.png")]'
        DYNAMIC_EMO_SEND = '//img[contains(@src, "emotion/1748497233471third_party_b2357909510.gif")]'
        self.click('//img[contains(@src, "sendbar-emoji.svg")]')
        sleep(2)
        if self.is_element_present('//img[contains(@src, "emotion-thirdParty-icon.png")]'):
            self.click('//img[contains(@src, "emotion-thirdParty-icon.png")]')
            sleep(4)
            if self.is_element_present(DYNAMIC_EMO):
                self.click(DYNAMIC_EMO)
                self.click('//div[@class="SendBox-Editor"]')
                sleep(2)
                assert_that(self.is_element_present(DYNAMIC_EMO_SEND))

    @pytest.mark.p1
    def test_check_pic_and_video(self):
        """
        查看聊天记录中的图片、视频
        """
        self.go_workbrench(self, id='1275816964')
        sleep(2)

        # 查看图片
        if self.is_element_present('//div[@class="kwaishop-cs-BizImageCard"]'):
            self.click('//div[@class="kwaishop-cs-BizImageCard"]')
            sleep(3)
            assert_that(self.is_element_present('//img[@class="ant-image-preview-img"]'))
            self.click('//span[@aria-label="system-close-large-line"]')

        # 将聊天记录上翻，拖动到视频处
        self.execute_script('document.getElementById("ChatMessageList-ContentWrap").scrollTo(0,1400)')
        sleep(2)

        # 查看视频
        if self.is_element_present('//div[@class="kwaishop-cs-BizVideoCard_videoCard"]'):
            self.click('//div[@class="kwaishop-cs-BizVideoCard_videoCard"]')
            sleep(5)
            assert_that(self.is_element_present('//video'))
            self.click('//span[@aria-label="system-close-large-line"]')

    @pytest.mark.p1
    def test_custom_emo_add_and_remove(self):
        """
        添加、删除自定义表情
        """
        self.go_workbrench(self, id='1275816964')
        sleep(2)

        # 如果图片存在（图片可被添加到自定义表情）
        if self.is_element_present('//div[@class="kwaishop-cs-BizImageCard"]'):
            # 右键：添加到表情包
            ele = self.find_elements('//div[@class="kwaishop-cs-BizImageCard"]')[-1]
            ActionChains(self.driver).context_click(ele).perform()
            self.click('//span[text()="添加到表情包"]')

            # 搜索刚添加到的自定义表情
            self.click('//img[contains(@src, "sendbar-emoji.svg")]')
            sleep(2)
            self.click('//img[contains(@src, "emotion-favorite-icon.png")]')
            sleep(2)

            # 右击刚才添加的表情，进行删除
            if self.is_element_present('//div[@class="preview-emotion-container"]'):
                ele = self.find_elements('//div[contains(@class, "preview-emotion-container"])')[-1]
                ActionChains(self.driver).context_click(ele).perform()
                self.click('//span[text()="删除"]')
                sleep(2)

    @pytest.mark.p1
    def test_coupon_display(self):
        """
        展示优惠券（店铺券/商品券）
        todo: 如果优惠券有展示，需要对其进行发送（max有次数限制）
        """
        self.go_workbrench(self, id='1615199887')
        sleep(2)

        # 点击优惠券图标
        self.click('//img[contains(@src, "coupon")]')

        # 在弹窗内分别点击店铺券、商品券
        self.click('//div[text()="店铺券"]')
        sleep(2)
        if self.is_element_present('//div[contains(text(), "暂无优惠券")]'):
            pass

        # 在弹窗内分别点击店铺券、商品券
        self.click('//div[text()="商品券"]')
        sleep(2)
        if self.is_element_present('//div[contains(text(), "暂无优惠券")]'):
            pass

    @pytest.mark.p0
    def test_evaluation_card_send(self):
        """
        (向买家)发送评价卡片
        """
        self.go_workbrench(self, id='1615199887')
        sleep(2)

        # 点击评价图标
        self.click('//img[contains(@src, "feedback.svg")]')
        # 如果弹出的是系统消息，获取系统消息内容后输出
        if self.is_element_present('//div[@class="kwaishop-cs-MessageNoticeCard_text"]'):
            ele = self.find_elements('//div[@class="kwaishop-cs-MessageNoticeCard_text"]')[-1]
            print("[不能发送评价卡片，具体原因]：", ele.text)

    @pytest.mark.p1
    def test_display_chat_record_history_half_screen(self):
        """
        弹出聊天记录半屏
        """
        self.go_workbrench(self, user='customer-z', id="2941560170")

        # 点击聊天记录图标弹出半屏
        self.click('//img[contains(@src, "chatRecord.svg")]')
        sleep(2)

        # 获取买卖头像/昵称
        if self.ENV == "prt":
            if self.get_text('//span[@class="kwaishop-cs-LayoutDefaultWrapper_sendItem"]') == "Z":
                print('[卖家昵称]：Z')
            if self.get_text('//span[@class="kwaishop-cs-LayoutDefaultWrapper_sendItem"]') == "西门":
                print('[买家昵称]：西门')

        # 聊天记录半屏上下滑动
        self.execute_script('document.getElementsByClassName("rc-virtual-list-holder")[0].scrollTo(0,100)')
        sleep(2)
        self.execute_script('document.getElementsByClassName("rc-virtual-list-holder")[0].scrollTo(0,3000)')
        sleep(2)

        # 根据关键词“你好”，查询聊天记录
        KEY_CHAT_WORD = "你好"
        self.input('//input[@placeholder="搜索3个月内的聊天记录"]', KEY_CHAT_WORD)
        self.click('//button[contains(@class, "ant-input-search-button")]')
        return          # todo: 暂时注解掉报错case

        # 搜索结果点击“你好”，定位外层聊天区上下文
        self.click('//div[text()="'+KEY_CHAT_WORD+'"]')
        sleep(2)
        self.click('//span[@aria-label="system-close-small-line"]')
        # 预期结果：聊天记录上下文出现”你好“
        assert_that(self.is_element_present('//div[text()="'+KEY_CHAT_WORD+'"]'))

    @pytest.mark.p1
    def test_history_half_screen_search_by_time(self):
        """
        弹出聊天记录半屏，按时间搜索聊天记录
        """
        self.go_workbrench(self, user='customer-z', id="2941560170")

        # 点击聊天记录图标弹出半屏
        self.click('//img[contains(@src, "chatRecord.svg")]')
        sleep(2)

        # 选择日期
        if self.is_element_visible('//div[@class="ant-picker-input"]'):
            self.click('//div[@class="ant-picker-input"]')
            self.click('//a[text()="今天"]')

            # 获取聊天记录时间
            if self.ENV == "prt":
                recent_time = self.get_text('//span[contains(@class, "kwaishop-cs-LayoutDefaultWrapper__time")]')
                print('[聊天记录上下文时间]：', recent_time)

    @pytest.mark.p1
    def test_history_half_screen_display_pic_video(self):
        """
        弹出聊天记录半屏，展示图片/视频聊天记录
        """
        self.go_workbrench(self, user='customer-z', id="2941560170")
        sleep(2)

        # 点击聊天记录图标弹出半屏，选择“图片/视频”
        self.click('//img[contains(@src, "chatRecord.svg")]')
        self.click('//div[text()="图片/视频"]')
        sleep(2)

        # 点击图片，在当前页面加载
        if self.is_element_present('//div[@class="kwaishop-cs-BizImageCard"]'):
            self.click('//div[@class="kwaishop-cs-BizImageCard"]')
            sleep(2)
            assert_that(self.is_element_present('//img[@class="ant-image-preview-img"]'))
            self.click('//span[@aria-label="system-close-large-line"]')

    @pytest.mark.p1
    def test_message_ocr(self):
        """
        主账号：图像ocr识别
        """
        self.go_workbrench(self, id='4333029841')
        sleep(2)

        # 买家发给卖家的图（手机界面）
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        self.click("//span[text()='图片识别']")
        sleep(8)
        self.execute_script('document.getElementById("ChatMessageList-ContentWrap").scrollTo(0,10000)')
        self.click_if_visible('//div[text()="复制全部文字"]')
        sleep(2)
        # 粘贴（ocr识别结果，粘贴后必须有文字）
        self.click('//div[contains(@class, "ql-editor")]')
        ele = self.driver.find_element("class name", "ql-editor")
        ele.send_keys(Keys.COMMAND, 'v')
        sleep(6)
        paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
        assert_that(len(paste_text) > 0)
        self.input('//div[contains(@class, "ql-editor")]', '')

        # 卖家发给买家的图（带文字的实体物品）
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-2]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        self.click("//span[text()='图片识别']")
        sleep(8)
        self.click_if_visible('//div[text()="复制全部文字"]')
        # 粘贴（ocr识别结果，粘贴后必须有文字）
        self.click('//div[contains(@class, "ql-editor")]')
        ele = self.driver.find_element("class name", "ql-editor")
        ele.send_keys(Keys.COMMAND, 'v')
        sleep(6)
        paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
        assert_that(len(paste_text) > 0)

