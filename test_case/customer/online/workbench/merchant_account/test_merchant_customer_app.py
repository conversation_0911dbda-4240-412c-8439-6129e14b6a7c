import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestMerchantAccountCustomerApp(BaseTestCase):

    @pytest.mark.p1
    def test_tripartite_app_display(self):
        """
        客服工作台-客服应用
        """
        self.go_workbrench(self, user="member_account2")

        # 点击客服应用
        if self.is_element_present('//div[text()="客服应用"]'):
            self.click('//div[text()="客服应用"]')
            sleep(3)
            # title = self.get_text('//div[contains(text(),"快麦")]')
            # assert_that(title, contains_string("快麦工单-客服"), "")


    @pytest.mark.p2
    def test_tripartite_app_not_display(self):
        """
        客服工作台-客服应用(不展示)
        """
        self.go_workbrench()
        if self.is_element_present('//div[contains(text(), "登录信息已经失效")]'): return

        # 点击客服应用
        self.click('//div[text()="客服应用"]')
        sleep(3)
        # 出现“还未设置客服应用”toast
        assert_that(self.is_element_present('//p[@class="cs-plugin-empty-notice"]'))


    @pytest.mark.p1
    def test_click_left_down_refresh_button(self):
        """
        点击左下角刷新按钮，数据+左侧+右侧区域刷新
        """
        self.go_workbrench()
        # 点击快捷回复
        self.click('//div[text()="全部会话"]')
        self.click('//div[text()="快捷回复"]')
        sleep(3)
        # 获取当前排队人数
        queuing_cnt_old = self.get_text('//div[@class="bottom"][1]//div[@class="value"]')

        # 点击刷新按钮
        self.click('//img[contains(@src, "SystemRefressinglearrowhLine")]')
        sleep(3)

        # 左侧会话区域：获取全部会话第一条tab买家昵称
        self.click('//*[text()="全部会话"]')
        sleep(3)
        buyer_name_new = self.get_text('//div[@class="SessionBaseCard-topHeadName"][1]')
        print('[左侧会话区域]：第一条tab买家昵称是['+buyer_name_new+']')
        # 顶部数据区：判断排队人数是否变化
        queuing_cnt_new = self.get_text('//div[@class="bottom"][1]//div[@class="value"]')
        if queuing_cnt_old == queuing_cnt_new:
            print('[顶部数据区域]：排队人数未发生变化，当前['+queuing_cnt_new+']')
        else:
            print('[顶部数据区域]：排队人数发生变化，当前['+queuing_cnt_new+'],刷新前['+queuing_cnt_old+']')
        # 右侧快捷回复区域
        self.click('//div[text()="快捷回复"]')
        sleep(2)
        fast_reply_group_name = self.get_text('//div[@class="ant-collapse-header"][1]')
        print('[右侧快捷回复区域]：第一条快捷回复分组是['+fast_reply_group_name+']')


    @pytest.mark.p1
    def test_click_right_top_refresh_button(self):
        """
        点击右上角刷新按钮，右侧区域刷新
        """
        self.go_workbrench()
        # 点击快捷回复
        self.click('//div[text()="全部会话"]')
        self.click('//div[text()="快捷回复"]')
        sleep(3)

        # 点击刷新按钮
        self.click('//div[@class="rightRefresh"]')
        sleep(3)

        # 右侧快捷回复区域
        fast_reply_group_name = self.get_text('//div[@class="ant-collapse-header"][1]')
        print('[右侧快捷回复区域]：第一条快捷回复分组是[' + fast_reply_group_name + ']')


    @pytest.mark.p1
    def test_click_left_down_download_button(self):
        """
        点击左下角下载按钮，下载windows端app
        """
        self.go_workbrench()
        sleep(3)
        return      # todo: 修改下载弹窗校验方式

        # 点击刷新按钮
        self.click('//img[contains(@src, "myrozpkdvg")]')
        sleep(3)
        # 出现弹窗，判断下载按钮是否存在
        assert_that(self.is_element_present('//span[text()="立即下载"]'))


