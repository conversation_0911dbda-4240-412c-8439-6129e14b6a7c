import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase, judge_in_seconds
from utils.time_help import *
from hamcrest import *


@ddt
class TestMerchantAccountSession(BaseTestCase):

    # 转移会话
    @skip
    @pytest.mark.p1
    def test_transfer_session(self):
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", "customer_merchant")
        self.assert_text("客服管理", "li[id= 'node_menu_for_intersectionObserver_6']")
        # print("current_url1:", self.get_current_url())
        self.click("li[id= 'menu_item_21']")  # 点击进入小店客服工作台
        self.sleep(1)
        self.switch_to_window(1)
        self.check_if_page_404()

        if self.get_title() == "快手小店客服":
            self.assert_element('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]')
        else:
            self.MerchantAccountLoginBySmscode("CUSTOMER_DOMAIN", "customer_merchant")

        self.maximize_window()
        sleep(1)
        # 点击全部tab
        self.click('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]/div')
        sleep(1)
        # 点击批量转移
        self.click('#entry_pc > div.left-area > div > div.container > div.sort-comp.new-class > p')
        sleep(0.5)
        # 点击取消
        self.click('#entry_pc > div.left-area > div > div.transfer-bar > div.batch-btns > span.text-btn.cancel')
        sleep(0.5)
        # 点击批量转移
        self.click('#entry_pc > div.left-area > div > div.container > div.sort-comp.new-class > p')
        sleep(0.5)

        try:
            # 选择转移的会话
            self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[' + str(
                random.randint(1, 2)) + ']/div/div[1]/label')
        except Exception as e:
            # 选择转移的会话
            self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[1]/div/div[1]/label')
        sleep(0.1)
        # 点击转移
        self.click('//*[@id="entry_pc"]/div[1]/div/div[4]/div[2]/span[2]')
        sleep(1)
        self.assert_text("转移会话", '//*[@id="entry_pc"]/div[4]/div/div/div[1]/span')
        sleep(0.5)
        # 点击弹窗左上角x
        self.click('#entry_pc > div.modal_item_transfer > div > div > div.el-dialog__header > button > i')
        sleep(1)
        # 点击转移
        self.click('//*[@id="entry_pc"]/div[1]/div/div[4]/div[2]/span[2]')
        sleep(1)
        self.assert_text("转移会话", '//*[@id="entry_pc"]/div[4]/div/div/div[1]/span')
        sleep(0.5)
        # 点击弹窗右下角完成
        self.click('#entry_pc > div.modal_item_transfer > div > div > div.el-dialog__footer > div > button')
        sleep(1)
        # 点击取消
        self.click('#entry_pc > div.left-area > div > div.transfer-bar > div.batch-btns > span.text-btn.cancel')
        sleep(1)

    # 标记买家会话&&取消标记买家会话
    @skip
    @pytest.mark.p2
    def test_mark_buyer_session(self):
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", "customer_merchant")
        self.assert_text("客服管理", "li[id= 'node_menu_for_intersectionObserver_6']")
        # print("current_url1:", self.get_current_url())
        self.click("li[id= 'menu_item_21']")  # 点击进入小店客服工作台
        self.sleep(1)
        self.switch_to_window(1)
        self.check_if_page_404()

        if self.get_title() == "快手小店客服":
            self.assert_element('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]')
        else:
            self.MerchantLoginBySmscode("CUSTOMER_DOMAIN", "detest017_76")
        self.maximize_window()
        sleep(1)
        # 点击全部
        self.click('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]/div')
        sleep(1)
        # 悬浮
        self.hover_on_element('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[1]')
        sleep(1)
        # 悬浮
        self.hover_on_element(
            '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-allSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(1) > div > div > span > span > svg')
        sleep(0.5)
        # 获取标记
        content = self.find_element(
            '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-allSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(1) > div > div > span > span > svg > g > path:nth-child(1)')
        sleep(0.1)
        # 获取标记颜色
        cur_mark_color = content.get_attribute('fill')
        # 判断是否是红黄蓝绿，代表已经标记着
        print("cur_mark_color:", cur_mark_color)
        if cur_mark_color != "#cccccc" and cur_mark_color in ["#FA4E3E", "#FFAA00", "#326bfb", "#30C453"]:
            # 点击取消标记
            self.click(
                '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-allSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(1) > div > div > span > span > svg')
            sleep(1)

        # 点击标记颜色
        self.click(
            '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-allSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(1) > div > div > span > span > svg')
        # 获取标记
        content = self.find_element(
            '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-allSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(1) > div > div > span > span > svg > g > path:nth-child(1)')
        sleep(0.1)
        # 校验是否标记成功
        cur_mark_color = content.get_attribute('fill')
        assert_that(cur_mark_color,equal_to("#FFAA00"),"")

        # 获取对应的会话客户昵称
        origin_buyer_name = self.get_text(
            '//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[1]/div/div/div/div/div[1]/div[1]/div')
        sleep(0.5)
        # 点击星标
        self.click('//*[@id="entry_pc"]/div[1]/div/div[3]/div[2]/div')
        # 比较昵称
        # print("origin_buyer_name:",self.get_text('#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-starSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(1) > div > div > div > div > div.top-head > div > div'))
        starSessionText=self.get_text('#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-starSessions.ready.direction-vertical')
        assert_that(starSessionText,contains_string(origin_buyer_name),"")

        # 取消标记
        self.click(
            '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-starSessions.ready.direction-vertical > div.vue-recycle-scroller__item-wrapper > div:nth-child(4) > div > div > span > span > svg')
        sleep(1)
        # 判断标记的买家是否还在星标tab下
        self.assert_not_in(origin_buyer_name, '#entry_pc > div.left-area > div > div.session-list-comp > div.vue-recycle-scroller.scroller-starSessions.ready.direction-vertical')


