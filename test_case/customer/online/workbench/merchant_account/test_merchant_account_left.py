import random
import time
from time import sleep
from unittest import skip

import pytest
from ddt import *
from selenium.webdriver import Keys, ActionChains
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory
import subprocess


@ddt
class TestMerchantAccountLeft(BaseTestCase):

    @pytest.mark.p0
    def test_left_tab_show(self):
        """
        主账号侧边栏展示
        """
        self.go_workbrench(self)
        self.is_element_visible("//div[text()='智能客服']")
        self.is_element_visible("//div[text()='设置']")
        self.is_element_visible("//div[text()='数据']")
        self.is_element_visible("//div[text()='会话']")

    @pytest.mark.p0
    def test_left_setting_tab_show(self):
        """
        主账号侧边栏-设置-展示
        """
        self.go_workbrench(self)
        self.is_element_visible("//div[text()='数据']")
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        time.sleep(3)
        self.is_element_visible("//div[text()='基础设置']")
        self.click("//div[text()='聊天记录']")
        time.sleep(3)

    @pytest.mark.p0
    def test_left_manage_tool_tab_show(self):
        """
        主账号侧边栏-管理工具-展示
        """
        self.go_workbrench(self)
        self.is_element_visible("//div[text()='数据']")
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        time.sleep(3)
        self.is_element_visible("//div[text()='管理工具']")
        self.click("//div[text()='管理工具']")
        time.sleep(3)
        self.click("//div[text()='实时管理']")
        time.sleep(3)
        self.is_element_visible("//span[text()='客服监控']")
        self.click("//div[text()='预警管理']")
        time.sleep(3)
        self.is_element_visible("//th[text()='预警状态']")


    @pytest.mark.p0
    def test_left_search_user(self):
        """
        左侧会话区—搜索框-按买家名称搜索
        """
        self.go_workbrench(self)
        # 搜索框输入买家昵称
        USER_NAME_FULL = "奥利奥草莓味"
        USER_NAME_SHORT = "奥利奥草"
        self.add_text('//*[@id="rc_select_0"]', USER_NAME_SHORT)
        self.sleep(3)

        # 搜索结果弹窗，识别买家昵称存在
        USER_SEARCH_RESULT = '//div[@class="ImSearch-drop-panel-text"][1]'
        assert_that(self.get_text(USER_SEARCH_RESULT) == USER_NAME_FULL)

        # 选择买家后展开聊天
        self.click(USER_SEARCH_RESULT)
        time.sleep(3)
        CHAT_USER_NAME = '(//div[@class="LoginUserInfo-mainTitle-text"])[1]'
        ORDER_USER_NAME = '(//div[@class="buyerName"])[1]'
        assert_that(self.get_text(CHAT_USER_NAME) == USER_NAME_FULL)
        assert_that(self.get_text(ORDER_USER_NAME) == USER_NAME_FULL)

    @pytest.mark.p0
    def test_left_search_id(self):
        """
        左侧会话区—搜索框-按买家id搜索
        """
        self.go_workbrench(self)
        # 搜索框输入买家id
        USER_ID = "1606636102"
        self.add_text('//*[@id="rc_select_0"]', USER_ID)
        time.sleep(3)

        # 获取买家昵称
        USER_SEARCH_RESULT = '//div[@class="ImSearch-drop-panel-text"][1]'
        print("[买家昵称]: ", self.get_text(USER_SEARCH_RESULT))

        # 输入错误id，匹配不到账号
        WRONG_USER_ID = "16066361029999"
        self.update_text('//*[@id="rc_select_0"]', WRONG_USER_ID)
        assert_that(not self.is_element_present(USER_SEARCH_RESULT))

    @pytest.mark.p0
    def test_left_search_chat_record(self):
        """
        左侧会话区—搜索框-按聊天记录搜索
        """
        self.go_workbrench(self, user='customer-z')
        return      # todo: 找一条时间近、历史记录少的聊天记录
        # 搜索框输入聊天记录
        CHAT_RECORD = "这个100块"      # web自动化发送消息_20240418
        self.add_text('//*[@id="rc_select_0"]', CHAT_RECORD)
        sleep(3)

        # 点击搜索结果，定位到聊天记录所在位置
        USER_SEARCH_RESULT = '//div[@class="ImSearch-drop-panel-text"][1]'
        self.click(USER_SEARCH_RESULT)
        sleep(3)

        # 判断聊天记录是否存在
        assert_that(self.is_element_present('//div[text()="'+CHAT_RECORD+'"]'))
        # 判断聊天记录发送人（卖家：Z）、发送时间
        assert_that(self.is_element_present('//span[text()="Z"]'))
        print('[(当前聊天记录)发送时间]：', self.get_text('//span[text()="Z"]/following-sibling::*[1]'))

        # 如果上一条聊天记录存在，获取买家头像
        PRE_CHAT_RECORD = "这个商品多少钱"
        if self.is_element_visible('//div[text()="'+PRE_CHAT_RECORD+'"]'):
            print('[(上一条聊天记录)买家头像]：', self.get_attribute('//img[@class="kwaishop-cs-userAvatar"]', 'src'))

        # 聊天记录上下滑动
        self.execute_script('document.getElementById("ChatMessageList-ContentWrap").scrollTo(0,100)')
        sleep(3)
        self.execute_script('document.getElementById("ChatMessageList-ContentWrap").scrollTo(0,10000)')
        sleep(3)

    @pytest.mark.p0
    @pytest.mark.skip
    def test_star_tab(self):
        """
        左侧会话区—给买家打星标
        """
        self.go_workbrench(self)
        self.click('//div[@class="SessionStarCard-tag"]')
        self.click('/html/body/div[3]/div/div/div/div[2]/div/div/div[1]')

    @pytest.mark.p0
    def test_wait_list(self):
        """
        排队池列表
        """
        self.go_workbrench(self)
        self.click('//span[contains(text(),"去处理")]')

        try:
            user = self.get_text('(//td[2])[2]/div')
            print('user1', user)
            self.find_element('(//td[8]/span)[1]')
            self.click('(//td[8]/span)[1]')
            sleep(2)
            ap = self.get_text('//*[@id="main_root"]/div/div/section/section/main/div/div/div/div/div[1]/div[2]/div[2]/div[1]/div/div')
            print('user2', ap)
            assert_that(user == ap, "")

        except Exception as e:
            print(e)
            return

    @pytest.mark.p0
    def test_text_message(self):
        """
        发送文本
        """
        self.go_workbrench(self)
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        if self.is_element_present(CHAT_EDIT_TEXT):
            # 输入文本消息
            self.add_text(CHAT_EDIT_TEXT, "测试发送")
            # 消息发送
            self.click(CHAT_SEND_BTN)
            # 校验发送后，消息框为空
            res = self.get_text(CHAT_EDIT_TEXT)
            assert_that(res == "", "")

    @pytest.mark.p1
    def test_quene_status(self):
        self.go_workbrench(self)
        """
        排队池转移
        """
        self.click('//span[contains(text(),"去处理")]')
        try:
            self.find_element('(//td[8]/span)[2]')
            '''转移按钮'''
            self.click('(//td[8]/span)[2]')
            self.sleep(2)
            '''转移'''
            status = self.get_text('//td[contains(text(),"思")]/parent::tr/td[3]')
            assert_that(status == "不自动分配(工作台未打开)")
        except Exception as e:
            return

    @pytest.mark.p0
    def test_wait_list_accept_has_repeat(self):
        """
        回复列表，排队池接待
        """
        self.go_workbrench(self)
        self.click('//span[contains(text(),"去处理")]')

        try:
            user = self.get_text('(//td[2])[2]/div')
            print('user1', user)
            self.find_element('(//td[8]/span)[1]')
            self.click('(//td[8]/span)[1]')
            sleep(2)
            self.add_text('//*[@id="esim-editor5678"]/div[1]', '3321')
            self.click('//span[contains(text(),"发送")]')
            sleep(1)
            self.click('//div[text()="我的当前接待"]')
            ap = self.get_text('//div[text()="已回复"]/following::div[@class="SessionBaseCard-topHeadName"]')
            print('user2', ap)
            assert_that(user == ap, "")

        except Exception as e:
            print(e)
            return

    @pytest.mark.p0
    def test_wait_list_accept_has_stat(self):
        """
        星标筛选
        """
        self.go_workbrench(self)
        # 星标颜色字典
        STAR_ICON_DICT = {
            "(48, 196, 83)": "绿星",
            "(50, 107, 251)": "蓝星",
            "(250, 78, 62)": "红星",
            "(255, 170, 0)": "黄星",
        }
        # 单机星标会话，默认展示全部买家
        self.click('//div[text()="全部会话"]')
        sleep(3)
        self.click('//div[text()="星标会话"]')

        # 获取第一条买家列表，及其颜色
        star_element = self.find_element('//div[@class="SessionStarCard-tag"][1]//span')
        # [对原始字符串进行截断]--color: rgb(48, 196, 83);
        star_color = star_element.get_attribute("style")[10:-1]
        buyer_name = self.get_text('//div[@class="SessionBaseCard-topHeadName"][1]')
        print("[买家名称+星标]:", buyer_name, ",", STAR_ICON_DICT[star_color])

        # 星标会话，按星标颜色筛选，正确情况该买家仍然显示
        self.click('//div[text()="全部"]')
        time.sleep(2)
        self.click('//span[text()="'+STAR_ICON_DICT[star_color]+'"]')
        sleep(3)
        # 判断星标筛选后，第一条买家昵称~是否与筛选前保持一致
        sub_buyer_name = self.get_text('//div[@class="SessionBaseCard-topHeadName"][1]')
        assert_that(sub_buyer_name == buyer_name)

    @pytest.mark.p0
    def test_search_user_by_order_id(self):
        """
        根据订单号，搜索买家聊天记录
        """
        self.go_workbrench(self)
        sleep(5)
        # 会话区搜索框，输入订单号
        ORDER_ID_WRONG = "233620003416433555"
        self.add_text('//input[@class="ant-select-selection-search-input"]', ORDER_ID_WRONG)
        sleep(3)
        assert_that(self.is_element_present('//div[text()="无符合的用户"]'))

        # 更新订单号(点击关闭按钮，清空搜索框)
        ORDER_ID = "2336200034164335"
        self.update_text('//input[@class="ant-select-selection-search-input"]', ORDER_ID)
        sleep(3)
        # 搜索成功，获取买家昵称，并点击买家头像展开会话
        buyer_name = self.get_text('//div[@class="ImSearch-drop-panel-text"][1]').split('\n')[0]
        self.click('//div[@class="ImSearch-drop-panel-text"][1]')
        sleep(3)

        # 判断右侧订单区-订单搜索结果内，订单号是否存在
        self.click('//div[text()="个人订单"]')
        assert_that(self.is_element_present('//div[@class="topLeft"]//div[text()="2336200034164335"]'))
        # 点击交易快照按钮，跳转到交易快照界面
        self.click('//div[@class="snapShot"]')
        try:
            # 点击知道了
            self.click('//button[text="知道了"]')
        except Exception as e:
            return
        self.sleep(3)
        # 判断商品图片是否出现在当前界面中
        assert_that(self.is_element_present('//div[@class="box"]'))

    @pytest.mark.p1
    def test_report_buyer(self):
        """
        举报买家
        """
        self.go_workbrench(self, user='customer-z', id='1275816963')
        sleep(3)

        # 点击举报图标，唤起弹窗
        self.click('//img[contains(@src, "seller_report")]')
        sleep(3)
        # 选择举报原因
        return      # todo: 修复代码
        self.click('//div[@class="ant-modal-body"]//child::div[@class="ant-select-selector"]')
        self.click('//*[text()="发送广告信息"]')
        self.input('//textarea[@placeholder="请输入举报描述"]', '123')
        self.click('//span[text()="选择证据"]')
        sleep(3)
        # 选择聊天记录证据，关闭弹窗
        self.click('//*[@id="0_1275816963_1275816963_17101430879650070-contain"]/label/span')
        self.click('//span[contains(text(), "取消举报")]')
        sleep(3)
        assert_that(not self.is_element_visible('//*[@id="0_1275816963_1275816963_17101430879650070-contain"]/label/span'))

    @pytest.mark.p0
    def test_display_workbench_status_info(self):
        """
        工作台内展示当前登录客服个人信息
        """
        self.go_workbrench(self, user='customer-z')
        sleep(5)

        # 获取当前登录用户的头像、昵称、角色信息(主账号)
        avatar_url = self.get_attribute('//img[@class="avatarHover"]', 'src')
        nick = self.get_text('//div[@class="infoTop"]')
        assert_that(("jpg" or "png") in avatar_url)
        assert_that("Z" in nick)
        assert_that(("主" in nick) and ('主账号' not in nick))
        print('[头像/昵称]：', avatar_url, nick)

        # 最大接待量、文案展示
        max_reception_text = self.get_text('//div[@class="receiveNum"]')
        assert_that("最大接待量" in max_reception_text)
        print("[最大接待量]：", max_reception_text)
        self.hover_on_element('//div[@class="maxSessionMenu"]//img')
        assert_that(self.is_element_visible('//*[contains(text(), "同时接待的最大会话量")]'))

        # hover头像，复制主账号id
        self.hover_on_element('//img[@class="avatarHover"]')
        merchant_id = self.get_text('//div[@class="hoverDiv"]//child::div[@class="id"]')
        self.click('//div[@class="hoverDiv"]//child::span[@aria-label="normal-copy-line"]')
        self.click('//div[contains(@class, "ql-editor")]')

        # 粘贴在会话输入框，判断复制后的账号是否存在
        ele = self.driver.find_element("class name", "ql-editor")
        ele.send_keys(Keys.COMMAND, 'v')
        paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
        if 'v' not in paste_text:
            assert_that(paste_text in merchant_id)

    def test_buyer_conversation_status_chaging_display(self):
        """
        工作台内，针对买家会话状态分组变更展示，进行校验
        """
        self.go_workbrench(self)
        sleep(3)

        # 主账号，确保当前状态切换到在线
        front_status = self.get_text('//div[@class="infoBottom"]').split('\n')[0]
        if front_status in ('离线', '挂起'):
            self.click('//div[@class="infoBottom"]//div[contains(text(),"线")]')
            self.click('//div[contains(text(),"在线：接待买家中")]')
            sleep(3)

        # 切换到我的当前接待tab
        self.click('//div[text()="我的当前接待"]')
        sleep(3)

        # 校验"3分钟内回复tab"是否存在买家       todo: 外接买家进线接口
        count_3min_reply = int(self.get_text('//div[contains(text(), "请在3分钟内回复")]//following::div[1]')[1:-1])
        if count_3min_reply > 0:
            """【我的当前接待-父级tab】"""
            # (1)校验tab附近红点是否出现
            self.is_element_present('//div[@class="RedDot"]')

            """【3分钟回复tab】"""
            # (1)右箭头，未展开状态下，需要将其展开
            if "right" in self.get_attribute('//div[contains(text(), "请在3分钟内回复")]//preceding::span[1]', 'aria-label'):
                self.click('//div[contains(text(), "请在3分钟内回复")]//preceding::span[1]')
            # (2)查看第一个买家是否有红点，获取消息维度数量
            FIRST_BUYER = '//div[@class="SessionListGroupItem"][1]//child::div[@class="SessionStarCard"][1]'
            RED_DOT = '//div[@class="SessionListGroupItem"][1]//child::div[@class="SessionStarCard"][1]//child::div[@class="SessionBaseCard-unread-rot"]'
            NICK = '//div[@class="SessionListGroupItem"][1]//child::div[@class="SessionStarCard"][1]//child::div[@class="SessionBaseCard-topHeadName"]'
            unread_dot = 0
            buyer_name = self.get_text(NICK)
            print("买家昵称：", buyer_name)
            if self.is_element_visible(RED_DOT):
                unread_dot = self.get_text(RED_DOT)
                print("该买家消息未读数：", RED_DOT)
            # (3)等待180秒，切换到已超时tab
            sleep(181)

            """【已超时tab】"""
            count_over_time_reply = int(self.get_text('//div[contains(text(), "已超时")]//following::div[1]')[1:-1])
            assert int(count_over_time_reply) > 0
            # (1)右箭头，未展开状态下，需要将其展开
            if "right" in self.get_attribute('//div[contains(text(), "已超时")]//preceding::span[1]', 'aria-label'):
                self.click('//div[contains(text(), "已超时")]//preceding::span[1]')
            # (2)点击买家，跳转到买家会话
            self.click('//div[text()="' + "西门" + '"]')
            # (3)校验打开的会话，买家是否出现
            assert_that(self.get_text('//div[@class="LoginUserInfo-mainTitle-text"]') == buyer_name)
            # (4)发送文本消息
            CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
            CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'
            self.add_text(CHAT_EDIT_TEXT, "web自动化-客服回复消息")
            self.click(CHAT_SEND_BTN)
            sleep(3)

            """【已回复tab】"""
            # (1)校验已回复买家数>0
            count_already_reply = int(self.get_text('//div[contains(text(), "已回复")]//following::div[1]')[1:-1])
            assert_that(count_already_reply > 0)
            # (2)点击关闭按钮，关闭会话，出现关闭提示
            CHAT_CLOSE_BTN = '//span[contains(text(), "关闭会话 ESC")]'
            self.click(CHAT_CLOSE_BTN)
            sleep(3)
            assert_that(self.is_element_present('//div[text()="客服关闭会话"]'))

    @pytest.mark.p2
    def test_display_subaccount_tab(self):
        """
        子账号不展示“子账号接待tab”
        """
        self.go_workbrench(self, user='customer-z')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="子账号接待"]'))
        self.click('//div[text()="子账号接待"]')

    @pytest.mark.p1
    def test_transfer_buyer(self):
        """
        转移单个买家会话
        """
        return
        self.go_workbrench(self, user='customer-z')
        sleep(5)
        self.click('//div[text()="全部会话"]')
        sleep(5)
        ele = self.find_elements('//div[@class="SessionStarCard"]')[1]
        # 获取买家信息(含标签)
        print("买家信息：", ele.text)

        # 转移买家
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        assert_that(self.is_element_present('//*[text()="转移会话"]'))
        assert_that(not self.is_element_present('//*[text()="删除会话"]'))
        self.click('//*[text()="转移会话"]')
        assert_that(self.is_element_present('//*[contains(text(), "您已选择 1 个会话")]'))

    @pytest.mark.p2
    def test_switch_buyer_up_down(self):
        """
        上下键切换买家
        """
        self.go_workbrench(self, user='customer-z')
        sleep(5)
        self.click('//div[text()="全部会话"]')
        sleep(5)

        # 选中买家
        self.click('//div[@class="SessionStarCard"]')
        # 按上键3次、下键3次
        ele = self.find_elements('//div[@class="SessionStarCard"]')[1]
        ele_pre_text = ele.text
        for i in range(3):
            ActionChains(self.driver).send_keys(Keys.ARROW_DOWN).perform()
        sleep(3)
        for i in range(3):
            ActionChains(self.driver).send_keys(Keys.ARROW_UP).perform()
        sleep(3)
        # 上下键切换回原来的买家。观察买家名称是否有变化
        ele = self.find_elements('//div[@class="SessionStarCard"]')[1]
        # assert_that(ele_pre_text == ele.text)
        print("[买家先后名称]：", ele_pre_text, ele.text)

