import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from utils.time_help import *
from hamcrest import *


@ddt
class TestMerchantAccountInviteEvalute(BaseTestCase):

    # 客服会话页面发送邀请评价
    @skip
    @pytest.mark.p1
    def test_send_invite_evaluate(self):
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", "customer_merchant")
        self.assert_text("客服管理", "li[id= 'node_menu_for_intersectionObserver_6']")
        self.click("li[id= 'menu_item_21']")  # 点击进入小店客服工作台
        self.sleep(2)
        self.switch_to_window(1)
        self.check_if_page_404()

        if self.get_title() == "快手小店客服":
            self.assert_element('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]')
        else:
            self.MerchantAccountLoginBySmscode("CUSTOMER_DOMAIN", "customer_merchant")

        self.maximize_window()
        sleep(2)
        self.click('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]/div')
        sleep(2)
        try:
            # 进入买家会话
            self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[' + str(random.randint(1, 2)) + ']')
        except Exception as e:
            # 进入买家会话
            self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[1]')
        sleep(2)
        # 点击邀请评价图标
        self.click('//*[@id="entry_pc"]/div[2]/div/div[4]/div[1]/div[1]/span[2]/span/img')
        sleep(1)
        content = self.get_text(
            '#esim_mylist > div >div:nth-last-child(1) > div > .esim_content-box > .esim_msg-body > .esim_notice_text')
        assert_that(content,
                    is_in(["过去24小时内，买家没有咨询，您无法发送评价邀请", "评价邀请已发出", "已对买家发送过评价邀请，24小时内不可重复发送", "用户已经评价，24小时内不可重复发送"]), "")



