import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestMerchantAccountCoupon(BaseTestCase):

    # 客服会话页面发送店铺优惠券-可领取
    @pytest.mark.p1
    @pytest.mark.run(order=1)
    @skip
    def test_send_shop_coupon_normal(self):
        star = DateFactory.Today.start_timestamp()
        end = DateFactory.Today.end_timestamp()

        data = {"data": [
            {"couponFuncType": 1, "couponTargetType": 1, "totalStock": 111, "receivePerLimit": 1, "couponName": "全11",
             "couponStart": star, "couponEnd": end, "receiveTimeStart": star,
             "receiveTimeEnd": end, "validityType": 1, "receiveChannel": 74, "needFocus": False,
             "couponFuncData": {"base": 1000, "price": 100}}]}

        url = "https://eshop-s.prt.kwaixiaodian.com/rest/app/tts/ks/coupon/create"

        self.go_workbrench(self, data, url)

        # 点击优惠券图标
        self.click('//div[@class="MiddleBarItem"][5]')
        sleep(1)
        # 发送店铺券
        self.click('//span[text()="发送优惠券"][1]')

        title = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[1]')

        type = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[2]/div/div[1]')

        limit = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[2]/div/div[3]/div[1]')
        carsh = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[2]/div/div[2]/div[2]')

        # message = self.get_text('/html/body/div[7]/div/div')
        #
        # assert_that(message, contains_string("优惠券发送成功!"), "")

        assert_that(title, contains_string("老铁，您的专属店铺券来啦～"), "")
        assert_that(type, contains_string("店铺券"), "")
        assert_that(limit, contains_string("满10可用"), "")
        assert_that(carsh, contains_string("1"), "")

    # 客服会话页面发送店铺优惠券-不可领取
    @pytest.mark.p1
    @pytest.mark.run(order=2)
    @skip
    def test_send_shop_coupon_limit(self):
        self.go_workbrench(self)
        sleep(2)
        # 点击优惠券图标
        self.click('//div[@class="MiddleBarItem"][5]')
        sleep(1)
        # 发送店铺券
        self.click('(//span[text()="发送优惠券"])[1]')

        message = self.get_text('/html/body/div[7]/div/div')

        # assert_that(message, contains_string("最多领取1张"), "")

    # 客服会话页面发送店铺商品优惠券-可领取
    @pytest.mark.p1
    @pytest.mark.run(order=3)
    @skip
    def test_send_item_coupon_normal(self):
        star = DateFactory.Today.start_timestamp()
        end = DateFactory.Today.end_timestamp()

        data = {"data": [{"couponFuncType": 1, "couponTargetType": 0, "totalStock": 100, "receivePerLimit": 1,
                          "couponName": "商品券", "couponStart": star, "couponEnd": end,
                          "receiveTimeStart": star, "receiveTimeEnd": end, "validityType": 1,
                          "receiveChannel": 74, "needFocus": False, "couponFuncData": {"base": 5000, "price": 1000},
                          "items": [{"itemId": **************}]}]}

        url = "https://eshop-s.prt.kwaixiaodian.com/rest/app/tts/ks/coupon/create"

        self.go_workbrench(self, data, url, user='product_account', name="Csr.12")

        # 点击优惠券图标
        self.click('//div[@class="MiddleBarItem"][5]')

        sleep(1)

        self.click('// div[text() = "商品券"]')

        sleep(1)
        # 发送商品券
        self.click('//span[text()="发送优惠券"][1]')

        title = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[1]')

        type = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[2]/div/div[1]')

        limit = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[2]/div/div[3]/div[1]')
        carsh = self.get_text('(//div[@class="kwaishop-cs-BizCouponCard"])[last()]/div[2]/div/div[2]/div[2]')

        assert_that(title, contains_string("老铁，您的专属商品券来啦～"), "")
        assert_that(type, contains_string("商品券"), "")
        assert_that(limit, contains_string("满33可用"), "")
        assert_that(carsh, contains_string("1"), "")

    @pytest.mark.p1
    @pytest.mark.run(order=4)
    @skip
    def test_send_item_coupon_limit(self):
        self.go_workbrench(self, user='product_account', name="Csr.12")
        sleep(2)
        # 点击优惠券图标
        self.click('//div[@class="MiddleBarItem"][5]')

        sleep(1)

        self.click('// div[text() = "商品券"]')

        sleep(1)
        # 发送商品券
        self.click('//span[text()="发送优惠券"][1]')

        message = self.get_text('/html/body/div[7]/div/div')

        # assert_that(message, contains_string("最多领取1张"), "")
