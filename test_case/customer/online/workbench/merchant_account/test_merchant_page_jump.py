import datetime
import random
import time
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase, judge_in_seconds
from utils.time_help import *
from hamcrest import *
from constant.domain import get_domain


@ddt
class TestMerchantAccountPageJump(BaseTestCase):

    # 客服管理-》客服机器人-〉使用帮助(主账号)
    # # 客服管理-》客服机器人-〉服务市场
    # 客服管理 -》客服设置 -〉应急通知设置 -》《关于疫情影响下的发货及售后政策调整公告》
    # 客服管理 -》客服设置 -〉分流设置 -》不分流设置教程
    # 客服管理 -》客服设置 -〉分流设置 -》分组分流设置教程
    # 客服管理-》聊天记录

    @pytest.mark.p1
    def test_order_detail_customer_link_jump_to_workbench(self):
        # 客服工作台订单详情页，跳转到客服工作台
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", 'member_account2')
        self.maximize_window()
        self.sleep(2)
        # 买家和订单号
        ORDER_ID = "****************"
        BUYER_ID = "**********"
        BUYER_NAME = "买卖买卖买"

        # 打开订单详情页
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)
        if self.ENV == "prt":
            self.open('https://eshop-s.prt.kwaixiaodian.com/zone/order/detail?id=' + ORDER_ID)
        else:
            self.open('https://s.kwaixiaodian.com/zone/order/detail?id='+ORDER_ID)
        time.sleep(5)
        try:
            # 点击知道了
            for i in range(3):
                if self.is_element_visible('//div[@id="driver-popover-item"]'):
                    self.click('//html')
                    time.sleep(1)
                    continue
                self.click_if_visible('//button[text()="知道了"]')
        except Exception as e:
            print("侧边栏toast知道了：已点完/未出现")
        # 移动经营助手弹窗
        self.move_seller_helper_box(bottom='650px')
        time.sleep(3)

        # 点击买家，进入客服工作台（提示引导还存在的时候，直接返回）
        if self.is_element_visible('//div[@id="driver-popover-item"]'):
            return
        self.click_if_visible('//div[text()="'+BUYER_NAME[0]+'**"]')
        self.click_if_visible('//div[text()="'+BUYER_NAME+'"]')
        time.sleep(4)
        try:
            # 退出引导
            self.find_element("//button[text()='退出引导']")
            self.click("//button[text()='退出引导']")
        except Exception as e:
            print(e)
        sleep(2)
        try:
            # 关闭问卷
            self.find_element('//span[contains(text(),"您有")]/parent::div/span[2]')
            self.click('//span[contains(text(),"您有")]/parent::div/span[2]')
        except Exception as e:
            print(e)
        try:
            for i in range(2):
                # 点击知道了
                self.click('//*[text()="知道了"]')
        except Exception as e:
            print(e)

        # 判断发送订单弹窗是否存在
        self.click_if_visible("//div[@class='PreCardItemHeader']//div[text()='点击发送订单']")
        # 点击过后，若订单卡片出现，对订单号进行校验
        # CARD_TITLE_PARENT = "//div[@class='kwaishop-cs-MaterialTitle']"
        # CARD_TITLE = CARD_TITLE_PARENT + "//span"
        # if self.is_element_present(CARD_TITLE_PARENT):
        #     sub_order_id_text = self.get_text(CARD_TITLE)
        #     assert ORDER_ID in sub_order_id_text

        # 判断中间底部聊天框是否存在
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        assert self.is_element_present(CHAT_EDIT_TEXT) == True

        # 判断会话框买家昵称是否存在
        assert self.is_element_present("//div[text()='"+BUYER_NAME+"']") == True
        #self.check_if_page_404()

    @pytest.mark.p1
    def test_order_evaluation_customer_link_jump_to_workbench(self):
        # 客服工作台评价详情页，跳转到客服工作台
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", 'member_account2')
        self.maximize_window()
        self.sleep(3)

        # 买家和订单号
        ORDER_ID = "****************"
        BUYER_ID = "**********"
        BUYER_NAME = "买卖买卖买"

        # 打开订单评价详情页
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)
        if self.ENV == "prt":
            self.open('https://eshop-s.prt.kwaixiaodian.com/zone/goods/manage/comment/list')
        else:
            self.open('https://s.kwaixiaodian.com/zone/goods/manage/comment/list')
        try:
            # 点击知道了
            for i in range(3):
                if self.is_element_visible('//div[@id="driver-popover-item"]'):
                    self.click('//html')
                    continue
                self.click('//button[text()="知道了"]')
        except Exception as e:
            print()
        time.sleep(3)

        # 查找订单
        self.add_text("input[placeholder='请输入订单编号']", ORDER_ID)
        self.click("//span[text()='查 询']")
        time.sleep(3)

        # 如果有查到到结果，点击“联系ta”进入客服工作台
        if self.is_element_present("//span[text()='"+BUYER_NAME+"']"):
            self.click("//a[text()='联系ta']")
            time.sleep(4)
            try:
                # 退出引导
                self.find_element("//button[text()='退出引导']")
                self.click("//button[text()='退出引导']")
            except Exception as e:
                print(e)
            sleep(2)
            try:
                # 关闭问卷
                self.find_element('//span[contains(text(),"您有")]/parent::div/span[2]')
                self.click('//span[contains(text(),"您有")]/parent::div/span[2]')
            except Exception as e:
                print(e)

            # 判断中间底部聊天框是否存在
            CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
            assert self.is_element_present(CHAT_EDIT_TEXT) == True

            # 判断会话框买家昵称是否存在         # todo: 怀疑网络原因，跳转错误，待排查
            # assert self.is_element_present("//div[text()='" + BUYER_NAME + "']") == True

    @pytest.mark.p1
    def test_aftersale_workbench_customer_link_jump_to_workbench(self):
        # 客服工作台售后工作台，跳转到客服工作台
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", 'member_account2')
        self.maximize_window()
        self.sleep(2)
        # 买家和订单号、售后单号
        ORDER_ID = "****************"
        BUYER_ID = "**********"
        BUYER_NAME = "ks测试账号"
        REFUND_ID = "****************"

        # 打开售后工作台，进入售后详情页
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)
        if self.ENV == "prt":
            self.open('https://prt-eshop-s.test.gifshow.com/zone/refund/detail?refundId=' + REFUND_ID + '&refer=REFUND_LIST')
        else:
            self.open('https://s.kwaixiaodian.com/zone/refund/detail?refundId=' + REFUND_ID + '&refer=REFUND_LIST')
        try:
            # 点击知道了
            for i in range(3):
                if self.is_element_visible('//div[@id="driver-popover-item"]'):
                    self.click('//html')
                    continue
                self.click('//button[text()="知道了"]')
        except Exception as e:
            print()
        time.sleep(3)

        # 如果有查到到结果，点击“联系ta”进入客服工作台
        if self.is_element_present("//a[text()='" + ORDER_ID + "']"):
            # 点击买家id附近的chat-icon
            self.click("//div[text()='"+BUYER_NAME+"']")
            time.sleep(4)
            try:
                # 退出引导
                self.find_element("//button[text()='退出引导']")
                self.click("//button[text()='退出引导']")
            except Exception as e:
                print(e)
            sleep(2)
            try:
                # 关闭问卷
                self.find_element('//span[contains(text(),"您有")]/parent::div/span[2]')
                self.click('//span[contains(text(),"您有")]/parent::div/span[2]')
            except Exception as e:
                print(e)

            # 判断中间底部聊天框是否存在
            CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
            assert self.is_element_present(CHAT_EDIT_TEXT) == True

            # 判断会话框买家昵称是否存在
            assert self.is_element_present("//div[text()='" + BUYER_NAME + "']") == True


