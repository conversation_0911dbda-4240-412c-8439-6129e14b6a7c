import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase, judge_in_seconds
from utils.time_help import *
from hamcrest import *


@ddt
class TestMerchantCustomerManager(BaseTestCase):

    # 客服管理-》自动消息开关
    @pytest.mark.p1
    @skip
    def test_customer_management_auto_message(self):
        self.merchantAccountLogin("MERCHANT_DOMAIN", "customer_merchant")
        self.hover_on_element("//span[text()='客服']")
        self.click("//span[contains(text(),'客服设置')]")
        self.click("//*[text()='自动消息']")
        list = ['催付','自动核单','已发货通知','商品寄回信息']
        for type in list:
            self.auto_message_switch(type)


    #管理工具
    @pytest.mark.p1
    @skip
    def test_customer_management_auto_message2(self):
        self.merchantAccountLogin("MERCHANT_DOMAIN", "customer_merchant")
        self.hover_on_element("//span[text()='客服']")
        self.click("//span[contains(text(),'管理工具')]")
        customer = self.find_elements("//div[@class='ant-table-fixed-left']//tr[@class='ant-table-row ant-table-row-level-0']")
        print(len(customer))
        self.assert_true(len(customer)>6)














