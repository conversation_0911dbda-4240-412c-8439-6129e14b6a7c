import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestMerchantAccountLeft(BaseTestCase):

    # 搜索框搜索
    @pytest.mark.p0
    @pytest.mark.skip
    def test_fast_replay(self):
        self.go_workbrench_child(self)

        self.click('//div[text()="快捷回复"]')

        sleep(1)

        self.click('//div[contains(text(),"问候")]')
        sleep(2)

        self.click('//div[contains(text(),"效劳")]/parent::div/div[2]')
        sleep(2)

        res = self.get_text('//*[@id="esim-editor5678"]/div[1]/p')

        assert_that(res, contains_string('宝宝'))

        self.click(
            '//*[@id="main_root"]/div/div/section/section/main/div/div/div/div/div[1]/div[2]/div[5]/div[1]/div[2]/div[2]/button[2]')

        res = self.get_text('//*[@id="esim-editor5678"]/div[1]/p')
        sleep(1)

        assert_that(res == '', '')

    @pytest.mark.p0
    @pytest.mark.skip
    def test_buyer_info(self):
        self.go_workbrench_child(self)

        sleep(1)

        self.click('//div[text()="个人订单"]')

        sleep(1)

        orders = self.get_text('//span[contains(text(),"下单")]')

        avg = self.get_text('//span[contains(text(),"件单价")]')
        goods = self.get_text('//span[contains(text(),"好评")]')
        refund = self.get_text('//span[contains(text(),"退货")]')
        notice = self.get_text('//span[contains(text(),"退货")]')

        assert_that(orders, "下单数：6")
        assert_that(avg, "件单价：￥0.0")
        assert_that(goods, "好评率：0.0%")
        assert_that(refund, "0.0%")
        assert_that(notice, "123")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_send_item_card(self):
        # 商品推荐页面发送热销商品

        self.go_workbrench_child(self)

        # 点击商品推荐
        self.click('//div[text()="商品推荐"]')

        sleep(1)

        self.click('//div[text()="热销商品"]')

        sleep(1)
        # 发送商品
        self.click('(//div[text()="发送商品"])[1]')

        self.sleep(2)

        title = self.get_text('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_msgBody"])[last()]/div[1]/div[1]')

        carsh = self.get_text('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_msgBody"])[last()]/div[1]/div[2]')

        assert_that(title, contains_string("our love测试商品不发货-运营策略测试商品1"), "")
        assert_that(carsh, contains_string("1.01"), "")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_send_item(self):
        self.go_workbrench_child(self)
        return          # todo：修改订单编号

        # 点击订单
        self.click('//div[text()="个人订单"]')

        sleep(1)

        self.click('//span[text()="发送订单"][1]')

        sleep(2)

        title = self.get_text('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_msgBody"])[last()]/div[1]/div[1]')

        item = self.get_text(
            '(//div[@class="kwaishop-cs-LayoutDefaultWrapper_msgBody"])[last()]/div[1]/div[2]/div[1]/div[1]')

        assert_that(title, contains_string("订单编号"), "")
        assert_that(item, contains_string("商品"), "")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_private_get(self):
        self.go_workbrench_child(self)

        # 点击订单
        self.click('//div[text()="个人订单"]')

        sleep(1)

        # 切换已收货
        self.click('//div[text()="已收货"]')

        sleep(1)

        # 解密
        self.click('(//div[text()="收货信息"])[1]/following::img[1]')

        self, sleep(1)

        name = self.get_text('(//div[text()="收货信息"])[1]/following::span[1]/span[1]')
        phone = self.get_text('(//div[text()="收货信息"])[1]/following::span[1]/span[2]')

        print(phone)

        assert_that(name == '同俄籍')

    @pytest.mark.p1
    def test_tripartite_app_not_display(self):
        """
        客服工作台-客服应用(不展示)
        """
        self.go_workbrench_child(self, user="detest017_75")
        # 点击客服应用
        self.click('//div[text()="客服应用"]')
        sleep(3)
        # 出现“还未设置客服应用”toast
        assert_that(self.is_element_present('//p[@class="cs-plugin-empty-notice"]'))

