import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from selenium.webdriver import Keys, ActionChains
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestChildAccountLeft(BaseTestCase):

    @pytest.mark.p0
    def test_left_tab_show(self):
        """
        子账号侧边栏展示
        """
        # self.go_workbrench_child(self,user='customer-***********')
        self.go_workbrench_child(self, user="detest017_75")
        self.is_element_visible("//div[text()='设置']")
        self.is_element_visible("//div[text()='数据']")
        self.is_element_visible("//div[text()='会话']")
        assert_that(not self.is_element_visible("//div[text()='智能客服']"))

    @pytest.mark.p1
    def test_left_search(self):
        """
        子账号搜索框，搜索接待过的买家
        """
        self.go_workbrench_child(self, user="detest017_75")
        BUYER_NAME = "奥利奥草莓味"
        self.add_text('//*[@id="rc_select_0"]', BUYER_NAME)
        sleep(5)
        # 搜索成功，获取买家昵称，并点击买家头像展开会话
        assert_that(self.get_text('//div[@class="ImSearch-drop-panel-text"][1]').split('\n')[0] == BUYER_NAME)
        self.click('//div[@class="ImSearch-drop-panel-text"][1]')
        sleep(5)
        assert_that(self.get_text('//div[@class="LoginUserInfo-mainTitle-text"]') == BUYER_NAME)
        # 校验买家头像
        buyer_avatar = self.get_attribute('//div[@class="LoginUserInfo"]/span/img', 'src')
        assert_that('jpg' in buyer_avatar or 'png' in buyer_avatar)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_wait_list(self):
        '''排队池接受会话'''
        self.go_workbrench_child(self)

        self.click('//span[contains(text(),"去处理")]')

        '''接待框第一个用户名字'''
        user = self.get_text('(//td[2])[2]/div')

        print('user1', user)
        '''接待第一个用户'''
        if self.find_elements('(//td[8]/span)[1]') != []:
            self.click('(//td[8]/span)[1]')
            sleep(2)
        else:
            assert '没有排队的人'

        ap = self.get_text(
            '//*[@id="main_root"]/div/div/section/section/main/div/div/div/div/div[1]/div[2]/div[2]/div[1]/div/div')
        print('user2', ap)

        assert_that(user == ap, "")

    @pytest.mark.p1
    def test_online_status(self):
        self.go_workbrench_child(self)
        sleep(3)
        front_status = self.get_text('//div[@class="infoBottom"]').split('\n')[0]
        if front_status == '离线':
            self.click('//div[@class="infoBottom"]//div[contains(text(),"线")]')
            self.click('//div[contains(text(),"在线：接待买家中")]')
            sleep(3)
        front_status = self.get_text('//div[@class="infoBottom"]').split('\n')[0]
        if front_status in('在线', '挂起'):
            self.click('//div[@class="infoBottom"]//div[contains(text(),"线")]')
            self.click('//div[contains(text(),"离线：不接待买家")]')
            # 当客服在线人数为0人时，新增离线留言弹窗
            if self.is_element_visible('//span[text()="确定切换吗"]'):
                assert_that(self.is_element_visible('//div[contains(text(), "切换状态后店铺空闲客服为0人")]'))
                self.click('//span[text()="确 认"]')
            sleep(3)
        end_status = self.get_text('//div[@class="infoBottom"]').split('\n')[0]
        assert_that(end_status == "离线")
        print("[最初/当前客服在线状态]：", front_status, end_status)

    @pytest.mark.p1
    @pytest.mark.skip
    def test_quene_repet(self):
        self.go_workbrench_child(self)

        self.click('//span[contains(text(),"去处理")]')

        '''接待框第一个用户名字'''
        user = self.get_text('(//td[2])[2]/div')

        '''转移按钮'''

        self.click('(//td[8]/span)[2]')

        self.sleep(2)

        '''转移'''
        self.click('//td[contains(text(),"思")]/parent::tr/td[7]')

        self.sleep(2)

        '''接待框第一个用户名字'''

        user_second = self.get_text('(//td[2])[2]/div')

        assert_that(user != user_second)


    @pytest.mark.p1
    def test_business_assistant_frame_display(self):
        """
        经营助手弹窗展示
        """
        self.go_workbrench_child(self, user="detest017_75")
        self.is_element_visible("//div[text()='智能客服']")

        # 展示经营助手弹窗
        self.click_if_visible('//span[text()="经营助手"]')
        self.is_element_visible('//span[contains(text(), "经营助手为你服务")]')
        self.is_element_visible('//span[text()="猜你想问"]')
        self.click('//span[text()="请输入您的问题搜索"]')

    @pytest.mark.p1
    def test_display_workbench_status_info(self):
        """
        工作台内展示当前登录客服个人信息
        """
        self.go_workbrench_child(self, user="detest017_75")
        sleep(3)
        # 获取当前登录用户的头像、昵称、角色信息(主账号)
        avatar_url = self.get_attribute('//img[@class="avatarHover"]', 'src')
        nick = self.get_text('//div[@class="infoTop"]')
        assert_that(("jpg" or "png") in avatar_url)
        assert_that("客服B" in nick)
        assert_that(("子" in nick) and ("子账号" not in nick))
        print('[头像/昵称]：', avatar_url, nick)

        # 最大接待量、文案展示
        max_reception_text = self.get_text('//div[@class="receiveNum"]')
        assert_that("最大接待量" in max_reception_text and len(max_reception_text)>5)
        #self.hover_on_element('//div[@class="maxSessionMenu"]//img')
        #assert_that(self.is_element_visible('//div[contains(text(), "最大接待量由管理员设置")]'))

        # hover头像，复制主账号id
        self.hover_on_element('//img[@class="avatarHover"]')
        customer_id = self.get_text('//div[@class="hoverDiv"]//child::div[@class="id"]')
        self.click('//div[@class="hoverDiv"]//child::span[@aria-label="normal-copy-line"]')
        self.click('//div[contains(@class, "ql-editor")]')

        # 粘贴在会话输入框，判断复制后的账号是否存在
        ele = self.driver.find_element("class name", "ql-editor")
        ele.send_keys(Keys.COMMAND, 'v')
        paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
        if 'v' not in paste_text:
            assert_that(paste_text in customer_id)

    @pytest.mark.p1
    def test_display_no_reception_status(self):
        """
        子账号不分流状态展示
        """
        self.go_workbrench_child(self, user="detest017_75_2")
        sleep(5)
        # 账号不分流状态下，hover展示文案
        if self.is_element_present('//div[@class="infoBottom"]//child::div[text()="不分流"]'):
            self.hover_on_element('//div[@class="infoBottom"]//child::div[text()="不分流"]')
            self.is_element_visible('//*[contains(text(), "该账号被设置了不分流")]')
            self.is_element_visible('//*[contains(text(), "分流设置-规则设置-不分流设置")]')

    @pytest.mark.p2
    def test_hover_buyer(self):
        """
        对买家信息进行hover
        """
        self.go_workbrench_child(self, user="detest017_75")
        sleep(2)
        self.click('//div[text()="全部会话"]')
        sleep(2)
        self.hover_on_element('//div[@class="SessionStarCard"]')

    @pytest.mark.p1
    def test_transfer_buyer(self):
        """
        转移单个买家会话
        """
        self.go_workbrench_child(self, user="detest017_75")
        sleep(2)
        self.click('//div[text()="全部会话"]')
        sleep(2)
        ele = self.find_elements('//div[@class="SessionStarCard"]')[1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        assert_that(self.is_element_present('//*[text()="转移会话"]'))
        assert_that(not self.is_element_present('//*[text()="删除会话"]'))
        self.click('//*[text()="转移会话"]')
        sleep(3)
        assert_that(self.is_element_present('//*[contains(text(), "您已选择 1 个会话")]'))

    @pytest.mark.p2
    def test_not_display_subaccount_tab(self):
        """
        子账号不展示“子账号接待tab”
        """
        self.go_workbrench_child(self, user="detest017_75")
        sleep(2)
        assert_that(not self.is_element_present('//div[text()="子账号接待"]'))

    @pytest.mark.p2
    def test_buyer_set_star_flag(self):
        """
        买家tab标注星标
        """
        self.go_workbrench_child(self, user="detest017_75")
        sleep(2)
        self.click('//div[text()="全部会话"]')
        sleep(2)
        # 打绿星标
        self.hover_on_element('//div[@class="SessionStarCard"]')
        self.click('//div[@class="SessionStarCard-tag"]')
        self.click('//*[text()="绿星"]')
        sleep(3)
        assert_that('rgb(48, 196, 83)' in self.get_attribute('//span[@aria-label="normal-favorite-star-fill"]', 'style'))
        # 取消星标
        self.hover_on_element('//div[@class="SessionStarCard"]')
        self.click('//div[@class="SessionStarCard-tag"]')
        self.click('//*[text()="取消星标"]')
        sleep(3)
        assert_that(
            (not self.is_element_present('//span[@aria-label="normal-favorite-star-fill"]'))
            or ('rgb(196, 196, 196)' in self.get_attribute('//span[@aria-label="normal-favorite-star-fill"]', 'style'))
        )




