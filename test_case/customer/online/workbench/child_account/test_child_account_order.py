import random
from time import sleep
import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from unittest import skip


@ddt
class TestChildAccountOrder(BaseTestCase):

    # 发送订单
    @skip
    @pytest.mark.p1
    def test_send_order(self):
        self.MerchantLoginBySmscode("MERCHANT_DOMAIN", "detest017_74")
        self.assert_text("客服管理", "li[id= 'node_menu_for_intersectionObserver_6']")
        self.click("li[id= 'menu_item_21']")
        self.switch_to_window(1)
        self.check_if_page_404()
        sleep(2)
        self.click('#entry_pc > div.left-area > div > div.session-tab-wrap > div:nth-child(3)')  # 点击全部
        sleep(1)
        try:
            # 进入买家会话
            self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[' + str(random.randint(1, 2)) + ']')
        except Exception as e:
            # 进入买家会话
            self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[1]')
        sleep(2)
        tab_list = self.get_text("div.tab-comp.feat-sort")
        self.assert_in("个人订单", tab_list)
        self.assert_element("div.list > div:nth-child(1)")  # 展示第一个订单
        self.click('//*[@id="entry_pc"]/div[3]/div/div[1]/div[3]/div[2]/div[1]/div[' + str(
            random.randint(1, 3)) + ']/div/div[7]/div[2]')  # 点击发送订单
        self.sleep(2)
        self.click('#esim_mylist > div >div:nth-last-child(1)')  # 点击会话页面底部商品
        self.sleep(2)
        self.switch_to_window(2)
        self.assert_title("快手小店")
        #self.assert_text("订单信息", '#root > section > section > main > div:nth-child(3) > div.sc-ieecCq.cFvzFs')
        self.assert_text("订单信息",'//div[contains(text(),"订单信息")]')
        self.sleep(2)
        self.switch_to_window(1)
        self.assert_title("快手小店客服")
        self.click('//*[@id="entry_pc"]/div[3]/div/div[1]/div[3]/div[2]/div[1]/div[' + str(
            random.randint(1, 3)) + ']/div/div[6]/a')  # 点击订单编号
        self.sleep(2)
        self.switch_to_window(3)
        self.assert_title("快手小店")
        #self.assert_text("订单信息", '#root > section > section > main > div:nth-child(3) > div.sc-ieecCq.cFvzFs')
        self.assert_text("订单信息",'//div[contains(text(),"订单信息")]')
        self.sleep(2)
        self.switch_to_window(1)
        self.assert_title("快手小店客服")
        self.click('//*[@id="entry_pc"]/div[3]/div/div[1]/div[3]/div[2]/div[1]/div[' + str(
            random.randint(1, 3)) + ']/div/div[2]')  # 点击订单热区
        self.sleep(2)
        self.switch_to_window(4)
        self.assert_title("快手小店")
        #self.assert_text("订单信息", '#root > section > section > main > div:nth-child(3) > div.sc-ieecCq.cFvzFs')
        self.assert_text("订单信息", '//div[contains(text(),"订单信息")]')
        self.sleep(2)

    #修改地址-智能识别
    @skip
    @pytest.mark.p1
    def test_modify_order_address(self):
        return
        self.SubaccountLogin("MERCHANT_DOMAIN", "detest017_75")
        self.assert_text("客服管理", "li[id= 'node_menu_for_intersectionObserver_6']")
        self.click("li[id= 'menu_item_21']")
        self.switch_to_window(1)
        self.check_if_page_404()
        sleep(2)
        self.click('#entry_pc > div.left-area > div > div.session-tab-wrap > div:nth-child(2)') #点击星标
        sleep(1)
        self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[3]/div[1]/div/div/div/div/div/div[1]/div/div')
        sleep(2)
        tab_list = self.get_text("div.tab-comp.feat-sort")
        self.assert_in("个人订单", tab_list)
        self.assert_in("待发货",self.get_text('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div.func-area > div.tab-list > div > div:nth-child(3) > span'))
        self.click('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div.func-area > div.tab-list > div > div:nth-child(3) > span')
        self.click('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div.scroll-list-wrap > div.list > div:nth-child(1) > div > div.status-btns > div.order-action > button:nth-child(1)')
        sleep(2)
        order_address_old = self.get_text('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__body > div > div.order-list-select > div > p:nth-child(2) > span:nth-child(2)')
        order_address_old = order_address_old.replace(',','').replace(' ','')
        print(order_address_old)
        if '汪成' in order_address_old:
            self.type('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__body > div > div.change-address-smartParse > div > textarea',
                      '北京市市辖区昌平区 沙河地区 绿杨花园 李天 15228841203')
        if '李天' in order_address_old:
            self.type(
                '#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__body > div > div.change-address-smartParse > div > textarea',
                '山西省运城市河津市 僧楼镇 绿杨庭院 汪成 15228841201')
        self.click('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__body > div > div.change-address-smartParse > button > span')
        sleep(2)
        self.click('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__footer > div > button.el-button.el-button--primary > span')
        sleep(2)
        self.click(
            '#entry_pc > div.right-area > div > div.right-comp > div.order-list > div.scroll-list-wrap > div.list > div > div > div.status-btns > div.order-action > button:nth-child(1) > span')
        order_address_new = self.get_text(
            '#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__body > div > div.order-list-select > div > p:nth-child(2) > span:nth-child(2)')
        order_address_new = order_address_new.replace(',', '').replace(' ', '')
        print(order_address_new)
        assert order_address_new!=order_address_old
        self.click('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div:nth-child(6) > div > div > div.el-dialog__footer > div > button.el-button.el-button--default > span')
        self.click('#entry_pc > div.right-area > div > div.right-comp > div.order-list > div.scroll-list-wrap > div.list > div > div > div:nth-child(6) > a')
        sleep(5)
        self.click('#root > section > section > main > div:nth-child(3) > div:nth-child(4) > div > table > tbody > tr:nth-child(7) > td > div > span.ant-descriptions-item-content > div > img')
        sleep(1)
        #判断和订单详情是否一致
        assert order_address_new == self.get_text('#root > section > section > main > div:nth-child(3) > div:nth-child(4) > div > table > tbody > tr:nth-child(7) > td > div > span.ant-descriptions-item-content > div > span').replace(',', '').replace(' ', '')
