import random
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestMerchantAccountMiddle(BaseTestCase):

    # 发送文本消息
    @pytest.mark.p0
    @pytest.mark.skip
    def test_text_message(self):
        self.go_workbrench_child(self)
        self.add_text('//*[@id="esim-editor5678"]/div[1]', "测试发送")

        # 消息发送
        self.click(
            '//*[@id="main_root"]/div/div/section/section/main/div/div/div/div/div[1]/div[2]/div[5]/div[1]/div[2]/div[2]/button[2]')

        res = self.get_text('//*[@id="esim-editor5678"]/div[1]')

        assert_that(res == "", "")

    @pytest.mark.p0
    @pytest.mark.skip
    def test_send_assistant(self):
        self.go_workbrench_child(self)

        # 发送评价
        self.click('//*[@id="main_root"]/div/div/div/div[1]/div[2]/div[4]/div[2]/img')

        res = self.get_text('(//div[@class="kwaishop-cs-MessageNoticeCard_text"])[last()]')

        assert_that(res == "过去24小时内，买家没有咨询，您无法发送评价邀请", "")

    @pytest.mark.p1
    @pytest.mark.skip
    def test_check_order(self):
        self.go_workbrench_child(self, id="1856387424")

        res = self.get_text('(//div[@class="kwaishop-cs-BizOrderCard defaultBorderCard"])[last()]/div')

        assert_that(res == "老铁，请您核对订单信息啊大大说", "")

        assert_that(res >= "0", "")
