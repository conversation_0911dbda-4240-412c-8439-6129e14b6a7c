import json
import time
from time import sleep

#import pyautogui
import requests
from hamcrest import *
from ddt import *
from pyautogui import click
from selenium import webdriver
from selenium.webdriver import Chrome
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import pytest

from test_case.customer.online.base import BaseTestCase


class Testgetigongshanghuss(BaseTestCase):

    @pytest.mark.p2
    def test_work_order(self):
        # 任务单【查询】/【重置】
        # 三次点击进入任务单详情，并断言其中左/右订单信息的一致性
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="任务单"]')
        time.sleep(3)
        self.hide_seller_helper_box()

        self.type("input[placeholder='请输入任务单编号']", 'W202410152907477')
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        self.click('//span[text()="查看详情"]')
        time.sleep(8)
        self.hide_seller_helper_box()
        assert_that(self.is_element_present('//div[text()="已完结"]'))
        assert_that(self.is_element_present('//div[text()="W202410152907477"]'))
        assert_that(self.is_element_present('//div[text()="完结任务单"]'))
        assert_that(self.is_element_present('//div[text()="****************"]'))
        assert_that(self.is_element_present('//div[text()="测试多仓库存"]'))
        time.sleep(2)
        self.go_back()
        time.sleep(2)
        self.click('//span[text()="重 置"]')
        time.sleep(2)
        self.type("input[placeholder='请输入任务单编号']", 'W202408298726328')
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        self.click('//span[text()="查看详情"]')
        time.sleep(8)
        self.hide_seller_helper_box()
        assert_that(self.is_element_present('//div[text()="已完结"]'))
        assert_that(self.is_element_present('//div[text()="W202408298726328"]'))
        assert_that(self.is_element_present('//div[text()="完结任务单"]'))
        assert_that(self.is_element_present('//div[text()="任务单超时"]'))
        assert_that(self.is_element_present('//div[text()="2424200064901335"]'))
        assert_that(self.is_element_present('//div[text()="测试多仓库存"]'))
        time.sleep(2)
        self.go_back()
        time.sleep(2)
        self.hide_seller_helper_box()
        self.click('//span[text()="重 置"]')
        self.type("input[placeholder='请输入任务单编号']", 'W202407251378484')
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        self.click('//span[text()="查看详情"]')
        time.sleep(8)

        self.hide_seller_helper_box()
        assert_that(self.is_element_present('//div[text()="已完结"]'))
        assert_that(self.is_element_present('//div[text()="W202407251378484"]'))
        assert_that(self.is_element_present('//div[text()="完结任务单"]'))
        assert_that(self.is_element_present('//div[text()="****************"]'))
        assert_that(self.is_element_present('//div[text()="一分钱测试商品"]'))
        time.sleep(2)
        self.go_back()
        time.sleep(3)

    @pytest.mark.p2
    def test_task_order_status(self):
        # 【任务单状态】筛选的可用条件
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="任务单"]')
        sleep(5)

        self.hide_seller_helper_box()
        self.click('//div[text()="已完结"]')
        sleep(2)
        # 断言按钮的状态
        ele1 = self.is_element_present('//div[@class="ant-row ant-form-item ant-pro-form-item ant-pro-form-item__parcel ant-pro-form-item-disabled ant-pro-form-item-disabled__parcel"]')
        assert ele1
        self.click('//div[text()="全部"]')
        sleep(2)
        # 断言按钮的状态
        ele1 = self.is_element_present('//div[@class="ant-row ant-form-item ant-pro-form-item ant-pro-form-item__parcel ant-pro-form-item-disabled ant-pro-form-item-disabled__parcel"]')
        assert not ele1

    @pytest.mark.p2
    def test_chat_jump(self):
        # 任务单点击买家名称，成功跳转到对应的买家聊天详情，并弹出正确的对应的任务单卡片，点击发送
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="任务单"]')
        sleep(5)

        self.hide_seller_helper_box()
        self.type("input[placeholder='请输入任务单编号']", 'W202408087846157')
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        # 点击买家名称
        self.click('//div[text()="MIkaesan"]')
        sleep(1)
        # 点击弹出的订单卡片
        self.click('//div[text()="点击发送订单"]')
        sleep(1)
        assert_that(self.is_element_present('//div[text()="测试多仓库存"]'))
        assert_that(self.is_element_present('//span[text()="订单编号 ****************"]'))

    @pytest.mark.p2
    def test_category_switch(self):
        # 【需要我处理的任务单】和【我创建的任务单】能正确的分类
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="任务单"]')
        sleep(5)
        self.click('//span[text()="仅看我创建的任务单"]')
        sleep(2)

        self.hide_seller_helper_box()
        self.type("input[placeholder='请输入任务单编号']", 'W202408156575621')
        self.click('//span[text()="查 询"]')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="****************"]'))
        self.click('//span[text()="重 置"]')
        sleep(2)
        self.click('//span[text()="仅看需要我处理的任务单"]')
        sleep(2)

        self.hide_seller_helper_box()
        self.type("input[placeholder='请输入任务单编号']", 'W202410307164764')
        self.click('//span[text()="查 询"]')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="****************"]'))
        sleep(2)


