import random
import time
from time import sleep
from unittest import skip

import pytest
from ddt import *
from selenium.webdriver import Keys

from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory


@ddt
class TestIntelligence(BaseTestCase):

    @pytest.mark.p0
    def test_notice_bell_switch_oper(self):
        """
        客服设置-通知设置-铃声提醒-开关
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='600px')

        # 聊天消息铃声按钮
        CHAT_RECORD_BTN = '//div[contains(text(), "聊天消息铃声")]//button'
        status = self.get_attribute(CHAT_RECORD_BTN, 'aria-checked')
        # 开关开启状态，先关闭
        if status == 'true' or status == True:
            self.click(CHAT_RECORD_BTN)
            sleep(3)
            status = self.get_attribute(CHAT_RECORD_BTN, 'aria-checked')
            assert_that(status == 'false' or status == False)
        # 开关关闭状态，先开启
        if status == 'false' or status == False:
            self.click(CHAT_RECORD_BTN)
            sleep(3)
            status = self.get_attribute(CHAT_RECORD_BTN, 'aria-checked')
            assert_that(status == 'true' or status == True)

    @pytest.mark.p1
    def test_new_chat_record_search(self):
        """
        新版聊天记录搜索
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(2)
        self.click('//div[text()="聊天记录"]')
        sleep(2)
        self.close_common_guide_toast()

        # 输入关键词，在新版聊天记录内搜索
        self.input('//input[contains(@placeholder, "输入客服ID")]', '140000046164300')
        self.input('//input[contains(@placeholder, "输入买家ID")]', '1691062442')
        self.click('//label[@title="人工接待"]/..//following::div[1]//child::span[@title="全部"]')
        self.click('//div[@title="否"]')
        self.input('//input[@placeholder="输入订单ID、商品ID或聊天关键词"]', '你好')
        self.click('//input[@placeholder="结束日期"]')
        self.click('//span[text()="最近30天"]')
        self.click('//span[text()="查 询"]')
        sleep(15)
        # 搜索查询结果，并输出
        row_1st = self.get_text('//tr[2]')                          # tr[1]: 表头
        self.is_element_visible('//td[text()="客服助手"]')
        assert_that("1691062442" in row_1st)
        print('[查询结果第1行]：', row_1st)

    @pytest.mark.p1
    def test_new_chat_record_loading_page(self):
        """
        新版聊天记录搜索-分页回测
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="聊天记录"]')
        sleep(3)
        self.close_common_guide_toast()

        # 输入关键词，在新版聊天记录内搜索
        self.click('//input[@placeholder="结束日期"]')
        self.click('//span[text()="最近30天"]')
        self.click('//span[text()="查 询"]')
        sleep(12)

        # 搜索查询结果，并输出
        row_1st = self.get_text('//tr[2]')              # tr[1]: 表头
        for i in range(2, 4):
            self.click('//a[text()="'+str(i)+'"]')
            sleep(10)
            assert_that(self.get_text('//tr[2]') != row_1st)

    @pytest.mark.p1
    def test_offline_chat_dispatch(self):
        """
        离线留言批量会话分配
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='离线留言']"):
            return
        self.click("//div[text()='离线留言']")
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='650px')

        # 校验离线留言文案
        assert_that(self.is_element_present('//div[text()="离线留言"]'))
        self.is_element_present('//div[contains(text(), "离线留言产生场景")]')
        self.is_element_present('//div[contains(text(), "离线留言处理方式")]')

        # 校验"标记为已处理按钮"是否存在
        assert_that(self.is_element_present('//div[text()="标记为已处理"]'))
        assert_that(self.is_element_present('//div[text()="批量分配"]'))
        return      # todo: 红点校验条件有没有正确

        # 校验离线留言红点
        red_dot_str = self.get_text('//img[contains(@src, "62ttlfy9xe")]/../preceding::div[1]')
        if len(red_dot_str) > 0:
            # 如果红点存在，待回复列表里面有对应买家
            assert_that(self.is_element_present('//span[@class="ant-checkbox"]'))
            self.click('//span[@class="ant-checkbox"][1]')
            # 分配会话、打开弹窗，校验弹窗元素
            self.click('//div[text()="分配会话"]')
            sleep(2)
            assert_that(self.is_element_present('//div[text()="分配给指定客服"]'))

    @pytest.mark.p2
    def test_customer_manage_search(self):
        """
        客服管理：展示列表并查询客服
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="管理工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="实时管理"]')
        sleep(3)
        self.click('//span[text()="刷新"]')
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='600px')

        # 列表数据获取
        assist_status = self.get_text('//tr[2]')
        assert_that("店铺数据" in assist_status)
        assert_that("主账号" in assist_status)
        assert_that("在线" in assist_status)
        assert_that("是小可儿" in assist_status)
        print("[客服状态(第一行)]：", assist_status)

        # 搜索结果，原先按 self.get_text('//tr[2]') 搜索
        self.input('//input[@placeholder="请输入客服子账号昵称进行搜索"]', 'wwww')
        self.click('//span[text()="查 询"]')
        sleep(3)
        assist_status = self.get_text('//tr[contains(@class, "ant-table-row-level-0")]')
        assert_that("wwww" in assist_status)

        assert_that("客服" in assist_status)
        # 如果“在线”状态，手动切离线
        if "在线" in assist_status:
            self.click('//span[text()="在线"]')
            self.click('//*[text()="离线"]')

        # 查看登录日志
        self.click('//span[text()="查看登录日志"]')
        sleep(3)
        assert_that(self.is_element_present('//div[text()="登录记录"]'))
        assert_that(self.is_element_present('//td[text()="离线"]'))

    @pytest.mark.p2
    def test_customer_manage_dispatch(self):
        """
        客服管理：批量分配客服会话
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="管理工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="实时管理"]')
        sleep(3)
        self.click('//span[text()="刷新"]')
        sleep(3)

        # 批量分配客服会话
        self.click('//span[text()="分配"]')
        assert_that(self.is_element_present('//div[text()="会话分配"]'))
        assert_that(self.is_element_present('//div[contains(text(), "转移后你将看不到该会话")]'))
        assert_that("账号ID" in self.get_text('//tr[1]'))             # todo: 帐号昵称表头错误字修复

    @pytest.mark.p2
    def test_session_manage_search(self):
        """
        会话监控：展示列表并查询客服
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="管理工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="实时管理"]')
        sleep(3)
        self.click('//div[text()="会话监控"]')
        sleep(8)

        # 列表搜索
        return      # todo: 输入框监控
        self.input('//input[@placeholder="请输入快手号或快手ID进行搜索"]', "2941560170")
        self.click('//span[text()="筛选"]')
        return      # todo: 元素未出现情况下需过滤assert
        if self.is_element_visible('//tbody/tr[1]'):
            res = self.get_text('//tbody/tr[1]')
            assert_that("2941560170" in res)
            # 查看详情
            self.click('//span[text()="详情"]')
            sleep(3)
            assert_that(self.is_element_present('//div[text()="会话详情"]'))
            assert_that(self.get_text('//span[@class="buyerNickName"]') == "西门")
            print("[当前会话列表第一条消息发送时间]：", self.get_text('//span[@class="send-item time"][1]'))

    @pytest.mark.p2
    def test_session_manage_customer_transfer(self):
        """
        会话监控：转移客服会话
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="管理工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="实时管理"]')
        sleep(3)
        self.click('//div[text()="会话监控"]')
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='600px')

        # 列表数据获取
        return      # todo: 输入框重构，影响已有功能
        self.click('//button[@class="el-button el-button--default"]')
        if self.is_element_present('//tbody/tr[1]'):
            res = self.get_text('//tbody/tr[1]')
            print("[当前搜索结果第一行]：", res)
            # 转移会话
            self.click('//span[text()="转移"]')
            sleep(3)
            assert_that(self.is_element_present('//span[text()="转移会话"]'))
            assert_that(self.is_element_present('//span[text()="1"]'))
            assert_that(self.is_element_present('//div[text()="帐号昵称"]'))

    @pytest.mark.p2
    def test_early_warning_manage(self):
        """
        预警管理
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="管理工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="预警管理"]')
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='0px')
        self.click('//span[text()="查 询"]')
        sleep(3)
        if self.is_element_visible('//tbody/tr[1]'):
            res = self.get_text('//tbody/tr[1]')
            print("[当前搜索结果第一行]：", res)
            if self.is_element_visible('//span[text()="转移"]'):
                self.click('//span[text()="转移"]')
                sleep(3)
                assert_that(self.is_element_visible('//div[text()="转移会话"]'))
                # assert_that(self.is_element_visible('//th[text()="账号昵称"]'))

    @pytest.mark.p1
    def test_fast_reply_list(self):
        """
        个人快捷回复列表
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="接待工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="个人快捷回复"]')
        sleep(3)
        if self.is_element_present('//p[text()="立即使用"]'):
            self.click('//p[text()="立即使用"]')

        # 展示快捷回复第1条分组
        ls = self.find_elements('//div[@class="ant-collapse-header"]')
        # ls[0].click()
        # 校验第1条快捷回复内容
        self.is_element_present('//span[contains(text(), "您好")]')
        # 更新快捷回复内容
        rand_num = random.randint(1000, 9999)
        self.driver.execute_script('document.getElementsByClassName("ant-btn-link")[0].click()')
        self.update_text('//textarea', self.get_text('//textarea')[:-4] + str(rand_num))
        self.click('//span[text()="确 定"]')
        sleep(6)
        assert_that(self.is_element_present('//span[contains(text(), "'+str(rand_num)+'")]'))

    @pytest.mark.p1
    def test_fast_reply_group_list(self):
        """
        个人快捷回复分组列表
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="接待工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="个人快捷回复"]')
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='600px')
        # 立即使用新人引导按钮点击后，进入编辑页面
        if self.is_element_present('//div[contains(@style, "border-radius: 32px")]'):
            self.click('//div[contains(@style, "border-radius: 32px")]')
        if self.is_element_present('//p[contains(@style, "border-radius: 32px")]'):
            self.click('//p[contains(@style, "border-radius: 32px")]')

        # 更新个人快捷回复分组第1条内容
        rand_num = random.randint(1000, 9999)
        self.click('//span[@aria-label="edit"]')
        self.update_text('//input[@placeholder="最多10个字符"]', self.get_text('//input[@placeholder="最多10个字符"]')[:-4]+str(rand_num))
        self.click('//span[text()="确 定"]')
        sleep(6)
        assert_that(self.is_element_present('//p[contains(text(), "'+str(rand_num)+'")]'))

    @pytest.mark.p1
    def test_group_fast_reply_list(self):
        """
        团队快捷回复列表
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="接待工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="团队快捷回复"]')
        sleep(3)
        if self.is_element_present('//div[contains(@style, "border-radius: 32px")]'):
            self.click('//div[contains(@style, "border-radius: 32px")]')

        # 展示快捷回复第1条分组
        ls = self.find_elements('//div[@class="ant-collapse-header"]')
        # ls[0].click()
        # 校验第1条快捷回复内容
        self.is_element_present('//span[contains(text(), "您好")]')
        # 更新快捷回复内容
        rand_num = random.randint(1000, 9999)
        self.driver.execute_script('document.getElementsByClassName("ant-btn-link")[0].click()')
        self.update_text('//textarea', self.get_text('//textarea')[:-4] + str(rand_num))
        self.click('//span[text()="确 定"]')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "' + str(rand_num) + '")]'))

    @pytest.mark.p1
    def test_group_fast_reply_group_list(self):
        """
        团队快捷回复分组列表
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="接待工具"]')
        self.click('//li[@role="menuitem"]/span/div[text()="团队快捷回复"]')
        sleep(3)
        if self.is_element_present('//div[contains(@style, "border-radius: 32px")]'):
            self.click('//div[contains(@style, "border-radius: 32px")]')

        # 获取团队快捷回复第1条分组
        ls = self.find_elements('//div[@class="ant-collapse-header"]')
        print("[第一条快捷回复分组内容]：", ls[0].text)
        rand_num = random.randint(1000, 9999)
        self.click('//span[@aria-label="edit"]')
        self.update_text('//input[@placeholder="最多10个字符"]',
                         self.get_text('//input[@placeholder="最多10个字符"]')[:-4] + str(rand_num))
        self.click('//span[text()="确 定"]')
        sleep(3)
        assert_that(self.is_element_present('//p[contains(text(), "' + str(rand_num) + '")]'))

        # 导入功能
        self.click('//span[text()="导 入"]')
        assert_that(self.is_element_present('//div[text()="导入快捷回复"]'))
        assert_that(self.is_element_present('//a[text()="下载模板"]'))

    @pytest.mark.p1
    def test_customer_entrance_switch_oper(self):
        """
        客服入口开关操作
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="客服入口"]')
        sleep(3)

        # 直播小黄车客服入口
        entrance_name = "直播间小黄车展示客服入口"
        checked = self.get_attribute('//span[text()="'+entrance_name+'"]//following::button', 'aria-checked')
        assert_that("小黄车" in self.get_text('//span[text()="'+entrance_name+'"]/..//following::p'))
        if entrance_name == "false" or entrance_name == False:
            self.click('//span[text()="'+entrance_name+'"]//following::button')
            checked = self.get_attribute('//span[text()="' + entrance_name + '"]//following::button', 'aria-checked')
            sleep(3)
            assert_that(entrance_name == "true" or entrance_name == True)

        # 直播公评客服入口
        entrance_name = "直播间公评展示客服入口"
        checked = self.get_attribute('//span[text()="'+entrance_name+'"]//following::button', 'aria-checked')
        assert_that("直播公评" in self.get_text('//span[text()="'+entrance_name+'"]/..//following::p'))
        if entrance_name == "false" or entrance_name == False:
            self.click('//span[text()="'+entrance_name+'"]//following::button')
            checked = self.get_attribute('//span[text()="' + entrance_name + '"]//following::button', 'aria-checked')
            sleep(3)
            assert_that(entrance_name == "true" or entrance_name == True)

        # 回头客差评入口
        entrance_name = "回头客差评转入人工客服接待"
        checked = self.get_attribute('//span[text()="'+entrance_name+'"]//following::button', 'aria-checked')
        assert_that("回头客" in self.get_text('//span[text()="'+entrance_name+'"]/..//following::p'))
        if entrance_name == "false" or entrance_name == False:
            self.click('//span[text()="'+entrance_name+'"]//following::button')
            checked = self.get_attribute('//span[text()="' + entrance_name + '"]//following::button', 'aria-checked')
            sleep(3)
            assert_that(entrance_name == "true" or entrance_name == True)

        # 直播间商品信息客服入口
        entrance_name = "直播间商品信息展示客服入口"
        checked = self.get_attribute('//span[text()="'+entrance_name+'"]//following::button', 'aria-checked')
        assert_that("商品信息" in self.get_text('//span[text()="'+entrance_name+'"]/..//following::p'))
        if entrance_name == "false" or entrance_name == False:
            self.click('//span[text()="'+entrance_name+'"]//following::button')
            checked = self.get_attribute('//span[text()="' + entrance_name + '"]//following::button', 'aria-checked')
            sleep(3)
            assert_that(entrance_name == "true" or entrance_name == True)

    @pytest.mark.p1
    def test_set_basic_dispatch(self):
        """
        客服开启基础分流
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(12)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="分流方式"]')
        sleep(3)

        status = self.get_text('//div[text()="基础分流"]//following::div[1]//child::span[@class="ant-switch-inner"]')
        if status == "关":
            self.click('//div[text()="基础分流"]//following::div[1]')
            sleep(6)
            assert_that(self.is_element_present('//span[text()="确认切换为基础分流吗"]'))
            assert_that(self.is_element_present('//div[contains(text(), "基础分流")]'))
            self.click('//span[text()="确认开启"]')
            sleep(3)
            status = self.get_text('//div[text()="基础分流"]//following::div[1]//child::span[@class="ant-switch-inner"]')
            assert_that(status == "开")

    @pytest.mark.p1
    def test_set_group_dispatch(self):
        """
        客服开启分组分流
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(12)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="分流方式"]')
        sleep(3)
        self.hide_seller_helper_box()
        self.move_seller_helper_box(bottom='600px')

        status = self.get_text('//div[text()="分组分流"]//following::div[1]//child::span[@class="ant-switch-inner"]')
        if status == "关":
            self.click('//div[text()="分组分流"]//following::div[1]')
            sleep(3)
            assert_that(self.is_element_present('//span[text()="确认切换为分组分流吗"]'))
            assert_that(self.is_element_present('//div[contains(text(), "分组分流")]'))
            self.click('//span[text()="确认开启"]')
            sleep(3)
            status = self.get_text('//div[text()="分组分流"]//following::div[1]//child::span[@class="ant-switch-inner"]')
            assert_that(status == "开")

        # 查看客服
        self.click('//div[contains(text(), "参与客服")]//following::button[1]')
        sleep(3)
        assert_that(self.is_element_visible('//div[text()="客服管理"]'))
        assert_that(self.is_element_visible('//div[contains(text(), "100个客服")]'))
        self.input('//input[@placeholder="请输入客服昵称"]', "可儿")
        self.click('//button[contains(@class, "ant-input-search-button")]')
        sleep(3)
        assert_that("可儿" in self.get_text('//tr[2]'))
        self.click('//span[contains(@class, "anticon-system-close-medium-line")]')

        # 绑定订单状态
        self.click('//div[contains(text(), "接待范围")]//following::button[2]')
        sleep(3)
        assert_that(self.is_element_present('//div[text()="接待范围设置"]'))
        assert_that(self.is_element_present('//span[text()="付款未发货"]'))
        assert_that(self.is_element_present('//span[text()="下单未付款"]'))
        self.click('//div[contains(@class, "button") and text()="设置"]')
        sleep(3)
        assert_that(self.is_element_present('//div[text()="绑定订单状态"]'))
        assert_that(self.is_element_present('//span[text()="付款未发货"]'))
        assert_that(self.is_element_present('//span[text()="下单未付款"]'))

    @pytest.mark.p1
    def test_dispatch_rule_setting_repeated_visit(self):
        """
        客服分流规则设置：重复来访
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="规则设置"]')
        sleep(3)
        # 最近联系客服优先: 不启动
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[4].click()')
        # 分配不超过最大接待量限制：不启动
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[3].click()')
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[6].click()')

    @pytest.mark.p1
    def test_dispatch_rule_setting_new_buyer(self):
        """
        客服分流规则设置：新客优先进线
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="规则设置"]')
        sleep(3)
        # 新客优先进线: 启动
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[7].click()')

    @pytest.mark.p1
    def test_dispatch_rule_setting_basic_choice(self):
        """
        客服分流规则设置：分配方式选择
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="规则设置"]')
        sleep(3)
        # 分配方式
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[2].click()')
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[1].click()')
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[0].click()')

    @pytest.mark.p1
    def test_dispatch_rule_setting_offline(self):
        """
        客服分流规则设置：离线分流
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="规则设置"]')
        sleep(3)
        # 离线分流
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[10].click()')
        self.driver.execute_script('document.getElementsByClassName("ant-radio-input")[9].click()')
        self.update_text('//input[@placeholder="请输入1-30之间的正整数"]', '9')
        self.click('//span[text()="保存并生效"]')
        assert_that(self.get_text('//input[@placeholder="请输入1-30之间的正整数"]') == '9')

    @pytest.mark.p1
    def test_dispatch_rule_setting_undispatch(self):
        """
        客服分流规则设置：不分流
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="分流设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="规则设置"]')
        sleep(3)
        # 不分流
        customer_res = self.get_text('//div[contains(@class, "listContainer")]')
        if '请选择不自动分配人员' in customer_res:
            # 无不分流人员
            self.click('//span[text()="添加不分流人员"]')
            sleep(3)
            assert_that(self.is_element_visible('//div[text()="设置不分流账号"]'))
            assert_that("添加客服角色" in self.get_text('//div[contains(text(), "默认不分流")]'))
            self.driver.execute_script('document.getElementsByClassName("ant-checkbox-input")[2].click()')
            self.click('//span[text()="确认选择"]')
            sleep(3)
            customer_res = self.get_text('//div[contains(@class, "listContainer")]')
            assert_that(len(customer_res) > 0 and ('请选择不自动分配人员' not in customer_res))
        else:
            # 有不分流人员
            self.click('//span[text()="修改不分流人员"]')
            sleep(3)
            assert_that(self.is_element_visible('//div[text()="设置不分流账号"]'))
            assert_that("添加客服角色" in self.get_text('//div[contains(text(), "默认不分流")]'))
            self.click('//span[text()="确认选择"]')
            sleep(3)
            customer_res_new = self.get_text('//div[contains(@class, "listContainer")]')
            assert_that(len(customer_res) > 0 and (customer_res_new == customer_res))

    @pytest.mark.p2
    def test_auto_setting_faq(self):
        """
        客服自动化设置：常见问题
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="自动化设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="自动接待"]')
        sleep(3)
        self.hide_seller_helper_box()

        #问题列表
        question_list=self.get_text('//table/tbody')
        # print(question_list)
        assert_that(len(question_list)>0)

        #问题修改
        self.click('//span[text()="修改"]')
        question_xpath = '//input[@class="ant-input"]'#编辑问题的问题输入框
        question_reply_xpath = '//textarea[@class="ant-input"]'#编辑问题的回复输入框
        a = self.get_text(question_xpath)
        b = self.get_text(question_reply_xpath)

        self.clear(question_xpath)#清除
        self.clear(question_reply_xpath)
        c=self.get_text(question_xpath)
        d=self.get_text(question_reply_xpath)
        # print(self.get_text(question_xpath))
        # print(self.get_text(question_reply_xpath))
        assert_that(len(self.get_text(question_xpath))==0 and len(self.get_text(question_reply_xpath))==0)

        #复原
        self.send_keys(question_xpath,"web自动化问题修改"+f'{random.randint(1,100)}')
        # self.send_keys(question_reply_xpath,"web自动化问题修改回复")
        self.click('//span[text()="确 定"]')
        #问题发布测试
        assert_that(self.is_element_visible('//div[text()="提示：常见问题列表有更改，保存并发布后才会生效"]'))#是否出现提示
        self.click('//span[text()="保存并发布"]')
        self.click('//span[text()="确 定"]')
        sleep(3)
        assert_that(not self.is_element_visible('//div[text()="提示：常见问题列表有更改，保存并发布后才会生效"]'))  # 提示是否消失

        # 问题勾选测试
        self.click('//table/tbody/tr[1]/td[1]/label')#点击
        assert_that(self.is_element_visible('//div[text()="提示：常见问题列表有更改，保存并发布后才会生效"]'))#是否出现提示
        # 复原
        self.click('//table/tbody/tr[1]/td[1]/label')#点击
        # sleep(3)
        assert_that(not self.is_element_visible('//div[text()="提示：常见问题列表有更改，保存并发布后才会生效"]'))# 提示是否消失

    @pytest.mark.p2
    def test_auto_setting_auto_msg(self):
        """
        客服自动化设置：自动回复
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.driver.implicitly_wait(20)
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="自动化设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="自动消息"]')
        sleep(3)
        num=0
        while not self.is_element_visible('//*[@id="root"]/div/div/div[1]/div[2]/div[1]/div[3]/button[2]/span'):
            print('正在加载')
            sleep(2)
            num+=2
            self.click('//li[@role="menuitem"]/span/div[text()="离线回复"]')
            self.click('//li[@role="menuitem"]/span/div[text()="自动消息"]')
            if num==100:
                print("网络异常")
                return
        #停用/启用
        if self.is_element_visible('//span[text()="催付"]/../..//child::span[text()="停 用"]'):       # self.click('//span[text()="催付"]/../..//child::span[text()="停 用"]')
            self.click('//span[text()="催付"]/../..//child::span[text()="停 用"]')#点击停用
            self.click('//span[text()="确 定"]')#点击确定
        self.click('//span[text()="催付"]/../..//child::span[text()="立即开启"]')#点击立即开启                     self.click('//span[text()="催付"]/../..//child::span[text()="立即开启"]')
        self.click('//span[text()="保 存"]')#保存                            self.click('//span[text()="保 存"]')
        sleep(5)
        assert_that(self.is_element_visible('//span[text()="催付"]/../..//child::span[text()="停 用"]'))#停用按钮是否可见
        #规则详情
        self.click('//span[text()="催付"]/../..//child::span[text()="规则详情"]')#点击规则详情
        card_title=self.get_text('//input[@class="ant-input"]')
        self.clear('//input[@class="ant-input"]')
        num=random.randint(1, 100)
        new_card_title=card_title[:-2] + f'{num}'
        self.send_keys('//input[@class="ant-input"]',new_card_title)
        self.click('//span[text()="保 存"]')  # 保存
        sleep(4)
        card_title_display=self.get_text('//span[contains(text(), "催付")]/..//child::span[contains(@class, "card-title")]')          # self.get_text('//span[contains(text(), "自动核单")]/..//child::span[contains(@class, "card-title")]')
        print(card_title_display,'   =   ',new_card_title)
        assert_that(card_title_display==new_card_title)

    @pytest.mark.p2
    def test_auto_setting_order_question_answer(self):
        """
        客服自动化设置：订单问答
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.driver.implicitly_wait(20)
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="自动化设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="订单问答"]')
        sleep(4)
        self.hide_seller_helper_box()

        #数据统计
        self.click('//span[text()="最近7天"]')
        sleep(4)
        start=self.get_attribute('//input[@placeholder="开始日期"]','value')
        end=self.get_attribute('//input[@placeholder="结束日期"]','value')
        print(start)
        print(end)
        # print(end-start)
        assert_that(start!=end)

        #停用/启用
        """
        if self.ENV == "prt":
            start_button=self.get_text('//button[@class="ant-btn"]')
            if start_button=='停 用':
                self.click('//button[@class="ant-btn"]')#停用
                self.click('//span[text()="确 定"]')#确认
                sleep(2)
                self.click('//span[text()="开 启"]')#开启
                sleep(3)
                now_button_text = self.get_text('//button[@class="ant-btn"]')
                print('1'+now_button_text)
                assert_that(now_button_text=='停 用')
            else:
                self.click('//span[text()="开 启"]')  # 开启
                sleep(3)
                now_button_text = self.get_text('//button[@class="ant-btn"]')
                print('1' + now_button_text)
                assert_that(now_button_text == '停 用')
            #场景预览
            sleep(3)
            self.hover('//table/tbody/tr[1]/td[6]/div/span')
            sleep(2)
            class_name=self.get_attribute('//table/tbody/tr[1]/td[6]/div/span','class')
            assert_that('ant-popover-open' in class_name)
            self.click('//*[contains(text(), "一级回复")]')

            #编辑修改
            self.click('//tbody/tr[1]//child::button')
            sleep(2)
            first_reply=self.get_text('//*[@id="main_root"]//child::div[1]/span/input')
            second_reply=self.get_text('//*[@id="main_root"]//child::div[2]/span/input')
            new_first_reply=first_reply[:-1]+f'{random.randint(1,9)}'
            new_second_reply=second_reply[:-1]+f'{random.randint(1,9)}'
            self.clear('//*[@id="main_root"]//child::div[1]/span/input')
            self.clear('//*[@id="main_root"]//child::div[2]/span/input')

            self.send_keys('//*[@id="main_root"]//child::div[1]/span/input',new_first_reply)

            self.send_keys('//*[@id="main_root"]//child::div[2]/span/input',new_second_reply)
            # sleep(2)
            self.click('//span[text()="确 认"]')#确认按钮

            sleep(2)
            now_first_reply = self.get_text(
                '//table/tbody/tr[1]/td[3]/span')
            now_second_reply = self.get_text(
                '//table/tbody/tr[1]/td[5]/span')
            print(new_first_reply, new_second_reply)
            print(now_first_reply, now_second_reply)
            assert_that(now_first_reply==new_first_reply and new_second_reply==now_second_reply)
        else:  # online     todo: 线上代码适配
        """
        # todo: 线上代码适配
        start_button = self.get_text('//span[@class="ant-switch-inner"]')
        if start_button == '开启':
            self.click('//span[@class="ant-switch-inner"]')
            sleep(1)
            self.click('//span[text()="确 定"]')
            sleep(2)
            assert_that(self.get_text('//span[@class="ant-switch-inner"]') == "停用")
        else:
            self.click('//span[@class="ant-switch-inner"]')
            sleep(2)
            assert_that(self.get_text('//span[@class="ant-switch-inner"]') == "开启")

    @pytest.mark.p2
    def test_auto_setting_offonine_reply(self):
        """
        客服自动化设置：离线回复
        """
        self.go_workbrench(self)
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.driver.implicitly_wait(20)
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="自动化设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="离线回复"]')
        #编辑
        offonline_text=self.get_text('//*[@id="root"]//child::textarea')#//*[@id="root"]/div/div[3]/span/textarea
        print(offonline_text)
        assert_that(self.is_element_enabled('//*[@id="root"]/div/div[3]/button')==False)
        self.send_keys('//*[@id="root"]//child::textarea',Keys.BACK_SPACE)

        self.send_keys('//*[@id="root"]//child::textarea',random.randint(1,9))
        new_offonline_text = self.get_text('//*[@id="root"]//child::textarea')
        sleep(2)
        assert_that(self.is_element_enabled('//*[@id="root"]/div/div[3]/button') == True)
        self.click('//*[@id="root"]/div/div[3]/button/span')
        sleep(2)
        assert_that(self.is_element_enabled('//*[@id="root"]/div/div[3]/button') == False)
        #数据展示
        now_offonline_text = self.get_text('//*[@id="root"]//child::textarea')
        assert_that(now_offonline_text == new_offonline_text)

    @pytest.mark.p2
    def test_auto_setting_goods_question_reply1(self):
        """
        客服自动化设置：商品问答
        """
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.driver.implicitly_wait(20)
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="自动化设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="商品问答"]')
        self.sleep(4)

        # 增加xss漏洞治理代码报错判断
        assert_that(not self.is_element_visible('//*[contains(text, "javascript")]'))
        assert_that(not self.is_element_visible('//*[contains(text, "im.kwaixiaodian.com 显示")]'))

        #商品问答筛选
        self.send_keys('//*[@id="questionId"]','111')
        self.click('//*[@id="goods-qa"]//child::span[text()="筛 选"]')#点击筛选            self.click('//*[@id="goods-qa"]//child::span[text()="筛 选"]')
        none_text=self.get_text('//div[text()="没有符合条件的商品"]')#//div[text()="没有符合条件的商品"]
        print(none_text)
        sleep(7)#等待加载
        assert_that(none_text=='没有符合条件的商品')
        self.clear('//*[@id="questionId"]')
        self.send_keys('//*[@id="questionId"]',Keys.ENTER)
        sleep(5)
        #商品问答展示
        good_desc=self.get_text('//span[contains(@class, "info-title")]')               # self.get_text('//span[contains(@class, "info-title")]')
        good_desc=good_desc.replace('\n','')#去掉换行符
        print(good_desc)
        assert_that(len(good_desc)>1)

        #商品问答编辑
        is_open=self.get_text('//table/tbody/tr/td[3]/span')
        if is_open=='未启用':
            self.click('//table/tbody/tr/td[4]/span/a[2]')#点击启用
        self.click('//a[text()="编辑"]')#点击编辑     self.click('//a[text()="编辑"]')
        sleep(4)
        first_question=self.get_text('//label[1]/span[2]/span')#获取第一个问题的文本
        first_question=first_question.replace('\n','')#去掉换行符
        first_question=first_question.replace('：',':')#去掉换行符
        print('first_question'+first_question)
        sleep(2)
        self.click('//label[contains(@class, "ant-checkbox-wrapper")]')  # 反选择  self.click('//label[contains(@class, "ant-checkbox-wrapper")]')
        sleep(1.5)
        if self.get_attribute('//label[contains(@class, "ant-checkbox-wrapper")]','class')=='ant-checkbox-wrapper ant-checkbox-wrapper-checked LeftItemContent':#以input框所在的属性class来确定是否够选上，条件成立表示勾选
            self.click('//span[text()="确 定"]')#点击确定
            sleep(3)
            display_question_text=self.get_text('//table/tbody/tr/td[2]')
            display_question_text=display_question_text.replace('\n','')#去掉换行符
            print("1"+display_question_text)
            assert_that(first_question in display_question_text)
        else:
            self.click('//span[text()="确 定"]')  # 点击确定
            sleep(3)
            display_question_text = self.get_text('//table/tbody/tr/td[2]')
            display_question_text=display_question_text.replace('\n','')
            print("2"+display_question_text)
            assert_that(first_question not in display_question_text)

    @pytest.mark.p2
    def test_auto_setting_goods_question_reply2(self):
        """
        客服自动化设置：商品问答
        """
        return      # todo: 暂时注释报错待修改
        self.go_workbrench(self, user='customer-z')
        if not self.is_element_visible("//div[text()='设置']"):
            return
        self.driver.implicitly_wait(20)
        self.click("//div[text()='设置']")
        sleep(3)
        self.click('//div[text()="自动化设置"]')
        self.click('//li[@role="menuitem"]/span/div[text()="商品问答"]')
        self.sleep(4)

        # 增加xss漏洞治理代码报错判断
        assert_that(not self.is_element_visible('//*[contains(text, "javascript")]'))
        assert_that(not self.is_element_visible('//*[contains(text, "im.kwaixiaodian.com 显示")]'))
        self.hide_seller_helper_box()

        # 筛选
        self.send_keys('//*[@id="content"]','2')
        self.send_keys('//*[@id="content"]', Keys.ENTER)
        sleep(3)
        assert_that(self.is_element_visible('//div[text()="没有符合条件的问答"]'))
        self.clear('//*[@id="content"]')
        self.send_keys('//*[@id="content"]', Keys.ENTER)
        sleep(3)
        origin_text=self.get_text('//*[@id="qa-list"]//child::tbody')
        print("origin"+origin_text)
        assert_that(len(self.get_text('//*[@id="qa-list"]//child::tbody'))!=0)
        #新增问答，然后判断tbody的内容变化来判断列表展示
        self.click('//*[@id="qa-list"]/div[1]/button')
        sleep(1)
        self.send_keys('//*[@id="question"]','测试问题1')
        self.send_keys('//*[@id="response"]','测试1回复11')
        self.click('//span[text()="确 定"]')
        sleep(3)

        now_text = self.get_text('//*[@id="qa-list"]//child::tbody')
        print("now" + now_text)
        assert_that(now_text!=origin_text)

        #对刚刚新增问题进行编辑
        add_question_title=self.get_text('//*[@id="qa-list"]//child::tr[3]//child::span')
        self.click('//*[@id="qa-list"]//child::tr[3]//child::a[contains(text(),"编辑")]')#//*[@id="qa-list"]//child::tr[3]//child::a[contains(text(),"编辑")]
        new_add_question_title=add_question_title[:-1]+f'{random.randint(2,9)}'
        self.clear('//*[@id="question"]')
        self.send_keys('//*[@id="question"]',new_add_question_title)
        self.click('//span[text()="修 改"]')#点击修改
        sleep(3)
        now_add_question_title =self.get_text('//*[@id="qa-list"]//child::tr[3]//child::span')
        assert_that(new_add_question_title==now_add_question_title)
        #将新增的问题删除恢复原样避免干扰下次测试
        self.click('//*[@id="qa-list"]//child::tr[3]//child::a[contains(text(),"删除")]')
        sleep(2)
        self.click('//span[text()="继续删除"]')#点击确定
        sleep(5)

        # 关联商品
        if '已关联' in self.get_text('//*[@id="qa-list"]//child::tr[2]/td[3]/a'):
            self.click('//*[@id="qa-list"]//child::tr[2]/td[3]/a')
            test_text=self.get_text('//*[@id="qa-list"]//child::tbody')
            print(test_text)
            assert_that(len(test_text)>0)




































