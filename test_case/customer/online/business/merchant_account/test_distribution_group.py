import random
import time
import datetime
from time import sleep
from unittest import skip

import pytest
from ddt import *
from test_case.customer.online.base import BaseTestCase
from hamcrest import *
from utils.time_help import DateFactory
from selenium.webdriver import Keys, ActionChains

from test_case.customer.online.tools.order_tools import OrderTools


@ddt
class TestDistributionGroup(BaseTestCase):

    def test_cooperation_left(self):
        """
        消息红点
        """
        self.go_cooperation_workbench(self, user='supply_express_account',id="**********")
        sleep(3)
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'
        #发送消息给另一个账号
        send_msg = "合作UI自动化" + str(random.randint(1000, 100000))
        # print(send_msg)
        self.input(CHAT_EDIT_TEXT,send_msg)
        self.click(CHAT_SEND_BTN)
        # 获取发送时间
        send_time=self.find_elements('//span[@class="kwaishop-cs-LayoutDefaultWrapper_sendItem kwaishop-cs-LayoutDefaultWrapper__time"]')[-1].text

        #回到另外一个账号查看
        self.go_cooperation_workbench(self, user='customer_merchant',id="*********")
        #合作tab红点存在
        assert_that(self.is_element_visible('//div[@class="unreadDot___uYmcd"]'))
        #判断消息列表红点不为空
        assert_that(self.is_element_visible('//div[@class="SessionBaseCard-unread-rot"]'))
        #点击操作
        self.click('//div[@class="SessionBaseCard-unread-rot"]')
        self.click('//div[contains(text(), "珊珊86621")]')
        sleep(3)
        #红点消失
        assert_that(self.is_element_visible('//div[@class="CpFdfqTTqaeoYiAEm0PM"]')==False)
        assert_that(self.is_element_visible('//div[@class="SessionBaseCard-unread-rot"]')==False)
        self.input(CHAT_EDIT_TEXT,send_msg)
        self.click(CHAT_SEND_BTN)
        # self.open('https://im.kwaixiaodian.com/workbench/zone/cooperation?role=merchant#ud=1691062442')
        sleep(2)
        msg_bubble='//div[contains(text(),'+send_msg+')]'
        assert_that(self.is_element_visible(msg_bubble))
        rec_send_time=self.find_elements('//span[@class="kwaishop-cs-LayoutDefaultWrapper_sendItem kwaishop-cs-LayoutDefaultWrapper__time"]')[-1].text
        #assert_that(rec_send_time==send_time)
        #是否滚动判断
        #判断滚动是否成功
        # assert_that(self.is_element_visible('//div[contains(text(), "珊珊86621")]')==False)

    def test_cooperation_right_basic_info(self):
        self.go_cooperation_workbench(self, user='chunxiao',id="1691062442")
        sleep(4)
        name=self.get_text("//span[@class='cps-communicate-baseinfo-user-name']")
        assert_that(name=="珊珊86621")

        assert_that(self.is_element_visible("//span[contains(text(),'近30天带货信息')]")==True)
        assert_that(self.is_element_visible("//span[contains(text(),'近30日店铺供货信息')]")==True)
        eles=self.find_elements('//div[@class="cps-communicate-baseinfo-expand-button"]')

        for ele in eles:
            ele.click()
        assert_that(self.is_element_visible("//span[contains(text(),'近30天带货信息')]") == False)
        assert_that(self.is_element_visible("//span[contains(text(),'近30日店铺供货信息')]") == False)
        eles1 = self.find_elements('//div[@class="cps-communicate-baseinfo-expand-button"]')
        for ele in eles1:
            ele.click()
        #点击跳转达人
        self.click('//span[@class="anticon anticon-normal-list-my-line"]')
        # self.click('//*[@id="main_root"]/div/div/div/div/div/div/div[3]/div/div[3]/div/div/div/div/div[2]/div[1]/span/svg')
        sleep(10)
        assert_that(self.is_element_visible("//span[contains(text(),'邀请带货')]"))

    def test_send_and_check_msg(self):
        self.go_cooperation_workbench(self, user='chunxiao', id="1691062442")
        sleep(4)
        # 会话区输入框
        CHAT_EDIT_TEXT = '//*[@id="esim-editor5678"]/div[1]'
        # 发送按钮
        CHAT_SEND_BTN = '//span[contains(text(), "发送 Enter")]'

        # 输入
        # aa：消息发送，2min内撤回
        self.add_text(CHAT_EDIT_TEXT, "web自动化测试_任意消息_aa")
        sleep(1)
        # 消息发送
        self.click(CHAT_SEND_BTN)
        sleep(3)
        last_msg_bubble_element=self.find_elements('//div[@class="kwaishop-cs-BizTextCard kwaishop-cs-BizTextCard__pc"]')[-1]
        ActionChains(self.driver).context_click(last_msg_bubble_element).perform()
        sleep(3)
        assert_that(self.is_element_visible("//span[contains(text(), '撤回')]"))
        assert_that(self.is_element_visible("//span[contains(text(), '复制')]"))
        assert_that(self.is_element_visible("//span[contains(text(), '删除')]"))
        self.click("//span[contains(text(), '撤回')]")
        sleep(2)
        assert_that(self.is_element_present("//div[text()='你撤回了一条消息']"))

    @pytest.mark.p1
    def test_cooperation_chat_history_id_search(self):
        """
        商达团-合作页面昵称
        聊天记录搜索
        """
        self.go_cooperation_workbench(self, user='member_account2')
        sleep(9)
        # 昵称校验
        assert_that(self.is_element_visible('//div[text()="珊珊86621"]'))
        assert_that(self.is_element_visible('//div[@class="avatar"]//img[contains(@src, "==_s.jpg")]'))
        # 左上角搜索框输入聊天记录
        self.click('//span[text()="请输入快手ID/快手昵称/聊天记录搜索"]')
        sleep(2)
        self.type("input[class='ant-select-selection-search-input']", '测试聊天记录搜索')
        sleep(3)
        assert_that(self.is_element_visible('//div[@class="ImSearch-drop-panel-text"]'))
        assert_that(self.is_element_visible('//span[text()="聊天记录"]'))
        assert_that(self.is_element_visible('//div[text()="Z"]'))
        # 搜索用户id
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(8)
        assert_that(self.is_element_visible('//div[@class="ImSearch-drop-panel-text"]'))
        assert_that(self.is_element_visible('//span[text()="用户"]'))
        # 点击搜索到的进入用户会话页
        self.click('//div[@class="ImSearch-drop-panel-text"]')
        sleep(5)
        # 校验是否进入用户会话详情页面
        res = self.get_text('//div[@class="LoginUserInfo-mainTitle-text"]')
        res_two = self.get_text('//span[@class="cps-communicate-baseinfo-user-name"]')
        assert_that(res == "柠檬琉璃夏", "")
        assert_that(res_two == "柠檬琉璃夏", "")
        assert_that(res >= "0", "")
        assert_that(res_two >= "0", "")
        print(res)
        print(res_two)
        assert_that(self.is_element_visible('//div[text()="达人带货数据"]'))
        assert_that(self.is_element_visible('//div[text()="柠檬琉璃夏"]'))
        assert_that(self.is_element_visible(
            '(//span[@class="ant-avatar ant-avatar-circle ant-avatar-image"][@style="width: 42px; height: 42px; line-height: 42px; font-size: 18px;"])[2]'))
        # 合作列表滑动
        # self.execute_script('document.getElementsByClassName("SessionBaseCard colorHighLight").scrollTo(0,100)')
        # sleep(3)
        # self.execute_script('document.getElementsByClassName("SessionBaseCard colorHighLight").scrollTo(0,10000)')
        # sleep(30)

    @pytest.mark.p1
    def test_long_press_text_message_on_cooperation_page_delete(self):
        """
        商达团-发送文本
        文本消息长按删除
        文本消息长按撤回
        文本长按复制
        """
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 发送文本消息
        num = random.randint(1, 1000)
        num_str = "web自动化测试_任意消息" + str(num)
        xpath_expression = f'(//div[text()="{num_str}"])[2]'
        self.type("//div[@data-placeholder='请输入...']", num_str)
        sleep(3)
        # 消息发送
        self.click("//span[text()='发送 Enter']")
        sleep(4)
        # 长按复制消息
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        self.click("//span[text()='复制']")
        sleep(4)
        # 粘贴在输入框
        self.click('//div[contains(@class, "ql-editor")]')
        ele = self.driver.find_element("class name", "ql-editor")
        ele.send_keys(Keys.CONTROL, 'v')
        sleep(6)
        paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
        assert_that(len(paste_text) > 0)
        assert_that(self.is_element_visible('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"])[1]'))
        assert_that(self.is_element_visible(xpath_expression))
        assert_that(self.is_element_visible('//div[text()="未读"]') or self.is_element_visible('//div[text()="已读"]'))
        sleep(1)
        # 右键，选择消息删除
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        self.click("//span[text()='删除']")
        sleep(2)
        assert_that(self.is_element_visible('(//span[text()="删除后将无法恢复，确定删除该消息吗？"])'))
        sleep(1)
        self.click("//span[text()='删 除']")
        sleep(2)
        assert_that(not self.is_element_visible(f'//div[contains(text(), "{num_str}")]'))
        # 消息发送
        self.click("//span[text()='发送 Enter']")
        sleep(5)
        assert_that(self.is_element_visible('(//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"])[1]'))
        assert_that(self.is_element_visible('//div[text()="未读"]') or self.is_element_visible('//div[text()="已读"]'))
        assert_that(self.is_element_visible(xpath_expression))
        sleep(1)
        # 右键，选择消息撤回（index=1）
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(3)
        self.click("//span[text()='撤回']")
        sleep(5)
        # 刷新页面
        self.refresh_page()
        sleep(7)
        assert_that(self.is_element_present("//div[text()='你撤回了一条消息']"))
        assert_that(not self.is_element_visible(f'//div[contains(text(), "{num_str}")]'))
    @pytest.mark.p1
    def test_cooperation_picture_message_long_press(self):
        """
        商达团-图片消息长按
        """
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 长按图片消息
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(2)
        assert_that(self.is_element_visible("//span[text()='删除']"))
        assert_that(self.is_element_visible("//span[text()='保存至本地']"))
        # 点击图片消息
        self.click("//img[@style='width: 255px; height: 566.667px;']")
        sleep(5)
        assert_that(self.is_element_visible("//img[@class='ant-image-preview-img']"))
        assert_that(self.is_element_visible("//span[@aria-label='system-close-large-line']"))

    @pytest.mark.p1
    def test_long_press_video_message(self):
        """
        商达团-视频消息长按
        """
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 长按视频消息
        ele = self.find_elements('//div[@class="kwaishop-cs-LayoutDefaultWrapper_contentBox"]')[-1]
        ActionChains(self.driver).context_click(ele).perform()
        sleep(5)
        assert_that(self.is_element_visible("//span[text()='删除']"))
        # 点击视频
        self.click('//img[@class="kwaishop-cs-BizVideoCard_videoBg"]')
        sleep(5)
        assert_that(self.is_element_visible('//div[text()="视频无法正常播放,点击这里试试?"]'))
        assert_that(self.is_element_visible('//video[@class="video"][@style="max-width: 100%; max-height: 100vh; display: block; outline: none; border: none;"]'))
    @pytest.mark.p1
    def test_voice_to_text(self):
        """
        商达团-语音消息转文字
        """
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 语音消息转文字
        self.click("//div[text()='转文字']")
        sleep(10)
        assert_that(self.is_element_visible('//div[text()="看来我是透明的手机套。制作。"]'))

    @pytest.mark.p1
    def test_send_emoticons(self):
        #表情包发送
        self.go_cooperation_workbench(self, user='member_account2',id="********")
        sleep(9)
        num = random.randint(1, 40)
        self.click("//img[@class='tool-file ant-dropdown-trigger']")
        sleep(2)
        assert_that(self.is_element_visible(f'(//img[@class="icon"])[{num}]'))
        self.click(f'(//img[@class="icon"])[{num}]')
        sleep(2)
        #获取输入框内表情包的文本 例：[红脸哪吒]
        second_switch = self.driver.find_element(by='xpath',
                                                 value="//img[@class='ql-emotion']")
        status = second_switch.get_attribute('alt')
        print(status)
        # 消息发送
        self.click("//span[text()='发送 Enter']")
        sleep(2)
        # 断言最后一条消息表情包文本和输入框获取到的一致
        second_switch = self.driver.find_elements(by='xpath',
                                                 value="//img[@style='vertical-align:text-bottom;']")[-1]
        new_status = second_switch.get_attribute('alt')
        print(new_status)
        sleep(2)
        assert_that(status,new_status)
        assert_that(self.is_element_visible("//img[@style='vertical-align:text-bottom;']"))

    @pytest.mark.p1
    def test_cooperation_chat_history_query(self):
        #商达团聊天记录tab点击&聊天记录半屏图片视频筛选
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        num = random.randint(1, 40)
        self.click("(//img[@class='tool-file'])[3]")
        sleep(7)
        assert_that(self.is_element_visible("//div[text()='没有更多消息了～']"))
        assert_that(self.is_element_visible("//div[text()='5月14日 凌晨 02:11']"))
        assert_that(self.is_element_visible("(//img[@style='width: 255px; height: 468.209px;'])[2]"))
        self.click("//div[text()='图片/视频']")
        # 消息发送
        sleep(7)
        assert_that(self.is_element_visible("(//img[@style='width: 255px; height: 468.209px;'])[2]"))
        self.click("//div[text()='全部']")
        sleep(7)
        assert_that(self.is_element_visible("(//img[@style='width: 255px; height: 468.209px;'])[2]"))
        assert_that(self.is_element_visible("//div[text()='没有更多消息了～']"))
        assert_that(self.is_element_visible("//div[text()='5月14日 凌晨 02:11']"))

    @pytest.mark.p1
    def test_cooperation_list_swipe(self):
        #合作列表滑动
        self.go_cooperation_workbench(self, user='chunxiao')
        sleep(5)
        self.execute_script('document.getElementsByClassName("rc-virtual-list-holder")[0].scrollTo(0,100)')
        sleep(2)
        self.execute_script('document.getElementsByClassName("rc-virtual-list-holder")[0].scrollTo(0,6000)')
        sleep(2)
        # 断言最底部商家名称&日期
        assert_that(self.is_element_visible("(//div[text()='2025/3/18'])[2]"))
        assert_that(self.is_element_visible("//div[text()='商品c端小店']"))
    @pytest.mark.p1
    def test_filter_specified_chat_history(self):
        #商达团聊天记录筛选指定聊天记录
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 点击聊天记录tab
        self.click("(//img[@class='tool-file'])[3]")
        sleep(7)
        # 筛选框搜索聊天记录"在吗"
        self.type("//input[@placeholder='搜索3个月内的聊天记录']","合作UI自动化59309" )
        sleep(4)
        self.click("(//span[@class='anticon anticon-system-search-line'])[2]")
        sleep(4)
        assert_that(self.is_element_visible("//div[text()='没有更多消息了～']"))
        assert_that(self.is_element_visible("//div[text()='合作UI自动化59309']"))
        assert_that(self.is_element_visible("//div[text()='5月30日 下午 18:05']"))

    @pytest.mark.p1
    def test_contact_card_decryption(self):
        #商达团联系方式卡片解密
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 点击卡片解密按钮
        assert_that(self.is_element_visible("(//span[text()='联系方式'])[2]"))
        self.click("(//div[@class='kope-view'][@role='button'][@style='position: relative; top: 2px; width: 16px; height: 16px;'])[1]")
        sleep(4)
        # 断言解密手机号***********
        assert_that(self.is_element_visible("//span[text()='***********']"))
        assert_that(self.is_element_visible("//span[text()='w241421414']"))
        #卡片点击复制
        self.click("(//div[@class='kope-view'][@style='width: 14px; height: 14px; margin-left: 4px;'])[2]")
        #粘贴
        self.click('//div[contains(@class, "ql-editor")]')
        ele = self.driver.find_element("class name", "ql-editor")
        ele.send_keys(Keys.CONTROL, 'v')
        sleep(6)
        paste_text = self.get_text('//div[contains(@class, "ql-editor")]')
        assert_that(len(paste_text) > 0)
        assert_that(self.is_element_visible("//p[text()='***********']"))

    @pytest.mark.p1
    def test_cooperate_to_get_historical_chat_records(self):
        #合作会话详情拉取历史聊天记录
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        self.execute_script('document.getElementsByClassName("ChatMessageList-ContentWrap")[0].scrollTo(0,100)')
        sleep(2)
        self.execute_script('document.getElementsByClassName("ChatMessageList-ContentWrap")[0].scrollTo(0,400)')
        sleep(2)
        # 断言拉取到 3月13日 下午 17:57 的聊天记录
        assert_that(self.is_element_visible("//div[text()='3月13日 下午 17:57']"))

    @pytest.mark.p1
    def test_message_read_not_read(self):
        #合作会话详情已读&未读
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 3月13日 下午 17:57
        assert_that(self.is_element_visible("//div[text()='未读']"))
        assert_that(self.is_element_visible("//div[text()='111']"))
        # 搜索用户id
        self.type("input[class='ant-select-selection-search-input']", '**********')
        sleep(8)
        # 点击搜索到的进入用户会话页
        self.click('//div[@class="ImSearch-drop-panel-text"]')
        sleep(2)
        assert_that(self.is_element_visible("//div[text()='已读']"))
        assert_that(self.is_element_visible("//div[text()='2']"))
    @pytest.mark.p1
    def test_send_invitation_cards_on_the_cooperation_page(self):
        #合作页面发送邀约卡
        self.go_cooperation_workbench(self, user='member_account2',id="**********")
        sleep(9)
        # 点击发送邀约tab
        self.click("(//img[@class='tool-file'])[2]")
        sleep(4)
        assert_that(self.is_element_visible("//div[text()='您当前的联系信息']"))
        #获取初始邀约合作元素数量
        second_switch = self.driver.find_elements(by='xpath',
                                                 value="//*[contains(text(), '邀约合作')]")
        count = len(second_switch)
        print(f"找到 {count} 个包含'邀约合作'的元素")
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        sleep(2)
        # 点击发送邀约按钮
        self.click("(//span[text()=' 发送邀约'])[1]")
        # 断言邀约合作元素数量+1
        sleep(4)
        new_second_switch = self.driver.find_elements(by='xpath',
                                                 value="//*[contains(text(), '邀约合作')]")
        new_count = len(new_second_switch)
        print(f"找到 {new_count} 个包含'邀约合作'的元素")
        assert_that(new_count,
            equal_to(count + 1))
        assert_that(self.is_element_visible("//span[text()='邀约合作']"))
        assert_that(self.is_element_visible("//span[text()='待处理']"))

    @pytest.mark.p1
    def test_modify_the_contact_information(self):
        #合作页面修改联系方式
        self.go_cooperation_workbench(self, user='chunxiao',id="1691062442")
        sleep(9)
        # 点击发送邀约tab
        self.click("(//img[@class='tool-file'])[2]")
        sleep(4)
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        # 点击修改联系方式按钮
        sleep(2)
        self.click("(//span[text()='修改联系方式'])[1]")
        sleep(3)
        assert_that(self.is_element_visible("//label[text()='联系人']"))
        assert_that(self.is_element_visible("//label[text()='手机号']"))
        num = random.randint(10000, 99999)
        num_str = '176422' + str(num)
        print(num_str)
        # 输入手机号
        self.type("//input[@placeholder='请输入手机号']",num_str)
        self.click("(//span[text()='仅保存'])[1]")
        sleep(4)
        #保存并发送邀约按钮
        self.click("(//img[@class='tool-file'])[2]")
        sleep(4)
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        sleep(2)
        phone_number=f"//div[text()='{num_str}']"
        assert_that(self.is_element_visible(phone_number))
        self.click("(//span[text()='修改联系方式'])[1]")
        sleep(3)
        #获取初始邀约合作元素数量
        second_switch = self.driver.find_elements(by='xpath',
                                                 value="//*[contains(text(), '邀约合作')]")
        count = len(second_switch)
        print(f"找到 {count} 个包含'邀约合作'的元素")
        # 点击发送邀约按钮
        self.click("(//span[text()='保存并发送邀约'])[1]")
        # 断言邀约合作元素数量+1
        sleep(4)
        new_second_switch = self.driver.find_elements(by='xpath',
                                                 value="//*[contains(text(), '邀约合作')]")
        new_count = len(new_second_switch)
        print(f"找到 {new_count} 个包含'邀约合作'的元素")
        assert_that(new_count,
            equal_to(count + 1))
        assert_that(self.is_element_visible("//span[text()='邀约合作']"))
        assert_that(self.is_element_visible("//span[text()='待处理']"))


    @pytest.mark.p1
    def test_quick_distribution_upper_right_corner_entrance(self):
        """商达团入口-快分销右上角icon"""
        self.go_distribution_workbench(user='chunxiao')
        time.sleep(6)
        self.click('//img[@width="28"]')
        # 校验链接
        time.sleep(6)
        c_url = self.get_current_url()
        self.assert_equal(c_url.endswith('/workbench/zone/cooperation?role=merchant'), True)
        print(c_url)
        assert_that(self.is_element_visible("//div[text()='全部会话']"))
        assert_that(self.is_element_visible("//div[text()='qqqqqqq']"))

    @pytest.mark.p1
    def test_quick_distribution_online_communication(self):
        """商达团入口-快分销在线沟通"""
        self.go_distribution_workbench(user='chunxiao')
        time.sleep(6)
        self.click('//div[text()="近期数据飙升"]')
        sleep(2)
        # 获取第一个达人昵称
        res = self.get_text('(//span[@class="index-module__userName--cyXg8"])[1]')
        assert_that(res >= "0", "")
        print(res)
        #点击第一个达人-在线沟通
        self.click('(// span[text() = " 在线沟通"])[1]')
        sleep(5)
        # 进入页面之后获取到的昵称和上个页面一致
        res_two = self.get_text('//span[@class="cps-communicate-baseinfo-user-name"]')
        assert_that(res_two >= "0", "")
        print(res_two)
        assert (res==res_two)

    @pytest.mark.p1
    def test_expert_details_online_communication(self):
        """达人详情-在线沟通"""
        self.go_distribution_workbench(user='chunxiao')
        time.sleep(6)
        self.click('//span[text()="达人广场"]')
        time.sleep(4)
        # 获取第一个达人昵称
        res = self.get_text('(//span[@class="userName___PlOOr"])[1]')
        assert_that(res >= "0", "")
        print(res)
        # 进入达人详情
        self.click('(//span[text()="查看详情"])[1]')
        time.sleep(4)
        # 在线沟通
        self.click('//span[text()=" 在线沟通"]')
        time.sleep(6)
        # 进入页面之后获取到的昵称和上个页面一致
        res_two = self.get_text('//span[@class="cps-communicate-baseinfo-user-name"]')
        assert_that(res_two >= "0", "")
        print(res_two)
        assert (res==res_two)