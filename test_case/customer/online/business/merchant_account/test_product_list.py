import json
import time
from time import sleep

#import pyautogui
import requests
from hamcrest import *
from ddt import *
from pyautogui import click
from selenium import webdriver
from selenium.webdriver import Chrome, ActionChains, Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import pytest

from test_case.customer.online.base import BaseTestCase


class Testgetigongshanghuss(BaseTestCase):

    @pytest.mark.p1
    def test_product_list_card_title(self):
        # 点击标题跳转
        # 登录到客服页面
        self.go_workbrench(self, user="member_account2")
        # 取消弹窗
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(3)
        self.click('//span/div/span[contains(text(), "自动化勿删")]')
        sleep(10)
        assert_that(self.is_element_present('//div[text()="商品详情"]'))
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))

    @pytest.mark.p1
    def test_product_list_card_titli_img(self):
        # 点击主图跳转
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(4)
        self.click('//div[@class="ant-spin-container"]/div/div[@class="kope-view"]/div/div/div/div[contains(@style,"image-kwaishop-product")]')
        sleep(10)
        assert_that(self.is_element_present('//div[text()="商品详情"]'))
        assert_that(self.is_element_present('//*[contains(text(), "自动化勿删")]'))

    @pytest.mark.p1
    def test_product_list_card_img(self):
        # 赠品详情
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(5)
        self.click('//div[text()="查看全部1个赠品信息"]')
        sleep(5)
        assert_that(self.is_element_present('//div[text()="当前商品赠品信息"]'))
        sleep(1)

    @pytest.mark.p1
    def test_product_list_card_order(self):
        # 邀请下单
        # 发送商品
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(10)
        for i in range(3):
            ActionChains(self.driver).send_keys(Keys.ARROW_RIGHT).perform()
            sleep(1)
        self.click('//div[@class="flexItem"]/div/span[text()="邀请下单"]')
        sleep(2)

        assert_that(self.is_element_present('//div[contains(@class, "ant-drawer-open")]'))
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        sleep(5)
        assert_that(self.is_element_visible('//*[contains(text(), "7天无理由退货")]'))
        # assert_that(self.is_element_visible('//*[contains(text(), "175/92A")]'))
        self.click('//span[text()="不选规格，直接发送"]')
        sleep(2)
        self.click('//span[text()="确认邀请"]')
        sleep(3)
        # 邀请下单卡片出现
        assert_that(self.is_element_visible('//*[contains(text(), "邀请下单")]'))
        assert_that(self.is_element_visible('//*[contains(text(), "自动化勿删")]'))
        assert_that(self.is_element_visible('//*[contains(text(), "7天无理由退货")]'))
        assert_that(self.is_element_visible('//*[contains(text(), "0.01")]'))
        assert_that(self.is_element_present('//*[text()="已读"]') or self.is_element_present('//*[text()="未读"]'))
        # 发送商品卡片，再次点击邀请下单按钮
        self.click('//span[text()="发送商品"]')
        sleep(5)
        self.click_if_visible('//div[@class="unread_message_list_tips"]')
        sleep(3)
        if self.is_element_visible('//div[contains(@class, "NormalBtnPrimary")]'):
            assert_that(self.is_element_present('//*[text()="已读"]') or self.is_element_present('//*[text()="未读"]'))
            self.click('//div[contains(@class, "NormalBtnPrimary")]')
            self.click_if_visible('//div[contains(@class, "NormalBtnPrimary")]')
            sleep(2)
            self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
            sleep(5)
            assert_that(self.is_element_visible('//*[contains(text(), "7天无理由退货")]'))
            # assert_that(self.is_element_visible('//*[contains(text(), "175/92A")]'))
            # self.click('//div[text()="邀请下单"]//following-sibling::button[1]')


    @pytest.mark.p1
    def test_send_coupons(self):
        return          # todo: 需求测试暂时注释掉case
        # 发送优惠券
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(5)
        self.click('//span[text()="发送优惠券"]')
        sleep(3)
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        sleep(3)
        assert_that(self.is_element_present('//*[contains(text(), "优惠券")]'))
        if self.is_element_present('//div[text()="发送优惠券"]'):
            self.click('//div[text()="发送优惠券"]')
            sleep(2)
        else:
            assert_that(self.is_element_present('//div[text()="暂无优惠券"]'))
            self.click('//div[@class="ant-tabs-tab"]//div[text()="店铺券"]')
            sleep(5)
            if self.is_element_present('//div[text()="发送优惠券"]'):
                self.click('//div[text()="发送优惠券"]')
                sleep(1)
                if self.is_element_present('//span[text()="最多领取5张"]') or self.is_element_present('//span[text()="不能给目标用户发优惠券"]'):
                    assert_that(self.is_element_present('//span[text()="最多领取5张"]') or self.is_element_present('//span[text()="不能给目标用户发优惠券"]'))
                else:
                    self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'right:0px;')
                    sleep(2)
                    assert_that(self.is_element_present('//span[text()="老铁，您的专属店铺券来啦～"]'))
                    self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
                    sleep(3)
            else:
                assert_that(self.is_element_present('//div[text()="暂无优惠券"]'))
        # 关闭半屏
        # self.click('//span[@class="anticon anticon-system-close-medium-line"]')
        self.click_if_visible('//div[text()="优惠券"]//following-sibling::button[1]')
        sleep(2)

    @pytest.mark.p1
    def test_product_list_Good(self):
        # 发送评价并断言发送成功
        self.go_workbrench(self, user="member_account2", id="********")
        self.close_common_guide_toast()
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '心相印')
        sleep(5)
        self.click('//span[text()="发送评价"]')
        sleep(3)
        assert_that(self.is_element_present('//div[contains(@class, "ant-drawer-open")]'))
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        sleep(2)
        self.click('//span[text()="发送"]')
        self.click('//div[text()="发送评价"]//following-sibling::button[1]')
        sleep(3)
        assert_that(self.is_element_present('//div[text()="这款商品买过的人评价很好哦，您可以放心购买~"]'))
        sleep(3)

    @pytest.mark.p1
    def test_product_list_Distribution(self):
        # 分销商品
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '心相印')
        sleep(7)
        assert_that(self.is_element_present('//span[text()="商家发货"]'))
        sleep(3)

        # 点击商品图片
        self.click('//div[@class="shopRecommendNew-Sessions"]//child::div[contains(@style,"image-kwaishop-product")]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="该商品为商家直发商品，不支持查看货主的商品信息"]') or self.is_element_present('//span[text()="商家发货"]'))
        sleep(5)
        self.click('//span[text()="￥"]')
        sleep(5)

        # 断言“规格与库存”不在
        ele = assert_that(self.is_element_present('//div[text()="规格与库存"]'))
        print(ele)
        assert ele == None

        sleep(2)
        self.click('//span[contains(text(), "心相印纸巾")]')
        sleep(1)
        assert_that(self.is_element_present('//span[text()="该商品为商家直发商品，不支持查看货主的商品信息"]') or self.is_element_present('//span[text()="商家发货"]'))
        sleep(5)
        # self.click('//div[@class="priview"]')
        self.click('//span[contains(text(), "预览")]')
        sleep(3)
        # 备注：不能开代理，开代理会影响半屏加载
        assert_that(
            self.is_element_present('//span[contains(text(), "心相印纸巾")]')
            or len(self.get_text('//div[@class="cs-plugin-self-drawer-body"]')) > 0
        )

    @pytest.mark.p1
    def test_product_list_big_link(self):
        # 大链接-标题跳转
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)

        # 大链接商品
        self.type("input[placeholder='请输入想要搜索的关键词']", '大链接测试商品03')
        sleep(3)
        assert_that(self.is_element_present('//span[text()="商品报名了平台活动，请按照平台商品信息回复用户咨询"]'))
        self.click('//span[text()="原商品信息"]')
        sleep(7)
        assert_that(self.is_element_present('//div[contains(@class, "ant-drawer-open")]'))
        self.set_attribute('//div[contains(@class, "ant-drawer-open")]', 'style', 'left:200px;')
        sleep(2)
        assert_that(self.is_element_present('//div[text()="商家商品信息"]'))
        assert_that(self.is_element_present('//span[text()="大链接测试商品03"]'))
        self.click('//span[@class="anticon anticon-system-close-medium-line"]')
        sleep(2)

        # 跳转详情
        self.click('//span[text()="大链接测试商品03"]')
        sleep(12)
        assert_that(self.is_element_present('//div[text()="商品详情"]'))
        assert_that(self.is_element_present('//span[text()="大链接测试商品03"]'))
        # 关闭页面

    @pytest.mark.p1
    def test_product_list_big_big_img(self):
        # 大链接-主图跳转
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//div[@class="ImSearch-drop-panel-text"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)

        # 大链接商品
        self.type("input[placeholder='请输入想要搜索的关键词']", '大链接测试商品03')
        sleep(3)
        self.click('//div[@class="ant-spin-container"]/div/div[@class="kope-view"]//child::div[contains(@style,"image-kwaishop-product")]')
        sleep(12)
        assert_that(self.is_element_present('//div[text()="商品详情"]'))
        assert_that(self.is_element_present('//span[text()="大链接测试商品03"]'))

    @pytest.mark.p1
    def test_commodity_search(self):
        # 搜索
        # 全部商品--足迹商品--热销商品
        # 名称搜索--ID搜索
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(1)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(1)
        self.click('//div[text()="商品推荐"]')
        sleep(2)

        # 全部商品--名称搜索
        self.click('//div[text()="全部商品"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))
        self.click('//span[@class="anticon anticon-system-error-circle-fill ant-input-clear-icon"]')
        sleep(2)
        # ID搜索
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '**************')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))
        self.click('//span[@class="anticon anticon-system-error-circle-fill ant-input-clear-icon"]')
        sleep(2)

        # 浏览足迹--名称搜索
        self.click('//div[text()="浏览足迹"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))
        self.click('//span[@class="anticon anticon-system-error-circle-fill ant-input-clear-icon"]')
        sleep(2)
        # ID搜索
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '**************')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))
        self.click('//span[@class="anticon anticon-system-error-circle-fill ant-input-clear-icon"]')
        sleep(2)

        # 热销商品--名称搜索
        self.click('//div[text()="热销商品"]')
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '自动化勿删')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))
        self.click('//span[@class="anticon anticon-system-error-circle-fill ant-input-clear-icon"]')
        sleep(2)
        # ID搜索
        sleep(2)
        self.type("input[placeholder='请输入想要搜索的关键词']", '**************')
        sleep(3)
        assert_that(self.is_element_present('//span[contains(text(), "自动化勿删")]'))
        self.click('//span[@class="anticon anticon-system-error-circle-fill ant-input-clear-icon"]')
        sleep(2)

    @pytest.mark.skip
    @pytest.mark.p1
    def test_page_turning(self):
        # 商品翻页
        # ---全部商品
        # ---浏览足迹
        # ---热销商品
        self.go_workbrench(self, user="member_account2")
        self.close_common_guide_toast()
        self.click('//div[text()="全部会话"]')
        sleep(2)
        # 搜索买家uid
        self.type("input[class='ant-select-selection-search-input']", '********')
        sleep(7)
        self.click('//*[text()="柠檬琉璃夏"]')
        sleep(5)
        self.click('//div[text()="商品推荐"]')
        sleep(2)

        # 全部商品
        self.click('//div[text()="全部商品"]')
        sleep(2)
        switch_ls = self.driver.find_elements(by='class name', value='ant-pagination-simple-pager')
        ele = switch_ls[0]

        if ele.get_attribute("title") == "1/3":
            # 点击下一页
            self.click('(//span[@class="anticon anticon-system-arrow-large-right-line"])[2]')
            sleep(3)
        assert_that(ele.get_attribute("title") == "2/3")

        if ele.get_attribute("title") == "2/3":
            # 点击下一页
            self.click('//span[@class="anticon anticon-system-arrow-large-left-line"]')
            sleep(3)
        assert_that(ele.get_attribute("title") == "1/3")

        # 浏览足迹
        self.click('//div[text()="浏览足迹"]')
        sleep(2)
        if ele.get_attribute("title") == "1/2":
            # 点击下一页
            self.click('(//span[@class="anticon anticon-system-arrow-large-right-line"])[2]')
            sleep(3)
        assert_that(ele.get_attribute("title") == "2/3")

        if ele.get_attribute("title") == "2/3":
            # 点击下一页
            self.click('//span[@class="anticon anticon-system-arrow-large-left-line"]')
            sleep(3)
        assert_that(ele.get_attribute("title") == "1/3")

        # 热销商品
        self.click('//div[text()="热销商品"]')
        sleep(2)
        if ele.get_attribute("title") == "1/2":
            # 点击下一页
            self.click('(//span[@class="anticon anticon-system-arrow-large-right-line"])[2]')
            sleep(3)
        assert_that(ele.get_attribute("title") == "2/3")

        if ele.get_attribute("title") == "2/3":
            # 点击下一页
            self.click('//span[@class="anticon anticon-system-arrow-large-left-line"]')
            sleep(3)
        assert_that(ele.get_attribute("title") == "1/3")





































