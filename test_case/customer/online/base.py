import datetime
from time import sleep

from hamcrest import *
from selenium import webdriver
from seleniumbase import get_driver
from selenium.webdriver.chrome.options import Options
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase.config import settings
from seleniumbase.fixtures import page_utils, constants
from utils.time_help import timestamp_to_date_str
import requests
from utils.config_help import get_config
import random


def judge_in_seconds(seconds, limit_seconds, cur_time, origin_text):
    timestamp = [trans_time_str(timestamp_to_date_str(cur_time)[6:])]
    for i in range(1, seconds + 1):
        timestamp.append(trans_time_str(timestamp_to_date_str(cur_time - i * 1000)[6:]))
        timestamp.append(trans_time_str(timestamp_to_date_str(cur_time + i * 1000)[6:]))

    flag = False
    for t in timestamp:
        print("t1:", t)
        if origin_text.find(t) != -1:
            flag = True
            break
    if flag:
        return flag
    else:
        for i in range(seconds + 1, limit_seconds + 1):
            timestamp.append(trans_time_str(timestamp_to_date_str(cur_time - i * 1000)[6:]))
            timestamp.append(trans_time_str(timestamp_to_date_str(cur_time + i * 1000)[6:]))
        for t in timestamp:
            print("t2:", t)
            if origin_text.find(t) != -1:
                flag = True
                break
        return flag


#


def trans_time_str(cur_str):
    hour_minute_seconds = cur_str[-9:]
    month = cur_str[-13:-11]
    day = cur_str[-11:-9]
    res = ""
    if day[0:1] == '0':
        res = month + day[1:2] + hour_minute_seconds
    else:
        return cur_str
    return res


class BaseTestCase(BaseCase):
    def get_environment(self, weekday, hour):
        """
        【X】（< 环境选择规则 > v1.0）[2024-11-11]
        【轮询的代码】：
            周一到周五（早上11点～晚上9点）：prt.test泳道 运行代码
            周一到周五（其他时间）+周末：online 运行代码
        【流水线准出代码】：
            所有时间：prt.test泳道 执行核心用例（执行时长控制在10min内）「这块不确定，有可能会不打泳道」

        【O】（< 环境选择规则 > v1.1）[2024-12-19]
        【变量名称】：
            var2：环境
            var3：泳道
        【轮询的代码】：
            全天时间段（环境、参数、执行时长）：
            prt.test    var2=prt,var3=test                  1~2 小时1次
            其他泳道     var2=prt,var3=泳道名称(ycdctest)      进行模糊故障演练（预留口子）
            online      var2=online,var3=空                 半小时1次
            prt主干      var2=prt,var3=空                    接入【流水线】，流水线准出时进行轮询
        """
        return self.var2

    def get_laneid(self, env):
        laneid = self.var3
        if env == "prt":
            if laneid is not None and len(laneid)>0:
                return "PRT."+laneid
            else:
                return ""
        else:
            return ""

    def request(self, data, url):
        # data: json格式，url：域名+前缀链接
        # data = {"buyerId": "1275802949", "limit": 10, "offset": 0, "searchKeyword": "", "viewTab": 2}
        # url = 'https://eshop-s.prt.kwaixiaodian.com/gateway/business/cs/list/item/info'
        if data != "" and url != "":
            cookies = self.driver.get_cookies()
            print("cls", cookies)
            cookie = ''
            for value in cookies:
                cookie = cookie + value["name"] + "=" + value["value"] + ";"

            print("ck=", cookie)
            headers = {
                'Content-Type': 'application/json',
                "Cookie": cookie
            }
            res = requests.request(url=url, method="POST", json=data, headers=headers).json()
            print("【请求结果】：")
            print(res)

    def go_workbrench(self, data="", url="", user="customer_merchant", id="1606636102"):
        # from seleniumwire import webdriver

        # 获取当前日期，根据当前日期获取环境变量
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)                # ⭐️环境变量     "prt"
        self.LANE_ID = self.get_laneid(self.ENV)                      # ⭐️泳道        "PRT.test"
        # 如果是prt带泳道的环境(泳道名称非空)，需要切换泳道
        if len(self.LANE_ID) > 0:
            self.driver.quit()
            self.driver = webdriver.Chrome()
            self.driver.header_overrides = {"trace-context": '{"laneId":"' + self.LANE_ID + '"}'}

        # 以主账号身份登录客服工作台
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", user)
        self.maximize_window()
        sleep(2)

        # 打开客服工作台页面
        if self.ENV == "prt":
            self.open('https://eshop-im.prt.kwaixiaodian.com/workbench#ud=' + id)
        else:
            self.open("https://im.kwaixiaodian.com/workbench#ud=" + id)
        sleep(1)

        # 增加xss漏洞治理代码报错判断
        assert_that(not self.is_element_visible('//*[contains(text, "javascript")]'))
        assert_that(not self.is_element_visible('//*[contains(text, "im.kwaixiaodian.com 显示")]'))

        # 退出新人引导
        # self.check_if_page_404()
        self.quit_newcomer_guide_frame()

        # 点击全部会话tab
        self.click('//div[text()="全部会话"]')
        sleep(1)

        # 关闭问卷弹窗
        self.close_questionnarie_frame()

    def go_workbrench_child(self, data="", url="", user="customer-jsh", id="**********"):
        # from seleniumwire import webdriver

        # 获取当前日期，根据当前日期获取环境变量
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)  # ⭐️环境变量
        self.LANE_ID = "PRT.test"  # ⭐️泳道
        # 如果是prt带泳道的环境(泳道名称非空)，需要切换泳道
        if len(self.LANE_ID) > 0:
            self.driver.quit()
            self.driver = webdriver.Chrome()
            self.driver.header_overrides = {"trace-context": '{"laneId":"' + self.LANE_ID + '"}'}

        # 以子账号身份登录客服工作台
        self.clear_all_cookies()
        self.ZiaccountLogin("MERCHANT_DOMAIN", user)
        self.maximize_window()
        sleep(3)

        # 打开客服工作台页面
        if self.ENV == "prt":
            self.open('https://eshop-im.prt.kwaixiaodian.com/workbench#ud=' + id)
        else:
            self.open("https://im.kwaixiaodian.com/workbench#ud=" + id)
        sleep(1)

        # 退出新人引导
        # self.check_if_page_404()
        self.quit_newcomer_guide_frame()

        # 关闭知道了弹窗
        self.close_common_guide_toast()

        # 点击全部会话tab
        self.click('//div[text()="全部会话"]')
        sleep(1)

        # 缩小【会话模块】弹窗
        self.reduce_chat_page_frame()

    def merchantAccountLogin(self, domain, account):
        account_data = get_account_info(account)
        host = get_domain(domain)
        self.open(host)
        self.click('//div[contains(text(),"我是店主")]')
        self.click('//div[contains(text(),"手机号登录")]')                               # todo: 新版工作台改成密码登录
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        sleep(3)
        self.driver.refresh()
        sleep(3)
        self.driver.refresh()
        sleep(3)
        try:
            skip_btn = self.find_elements("//*[@class='seller-main-modal-close']")
            for btn in skip_btn:
                btn.click()
        except Exception:
            print(Exception)

    def SubaccountLogin(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)
        self.open(host)
        self.click('//div[contains(text(),"我是员工")]')
        self.click('//div[contains(text(),"账号登录")]')                               # todo: 新版工作台改成密码登录
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        sleep(3)
        self.assert_text("请选择您要登录的店铺", '//*[@id="rcDialogTitle0"]')             # todo: 新版工作台改成密码登录(下面都要改)
        self.click(account_data['default_log_select_page'])  # 点击小店试试的小店
        sleep(3)
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dingdanchaxun-yinsijiami_2817449844','1');")
        self.refresh()
        sleep(2)

    def ZiaccountLogin(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)
        self.open(host)

        self.click('//span[contains(text(),"我是员工")]')
        self.click('//span[contains(text(),"密码登录")]')
        if self.is_element_visible('//*[@id="username"]'):
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("button[type='submit']")
            self.sleep(1)
            self.click_if_visible(account_data['default_log_select_page'])  # 点击小店试试的小店
            self.sleep(1)
        else:
            self.type("(//input[@placeholder='请输入手机号'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click('//button[text()="快手商家账号登录"]')
            self.sleep(2)
            self.click_if_visible(account_data['default_log_select_page'])  # 点击小店试试的小店
            self.sleep(2)

    def get_cookies(self):
        """Saves the page cookies to the "saved_cookies" folder."""
        self.wait_for_ready_state_complete()
        cookies = self.driver.get_cookies()
        return cookies

    def MerchantLoginBySmscode(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)

        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[2]')
        self.assert_text("账号登录", '//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')  # div标签中的第一个元素
        # self.assert_text("验证码登录", "div.choseTab--xMHsG > div:nth-child(2)")
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[4]/form/div[1]/div/div/span/input')
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        sleep(2)
        self.assert_text("请选择您要登录的店铺", '//*[@id="rcDialogTitle0"]')
        self.click(account_data['default_log_select_page'])  # 点击小店试试的小店
        sleep(1)

    def MerchantAccountLoginBySmscode(self, domain, account):
        account_data = get_account_info(account)
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)

        self.open(host)
        self.click('//span[text()="密码登录"]')
        # 老的客服工作台登录
        if self.is_element_visible('//*[@id="username"]'):
            self.click('//*[@id="username"]')
            self.type('//*[@id="username"]', account_data['account'])
            self.click('//*[@id="password"]')
            self.type('//*[@id="password"]', account_data['password'])
            self.click("button[type='submit']")
            # sleep(2)
        # 新的客服工作台登录
        else:
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click('//button[text()="快手APP账号授权登录"]')
            # self.sleep(2)

    def goto_cs_detail(self):
        self.MerchantLoginBySmscode("MERCHANT_DOMAIN", "detest017_74")
        self.click('//*[@id="menu_item_21"]/span/span')
        self.click('//*[@id="entry_pc"]/div[1]/div/div[3]/div[3]/div')
        self.click('//*[@id="entry_pc"]/div[1]/div/div[5]/div[2]/div[1]/div[1]/div/div/div')

    def check_if_page_404(self):
        print("---")
        # current_url = self.get_current_url()
        # flag = True
        # if current_url.find("404") != -1 or current_url.find("error") != -1 or current_url.find(
        #         "err") != -1 or current_url.find("ERROR") != -1:
        #     flag = False
        # assert_that(flag, is_(True), "页面404或者error，请关注")

    def close_alert_window(self):
        close_icon = self.find_elements("//div[@class='close-icon']")
        for item in close_icon:
            try:
                item.click()
            except Exception:
                print(Exception)
                self.driver.refresh()
        close_icon = self.find_elements("//div[@class='el-notification__closeBtn el-icon-close']")
        for item in close_icon:
            try:
                item.click()
            except Exception:
                print(Exception)
                self.driver.refresh()

    def auto_message_switch(self, type):
        self.driver.execute_script("arguments[0].scrollIntoView();", self.find_element(
            "//span[text()='{}']/../..//div[@class='actions']/button[2]".format(type)))
        self.click("//span[text()='{}']/../..//div[@class='actions']/button[2]".format(type))
        self.click("//div[@class='ant-modal-body']//button[2]")
        self.assert_element("//span[text()='{}']/../..//span[text()='立即开启']".format(type))
        self.click("//span[text()='{}']/../..//div[@class='actions']/button[1]".format(type))
        self.click("//span[text()='保 存']/..")
        self.assert_element("//span[text()='{}']/../..//span[text()='停 用']".format(type))

    def hide_seller_helper_box(self):
        # 隐藏经营助手大屏弹窗
        try:
            # 缩小【会话模块】弹窗
            self.find_element(
                '//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img')
            self.click(
                '//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img')
        except Exception as e:
            print(e)

    def move_seller_helper_box(self, bottom='95px'):
        # 移动经营助手弹窗
        self.execute_script(
            'document.getElementById("kpro-tool-box-tools-containner").style="bottom: '
            + bottom + '; right: 4px; z-index: 99; transform: translate(0px, -101px);"'
        )

    def close_sidebar_guide_toast(self):
        # 点击侧边栏：知道了-toast
        try:
            for i in range(3):
                if self.is_element_visible('//div[@id="driver-popover-item"]'):
                    self.click('//html')
                    sleep(1)
                    continue
                self.click_if_visible('//button[text()="知道了"]')
        except Exception as e:
            print("侧边栏toast知道了：已点完/未出现")

    def close_common_guide_toast(self):
        # 点击客服工作台普通：知道了-toast
        try:
            for i in range(2):
                # 点击知道了
                self.click('//*[text()="知道了"]')
        except Exception as e:
            print("普通toast知道了：已点完/未出现")

    def reduce_chat_page_frame(self):
        # 缩小【会话模块】弹窗
        try:
            self.find_element('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img')
            self.click('//*[@id="kpro-tool-box--sellerHelperBox"]/div[1]/img')
        except Exception as e:
            print(e)

    def close_questionnarie_frame(self):
        # 关闭问卷调查弹窗
        try:
            self.find_element('//span[contains(text(),"感受如何")]/parent::div/span[2]')
            self.click('//span[contains(text(),"感受如何")]/parent::div/span[2]')
        except Exception as e:
            print(e)

        # 缩小【会话模块】弹窗
        self.reduce_chat_page_frame()

        # 关闭【售前转化】弹窗
        try:
            self.click('//span[text()="关闭"]')
        except Exception as e:
            print(e)

    def quit_newcomer_guide_frame(self):
        # 退出新人引导弹窗
        try:
            self.find_element("//button[text()='退出引导']")
            self.click("//button[text()='退出引导']")
        except Exception as e:
            print(e)
        sleep(1)

    # def process_store_hint_frame(self):
    #     # 处理小店后台弹窗

    def Close_the_scene_solution_popup_window(self):
        # 智能客服-问答管理-
        try:
            for i in range(3):
                if self.is_element_visible('//button[@class="ant-btn ant-btn-primary"]'):
                    self.click('//span[text()="完 成"]')
                    sleep(1)
                    continue
                self.click_if_visible('//button[text()="完 成"]')
        except Exception as e:
            print("完成：已点完/未出现")

    def close_common_guide_toast2(self):
        try:
            for i in range(3):
                if self.is_element_visible('#driver-popover-item'):
                    self.click('#driver-popover-item > div.driver-clearfix.driver-popover-footer > button')
        except Exception as e:
            print("完成：已点完/未出现")

    def close_tianhe_frame(self):
        try:
            for i in range(3):
                self.click('//button[@class="kwaishop-tianhe-promote-reach-out-pc-modal-close"]')
        except Exception as e:
            print("完成：已点完/未出现")

    def go_cooperation_workbench(self, data="", url="", user="customer_merchant", id=''):
        # 添加泳道
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", user)

        self.maximize_window()

        sleep(2)

        if data != "" and url != "":
            cookies = self.driver.get_cookies()
            print("cls", cookies)
            cookie = ''
            for value in cookies:
                cookie = cookie + value["name"] + "=" + value["value"] + ";"

            print("ck=", cookie)
            headers = {
                'Content-Type': 'application/json',
                "Cookie": cookie
            }
            res = requests.request(url=url, method="POST",
                                   json=data, headers=headers).json()
            print(res)

        self.open('https://im.kwaixiaodian.com/workbench/zone/cooperation?role=merchant#ud='+id)
        # self.open("https://im.kwaixiaodian.com/workbench#ud=" + id)
        sleep(1)

        # 退出新人引导
        # self.check_if_page_404()
        self.quit_newcomer_guide_frame()

        # 点击全部会话tab
        self.click('//div[text()="全部会话"]')
        sleep(1)

        # 关闭问卷弹窗
        self.close_questionnarie_frame()

    def go_distribution_workbench(self, data="", url="", user="customer_merchant", id=''):
        # 添加泳道
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", user)

        self.maximize_window()

        sleep(2)

        if data != "" and url != "":
            cookies = self.driver.get_cookies()
            print("cls", cookies)
            cookie = ''
            for value in cookies:
                cookie = cookie + value["name"] + "=" + value["value"] + ";"

            print("ck=", cookie)
            headers = {
                'Content-Type': 'application/json',
                "Cookie": cookie
            }
            res = requests.request(url=url, method="POST",
                                   json=data, headers=headers).json()
            print(res)

        self.open('https://cps.kwaixiaodian.com/zone-cps/home')
        # self.open("https://im.kwaixiaodian.com/workbench#ud=" + id)
        sleep(1)

        # 退出新人引导
        # self.check_if_page_404()
        self.quit_newcomer_guide_frame()

        # 关闭问卷弹窗
        self.close_questionnarie_frame()

    def go_operator_workbrench(self, data="", url="", user="customer_merchant"):
        # 主账号登录小二工作台
        # from seleniumwire import webdriver

        # 获取当前日期，根据当前日期获取环境变量
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)                # ⭐️环境变量     "prt"
        self.LANE_ID = self.get_laneid(self.ENV)                      # ⭐️泳道        "PRT.test"
        # 如果是prt带泳道的环境(泳道名称非空)，需要切换泳道
        if len(self.LANE_ID) > 0:
            self.driver.quit()
            self.driver = webdriver.Chrome()
            self.driver.header_overrides = {"trace-context": '{"laneId":"' + self.LANE_ID + '"}'}

        # 以主账号身份登录客服工作台
        self.clear_all_cookies()
        self.MerchantAccountLoginBySmscode("MERCHANT_DOMAIN", user)
        self.maximize_window()
        sleep(2)

        # 打开小二工作台页面
        if self.ENV == "prt":
            self.open("https://eshop-im.prt.kwaixiaodian.com/kwaishop/im/kwaioperator")
        else:
            self.open("https://im.kwaixiaodian.com/kwaishop/im/kwaioperator")
        sleep(8)

    def go_operator_workbrench_child(self, data="", url="", user="customer-jsh", id="**********"):
        # 子账号登录小二工作台
        from seleniumwire import webdriver

        # 获取当前日期，根据当前日期获取环境变量
        weekday = datetime.date.today().strftime("%A")
        hour = datetime.datetime.now().hour
        self.ENV = self.get_environment(weekday, hour)  # ⭐️环境变量
        self.LANE_ID = "PRT.test"  # ⭐️泳道
        # 如果是prt带泳道的环境(泳道名称非空)，需要切换泳道
        if len(self.LANE_ID) > 0:
            self.driver.quit()
            self.driver = webdriver.Chrome()
            self.driver.header_overrides = {"trace-context": '{"laneId":"' + self.LANE_ID + '"}'}

        # 以子账号身份登录客服工作台
        self.clear_all_cookies()
        self.ZiaccountLogin("MERCHANT_DOMAIN", user)
        self.maximize_window()
        sleep(3)

        # 打开小二工作台页面
        if self.ENV == "prt":
            self.open("https://eshop-im.prt.kwaixiaodian.com/kwaishop/im/kwaioperator")
        else:
            self.open("https://im.kwaixiaodian.com/kwaishop/im/kwaioperator")
        sleep(8)


