import json

import requests
import base64
from io import StringIO
import hashlib
from urllib.parse import urlencode
import time


class OrderTools:

    # 创建订单
    def create_order_union(self, account=None, seller_id=None, item_id=None, sku_id=None, payment_fee=None,
                           address_id=None, **kw):
        account_data = ACCOUNT_ONLINE_CONFIG.get(account)
        url = "https://prt-eshop-app.test.gifshow.com/rest/app/trade/c/v1/ks/order/create/order/union"
        sign_data = api_login_for_order(account=account, password=account_data["password"],
                                        account_type=account_data["type"], user_id=account_data["uid"])
        ver = CLIENT_CONFIG["IOS_NEW"]["version"]
        did = CLIENT_CONFIG["IOS_NEW"]["did"]
        client_key = CLIENT_CONFIG["IOS_NEW"]["client_key"]
        cookies = "token={};appver={};did={};client_key={};kuaishou.api_st={};token_client_salt={};" \
                  "userId={};ud={};KS-CSRF-Token={}".format(sign_data['token'], ver, did, client_key,
                                                            sign_data['kuaishou.api_st'],
                                                            sign_data['token_client_salt'],
                                                            sign_data['region']['uid'], sign_data['region']['uid'],
                                                            "autotest")
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'prt-eshop-app.test.gifshow.com',
            'KS-CSRF-Token': 'autotest',
            "Cookie": cookies,
            'X-KTrace-Id-Enabled': "1",
            'ptp-flag': "1",
            'trace-context': json.dumps({"laneId": "PRT.test"}),
        }
        data = {
            "createOrderParam": json.dumps({"freightInsuranceProviderUnused": 0, "remark": "",
                                            "transparentParam": json.dumps(
                                                {"addressId": address_id, "promotionParam": [],
                                                 "shopInfo": {"sellerId": str(seller_id)},
                                                 "orderProductInfo": {"itemId": item_id, "skuId": sku_id,
                                                                      "skuSalePrice": 1, "count": 1, "traceTag": "",
                                                                      "promoteId": "", "promoteChannel": ""},
                                                 "expressInfo": {"expressFee": 0},
                                                 "cashierParam": {"installWechatSdk": True, "installWechat": True,
                                                                  "installAlipaySdk": True, "installAlipay": True,
                                                                  "installUnionPaySdk": True, "installUnionPay": False},
                                                 "skuExtraInfo": {"whetherSeckill": False, "whetherPreSale": False,
                                                                  "startSaleTime": 0, "sellerControlStartSale": False},
                                                 "paymentFee": payment_fee, "cpsVersion": "", "description": "",
                                                 "freightInsuranceInfo": None}),
                                            "carrierEntry": "", "carrierId": "JtuAuguFO6Y", "carrierType": "2",
                                            "extInfo": None,
                                            "kwaiMerchantCpsTrack": "", "likeExpTag": "", "saleAuthorization": "",
                                            "serverExpTag": "feed_live|d_lbDa_oc-c|**********|1_a/2000315720567429714_f81sl",
                                            "serverExpTagList": "feed_live|d_lbDa_oc-c|**********|1_a/2000315720567429714_f81sl",
                                            "traceTag": ""}),
            "preCreateParam": json.dumps({"hide": True, "provider": "ALIPAY", "providerChannelType": "NORMAL"}),
            "freightInsurance": 0,
            "act": "",
            "cs": False,
            "client_key": client_key,
            "os": "android",
            "appver": ver,
            "token": sign_data['token'],
            "token_client_salt": sign_data["token_client_salt"],
            "kuaishou.api_st": sign_data["kuaishou.api_st"],
            "ud": sign_data['region']['uid'],
            "userId": sign_data['region']['uid'],
            "did": did
        }
        sig_data = get_kuaishou_api_sign_param(data, sign_data['token'], sign_data["token_client_salt"],
                                               client_key, CLIENT_CONFIG["IOS_NEW"]["secret"])
        # createOrderParam = {"freightInsuranceProviderUnused": 0, "remark": "",
        #                         "transparentParam": "{\"addressId\":1544965355079, \"promotionParam\":[], \"shopInfo\":{\"sellerId\": \"**********\"}, \"orderProductInfo\":{\"itemId\": 20367752532916,\"skuId\":76464600926916,\"skuSalePrice\":1,\"count\":1,\"traceTag\":\"\",\"promoteId\":\"\",\"promoteChannel\":\"\"},\"expressInfo\":{\"expressFee\":0},\"cashierParam\":{\"installWechatSdk\":True,\"installWechat\":True,\"installAlipaySdk\":True,\"installAlipay\":True,\"installUnionPaySdk\":True,\"installUnionPay\":False},\"skuExtraInfo\":{\"whetherSeckill\":False,\"whetherPreSale\":False,\"startSaleTime\":0,\"sellerControlStartSale\":False},\"paymentFee\":1,\"cpsVersion\":\"\",\"description\":\"\",\"freightInsuranceInfo\":None}",
        #                         "carrierEntry": "", "carrierId": "JtuAuguFO6Y", "carrierType": "2", "extInfo": None,
        #                         "kwaiMerchantCpsTrack": "", "likeExpTag": "", "saleAuthorization": "",
        #                         "serverExpTag": "feed_live|d_lbDa_oc-c|**********|1_a/2000315720567429714_f81sl",
        #                         "serverExpTagList": "feed_live|d_lbDa_oc-c|**********|1_a/2000315720567429714_f81sl",
        #                         "traceTag": ""}
        data.update({"sig": sig_data["sig"]})
        data.update({"__NS_sig3": sig_data["__NS_sig3"]})
        data.update({"__NStokensig": sig_data["__NStokensig"]})
        data = urlencode(data)
        res = requests.post(url, data=data, headers=headers).json()
        return res

    # 取消订单
    def cancel_order(self, account=None, oid=None):
        account_data = ACCOUNT_ONLINE_CONFIG.get(account)
        url = "https://prt-eshop-app.test.gifshow.com/rest/app/grocery/ks/order/cancel"
        sign_data = api_login_for_order(account=account, password=account_data["password"],
                                        account_type=account_data["type"], user_id=account_data["uid"])
        ver = CLIENT_CONFIG["IOS_NEW"]["version"]
        did = CLIENT_CONFIG["IOS_NEW"]["did"]
        client_key = CLIENT_CONFIG["IOS_NEW"]["client_key"]
        cookies = "token={};appver={};did={};client_key={};kuaishou.api_st={};token_client_salt={};" \
                  "userId={};ud={};KS-CSRF-Token={}".format(sign_data['token'], ver, did, client_key,
                                                            sign_data['kuaishou.api_st'],
                                                            sign_data['token_client_salt'],
                                                            sign_data['region']['uid'], sign_data['region']['uid'],
                                                            "autotest")
        headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'Host': 'prt-eshop-app.test.gifshow.com',
            'KS-CSRF-Token': 'autotest',
            "Cookie": cookies,
            'X-KTrace-Id-Enabled': "1",
            'ptp-flag': "1",
            'trace-context': json.dumps({"laneId": "PRT.test"}),
        }
        data = {
            "oid": oid
        }
        res = requests.post(url, json=data, headers=headers).json()
        return res

    # 免密支付
    def pay_order_without_passwd_test(self, account, oid, provider_pay_amount=1):
        cashier_api = 'https://www.kuaishoupay.com/pay/order/app/trade/cashier'
        pay_order_api = 'https://www.kuaishoupay.com/pay/order/app/trade/create_pay_order'
        merchant_id = '********************'
        data = {
            'merchant_id': merchant_id,
            'out_order_no': oid,
            'extra': '',
            'is_install_wechat': 'True',
            'is_install_alipay': 'True',
            'is_install_union_pay': 'false',
            'is_install_wechat_sdk': 'True',
            'is_install_alipay_sdk': 'True',
            'is_install_union_pay_sdk': 'false',
            'retry_times': '0'
        }
        account_data = ACCOUNT_ONLINE_CONFIG.get(account)
        cookies = dict()
        # 设置api_token
        api_token = api_login_for_order(account=account, password=account_data["password"],
                                        account_type=account_data["type"], user_id=account_data["uid"])
        data["out_order_no"] = str(oid)
        cookies["kuaishou.api_st"] = api_token["kuaishou.api_st"]
        cookies["token"] = api_token["token"]
        cookies["did"] = "51B907F5-998C-4D89-9E8A-41377FF0724C"
        print(f'cookies: {cookies}')
        resp = requests.post(cashier_api, cookies=cookies, data=data)
        for _ in range(3):
            if resp.json()['result'] == 'SUCCESS':
                break
            time.sleep(2)
            resp = requests.post(cashier_api, cookies=cookies, data=data)

        # 支付
        pay_data = {'merchant_id': merchant_id, 'provider': 'ALIPAY', "out_order_no": str(oid),
                    'payment_method': 'PAP', 'provider_channel_type': 'NORMAL',
                    'provider_pay_amount': provider_pay_amount,
                    'fq_stage': ''}
        # headers = {'trace-context': json.dumps({'laneId': 'STAGING.channel_mock'})}
        headers = {}  # 试了下headers不用这个泳道也可以支付成功, 先注释掉泳道
        resp = requests.post(pay_order_api, headers=headers, cookies=cookies, data=pay_data)
        for _ in range(3):
            if resp.json()['result'] in ('SUCCESS', 'ORDER_TRADE_HAS_PAID'):
                break
            time.sleep(2)
            resp = requests.post(pay_order_api, cookies=cookies, data=pay_data)
        return resp.json()


def decrypt(content):
    if not content.startswith("enc:"):
        return content
    content = content[4:]
    return str(base64.decodebytes(str.encode(content[1:] + '=' * int(content[0]))), encoding='utf8')


def api_login_for_order(account="dstest079", password="zzj19960329", account_type="mobile", did=None, appver=None,
                        client_key=None, user_id=None):
    if did is None:
        did = CLIENT_CONFIG['IOS_NEW']['did']
    if appver is None:
        appver = CLIENT_CONFIG['IOS_NEW']['version']
    if client_key is None:
        client_key = CLIENT_CONFIG['IOS_NEW']['client_key']
    # sig = hash('md5', "client_key=56c3713cmobile=***********mobileCountryCode=+86password=089d0dc9a68cc583873dc46306e3091a4eab4dbf3df06fdb77b0f04072ec1c33acd055decae4580478170407073cc5ba4dc66b70e2829fe057267d1d317d260823caab00356c")
    if account_type == 'smscode':
        url_suffix = 'mobileVerifyCode'
        type_key = 'mobile'
        password_key = 'code'
        pw = decrypt(password)
    else:
        url_suffix = account_type
        type_key = account_type
        password_key = 'password'
        pw = hash('sha512', decrypt(password))
    url = "https://apissl.gifshow.com/rest/n/user/login/" + url_suffix
    account_config = ACCOUNT_ONLINE_CONFIG.get(account)
    account, mobile_country_code = account_config["account"], '+86' if "countryCode" not in account_config.keys() else \
    account_config["countryCode"]
    param_data = {
        type_key: account,
        password_key: pw,
        "mobileCountryCode": mobile_country_code,
        "client_key": client_key,
    }
    secret = "23caab00356c"
    # for value in CLIENT_CONFIG.values():
    #     if value.get("client_key") == client_key:
    #         secret = value.get("secret")
    sign_param = get_kuaishou_api_sign_param(param_data, None, None, client_key, secret)
    param_data["sig"] = sign_param["sig"]
    param_data["__NS_sig3"] = "2159768269c7ffffffffffffffffffffffffffffff"
    header = {
        'Cookie': "appver={};did={};client_key={}".format(appver, did, client_key)}
    login_data = requests.post(url=url, data=param_data, headers=header).json()
    return login_data


def get_kuaishou_api_sign_param(param_dict, token, token_client_salt, client_key, secret):
    if token:
        param_dict['token'] = token
    param_dict['client_key'] = client_key
    arg_str_buffer = StringIO()
    for k, v in sorted(param_dict.items(), key=lambda d: d[0]):
        if k == 'sig' or k.startswith('__NS'):
            continue
        arg_str_buffer.write('{}={}'.format(k, v))
    arg_str_buffer.write(secret)
    arg_str = arg_str_buffer.getvalue()
    sig = hash('md5', arg_str)
    if token_client_salt:
        ns_token_sig = hash('sha256', sig + token_client_salt)
    else:
        ns_token_sig = None
    ns_sig3 = '2159768269c7ffffffffffffffffffffffffffffff'
    sign_param = {'client_key': client_key, 'sig': sig, '__NS_sig3': ns_sig3}
    if token:
        sign_param['token'] = token
    if ns_token_sig:
        sign_param['__NStokensig'] = ns_token_sig
    return sign_param

def hash(algorithm, content):
    hashobj = getattr(hashlib, algorithm)()
    hashobj.update(content.encode('utf-8'))
    return hashobj.hexdigest()

TOKEN_CONFIG = CLIENT_CONFIG = {

    # 框架自动登录体系专用
    "IOS_NEW": {"version": "11.3.20.1334759",
                "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
                "client_key": "56c3713c",
                "secret": "23caab00356c"},

    "IOS_10": {"version": "10.9.10.630",
               "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
               "client_key": "56c3713c",
               "secret": "23caab00356c"},

    "IOS_11": {"version": "11.1.30.630",
               "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
               "client_key": "56c3713c",
               "secret": "23caab00356c"},

    "IOS_12": {"version": "11.5.30.630",
               "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
               "client_key": "56c3713c",
               "secret": "23caab00356c"},

    # 框架自动登录体系专用
    "WEB_NEW": {"did": 'web_3zis6zhuz1lgnfqblwwyw7mvgi2it7ph', "client_key": "56c3713c"},

    "Pay": {
        "cookie": {"Kspay-Client-SDK": "2.1.7",
                   "kuaishou.midground.api_st": "ChlrdWFpc2hvdS5taWRncm91bmQuYXBpLnN0EqABYS8Qoy-m30TTfh5avesoAfAScbvUyNxIbKQlndW1SsHUqNTmcni7B5nWBsy83QLGjNDXbex-Ph5Jf8ihujtMB25gcAGcYLlUCLUfpGsvRNIPuEeHLjb4DgmNH3mDHHRcrLZVoGz_1Y07GtlHVgeqSoR9gFDnJyu4j_UBljB7BakB1S2piW47XOWVJN7ejhJ1ddOiX0gMrWD5PuiZtz7BtBoSImJ5p6EN2Qh_Uw-gb4grsigYIiBwMYOtJyIRBdbKsBTxorm_wPV83ATDEsh8so0E7VvTsCgFMAE",
                   "User-Agent": "kwai-ios", "kspay_encode": True, "country_code": "cn"},
        "header": {"Content-Type": "application/x-www-form-urlencoded"}
    }

}

ACCOUNT_ONLINE_CONFIG = {
    "dstest079": {"account": "***********", "password": "zzj19960329", "type": "mobile", "uid": "**********"},
    "dstest059": {"account": "**********", "password": "dstest@123", "type": "mobile", "uid": "**********"},
    "dstest045": {"account": "**********", "password": "123456", "type": "mobile", "uid": "**********"},
    "autotest_tcw": {"account": "***********", "password": "cesczero1", "type": "mobile", "uid": "**********"},
    "customer-z": {"account": "***********", "password": "ltn5090791", "type": "mobile", "uid": "**********"},
    "customer-ning": {"account": "***********", "password": "wang123456", "type": "mobile", "uid": "**********"}
}
