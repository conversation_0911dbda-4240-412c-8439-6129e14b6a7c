import time
from seleniumbase import BaseCase



class TestOperate(BaseCase):  # 继承自BaseCase框架

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # 初始化配置项
        cls.login_url = "https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage"
        cls.username = 'wb_<PERSON><PERSON><PERSON><PERSON>@kuaishou.com'
        cls.password = '11193018Hui#'

    def load_urls(self):
        """页面加载通用逻辑"""
        self.open(self.login_url)
        self.maximize_window()
        self.wait_for_element_present('body', timeout=30)
        print("页面加载完成，开始登录流程")
        self.login_flow()
        time.sleep(5)
        self.handle_guides()#处理按钮

    def login_flow(self):
        """登录流程重构"""
        print("等待 iframe 元素可见")
        self.wait_for_element_visible("iframe[src*='sso.corp.kuaishou.com']", timeout=30)  # 增加等待时间
        print("切换到 iframe")
        self.switch_to_frame("iframe[src*='sso.corp.kuaishou.com']")  # 使用部分匹配
        self.wait_for_element_visible("#loginTabSso", timeout=30)
        self.click("#loginTabSso")  # 使用框架的点击方法
        self.type("#ssoUsername", self.username)  # 使用框架的输入方法
        self.type("#ssoPassword", self.password)
        self.click("#ssoSubmit")
        self.switch_to_default_content()
        print("登录成功")


    def login_out(self):
        # 退出操作
        self.click('//div[text()="wb_zhangzhihui"]')  # 确认用户名正确
        self.click('//span[text()="退出"]')
        print('退出成功')
    def handle_guides(self):
        """处理引导按钮"""
        guide_selectors = [
            ('div.guide1 button.button1', 5),
            ('div.guide2 button.button2', 5),
            ('div.guide3 button.button3', 5)
        ]
        for selector, wait_time in guide_selectors:
            self.wait_for_element_clickable(selector)
            self.click(selector)
            time.sleep(wait_time)
    def scroll_to_element(self, element):#下滑到特定元素
        self.execute_script("arguments[0].scrollIntoView();", element)

    # 确保活动名称不重复，使用时间戳
    def generate_unique_activity_names(base_name):
        timestamp = int(time.time())
        return f"{base_name}_{timestamp}"


    """支付运营平台——营销活动管理——详情页：进入页面/相关信息cherck是否正确"""
    def test_marketing_case1(self):
        """支付运营平台——营销活动管理——详情页"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")  # 使用框架的输入方法
        # 点击查询按钮
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)
        # 验证查询结果（营销活动详情）
        self.wait_for_element_visible('//div[text()="营销活动详情"]', timeout=30)  # 状态文本验证<cite>[1]</cite>
        self.assert_element('//div[text()="营销活动详情"]')#判断是否进入到营销活动详情页
        print('进入营销活动详情页正常')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case2(self):
        """支付运营平台——营销活动管理——详情—活动名称、活动有效时间、活动业务方校验"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")  # 使用框架的输入方法
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)

        # 检查活动名称（仅内部看）是否正确
        tc = self.find_element('#basic_activityName')
        ed = tc.get_attribute('value')
        sc = 'UI测试-营销活动测试3'
        if ed == sc:
            print('活动名称正确')
        else:
            raise ValueError('活动名称错误')

        # 检查活动有效时间是否正确
        ac = self.find_element('#basic_promoRangeTime')  # 开始时间
        cc = ac.get_attribute('value')
        ec = '2024-12-01 00:00:00'
        # 开始时间是否正确
        if cc == ec:
            print('活动开始时间正确')
        else:
            raise '活动开始时间错误'
        bc = self.find_element('input[value="2025-12-31 23:59:59"]')  # 结束时间
        dc = bc.get_attribute('value')
        fc = '2025-12-31 23:59:59'
        # 结束时间是否正确
        if dc == fc:
            print('活动结束时间正确')
        else:
            raise '活动结束时间错误'

        # 检查活动业务房是否正常
        qc = self.find_element('span[title="支付业务部"]')
        wc = qc.get_attribute('title')
        rc = '支付业务部'
        if wc == rc:
            print('活动业务房正确')
        else:
            raise '活动业务房错误'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case3(self):
        """支付运营平台——营销活动管理——详情—支付优惠营销系统校验"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)

        # 检查活动业务房是否正常
        qc = self.find_element('span[title="支付自有营销系统"]')
        wc = qc.get_attribute('title')
        rc = '支付自有营销系统'
        if wc == rc:
            print('活动业务房正确')
        else:
            raise '活动业务房错误'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()




    def test_marketing_case4(self):
        """支付运营平台——营销活动管理——详情—优惠场景（营销场景）、营销方式（优惠类型）、优惠预算承担规则、收银台营销标签（对客展示）、校验"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)
        # 下滑到-优惠信息配置
        test1 = self.find_element("//h5[text()='优惠信息配置']")
        self.scroll_to_element(test1)
        time.sleep(1)

        # 检查优惠场景（营销场景）是否正常
        qc = self.find_element('span[title="收银台支付场景"]')
        wc = qc.get_attribute('title')
        rc = '收银台支付场景'
        if wc == rc:
            print('优惠场景（营销场景）正确')
        else:
            raise '优惠场景（营销场景）错误'

        # 检查营销方式（优惠类型）立减数据是否正常
        reduction_category = self.find_element('#basic_promoDisCountRule_fixedDiscountAmount')
        sd = reduction_category.get_attribute('value')
        df = '1.88'
        if sd == df:
            print('优惠场景（营销场景）立减数据正确')
        else:
            raise '优惠场景（营销场景）立减数据错误'
        # 下滑到-赠运费险
        test2 = self.find_element("//span[text()='赠运费险']")
        self.scroll_to_element(test2)
        time.sleep(1)

        # 优惠预算承担规则
        tc = self.find_element('span[title="内部出资100%"]')
        yc = tc.get_attribute('title')
        uc = '内部出资100%'
        # 开始时间是否正确
        if yc == uc:
            print('优惠预算承担规则正确')
        else:
            raise '优惠预算承担规则错误'

        #检查收银台营销标签（对客展示）规则
        tc = self.find_element('#basic_promoDisCountRule_promoDesc')
        yc = tc.get_attribute('value')
        uc = '测试营销标签'
        if yc == uc:
            print('收银台营销标签展示正确')
        else:
            raise '收银台营销标签展示错误'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case5(self):
        """支付运营平台——营销活动管理——详情—限定支付商户号、限定订单金额、是否支持预售、是否支持合单、商家进件情况校验"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)
        # 下滑到-活动参与条件-业务场景规则
        test1 = self.find_element("//h5[text()='活动参与条件-业务场景规则']")
        self.scroll_to_element(test1)
        time.sleep(1)

        # 检查限定支付商户号是否正确
        qc = self.find_element('span[title="测试业务6-testmerchant20180328000"]')
        wc = qc.get_attribute('title')
        rc = '测试业务6-testmerchant20180328000'
        if wc == rc:
            print('限定支付商户号正确')
        else:
            raise '限定支付商户号错误'

        # 检查限定订单最低金额是否正确
        ac = self.find_element('#basic_contributionRule_minOrderAmount')
        bc = ac.get_attribute('value')
        cc = '10.00'
        if bc == cc:
            print('限定订单最低金额正确')
        else:
            raise '限定订单最低金额错误'
        # 检查限定订单最高金额是否正确
        ac = self.find_element('#basic_contributionRule_maxOrderAmount')
        bc = ac.get_attribute('value')
        cc = '100.00'
        if bc == cc:
            print('限定订单最高金额正确')
        else:
            raise '限定订单最高金额错误'

        # 下滑到-限定商品id
        test2 = self.find_element("#basic_contributionRule_goodsIdList")
        self.scroll_to_element(test2)
        time.sleep(1)

        #校验 是否支持预售
        bb = self.find_element('span[title="支持订金单+普通单可用"]')
        bt = bb.get_attribute('title')
        tt = '支持订金单+普通单可用'
        if bt == tt:
            print('是否支持预售数据正确')
        else:
            raise '是否支持预售数据错误'
        #校验 是否支持合单
        rr = self.find_element("input[value='true']")
        yy = rr.get_attribute('value')
        uu = 'true'
        if yy == uu:
            print('是否支持合单数据正确')
        else:
            raise '是否支持合单数据错误'
        #校验 商家进件情况
        kk = self.find_element("input[value='PINGAN_JZB']")
        ll = kk.get_attribute('value')
        jj = 'PINGAN_JZB'
        if ll == jj:
            print('是否支持合单数据正确')
        else:
            raise '是否支持合单数据错误'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case6(self):
        """支付运营平台——营销活动管理——限定支付渠道、限定支付场景 数据校验"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)
        # 下滑到-限定订单来源carriertype
        test1 = self.find_element("#basic_contributionRule_carrierType")
        self.scroll_to_element(test1)
        time.sleep(1)

        # 检查限定支付渠道是否正确
        qc = self.find_element('span[title="微信"]')
        wc = qc.get_attribute('title')
        rc = '微信'
        if wc == rc:
            print('限限定支付渠道正确')
        else:
            raise '限定支付渠道错误'
        # 检查限定支付场景是否正确
        qc = self.find_element('span[title="INAPP"]')
        wc = qc.get_attribute('title')
        rc = 'INAPP'
        if wc == rc:
            print('限定支付场景正确')
        else:
            raise '限定支付场景错误'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case7(self):
        """支付运营平台——营销活动管理——限制用户风控等级、每用户参与次数限制、活动team 数据校验"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-详情
        self.click("//a[text()='详情']")
        time.sleep(3)
        # 下滑到-活动目标用户
        test1 = self.find_element("//h5[text()='活动目标用户']")
        self.scroll_to_element(test1)
        time.sleep(1)

        # 检查限制用户风控等级是否正确
        qc = self.find_element('//*[@id="basic_promoAccountInfoRule_accountRiskLevelList"]/label[2]/span[1]/input')
        wc = qc.get_attribute('title')
        rc = 'LOW'
        if wc == rc:
            print('限制用户风控等级正确')
        else:
            raise '限制用户风控等级错误'

        # 下滑到-活动限频次规则
        test1 = self.find_element("//h5[text()='活动限频次规则']")
        self.scroll_to_element(test1)
        time.sleep(1)

        # 检查每用户参与次数限制是否正确
        mm = self.find_element('#basic_accountIdTimes')
        if mm:
            print('每uid该活动限制参与存在')
        # 检查参与次数是否正确
        qq = self.find_element('#basic_accountIdTimes')
        ww = qq.get_attribute('value')
        ee = '1'
        if ww == ee:
            print('参与次数正确')
        else:
            raise '参与次数错误'

        # 检查活动team是否正确
        rr = self.find_element('#basic_team')
        tt = rr.get_attribute('value')
        yy = 'ksjdsjdksjdjsdj'
        if tt == yy:
            print('活动team正确')
        else:
            raise '活动team错误'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case8(self):
        """支付运营平台——营销活动管理——修改"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-修改
        self.click("//a[text()='修改']")
        time.sleep(3)
        aa = self.find_element("//div[text()='修改营销活动']")
        if aa:
            print('进入修改营销活动页正常')
        else:
            print('进入修改营销活动页异常')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case9(self):
        """支付运营平台——营销活动管理——白名单验证-打开弹窗、关闭弹窗"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-白名单验证
        self.click("//a[text()='白名单验证']")
        time.sleep(3)
        aa = self.find_element('#whiteAcountIds')
        if aa:
            print('触发白名单弹窗正常')
        else:
            print('未触发弹窗')
        self.click("//span[text()='取 消']")
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case10(self):
        """支付运营平台——营销活动管理——白名单验证-输入账号uid，加白"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-白名单验证
        self.click("//a[text()='白名单验证']")
        time.sleep(3)
        self.type("#whiteAcountIds", "123", timeout=30)  # 输入uid
        self.click('//span[text()="确 定"]')
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case11(self):
        """支付运营平台——营销活动管理——复制"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-复制
        self.click("//a[text()='复制']")
        time.sleep(3)
        aa = self.find_element("//div[text()='新建营销活动']")
        if aa:
            print('进入复制营销活动页正常')
        else:
            print('进入复制营销活动页异常')
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case12(self):
        """支付运营平台——营销活动管理——上线/触发弹窗，关闭弹窗"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-上线
        self.click("//a[text()='上线']")
        time.sleep(3)
        aa = self.find_element('#rcDialogTitle5')
        if aa:
            print('触发上线弹窗正常')
        else:
            print('触发上线弹窗异常')
        self.click("//span[text()='取消，去白名单验证']")
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case13(self):
        """支付运营平台——营销活动管理——活动参与实时数据"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 营销活动-上线
        aa = self.find_element("//a[text()='活动参与实时数据']")
        if aa:
            print('活动参与实时数据存在')
            aa.click()
            time.sleep(3)
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case14(self):
        """支付运营平台——营销活动管理——批量操作-批量导出"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        qc = self.find_element('span[text="批量导出"]')
        if qc:
            print('批量导出存在')
            self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span/input')#勾选活动
            qc.click()
            time.sleep(3)
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    def test_marketing_case15(self):
        """支付运营平台——营销活动管理——批量操作-批量修改活动有效期"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        qc = self.find_element('span[text="批量修改活动有效期"]')
        if qc:
            print('批量修改活动有效期存在')
            self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span/input')#勾选活动
            qc.click()
            time.sleep(3)
        rc = self.find_element('#rcDialogTitle12')
        if rc:
            print('批量修改活动有效期弹窗展示正常')
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()


    def test_marketing_case16(self):
        """支付运营平台——营销活动管理——批量操作-批量下线"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        qc = self.find_element('span[text="批量下线"]')
        if qc:
            print('批量下线存在')
            self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span/input')#勾选活动
            qc.click()
            time.sleep(3)
        rc = self.find_element('#rcDialogTitle13')
        if rc:
            print('批量下线操作弹窗展示正常')
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case17(self):
        """支付运营平台——营销活动管理——批量操作-批量上线"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试3")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        qc = self.find_element('span[text="批量上线"]')
        if qc:
            print('批量上线存在')
            self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/thead/tr/th[1]/div/label/span/input')#勾选活动
            qc.click()
            time.sleep(3)
        rc = self.find_element('#rcDialogTitle15')
        if rc:
            print('确定要操作上线吗弹窗展示正常')
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case18(self):
        """支付运营平台——营销活动管理——批量操作-优先级调整"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 活动名称
        self.type("#activityName", "UI测试-营销活动测试")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        qc = self.find_element('span[text="优先级调整"]')
        if qc:
            print('优先级调整存在')
            qc.click()
            self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/button[1]/span/svg')#勾选活动
            time.sleep(3)
        rc = self.find_element('#rcDialogTitle17')
        if rc:
            print('优先级调整弹窗展示正常')
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case19(self):
        """支付运营平台——营销活动管理——新建营销活动/入口"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 新建营销活动（立减类营销）
        self.click('//*[@id="root"]/div/div/article/div[2]/div[2]/div/div[1]/div/div/button')
        time.sleep(3)
        qc = self.find_element('div[text="新建营销活动"]')
        if qc:
            print('进入新建营销活动页正常')
        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_marketing_case20(self):
        """支付运营平台——营销活动管理——新建营销活动/立减类"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 新建营销活动（立减类营销）
        self.click('//*[@id="root"]/div/div/article/div[2]/div[2]/div/div[1]/div/div/button')
        time.sleep(3)
        #活动基础信息================>>>>>>>>>>>>
        # activity_name = self.generate_unique_activity_names()# 生成一个随机名称的活动
        self.type("#basic_activityName", 'UI测试-营销立减类活动9999', timeout=30)#营销活动名称
        time.sleep(2)
        self.type("#basic_promoRangeTime", "2024-12-11 00:00:00", timeout=30) # 输入活动开始时间
        self.type('//input[@placeholder="结束时间"]', "2030-12-11 00:00:00", timeout=30) # 输入活动结束时间
        self.click('//span[text()="确 定"]')# 时间确定
        time.sleep(2)
        self.click('#basic_activityBusiness')#活动业务方
        self.click('//div[text()="支付业务部"]')
        time.sleep(2)
        #优惠系统规则================>>>>>>>>>>>>
        self.click('#basic_promoSystemRule_promoSystemType')#支付优惠营销系统
        self.click('//div[text()="支付自有营销系统"]')
        time.sleep(2)
        # 下滑到-优惠信息配置
        test1 = self.find_element("//h5[text()='优惠信息配置']")
        self.scroll_to_element(test1)
        time.sleep(1)
        #优惠信息配置================>>>>>>>>>>>>
        self.click('#basic_promoDisCountRule_activityScene')#优惠场景（营销场景）
        self.click('//div[text()="收银台支付场景"]')
        time.sleep(2)
        self.click('//span[text()="固定立减"]')
        self.type("#basic_promoDisCountRule_fixedDiscountAmount", "0.1", timeout=30)#固定立减xx元
        time.sleep(2)
        # 下滑到-赠运费险
        test2 = self.find_element("//span[text()='赠运费险']")
        self.scroll_to_element(test2)
        time.sleep(1)
        self.click('#basic_promoDisCountRule_fundSourceType')#优惠预算承担规则
        self.click('//div[text()="内部出资100%"]')
        time.sleep(2)
        self.type("#basic_promoDisCountRule_promoDesc", "营销测试文案", timeout=30)#营销文案（对客展示）
        time.sleep(2)
        #活动参与条件-业务场景规则================>>>>>>>>>>>>
        self.click('#basic_contributionRule_merchantId')#限定支付商户号
        self.click('//div[text()="测试业务6-testmerchant20180328000"]')
        time.sleep(2)
        self.type("#basic_contributionRule_minOrderAmount", "1", timeout=30)#限定订单最小金额
        time.sleep(2)
        self.type("#basic_contributionRule_maxOrderAmount", "10", timeout=30)#限定订单最大金额
        time.sleep(2)
        # 下滑到-限定商品id
        test3 = self.find_element('#basic_contributionRule_goodsIdList')
        self.scroll_to_element(test3)
        time.sleep(1)
        self.click('#basic_contributionRule_supportPresaleType')#是否支持预售
        self.click('//div[text()="支持订金单+普通单可用"]')
        time.sleep(2)
        self.click('//span[text()="平安见证宝"]')#商家进件情况
        time.sleep(2)
        # 下滑到-活动参与条件-支付规则
        test4 = self.find_element("//h5[text()='活动参与条件-支付规则']")
        self.scroll_to_element(test4)
        time.sleep(1)
        self.click('//*[@id="basic"]/div[11]/div/div/div/div[1]/div[2]/div/div/div/div/span[2]')#限定支付渠道
        self.click('//div[text()="银行卡"]')
        time.sleep(2)
        self.click('//*[@id="basic"]/div[11]/div/div/div/div[3]/div[2]/div/div/div/div/div')#限定支付场景
        self.click('//div[text()="INAPP"]')
        time.sleep(2)
        # 下滑到-活动限频次规则
        test5 = self.find_element("//h5[text()='活动限频次规则']")
        self.scroll_to_element(test5)
        time.sleep(1)
        #活动限频次规则================>>>>>>>>>>>>
        self.click('//span[text()="uid限频次"]')
        self.type("#basic_accountIdTimes", "1", timeout=30)#营销文案（对客展示）
        time.sleep(2)
        self.click('//span[text()="uid限频次"]')
        self.type("#basic_team", "https://team.corp.kuaishou.com/task/test1111", timeout=30)#输入对应team
        time.sleep(2)
        #所有信息填写完成，确认新建营销活动
        self.click('//span[text()="确 认"]')
        time.sleep(3)
        #校验新建的活动，是否成功新建
        self.type("#activityName", "UI测试-营销活动立减类9999", timeout=30) #输入对应活动名称
        self.click('//span[text()="查 询"]', timeout=30)  # 点击查询按钮
        time.sleep(3)
        # 2. 等待查询结果加载
        self.wait_for_element_visible('//td[text()="营销立减类活动9999"]', timeout=30)
        self.assert_element('//td[text()="营销立减类活动9999"]')  # 使用框架断言方法
        print('查询活动名称成功')

        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()




    def test_marketing_case21(self):
        """支付运营平台——营销活动管理——新建营销活动/赠送类"""
        # 初始化浏览器并加载页面
        self.load_urls()
        # 新建营销活动（立减类营销）
        self.click('//*[@id="root"]/div/div/article/div[2]/div[2]/div/div[1]/div/div/button')
        time.sleep(3)
        #活动基础信息================>>>>>>>>>>>>
        # activity_names = self.generate_unique_activity_names()# 生成一个随机名称的活动
        self.type("#basic_activityName", 'UI测试-营销赠送类活动999', timeout=30) # 输入活动开始时间
        time.sleep(2)
        self.type("#basic_promoRangeTime", "2024-12-11 00:00:00", timeout=30) # 输入活动开始时间
        self.type('//input[@placeholder="结束时间"]', "2040-12-11 00:00:00", timeout=30) # 输入活动结束时间
        self.click('//span[text()="确 定"]')# 时间确定
        time.sleep(2)
        self.click('#basic_activityBusiness')#活动业务方
        self.click('//div[text()="行业-国补业务方"]')
        time.sleep(2)
        #优惠系统规则================>>>>>>>>>>>>
        self.click('#basic_promoSystemRule_promoSystemType')#支付优惠营销系统
        self.click('//div[text()="支付自有营销系统"]')
        time.sleep(2)
        # 下滑到-优惠信息配置
        test1 = self.find_element("//h5[text()='优惠信息配置']")
        self.scroll_to_element(test1)
        time.sleep(1)
        #优惠信息配置================>>>>>>>>>>>>
        self.click('#basic_promoDisCountRule_activityScene')#优惠场景（营销场景）
        self.click('//div[text()="独立绑卡场景"]')
        time.sleep(2)
        self.click('//span[text()="赠支付券"]')
        self.type("#basic_promoDisCountRule_couponTemplates_0_template", "CT_4564668630773071", timeout=30)#劵模版
        time.sleep(2)
        self.type("#basic_promoDisCountRule_couponTemplates_0_count", "1", timeout=30)#固定立减xx元
        time.sleep(2)
        self.click('#basic_promoDisCountRule_fundSourceType')#优惠预算承担规则
        self.click('//div[text()="内部出资100%"]')
        time.sleep(2)
        self.type("#basic_promoDisCountRule_promoDesc", "营销测试文案", timeout=30)#营销文案（对客展示）
        time.sleep(2)
        #活动参与条件-业务场景规则================>>>>>>>>>>>>
        self.click('#basic_contributionRule_merchantId')#限定支付商户号
        self.click('//div[text()="测试业务6-testmerchant20180328000"]')
        time.sleep(2)
        self.click('//*[@id="basic"]/div[9]/div/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[1]/div')#限制可参与来源
        self.click('//div[text()="WITHDRAW_PAGE"]')
        time.sleep(2)
        # 下滑到-活动限频次规则
        test2 = self.find_element("//h5[text()='活动限频次规则']")
        self.scroll_to_element(test2)
        time.sleep(1)
        #活动限频次规则================>>>>>>>>>>>>
        self.click('//span[text()="uid限频次"]')
        self.type("#basic_accountIdTimes", "1", timeout=30)#营销文案（对客展示）
        time.sleep(2)
        self.click('//span[text()="uid限频次"]')
        self.type("#basic_team", "https://team.corp.kuaishou.com/task/test1111", timeout=30)#输入对应team
        time.sleep(2)
        #所有信息填写完成，确认新建营销活动
        self.click('//span[text()="确 认"]')
        time.sleep(3)
        #校验新建的活动，是否成功新建
        self.type("#activityName", "UI测试-营销立减类活动999", timeout=30) #输入对应活动名称
        self.click('//span[text()="查 询"]', timeout=30)  # 点击查询按钮
        time.sleep(3)
        # 2. 等待查询结果加载
        self.wait_for_element_visible('//td[text()="UI测试-营销立减类活动999"]', timeout=30)
        self.assert_element('//td[text()="UI测试-营销立减类活动999"]')  # 使用框架断言方法
        print('查询活动名称成功')

        #退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()




