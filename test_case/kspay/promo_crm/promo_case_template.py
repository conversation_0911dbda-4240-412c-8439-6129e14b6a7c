import time
from telnetlib import EC

from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from seleniumbase import BaseCase

"""支付营销系统模版代码，参考这个"""
class TestOperate(BaseCase):  # 继承自BaseCase框架

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # 初始化配置项
        cls.login_url = "https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage"
        cls.username = 'wb_zhang<PERSON><PERSON>@kuaishou.com'
        cls.password = '11193018Hui#'

    def load_urls(self):
        """页面加载通用逻辑"""
        self.open(self.login_url)
        self.maximize_window()
        self.wait_for_element_present('body', timeout=20)
        print("页面加载完成，开始登录流程")
        self.login_flow()
        self.handle_guides()

    def login_flow(self):
        """登录流程重构"""
        try:
            print("等待 iframe 元素可见")
            self.wait_for_element_visible("iframe[src*='sso.corp.kuaishou.com']", timeout=30)  # 增加等待时间
            print("切换到 iframe")
            self.switch_to_frame("iframe[src*='sso.corp.kuaishou.com']")  # 使用部分匹配
            self.wait_for_element_visible("#loginTabSso", timeout=20)
            self.click("#loginTabSso")  # 使用框架的点击方法
            self.type("#ssoUsername", self.username)  # 使用框架的输入方法
            self.type("#ssoPassword", self.password)
            self.click("#ssoSubmit")
            self.switch_to_default_content()
            print("登录成功")
        except Exception as e:
            print(f"登录失败: {e}")
            self.save_screenshot("login_error")
            raise e

    def handle_guides(self):
        """处理引导按钮"""
        guide_selectors = [
            ('div.guide1 button.button1', 5),
            ('div.guide2 button.button2', 5),
            ('div.guide3 button.button3', 5)
        ]
        for selector, wait_time in guide_selectors:
            self.wait_for_element_clickable(selector)
            self.click(selector)
            time.sleep(wait_time)

    # 滚动到页面底部
    def scroll_to_bottom(self):
        self.execute_script("window.scrollTo(0, document.body.scrollHeight);")

    # 滚动指定像素
    def scroll_down_by_pixel(self, pixels):
        self.execute_script(f"window.scrollBy(0, {pixels});")

    # 滚动到特定元素
    def scroll_to_elements(self, element):
        self.execute_script("arguments[0].scrollIntoView();", element)

    def test_case1(self):
        """支付运营平台——营销活动管理——查询活动名称"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 执行查询操作
            # print("营销活动页面源码" + self.get_page_source())  # 打印页面源码
            self.type("#activityName", "营销活动测试3")  # 使用框架的输入方法
            self.click('//span[text()="查 询"]')  # 支持XPath定位

            # 等待查询结果加载
            self.wait_for_element_visible('//td[text()="AC_C_20241231173315"]', timeout=30)  # 增加等待时间
            self.assert_element('//td[text()="AC_C_20241231173315"]')  # 使用框架断言方法
            print('查询活动名称成功')

            # 退出操作
            self.click('//div[text()="wb_zhangzhihui"]')
            self.click('//span[text()="退出"]')
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_case1_error.png")  # 自动截图
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()