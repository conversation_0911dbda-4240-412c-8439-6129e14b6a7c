import time
import random
import string
from unittest import skip

from seleniumbase import BaseCase



class TestOperate(BaseCase):  # 继承自BaseCase框架
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # 初始化配置项
        cls.login_url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/redPacketManage'
        cls.username = 'wb_<PERSON><PERSON><PERSON><PERSON>@kuaishou.com'
        cls.password = '11193018Hui#'

    def load_urls(self):
        """页面加载通用逻辑"""
        self.open(self.login_url)
        self.maximize_window()
        time.sleep(2)
        self.wait_for_element_present('body', timeout=30)
        print("页面加载完成，开始登录流程")
        self.login_flow()
        time.sleep(5)
        self.handle_guides()#处理按钮
        self.maximize_window()
        time.sleep(2)

    def login_flow(self):
        """登录流程重构"""
        print("等待 iframe 元素可见")
        self.wait_for_element_visible("iframe[src*='sso.corp.kuaishou.com']", timeout=30)  # 增加等待时间
        print("切换到 iframe")
        self.switch_to_frame("iframe[src*='sso.corp.kuaishou.com']")  # 使用部分匹配
        self.wait_for_element_visible("#loginTabSso", timeout=30)
        self.click("#loginTabSso")  # 使用框架的点击方法
        self.type("#ssoUsername", self.username)  # 使用框架的输入方法
        self.type("#ssoPassword", self.password)
        self.click("#ssoSubmit")
        self.switch_to_default_content()
        print("登录成功")

    def login_out(self):
        # 退出操作
        self.click('//div[text()="wb_zhangzhihui"]')  # 确认用户名正确
        time.sleep(2)
        self.click('//span[text()="退出"]')
        time.sleep(2)
        print('退出成功')

    def handle_guides(self):
        """处理引导按钮"""
        guide_selectors = [
            ('div.guide1 button.button1', 5),
            ('div.guide2 button.button2', 5),
            ('div.guide3 button.button3', 5)
        ]
        for selector, wait_time in guide_selectors:
            self.wait_for_element_clickable(selector)
            self.click(selector)
            time.sleep(wait_time)
    def scroll_to_element(self, element):#下滑到特定元素
        self.execute_script("arguments[0].scrollIntoView();", element)

    # 确保活动名称不重复，使用该方法
    def generate_combined_activity_name(self,length=5, prefix='activity_'):
        random_part = ''.join(random.choices(string.ascii_letters + string.digits, k=length))
        timestamp = int(time.time())
        activity_name = f"{prefix}{random_part}_{timestamp}"
        return activity_name

    def test_case1(self):
        """支付运营平台——营销活动管理——现金红包模版——红包模版名称查询"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版名称
        self.type("#templateName", "UI测试-现金红包test")
        time.sleep(2)
        # 点击查询按钮
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        # 检查红包模版名称是否正确
        self.assert_text('UI测试-现金红包test','//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[2]')
        time.sleep(2)
        print('红包模版名称正确')
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()



    def test_case2(self):
        """支付运营平台——营销活动管理——现金红包模版——红包模版编码查询"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版编码
        self.type("#templateCode", "CPT_20250311201041")
        time.sleep(2)
        # 点击查询按钮
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        # 检查红包模版编码是否正确
        self.assert_text('CPT_20250311201041','//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[1]')#判断红包模版编码是否正确
        print('红包模版编码正确')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    def test_case3(self):
        """支付运营平台——营销活动管理——现金红包模版——重置选项"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版名称
        self.type("#templateName", "UI测试-现金红包test")
        time.sleep(2)
        # 输入红包模版编码
        self.type("#templateCode", "CPT_20250311201041")
        time.sleep(2)
        # 点击查询按钮
        self.click('//span[text()="查 询"]')
        time.sleep(5)
        # 重置选项内容
        self.click('//span[text()="重 置"]')
        time.sleep(3)
        # 检查重置是否成功
        try:
            self.assert_text('CPT_20250311201041','#templateCode')  # 判断红包模版编码输入框是否有值
            print("重置失败")
        except Exception as e:
            print("重置成功", e)
        time.sleep(5)
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    @skip("111")
    def test_case4(self):
        """支付运营平台——营销活动管理——现金红包模版——新建红包模版/发权益时刻+固定天数"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        if not none:
            print('页面数据拉到了')
        else:
            print('页面数据没有拉到')
            self.refresh()  # 刷新页面
            time.sleep(5)
        time.sleep(2)
        # 新建红包模版
        self.click('//*[@id="root"]/div/div/article/div[2]/div[2]/button/a')
        time.sleep(3)
        # 生成一个随机名称的红包模版名称
        combined_activity_name = self.generate_combined_activity_name()
        self.type('#basic_basicInfo_templateName','UI测试-现金红包'+combined_activity_name)
        time.sleep(2)
        # 红包金额xx元
        self.type("#basic_basicInfo_amount", "5")
        time.sleep(3)
        # 发放到钱包
        self.type("#basic_basicInfo_accountGroupKey", "TEST_KUAISHOU_APP_DEMO")
        time.sleep(2)
        # 红包标题（对客展示）
        self.type("#basic_basicInfo_cashPacketName", "红包测试文案")
        time.sleep(2)
        # 红包规则简述（对客展示）
        self.type("#basic_basicInfo_withdrawBriefRule", "满50可以使用")
        time.sleep(2)
        # 红包规则详述（对客展示）
        self.type("#basic_basicInfo_withdrawCompleteRule", "小店下单，商品满50元及以上，即可使用该红包")
        time.sleep(2)
        # 有效期规则
        self.click("#basic_basicInfo_validDateType")
        self.click("//div[text()='发权益时刻+固定天数']")
        # 输入间隔数值
        self.type("#basic_basicInfo_validDateExpressDaily", "3")
        time.sleep(2)
        # 请选择时间单位
        self.click('//*[@id="basic"]/div[2]/div/div/div/div[8]/div[2]/div[2]/div/div/div/div/div/div[1]/span[1]')
        self.click("//div[text()='天']")
        time.sleep(2)
        # 确定按钮
        self.click("//span[text()='确 认']")
        time.sleep(5)
        # 输入随机生成的红包模版名称
        self.type("#templateName", combined_activity_name)
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 检查红包模版创建人是否正确
        self.assert_text('wb_zhangzhihui','//td[text()="wb_zhangzhihui"]')
        print('红包模版创建人正确')
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    @skip("111")
    def test_case5(self):
        """支付运营平台——营销活动管理——现金红包模版——新建红包模版/固定到期时间"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        if not none:
            print('页面数据拉到了')
        else:
            print('页面数据没有拉到')
            self.refresh()  # 刷新页面
            time.sleep(5)
        time.sleep(2)
        # 新建红包模版
        self.click('//*[@id="root"]/div/div/article/div[2]/div[2]/button/a')
        time.sleep(3)
        # 生成一个随机名称的红包模版名称
        combined_activity_name = self.generate_combined_activity_name()
        self.type('#basic_basicInfo_templateName','UI测试-现金红包'+combined_activity_name)
        time.sleep(2)
        # 红包金额xx元
        self.type("#basic_basicInfo_amount", "5")
        time.sleep(3)
        # 发放到钱包
        self.type("#basic_basicInfo_accountGroupKey", "TEST_KUAISHOU_APP_DEMO")
        time.sleep(3)
        # 红包标题（对客展示）
        self.type("#basic_basicInfo_cashPacketName", "红包测试文案")
        time.sleep(3)
        # 红包规则简述（对客展示）
        self.type("#basic_basicInfo_withdrawBriefRule", "满50可以使用")
        time.sleep(3)
        # 红包规则详述（对客展示）
        self.type("#basic_basicInfo_withdrawCompleteRule", "小店下单，商品满50元及以上，即可使用该红包")
        time.sleep(3)
        # 有效期规则
        self.click("#basic_basicInfo_validDateType")
        time.sleep(2)
        self.click("//div[text()='固定到期时间']")
        time.sleep(2)
        #选择到期时间
        self.type("#basic_basicInfo_validDateExpressTime", "2028-05-24 21:15:49")
        time.sleep(2)
        # 确定日期
        self.click("//span[text()='确 定']")
        #确认提交活动
        self.click("//span[text()='确 认']")
        time.sleep(5)
        # 输入随机生成的红包模版名称
        self.type("#templateName", combined_activity_name)
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 检查创建人是否正确
        self.assert_text('wb_zhangzhihui','//td[text()="wb_zhangzhihui"]')
        print('红包模版创建人正确')
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    def test_case6(self):
        """支付运营平台——营销活动管理——现金红包模版——进入红包模版详情"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版名称
        self.type("#templateName", "UI测试-现金红包test")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 查看详情页
        self.click("//a[text()='查看详情']")
        time.sleep(3)
        # 红包模版详情页标题是否正确
        self.assert_text('新建红包模版',"//div[text()='新建红包模版']")  # 判断红包模版名称是否正确
        print('进入红包模版详情页正常')
        time.sleep(5)
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    def test_case7(self):
        """支付运营平台——营销活动管理——现金红包模版——红包模版详情——红包基础信息 校验"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//dive[text()="暂无数据"]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版名称
        self.type("#templateName", "UI测试-现金红包test")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 查看详情页
        self.click("//a[text()='查看详情']")
        time.sleep(3)
        # 红包模版名称（仅内部看）校验
        self.assert_text('UI测试-现金红包test','#basic_basicInfo_templateName')
        print('红包模版名称正确')
        # 红包金额校验
        self.assert_text('10','#basic_basicInfo_amount')
        print('红包金额正确')
        # 发放到钱包校验
        self.assert_text('TEST_KUAISHOU_APP_DEMO','#basic_basicInfo_accountGroupKey')
        print('发放到钱包正确')
        # 红包标题（对客展示）校验
        self.assert_text('现金红包测试营销','#basic_basicInfo_cashPacketName')
        print('红包标题（对客展示）正确')
        # 红包规则简述（对客展示）校验
        self.assert_text('消费满50可享受此优惠','#basic_basicInfo_withdrawBriefRule')
        print('红包规则简述（对客展示）校验正确')
        # 红包规则详述（对客展示）校验
        self.assert_text('欢迎来消费','#basic_basicInfo_withdrawCompleteRule')
        print('红包规则详述（对客展示）校验校验正确')
        # 有效期规则1校验
        self.assert_text('发权益时刻+固定天数','//*[@id="basic"]/div[2]/div/div/div/div[8]/div[1]/div/div[2]/div/div/div/div[1]/span[2]')
        print('有效期规则1校验 正确')
        # 有效期规则2校验
        self.assert_text('10','#basic_basicInfo_validDateExpressDaily')
        print('有效期规则2校验 正确')
        # 有效期规则3校验
        self.assert_text('天','//*[@id="basic"]/div[2]/div/div/div/div[8]/div[2]/div[2]/div/div/div/div/div/div[1]/span[2]')
        print('有效期规则3校验 正确')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    def test_case8(self):
        """支付运营平台——营销活动管理——现金红包模版——红包模版详情——红包可提现的的用户画像条件 校验"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版名称
        self.type("#templateName", "UI测试-现金红包test")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 查看详情页
        self.click("//a[text()='查看详情']")
        time.sleep(3)
        # 下滑到-红包规则详述（对客展示）
        test = self.find_element('#basic_basicInfo_withdrawCompleteRule')
        self.scroll_to_element(test)
        time.sleep(1)
        # 用户画像 校验
        self.assert_text('小店-银行卡支付新用户','//*[@id="basic"]/div[4]/div[2]/div/div/div/div[1]/span[2]')
        print('用户画像校验 正确')
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()

    def test_case9(self):
        """支付运营平台——营销活动管理——现金红包模版——红包模版详情——修改 校验"""
        # 前置登录
        self.load_urls()
        time.sleep(5)
        # none = self.find_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td/div/div/div[2]')
        # if not none:
        #     print('页面数据拉到了')
        # else:
        #     print('页面数据没有拉到')
        #     self.refresh()  # 刷新页面
        #     time.sleep(5)
        # time.sleep(2)
        # 输入红包模版名称
        self.type("#templateName", "UI测试-现金红包test")
        time.sleep(2)
        # 点击查询按钮
        self.click("//span[text()='查 询']")
        time.sleep(5)
        # 修改
        self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/div/div[2]/a')
        time.sleep(3)
        # 红包模版详情页标题是否正确
        self.assert_text('新建红包模版',"//div[text()='新建红包模版']")  # 判断红包模版名称是否正确
        print('进入红包模版详情页正常')
        time.sleep(5)
        # 用户画像 校验
        # try:
        #     # 显式等待，直到输入框可用（可交互）
        #     input_box = WebselfWait(self, 10).until(
        #         EC.element_to_be_clickable((By.ID, "basic_basicInfo_templateName")) )
        #     # 清除输入框内容
        #     input_box.clear()
        #     # 你可以在这里添加其他操作，比如输入文本
        #     input_box.send_keys("UI测试-现金红包test1")
        #
        # except Exception as e:
        #     print('111')
        # time.sleep(3)
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清除cookie数据
        self.delete_all_cookies()
        self.driver.quit()
