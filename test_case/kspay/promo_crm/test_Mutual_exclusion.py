import random
import string
import time
from seleniumbase import BaseCase


class TestOperate(BaseCase):  # 继承自BaseCase框架
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # 初始化配置项
        cls.login_url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoGroup#/'
        cls.username = 'wb_<PERSON><PERSON><PERSON><PERSON>@kuaishou.com'
        cls.password = '11193018Hui#'

    def load_urls(self):
        """页面加载通用逻辑"""
        self.open(self.login_url)
        self.maximize_window()
        time.sleep(2)
        self.wait_for_element_present('body', timeout=30)
        print("页面加载完成，开始登录流程")
        self.login_flow()
        self.handle_guides()
        self.maximize_window()
        time.sleep(2)

    def login_flow(self):
        """登录流程重构"""
        print("等待 iframe 元素可见")
        self.wait_for_element_visible("iframe[src*='sso.corp.kuaishou.com']", timeout=30)  # 增加等待时间
        print("切换到 iframe")
        self.switch_to_frame("iframe[src*='sso.corp.kuaishou.com']")  # 使用部分匹配
        self.wait_for_element_visible("#loginTabSso", timeout=30)
        self.click("#loginTabSso")  # 使用框架的点击方法
        self.type("#ssoUsername", self.username)  # 使用框架的输入方法
        self.type("#ssoPassword", self.password)
        self.click("#ssoSubmit")
        self.switch_to_default_content()
        print("登录成功")

    def login_out(self):
        # 退出操作
        self.click('//div[text()="wb_zhangzhihui"]')  # 确认用户名正确
        time.sleep(2)
        self.click('//span[text()="退出"]')
        time.sleep(2)
        print('退出成功')
    def handle_guides(self):
        """处理引导按钮"""
        guide_selectors = [
            ('div.guide1 button.button1', 5),
            ('div.guide2 button.button2', 5),
            ('div.guide3 button.button3', 5)
        ]
        for selector, wait_time in guide_selectors:
            self.wait_for_element_clickable(selector)
            self.click(selector)
            time.sleep(wait_time)
    #页面滑动
    def scroll_to_element(self, element):#下滑到特定元素
        self.execute_script("arguments[0].scrollIntoView();", element)

    # 确保活动名称不重复，使用该方法
    def generate_combined_activity_name(self,length=5, prefix='activity_'):
        random_part = ''.join(random.choices(string.ascii_letters + string.digits, k=length))
        timestamp = int(time.time())
        activity_name = f"{prefix}{random_part}_{timestamp}"
        return activity_name



    def test_Voucher_template(self):
        """支付运营平台--新建互斥组"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #新建券模版按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[2]/button')
        time.sleep(5)
        #生成一个随机的互斥组名称
        combined_activity_name = self.generate_combined_activity_name()
        self.type('#basic_promoGroupConfigName','互斥组'+combined_activity_name)
        time.sleep(2)
        #互斥组活动
        self.type('#basic_excludePromoCodeListSingle_0_value','AC_C_20241231173315')
        time.sleep(2)
        self.type('#basic_excludePromoCodeListSingle_1_value','AC_C_20241231173315')
        time.sleep(2)
        # #互斥豁免活动
        # self.click('//*[@id="basic"]/div/div[1]/div/div[3]/div/div[2]/div/div/div/div/div/div/div/div[2]/a')
        # time.sleep(1)
        # self.type('#basic_freePromoCodeList_0_value','AC_C_20241231173315')
        # time.sleep(2)

        #确定按钮点击
        self.click('//*[@id="basic"]/div/div[2]/div/div[2]/div/div/div/div/button/span')
        time.sleep(5)

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()



    def test_The_name_of_the_exclusive_group(self):
        """支付运营平台--互斥组名称查询"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组名称输入
        self.type("#promoGroupConfigName", "互斥组test999", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        self.assert_element('//td[text()="互斥组test999"]')#判断券模版编码
        print('查询互斥组名称正确')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()


    def test_The_name_of_the_exclusive_groups(self):
        """支付运营平台--互斥组编码查询"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组编码输入
        self.type("#promoGroupConfigCode", "759225520780222464", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        self.assert_element('//td[text()="759225520780222464"]')
        print('查询互斥组编码正确')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()




    def test_The_name_of_the_exclusive_groupss(self):
        """支付运营平台--营销活动编码查询"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #营销活动编码输入
        self.type("#promoCode", "AC_C_20241231173315", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        self.assert_element('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/span')
        print('查询互斥组编码正确')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()



    def test_Exclusive_group_reset(self):
        """支付运营平台--互斥组管理--重置按钮"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组编码输入
        self.type("#promoGroupConfigCode", "759225520780222464", timeout=30)
        time.sleep(2)
        #互斥组名称输入
        self.type("#promoGroupConfigName", "互斥组test999", timeout=30)
        time.sleep(2)
        #营销活动编码输入
        self.type("#promoCode", "AC_C_20241231173315", timeout=30)
        time.sleep(2)
        # 执行重置
        self.click('//span[text()="重 置"]')
        time.sleep(4)
        aa = self.find_element('#promoGroupConfigName')
        bb = aa.get_attribute('value')
        cc = '互斥组test999'
        # 验证重置结果
        if not bb == cc:
            print('重置成功')
        else:
            raise '重置失败'

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()


    def test_inquire(self):
        """支付运营平台--互斥组管理--互斥组-查看"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组名称输入
        self.type("#promoGroupConfigName", "互斥组test999", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        #点击查看，进入详情页
        self.click('//a[text()="查看"]')
        time.sleep(2)
        aa = self.find_element('//input[@value="759225520780222464"]')
        bb = aa.get_attribute('value')
        cc = '759225520780222464'
        # 验证互斥组编码
        if bb == cc:
            print('数据正确')
        else:
            raise '数据错误'
        qq = self.find_element('//input[@value="互斥组test999"]')
        ww = qq.get_attribute('value')
        ee = '互斥组test999'
        # 验证互斥组名称
        if ww == ee:
            print('数据正确')
        else:
            raise '数据错误'
        rr = self.find_element('//input[@value="AC_C_20241231173315"]')
        tt = rr.get_attribute('value')
        yy = 'AC_C_20241231173315'
        # 验证互斥组名称
        if tt == yy:
            print('数据正确')
        else:
            raise '数据错误'
        #关闭互斥组详情弹窗
        self.click('//span[text()="关 闭"]')
        time.sleep(2)
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()


    def test_The_exclusive_group_is_modified(self):
        """支付运营平台--互斥组管理--互斥组-修改"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组名称输入
        self.type("#promoGroupConfigName", "互斥组test999", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        #点击查看，进入详情页
        self.click('//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[10]/div/div[2]/a')
        time.sleep(4)
        rr = self.find_element('//h5[text()="修改互斥组"]')
        if rr:
         print('进入修改互斥组页正常')

        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()



    def test_Operation_Log(self):
        """支付运营平台--互斥组管理--互斥组-操作日志"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组名称输入
        self.type("#promoGroupConfigName", "互斥组test999", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        #点击操作日志
        self.click('//a[text()="操作日志"]')
        time.sleep(4)
        rr = self.find_element('#rcDialogTitle0')
        if rr:
         print('本互斥组操作日志如下')
        self.click('//span[text()="关 闭"]')
        time.sleep(2)
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()



    def test_open(self):
        """支付运营平台--互斥组管理--互斥组-开启、删除"""
        # 前置登录流程
        self.load_urls()
        time.sleep(5)
        #互斥组名称输入
        self.type("#promoGroupConfigName", "互斥组test999", timeout=30)
        time.sleep(2)
        #查询按钮点击
        self.click('//*[@id="root"]/div/div/article/div[2]/div[1]/form/div/div[2]/div/div[1]/div/div/div/div/button/span')
        time.sleep(4)
        #开启互斥组
        self.click('//a[text()="开启"]')
        time.sleep(4)
        self.click('//span[text()="取 消"]')
        time.sleep(2)
        #删除互斥组
        self.click('//a[text()="删除"]')
        time.sleep(4)
        self.click('//span[text()="取 消"]')
        time.sleep(2)
        # 退出操作
        self.login_out()
        print('退出成功')
        # 清理浏览器cookies, 关闭浏览器
        self.delete_all_cookies()
        self.driver.quit()
