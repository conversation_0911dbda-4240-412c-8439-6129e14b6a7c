import time
from selenium import webdriver
from selenium.common import TimeoutException
from selenium.webdriver import Chrome
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def promo_manage_login_marketing(driver: Chrome, url: str) -> None:
    driver.get(url)
    time.sleep(2)
    driver.maximize_window()  # 浏览器最大化
    time.sleep(2)
    # 等待页面加载完成
    wait = WebDriverWait(driver, 20)
    # 打印页面源代码
    print(driver.page_source)
    # 检查页面加载状态
    if "Loading" in driver.page_source:
        time.sleep(5)  # 等待页面加载完成
    # 登录
    login(driver, wait, '<EMAIL>', '11193018Hui#')
    time.sleep(5)
    # 检查是否有 iframe
    iframes = driver.find_elements(By.TAG_NAME, 'iframe')
    if iframes:
        driver.switch_to.frame(iframes[0])
        # 再次确认是否成功切换
        assert driver.execute_script("return window.self !== window.top")
    time.sleep(5)
    # 等待页面加载完成并点击“NEXT”按钮
    click_next_button(driver)
    time.sleep(10)


def switch_to_iframe(driver, iframe_xpath):
    wait = WebDriverWait(driver, 30)
    iframe = wait.until(EC.presence_of_element_located((By.XPATH, iframe_xpath)))
    driver.switch_to.frame(iframe)


def login(driver, wait, email, password):
    # 切换到 iframe
    switch_to_iframe(driver, '//*[@id="sso"]//iframe')

    # 确保页面已经完全加载
    wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))

    # 打印当前页面的HTML内容
    logging.info("Current page HTML: \n%s", driver.page_source)

    # 切换到“账号密码”选项卡
    try:
        account_password_tab = wait.until(EC.element_to_be_clickable((By.ID, 'loginTabSso')))
        account_password_tab.click()
        time.sleep(1)
    except TimeoutException:
        logging.error("Account password tab not found")
        raise

    # 输入邮箱
    try:
        email_input = wait.until(EC.visibility_of_element_located((By.ID, 'ssoUsername')))
        email_input.send_keys(email)
        time.sleep(1)
    except TimeoutException:
        logging.error("Email input element not found")
        raise

    # 输入密码
    try:
        password_input = wait.until(EC.visibility_of_element_located((By.ID, 'ssoPassword')))
        password_input.send_keys(password)
        time.sleep(1)
    except TimeoutException:
        logging.error("Password input element not found")
        raise

    # 点击登录
    login_button = wait.until(EC.element_to_be_clickable((By.ID, 'ssoSubmit')))
    login_button.click()
    time.sleep(2)

    # 切换回主页面
    driver.switch_to.default_content()


def click_next_button(driver: webdriver.Chrome):
    # 等待页面加载完成
    wait = WebDriverWait(driver, 30)
    # 定位并点击顶部导航栏“NEXT”按钮
    wait.until(
        EC.presence_of_element_located((By.XPATH, '//div[@class="guide guide1"]//button[@class="button button1"]')))
    next_button = driver.find_element(By.XPATH, '//div[@class="guide guide1"]//button[@class="button button1"]')
    next_button.click()

    # 定位并点击侧边菜单栏“NEXT”按钮
    wait.until(
        EC.presence_of_element_located((By.XPATH, '//div[@class="guide guide2"]//button[@class="button button2"]')))
    next_button = driver.find_element(By.XPATH, '//div[@class="guide guide2"]//button[@class="button button2"]')
    next_button.click()

    # 定位并点击搜索框引导“开始使用”按钮
    wait.until(
        EC.presence_of_element_located((By.XPATH, '//div[@class="guide guide3"]//button[@class="button button3"]')))
    next_button = driver.find_element(By.XPATH, '//div[@class="guide guide3"]//button[@class="button button3"]')
    # next_button = driver.find_element(By.XPATH, '//div[@class="guide guide3"]//button[contains(text(), "开始使用")]')  # 也可以这样定位
    next_button.click()
    # 等待页面加载完成
    wait = WebDriverWait(driver, 20)


def find_and_fill_input(driver, xpath, value):
    wait = WebDriverWait(driver, 20)
    input_element = wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
    input_element.clear()  # 清除输入框中的内容
    input_element.send_keys(value)  # 输入值


def wait_for_page_load(driver, xpath):
    wait = WebDriverWait(driver, 30)
    try:
        title_element = wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
        logging.info("Element located successfully: %s", xpath)
        print("Page loaded successfully")
    except TimeoutException:
        logging.error("Element not found within the timeout period: %s", xpath)
        print("Page loading timed out")
        raise
