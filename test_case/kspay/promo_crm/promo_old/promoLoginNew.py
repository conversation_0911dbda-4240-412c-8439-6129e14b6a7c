import logging
import time

from seleniumbase import BaseCase

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class PromoManageLoginTest(BaseCase):
    def setUp(self):
        super().setUp()

    def test_promo_manage_login(self, url):
        self.open(url)
        self.maximize_window()  # 浏览器最大化
        time.sleep(2)

        # 打印页面源代码
        logging.info("Page source: \n%s", self.get_page_source())

        # 检查页面加载状态
        if "Loading" in self.get_page_source():
            time.sleep(5)  # 等待页面加载完成

        # 登录
        self.login('<EMAIL>', '11193018Hui#')
        time.sleep(5)

        # 检查是否有 iframe
        iframes = self.find_elements("iframe")
        if iframes:
            self.switch_to_frame(iframes[0])
            # 再次确认是否成功切换
            assert self.execute_script("return window.self !== window.top")
        time.sleep(10)

        # 等待页面加载完成并点击“NEXT”按钮
        self.click_next_button()
        time.sleep(10)

    # 确保活动名称不重复，使用时间戳
    def generate_unique_activity_name(self, base_name):
        timestamp = int(time.time())
        return f"{base_name}_{timestamp}"

    def login(self, email, password):
        # 切换到 iframe
        self.switch_to_frame('//*[@id="sso"]//iframe')

        # 打印当前页面的HTML内容
        logging.info("Current page HTML: \n%s", self.get_page_source())

        # 切换到“账号密码”选项卡
        try:
            self.click("#loginTabSso")
            time.sleep(1)
        except Exception as e:
            logging.error("Account password tab not found: %s", e)
            raise

        # 输入邮箱
        try:
            self.type("#ssoUsername", email)
            time.sleep(1)
        except Exception as e:
            logging.error("Email input element not found: %s", e)
            raise

        # 输入密码
        try:
            self.type("#ssoPassword", password)
            time.sleep(1)
        except Exception as e:
            logging.error("Password input element not found: %s", e)
            raise

        # 点击登录
        self.click("#ssoSubmit")
        time.sleep(2)

        # 切换回主页面
        self.switch_to_default_content()

    def click_next_button(self):
        # 等待页面加载完成
        self.wait_for_element_visible('//div[@class="guide guide1"]//button[@class="button button1"]')
        self.click('//div[@class="guide guide1"]//button[@class="button button1"]')

        # 定位并点击侧边菜单栏“NEXT”按钮
        self.wait_for_element_visible('//div[@class="guide guide2"]//button[@class="button button2"]')
        self.click('//div[@class="guide guide2"]//button[@class="button button2"]')

        # 定位并点击搜索框引导“开始使用”按钮
        self.wait_for_element_visible('//div[@class="guide guide3"]//button[@class="button button3"]')
        self.click('//div[@class="guide guide3"]//button[@class="button button3"]')

        # 等待页面加载完成
        self.wait_for_element_visible('//div[@class="guide guide3"]//button[@class="button button3"]')

    def find_and_fill_input(self, xpath, value):
        self.type(xpath, value)

    def wait_for_page_load(self, xpath):
        try:
            self.wait_for_element_visible(xpath)
            logging.info("Element located successfully: %s", xpath)
            print("Page loaded successfully")
        except Exception as e:
            logging.error("Element not found within the timeout period: %s", xpath)
            print("Page loading timed out")
            raise
