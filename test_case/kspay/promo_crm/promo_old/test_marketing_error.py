import time
import pytest
from selenium.webdriver.common.by import By
from seleniumbase import BaseCase
import logging
from test_case.kspay.promo_crm.promo_old.promoLoginNew import PromoManageLoginTest
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class TestMarketing(BaseCase):
    # @skip("111")
    @pytest.mark.P0
    def test_case1(self):
        """支付运营平台——营销活动管理——查询活动名称"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动名称xxx
        self.find_element(By.ID, 'activityName').send_keys('营销活动测试3')
        time.sleep(3)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查活动编码是否存在
        td = self.find_element(By.XPATH, "//td[text()='AC_C_20241231173315']")
        if td:
            td.click()
            time.sleep(1)
            print('查询活动名称成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case2(self):
        """支付运营平台——营销活动管理——活动编码查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动编码xxx
        self.find_element(By.ID, 'activityCode').send_keys('AC_C_20241231173315')
        time.sleep(3)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查活动编码是否存在
        td = self.find_element(By.XPATH, "//td[text()='AC_C_20241231173315']")
        if td:
            td.click()
            time.sleep(1)
            print('查询活动编码成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case3(self):
        """支付运营平台——营销活动管理——活动状态：待上线 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动状态
        self.find_element(By.ID,  'state').click()
        time.sleep(2)
        # 活动状态——待上线
        self.find_element(By.XPATH, "//div[text()='待上线']").click()
        time.sleep(2)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        self.quit()  # 关闭浏览器
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case4(self):
        """支付运营平台——营销活动管理——活动状态：已上线 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动状态
        self.find_element(By.ID, 'state').click()
        time.sleep(2)
        # 活动状态——已上线
        self.find_element(By.XPATH, "//div[text()='已上线']").click()
        time.sleep(2)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case5(self):
        """支付运营平台——营销活动管理——活动状态：已下线 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动状态
        self.find_element(By.ID, 'state').click()
        time.sleep(2)
        # 活动状态——已下线
        self.find_element(By.XPATH, "//div[text()='已下线']").click()
        time.sleep(2)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case6(self):
        """支付运营平台——营销活动管理——活动开始、结束日前查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动开始时间
        self.find_element(By.ID, 'timeRange').send_keys('2024-12-01 00:00:00')
        time.sleep(2)
        # 活动结束时间
        self.find_element(By.XPATH, '//input[@placeholder="结束时间"]').send_keys('2025-12-31 23:59:59')
        time.sleep(2)
        self.find_element(By.XPATH, "//span[text()='确 定']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case7(self):
        """支付运营平台——营销活动管理——优惠场景：收银台支付场景 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 请选择优惠场景
        self.find_element(By.ID,  'activityScene').click()
        time.sleep(2)
        # 收银台支付场景
        self.find_element(By.XPATH, "//div[text()='收银台支付场景']").click()
        time.sleep(2)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查收银台支付场景是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 收银台支付场景 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case8(self):
        """支付运营平台——营销活动管理——优惠场景：独立绑卡场景 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 请选择优惠场景
        self.find_element(By.ID,  'activityScene').click()
        time.sleep(2)
        # 独立绑卡场景
        self.find_element(By.XPATH, "//div[text()='独立绑卡场景']").click()
        time.sleep(2)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查独立绑卡场景景是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 独立绑卡场景 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case9(self):
        """支付运营平台——营销活动管理——优惠场景：直发/直领场景 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 请选择优惠场景
        self.find_element(By.ID,  'activityScene').click()
        time.sleep(1)
        # 直发/直领场景
        self.find_element(By.XPATH, "//div[text()='直发/直领场景']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查直发/直领场景是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[5]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 直发/直领场景 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case10(self):
        """支付运营平台——营销活动管理——优惠类型：立减类 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 请选择优惠类型
        self.find_element(By.ID, 'activityType').click()
        time.sleep(2)
        # 立减类
        self.find_element(By.XPATH, "//div[text()='立减类']").click()
        time.sleep(2)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查立减类是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[6]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 立减类 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case11(self):
        """支付运营平台——营销活动管理——优惠类型：送券类 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 请选择优惠类型
        self.find_element(By.ID, 'activityType').click()
        time.sleep(1)
        # 送券类
        self.find_element(By.XPATH, "//div[text()='送券类']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查送券类是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[6]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 送券类 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case12(self):
        """支付运营平台——营销活动管理——支付渠道：银行卡 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 选择支付渠道
        self.find_element(By.ID, 'provider').click()
        time.sleep(1)
        # 银行卡
        self.find_element(By.XPATH, "//div[text()='银行卡']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查银行卡是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 银行卡 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case13(self):
        """支付运营平台——营销活动管理——支付渠道：支付宝 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 选择支付渠道
        self.find_element(By.ID, 'provider').click()
        time.sleep(2)
        # 支付宝
        self.find_element(By.XPATH, "//div[text()='支付宝']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查支付宝是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 支付宝 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case14(self):
        """支付运营平台——营销活动管理——支付渠道：微信 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 选择支付渠道
        self.find_element(By.ID, 'provider').click()
        time.sleep(1)
        # 微信
        self.find_element(By.XPATH, "//div[text()='微信']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查微信是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 微信 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case15(self):
        """支付运营平台——营销活动管理——支付渠道：银联云闪付 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 选择支付渠道
        self.find_element(By.ID, 'provider').click()
        time.sleep(1)
        # 银联云闪付
        self.find_element(By.XPATH, "//div[text()='银联云闪付']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查银联云闪付是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 银联云闪付 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case16(self):
        """支付运营平台——营销活动管理——支付渠道：KSPAY_UNION 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 选择支付渠道
        self.find_element(By.ID, 'provider').click()
        time.sleep(1)
        # 快币KSPAY_UNION
        self.find_element(By.XPATH, "//div[text()='KSPAY_UNION']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case17(self):
        """支付运营平台——营销活动管理——运算承担规则：内部出资100% 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 预算承担规则
        self.find_element(By.ID, 'fundSourceType').click()
        time.sleep(1)
        # 内部出资100%
        self.find_element(By.XPATH, "//div[text()='内部出资100%']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查内部出资100%是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[7]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 内部出资100% 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case18(self):
        """支付运营平台——营销活动管理——运算承担规则：外部出资100% 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 预算承担规则
        self.find_element(By.ID, 'fundSourceType').click()
        time.sleep(1)
        # 外部出资100%
        self.find_element(By.XPATH, "//div[text()='外部出资100%']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查外部出资100%是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[7]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询 外部出资100% 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case19(self):
        """支付运营平台——营销活动管理——预算承担规则：内外联合出资-优先使用外部 查询"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 预算承担规则
        self.find_element(By.ID, 'fundSourceType').click()
        time.sleep(1)
        # 内外联合出资-优先使用外部
        self.find_element(By.XPATH, "//div[text()='内外联合出资-优先使用外部']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查 内外联合出资-优先使用外部 是否存在
        td = self.find_element(By.XPATH, "//span[text()='内外联合出资-优先使用外部']")
        if td:
            td.click()
            time.sleep(1)
            print('查询 内外联合出资-优先使用外部 成功')
        time.sleep(5)
        try:
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器

    @pytest.mark.p0
    def test_case20(self):
        """支付运营平台——营销活动管理——所有规则查询、重置"""
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage'
        # 前置登录
        promo_login = PromoManageLoginTest()
        promo_login.setUp()
        promo_login.test_promo_manage_login(url)
        promo_login.tearDown()
        time.sleep(2)
        # 活动名称xxx
        self.find_element(By.ID, 'activityName').send_keys('营销活动测试3')
        time.sleep(2)
        # 活动编码xxx
        self.find_element(By.ID, 'activityCode').send_keys('AC_C_20241231173315')
        time.sleep(2)
        # 活动状态
        self.find_element(By.ID, 'state').click()
        time.sleep(1)
        # 活动状态——待上线
        self.find_element(By.XPATH, "//div[text()='待上线']").click()
        time.sleep(1)
        # 活动开始时间
        self.find_element(By.ID, 'timeRange').send_keys('2024-12-01 00:00:00')
        time.sleep(2)
        # 活动结束时间
        self.find_element(By.XPATH, '//input[@placeholder="结束时间"]').send_keys('2025-12-31 23:59:59')
        time.sleep(3)
        # 确定
        self.find_element(By.XPATH, "//span[text()='确 定']").click()
        time.sleep(2)
        # 请选择优惠场景
        self.find_element(By.ID, 'activityScene').click()
        time.sleep(1)
        # 收银台支付场景
        self.find_element(By.XPATH, "//div[text()='收银台支付场景']").click()
        time.sleep(1)
        # 请选择优惠类型
        self.find_element(By.ID, 'activityType').click()
        time.sleep(1)
        # 立减类
        self.find_element(By.XPATH, "//div[text()='立减类']").click()
        time.sleep(1)
        # 选择支付渠道
        self.find_element(By.ID,  'provider').click()
        time.sleep(1)
        # 微信
        self.find_element(By.XPATH, "//div[text()='微信']").click()
        time.sleep(1)
        # 预算承担规则
        self.find_element(By.ID,  'fundSourceType').click()
        time.sleep(1)
        # 内部出资100%
        self.find_element(By.XPATH, "//div[text()='内部出资100%']").click()
        time.sleep(1)
        # 点击查询按钮
        self.find_element(By.XPATH, "//span[text()='查 询']").click()
        time.sleep(5)
        # 检查内部出资100%是否存在
        td = self.find_element(By.XPATH,
                                 '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[7]/span')
        if td:
            td.click()
            time.sleep(1)
            print('查询成功')
        time.sleep(5)
        # 重置所有规则
        self.find_element(By.XPATH, "//span[text()='重 置']").click()
        time.sleep(5)
        # 检查活动名称是否存在
        td = self.find_element(By.ID,  'activityName')
        if not td:
            td.click()
            time.sleep(1)
            print('未查询成功')
        time.sleep(5)
        try:  # 清楚账号cookie信息
            self.find_element(By.XPATH, "//div[text()='wb_zhangzhihui']").click()  # 替换为实际的 XPath
            time.sleep(3)
            self.find_element(By.XPATH, "//span[text()='退出']").click()
            time.sleep(3)
        except Exception as e:
            print("退出按钮未找到，可能已经退出或出现其他错误:", e)
        self.delete_all_cookies()
        time.sleep(3)
        self.quit()  # 关闭浏览器
