import time
from seleniumbase import BaseCase


class TestOperate(BaseCase):  # 继承自BaseCase框架
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # 初始化配置项
        cls.login_url = "https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage"
        cls.username = 'wb_<PERSON><PERSON><PERSON><PERSON>@kuaishou.com'
        cls.password = '11193018Hui#'

    def load_urls(self):
        """页面加载通用逻辑"""
        self.open(self.login_url)
        self.maximize_window()
        self.wait_for_element_present('body', timeout=20)
        print("页面加载完成，开始登录流程")
        self.login_flow()
        self.handle_guides()

    def login_flow(self):
        """登录流程重构"""
        try:
            print("等待 iframe 元素可见")
            self.wait_for_element_visible("iframe[src*='sso.corp.kuaishou.com']", timeout=30)  # 增加等待时间
            print("切换到 iframe")
            self.switch_to_frame("iframe[src*='sso.corp.kuaishou.com']")  # 使用部分匹配
            self.wait_for_element_visible("#loginTabSso", timeout=20)
            self.click("#loginTabSso")  # 使用框架的点击方法
            self.type("#ssoUsername", self.username)  # 使用框架的输入方法
            self.type("#ssoPassword", self.password)
            self.click("#ssoSubmit")
            self.switch_to_default_content()
            print("登录成功")
        except Exception as e:
            print(f"登录失败: {e}")
            self.save_screenshot("login_error")
            raise e
    def login_out(self):
        # 退出操作
        self.click('//div[text()="wb_zhangzhihui"]')  # 确认用户名正确
        self.click('//span[text()="退出"]')
        print('退出成功')
    def handle_guides(self):
        """处理引导按钮"""
        guide_selectors = [
            ('div.guide1 button.button1', 30),
            ('div.guide2 button.button2', 30),
            ('div.guide3 button.button3', 30)
        ]
        for selector, wait_time in guide_selectors:
            self.wait_for_element_clickable(selector)
            self.click(selector)
            time.sleep(wait_time)

    def test_case1(self):
        """支付运营平台——营销活动管理——查询活动名称"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 1. 执行查询操作
            # print("营销活动页面源码" + self.get_page_source())  # 打印页面源码
            self.type("#activityName", "营销活动测试3", timeout=30)  # 使用框架的输入方法
            self.click('//span[text()="查 询"]', timeout=30)  # 支持XPath定位

            # 2. 等待查询结果加载
            self.wait_for_element_visible('//td[text()="AC_C_20241231173315"]', timeout=30)  # 增加等待时间
            self.assert_element('//td[text()="AC_C_20241231173315"]')  # 使用框架断言方法
            print('查询活动名称成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case1_error.png")  # 自动截图
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()
    def test_case2(self):
        """支付运营平台——营销活动管理——活动编码查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()
            # 1. 输入活动编码
            self.type("#activityCode", "AC_C_20241231173315", timeout=30)  # 假设ID为activityCode对应的选择器是#activityCode
            self.click('//span[text()="查 询"]', timeout=30)

            # 2. 等待结果并断言
            self.wait_for_element_visible('//td[text()="AC_C_20241231173315"]', timeout=30)
            self.assert_element('//td[text()="AC_C_20241231173315"]')
            print('查询活动编码成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case2_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case3(self):
        """支付运营平台——营销活动管理——活动状态：待上线 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 选择活动状态
            self.click('#state', timeout=30)  # 使用框架的点击方法
            self.click("//div[text()='待上线']", timeout=30)

            # 执行查询操作
            self.click('//span[text()="查 询"]')

            # 验证查询结果 todo 目前元素定位不稳定，后续需要加上相关校验
            # self.wait_for_element_visible('//td[text()="待上线"]', 30)  # 状态文本验证<cite>[1]</cite>
            # self.assert_element('//td[contains(@class, "state")]/span[text()="待上线"]')
            # print('活动状态查询成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case3_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case4(self):
        """支付运营平台——营销活动管理——活动状态：已上线 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 1. 设置活动状态筛选条件
            self.click("#state", timeout=30)  # 更符合语义化的ID定位方式<cite>[8]</cite>
            self.click("//div[text()='已上线']", timeout=30)

            # 2. 执行查询操作
            self.click('//span[text()="查 询"]')

            # 标准化退出操作
            self.login_out()

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case4_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case5(self):
        """支付运营平台——营销活动管理——活动状态：已下线 查询"""
        try:
            self.load_urls()

            # 1.设置活动状态筛选条件
            self.click("#state")  # 更符合语义化的ID定位方式<cite>[8]</cite>
            self.click("//div[text()='已下线']")
            # 2. 执行查询操作
            self.click('//span[text()="查 询"]')

            self.login_out()

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case5_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case6(self):
        """支付运营平台——营销活动管理——活动开始、结束日前查询"""
        try:
            self.load_urls()

            # 输入活动开始时间
            self.type("#timeRange", "2024-12-01 00:00:00", timeout=30)
            # 输入活动结束时间
            self.type('//input[@placeholder="结束时间"]', '2025-12-31 23:59:59', timeout=30)
            # 点击确定按钮（时间选择器的确定按钮？）
            self.click("//span[text()='确 定']")
            # 点击查询按钮
            self.click("//span[text()='查 询']")
            print('按活动时间范围查询成功')
            # 退出操作
            self.login_out()

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case6_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case7(self):
        """支付运营平台——营销活动管理——优惠场景：收银台支付场景 查询"""
        try:
            self.load_urls()

            # 选择优惠场景
            self.click("#activityScene", timeout=30)  # 使用ID定位，对应原来的find_element(By.ID, 'activityScene').click()
            self.click("//div[text()='收银台支付场景']", timeout=30)
            # 点击查询按钮
            self.click('//span[text()="查 询"]')
            print('查询 收银台支付场景 成功')

            # 等待并验证结果  todo 目前元素定位不稳定，后续需要加上相关校验
            # self.wait_for_element_visible('//td[contains(text(), "收银台支付场景")]', timeout=30)
            # self.assert_element('//td[contains(text(), "收银台支付场景")]')
            # print('查询 收银台支付场景 成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case7_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case8(self):
        """支付运营平台——营销活动管理——优惠场景：独立绑卡场景 查询"""
        try:
            self.load_urls()

            # 选择优惠场景
            self.click("#activityScene", timeout=30)
            self.click("//div[text()='独立绑卡场景']", timeout=30)
            # 点击查询按钮
            self.click('//span[text()="查 询"]')
            print('查询 独立绑卡场景 成功')
            # 等待并验证结果 todo 目前元素定位不稳定，后续需要加上相关校验
            # self.wait_for_element_visible('//td[contains(text(), "独立绑卡场景")]', timeout=30)
            # self.assert_element('//td[contains(text(), "独立绑卡场景")]')
            # print('查询 独立绑卡场景 成功')
            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case8_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case9(self):
        """支付运营平台——营销活动管理——优惠场景：直发/直领场景 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 执行查询操作
            self.click("#activityScene", timeout=30)  # 打开优惠场景下拉框
            self.click("//div[text()='直发/直领场景']", timeout=30)  # 选择选项
            self.click('//span[text()="查 询"]')  # 点击查询
            print('查询 直发/直领场景 成功')

            # 等待并验证结果
            # self.wait_for_element_visible('//td[text()="直发/直领场景"]', timeout=30)
            # self.assert_element('//td[text()="直发/直领场景"]')
            # print('查询 直发/直领场景 成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case9_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case10(self):
        """支付运营平台——营销活动管理——优惠类型：立减类 查询"""
        # 初始化浏览器并加载页面
        self.load_urls()

        # 执行查询操作
        self.click("#activityType", timeout=30)  # 打开优惠类型下拉框
        self.click("//div[text()='立减类']", timeout=30)  # 选择选项
        self.click('//span[text()="查 询"]')  # 点击查询

        # 等待并验证结果
        self.wait_for_element_visible('//td[text()="立减类"]', timeout=30)
        self.assert_element('//td[text()="立减类"]')
        print('查询 立减类 成功')

        # 退出操作
        self.login_out()
        print('退出成功')

    def test_case11(self):
        """支付运营平台——营销活动管理——优惠类型：赠券 查询"""
        try:
            # 初始化浏览器并加载页面（集成登录逻辑）
            self.load_urls()

            # 选择优惠类型
            self.click("#activityType", timeout=30)  # 使用ID定位
            self.click("//div[text()='赠券']", timeout=30)

            # 点击查询
            self.click('//span[text()="查 询"]')
            print('查询 赠券 成功')

            # 等待结果并验证
            self.wait_for_element_visible('//td[contains(text(), "赠券")]', timeout=30)
            self.assert_element('//td[contains(text(), "赠券")]')
            print('查询赠券成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case11_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case12(self):
        """支付运营平台——营销活动管理——支付渠道：银行卡 查询"""
        try:
            self.load_urls()

            # 选择支付渠道
            # self.click("#provider")
            # self.wait_for_element_visible("//div[text()='银行卡']", timeout=30)
            # self.click("//div[text()='银行卡']")
            self.click("#provider", timeout=30)  # 使用ID定位
            self.click("//div[text()='银行卡']", timeout=30)

            # 点击查询
            self.click('//span[text()="查 询"]')
            print('查询 银行卡 成功')

            # 等待结果并验证
            self.wait_for_element_visible(
                '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span',
                timeout=30
            )
            self.assert_element('//*[contains(text(), "银行卡")]')  # 根据实际结果调整断言
            print('查询 银行卡 成功')
            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case12_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case13(self):
        """支付运营平台——营销活动管理——支付渠道：支付宝 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()
            # 选择支付渠道
            self.click("#provider", timeout=30)  # 更符合语义化的ID定位方式<cite>[8]</cite>
            self.click("//div[text()='支付宝']", timeout=30)
            # 点击查询按钮
            self.click('//span[text()="查 询"]')

            # 等待结果并断言支付宝存在
            self.wait_for_element_visible(
                '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span',
                timeout=30
            )
            self.assert_element('//*[contains(text(), "支付宝")]')  # 根据实际结果调整断言
            print('查询 支付宝 成功')
            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case13_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case14(self):
        """支付运营平台——营销活动管理——支付渠道：微信 查询"""
        try:
            self.load_urls()

            # 选择支付渠道
            self.click("#provider", timeout=30)  # 更符合语义化的ID定位方式<cite>[8]</cite>
            self.click("//div[text()='微信']", timeout=30)

            # 点击查询按钮
            self.click('//span[text()="查 询"]')

            # 等待结果并断言微信存在
            self.wait_for_element_visible(
                '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span',
                timeout=30
            )
            self.assert_element('//*[contains(text(), "微信")]')  # 根据实际结果调整断言
            print('查询 微信 成功')
            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case14_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case15(self):
        """支付运营平台——营销活动管理——支付渠道：银联云闪付 查询"""
        try:
            self.load_urls()

            # 选择支付渠道
            self.click("#provider", timeout=30)  # 更符合语义化的ID定位方式<cite>[8]</cite>
            self.click("//div[text()='银联云闪付']", timeout=30)

            # 点击查询按钮
            self.click('//span[text()="查 询"]')
            print('查询 银联云闪付 成功')
            # 等待结果并断言银联云闪付存在
            self.wait_for_element_visible(
                '//*[@id="root"]/div/div/article/div[2]/div[3]/div/div/div/div/div/div/table/tbody/tr[2]/td[8]/span',
                timeout=30
            )
            self.assert_element('//*[contains(text(), "银联云闪付")]')  # 根据实际结果调整断言
            print('银联云闪付渠道验证成功')
            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case15_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case16(self):
        """支付运营平台——营销活动管理——支付渠道：KSPAY 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 选择支付渠道
            # 选择支付渠道
            self.click("#provider", timeout=30)  # 更符合语义化的ID定位方式<cite>[8]</cite>
            self.click("//div[text()='KSPAY']", timeout=30)

            # 执行查询
            self.click('//span[text()="查 询"]')

            # 验证结果 todo 目前元素定位不稳定，后续需要加上相关校验
            # self.wait_for_element_visible(
            #     '//*[contains(text(), "KSPAY")]',  # 根据实际表格结构调整XPath
            #     timeout=30
            # )
            # self.assert_element('//*[contains(text(), "KSPAY")]')
            # print('查询 KSPAY 成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case16_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case17(self):
        """支付运营平台——营销活动管理——预算承担规则：内部出资100% 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 选择预算承担规则
            self.click("#fundSourceType", timeout=30)
            self.wait_for_element_visible("//div[text()='内部出资100%']", timeout=30)
            self.click("//div[text()='内部出资100%']")

            # 执行查询
            self.click('//span[text()="查 询"]')

            # 验证结果
            self.wait_for_element_visible(
                '//*[contains(text(), "内部出资100%")]',  # 根据实际表格结构调整XPath
                timeout=30
            )
            self.assert_element('//*[contains(text(), "内部出资100%")]')
            print('查询 内部出资100% 成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case17_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case18(self):
        """支付运营平台——营销活动管理——预算承担规则：外部出资100% 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 选择预算承担规则
            self.click("#fundSourceType", timeout=30)
            self.wait_for_element_visible("//div[text()='外部出资100%']", timeout=30)
            self.click("//div[text()='外部出资100%']")

            # 执行查询
            self.click('//span[text()="查 询"]')

            # 验证结果
            self.wait_for_element_visible(
                '//*[contains(text(), "外部出资100%")]',  # 根据实际表格结构调整XPath
                timeout=30
            )
            self.assert_element('//*[contains(text(), "外部出资100%")]')
            print('查询 外部出资100% 成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case18_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case19(self):
        """支付运营平台——营销活动管理——预算承担规则：内外联合出资-优先使用外部 查询"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 选择预算承担规则
            self.click("#fundSourceType", timeout=30)
            self.wait_for_element_visible("//div[text()='内外联合出资-优先使用外部']", timeout=30)
            self.click("//div[text()='内外联合出资-优先使用外部']")

            # 执行查询
            self.click('//span[text()="查 询"]')

            # 验证结果
            self.wait_for_element_visible(
                '//*[contains(text(), "内外联合出资-优先使用外部")]',  # 根据实际表格结构调整XPath
                timeout=30
            )
            self.assert_element('//*[contains(text(), "内外联合出资-优先使用外部")]')
            print('查询 内外联合出资-优先使用外部 成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case19_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()

    def test_case20(self):
        """支付运营平台——营销活动管理——所有规则查询、重置"""
        try:
            # 初始化浏览器并加载页面
            self.load_urls()

            # 填写查询条件
            self.type("#activityName", "营销活动测试3")
            self.type("#activityCode", "AC_C_20241231173315")

            # 选择活动状态
            self.click("#state")
            self.click("//div[text()='待上线']")

            # 设置时间范围
            self.type("#timeRange", "2024-12-01 00:00:00")
            self.type('//input[@placeholder="结束时间"]', "2025-12-31 23:59:59")
            self.click("//span[text()='确 定']")

            # 选择优惠场景
            self.click("#activityScene")
            self.click("//div[text()='收银台支付场景']")

            # 选择优惠类型
            self.click("#activityType")
            self.click("//div[text()='立减类']")

            # 选择支付渠道
            self.click("#provider")
            self.click("//div[text()='微信']")

            # 选择预算规则
            self.click("#fundSourceType")
            self.click("//div[text()='内部出资100%']")

            # 执行查询
            self.click('//span[text()="查 询"]')

            # 验证查询结果
            self.wait_for_element_visible(
                '//td[contains(text(),"AC_C_20241231173315")]',
                timeout=30
            )
            self.assert_element('//td[text()="待上线"]')
            print('复合查询成功')

            # 执行重置
            self.click('//span[text()="重 置"]')

            # 验证重置结果
            self.wait_for_element_invisible('//td[text()="AC_C_20241231173315"]', timeout=30)
            self.assert_element_not_present('//td[text()="AC_C_20241231173315"]')
            print('重置成功')

            # 退出操作
            self.login_out()
            print('退出成功')

        except Exception as e:
            self.save_screenshot("kspay_error_image/test_marketing/test_case20_error.png")
            raise e
        finally:
            self.delete_all_cookies()
            self.driver.quit()