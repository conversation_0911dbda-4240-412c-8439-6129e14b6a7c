# promo_utils_xpath.py

import json
import time
import pyautogui
import requests
from selenium import webdriver
from selenium.common import TimeoutException
from selenium.webdriver import Chrome
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from seleniumbase import BaseCase
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def get_options():
    chrome_options = Options()
    # chrome_options.add_argument('--headless')
    chrome_options.add_experimental_option("detach", True)
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])

    return chrome_options


class KspayXpathBaseCase(BaseCase):

    # 本地照片
    def load_file(path):
        time.sleep(1)
        pyautogui.click(700, 700)  # 鼠标点前端页面一下
        time.sleep(1)
        message_list = []
        for i in range(len(path)):
            message_list.append(path[i])

        pyautogui.press('a', _pause=False)
        pyautogui.press('backspace', _pause=False)
        pyautogui.press('backspace', _pause=False)
        pyautogui.press('backspace', _pause=False)
        time.sleep(1)
        for c in message_list:
            if len(c) > 1:
                c = c.lower()
            pyautogui.press(c, _pause=False)
            time.sleep(0.2)
            pyautogui.failSafeCheck()

        pyautogui.press('enter', 2)
        time.sleep(2)
        pyautogui.press('enter', 1)

    def load_urls(driver: Chrome, url: str, self=None) -> None:
        driver.get(url)
        # add_cookies(driver)
        # driver.get(url)
        time.sleep(2)
        driver.maximize_window()  # 浏览器最大化
        time.sleep(1)

    def add_cookies(driver):
        # 获取动态token信息
        uid = '2198403709'
        response = requests.post(
            url='https://staging-infra-admin.corp.kuaishou.com/token/create?userId=' + uid + '&sid=kuaishou.api')
        res = "{" + str(response.content)[1:-1].split(':1,')[1]
        res_map = json.loads(
            res)  # res_map :{kuaishou.api_ph : xx, ssecurity:xx, kuaishou.api.at:xx, tokenClientSalt:xx, kuaishou.api_st:xx, did:xxx}

        # 倒入cookie信息
        did = res_map.get('did')
        api_st = res_map.get('kuaishou.api_st')
        cookies_str = '_did=' + did + '; hdige2wqwoino=th5j4YFi77C7yWDxarT77QjNy8SQF46Md2dffa07; result=1; kpf=ANDROID_PHONE; c=TEST; language=zh-cn; countryCode=CN; did_tag=0; thermal=10000; app=0; android_os=0; newOc=TEST; slh=0; country_code=cn; hotfix_ver=; keyconfig_state=2; max_memory=256; oc=TEST; sh=2400; deviceBit=0; X-KTrace-Id-Enabled=0; is_background=0; sw=1080; ftt=; abi=arm64; cl=0; userRecoBit=0; device_abi=arm64; grant_browse_type=AUTHORIZED; iuid=; darkMode=false; __NSWJ=; net=WIFI; ktrace-context=1|MS44NDczNjc0NTc4Njk4ODg0LjQ3NTM0Njc1LjE3MDA2MzU2MzgxNTAuMjY4MTk2|MS44NDczNjc0NTc4Njk4ODg0LjYyNDY0NDg1LjE3MDA2MzU2MzgxNTAuMjY4MTk3|0|kfx-static-next|webservice|true|src-Js; earphoneMode=1; did=ANDROID_6616070cc3c2f921; ver=11.11; appver=11.11.10.99999; sys=ANDROID_13; mod=vivo%28V2304A%29; deviceName=vivo%28V2304A%29; isp=CMCC; oDid=ANDROID_6616070cc3c2f921; boardPlatform=kalama; androidApiLevel=33; nbh=126; cdid_tag=5; ddpi=480; socName=Unknown; totalMemory=15258; trace-context=; rdid=ANDROID_9409958d97e65a58; sbh=96; kpn=NEBULA; userId=2197688905; ud=2197688905; egid=DFP5B2ADF95B606AD73AD8C889E103EC06D604387F728F1082FE45907C639199; kcv=1545; bottom_navigation=true; did_gt=1701401491626; cold_launch_time_ms=1701847003054; browseType=3; apptype=22; kuaishou.api_st=' + api_st + '; token=Cg9rdWFpc2hvdS5hcGkuc3QSoAG_zFdQ6RsQuCX9M9jzj6cCXkIlnOft0cpk49wiyYUsjRHT1CQgMNesMLl2WoyP0Ypo-b_uHTQkfF1lILdejzb_DdUkx7FcOhplNihNbEgQKUNe27GIw26pVwRj8cBKS9W6OT6O4JG7ysgAEyVD3urbFfSrWQIsKCATHgTQLhKR6j--Jd0yxxPkv12lsrwETLnSTo6TJUI5zQ_wWy0yDuhuGhJ0Iffd11DQSKB-JV8Bvl49xBYiIJuCsfVgYJxVd-RKndRzPhypG1CEgGZrztbtNY8mXkkyKA8wAQ; client_key=2ac2a76d; kuaishou.h5_st=Cg5rdWFpc2hvdC5oNS5zdBKQAXqOeJ8EsWnHqsBYknWNGeUhk9aFOjrXisgUjs6pBBhD7sc017iOsy7U-4Q5VO10IhrCdLKnAwYHjVklTRYN7ASo7kOzrqTfJtLbB9taDRIPSc4eaXKehcnl37IWK305M46EptXif2gvLqQD6oREJRFjs6WcnhKA_bGuLPAMroXVXQcA-eAmaFvif7q8J1uExBoShmuA04kZhU2bR6RcTY6Lcsr6IiBYfIbA2AVlhdVJgZA9Q2JDTWVLLNp7AgxAOzGrh3msMSgPMAE; apdid=3ac1c508-c112-4cb4-9a35-93c4c815a3347c5b5382d8861e6f72f64b0d5d3c2efc:1701847075:1; sid=86b6e3ec-6c81-49d4-b27e-a92b1e42efa1; ehid=06GlGketQbuiq0-R7oqBn1k4EXiiDU8vxdGeZ'
        cookie = {'name': 'cookie_name', 'value': 'cookie_value'}
        driver.add_cookie(cookie)
        for cookie in cookies_str.split('; '):
            cookies_dict = {}
            cookies_dict['name'] = cookie.split('=')[0]
            cookies_dict['value'] = cookie.split('=')[-1]
            print(1, cookies_dict)
            driver.add_cookie(cookies_dict)
        # 导入泳道
        # driver.add_cookie({"name": "trace-context", "value": "%7B%22laneId%22%3A%22STAGING.bank_logo%22%7D"})

    def login(driver, wait, email, password):
        # 切换到 iframe
        iframe = wait.until(EC.presence_of_element_located((By.XPATH, '//iframe')))
        driver.switch_to.frame(iframe)

        # 输入邮箱
        email_input = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="ssoUsername"]')))
        email_input.send_keys(email)
        time.sleep(1)

        # 输入密码
        password_input = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@name="password"]')))
        password_input.send_keys(password)
        time.sleep(1)

        # 点击登录
        login_button = wait.until(EC.element_to_be_clickable((By.XPATH, '//*[@id="ssoSubmit"]')))
        login_button.click()
        time.sleep(2)

        # 切换回主页面
        driver.switch_to.default_content()

    def click_next_button(driver: webdriver.Chrome):
        # 等待页面加载完成
        wait = WebDriverWait(driver, 30)
        # 定位并点击顶部导航栏“NEXT”按钮
        wait.until(
            EC.presence_of_element_located((By.XPATH, '//div[@class="guide guide1"]//button[@class="button button1"]')))
        next_button = driver.find_element(By.XPATH, '//div[@class="guide guide1"]//button[@class="button button1"]')
        next_button.click()

        # 定位并点击侧边菜单栏“NEXT”按钮
        wait.until(
            EC.presence_of_element_located((By.XPATH, '//div[@class="guide guide2"]//button[@class="button button2"]')))
        next_button = driver.find_element(By.XPATH, '//div[@class="guide guide2"]//button[@class="button button2"]')
        next_button.click()

        # 定位并点击搜索框引导“开始使用”按钮
        wait.until(
            EC.presence_of_element_located((By.XPATH, '//div[@class="guide guide3"]//button[@class="button button3"]')))
        next_button = driver.find_element(By.XPATH, '//div[@class="guide guide3"]//button[@class="button button3"]')
        # next_button = driver.find_element(By.XPATH, '//div[@class="guide guide3"]//button[contains(text(), "开始使用")]')  # 也可以这样定位
        next_button.click()
        # 等待页面加载完成
        wait = WebDriverWait(driver, 20)

    def find_and_fill_input(driver, xpath, value):
        wait = WebDriverWait(driver, 20)
        input_element = wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
        input_element.clear()  # 清除输入框中的内容
        input_element.send_keys(value)  # 输入值

    def wait_for_page_load(driver, xpath):
        wait = WebDriverWait(driver, 30)
        try:
            title_element = wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
            logging.info("Element located successfully: %s", xpath)
            print("Page loaded successfully")
        except TimeoutException:
            logging.error("Element not found within the timeout period: %s", xpath)
            print("Page loading timed out")
            raise

    """进入营销活动管理首页"""

    def promo_manage_initialization(self):
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=get_options())
        url = 'https://kspay.staging.kuaishou.com/operation/fangzhou/promoActivity/activityManage/payActivityDetail'
        self.load_urls(driver, url)

        # 等待页面加载完成
        wait = WebDriverWait(driver, 20)

        # 检查页面加载状态
        if "Loading" in driver.page_source:
            time.sleep(5)  # 等待页面加载完成

        # 登录
        self.login(driver, wait, '<EMAIL>', '11193018Hui#')

        # 检查是否有 iframe
        iframes = driver.find_elements(By.TAG_NAME, 'iframe')
        if iframes:
            driver.switch_to.frame(iframes[0])
        # 等待页面加载完成并点击“NEXT”按钮
        self.click_next_button(driver)

        # 构建 XPath 表达式
        xpath = "//input[@class='el-input__inner' and @placeholder='请输入关键字']"
        xpath_title = "//h1[text()='营销活动管理']"
        # 定位到营销活动管理主页面
        self.find_and_fill_input(driver, xpath, "营销活动管理")
        # 等待主页面加载完成
        xpath_main = "//h5[@class='kspay-platform-promo-activity-manage-pc-typography']"
        self.wait_for_page_load(driver, xpath_main)

        print(driver.page_source)
