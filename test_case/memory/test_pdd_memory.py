import pytest
import time
from seleniumbase import BaseCase

BaseCase.main(__name__, __file__)
# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime


class TestPDDHome(BaseCase):

    # def setUp(self, **kwargs):
    #     super().setUp()
    #     self.maximize_window()

    @pytest.mark.p0
    def test_pdd_home(self):
        "https://mms.pinduoduo.com/home"
        "pdd-首页-工作台"
        self.maximize_window()

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 打开网页
        self.open("https://mms.pinduoduo.com/home")

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = ("INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
                      format("test_pdd_home", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)






