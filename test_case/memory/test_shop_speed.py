import pytest
import time
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime

BaseCase.main(__name__, __file__)


class TestShopSpeed(BaseCase):

    # def setUp(self, **kwargs):
    #     super().setUp()
    #     self.maximize_window()

    @pytest.mark.p0
    def test_shop_home(self):
        "s.kwaixiaodian.com/zone/home"
        "快手小店-首页-工作台"
        self.maximize_window()

        self.open("s.kwaixiaodian.com/zone/home")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_home", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_goods_list(self):
        "https://s.kwaixiaodian.com/zone/goods/v1/list"
        "快手小店-商品管理-商品"
        self.maximize_window()

        self.open("s.kwaixiaodian.com/zone/goods/v1/list")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_goods_list", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_goods_add(self):
        "https://s.kwaixiaodian.com/zone/goods/config/release/add"
        "快手小店-商品新增-商品"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/add")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_goods_add", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_goods_detail(self):
        "https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=23553629294008"
        "快手小店-商品详情-商品"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=23553629294008")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_goods_detail", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_goods_edit(self):
        "s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary"
        "快手小店-商品编辑-商品"

        self.open("https://s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary?itemId=23553629294008")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_goods_edit", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_order_list(self):
        "s.kwaixiaodian.com/zone/order/list"
        "快手小店-订单列表-正向"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/order/list")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_order_list", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_order_detail(self):
        "s.kwaixiaodian.com/zone/order/detail"
        "快手小店-订单详情-正向"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=****************")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_order_detail", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    # @pytest.mark.p0
    # def test_shop_refund_detail(self):
    #     "s.kwaixiaodian.com/zone/refund/detail"
    #     "快手小店-售后详情-逆向"
    #     self.maximize_window()
    #
    #     account_data = get_account_info("wb_huoyangyang")
    #     host = get_domain_by_env("DISTRIBUTION_DOMAIN", "online")
    #
    #     self.open(host)
    #     self.sleep(1)
    #     self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
    #     self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
    #     self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
    #     self.click("div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
    #     self.type("input[placeholder='请输入手机号']", account_data['account'])
    #     self.sleep(1)
    #     self.type("input[placeholder='请输入密码']", account_data['password'])
    #     self.click("button[type='button']")
    #     self.sleep(5)
    #
    #     self.open("https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=****************")
    #     start_time = int(time.time() * 1000)
    #     print("开始访问页面的时间：" + str(start_time))
    #     # 等待元素出现的时间戳
    #
    #     try:
    #         # 左上角图标
    #         self.wait_for_element_visible("(//img[@alt='logo'])[1]", timeout=10)  # 等待最多10秒
    #
    #         # 侧边栏
    #         self.wait_for_element_visible("(//div[@id='menu_item_R553YgpLnhQ'])[1]", timeout=10)  # 等待最多10秒
    #
    #         # 商品信息
    #         self.wait_for_element_visible("(//th[contains(text(),'商品')])[1]", timeout=10)  # 等待最多10秒
    #         element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳
    #
    #         # 订单编号
    #         self.wait_for_element_visible("(//span[contains(text(),'订单编号')])[1]", timeout=10)  # 等待最多10秒
    #         element_visible_time = int(time.time() * 1000)  # 记录元素可见时的时间戳
    #
    #         print("元素全部可见的时间戳：" + str(element_visible_time))
    #
    #         # 计算元素加载时间
    #         load_time = element_visible_time - start_time
    #         print(f"Element loaded in {load_time} milliseconds.")
    #     except Exception as e:
    #         print("Element is not visible or did not appear in time.")
    #         print(e)

    @pytest.mark.p0
    def test_shop_fund_account(self):
        "https://s.kwaixiaodian.com/zone/fund/accounting/overview"
        "快手小店-账户中心-资金结算"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/fund/accounting/overview")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_fund_account", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_fund_bonus(self):
        "https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus"
        "快手小店-资金提现-资金结算"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/fund/transfer/store/funds/bonus")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_fund_bonus", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_marketing_home(self):
        "https://s.kwaixiaodian.com/zone/marketing/tools/v2"
        "快手小店-营销中心-营销"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/marketing/tools/v2")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_marketing_home", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_marketing_tools(self):
        "https://s.kwaixiaodian.com/zone/marketing/tools/all-tools"
        "快手小店-营销工具-营销"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/marketing/tools/all-tools")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_marketing_tools", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_marketing_business(self):
        "https://s.kwaixiaodian.com/zone/business-activity/business/list"
        "快手小店-营销活动首页-营销"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/business-activity/business/list")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_marketing_business", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)





