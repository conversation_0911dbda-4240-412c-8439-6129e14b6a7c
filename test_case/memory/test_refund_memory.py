import pytest
import time
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
from utils.count import conn_mysql
from datetime import datetime

BaseCase.main(__name__, __file__)


class TestShopSpeed(BaseCase):

    # def setUp(self, **kwargs):
    #     super().setUp()
    #     self.maximize_window()

    @pytest.mark.p0
    def test_shop_refund_workbench(self):
        "s.kwaixiaodian.com/zone/refund/refund-workbench/index"
        "快手小店-售后工作台-逆向"
        self.maximize_window()

        self.open("s.kwaixiaodian.com/zone/refund/refund-workbench/index")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_refund_workbench", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)

    @pytest.mark.p0
    def test_shop_refund_detail(self):
        "s.kwaixiaodian.com/zone/refund/detail"
        "快手小店-售后详情-逆向"
        self.maximize_window()

        self.open("https://s.kwaixiaodian.com/zone/refund/detail?refundId=2419800025815513&refer=REFUND_LIST&effortTimestamp=1736753674182")

        # 启用性能监控
        self.execute_cdp_cmd('Performance.enable', {})

        # 等待页面加载
        time.sleep(10)

        # 获取性能数据
        performance_data = self.execute_cdp_cmd('Performance.getMetrics', {})
        js_heap_memory = None
        js_heap_total_memory = None

        # 从性能数据中提取 JavaScript 堆内存使用情况
        for metric in performance_data['metrics']:
            if metric['name'] == 'JSHeapUsedSize':
                js_heap_memory = metric['value']
            elif metric['name'] == 'JSHeapTotalSize':
                js_heap_total_memory = metric['value']

        # 输出 JavaScript 堆内存使用量
        print(f"JavaScript 堆内存使用量: {js_heap_memory} bytes")
        print(f"总的堆内存: {js_heap_total_memory} 字节")

        # 落库
        sql_insert = (
            "INSERT INTO pc_speed (case_name, run_speed, date, memory, tolal_memory) VALUES ('{}','{}','{}','{}','{}')".
            format("test_shop_refund_detail", 0, datetime.now(), js_heap_memory, js_heap_total_memory))
        print(sql_insert)
        # 执行结果写入db
        conn_mysql(sql_insert)