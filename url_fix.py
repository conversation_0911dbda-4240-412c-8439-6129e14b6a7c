import re

# pattern = re.compile(r'.*com(.*?)(\?|$)')  # 匹配最后一个com到问号或行尾

with open('pattern_log.log', 'r') as f_in, open('cleaned_regex.log', 'w') as f_out:
    for line in f_in:
        # 新增条件：如果行包含 login 则跳过
        if 'login' in line:
            continue

        # 原有处理逻辑
        processed = line.strip()
        processed = processed.split('.com')[-1]  # 删 com 前内容
        processed = processed.split('?')[0]  # 删 ? 后内容
        processed = processed.split(' ')[0]  # 删 and,
        f_out.write(processed + '\n')