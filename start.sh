#!/usr/bin/env bash

# shellcheck disable=SC2002
title=$(cat config/baseConfig.yaml | grep title | awk '{print $2}')
retry=$(cat config/baseConfig.yaml | grep retry | awk '{print $2}')
thread=$(cat config/baseConfig.yaml | grep thread | awk '{print $2}')
path=$(cat config/baseConfig.yaml | grep path | awk '{print $2}')
browser=$(cat config/baseConfig.yaml | grep browser | awk '{print $2}')
headless=$(cat config/baseConfig.yaml | grep headless | awk '{print $2}')
debug=$(cat config/baseConfig.yaml | grep debug | awk '{print $2}')

echo python3 -m pytest ${path} --html=report/${title}.html --reruns=${retry} -n=${thread}

# shellcheck disable=SC2086
if [ ${headless} = True ]; then
  # shellcheck disable=SC1009
  if [ ${debug} = True ]; then
    python3 -m pytest ${path} --html=report/${title}.html --reruns=${retry} -n=${thread} --browser=${browser} --headless --pdb
  else
    python3 -m pytest ${path} --html=report/${title}.html --reruns=${retry} -n=${thread} --browser=${browser} --headless
  fi
else
  if [ ${debug} = True ]; then
    python3 -m pytest ${path} --html=report/${title}.html --reruns=${retry} -n=${thread} --browser=${browser} --pdb
  else
    python3 -m pytest ${path} --html=report/${title}.html --reruns=${retry} -n=${thread} --browser=${browser}
  fi
fi