#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-08 11:44
 DESC :

 """
from seleniumbase.core import log_helper


# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain
from seleniumbase import BaseCase
from utils.config_help import get_config


import time

class BaseTestCase(BaseCase):

    def setUp(self, masterqa_mode=False):
        super(BaseTestCase, self).setUp()
        # width = self.driver.execute_script(
        #     "return Math.max(document.body.scrollWidth, document.body.offsetWidth, document.documentElement.clientWidth, document.documentElement.scrollWidth, document.documentElement.offsetWidth);")
        # height = self.driver.execute_script(
        #     "return Math.max(document.body.scrollHeight, document.body.offsetHeight, document.documentElement.clientHeight, document.documentElement.scrollHeight, document.documentElement.offsetHeight);")
        # self.driver.set_window_size(width + 100, height + 100)
        lineId = get_config("laneId")
        print("laneId",lineId)

    def login(self, domain, account):
        account_data = get_account_info("product_account")
        # 用户名 account_data['account']
        # 密码 account_data['password']
        host = get_domain(domain)
        self.open(host)
        time.sleep(3)
        self.click('//*[@id="root"]/div/div[2]/div/div/div/div[2]/div[1]')
        self.assert_text("扫码登录", "//*[text()='扫码登录']")  # div标签中的第一个元素
        self.assert_text("手机号登录", "//*[text()='手机号登录']")
        self.click("//*[text()='手机号登录']")
        self.type("input[placeholder='请输入手机号']", account_data['account'])
        self.type("input[placeholder='请输入密码']", account_data['password'])
        self.click("button[type='button']")
        time.sleep(5)
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dianpuxinxishezhi-eduguanli_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_xiaodiankefupingtai-lixiangongneng_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_dingdanchaxun-yinsijiami_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_dadanfahuo_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_tools-xiaoer-message_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_daifaguanli_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_tools-cs-center_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_goodsList_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('merchantSellerPCDriver_kwaishop-seller-micro-goods-list-pc_promotion_1724903200','1');")
        self.execute_script(
            "window.localStorage.setItem('workbench-dirver-modal','{\"1724903200\":{\"value\":1,\"maxAge\":0,\"createTime\":1668506846414}}');")

        #prt调试,后期要确认删除
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_33333332_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_11111111_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_111111113_1724903200','1');")
        self.execute_script("window.localStorage.setItem('merchantSellerPCDriver_111111112_1724903200','1');")

        self.refresh()
        time.sleep(2)

    def click_ocr(self,text):
        from selenium.webdriver.common.action_chains import ActionChains
        from utils.save_screenshot_help import recognize

        ss = self.driver.get_screenshot_as_png()
        top_left,bottom_right = recognize(ss,text,path=self.shotClass.path)
        x = (top_left[0] + ((bottom_right[0] - top_left[0])//2))//2
        y = (top_left[1] + ((bottom_right[1] - top_left[1])//2))//2
        print(x,y)
        action = ActionChains(self.driver)
        action.reset_actions()
        # action.move_by_offset(x, y).click()
        action.move_by_offset(x, y).click().perform()

    def click_js(self, jsStr,num=0):
        '''
        提前安装扩展，并保持开启
            https://chrome.google.com/webstore/detail/disable-content-security/ieelmcmcagommplceebfedjlakkhpden
        本地调试时需要在控制台执行以下命令:

            var jq = document.createElement('script');
            jq.src = "https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js";
            document.getElementsByTagName('head')[0].appendChild(jq);

        点击操作例如:
            var a = new Array();
            $("span:contains(删除)").filter(function(){
               if ($(this).text() == '删除') {
                    a.push(this)
                    }
            });
            a[0].click()

        :param jsStr:
        :param num:
        :return:
        '''

        print(f'''
        var a = new Array();
        $("span:contains({jsStr})").filter(function(){{        
           if ($(this).text() == '{jsStr}') {{
                a.push(this)
                }}
        }});
        a[{num}].click()
        
        ''')
        self.activate_jquery()
        self.execute_script(f'''
        var a = new Array();
        $("span:contains({jsStr})").filter(function(){{        
           if ($(this).text() == '{jsStr}') {{
                a.push(this)
                }}
            }});
            a[{num}].click()
    
    ''')  # Click elements on a page

    def tearDown(self):
        super(BaseTestCase, self).tearDown()
