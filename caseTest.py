import os
import subprocess
import re
from collections import defaultdict


def count_pytest_cases(directory, target_folder):
    test_case_count = defaultdict(int)
    processed_files = set()  # 用于跟踪已处理的文件

    # 获取 2024 年的 Git 提交记录
    git_log = subprocess.check_output(['git', 'log', '--pretty=format:%H %an', '--since=2024-01-01']).decode('utf-8')
    commits = git_log.splitlines()

    for commit in commits:
        commit_hash, author = commit.split(' ', 1)
        # 检查每个提交的变更
        diff_files = subprocess.check_output(
            ['git', 'diff-tree', '--no-commit-id', '--name-only', '-r', commit_hash]).decode(
            'utf-8').strip().splitlines()

        for file in diff_files:
            # 只处理指定文件夹下的 Python 文件
            if file.endswith('.py') and target_folder in file:
                file_path = os.path.abspath(os.path.join(directory, file))

                # 只处理未处理过的文件
                if file_path not in processed_files:
                    processed_files.add(file_path)  # 记录已处理的文件
                    print(f"Trying to open file: {file_path}")  # 输出文件路径

                    try:
                        with open(file_path, 'r') as f:
                            content = f.readlines()
                            for line in content:
                                if re.match(r'^\s*def\s+test_', line):
                                    test_case_count[author] += 1
                    except FileNotFoundError:
                        print(f"File not found: {file_path}")

    return test_case_count


# 设置代码库根目录和目标文件夹
directory_path = ''  # 确保这里是你的代码库路径
target_folder = 'test_case/distribution/leader/test_leader_rank.py'  # 指定要统计的文件夹

result = count_pytest_cases(directory_path, target_folder)

for author, count in result.items():
    print(f"{author}: {count} pytest cases")