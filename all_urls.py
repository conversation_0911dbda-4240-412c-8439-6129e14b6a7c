# from pathlib import Path
#
# # 1. 动态获取当前目录作为根目录（或替换为你的路径）
# root_dir = Path.cwd()
# print(f"正在扫描目录：{root_dir}")
#
# # 2. 查找文件并打印结果
# log_files = list(root_dir.rglob("*_urls.log"))
# print(f"找到 {len(log_files)} 个文件：")
# for file in log_files:
#     print(f" - {file}")
#
# # 3. 写入文件（含异常捕获）
# output_file = "output.log"
# try:
#     with open(output_file, "w", encoding="utf-8") as f:
#         for file in log_files:
#             f.write(f"{file.resolve()}\n")
#     print(f"文件已保存到：{Path(output_file).resolve()}")
# except Exception as e:
#     print(f"写入失败：{str(e)}")
#


from pathlib import Path

# 1. 定义根目录（替换为你的实际路径）
# 1. 动态获取当前目录作为根目录（或替换为你的路径）
root_dir = Path.cwd()
print(f"正在扫描目录：{root_dir}")
output_file = "output.log"

# 2. 查找所有符合条件的文件
log_files = root_dir.rglob("*_urls.log")

# 3. 读取内容并写入到输出文件
with open(output_file, "w", encoding="utf-8") as f_out:
    for file_path in log_files:
        try:
            with open(file_path, "r", encoding="utf-8") as f_in:
                content = f_in.read().strip()  # 读取内容并去掉首尾空行
                # 写入文件名作为分隔标识
                # f_out.write(f"\n\n===== 文件内容来自：{file_path} =====\n")
                f_out.write(content + "\n")
        except UnicodeDecodeError:
            print(f"警告：文件 {file_path} 编码不兼容，已跳过")
        except Exception as e:
            print(f"读取 {file_path} 失败：{str(e)}")

print(f"内容已合并保存到：{output_file}")