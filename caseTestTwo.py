import os
import subprocess
import re
from collections import defaultdict
from datetime import datetime


def get_last_committers(file_path):
    global test_case_count
    try:
        # 使用 git blame 命令
        result = subprocess.run(['git', 'blame', file_path], capture_output=True, text=True, check=True)

        # 解析输出
        lines = result.stdout.splitlines()
        committers = []

        for line in lines:
            # 每行的格式通常是 "commit_hash (author_name timestamp) line_content"
            commit_str = line.split('(')[1]

            # 获取作者信息
            author = commit_str.strip().split()[0]
            # 获取代码信息
            code_str = line.split(')')[1]

            # 判断时间区间
            time_str = commit_str.strip().split()[1]
            date_to_check = datetime.strptime(time_str, "%Y-%m-%d")
            specified_date = datetime.strptime('2024-01-01', "%Y-%m-%d")
            if date_to_check > specified_date:
                if author not in test_case_count:
                    test_case_count[author] = []
                match = re.match(r'^\s*def\s+(test_\w+)', code_str)
                if match:
                    function_name = match.group(1)
                    test_case_count[author].append(function_name)

        return

    except Exception as e:
        print("问题文件: " + file_path)
        print(e)
        return


if __name__ == "__main__":
    test_case_count = {}
    for root, dirs, files in os.walk('/Users/<USER>/pcUI/test_case/distribution'):
        for file in files:
            if file.endswith('.py') and file != '__init__.py':
                tofile = os.path.join(root, file)
                get_last_committers(tofile)

    # 调试用
    # file_path = '/Users/<USER>/Desktop/code/kwaishopuiautotest/test_case/distribution/distribute_promter/test_promter_Effect_Board.py'  # 替换为你的文件路径
    # get_last_committers(file_path)

    for author, functions in test_case_count.items():
        print(f"{author}: {len(functions)} unique pytest cases")
