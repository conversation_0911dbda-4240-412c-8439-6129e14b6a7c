import requests
import json
from datetime import datetime, timedelta


# 将业务域名称映射成应用列表
name_mapping_application = {
    "商家运营平台&策略": [  # 15个
        "kwaishop-enrichment-operation-center",
        "kwaishop-merchant-operation-core-center",
        "kwaishop-sellergrowth-orion-service",
        "kwaishop-merchant-interest-center",
        "kwaishop-merchant-operation-center",
        "kwaishop-merchant-growth-center",
        "kwaishop-merchant-growth-service",
        "kwaishop-kp-hades-center",
        "kwaishop-merchant-muses-center",
        "kwaishop-kp-metis-center",
        "kwaishop-kp-major-center",
        "kwaishop-spplatform-admin",
        "kwaishop-merchant-trinity-center",
        "kwaishop-spplatform-notify",
        "kwaishop-merchant-strategy-center",
        "kwaishop-merchant-growth-center",
        "kwaishop-tianhe-galax-center"
    ],
    "治理": [  # 17个
        "kwaishop-themis-mirror-center",
        "kwaishop-themis-guard-center",
        "kwaishop-apollo-pinocchio-center",
        "kwaishop-themis-action-center",
        "kwaishop-themis-admin-center",
        "kwaishop-themis-rulehub",
        "kwaishop-themis-judge-center",
        "kwaishop-apollo-hera-center",
        "kwaishop-themis-totoro-center",
        "kwaishop-themis-tools-center",
        "kwaishop-themis-rightprotect-center",
        "kwaishop-themis-punishhub-center",
        "kwaishop-ecology-compensation-center",
        "kwaishop-themis-acceptor-center",
        "kwaishop-apollo-strategy-center",
        "kwaishop-themis-data-center",
        "kwaishop-staff-workbench-service",
        "themis-mirror-center",
        "kuaishou-audit-themis"
    ],
    "数据": [  # 10个
        "kwaishop-kdump-caelus-center",
        "kwaishop-sellerdata-dashboard-service",
        "kwaishop-sellerdata-management-service",
        "kwaishop-sellerdata-operation-service",
        "kwaishop-sellerdata-management-task",
        "kwaishop-sellerdata-syt-api",
        "kwaishop-sellerdata-orchestration-service",
        "kwaishop-sellerdata-databank-service",
        "kwaishop-sellerdata-syt-service",
        "kwaishop-ksearch-admin-service"
    ],
    "店铺": [  # 5个
        "kwaishop-shop-api",
        "kwaishop-material-center",
        "kwaishop-shop-center",
        "kwaishop-shop-combine",
        "kwaishop-shop-page-service",
        "kwaishop-biz-account-center"
    ],
    "达人工作台": [  # 2
        "kwaishop-merchant-invite-center",
        "kwaishop-merchant-invite-service"
    ],
    "客服": [  # 12个
        "kwaishop-message-center",
        "kwaishop-cs-intelligence",
        "kwaishop-cs-datawarehouse-center",
        "Kuaishou Merchant Customer Service",
        "kwaishop-cs-intelligence-center",
        "kwaishop-cs-b-api",
        "kwaishop-cs-scene-service",
        "kwaishop-cs-core-center",
        "kwaishop-cs-c-api",
        "kwaishop-cs-event-service",
        "kwaishop-cs-common-parent",
        "kwaishop-cs-platform-service",
        "kwaishop-cs-biz"
    ],
    "分销": [  # 23
        "kwaishop-merchant-ares-center",
        "kwaishop-distribute-operation-admin",
        "kwaishop-distribute-backend-server",
        "kwaishop-distribute-promotion-service",
        "kwaishop-distribute-user-center",
        "kwaishop-distribute-pontus-service",
        "kwaishop-distribute-center",
        "kwaishop-distribute-timed-task",
        "kwaishop-distribute-search-service",
        "kwaishop-distribute-message-task",
        "kwaishop-distribute-match-service",
        "kwaishop-distribute-commission-center",
        "kwaishop-distribute-data-service",
        "kwaishop-distribute-seller-api",
        "kwaishop-distribute-selection-api",
        "kwaishop-distribute-order-service",
        "kwaishop-distribute-gateway-server",
        "kwaishop-distribute-common",
        "kwaishop-distribute-shelf",
        "kwaishop-distribute-selection-server",
        "kwaishop-distribute-pid",
        "kwaishop-distribute-kwaimoney-admin",
        "kwaishop-distribute-seller-server"
    ],
    "招商": [  # 8
       "kwaishop-merchant-leads-center",
        "kwaishop-merchant-leads-service",
        "kwaishop-merchant-janus-center",
        "kwaishop-merchant-terra-center",
        "kwaishop-spplatform-workbench",
        "kwaishop-merchant-hestia-api",
        "kwaishop-merchant-hestia-admin-api",
        "kwaishop-merchant-domain"
    ],
    "工作台": [  # 3
        "kwaishop-workbench-config-center",
        "ks-access-control",
        "kwaishop-workbench-center"
    ],
    "资金": [  # 7
        "kwaishop-funds-platform-center",
        "kwaishop-funds-account",
        "kwaishop-funds-flow",
        "kwaishop-funds-right",
        "kuaishou-merchant-funds-center",
        "kuaishou-merchant-financial-center",
        "kuaishou-merchant-funds-invoice"
    ],
    "私域": [  # 2个
        "kwaishop-merchant-member-center",
        "kwaishop-merchant-selfoperation-center",
        "kwaishop-llm-engine",
        "kwaishop-flow-reservation",
        "kwaishop-live-assistant"
    ],
}


# 获取当前时间的日期
def get_no_pass(robot: str):
    """
    :param application: 输入mapping映射的key
    :param people:兜底人  eg:huangtengjie
    :param robot：群聊机器人的url
    :return:
    """
    # 获取当前日期
    today = datetime.now()
    print(today)
    # 获取7天前的日期
    yesterday = today - timedelta(days=7)

    # 将时间转换为时间戳，乘以1000转换为毫秒
    start = int(yesterday.replace(hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000)
    end = int(today.timestamp() * 1000)

    url = "https://qa-itest.corp.kuaishou.com/rest/api/accuracy/v1/export/report/data/page/list"  # post请求精准测试平台
    payload = json.dumps({
        "biz": "商家",
        "start": start,
        "end": end,
        "page": 1,
        "pageSize": 150,
        "conclusion": "未通过",
        "isReleaseBranch": "false",
        "userName": "huangtengjie",
        # "testType": "1"
    })

    header = {
      'Content-Type': 'application/json'
    }
    res = requests.post(url, headers=header, data=payload)
    res_json = json.loads(res.text)
    print(res_json["data"])
    last_data = res_json["data"]['data']#列表数据
    require_data=[#在last_data 里面提取出来需要的字段
        {
            'biz': item.get('biz', ''),
            'application': item.get('application', ''),
            'branch': item.get('branch', ''),
            'requireName': item.get('requireName', ''),
            'members': item.get('members', '').split(',') if item.get('members') else [],  # 将 members 按照 ',' 分割为列表
            'reportUrl':item.get('reportUrl')

        }
        for item in last_data
    ]  # 这个列表的每个字典放着branch，requireName，members
    print("re", require_data)
    total = len(require_data)
    # print(len(require_data))
    number = 0
    warn = ""
    for item in require_data:
        relevant_people = ''
        if len(item['members']) > 0:
            for p in item['members']:
                relevant_people += f'<@=username({p})=>'
        if len(relevant_people) > 0:
            number += 1
            warn += f"第{number}条未通过详情如下:\n业务域：{item['biz']}\n应用：{item['application']}\n分支：{item['branch']}\n需求名称:{item['requireName']}\n参与者:{relevant_people}\n报告链接：{item['reportUrl']}\n\n"
    warn = f"本周 {number} 条数据未通过，辛苦大佬们确认\n"+warn
    payload = json.dumps({
        "msgtype": "text",
        "text": {
           "content":  f'{warn}'
        }
    })
    send_url = robot
    if number > 0:
        print(warn)
        re = requests.post(url=send_url, headers=header, data=payload)
        print(1)
        print(re)
    else:
        print("无未通过")


if __name__ == '__main__':
    # # 客服
    # get_no_pass(name_mapping_application['客服'],"litianning",'https://kim-robot.kwaitalk.com/api/robot/send?key=1c5b4108-d57d-49e5-bf5c-044f770f90aa')
    # # 店铺
    # get_no_pass(name_mapping_application['店铺'],"xieyi",'https://kim-robot.kwaitalk.com/api/robot/send?key=1c5b4108-d57d-49e5-bf5c-044f770f90aa')
    # # get_no_pass(name_mapping_application["商家运营平台&策略"],"litianning",'https://kim-robot.kwaitalk.com/api/robot/send?key=8fdeee00-9bb9-4de2-bae7-a0833eea5759')#huangtengjie的机器人，调试
    # get_no_pass(name_mapping_application["分销"], "liuxiaohui07",'https://kim-robot.kwaitalk.com/api/robot/send?key=7a0b90db-cb36-44ee-8cec-fea17fca077f')
    # get_no_pass(name_mapping_application["数据"], "zhanle",'https://kim-robot.kwaitalk.com/api/robot/send?key=aa71da28-35e9-4952-9d69-d2a968c551a7')
    # get_no_pass(name_mapping_application["治理"], "taoheliang",'https://kim-robot.kwaitalk.com/api/robot/send?key=c0b3e2cb-3779-43ef-a84c-d2337f022141')
    # get_no_pass(name_mapping_application["资金"]+name_mapping_application["私域"]+name_mapping_application["工作台"], "rongjunqiao",'https://kim-robot.kwaitalk.com/api/robot/send?key=5951e7a6-28c5-478b-8745-02f239d3b7f8')
    # get_no_pass(name_mapping_application["商家运营平台&策略"]+name_mapping_application["达人工作台"], "zhanghongbin",'https://kim-robot.kwaitalk.com/api/robot/send?key=8b5bccce-1bcd-4b15-b1f3-e04b2262ab60')
    # get_no_pass(name_mapping_application["招商"], "xuanmanlin",'https://kim-robot.kwaitalk.com/api/robot/send?key=a83b6874-2fa4-4851-ac46-467663737da5')
    get_no_pass('https://kim-robot.kwaitalk.com/api/robot/send?key=06f3d4d4-f952-4fcc-b4b2-d498bcdb5590')


