import logging
import time

from stream_chat_service_pb2 import ChatCompletionRequest, Message
from stream_chat_service_pb2_grpc import KwaishopLangBridgeModelServiceStub
from kess.framework import ClientOption, GrpcClient

logger = logging.getLogger(__name__)
fmt_str = ('%(asctime)s.%(msecs)03d %(levelname)7s '
           '[%(thread)d][%(process)d] %(message)s')
fmt = logging.Formatter(fmt_str, datefmt='%H:%M:%S')
handler = logging.StreamHandler()
handler.setFormatter(fmt)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)


def async_hello(grpc_client: GrpcClient, content: str, timeout: float):

    try:
        messages = [Message(role="user", content=content)]
        req = ChatCompletionRequest(messages=messages, model="gpt-4o", biz_key="***", operator="***")
        resp = grpc_client.StreamChatCompletion(req, timeout=timeout)
        for chunk in resp:
            logger.info('%s', chunk)
    except Exception as e:
        logger.error('发生异常, err: %s', e)


if __name__ == "__main__":

    client_option = ClientOption(
        biz_def='MERCHANT',
        grpc_service_name='kwaishop-lang-bridge',
        grpc_stub_class=KwaishopLangBridgeModelServiceStub,
    )

    client = GrpcClient(client_option)
    while True:
        async_hello(client, 'async', 1.5)
        time.sleep(1)
    logger.info('88,good luck')