import os

import yaml

_work_dir = os.path.abspath(os.curdir)

if "/" in _work_dir:
    dritype = "/"
    print("mac", dritype)

else:
    dritype = "\\"
    print("win", dritype)


def get_config(key):
    if os.environ.get(key):
        return os.environ.get(key)
    with open("{}{}config{}baseConfig.yaml".format(_work_dir, dritype, dritype), encoding='utf-8') as f:
        print("{}{}config{}baseConfig.yaml".format(_work_dir, dritype, dritype))
        config_data = yaml.safe_load(f)
    try:
        value = config_data[key].lower()
        os.environ[key] = value
    except Exception as ex:
        value = None
    return value


if __name__ == '__main__':
    s = get_config("laneId")
    print(s)
