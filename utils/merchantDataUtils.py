import re
from datetime import datetime

def extract_total_count(text):
    """
    从字符串中提取总条数。

    :param text: 包含总条数的字符串，格式为 "1-10 共 25 条"/总计 XX 条"
    :return: 提取的总条数（整数）
    """
    # 使用正则表达式匹配 "共 XX 条" 中的数字
    match = re.search(r'(共|总计) (\d+) 条', text)

    if match:
        # 提取匹配的数字并转换为整数
        total_count = int(match.group(2))
        return total_count
    else:
        raise ValueError("字符串格式不正确，无法提取总条数")


def convert_to_float(text):
    """
    将形如 '$1,457.3'的字符串转换为float。

    :param text: 需要转换的字符串
    :return: 转换后的整数
    """
    # 使用正则表达式去掉非数字和小数点的符号
    cleaned_text = re.sub(r'[^\d.]+', '', text)
    if cleaned_text == '':
        #说明不含数字
        return 0
    parts = cleaned_text.split(',')
    # 将所有部分连接成一个完整的字符串
    full_number_str = ''.join(parts)
    # 将字符串转换为整数
    number = float(full_number_str)
    return number

def compare_dates(date1, date2):
    """
    比较两个日期时间字符串。
    :param date1: 第一个日期时间字符串，格式为 'YYYY-MM-DD HH:MM:SS'
    :param date2: 第二个日期时间字符串，格式为 'YYYY-MM-DD HH:MM:SS'
    :return: -1 如果 date1 早于 date2, 0 如果 date1 == date2, 1 如果 date1 晚于 date2
    """
    # 定义日期时间格式
    date_format = '%Y-%m-%d %H:%M:%S'

    # 解析日期时间字符串
    dt1 = datetime.strptime(date1, date_format)
    dt2 = datetime.strptime(date2, date_format)

    # 比较日期时间
    if dt1 < dt2:
        return -1
    elif dt1 == dt2:
        return 0
    else:
        return 1


