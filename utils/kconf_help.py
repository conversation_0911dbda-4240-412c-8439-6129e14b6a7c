from requests import request
from requests.exceptions import HTTPError


def httpdo(method, url, **kwargs):
    r = request(method, url, **kwargs)
    if r.status_code != 200:
        raise HTTPError(f'HTTP error occurred: {r.status_code} - {r.reason}')
    res = r.json()
    return res


def get_kconf(env_name, conf_key):
    url = "https://video-grpc.corp.kuaishou.com/" + env_name + '/kconf/get?conf_key=' + conf_key
    res = httpdo("get", url, timeout=30)
    return res.get('data')


# 获取kconf的配置
def get_staging_kconf(kconfName):
    return get_kconf(env_name="staging", conf_key=kconfName)


if __name__ == '__main__':
    tail_number_t = get_staging_kconf('kwaishop.kwaishop-qa-manufacture-service.AftersalesUIAutoTestConfig')
    print(isinstance(tail_number_t, dict))
    print(tail_number_t)
