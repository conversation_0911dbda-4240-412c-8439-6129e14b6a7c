import json
import requests

from constant import domain
from utils.env_help import get_env

url = domain.get_domain("DATAFACTORY_DOMAIN") + "/rest/api/manufacture/getAccountList"

if get_env() == "staging":
    env = "staging"
else:
    env = "online"


def get_account_detail(uid):
    payload = json.dumps({
        "pageNum": 1,
        "pageSize": 10,
        "orderBy": "update_time desc",
        "env": env,
        "uid": uid
    })
    headers = {
        'Content-Type': 'application/json',
        'Cookie': 'apdid=36b60caa-b71f-4617-b57c-85b50a403512daa8a01b2ce65d48f5849f79a734bae1:**********:1; accessproxy_session=2a5d9ec9-202a-46d1-a858-ae9c53e4093f'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    a = json.loads(response.text)
    return a['data']['accounts'][0]


if __name__ == "__main__":
    print(get_account_detail(**********))
