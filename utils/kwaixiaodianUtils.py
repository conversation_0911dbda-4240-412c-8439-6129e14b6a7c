import datetime
import random
import time

import pyautogui
from selenium.webdriver import <PERSON><PERSON><PERSON><PERSON>, Keys
from selenium.webdriver.remote.webelement import WebElement
from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env

BaseCase.main(__name__, __file__)


class KwaiXiaoDianToolTest(BaseCase):

    def setUp(self, **kwargs):
        super().setUp()

    def tearDown(self):
        super().tearDown()

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()

    def kwaixiaodian_login(self, account):
        """ 快手小店登录 """
        self.maximize_window()

        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        elif self.var2 and self.var2 == 'prt':
            env = 'prt'
        elif self.var3 and self.var3 == 'prt':
            env = 'prt'
        else:
            env = 'online'

        domain = 'KWAIXIAODIAN'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.sleep(1)
            self.type("input[placeholder='请输入密码']", account_data['password'])
            self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
            self.sleep(2)
        else:
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click("(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)

    def kwaixiaodian_login_sub_child(self, account):
        """ 快手小店登录-子账号 """
        self.maximize_window()

        if self.var1 and self.var1 == 'prt':
            env = 'prt'
        elif self.var2 and self.var2 == 'prt':
            env = 'prt'
        elif self.var3 and self.var3 == 'prt':
            env = 'prt'
        else:
            env = 'online'

        domain = 'KWAIXIAODIAN'

        account_data = get_account_info(account)
        host = get_domain_by_env(domain, env)
        self.open(host)
        self.sleep(2)

        if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
            # 选择我是员工
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[1]/div[2]/span[2]')
            # 选择密码登陆
            self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            self.type("input[placeholder='请输入手机号']", account_data['account'])
            self.type("input[placeholder='请输入密码']", account_data['password'])
            # 登陆
            self.click(
                '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button')
            time.sleep(1)
        else:
            self.click("(//span[contains(text(),'我是员工')])[1]")
            self.sleep(1)
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.type("(//input[@placeholder='请输入手机号'])[1]", account_data['account'])
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
            self.sleep(1)
            self.click(
                "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentB svelte-am7f5d'])[1]")
            self.sleep(2)
            self.click_if_visible(account_data['default_log_select_page'])  # 点击小店试试的小店
            self.sleep(2)


    # def is_element_clickable(self, element: WebElement, **kwargs) -> bool:
    #     """
    #     判断元素是否可点击
    #     :param element: WebElement对象
    #     :return: 元素是否可点击，可点击返回True，否则返回False
    #     """
    #     try:
    #         if element.is_enabled() and element.is_displayed():
    #             return True
    #         else:
    #             return False
    #     except Exception:
    #         return False

    def scroll_down(self, times):
        """
        模拟向下滑动页面
        :param times: 向下滑动的次数
        """
        for i in range(times):
            ActionChains(self.driver).send_keys(Keys.PAGE_DOWN).perform()
            time.sleep(3)

    def get_random_activity_id(self, file_path):
        """
           从指定路径的txt文件中读取活动ID，并返回一个随机的活动ID

           参数:
           - file_path: 文件路径，包含活动ID的文本文件路径

           返回值:
           - 随机选择的活动ID

           异常:
           - FileNotFoundError: 文件不存在时引发该异常
           - IOError: 读取文件时发生错误时引发该异常
           - ValueError: 文件为空时引发该异常
           """
        try:
            with open(file_path, 'r') as file:
                activity_ids = file.read().splitlines()
                if activity_ids:
                    random_id = random.choice(activity_ids)
                    return random_id
                else:
                    raise ValueError("文件为空")
        except FileNotFoundError:
            raise FileNotFoundError(f"文件“{file_path}”不存在")
        except IOError:
            raise IOError(f"读取文件“{file_path}”时出错")

    def should_run_test_on_date(self):
        """
        判断是否应该在当前日期执行测试用例。

        返回值:
        - True：如果当前日期为双数
        - False：如果当前日期为单数
        """
        current_date = datetime.date.today()
        day = current_date.day
        if day % 2 == 0:  # 判断日期是否为双数
            return True
        else:
            return False

    def select_option(self, num_options, direction='down'):
        """
        使用方向键选择下拉框中的选项

        参数：
        - num_options：选择的次数
        - direction：选择的方向，可选值为 'down'、'up'、'left' 和 'right'，默认为 'down'

        注意：在调用该函数之前，请确保下拉框已经被打开

        """

        time.sleep(2)  # 等待2秒钟，确保下拉框已经打开

        # 确定选择的方向键
        if direction not in ['down', 'up', 'left', 'right']:
            raise ValueError("Invalid direction. Valid directions are 'down', 'up', 'left', and 'right'.")

        # 使用方向键选择选项
        for _ in range(num_options):
            pyautogui.press(direction)
            time.sleep(0.5)  # 等待0.5秒钟，给下拉框反应时间

        # 模拟按下回车键选择选项
        pyautogui.press('enter')

    def is_element_expected(self, element, attrsMap):
        """
        判断元素是否符合预期
        """
        try:
            for attrExpectedKey, attrExpectedValue in attrsMap.items():
                actualValue = element.get_attribute(attrExpectedKey).strip()
                if actualValue != attrExpectedValue:
                    return False
            return True
        except Exception:
            return False
