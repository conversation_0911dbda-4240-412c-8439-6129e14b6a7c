"""
# Time       ：2023/11/17 5:39 下午
# Author     ：author chen<PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
import json

import requests
from cacheout import LFUCache

cache = LFUCache(ttl=60 * 60 * 24 * 7)

def get_cookies_from_ks_infra_admin(user_id: str, sid) -> dict:
    if cache.get(f'{user_id}{sid}') is not None:
        return cache.get(f'{user_id}{sid}')

    cookies_dict = get_cookies_without_cache(user_id, sid)
    cache.set(f'{user_id}{sid}', cookies_dict)
    print("cookies = " + str(cookies_dict))
    return cookies_dict


def get_cookies_without_cache(user_id: str, sid, type=None):
    user_id = str(user_id)
    url_params = "?userId={}&sid={}".format(user_id, sid)

    ret = {}
    if user_id in passport_applied_accounts.keys():
        ret = passport_applied_accounts.get(user_id)
    else:
        ret = json.loads(requests.get(get_infra_admin_domain_by_env() + url_params).content)
    return extract_info_to_cookie(user_id, ret)

def extract_info_to_cookie(user_id: str, ret: dict) -> dict:
    cookies_dict = {"appver": "9.10.40", "userId": user_id}
    for key in ret.keys():
        if key == "userId":
            cookies_dict[key] = ret[key]
        if str(key).endswith("_st") or str(key).endswith("_ph"):
            cookies_dict[key] = ret[key]
    return cookies_dict

def get_infra_admin_domain_by_env() -> str:
        return "https://infra-admin.corp.kuaishou.com/token/create"



passport_applied_accounts = {
    # 生意通账号prtTest
    "prtTest": {
        "userId": "**********",
        "kuaishou.shop.b_st": "__mt_G3__e3MPq63d4fwtrYWp7PxPVFrrpvzWFQeQ4PdkuwsupXIDKHwt/p5XBNCshRb8FIT1Wn6QhAhMDfk8PiumMDxghj6Qh6J0esMGoxYi1eclr5T7+fkQc+kNG9lVS6d65hohP5NEC5D/7liRweC/cgBiWft+mwl0APCMqzlgC+ovOomny7AWMMJFd5XIK5wOSRLZ1L3+7/aP/yH2Vd0MB96nWrd/2LEq9G0p1qQSb6SQ64DwBCscQFN8BUiYuPdpu54l",
        "ssecurity": "lNfdD8ByKHPxwHK8z338mQ==",
        "tokenClientSalt": "94d7dd0fc0722873f1c072bccf7dfc99"
    },
    # 生意通账号zhanle
    "zhanle": {
        "userId": "**********",
        "kuaishou.shop.b_st": "__mt_G3__e3MPq63d4fwtrYWp7PxPVFrrpvzWFdCAQdpuODnBwWOe8FyBn/uQPkSgAMDFKpiMptluo6IvmKW1bL70a8e53Ke0YCKcBmA2mLb1BKaGfnKRTLujwqsPQGMcEd88rCYyf9UsJIB/hw1frjcWHux/DrCDwoaSky8uGOpRPLwF0GjCVesHLOlFdxJ2xC3rgl2X90GR3UOfhz145N0MxqNfuG4BIUfLGsgsR/0+UoRQAqQgT5EPjf3jrYmn415pu54l",
        "ssecurity": "wg+eoVi6i7gQmxmozcvGtA==",
        "tokenClientSalt": "c20f9ea158ba8bb8109b19a8cdcbc6b4"
    },

    # 生意通账号clj
    "clj": {
        "userId": "1275808322",
        "kuaishou.shop.b_st": "__mt_G3__e3MPq63d4fwtrYWp7PxPVFrrpvzWFVkTLYut4m30zEciURVLvZZot7wnbtm8pQGDiAZEVQvI8F4OuFkFWUqzHp63VxGyxeWngvVAh2jNIYwOP4DBmANMwIDzmWm2Nvu9z2sVUpXYlVNezTVfNNlSQ2kCZPtdhk1xYJk6sCgccmx2C1NCGmhFdzitMZnu7JO+dMVFMH3owxNeTN0MpDV++rSSqU2SNzmlrrwnSnYtFK5vtIMq4BnwlUnnyFxpu54l",
        "ssecurity": "U27rkymc/rVajEPXg5rjog==",
        "tokenClientSalt": "536eeb93299cfeb55a8c43d7839ae3a2"
    },
    "sytAPP": {
        "userId": "1275808322",
        "kuaishou.api_st": "__mt_Jq__e24Pq63d4fwtrYW79PoRCUzXpQmBcfhQWQaZe3/jJALZOxf36TvVdMmtRFWjN436HYJl4lLkP08qClfYo38I3ufkNss/muM65Bs8wgFpujh8Zj8tJRWNDpmdkAKiKX2zbCfBLwA+KjE25tkBuFKgHzkBA4fLUO71U70+nVgCDBWtOCuZdujqbL7VPGaeoNRT/5Z2xcbVxkb6WT6GQ+KwGi4U6TDr+IkcA7HXI3kp5vzjp2NmF9APQ6V016xA",
        "ssecurity": "qj28EqC52EgZzLEkdHJFLw==",
        "tokenClientSalt": "aa3dbc12a0b9d84819ccb1247472452f"
    },
    #pc-UI-kuaishou.ad.analysis_st
    # 生意通-会员账号
    "sytAD_2880831799": {
        "userId": "2880831799",
        "kuaishou.ad.analysis_st": "__mt_V6__e3YPq63d4fwtrYW74L1eFFmprPutFnMhapgIP58HkG9zRG5ctxRkYlHixUW14D8OOplYHpQE5c9FZOmHLe0upfnb+EE8FfQ6LjNNPtcjMBfShQaXNszwD3h1Nq+HglED0ZEKtBYgWa6UEr34413yaGqecv7vkA3C05OvSwa3zTt5Q3VH3ItPd+qqkjY6f4r+/r+oQ4NcbHn8Z11i6py+GiVSIny0eaUYTXY9qhLcOLLluwdzsy5sj7DSFbF+YEy8KY+a39wfqPCGMlNMxgb/drHhCuM5+g==",
        "ssecurity": "Su3z56kutw1B3p5VlGNGPg==",
        "tokenClientSalt": "4aedf3e7a92eb70d41de9e559463463e"
    },

    "sytAD_**********": {
        "userId": "**********",
        "kuaishou.ad.analysis_st": "__mt_Le__e3YPq63d4fwtrYW74L1eFFmprPutFnMhapgIPxr1KYzMlSXQd1Irodf4iXvh9Cn0yfd5sKTPSiSHBaTTYtlOSQU/MFnnj76XmVaa9DVyVz1dvRtdLKyd0mtJHO0VCzQHVJrQm9ERyiGghw4mVmwfuHtwpTJKCtoPVR6tXbodifkiaKHoCZSbN4kbhwPFoDKkcT7lQu5xLFd3Tc2g6pwk87Ph+bUhnGNKS9lIlNv8T+rlu3CakiYXwWbrPSvKuk9LYyaLgSogHY9vlD7rFt09RhcJCuM5+g==",
        "ssecurity": "YHib6hJK5v7Bfh1p6EfHKQ==",
        "tokenClientSalt": "60789bea124ae6fec17e1d69e847c729"
    },
    "APP_**********": {
        "userId": "**********",
        "kuaishou.ad.analysis_st": "__mt_TH__e24Pq63d4fwtrYW79PoRCUzXpfScd/yxfgI+k5riXBaifSIlQ2bbf1d3XgxheupBDwheVOA/QwWoP+eze2TZjhseD6RDC0x16uRDKB1XVXp0JuqDQSsb+n3hnAo8toJjTQM7P8iHyYzl4M72JdhY8gZA03TN2PYOZ3DXuqF3JBWwHbGZdqsd7961KmeShqlaggWhimRBSEb6nRPcUeE+rUxGzOlFqFrM33MmIXZL4zM0Mxx/AIUQBuB016xA",
        "ssecurity": "gGJ+31mROizLBUI7VCrvbg==",
        "tokenClientSalt": "80627edf59913a2ccb05423b542aef6e"
    },
    "sytAD_2201241513": {
        "userId": "2201241513",
        "kuaishou.ad.analysis_st": "__mt_UK__e3YPq63d4fwtrYW74L1eFFmprPutFnMhapgIP1uxb7aItAcRKQlfxfxdzUC8lJRXkiF3sdaY/cgJAitNE4OnKRKc+HSqKq6wAp/IAlZzLRuSW4E2pUPRvFbVj2CD2W3ec7gWlZouBSQvtnwd4/1ILymz8g9ljUhURE+da84lASqFifmHN6VpA4JdtwPPkCfcRI0COEEN1E8QoU2Z6pw2+XAqPC/Nfhzqf77FtAVEGh3lu7uhce87xHJpVPnT8TyeoGb/t60t1q+hoZjNDqIKk6hhCuM5+g==",
        "ssecurity": "BzgngY+BYMDrrGhylt7Vsg==",
        "tokenClientSalt": "073827818f8160c0ebac687296ded5b2"
    },
    #app-ui-kuaishou.api_st
    "sytAPP_***************": {
        "userId": "***************",
        "kuaishou.api_st": "__mt_Jq__e24Pq63d4fwtrYW79PoRCUzXpchv5vTNdj9ftTDxRFcl5XvYKArHoVWIFkgy6L04BmevJ5ASeroEnMPhHd9jBHbwDWBdx5vyYgfA0309a87nALXrlg3L5GPsV27Z6GBkyEKjBKx1tsxUGdJh/SbWzyD4CTudbXKV3gKzpCp5sGAWxeWZdtEkpiRG0WjdiaEEjTQzgRA43Ub6og5G2o+Wi93IX/HeEEitHNADJ/veAhQFtQ0kL7i4liR016xA",
        "ssecurity": "aqNcNO5dT21Rjgs4dJUu4g==",
        "tokenClientSalt": "6aa35c34ee5d4f6d518e0b3874952ee2"
    },
    "sytAPP_**********": {
        "userId": "**********",
        "kuaishou.api_st": "__mt_Jq__e24Pq63d4fwtrYW79PoRCUzXpajaBgS75UQuamgVH1HQtfUBIBsovev+VuOmB0Dixr1ZvxVFP2qnOxCqw+b9lC3jtSbANQ2LXuvA7VpAwZ7dAX3SP6hzwSPjT5wzqAE7h+2wqkLkfNKbtYDah1dWsFr6UyJD5UQCC3pJFOWhrxGdp+WZdiqdTU5yvmgWi9s2r3m5F8bqH0b6plLB/ooVGMGC1AIY1CtVd0X+r/AZgyFv4pPF2QDU5X5016xA",
        "ssecurity": "RK0+D6KpX2PQl9UofKQv1g==",
        "tokenClientSalt": "44ad3e0fa2a95f63d097d5287ca42fd6"
    },
    "sytAPP_**********": {
        "userId": "**********",
        "kuaishou.api_st": "__mt_Jq__e24Pq63d4fwtrYW79PoRCUzXpbkNpMEkoXOn3NzAWHN+J2P9BtLM/e7n+TKAQHDteWAu3V/M+T76QatkaTRrHqMByOXfIM6IjkslKp27phtiluWm8+ughN6wYx1BEfEJdEpx3FUiFZ55OM1fxvdYJsIrYY+0QKyVLBS+Bd1JshJAylaZdvKMpAj+52b/hjDkJIFzTetnPkb6bPzkr/hUeP9r/vQ2frge/tbmbkSg3Hyt9C1oUb/nByt016xA",
        "ssecurity": "+dBnZWyxjK5emaNqKVEmLA==",
        "tokenClientSalt": "f9d067656cb18cae5e99a36a2951262c"
    },
    "sytAPP_**********": {
        "userId": "**********",
        "kuaishou.api_st": "__mt_Jq__e24Pq63d4fwtrYW79PoRCUzXpQp1KYrm4mWKVgo6njPnV9P2ATvngArC7A05epUR/sxoGpBdG6WD51wqfaR4C4RTYt32WRmHGWxYbYBtCqvksRii7HBcBfkIKg0uk56//NLVaFZigdQ4jXXvNqL5OWgHnQ9/q5uaIYz1CPajTbDdKlaZdrFTyd89RGtRs+OV9uHBR03Cm0b6APGf3EajXTML/kAlhYAG0kp0bFeO6xW4xuRr+GvkyLl016xA",
        "ssecurity": "HQMQq7NCy6EqcHjHnGT8jA==",
        "tokenClientSalt": "1d0310abb342cba12a7078c79c64fc8c"
    },
    # 生意通账号 chenshengrui
    "chenshengrui": {
        "userId": "**********",
        "kuaishou.shop.b_st": "__mt_G3__e3MPq63d4fwtrYWp7PxPVFrrpvzWFZhU824RnVCP+RVA5VapmsVTFa70H31uMMCO9NnClRizICJULb89Xw3BQevPlSBaPGoAeipTXrkpqVzcbSC1qzxI5L9FfTQPZkpgsghFMbAgf2PNwSt7QwSFRg+THK3N2WwlbruyaMwHWa0iwhUxGoRFdzitMZnu7JO+dMVFMH3owxNeTN0MaFoKv/rVVwlq/kaxggIJZmrVtWtJWnpNoR+6pgbaoappu54l",
        "ssecurity": "yiedrt0X/FmGI6Hh0yhrKw==",
        "tokenClientSalt": "ca279daedd17fc598623a1e1d3286b2b"
    },
    # 生意通子账号
    "pxw_childAccount": {
        "userId": "***************",
        "kuaishou.shop.b_st": "__mt_G3__e3MPq63d4fwtrYWp7PxPVFrrpvzWFbdOBBLrf8mmLfG5Ke87CQG4m5AGNRIIVEy9pREd9FyJ1QqShJx3KbatgtEF0l4DVV9GQA4OEe578ayE3+oVLyukRtZPOSod9v2ckR8nFCuAS+n22bo0PKOqVnC9nPoY9xCTb77R3yFR0yQtKut7TdlFd4tkHrn1pnlSX6Z0sCxjZdxev90MS4XbQ24aKAKZWBu1h2OSlwQB49sWQ6GMt7P6vsaFSu5pu54l",
        "ssecurity": "tZDd2D4MIRGy6kDuA8yxDg==",
        "tokenClientSalt": "b590ddd83e0c2111b2ea40ee03ccb10e"
    },
    # 生意通
    "B_**********": {
        "userId": "**********",
        "kuaishou.shop.b_st": "__mt_1pf__e3MPq63d4fwtrYWp7PxPVFrrpvzWFfMHaZuI7qrp3CWGbLWrUKSulLAeR9axnMlPPYy2X8GaOQIdIPPRdzdaXfeEHWR6mePpWZvGM9yiCypzyUcjgHT/X6t20rvx20C+GB2cAoBwd2fksKRUZpHY+sXrQcNa+V66KK+EiminFpFrNf8wOXhFdwpRB+VM98+1VD8lWh91K85l2t0Mh0fQea4nTJScDlGhfRb2lcQfLlCcPWzQWQ502zO6y9ppu54l",
        "ssecurity": "UuZBDMwhXmf3dXvTidRRvg==",
        "tokenClientSalt": "52e6410ccc215e67f7757bd389d451be"
    },

}