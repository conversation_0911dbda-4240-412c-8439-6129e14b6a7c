from selenium import webdriver
import threading
import os
import time
from PIL import Image

class shotImageGif():
    def __init__(self,imgHeadPath="linkreport/"):
        self.img_head = imgHeadPath
        self.path = ""
    def clear_dir(self,path):
        """创建或清空目录"""
        if not os.path.isdir(path):
            os.makedirs(path)  # 创建目录
        else:  # 清空目录
            print("清空目录前",os.listdir(path))
            [os.remove(os.path.join(path, file_name)) for file_name in os.listdir(path)]
            print("清空目录后",os.listdir(path))

    def shot(self,dr, img_dir):

        if self.img_head is not None:
            img_dir = self.img_head + img_dir
        """循环截图函数"""
        i = 0
        self.clear_dir(img_dir)  # 清空目录
        dr.maximize_window()
        while True:
            img_file = os.path.join(img_dir, f'{i}.png')
            try:
                dr.save_screenshot(img_file)
            except:
                return
            i += 1
            self.path = os.path.join(img_dir, f'{i+1}.png')
    def first_img_save(self, img_dir):
        his_img_dir = img_dir
        if self.img_head is not None:
            img_dir = self.img_head + img_dir
        self.img_dir = img_dir
        # 图片拼接成gif
        img_list = os.listdir(img_dir)  # 列出目录所有图片
        img_list.sort(key=lambda x: int(x[:-4]))  # 排序

        first_img = Image.open(os.path.join(img_dir, img_list[0]))  # 第一张图片对象
        else_imgs = [Image.open(os.path.join(img_dir, img)) for img in img_list[1:]]  # 剩余图片对象



        first_img.save(img_dir+"/record.gif", append_images=else_imgs,
                       duration=300,  # 每张图片的过过渡时间
                       save_all=True) # 拼接保存，如果想要循环播放可以加上loop=0

        [os.remove(os.path.join(img_dir, file_name)) for file_name in os.listdir(img_dir) if "gif" not in file_name]

        return his_img_dir+"/record.gif"

def recognize(image,textas,path=""):

    import easyocr

    reader = easyocr.Reader(['ch_sim', 'en'])
    result = reader.readtext(image)
    for i in result:
        top_left = i[0][0]
        bottom_right = i[0][2]
        text = i[1]
        print(text)
        if textas == text:
            print(top_left,bottom_right)
            if path != "":
                import cv2
                import matplotlib.pyplot as plt
                import numpy as np

                font = cv2.FONT_HERSHEY_SIMPLEX

                img = np.asarray(bytearray(image), dtype="uint8")
                img = cv2.imdecode(img, cv2.IMREAD_COLOR)
                img = cv2.rectangle(img, top_left, bottom_right, (0, 255, 0), 3)
                img = cv2.putText(img, text, bottom_right, font, 0.5, (0, 255, 0), 2, cv2.LINE_AA)
                plt.figure(figsize=(10, 10))
                plt.imshow(img)
                plt.gca().xaxis.set_major_locator(plt.NullLocator())
                plt.gca().yaxis.set_major_locator(plt.NullLocator())
                plt.subplots_adjust(top=1, bottom=0, left=0, right=1, hspace=0, wspace=0)
                print(1231231231231231231231231,path)
                plt.savefig(path)
                # plt.show()
            return top_left,bottom_right
    raise Exception("文本不存在")



if __name__ == '__main__':
    recognize("/Users/<USER>/Downloads/归档3/X7Code/kwaishopuiautotest/linkreport/assets/iShot2022-12-07 16.53.23.png","男包","1.png")