# encoding: utf-8
import pymysql


def conn_mysql(sql, all_type=None):
    MYSQL_HOST = 'cluster01.proxysql.staging.internal'  # ip
    MYSQL_PORT = 6032  # 端口
    USER = 'merchant_qa_15520_v1_rw'  # 用户名
    PASSWORD = 'Kv1M5SunbtxPcZzi4R0DqI9k67XsoHWd'  # 密码
    DB = 'merchant_qa'  # 数据库名

    try:
        conn = pymysql.connect(host=MYSQL_HOST, user=USER, password=PASSWORD, db=DB, charset='utf8', port=MYSQL_PORT)
    except Exception as e:
        print('mysql连接出错,错误信息为%s' % e)
        res = {"code": 9901, "msg": 'mysql连接出错,错误信息为%s' % e}
    else:
        cur = conn.cursor(cursor=pymysql.cursors.DictCursor)
    try:
        cur.execute(sql)
    except Exception as e:
        msg = "sql执行出错，请检查sql,错误信息为：%s" % e
        res = {"code": 9902, "msg": msg}
    else:
        if all_type != None:

            res = cur.fetchall()
        else:
            res = cur.fetchone()
        conn.commit()
    finally:
        cur.close()
        conn.close()
        # print("res:", res)
    return res


if __name__ == '__main__':
    conn_mysql("")
