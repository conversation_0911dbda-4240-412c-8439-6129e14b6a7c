import json
import logging

import requests
import base64
from io import StringIO
import hashlib
from urllib.parse import urlencode

DOMAIN_NAME = "https://eshop-app.prt.kwaixiaodian.com"


class ApiTools:

    def api_for_app(self, account=None, url=None, data=None):
        account_data = ACCOUNT_ONLINE_CONFIG.get(account)
        api_token = api_login(account=account, password=account_data["password"],
                                        account_type=account_data["type"], user_id=account_data["uid"])
        print(str(api_token))
        ver = CLIENT_CONFIG["IOS_NEW"]["version"]
        did = CLIENT_CONFIG["IOS_NEW"]["did"]
        client_key = CLIENT_CONFIG["IOS_NEW"]["client_key"]
        cookies = "token={};appver={};did={};client_key={};kuaishou.api_st={};token_client_salt={};" \
                  "userId={};ud={};KS-CSRF-Token={}".format(api_token['token'], ver, did, client_key,
                                                            api_token['kuaishou.api_st'],
                                                            api_token['token_client_salt'],
                                                            api_token['region']['uid'], api_token['region']['uid'],
                                                            "autotest")
        headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'Host': 'prt-eshop-app.test.gifshow.com',
            'KS-CSRF-Token': 'autotest',
            "Cookie": cookies,
            'X-KTrace-Id-Enabled': "1",
            'ptp-flag': "1"
        }
        res = requests.post(url, json=data, headers=headers).json()
        return res


def decrypt(content):
    if not content.startswith("enc:"):
        return content
    content = content[4:]
    return str(base64.decodebytes(str.encode(content[1:] + '=' * int(content[0]))), encoding='utf8')


def api_login(account="dstest079", password="zzj19960329", account_type="mobile", did=None, appver=None,
                        client_key=None, user_id=None):
    if did is None:
        did = CLIENT_CONFIG['IOS_NEW']['did']
    if appver is None:
        appver = CLIENT_CONFIG['IOS_NEW']['version']
    if client_key is None:
        client_key = CLIENT_CONFIG['IOS_NEW']['client_key']
    # sig = hash('md5', "client_key=56c3713cmobile=19854971965mobileCountryCode=+86password=089d0dc9a68cc583873dc46306e3091a4eab4dbf3df06fdb77b0f04072ec1c33acd055decae4580478170407073cc5ba4dc66b70e2829fe057267d1d317d260823caab00356c")
    if account_type == 'smscode':
        url_suffix = 'mobileVerifyCode'
        type_key = 'mobile'
        password_key = 'code'
        pw = decrypt(password)
    else:
        url_suffix = account_type
        type_key = account_type
        password_key = 'password'
        pw = hash('sha512', decrypt(password))
    url = "https://apissl.gifshow.com/rest/n/user/login/" + url_suffix
    account_config = ACCOUNT_ONLINE_CONFIG.get(account)
    account, mobile_country_code = account_config["account"], '+86' if "countryCode" not in account_config.keys() else \
        account_config["countryCode"]
    param_data = {
        type_key: account,
        password_key: pw,
        "mobileCountryCode": mobile_country_code,
        "client_key": client_key,
    }
    secret = "23caab00356c"
    # for value in CLIENT_CONFIG.values():
    #     if value.get("client_key") == client_key:
    #         secret = value.get("secret")
    sign_param = get_kuaishou_api_sign_param(param_data, None, None, client_key, secret)
    param_data["sig"] = sign_param["sig"]
    param_data["__NS_sig3"] = "2159768269c7ffffffffffffffffffffffffffffff"
    header = {
        'Cookie': "appver={};did={};client_key={}".format(appver, did, client_key)}
    login_data = requests.post(url=url, data=param_data, headers=header).json()
    return login_data


def get_kuaishou_api_sign_param(param_dict, token, token_client_salt, client_key, secret):
    if token:
        param_dict['token'] = token
    param_dict['client_key'] = client_key
    arg_str_buffer = StringIO()
    for k, v in sorted(param_dict.items(), key=lambda d: d[0]):
        if k == 'sig' or k.startswith('__NS'):
            continue
        arg_str_buffer.write('{}={}'.format(k, v))
    arg_str_buffer.write(secret)
    arg_str = arg_str_buffer.getvalue()
    sig = hash('md5', arg_str)
    if token_client_salt:
        ns_token_sig = hash('sha256', sig + token_client_salt)
    else:
        ns_token_sig = None
    ns_sig3 = '2159768269c7ffffffffffffffffffffffffffffff'
    sign_param = {'client_key': client_key, 'sig': sig, '__NS_sig3': ns_sig3}
    if token:
        sign_param['token'] = token
    if ns_token_sig:
        sign_param['__NStokensig'] = ns_token_sig
    return sign_param


def hash(algorithm, content):
    hashobj = getattr(hashlib, algorithm)()
    hashobj.update(content.encode('utf-8'))
    return hashobj.hexdigest()


TOKEN_CONFIG = CLIENT_CONFIG = {

    # 框架自动登录体系专用
    "IOS_NEW": {"version": "11.3.20.1334759",
                "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
                "client_key": "56c3713c",
                "secret": "23caab00356c"},

    "IOS_10": {"version": "10.9.10.630",
               "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
               "client_key": "56c3713c",
               "secret": "23caab00356c"},

    "IOS_11": {"version": "11.1.30.630",
               "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
               "client_key": "56c3713c",
               "secret": "23caab00356c"},

    "IOS_12": {"version": "11.5.30.630",
               "did": '51B907F5-998C-4D89-9E8A-41377FF0724C',
               "client_key": "56c3713c",
               "secret": "23caab00356c"},

    # 框架自动登录体系专用
    "WEB_NEW": {"did": 'web_3zis6zhuz1lgnfqblwwyw7mvgi2it7ph', "client_key": "56c3713c"},

    "Pay": {
        "cookie": {"Kspay-Client-SDK": "2.1.7",
                   "kuaishou.midground.api_st": "ChlrdWFpc2hvdS5taWRncm91bmQuYXBpLnN0EqABYS8Qoy-m30TTfh5avesoAfAScbvUyNxIbKQlndW1SsHUqNTmcni7B5nWBsy83QLGjNDXbex-Ph5Jf8ihujtMB25gcAGcYLlUCLUfpGsvRNIPuEeHLjb4DgmNH3mDHHRcrLZVoGz_1Y07GtlHVgeqSoR9gFDnJyu4j_UBljB7BakB1S2piW47XOWVJN7ejhJ1ddOiX0gMrWD5PuiZtz7BtBoSImJ5p6EN2Qh_Uw-gb4grsigYIiBwMYOtJyIRBdbKsBTxorm_wPV83ATDEsh8so0E7VvTsCgFMAE",
                   "User-Agent": "kwai-ios", "kspay_encode": True, "country_code": "cn"},
        "header": {"Content-Type": "application/x-www-form-urlencoded"}
    }

}

ACCOUNT_ONLINE_CONFIG = {
    'returnnull': {'account': '***********', 'password': 'guessMe@2022', 'type': 'mobile', 'uid': '**********'},
    'gaohuzhen': {'account': '***********', 'password': 'Ghz@test123', 'type': 'mobile', 'uid': '**********'},
    'lijinglei': {'account': '***********', 'password': 'zhzh9999', 'type': 'mobile', 'uid': '**********'},
    'bjfeng': {'account': '***********', 'password': 'kkk111111', 'type': 'mobile', 'uid': '**********'},
    'huoyangyang': {'account': '***********', 'password': 'test12345678', 'type': 'mobile', 'uid': '**********'},

    'zhangchenghong': {'account': '***********', 'password': 'dstest@123', 'type': 'mobile', 'uid': '**********'},

    "syt_account": {"account": "***********", "password": "ks1234567", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "syt_account_admin": {"account": "***********", "password": "csr123456", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    "clj": {"account": "***********", "password": "chenlijun1227", "type": "mobile", "remark": "kssyt",
            "uid": "**********"},
    "sanzhimao":{"account": "***********", "password": "dstest@123", "type": "mobile", "remark": "kssyt",
            "uid": "**********"},
    "xiaohs":{"account": "***********", "password": "zxcvbnm0910", "type": "mobile", "uid": "*********"},
    # "xiaohs":{"account": "***********", "password": "123456ly", "type": "mobile", "uid": "**********"},
    "xiaohuishuang":{"account": "***********", "password": "abc123456", "type": "mobile", "uid": "**********"},
    "syt_account_member": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    "syt_account_zl": {"account": "***********", "password": "test1234", "type": "mobile","remark": "kssyt", "uid": "**********",
                          "countryCode": "+86"},
    "syt_account_wj": {"account": "***********", "password": "test123456", "type": "mobile","remark": "kssyt", "uid": "**********",
                          "countryCode": "+86"},
    # 售后
    "refund_account": {"account": "***********", "password": "dianlaotie000", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},
    "huwen": {"account": "***********", "password": "haha123456", "type": "mobile", "uid": "**********",
              "countryCode": "+86"},
    "sanzhimao": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
                  "countryCode": "+86"},

    "live_plan_account": {"account": "***********", "password": "csr123456", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    "live_screen_account": {"account": "***********", "password": "qa123123", "type": "mobile", "uid": "**********",
                            "countryCode": "+86"},
    # 供应链账号 child：*********** wy@123456 baba：***********. qweasd111
    "supply_account_child": {"account": "***********", "password": "wy@123456", "type": "mobile",
                             "uid": "***************",
                             "countryCode": "+86"},

    "supply_account": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "supply_account_template":{"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "supply_express_account": {"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "wb_account": {"account": "***********", "password": "cc1234566", "type": "mobile", "uid": "**********",
                   "countryCode": "+86"},

    "gaozhongshu": {"account": "***********", "password": "gzs19921217", "type": "mobile", "uid": "*********",
                    "countryCode": "+86"},

    "gaozhongshu1": {"account": "***********", "password": "gzs17031217", "type": "mobile", "uid": "**********",
                     "countryCode": "+86"},

    # 工作台子账号
    "child_account": {"account": "***********", "password": "Cc@123456", "type": "mobile", "uid": "***************",
                      "countryCode": "+86"},
    # 客服子账号B
    "detest017_74": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "div.shopSelect--G-H8I > div:nth-child(6)"},
    # 客服子账号A
    "detest017_76": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                     "child": True,
                     "default_log_select_page": "div.kwaishop-login-pc-modal-body > div:nth-child(1) > div:nth-child(1)"},
    # 客服主账号
    "customer_merchant": {"account": "***********", "password": "hkk.123456", "type": "mobile", "uid": "**********"},

    # 客服子账号丁湘
    "detest017_75": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "//*[text()='丁崽']"},
    "detest017_75_2": {"account": "***********", "password": "eptest123!", "type": "mobile", "uid": "***************",
                       "child": True, "default_log_select_page": "//*[text()='地猫小店']"},

    "customer-jsh": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "//*[contains(text(),'开心一万年')]"},

    "customer-***********":{"account": "***********", "password": "ltn@5090791", "type": "mobile", "uid": "***************",
                     "child": True, "default_log_select_page": "//*[contains(text(),'七里香的小店')]"},

    "customer-z": {"account": "***********", "password": "ltn5090791", "type": "mobile", "uid": "**********"},

    # 商品账号
    "product_account": {"account": "***********", "password": "ks654321", "type": "mobile", "uid": "**********",
                        "countryCode": "+86"},
    "product_account_2": {"account": "***********", "password": "ks654321", "type": "mobile", "uid": "********",
                        "countryCode": "+86"},
    "product_account_3": {"account": "***********", "password": "s12345678", "type": "mobile", "uid": "**********",
                            "countryCode": "+86"},
    # 订单账号

    "order_account": {"account": "***********", "password": "zxcvbnm0910", "type": "mobile", "uid": "*********",
                      "countryCode": "+86"},

    # 跨境商家
    "clearance_account": {"account": "***********", "password": "kuajing666", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},

    # 物流相关账号
    "logistics_account": {"account": "***********", "password": "Fahuo666", "type": "mobile", "uid": "**********",
                          "countryCode": "+86"},
    # 跟播助手账号
    "helper_assistant": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "helper_assistant_child": {"account": "***********", "password": "zjj123456", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    # 短视频
    "short_video": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    # 直播计划账号
    "reservation_account": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                            "countryCode": "+86"},

    # 券码核销的账号
    "locallife_account": {"account": "***********", "password": "huangzt!2022", "type": "mobile",
                          "uid": "**********",
                          "countryCode": "+86"},
    # 商家会员账号
    "member_account": {"account": "***********", "password": "csr123456", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},

    "member_account2": {"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                        "countryCode": "+86"},
    "lyh_UI": {"account": "***********", "password": "ks1234567", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "lyh_sub": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                "countryCode": "+86"},
    # 资金账号
    "funds_account_01": {"account": "***********", "password": "zxcvbnm0910", "type": "mobile", "uid": "*********",
                         "countryCode": "+86"},
    "funds_account_02": {"account": "***********", "password": "kuajing666", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "funds_account_03": {"account": "***********", "password": "yangping123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "funds_account_04": {"account": "***********", "password": "qa123456", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "funds_account_08": {"account": "***********", "password": "qa123456", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
    "funds_account_22": {"account": "***********", "password": "kuaishoukuajing666", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
        #个人店-未进件-已认真
    "funds_account_05": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},
        #旗舰店-已进件-已认真
    "funds_account_06": {"account": "***********", "password": "yangping123", "type": "email",
                 "remark": "merchant","uid": "**********"},
        # 个人店-已进件-已认真
    "funds_account_07": {"account": "***********", "password": "qa123456", "type": "mobile", "uid": "**********",
                         "countryCode": "+86"},

    # 招商账号
    'rz_login': {"account": "***********", "password": "wyztest@1234", "type": "mobile", "uid": "**********",
                 "countryCode": "+86"},

    # 分销账号
    "wb_huoyangyang": {"account": "***********", "password": "test12345678", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},
    "zhangwei": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
                       "countryCode": "+86"},
    "wb_caijinwei": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
                     "countryCode": "+86"},
    "liuxiaohui07": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
                     "countryCode": "+86"},
    "maliya": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "taoheliang": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "nashui": {"account": "***********", "password": "wyztest@1234", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "zhudong": {"account": "***********", "password": "test123456", "type": "mobile", "uid": "*********",
                   "countryCode": "+86"},
    "lizhiqiang06": {"account": "***********", "password": "test12345678", "type": "mobile", "uid": "**********",
                "countryCode": "+86"},
    "chenshuzhan": {"account": "***********", "password": "tom123456", "type": "mobile", "uid": "**********",
                "countryCode": "+86"},
    # 子账号【商家&团长】
    "bypass_account": {"account": "***********", "password": "344354", "type": "mobile", "uid": "***************",
                       "countryCode": "+86"},
    # 服务市场服务商主账号
    "service_market_provider": {"account": "***********", "password": "test1234", "type": "mobile", "uid": "**********",
                                "countryCode": "+86"},
    # 服务市场服务商主账号
    "service_market_provider_2": {"account": "***********", "password": "ks123456", "type": "mobile",
                                  "uid": "**********", "countryCode": "+86"},
    # 服务市场商家主账号
    "service_market_merchant": {"account": "15558011995", "password": "test112233", "type": "mobile",
                                "uid": "506125720", "countryCode": "+86"},
    # 服务市场商家主账号
    "service_market_merchant_2": {"account": "***********", "password": "test1234", "type": "mobile",
                                  "uid": "**********", "countryCode": "+86"},
    # 服务市场服务商子账号
    "service_market_provider_child": {"account": "18843019492", "password": "test.1234", "type": "mobile",
                                      "uid": "140000043461442", "countryCode": "+86", "child": True,
                                      "default_log_select_page": "//*[text()='东阳市横店鑫荣影视工作室']"},
    # 服务市场商家子账号
    "service_market_merchant_child": {"account": "18843019492", "password": "test.1234", "type": "mobile",
                                      "uid": "140000043461664", "countryCode": "+86", "child": True,
                                      "default_log_select_page": "//*[text()='小黑不黑工作室']"},
    # 治理商家账号
    "themis": {"account": "19133301607", "password": "test@123", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},

    # 营销商家账号
    "kdstest102": {"account": "***********", "password": "dstest@123", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "marketing": {"account": "***********", "password": "lrx123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    #会员群聊账号
    #会员群聊账号
    "marketing_member": {"account": "***********", "password": "hsg123456", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    'hanyanping': {'account': 'hanyanping', 'password': 'hyp@HYP123', 'type': 'mobile', 'uid': '**********'},

    # 商家成长账号
    "zhengguihua": {'account': '***********', 'password': 'a1234567', 'type': 'mobile', 'uid': '**********'},
    "csr": {'account': '***********', 'password': 'csr123456', 'type': 'mobile', 'uid': '**********'},

    #备店账号
    "zgh_beidian": {'account': '***********', 'password': 'a12345678', 'type': 'mobile', 'uid': '**********'},

    # 精准营销测试账号
    "zengjingwen": {"account": "***********", "password": "test0000", "type": "mobile", "uid": "**********",
               "countryCode": "+86"},
    "pengshengpu": {"account": "***********", "password": "test@123", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "xuwei": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "didengke": {"account": "***********", "password": "kkk111111", "type": "mobile", "uid": "**********",
                    "countryCode": "+86"},
    "sanbanfu": {"account": "***********", "password": "ks123456", "type": "mobile", "uid": "**********",
                 "countryCode": "+86"},

}
