# 用于对相关内容进行转换输出

import re

def str2number(text):
    """
    用于把一个字符串类型的数字转换成常规的计数方式，如果出现异常将返回异常
    比如：¥1.01万=10100
    return: float
    """
    number_mapping = {
        "%": 0.01,
        "千": 1000,
        "万": 10000,
        "亿": 100000000,
    }

    text = text.strip()
    pattern = r"\d+(\.\d+)?"

    try:
        match = re.search(pattern, text)
        if match:
            num = float(match.group())
            start, end = match.span()  # end不包含在内
            if len(text) > end:
                char = text[end]
                num *= number_mapping.get(char, 1)
            return round(num, 8)    # 仅保留8位，避免精度问题
        else:
            return None
    except Exception as e:
        raise Exception("不支持的转换类型")

def timeStr2second(text):
    """
    用于把一个字符串类型的时间转换成成常规的计数方式，如果出现异常将返回异常
    比如：12时30分钟15秒=45015
    return: int
    """
    # pattern = r'(?:(\d+)[小时h])?(?:(\d+)[分m])?(?:(\d+)[秒s])?'
    pattern = r'(?:(\d+)\s*(?:小时|时|h))?\s*(?:(\d+)\s*(?:分钟|分|m))?\s*(?:(\d+)\s*(?:秒|s)?)?'

    try:
        match = re.match(pattern, text.strip())
        if match:
            # print(match.groups())
            hours = int(match.group(1)) if match.group(1) else 0
            minutes = int(match.group(2)) if match.group(2) else 0
            seconds = int(match.group(3)) if match.group(3) else 0

            # 计算总秒数
            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds
        else:
            raise ValueError("输入的时间格式不正确")
    except Exception as e:
        raise Exception("不支持的转换类型")