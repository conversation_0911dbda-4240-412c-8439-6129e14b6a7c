"""
# Time       ：2023/11/17 4:51 下午
# Author     ：author ch<PERSON><PERSON><PERSON>
# version    ：python 3.9
# Description：
"""
from enum import Enum
import requests
from utils.password_support import get_cookies_from_ks_infra_admin

class PassingMethod(Enum):
    params = 1,
    data = 2,
    json = 3

class BaseHttpRequest(object):

    def __init__(self, user_id: str, sid: str = "kuaishou.shop.b") -> None:
        super().__init__()
        self.user_id = user_id
        self.sid = sid

    # 生意通账号代理
    def account_proxy_confirm(self, key, value):
        api = "https://syt.kwaixiaodian.com/rest/business/analysis/account/info"
        data = {}
        res = self.send_normal_request(api, method='POST', json=data)
        if not res['data']['userInfo']['user_id'] == value:
            print(res['data']['userInfo']['user_id'] == value)
            api_proxy = "https://lego.corp.kuaishou.com/gateway/business/sellerdata/operation/proxy/set"
        # api = SytDomainOfPc.get_domain() + "/rest/business/analysis/account/seller"
        # data = {"userOrKwaiId": uid}
        # res = self.send_normal_request(url=api, method='post', json=data)
        # user_id = res['data']['userId']
        # account_config = ACCOUNT_CONFIG.get(self.user_id, None)
        # self.account_switch(account_config["uid"])

            data_proxy = {"env": "PROD", "secs": "86400", "key": key, "value": value, "scene": "SYT"}
            res_proxy = self.send_normal_request(api_proxy, method='POST', json=data_proxy)
        return value

    # 生意通账号代理
    def account_proxy(self, key, value):

        api_proxy = "https://lego.corp.kuaishou.com/gateway/business/sellerdata/operation/proxy/set"
        # api = SytDomainOfPc.get_domain() + "/rest/business/analysis/account/seller"
        # data = {"userOrKwaiId": uid}
        # res = self.send_normal_request(url=api, method='post', json=data)
        # user_id = res['data']['userId']
        # account_config = ACCOUNT_CONFIG.get(self.user_id, None)
        # self.account_switch(account_config["uid"])

        data_proxy = {"env": "PROD", "secs": "86400", "key": key, "value": value, "scene": "SYT"}
        res_proxy = self.send_normal_request(api_proxy, method='POST', json=data_proxy)
        return value



    def account_proxy_prt(self, key, value):
        api_proxy = "https://lego.corp.kuaishou.com/gateway/business/sellerdata/operation/proxy/set"
        # api = SytDomainOfPc.get_domain() + "/rest/business/analysis/account/seller"
        # data = {"userOrKwaiId": uid}
        # res = self.send_normal_request(url=api, method='post', json=data)
        # user_id = res['data']['userId']
        # account_config = ACCOUNT_CONFIG.get(self.user_id, None)
        # self.account_switch(account_config["uid"])
        data_proxy = {"env": "PRT", "secs": "86400", "key": key, "value": value, "scene": "SYT"}
        res_proxy = self.send_normal_request(api_proxy, method='POST', json=data_proxy)
        return value

    def account_proxy_remove(self,key,value):
        api_remove="https://lego.corp.kuaishou.com/gateway/business/sellerdata/operation/proxy/remove"
        data_remove = {"env": "PROD", "secs": "86400", "key": key, "value": value, "scene": "SYT"}
        res_remove = self.send_normal_request(api_remove, method='POST', json=data_remove)
        assert res_remove['result']==200

    def send_normal_request(self, url: str, method: str, params=None, data=None, json=None, lane_id='PRT.test', **kw):
        headers = {
            # "trace-context": "{\"laneId\": lane_id}"
        }
        res = requests.request(url=url,
                               method=method,
                               cookies=get_cookies_from_ks_infra_admin(self.user_id, self.sid),
                               params=params,
                               data=data,
                               json=json,
                               headers=headers)
        return res.json()