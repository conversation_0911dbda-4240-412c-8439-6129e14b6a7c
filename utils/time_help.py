import datetime
import time
from enum import IntEnum
from functools import wraps
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def current_timestamp() -> int:
    return int(time.time() * 1000)


class TimeUnit(IntEnum):
    MILLISECOND = 1
    SECOND = MILLISECOND * 1000
    MINUTE = SECOND * 60
    HOUR = MINUTE * 60
    DAY = HOUR * 24
    WEEK = DAY * 7


def calculate_timestamp(timestamp: int, delta: int, time_unit: TimeUnit = TimeUnit.MILLISECOND) -> int:
    assert len(str(timestamp)) == 13, 'calculate_timestamp: invalid timestamp'
    return timestamp + delta * time_unit


def timestamp_to_date_str(timestamp: int, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    return datetime.datetime.fromtimestamp(timestamp / 1000.0).strftime(fmt)


def date_str_to_timestamp(date_str: str, fmt: str = '%Y-%m-%d %H:%M:%S') -> int:
    return int(datetime.datetime.strptime(date_str, fmt).timestamp() * 1000)


def is_current_time_in(unit: str, *values: int) -> bool:
    current_time = datetime.datetime.now()
    if unit == 'minute':
        current_value = current_time.minute // 10
        return any(current_value == value // 10 for value in values)
    elif unit == 'hour':
        return current_time.hour in values
    elif unit == 'weekday':
        return current_time.weekday() + 1 in values
    return False


def day_start_time(interval_days: int = 0, time_unit: TimeUnit = TimeUnit.DAY, fmt: str = '%Y-%m-%d %H:%M:%S') -> int:
    today = datetime.date.today()
    target_date = today + datetime.timedelta(days=interval_days)
    return date_str_to_timestamp(target_date.strftime(fmt), fmt)


def timing_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()  # 记录开始时间
        result = func(*args, **kwargs)  # 调用原函数
        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算运行时间
        logger.info(f"Function '{func.__name__}' executed in {elapsed_time:.4f} seconds.")
        return result  # 返回原函数的返回值

    return wrapper


class DateFactory:
    @staticmethod
    def get_day_range(date: datetime.date) -> tuple[int, int]:
        begin = datetime.datetime.combine(date, datetime.time.min)
        end = datetime.datetime.combine(date, datetime.time.max)
        return int(begin.timestamp() * 1000), int(end.timestamp() * 1000)

    @classmethod
    def today(cls) -> tuple[int, int]:
        return cls.get_day_range(datetime.date.today())

    @classmethod
    def last_month(cls) -> tuple[int, int]:
        today = datetime.date.today()
        first_day = (today.replace(day=1) - datetime.timedelta(days=1)).replace(day=1)
        last_day = today.replace(day=1) - datetime.timedelta(days=1)
        return cls.get_day_range(first_day)[0], cls.get_day_range(last_day)[1]


if __name__ == '__main__':
    print(day_start_time(interval_days=30))

    live_time_start = calculate_timestamp(current_timestamp(), 5, TimeUnit.MINUTE)
    live_time_end = calculate_timestamp(current_timestamp(), 10, TimeUnit.MINUTE)
    live_time_start = timestamp_to_date_str(live_time_start)[:-3]
    live_time_end = timestamp_to_date_str(live_time_end)[:-3]
    print(live_time_start)
    print(live_time_end)
